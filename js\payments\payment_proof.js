+ function ($) {
	'use strict';

	function mark_acknowledge(payment_id, ar_no) {
		return $.ajax({
			url: BASE_URI + 'payments/mark_acknowledged/',
			type: 'POST',
			data: { payment_id: payment_id, ar_no: ar_no },
			dataType: 'json'
		});
	}

	$(function () {
		$('.action-print').click(function () {
			var paymentId = $('input[name="payment_id"]').val();
			var arNo = $('input[name="ar_no"]').val();
			alertify
				.confirm('Are you sure you want to print?', function (e) {
					if (e) {
						mark_acknowledge(paymentId, arNo);
						window.print();
						window.location.reload();
					} else {
						alertify.closeLogOnClick(true);
					}
            	})
				.setting({
					'title': 'Confirm Acknowledgement Receipt!',
					'message': '<div style="text-align: center;">Are you sure you want to print this acknowledgement receipt?</div>'
				})
		});
	});
}(jQ<PERSON>y);
