<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Customers extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->customer_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('customers/manager'));
    }

    /**
     * Get Customer Data
     */
    public function get_data($customer_id = null)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'name' => $customer->name,
                'address' => $customer->address,
                'contact_number' => $customer->phone_no,
                'email' => $customer->email,
                'available_credit' => $customer->available_credit,
                'credit_limit' => $customer->credit_limit,
                'tin_number' => $customer->tin_number,
                'is_active' => $customer->is_active,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Callback: Check if customer name exists
     */
    public function _is_customer($customer_name)
    {
        $where = [
            'name' => $customer_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->customer_name) === strtolower($customer_name)) {
            return true;
        }

        return empty($this->customer->select('', $where, 1));
    }

    /**
     * Add Customer
     */
    public function add()
    {

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($this->config->item('customers/add')) and $this->_attempt_upload($config, 'attachments', false) and $id = $this->_attempt_add() and $this->_add_attachment($id)) {

            $new_path = FCPATH . 'assets/asylum/customers/' . $id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('customers/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Add Customer';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'items' => $items,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'customers/add', $data);
        }
    }

    /**
     * Edit Customer
     */
    public function edit($customer_id)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        $customer = $this->customer->select('', $where, 1) or show_404();

        $this->customer_name = $customer->name;

        $where = ['is_deleted' => 0];
        $customer_prices = $this->customer_prices->select('customer_id, item_id, customer_price', $where);

        if (!empty($customer_prices)) {
            //creates two dimenstional array for customer price checker
            foreach ($customer_prices as $customer_price) {
                $curr_customer_id = $customer_price->customer_id;
                $curr_item_id = $customer_price->item_id;
                $curr_customer_price = $customer_price->customer_price;
                $customer_prices_dictionary[$curr_customer_id][$curr_item_id] = $curr_customer_price;
            }
        } else {
            $customer_prices_dictionary = null;
        }

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($this->config->item('customers/edit')) and $this->_attempt_upload($config, 'attachments', false) and $this->_attempt_edit($customer, $customer_prices_dictionary) and $this->_add_attachment($customer_id)) {

            $new_path = FCPATH . 'assets/asylum/customers/' . $customer_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('customers/manager'));
        } else {
            $this->title = 'Edit Customer';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'form_error' => $form_error,
                'customer' => $customer,
                'items' => $items,
                'customer_prices_dictionary' => $customer_prices_dictionary
            ];
            $this->build_content('default', 'customers/edit', $data);
        }
    }

    /**
     * Delete Customer
     */
    public function delete($customer_id)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } elseif (!$this->_attempt_delete($customer)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Customer is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Set Customer Status
     */
    public function inactive($customer_id)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } elseif (!$this->_attempt_inactive($customer)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Customer is successfully updated.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Check Customer remaining Credit Limit
     */
    public function check_credit($customer_id)
    {
        $used_credit = $this->customer->used_credit($customer_id) ?: 0;

        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];

        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Customer is found.',
                'credit_limit' => $customer->credit_limit ? (float)$customer->credit_limit - (float)$used_credit->credit : 0
            ];
        }

        echo json_encode($response);
    }

    /**
     * Customers
     */
    public function manager()
    {
        $this->title = 'Customers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/customers/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'customers/manager', $data);
    }


    /**
     * Customers SOA
     */
    public function soa()
    {
        $this->title = 'Customers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'js/customers/soa'
        ];

        $this->build_content('default', 'customers/soa');
    }

    /**
     * Customers with negative credit
     */
    public function credit_report()
    {
        $this->title = 'Credit Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/customers/credit_report'
        ];

        $this->build_content('default', 'customers/credit_report');
    }

    /**
     * Customers
     */
    public function customers_sale()
    {
        $this->title = 'Sales Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/select2/dist/js/select2.full',
            'js/customers/sales_report'
        ];

        $this->build_content('default', 'customers/sales_report');
    }

    /**
     * Get Info of Customer
     */
    public function info($customer_id = '')
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Customer is found.',
                'customer' => $customer
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Details of Customer
     */
    public function get_details($customer_id)
    {
        if (!$customer = $this->customer->get_details($customer_id)) {
            http_response_code(404);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Customer is found.',
                'customer' => $customer
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of Customer by name
     */
    public function info_by_name($customer_name = '')
    {
        $where = [
            'name' => urldecode($customer_name),
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        if (!$customer = $this->customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Customer is found.',
                'customer' => $customer
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('customer');
        $this->load->model('customer_prices');
        $this->load->model('customer_attachment');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'zip_code' => $this->input->post('zip'),
            'company_name	' => $this->input->post('company'),
            'business_style' => $this->input->post('business_style'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'tin_number' => $this->input->post('tin_number'),
            'available_credit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];
        if (!$customer_id = $this->customer->insert($values)) {
            $this->_error = 'server_error';
        }

        // $item_prices = $this->input->post('item_price');

        // foreach ($item_prices as $item_id => $item_price) {
        //     if ($item_price == NULL) {
        //         continue;
        //     }

        //     $values = [
        //         'item_id' => $item_id,
        //         'customer_id' => $customer_id,
        //         'customer_price' => (float)$item_price,
        //         'added_by' => $_SESSION['user']->id,
        //         'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        //     ];

        //     if (!$this->customer_prices->insert($values)) {
        //         $this->db->trans_rollback();
        //         $this->_error = 'server_error';
        //     }

        // }

        return $customer_id;
    }

    /**
     * Attempt add attachment
     */
    protected function _add_attachment($id)
    {
        if (!empty($this->_uploaded)) {
            $values = [];
            foreach ($this->_uploaded as $uploaded) {
                $values[] = [
                    'customer_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];
            }
            if (!$attachment_id = $this->customer_attachment->insert_batch($values)) {
                $this->_error = 'server_error';
                return false;
            }

            return $attachment_id;
        } else {
            return true;
        }

    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($customer, $customer_prices_dictionary)
    {
        $where = [
            'id' => $customer->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'zip_code' => $this->input->post('zip'),
            'company_name' => $this->input->post('company'),
            'business_style' => $this->input->post('business_style'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'tin_number' => $this->input->post('tin_number'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->customer->update($where, $values)) {
            $this->_error = 'server_error';
        }

        $new_item_prices = $this->input->post('item_price');

        foreach ($new_item_prices as $item_id => $new_item_price) {
            if ($new_item_price == null) {
                continue;
            }

            //Check whether a variable is empty. Also check whether the variable is set/declared:
            if (isset($customer_prices_dictionary[$customer->id][$item_id])) {
                $where = [
                    'item_id' => $item_id,
                    'customer_id' => $customer->id,
                    'is_deleted' => 0
                ];

                $values = [
                    'customer_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$is_updated = $this->customer_prices->update($where, $values)) {
                    $this->_error = 'server_error';
                }
            } else {
                $values = [
                    'item_id' => $item_id,
                    'customer_id' => $customer->id,
                    'customer_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$this->customer_prices->insert($values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                }
            }

        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($customer)
    {
        $where = [
            'id' => $customer->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $customer->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->customer->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Inactive Status
     */
    protected function _attempt_inactive($customer)
    {
        $user = $_SESSION['user']->id;
        
        $newStatus = $customer->is_active ? 0 : 1;
    
        $where = [
            'id' => $customer->id,
            'is_deleted' => 0
        ];
    
        $values = [
            'is_active'  => $newStatus,
            'updated_by' => $user,
            'updated_on' => date('Y-m-d H:i:s'),
        ];
    
        if (!$is_updated = $this->customer->update($where, $values)) {
            $this->_error = 'server_error';
        }
    
        return $is_updated;
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();

                $uploads = $this->_uploaded;

            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {

        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
