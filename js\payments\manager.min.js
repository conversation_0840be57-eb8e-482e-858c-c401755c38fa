!function(t){"use strict";function e(e){var a=t(e),n=a.DataTable({lengthChange:!1,dom:"Brtip",info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-left",targets:[0,1,3,4]},{className:"text-right",targets:2},{className:"dt-action",targets:-1},{searchable:!0,targets:[1]}],processing:!0,serverSide:!0,buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:":not(:last-child)"}}],ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(e,a){t.when(n(e)).then(function(){a.ajax.reload(null,!1)},function(t){a.ajax.reload(null,!1);var e=JSON.parse(t.responseText);alertify.error(e.message)})}function n(e){return t.ajax({url:BASE_URI+"payments/delete/"+e,type:"POST",dataType:"json"})}t(function(){var n=e(".tableA");t(".tableA").on("click",".action-delete",function(t){var e=t.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to delete Payment No. "+e+"?",function(){a(e,n)}).setHeader("<em>Delete Payment</em>")}),t(".tab-link").click(function(e){t(".tab-link.active").removeClass("active"),e.currentTarget.classList.add("active"),t("#item").val(null).trigger("change");var a=e.currentTarget.getAttribute("data-status"),n=BASE_URI+"payments/manager?status="+encodeURI(a);window.location.href=n}),t("#tableFilter").submit(function(e){e.preventDefault();var a=t(".tab-link.active").attr("data-status"),r=t(e.currentTarget).serialize(),s=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"&"+r;n.ajax.url(s).load(),window.history.pushState(null,null,BASE_URI+"payments/manager?status="+encodeURI(a)+"&"+r)}).trigger("submit"),t("#generatePayments").click(function(){console.log(BASE_URI),t.ajax({type:"POST",url:BASE_URI+"payments/generate_payments/",dataType:"json",success:function(){var e="AR/PR Payments";t(".btn-process").text(e),t(".btn-process").css("pointer-events","auto")},error:function(t){console.error(t)}})})})}(jQuery);