[{"type": "p", "name": "message", "label": "{ String or DOMElement }", "desc": "Sets the dialog contents", "defaultValue": "undefined", "script": "alertify.alert().set('message', 'This is a new message!').show();"}, {"type": "p", "name": "label", "label": "{ String }", "desc": "Sets the OK button label", "defaultValue": "alertify.defaults.glossary.ok", "script": "alertify.alert('label changed!').set('label', 'Alright!');"}, {"type": "e", "name": "onok", "label": "{ Function }", "desc": "Gets or sets a callback function to be invoked when the user clicks 'Ok' button or closes the dialog.", "defaultValue": "undefined", "script": "alertify.alert('a callback will be invoked on ok.').set('onok', function(closeEvent){ alertify.success('Ok');} );", "remark": "To prevent the dialog from closing, either set <code>closeEvent.cancel = true</code> or make your callback return <code>false</code> ."}]