<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">  
<script type="text/javascript">
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	title:{
		text: "Rail Roads Vs Land Area and Population"
	},
	axisX: {
		title:"Land Area (million sq. km)",
		maximum: 17,
		minimum: -.1
	},
	axisY:{
		title: "Rail Lines (in km)",
		gridColor: "lightgrey",
		tickColor: "lightgrey",
		lineThickness: 0,
		valueFormatString:"#,##0 km,.",
		interval: 50000
	},
	data: [{        
		type: "bubble",
		markerType: "triangle",
		toolTipContent: "<b>{label}</b><br/><b>Land Area:</b> {x}mn sq. km <br/><b>Rail Road:</b> {y} km<br/> <b>Population:</b>{z}mn",
		dataPoints: [
			{ x: 9.14, y: 228513, z:309.34, label:"US" },
			{ x: 16.37, y: 85292, z:141.92, label:"Russia" },
			{ x: 9.327, y: 66239, z:1337, label:"China" },
			{ x: 9.09, y: 58345, z:34.12, label:"Canada" },
			{ x: 8.45, y: 29817, z:194.94, label:"Brazil" },
			{ x: 7.68, y: 8615, z:22.29, label:"Australia" },
			{ x: 2.97, y: 63974, z:1224.61, label:"India" },
			{ x: 2.73, y: 25023, z:40.41, label:"Argentina" },
			{ x: 1.94, y: 26704, z:113.42, label:"Mexico" },
			{ x: 1.21, y: 22051, z:49.99, label:"SA" },
			{ x: .547, y: 33608, z:65.07, label:"France" },
			{ x: .241, y: 31471, z:62.23, label:"U.K" },
			{ x: .348, y: 33708, z:81.77, label:"Germany" },
			{ x: .364, y: 20035, z:127.45, label:"Japan" },
			{ x: .995, y: 5195, z:81.12, label:"Egypt" },
			{ x: .743, y: 5352, z:17.11, label:"Chile" }
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>