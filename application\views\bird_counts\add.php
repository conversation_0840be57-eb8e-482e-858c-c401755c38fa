<div class="bird-counts-container">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <div class="sect">
                        <div class="col-md-12 form-group">
                            <form id="birdCountForm" method="post" action="<?php echo site_url('bird_counts/add'); ?>" class="crud-box" accept-charset="utf-8">
                                <h1 class="crud-title">Add Bird Count</h1>
                                <fieldset>
                                    <h5 class="crud-subtitle"><span>Bird Count Information</span></h5>
                                    <br><br>
                                    <div class="row">
                                        <div class="col-md-4 form-group">
                                            <label for="grower_farm" class="control-label">Grower Farm Name/Place</label>
                                            <select id="grower_farm" class="form-control dropsearch" name="grower_farm" data-placeholder="Choose a grower farm...">
                                                <option></option>
                                                <?php foreach ($grower_farms as $farm): ?>
                                                    <option value="<?= htmlspecialchars($farm->customer_id) ?>" <?= set_select('grower_farm', $farm->customer_id); ?>>
                                                        <?= ucwords($farm->grower_farm_name) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="red-text flash-message">
                                                <?= form_error('grower_farm'); ?>
                                            </div>
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label for="hauling_log_id" class="control-label">Trip No.</label>
                                            <select class="form-control" id="hauling_log_id" name="hauling_log_id" disabled>
                                                <option value="">Select Trip No.</option>
                                            </select>
                                            <div class="red-text flash-message">
                                                <?= form_error('hauling_log_id'); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-2 form-group">
                                            <label for="live_heads" class="control-label">Live Heads</label>
                                            <input type="number" class="form-control" id="live_heads" name="live_heads" value="0">
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <label for="doa_heads" class="control-label">Dead on Arrival</label>
                                            <input type="number" class="form-control" id="doa_heads" name="doa_heads" value="0">
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <label for="daa_heads" class="control-label">Dead after Arrival</label>
                                            <input type="number" class="form-control" id="daa_heads" name="daa_heads" value="0">
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <label for="runts_heads" class="control-label">Runts</label>
                                            <input type="number" class="form-control" id="runts_heads" name="runts_heads" value="0">
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <label for="rejected_heads" class="control-label">Rejected</label>
                                            <input type="number" class="form-control" id="rejected_heads" name="rejected_heads" value="0">
                                        </div>
                                    </div>

                                    <h5 class="crud-subtitle mt-4"><span>Shackle & Weight Info</span></h5>
                                    <br>
                                    <div class="row">
                                        <div class="col-md-3 form-group">
                                            <label for="shackle_heads" class="control-label">Shackle Count</label>
                                            <input type="number" class="form-control" id="shackle_heads" name="shackle_heads" value="0">
                                             <div class="red-text flash-message">
                                                <?= form_error('shackle_heads'); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="net_weight_display" class="control-label">Net Weight (kg)</label>
                                            <input type="text" class="form-control" id="net_weight_display" name="net_weight_display" readonly>
                                            <input type="hidden" id="net_weight" name="net_weight">
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="alw_display" class="control-label">Average Live Weight</label>
                                            <input type="text" class="form-control" id="alw_display" readonly>
                                            <input type="hidden" id="alw" name="alw">
                                        </div>
                                    </div>

                                </fieldset>
                                <hr>
                                <div class="clearfix text-center">
                                    <button type="submit" class="btn btn-primary">Save</button>
                                    <a href="<?=site_url('bird_counts')?>" class="btn btn-light">Close</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>