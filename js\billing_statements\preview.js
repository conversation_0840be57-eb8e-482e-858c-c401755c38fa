+ function ($) {
    'use strict';
  
      function approve(invoiceId) {
          return $.ajax({
              url: BASE_URI + 'invoices/approve/' + invoiceId,
              type: 'POST',
              dataType: 'json'
          });
      }
      
      function review(invoiceId) {
          return $.ajax({
              url: BASE_URI + 'invoices/review/' + invoiceId,
              type: 'POST',
              dataType: 'json'
          });
      }
  
      $(function () {
          $('.action-print').click(function () {
              window.print();
      });
  
          $(document)
      .on('click', '.action-approve', function (event) {
        var invoiceId = event.currentTarget.getAttribute('data-id');
  
        alertify.confirm(
          'Are you sure you want to approve the Billing No. ' + invoiceId + '?',
          function () {
            approve(invoiceId);
            window.print();
            document.location.href = BASE_URI + "invoices/preview/" + invoiceId;
          }
        ).setHeader('<em>Approve Billing</em>');
      })
      .on('click', '.action-review', function() {
          let invoiceId = $(this).attr('data-invoiceid');
          
          alertify.confirm(
              'Are you sure you want to mark invoice as Reviewed?',
              function() {
                  $.when(review(invoiceId)).then(function(response) {
                      if (response.isSuccess) {
                          window.location.href = BASE_URI + "invoices/manager";
                      } else {
                          alertify.error(response.message);
                      }
                  });
              }
          ).setHeader('<em>Mark as Reviewed</em>')
      });
      });
  }(jQuery);
  