<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer's Name</label>

						<select class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach($customers as $customer):?>
								<option value="<?=$customer->id?>" <?=set_select('customer', $customer->id);?>><?=ucwords($customer->name)?></option>
							<?php endforeach;?>
						</select>
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="company_name" class="control-label">Company Name</label>

						<input type="text" name="company_name" class="form-control" value="<?=set_value('company_name');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('company_name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Complete Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>
				</div>
				
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="phone_no" class="control-label">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-2 form-group">
						<label for="poc_date" class="control-label">POC Date</label>

						<input type="text" name="poc_date" class="form-control datepicker" value="<?=set_value('poc_date', date('m/d/Y'));?>">
						<input type="hidden" name="grand_total" class="form-control" value="<?=set_value('grand_total');?>">

						<div class="red-text flash-message">
							<?=form_error('poc_date');?>
						</div>
					</div>

					<!-- <div class="col-md-2 form-group">
						<label for="reference" class="control-label">POC No.</label>

						<input type="hidden" name="latest_id" class="form-control" value="<?=set_value('latest_id', $latest_id);?>">
						<input type="text" name="reference" class="form-control" value="<?=set_value('reference');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('reference');?>
						</div>
					</div> -->
				</div>

            	<h3 class="crud-subtitle" style="border-bottom: 2px solid black;"></h3>

				<br>

				<div class="row">
					<div class="col-md-3 form-group">
						<label for="services">Product/Services Availed</label>
					</div>
					<div class="col form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<input type="checkbox" id="processing" name="processing" value="1">
								<label for="processing">Processing</label>
							</div>
						</div>
					</div>
					<div class="col form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<input type="checkbox" id="live_sell" name="live_sell" value="1">
								<label for="live_sell">Live Sell</label>
							</div>
						</div>
					</div>
					<div class="col form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<input type="checkbox" id="ice" name="ice" value="1">
								<label for="ice">Ice</label>
							</div>
						</div>
					</div>
					<div class="col form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<input type="checkbox" id="water" name="water" value="1">
								<label for="water">Water</label>
							</div>
						</div>
					</div>
					<div class="col-md-3 form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<input type="checkbox" id="others" name="other_services" value="1">
								<label for="others">Others:</label> 
								<!-- <input type="text" name="other_services_field" readonly> -->
								<input type="text" name="other_services_field" value="<?=set_value('other_services_field');?>" readonly>
							</div>
						</div>
					</div>
				</div>

				<br>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" colspan="6">TRANSACTION RECORD</th>
								</tr>
								<tr>
									<th class="text-center" style="width: 30%;">Description</th>

									<th class="text-center" style="width: 20%;">Quantity</th>

									<th class="text-center" style="width: 20%;">Unit Price</th>

									<th class="text-center" style="width: 20%;">Total Amount</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>
							<tbody>
								<?php
									if (!empty($poc_item_ids)):
										foreach ($poc_item_ids as $poc_item_id):
								?>
											<tr class="poc_item">
												<td>
													<!-- <?php $fn_poc_item = 'poc_item_' . $poc_item_id;?> -->
													<!-- <input type="hidden" name="<?=$fn_poc_item;?>" value="<?=set_value($fn_poc_item, $poc_item_id);?>"> -->

													<?php $fn_description = 'description_' . $poc_item_id;?>
													<select name="<?=$fn_description;?>" class="form-control dropsearch" data-placeholder="">
														<option></option>
														<?php
														foreach($item_for_poc as $item):?>
															<option value="<?= $item->id; ?>" <?=set_select($fn_description, $item->id); ?>><?=ucwords($item->item_name)?></option>
														<?php endforeach;?>
													</select>

													<div class="red-text flash-message">
														<?=form_error($fn_description);?>
													</div>
												</td>

												<td>
													<?php $fn_qty = 'qty_' . $poc_item_id;?>
													<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

													<div class="red-text flash-message">
														<?=form_error($fn_qty);?>
													</div>
												</td>

												<td>
													<?php $fn_unit_price = 'unit_price_' . $poc_item_id;?>
													<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price);?>" step="any">

													<div class="red-text flash-message">
														<?=form_error($fn_unit_price);?>
													</div>
												</td>
	`
												<td>
													<?php $fn_total_amount = 'total_amount_' . $poc_item_id;?>
													<input type="number" name="<?=$fn_total_amount;?>" class="form-control" value="<?=set_value($fn_total_amount);?>" step="any" readonly>

													<div class="red-text flash-message">
														<?=form_error($fn_total_amount);?>
													</div>
												</td>

												<td class="text-center">
													<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
												</td>
											</tr>
								<?php
										endforeach;
									else:
								?>
										<tr class="no-item">
											<td class="text-center" colspan="12">No items added yet.</td>
										</tr>
								<?php
									endif;
								?>
							</tbody>
							<tfoot>
								<tr>
									<th class="text-right" colspan="3">TOTAL AMOUNT DUE</th>
									<th class="text-right footer_total"></th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item" disabled>Add Item</button>
					</div>
				</div>

				<div class="row">
					<div class="col-md-1 form-group">
						<label for="remarks">Remarks:</label>
					</div>
					<div class="col form-group">
						<div class="row">
							<div class="col-md-12 form-group">
								<div class="row">
									<div class="col-md-12 form-group">
										<input type="checkbox" id="remarks_1" name="remarks_1" value="1">
										<span for="remarks_1">Order was made through call/text, confirmed by aforementioned customer and agreed to signed this form prior to the release of goods as proof of purchase.</span>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12 form-group">
										<input type="checkbox" id="remarks_2" name="remarks_2" value="1">
										<span for="remarks_2">Final due amount may vary depending on actual delivery.</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-2 form-group">
						<label for="other_remarks" class="custom-label">Other Remarks:</label>
					</div>
					<div class="col-md-10 form-group text-left">
						<input type="text" id="other_remarks" class="form-control-plaintext custom-border" style="border: none; border-bottom: 1px solid #000;" name="other_remarks" value="<?=set_value('other_remarks');?>">
						<!-- <textarea name="other_remarks" class="form-control-plaintext autosize custom-border" style="border: none; border-bottom: 1px solid #000;"><?= set_value('other_remarks'); ?></textarea> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('poc/manager')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>

<style>
	.custom-label {
		margin: 0;
		padding: 0;
		line-height: 1.5;
		vertical-align: middle;
	}

	.custom-border {
		border: none;
		border-bottom: 1px solid #000;
		padding: 0;
		margin: 0;
		height: auto;
		line-height: 1.5;
		vertical-align: middle;
	}
</style>
