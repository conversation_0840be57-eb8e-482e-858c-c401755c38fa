<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Processings extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('processings/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Receive processing Order for Approval
     */
    public function receive($processing_id)
    {
        $processing = $this->processing->get_by_id($processing_id) or show_404();

        $this->title = 'Receive processing No. ' . $processing->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processings/receive'
        ];

        $processing_items = $this->processing_item->get_by_processing($processing->id);

        $data = [
            'processing'          => $processing,
            'processing_items'    => $processing_items
        ];
        $this->build_content('default', 'processings/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add processing
     */
    public function add($schedule_hauling_id = null)
    {
        $schedule_hauling = $this->schedule_hauling->get_by_id($schedule_hauling_id) or show_404();
        $order_slip_id = $schedule_hauling->order_slip_id;
        $schedule_hauling_items = $this->schedule_hauling_item->select('', ['schedule_hauling_id' => $schedule_hauling->id, 'is_deleted' => 0]);

        foreach($schedule_hauling_items AS $index => $schedule_hauling_item) {
            $where = [
                'id' => $schedule_hauling_items[$index]->item_id,
                'is_deleted' => 0
            ];

            $item = $this->live_sell_item->select('', $where, 1);

            $schedule_hauling_items[$index]->item_name = $item?$item->name:"";
        }

        $processing_item_ids = $this->_get_processing_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/processings',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('processings/add'), $this->_get_processing_item_rules('', $processing_item_ids));
        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($processing_id = $this->_attempt_add($order_slip_id, $schedule_hauling_id))) {
            $new_path = FCPATH . 'assets/processings/' . $processing_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('processings/manager'));
        } else {
            $this->title = 'Processing';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/processings/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_processing_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['stock_keeping_unit_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $where = [
                'is_deleted' => 0
            ];
            $farms = $this->farm->select('', $where, '', 'name');

            $data = [
                'schedule_hauling'                => $schedule_hauling,
                'schedule_hauling_items'          => $schedule_hauling_items,
                'form_error'                    => $form_error,
                'processing_item_ids'     => $processing_item_ids,
                'customers'                     => $customers,
                'retain_items'                  => $retain_items,
                'farms'                          => $farms
            ];

            $this->build_content('default', 'processings/add', $data);
        }
    }

    /**
     * Edit processing
     */
    public function edit($processing_id)
    {
        $processing = $this->processing->get_by_id($processing_id) or show_404();

        $processing_items = $this->processing_item->get_by_processing($processing->id);
        $carcass_items = $this->_get_processing_item_data('carcass');
        $incidental_items = $this->_get_processing_item_data('incidental');
        $giblets_items = $this->_get_processing_item_data('giblets');
        $byproducts_items = $this->_get_processing_item_data('byproducts');
        
        // $retain_items = $this->_get_processing_item_data();

        $rules = $this->config->item('processings/edit');

        if (!$processing_item_ids = $this->_get_processing_item_ids()) {
            foreach($processing_items AS $processing_item) {
                $item = $this->stock_keeping_unit->select('', ['id' => $processing_item->stock_keeping_unit_id, 'is_deleted' => 0], 1);

                if($processing_item->classification === "carcass") {
                    $carcass_items[] = [
                        'id' => $processing_item->id,
                        'item_id' => $processing_item->stock_keeping_unit_id,
                        'item_name' => $item->name,
                        'classification' => $processing_item->classification,
                        'stock_keeping_unit_id' => $processing_item->stock_keeping_unit_id,
                        'crates' => $processing_item->crates,
                        'size_range' => $item->size_range,
                        'by_unit' => $item->by_unit,
                        'heads' => $processing_item->heads,
                        'kilos' => $processing_item->kilos,
                        'recovery' => $processing_item->recovery,
                    ];
                } 

                if ($processing_item->classification === "incidental") {
                    $incidental_items[] = [
                        'id' => $processing_item->id,
                        'item_id' => $processing_item->stock_keeping_unit_id,
                        'item_name' => $item->name,
                        'classification' => $processing_item->classification,
                        'stock_keeping_unit_id' => $processing_item->stock_keeping_unit_id,
                        'crates' => $processing_item->crates,
                        'size_range' => $item->size_range,
                        'by_unit' => $item->by_unit,
                        'heads' => $processing_item->heads,
                        'kilos' => $processing_item->kilos,
                        'recovery' => $processing_item->recovery,
                    ];
                } 
                
                if ($processing_item->classification === "giblets") {
                    $giblets_items[] = [
                        'id' => $processing_item->id,
                        'item_id' => $processing_item->stock_keeping_unit_id,
                        'item_name' => $item->name,
                        'classification' => $processing_item->classification,
                        'stock_keeping_unit_id' => $processing_item->stock_keeping_unit_id,
                        'crates' => $processing_item->crates,
                        'size_range' => $item->size_range,
                        'by_unit' => $item->by_unit,
                        'heads' => $processing_item->heads,
                        'kilos' => $processing_item->kilos,
                        'recovery' => $processing_item->recovery,
                    ];
                } 
                
                if ($processing_item->classification === "byproducts") {
                    $byproducts_items[] = [
                        'id' => $processing_item->id,
                        'item_id' => $processing_item->stock_keeping_unit_id,
                        'item_name' => $item->name,
                        'classification' => $processing_item->classification,
                        'stock_keeping_unit_id' => $processing_item->stock_keeping_unit_id,
                        'crates' => $processing_item->crates,
                        'size_range' => $item->size_range,
                        'by_unit' => $item->by_unit,
                        'heads' => $processing_item->heads,
                        'kilos' => $processing_item->kilos,
                        'recovery' => $processing_item->recovery,
                    ];
                }

                $processing_item_ids[] = $processing_item->id;
            }

            $existing_ids = array_map(function ($processing_item) {
                return $processing_item->id;
            }, $processing_items);

            $item_rules = $this->_get_processing_item_rules($processing->id, $existing_ids);
        } else {
            $item_rules = $this->_get_processing_item_rules($processing->id, $processing_item_ids);
        }

        $rules = array_merge($this->config->item('processings/edit'), $item_rules);

        if ($this->_validate_form($rules) && $this->_attempt_edit($processing)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('processings/preview/' . $processing->id));
            } else {
                redirect(site_url('processings/manager'));
            }
        } else {
            $this->title = 'Edit Processing';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/processings/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'processing' => $processing,
                'processing_items' => $processing_items,
                'processing_item_ids' => $processing_item_ids,
                'carcass_items' => $carcass_items,
                'giblets_items' => $giblets_items,
                'incidental_items' => $incidental_items,
                'byproducts_items' => $byproducts_items,
            ];

            $this->build_content('default', 'processings/edit', $data);
        }
    }

    /**
     * Approve processing
     */
    public function receive_processing($processing_id)
    {
        $where = [
            'id' => $processing_id,
            'is_deleted' => 0
        ];
        if (!$processing = $this->processing->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Processing is not found.'
            ];
        } elseif (!$this->_attempt_receive($processing)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Processing is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($processing)
    {
        $where = [
            'id' => $processing->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->processing->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete processing
     */
    public function delete($processing_id)
    {
        $where = [
            'id' => $processing_id,
            'is_deleted' => 0
        ];
        if (!$processing = $this->processing->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Processing is not found.'
            ];
        } elseif (!$this->_attempt_delete($processing)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Processing is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * processings
     */
    public function manager()
    {
        $this->title = "Processing";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processings/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'processings/manager', $data);
    }

    /**
     * processing Print Preview
     */
    public function preview($processing_id)
    {
        $processing = $this->processing->get_by_id($processing_id) or show_404();
        $processing_items = $this->processing_item->get_by_processing($processing_id);

        if (!$processing_item_ids = $this->_get_processing_item_ids()) {
            foreach($processing_items AS $processing_item) {
                $item = $this->stock_keeping_unit->select('', ['id' => $processing_item->stock_keeping_unit_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $processing_item->id,
                    'item_id' => $processing_item->stock_keeping_unit_id,
                    'item_name' => $item->name,
                    'classification' => $processing_item->classification,
                    'stock_keeping_unit_id' => $processing_item->stock_keeping_unit_id,
                    'crates' => $processing_item->crates,
                    'size_range' => $item->size_range,
                    'by_unit' => $item->by_unit,
                    'heads' => $processing_item->heads,
                    'kilos' => $processing_item->kilos,
                    'recovery' => $processing_item->recovery,
                ];

            

                $processing_item_ids[] = $processing_item->id;
            }

            $existing_ids = array_map(function ($processing_item) {
                return $processing_item->id;
            }, $processing_items);

            $item_rules = $this->_get_processing_item_rules($processing->id, $existing_ids);
        } else {
            $item_rules = $this->_get_processing_item_rules($processing->id, $processing_item_ids);
        }

        $this->title = 'Print Preview: Processing No. ' . $processing->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processings/preview',
        ];

        $where = [
            'id' => $processing->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);

        $data = [
            'processing' => $processing,
            'customer' => $customer,
            'processing_items' => $processing_items,
            'processing_item_ids' => $processing_item_ids,
            'retain_items' => $retain_items
        ];
        $this->build_content('default', 'processings/preview', $data);
    }

    /**
     * Get Info for processing
     */
    public function info($processing_id = null)
    {
        $data = [
            'processing' => $this->processing->get_by_id($processing_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid processings
     */
    public function partially_paid()
    {
        $data = [
            'processings' => $this->processing->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('farm');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('schedule_hauling');
        $this->load->model('schedule_hauling_item');
        $this->load->model('order_confirmation');
        $this->load->model('order_confirmation_item');
        $this->load->model('processing');
        $this->load->model('processing_item');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');
        $this->load->model('stock_keeping_unit');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_processing_item_ids($table_type = null)
    {
        $processing_item_ids = [];
        $match = $table_type?0:1;
        foreach ($_POST as $field => $value) {
            if($table_type) {
                if (strpos($field, 'processing_item_') !== 0 && strpos($field, 'classification_') === 0) {
                    if($table_type && $table_type === $value) {
                        $match = 1;
                    } else {
                        $match = 0;
                    }
                }
            }


            if($match === 1 && strpos($field, 'processing_item_') === 0) {
                $processing_item_ids[] = $value;
            }

            // if($table_type && strpos($field, 'classification_') !== $table_type) {
            //     array_pop($processing_item_ids);
            // }
        }

        return $processing_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_processing_item_rules($processing_id, $processing_item_ids)
    {
        $rules = [];

        foreach ($processing_item_ids as $processing_item_id) {
            $rules[] = [
                'field' => 'stock_keeping_unit_' . $processing_item_id,
                'label' => 'Stock Keeping Unit',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'crates_' . $processing_item_id,
                'label' => 'Crates',
                'rules' => 'required|trim|max_length[255]'
            ];
            
            $rules[] = [
                'field' => 'heads_' . $processing_item_id,
                'label' => 'Heads',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'kilos_' . $processing_item_id,
                'label' => 'Kilos',
                'rules' => 'required|trim|max_length[255]'
            ];
            
            $rules[] = [
                'field' => 'recovery_' . $processing_item_id,
                'label' => 'Recovery',
                'rules' => 'required|trim|max_length[255]'
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id, $schedule_hauling_id)
    {
        $this->db->trans_begin();

        if (!$processing_items = $this->_get_processing_item_data()) {
            $this->_error = 'No Processing Item added';
            return false;
        }

        if (!$processing_id = $this->_insert_processing($order_slip_id, $schedule_hauling_id) or !$this->_insert_processing_items($processing_id, $processing_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $processing_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_processing_item_data($table_type = null)
    {
        $data = [];
        $processing_item_ids = $this->_get_processing_item_ids($table_type);

        foreach ($processing_item_ids as $processing_item_id) {
            $data[] = [
                'id'                    => is_numeric($processing_item_id) ? $processing_item_id : null,
                'classification'        => $this->input->post('classification_' . $processing_item_id),
                'stock_keeping_unit_id' => $this->input->post('stock_keeping_unit_' . $processing_item_id),
                'crates'                => $this->input->post('crates_' . $processing_item_id),
                'heads'                 => $this->input->post('heads_' . $processing_item_id),
                'kilos'                 => $this->input->post('kilos_' . $processing_item_id),
                'recovery'              => $this->input->post('recovery_' . $processing_item_id),
                'added_by'              => $_SESSION['user']->id,
                'added_on'              => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Insert processing
     */
    protected function _insert_processing($order_slip_id, $schedule_hauling_id)
    {
        $customer_id = $this->input->post('customer');

        $processing_date = DateTime::createFromFormat('m/d/Y', $this->input->post('processing_date'));
        $processing_time = DateTime::createFromFormat('H:i:s', $this->input->post('processing_time'));

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $values = [
            'customer_id' => $customer_id,
            'schedule_hauling_id' => $schedule_hauling_id,
            'order_slip_id' => $order_slip_id,
            'processing_date' => $processing_date->format('Y-m-d'),
            'total_live_arrived' => $this->input->post('total_live_arrived') ?: 0,
            'total_death_on_arrival' => $this->input->post('total_death_on_arrival') ?: 0,
            'total_death_after_arrival' => $this->input->post('total_death_after_arrival') ?: 0,
            'la_in_kilos' => $this->input->post('la_in_kilos') ?: 0,
            'doa_in_kilos' => $this->input->post('doa_in_kilos') ?: 0,
            'daa_in_kilos' => $this->input->post('daa_in_kilos') ?: 0,
            'total_to_be_processed' => $this->input->post('total_to_be_processed') ?: 0,
            'lp_in_kilos' => $this->input->post('lp_in_kilos') ?: 0,
            'total_trips' => $this->input->post('total_trips') ?: 0,
            'alw' => $this->input->post('alw') ?: 0,
            'coops_loaded' => $this->input->post('coops_loaded') ?: 0,
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->processing->insert($values);
    }

    /**
     * Generate processing Items
     */
    protected function _insert_processing_items($processing_id, $processing_items)
    {

        foreach ($processing_items as $processing_item) {
            $values = [
                'processing_id' => $processing_id,
                'classification' => $processing_item['classification'],
                'stock_keeping_unit_id' => $processing_item['stock_keeping_unit_id'],
                'crates' => $processing_item['crates'],
                'heads' => $processing_item['heads'],
                'kilos' => $processing_item['kilos'],
                'recovery' => $processing_item['recovery'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];
            if(!$this->processing_item->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Old Generate processing Items
     */
    // protected function _generate_processing_items($processing_id, $processing_items)
    // {

    //     foreach ($processing_items as $processing_item) {
    //         $where = [
    //             'id' => $processing_item['item_id'],
    //             'is_deleted' => 0
    //         ];
    //         $item = $this->live_sell_item->select('', $where, 1);
    //         // if(($item->current_stock - (int)$processing_item['qty']) < 0) {
    //         //     $this->_error = "Can't processing item, it will lead to negative inventory.";
    //         //     return false;
    //         // }
    //         $where = [
    //             'id' => $processing_item['item_id']
    //         ];
    //         $values = [
    //             'updated_by' => $processing_item['added_by']
    //         ];

    //         if (!$this->live_sell_item->update($where, $values) ||
    //             !$this->processing_item->generate(
    //                 $processing_item['id'],
    //                 $processing_id,
    //                 $processing_item['item_id'],
    //                 $processing_item['qty'],
    //                 $processing_item['est_alw'],
    //                 $processing_item['est_due_amount'],
    //                 $processing_item['added_by']
    //             )
    //         ) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($processing)
    {
        $this->db->trans_begin();

        $where = [
            'processing_id' => $processing->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->processing_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$processing_items = $this->_get_processing_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Processing Item added';
            return false;
        } elseif(!$this->_attempt_delete_items($processing->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No Processing Item added';
            return false;
        } elseif (!$this->_revise_processing($processing->id, $processing_items) || !$this->_insert_processing_items($processing->id, $processing_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise processing
     */
    protected function _revise_processing($processing_id, $processing_items)
    {
        $processing_date = DateTime::createFromFormat('m/d/Y', $this->input->post('processing_date'));
        // $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($processing_items, function ($current_total, $processing_item) {
            if (isset($purchase_order_item['amount'])) {
                return $current_total + $processing_item['amount'];
            }
            return $current_total;
        });
        $discount = $this->input->post('discount') ?: null;
        
        $customer_id = $this->input->post('customer');
        
        $where = [
            'id' => $processing_id
        ];
        $processing = $this->processing->select('', $where, 1);
        $schedule_hauling_id = $processing->schedule_hauling_id;
        $order_slip_id = $processing->order_slip_id;        

        $values = [
            'customer_id' => $customer_id,
            'schedule_hauling_id' => $schedule_hauling_id,
            'order_slip_id' => $order_slip_id,
            'processing_date' => $processing_date->format('Y-m-d'),
            'total_live_arrived' => $this->input->post('total_live_arrived') ?: 0,
            'total_death_on_arrival' => $this->input->post('total_death_on_arrival') ?: 0,
            'total_death_after_arrival' => $this->input->post('total_death_after_arrival') ?: 0,
            'la_in_kilos' => $this->input->post('la_in_kilos') ?: 0,
            'doa_in_kilos' => $this->input->post('doa_in_kilos') ?: 0,
            'daa_in_kilos' => $this->input->post('daa_in_kilos') ?: 0,
            'total_to_be_processed' => $this->input->post('total_to_be_processed') ?: 0,
            'lp_in_kilos' => $this->input->post('lp_in_kilos') ?: 0,
            'total_trips' => $this->input->post('total_trips') ?: 0,
            'alw' => $this->input->post('alw') ?: 0,
            'coops_loaded' => $this->input->post('coops_loaded') ?: 0,
            'remarks' => $this->input->post('remarks') ?: null,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->processing->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($processing)
    {
        $where = [
            'id' => $processing->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->processing->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($processing)
    {
        $where = [
            'id' => $processing->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->processing->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($processing_id)
    {
        $where = [
            'processing_id' => $processing_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->processing_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if processing Item Belongs to processing
     */
    public function _is_processing_item($processing_item_id, $processing_id)
    {
        if (!is_numeric($processing_item_id)) {
            return true;
        }

        $where = [
            'id' => $processing_item_id,
            'processing_id' => $processing_id
        ];

        return !empty($this->processing_item->select('', $where, 1));
    }

    /**
     * Callback: Check if processing Term is Either Cash or Check
     */
    public function _is_processing_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
