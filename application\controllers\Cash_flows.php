<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class Cash_flows extends MYT_Controller
{

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('check_slip');
        $this->load->model('payment');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Cash Flow Statement';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'js/cash_flows/index'
        ];
        $this->build_content('default', 'cash_flows/index');
    }

    /**
     * Chart Data
     */
    public function chart_data()
    {
        $this->title = 'Cash Flow Statement';
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/canvasjs/canvasjs-chart-3.7.44/canvasjs.min',
            'js/cash_flows/index'
        ];

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('fromDate'));
        $date_from = $date_from ? $date_from->format('Y-m-d') : date('Y-m-d');
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->post('toDate'));
        $date_to = $date_to ? $date_to->format('Y-m-d') : date('Y-m-d');

        // Fetch Cash Out data using the model
        $cashOutData = $this->check_slip->get_check_slip_breakdown_by_day($date_from, $date_to);

        // Fetch revenue data using the model
        $cashInData= $this->payment->get_payment_breakdown_by_day($date_from, $date_to);

        $values = [
            'cash_in' => $cashInData,
            'cash_out' => $cashOutData
        ];
        echo json_encode(['status' => 200, 'data' => $values]);
    }

    /**
     * Export data to Excel
     */
    public function export_excel()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $borderMedium = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ]; 
        
        $borderThin = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $borderDouble = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DOUBLE,
                ],
            ],
        ];

        //Bottom Borders
        $sheet->getStyle('A3:C3')->applyFromArray($borderMedium);
        $sheet->getStyle('A5:C5')->applyFromArray($borderMedium);
        $sheet->getStyle('A6:C6')->applyFromArray($borderThin);

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('fromDate'));
        $date_from = $date_from->format('Y-m-d');
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->post('toDate'));
        $date_to = $date_to->format('Y-m-d');

        // Fetch revenue data using the model
        $payment_data = $this->payment->get_by_payment($date_from, $date_to);

        // Fetch expense data using the model
        $check_slip_data = $this->check_slip->get_by_check_slip($date_from, $date_to);

        // Create a worksheet for revenue data
        // $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Cash Flow Statement');

        $sheet->setCellValue('A1', 'Leyte Agri Ventures Corp.');
        $sheet->getStyle('A1')->getFont()->setSize(14)->setBold(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A1:C1");

        $sheet->setCellValue('A2', 'CASH FLOW STATEMENT');
        $sheet->getStyle('A2')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A2:C2");

        $start_date = "";
        $end_date = "";

        if ($date_from != "") {
            $start_date = date("F j, Y", strtotime($date_from));
        }
        
        if ($date_to != "") {
            $end_date = date("F j, Y", strtotime($date_to));
        }

        $sheet->setCellValue('A3', "As of " . $start_date . " to " . $end_date);
        $sheet->getStyle('A3')->getFont()->setSize(12)->setItalic(true);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 
        $sheet->mergeCells("A3:C3"); 

        // Set headers for revenue data
        $sheet->setCellValue('A5', 'Cash In');
        $sheet->getRowDimension('5')->setRowHeight(21);
        $sheet->getStyle('A5')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A5:C5");

        $sheet->setCellValue('A6', 'Mode of Payment');
        $sheet->getStyle('A6')->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A6:B6");

        $sheet->setCellValue('C6', 'Amount');
        $sheet->getStyle('C6')->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        // Populate revenue data
        $total_cash_in = 0;
        $row = 7;
        foreach ($payment_data as $item) {
            $sheet->setCellValue('B' . $row, $item->mop); 
            $sheet->setCellValue('C' . $row, "₱ ".number_format($item->total_paid, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $total_cash_in += $item->total_paid;
            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;
        
        $sheet->setCellValue('B'.$row, 'Total');
        $sheet->getStyle('B'.$row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C'.$row, "₱ ".number_format($total_cash_in, 2));
        $sheet->getStyle('C'.$row)->getFont()->setSize(10)->setBold(true);

        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for expense data
        $sheet->setCellValue('A' . $row, 'Cash Out');
        $sheet->getStyle("A$row:C$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:C$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Mode of Payment');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->getStyle('C' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderThin);

        // Populate expense data
        $total_cash_out = 0;
        $row++;
        foreach ($check_slip_data as $item) {
            $sheet->setCellValue('B' . $row, $item->mop);
            $sheet->setCellValue('C' . $row,  "₱ ". number_format($item->amount, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $total_cash_out += $item->amount;
            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_cash_out, 2));
        $sheet->getStyle('C'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for Income Statement
        $sheet->setCellValue('A' . $row, 'Cash Flow Statement');
        $sheet->getStyle("A$row:C$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:C$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Statement Report');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->getStyle('C' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderThin);

        // Populate Income Statement
        $row++;
        
        $sheet->setCellValue('B' . $row, 'Cash In');
        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_cash_in, 2));
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row++;
        
        $sheet->setCellValue('B' . $row, 'Cash Out');
        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_cash_out, 2));
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total Income');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format(($total_cash_in - $total_cash_out), 2));
        $sheet->getStyle('C'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        foreach ($sheet->getColumnIterator() as $column)
            $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);

        // Create a writer and send the Excel file as a download
        $writer = new Xlsx($spreadsheet);

        // Set the file name
        $filename = 'Cash Flow Statement.xlsx';

        // Send the Excel file to the browser
        $writer->save(FCPATH . 'assets/Cash Flow Statement.xlsx');
        echo json_encode(['status' => 200, 'filename' => 'Cash Flow Statement.xlsx']);
    }
}
