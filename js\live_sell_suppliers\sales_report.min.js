!function(f){"use strict";f(function(){setTimeout(()=>{f(".form-control").attr("autocomplete","off")},500);var c,e=(c=f(".tableA")).DataTable({dom:"Brtip",lengthChange:!0,paging:!1,paging:!1,filter:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],buttons:[{extend:"print",footer:!0,exportOptions:{stripHtml:!1}}],scrollX:!0,autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+c.attr("data-package"),footerCallback:function(t,e,a,r,o){var n,i,l=this.api(),u=c.attr("data-col-sum");void 0!==u&&-1<u&&(n=function(t){return"string"==typeof t?+t.replace(/[\$,]/g,""):"number"==typeof t?t:0},i=l.column(u).data().reduce(function(t,e){return n(t)+n(e)},0),f(l.column(u).footer()).html(f.fn.addNumericCommas(parseFloat(i).toFixed(2))))}});f("#tableFilter").submit(function(t){t.preventDefault();t=BASE_URI+"fetch/"+f(".tableA").attr("data-package")+"?"+f(t.currentTarget).serialize();e.ajax.url(t).load()})})}(jQuery);