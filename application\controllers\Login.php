<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Login extends MYT_Controller
{
    /**
     * @var string
     */
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Login
     */
    public function index()
    {
        if (!isset($_SESSION['user']) and // use and operator so form validation will run if there is no logged in user
            !$this->_validate_form($this->config->item('login/index')) || !$this->_attempt_login()
        ) {
            $this->title = 'Login';

            if (isset($_SESSION['form_error'])) {
                $form_error = $_SESSION['form_error'];
            } else {
                $form_error = $this->_error_msg();
            }

            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('login', 'login/index', $data);
        } elseif (in_array($_SESSION['user']->role_id, ['1', '8'])) {
            redirect(site_url('dressing_plant_dashboards/manager'));
        } elseif (in_array($_SESSION['user']->role_id, ['10', '11'])) {
            redirect(site_url('invoices/manager'));
        } elseif (in_array($_SESSION['user']->role_id, ['12'])) {
            redirect(site_url('invoices/manager'));
        } else {
            redirect(site_url('purchases/manager'));
        }
    }

    /**
     * Load pre-requisite models, config files, and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('user');
        $this->load->model('logins');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Login
     */
    protected function _attempt_login()
    {
        $where = [
            'username' => $this->input->post('username'),
            'is_deleted' => 0
        ];

        if (!$user = $this->user->select('', $where, 1)) {
            return !$this->_error = 'unregistered_user';
        } elseif (!password_verify($this->input->post('password'), $user->password)) {
            return !$this->_error = 'incorrect_password';
        } else {
            $values = [
                'user_id' => $user->id,
                'date_time' => date('Y-m-d H:i:s'),
                'ip' => $_SERVER['REMOTE_ADDR']
            ];

            $this->logins->insert($values);
            return $_SESSION['user'] = $user;
        }
    }
}
