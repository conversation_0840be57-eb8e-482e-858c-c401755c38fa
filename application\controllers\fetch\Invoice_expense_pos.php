<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Invoice_expense_pos extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            die(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Users
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db'   => $this->db->database,
            'host' => $this->db->hostname,
        ];
        $table       = 'purchase_item';
        $primary_key = 'purchase_item.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_po_no(),
            $this->_get_po_date(),
            $this->_get_items(),
            $this->_get_po_amount(),
        ];

        $joins = <<<EOT
purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
LEFT JOIN item ON item.id = purchase_item.item_id AND item.is_deleted = 0
EOT;

        $where = <<<EOT
purchase.is_deleted = 0
AND purchase_item.status = 'approved'
AND purchase_item.is_deleted = 0
AND purchase.status NOT IN ('pending','for_approval')
EOT;

        $account_name = $this->input->get('account_name', true);
        if (!empty($account_name)) {
            $where .= <<<EOT

AND purchase_item.account_type = '{$account_name}'
EOT;
        }

        $from = $this->input->get('from', true);
        $to   = $this->input->get('to', true);
        if (isset($from) and $from_date = date("Y-m-d", strtotime(urldecode($from))) and
            isset($to) and $to_date = date("Y-m-d", strtotime(urldecode($to)))
        ) {
            $where .= <<<EOT

AND purchase.purchase_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $group_by = <<<EOT
purchase_item.account_type, purchase_item.purchase_id
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'as'    => 'id',
            'db'    => 'purchase_item.purchase_id',
            'dt'    => 'DT_RowId',
            'field' => 'id',
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db'        => 'purchase_item.purchase_id',
            'as'        => 'class',
            'dt'        => 'DT_RowClass',
            'field'     => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase_item';
            },
        ];
    }

    /**
     * Get PO
     */
    protected function _get_po_no()
    {
        return [
            'db'    => 'purchase_item.purchase_id',
            'as'    => 'po_no',
            'dt'    => 0,
            'field' => 'po_no',
        ];
    }

    /**
     * Get PO Date
     */
    protected function _get_po_date()
    {
        return [
            'db'    => 'purchase.purchase_date',
            'as'    => 'po_date',
            'dt'    => 1,
            'field' => 'po_date',
        ];
    }

    /**
     * Get PO Items
     */
    protected function _get_items()
    {
        return [
            'db'    => 'GROUP_CONCAT(CONCAT("• ",item.name) SEPARATOR "<br>")',
            'as'    => 'items',
            'dt'    => 2,
            'field' => 'items',
        ];
    }

    /**
     * Get PO amount
     */
    protected function _get_po_amount()
    {
        return [
            'db'         => 'SUM(purchase_item.amount)',
            'as'         => 'amount',
            'dt'         => 3,
            'field'      => 'amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            },
        ];
    }

}
