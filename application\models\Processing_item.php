<?php
class Processing_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'processing_item';
        parent::__construct();
    }

    /**
     * Generate processing Item
     */
    public function generate($id, $processing_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO processing_item(id, processing_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, processing.id AS processing_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM processing
    CROSS JOIN item
    WHERE processing.id = ?
        AND processing.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    processing_item.id = LAST_INSERT_ID(processing_item.id),
    processing_item.qty = VALUES(processing_item.qty),
    processing_item.unit = VALUES(processing_item.unit),
    processing_item.updated_by = VALUES(processing_item.added_by),
    processing_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $processing_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get processing Item By ID
     */
    public function get_by_id($processing_item_id)
    {
        $sql = <<<EOT
SELECT processing_item.*, item.name AS item
FROM processing_item
LEFT JOIN item ON item.id = processing_item.item_id
WHERE processing_item.id = ?
    AND processing_item.is_deleted = 0
EOT;
        $binds = [$processing_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get processing Items by processing
     */
    public function get_by_processing($processing_id)
    {
        $sql = <<<EOT
SELECT processing_item.*, stock_keeping_unit.name AS item
FROM processing_item
LEFT JOIN stock_keeping_unit ON stock_keeping_unit.id = processing_item.stock_keeping_unit_id
WHERE processing_item.processing_id = ?
    AND processing_item.is_deleted = 0
EOT;
        $binds = [$processing_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
