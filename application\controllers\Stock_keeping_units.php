<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Stock_keeping_units extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->stock_keeping_unit_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('stock_keeping_units/manager'));
    }

    /**
     * Get units of item
     */
    public function get_data($stock_keeping_unit_id = null)
    {
        $where = [
            'id' => $stock_keeping_unit_id,
            'is_deleted' => 0
        ];
        if (!$stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'name' => $stock_keeping_unit->name,
                'size_range' => $stock_keeping_unit->size_range,
                'by_unit' => $stock_keeping_unit->by_unit,
                'type' => $stock_keeping_unit->type,
            ];
        }

        echo json_encode($response);
    }

    public function search_by_stock_keeping_unit()
    {
        $term = $this->input->get('term', true);

        $where = <<<EOT
CONCAT(name) LIKE '%{$term}%'
AND is_deleted = 0
EOT;

        $this->stock_keeping_unit->db->distinct();
        $stock_keeping_units = $this->stock_keeping_unit->select('', $where);

        $response =  $stock_keeping_units;

        echo json_encode($response);
    }

    /**
     * Add stock_keeping_unit
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('stock_keeping_units/add')) && $id = $this->_attempt_add()) {
            redirect(site_url('stock_keeping_units/manager'));
        } else {
            $this->title = 'Add SKU';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'items' => $items,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'stock_keeping_units/add', $data);
        }
    }

    /**
     * Edit stock_keeping_unit
     */
    public function edit($stock_keeping_unit_id)
    {
        $where = [
            'id' => $stock_keeping_unit_id,
            'is_deleted' => 0
        ];
        $stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1) or show_404();
        $this->stock_keeping_unit_name = $stock_keeping_unit->name;
        $where = ['is_deleted' => 0];

        if ($this->_validate_form($this->config->item('stock_keeping_units/edit')) and $this->_attempt_edit($stock_keeping_unit)) {
            redirect(site_url('stock_keeping_units/manager'));
        } else {
            $this->title = 'Edit SKU';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $items = $this->stock_keeping_unit->select('', $where);

            $data = [
                'form_error' => $form_error,
                'stock_keeping_unit' => $stock_keeping_unit,
                'items' => $items,
            ];
            $this->build_content('default', 'stock_keeping_units/edit', $data);
        }
    }

    /**
     * Delete stock_keeping_unit
     */
    public function delete($stock_keeping_unit_id)
    {
        $where = [
            'id' => $stock_keeping_unit_id,
            'is_deleted' => 0
        ];
        if (!$stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Stock Keeping Unit is not found.'
            ];
        } elseif (!$this->_attempt_delete($stock_keeping_unit)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Stock Keeping Unit is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Check stock_keeping_unit remaining Credit Limit
     */
    public function check_credit($stock_keeping_unit_id)
    {
        $used_credit = $this->stock_keeping_unit->used_credit($stock_keeping_unit_id) ?: 0;

        $where = [
            'id' => $stock_keeping_unit_id,
            'is_deleted' => 0
        ];

        if (!$stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Stock Keeping Unit is not found.'
            ];
        } else {
            $response = [
                'message' => 'Stock Keeping Unit is found.',
                'credit_limit' => $stock_keeping_unit->credit_limit ? (float)$stock_keeping_unit->credit_limit - (float)$used_credit->credit : 0
            ];
        }

        echo json_encode($response);
    }

    /**
     * stock_keeping_units
     */
    public function manager()
    {
        $this->title = 'Stock Keeping Unit';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/stock_keeping_units/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'stock_keeping_units/manager', $data);
    }


    /**
     * stock_keeping_units SOA
     */
    public function soa()
    {
        $this->title = 'stock_keeping_units';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'js/stock_keeping_units/soa'
        ];

        $this->build_content('default', 'stock_keeping_units/soa');
    }

    /**
     * stock_keeping_units with negative credit
     */
    public function credit_report()
    {
        $this->title = 'Credit Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/stock_keeping_units/credit_report'
        ];

        $this->build_content('default', 'stock_keeping_units/credit_report');
    }

    /**
     * stock_keeping_units
     */
    public function stock_keeping_units_sale()
    {
        $this->title = 'Sales Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/select2/dist/js/select2.full',
            'js/stock_keeping_units/sales_report'
        ];

        $this->build_content('default', 'stock_keeping_units/sales_report');
    }

    /**
     * Get Info of stock_keeping_unit
     */
    public function info($stock_keeping_unit_id = '')
    {
        $where = [
            'id' => $stock_keeping_unit_id,
            'is_deleted' => 0
        ];
        if (!$stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Stock Keeping Unit is not found.'
            ];
        } else {
            $response = [
                'message' => 'Stock Keeping Unit is found.',
                'stock_keeping_unit' => $stock_keeping_unit
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of stock_keeping_unit by name
     */
    public function info_by_name($stock_keeping_unit_name = '')
    {
        $where = [
            'name' => urldecode($stock_keeping_unit_name),
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        if (!$stock_keeping_unit = $this->stock_keeping_unit->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Stock Keeping Unit is not found.'
            ];
        } else {
            $response = [
                'message' => 'Stock Keeping Unit is found.',
                'stock_keeping_unit' => $stock_keeping_unit
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('stock_keeping_unit');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'size_range' => $this->input->post('size_range'),
            'by_unit' => $this->input->post('by_unit')?:null,
            'type' => $this->input->post('type'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$stock_keeping_unit_id = $this->stock_keeping_unit->insert($values)) {
            $this->_error = 'server_error';
        }

        return $stock_keeping_unit_id;
    }

    /**
     * Callback: Check if sku name exists
     */
    public function _is_sku($sku_name)
    {
        $where = [
            'name' => $sku_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->stock_keeping_unit_name) === strtolower($sku_name)) {
            return true;
        }

        return empty($this->stock_keeping_unit->select('', $where, 1));
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($stock_keeping_unit)
    {
        $where = [
            'id' => $stock_keeping_unit->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'size_range' => $this->input->post('size_range'),
            'by_unit' => $this->input->post('by_unit')?:1,
            'type' => $this->input->post('type'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->stock_keeping_unit->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($stock_keeping_unit)
    {
        $where = [
            'id' => $stock_keeping_unit->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $stock_keeping_unit->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->stock_keeping_unit->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
