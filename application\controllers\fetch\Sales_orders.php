<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sales_orders extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('sales_order');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch sales_orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'sales_order';
        $primary_key = 'sales_order.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_number(),
            $this->_get_sales_order_date(),
            $this->_get_sales_order_number(),
            $this->_get_customer(),
            $this->_get_grand_total(),
            $this->_get_paid_amount(),
            $this->_get_balance(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
sales_order
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order.customer_id
LEFT JOIN user ON user.id = sales_order.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        $where = <<<EOT
sales_order.is_deleted = 0
EOT;

        if ($status === 'purchased') {
            $where .= <<<EOT

AND sales_order.status = 'purchased'
EOT;
        } elseif ($status === 'received') {
            $where .= <<<EOT

AND sales_order.status = 'received'
EOT;
        } elseif ($status === 'all') {
            $where .= <<<EOT

AND sales_order.status IS NOT NULL
EOT;
        } elseif ($status === 'pending'){
            $where .= <<<EOT

AND sales_order.status = 'pending'
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'sales_order.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'sales_order.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'sales_order';
            }
        ];
    }

    /**
     * Get sales_order date
     */
    protected function _get_sales_order_number()
    {
        return [
            'db' => 'sales_order.id',
            'as' => 'sales_order_number',
            'dt' => 0,
            'field' => 'sales_order_number'
        ];
    }

    /**
     * Get order slip number
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'sales_order.order_slip_id',
            'as' => 'order_slip_number',
            'dt' => 1,
            'field' => 'order_slip_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get sales_order date
     */
    protected function _get_sales_order_date()
    {
        return [
            'db' => 'DATE_FORMAT(sales_order.sales_order_date, "%b %d, %Y")',
            'as' => 'sales_order_date',
            'dt' => 2,
            'field' => 'sales_order_date'
        ];
    }

    /**
     * Get sales_order customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 3,
            'field' => 'customer'
        ];
    }

    /**
     * Get grand_total
     */
    protected function _get_grand_total()
    {
        return [
            'db' => 'sales_order.grand_total',
            'dt' => 4,
            'field' => 'grand_total',
            'formatter' => function ($d, $row) {
                return $d;
            }
        ];
    }

    /**
     * Get paid_amount
     */
    protected function _get_paid_amount()
    {
        return [
            'db' => 'sales_order.paid_amount',
            'dt' => 5,
            'field' => 'paid_amount',
            'formatter' => function ($d, $row) {
                return $d;
            }
        ];
    }

    /**
     * Get balance
     */
    protected function _get_balance()
    {
        return [
            'db' => 'sales_order.balance',
            'dt' => 6,
            'field' => 'balance',
            'formatter' => function ($d, $row) {
                return $d;
            }
        ];
    }

    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'sales_order.remarks',
            'dt' => 7,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'sales_order.status',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 8,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    // /**
    //  * Get File
    //  */
    // protected function _get_file()
    // {
    //     return [
    //         'db' => 'sales_order.id',
    //         'as' => 'file',
    //         'dt' => 6,
    //         'field' => 'file',
    //         'formatter' => function ($d, $row) {
    //             $files = $this->sales_order->get_file_by_id($d);
    //             $res = '<ul>';

    //             $files = explode('🔥', $files[0]->attachment);
    //             $files = array_map('trim', $files);
    //             $files = array_filter($files);

    //             foreach ($files as $i => $file) {
    //                 $res .= '<a href="' . base_url('assets/sales_orders/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
    //             }

    //             $res .= '</ul>';

    //             return $res;
    //         }
    //     ];
    // }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'sales_order.id',
            'as' => 'actions',
            'dt' => 9,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        // $res .= anchor('sales_order_receives/add/' . $d, 'Proceed PO', 'title="Proceed PO" class="dropdown-item text-left"');
                        // $res .= anchor('sales_order_receives/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        $res .= anchor('sales_orders/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= '<button class="action-payment dropdown-item text-left" role="button" data-id="' . $d . '">Add Payment</button>';
                        $res .= anchor('sales_orders/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } elseif($row['status'] === 'received') {
                        $res .= anchor('sales_orders/preview/' . $d, 'Print Preview Received', 'title="Print Preview" class="dropdown-item text-left"');
                    } elseif($row['status'] === 'purchased') {
                        $res .= anchor('sales_orders/preview/' . $d, 'Print Preview Purchased', 'title="Print Preview" class="dropdown-item text-left"');
                    } else {
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    }
                } elseif($_SESSION['user']->role_id === '10') {
                    if($row['status'] != 'received') {
                        $res .= anchor('sales_order_receives/add/' . $d, 'Receive', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                    $res .= anchor('sales_orders/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                } else {
                    // if($row['status'] != 'received') {
                    //     $res .= anchor('sales_order_receives/add/' . $d, 'Receive', 'title="Print Preview" class="dropdown-item text-left"');
                    // }
                    if($row['status'] != 'received') {
                        $res .= anchor('sales_orders/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    }
                    $res .= anchor('sales_orders/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    if($row['status'] == 'pending') {
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    }
                }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
