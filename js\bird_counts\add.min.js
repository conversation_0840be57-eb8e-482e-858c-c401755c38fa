$(function(){function t(){var t=parseFloat($("#net_weight").val())||0,e=parseFloat($("#shackle_heads").val())||0;if(t>0&&e>0){var i=t/e,o=a(i,4);$("#alw_display").val(o),$("#alw").val(i)}else $("#alw_display").val(""),$("#alw").val("")}function a(t,a=0){return isNaN(t)?"":Number(t).toLocaleString("en-US",{minimumFractionDigits:a,maximumFractionDigits:a})}$("#grower_farm").on("change",function(){var t=$(this).val(),a=$("#hauling_log_id");if(a.prop("disabled",!0).html('<option value="">Loading trips...</option>'),$("#net_weight_display").val(""),$("#net_weight").val(""),t){var e=BASE_URI.endsWith("/")?BASE_URI:BASE_URI+"/",i=e+"bird_counts/get_trips_by_customer/"+encodeURIComponent(t);$.ajax({url:i,type:"GET",dataType:"json",cache:!1,success:function(t){if(t&&t.success&&t.trips&&t.trips.length>0){var e='<option value="">Select Trip No.</option>';$.each(t.trips,function(t,a){var i="Trip "+a.trip_no;a.formatted_date&&a.formatted_time&&(i+=" - "+a.formatted_date+" "+a.formatted_time),e+='<option value="'+a.hauling_log_id+'" data-net-weight="'+a.net_weight+'">'+i+"</option>"}),a.html(e).prop("disabled",!1)}else a.html('<option value="">No trips available</option>')},error:function(t,e,i){a.html('<option value="">Error loading trips</option>')}})}else a.html('<option value="">Select Trip No.</option>')}),$("#hauling_log_id").on("change",function(){var e=$(this).find("option:selected"),i=$(this).val();if(i&&e.val()){var o=e.data("net-weight"),l=a(o,2);$("#net_weight_display").val(l),$("#net_weight").val(o),t()}else $("#net_weight_display").val(""),$("#net_weight").val(""),$("#alw_display").val(""),$("#alw").val("")}),$("#shackle_heads").on("input keyup change",function(){t()}),$("#grower_farm").select2({placeholder:"Choose a grower farm...",allowClear:!0})});