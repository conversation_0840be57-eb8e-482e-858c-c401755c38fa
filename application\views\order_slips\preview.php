<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><large><b>Customer : </b></large> <?=$order_slip->customer;?></p>
					<p class="text-left"><large><b>Address : </b></large> <?=$customer->address;?></p>
					<p class="text-left"><large><b>TIN : </b></large> <?=$customer->tin_number;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><large><b>Doc No. : </b></large> <?=$order_slip->id;?></p>
					<p class="text-left"><large><b>Date : </b></large> <?=date('m/d/Y', strtotime($order_slip->order_slip_date));?></p>
				</div>
			</div>

            	<h5 class="crud-subtitle"><span></span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-left" style="width: 15%;">Item</th>

									<th class="text-left" style="width: 10%;">Qty</th>

									<th class="text-left" style="width: 10%;">Unit</th>

									<th class="text-left" style="width: 10%;">ALW</th>

									<th class="text-right" style="width: 10%;">Unit Price</th>

									<th class="text-right" style="width: 15%;">Price</th>
								</tr>
							</thead>

							<tbody>
								<?php
									$total_quantity = 0;
									if (!empty($order_slip_item_ids)):
										$counter = 0;
										foreach ($order_slip_item_ids as $i => $order_slip_item_id):
											$total_quantity += $retain_items[$counter]['est_due_amount'];
								?>
											<tr class="order_slip_item">
												<td class="text-left"><?=$retain_items[$counter]['item_name'];?></td>

												<td class="text-left"><?=number_format($retain_items[$counter]['qty'], 2);?></td>

												<td class="text-left"><?=$retain_items[$counter]['unit'];?></td>

												<td class="text-left"><?=number_format($retain_items[$counter]['pref_alw'], 2);?></td>

												<td class="text-right"><?=number_format($retain_items[$counter]['current_price'], 2);?></td>

												<td class="text-right"><?=number_format($retain_items[$counter]['est_due_amount'], 2);?></td>
											</tr>
								<?php
										$counter++;
										endforeach;
									else:
								?>
										<tr class="no-item">
											<td class="text-center" colspan="7">No items added yet.</td>
										</tr>
								<?php
									endif;
								?>
										<!-- <tr class="order_slip_item">
											<td></td>

											<td class="text-right"><strong>Total: </td>

											<td class="text-right"><strong><?=floatval($total_quantity);?></td>
										</tr> -->
							</tbody>
							<tfoot>
								<tr class="item">
									<td colspan="4">
											<table width="100%">
												<tr>
													<td class="text-left"><strong>Remarks</strong></td>
													<?php if (!empty($order_slip->remarks)): ?>
														<td class="text-left"><?=$order_slip->remarks;?></td>
													<?php endif;?>
												</tr>
											</table>
										
									</td>

									<td colspan="2">
										<table width="100%">
											<tr>
												<td class="text-right"><strong>Total</strong></td>
												<td class="text-right"><?=number_format($total_quantity, 2);?></td>
											</tr>
										</table>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

				<?php if(!empty($order_slip->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>Order Slip Attachments</span></h5>
						<?php $attachments = explode('🔥',$order_slip->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/order_slips/'.$order_slip->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$order_slip->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Prepared By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('order_slips');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="order_slip_id" value="<?=$order_slip->id?>">
		</form>
	</div>
</div>