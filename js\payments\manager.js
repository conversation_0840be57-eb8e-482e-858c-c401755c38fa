+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			lengthChange: false,
			dom: 'Brtip',
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'text-left',
				targets: [0, 1, 3, 4]
			}, {
				className: 'text-right',
				targets: 2
			}, {
				className: 'dt-action',
				targets: -1
			},
			{
				'searchable'    : true,
				'targets'       : [1]
			}],
			processing: true,
			serverSide: true,
			buttons: [{
		        extend: 'print',
				className: 'btn btn-secondary',
		        footer: true,
                exportOptions: {
                    stripHtml: false
                }
			},
			{
				extend: 'excel',
			  	className: 'btn btn-success ml-2',
			  	text: 'Download Excel File',
					footer: true,
					exportOptions: {
				 		columns: ':not(:last-child)'
					}
			}],
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(paymentId, dataTable) {
		$.when(deleteRow(paymentId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(paymentId) {
		return $.ajax({
			url: BASE_URI + 'payments/delete/' + paymentId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			// var cols = $(event.currentTarget).closest('tr.payment').find('td');
			// var account_name = typeof cols[1] !== 'undefined' ? cols[1].innerHTML : '';
			var paymentId = event.currentTarget.getAttribute('data-id');

			alertify.confirm(
				'Are you sure you want to delete Payment No. ' + paymentId + '?',
				function () {
					deleteAndReload(paymentId, dataTable);
				}
			).setHeader('<em>Delete Payment</em>');
		});

		$(".tab-link").click(function (event) {
            $(".tab-link.active").removeClass("active");
            event.currentTarget.classList.add("active");
            $("#item").val(null).trigger("change");

            var paymentstatus = event.currentTarget.getAttribute("data-status");
            var address =
                BASE_URI + "payments/manager?status=" + encodeURI(paymentstatus);
            window.location.href = address;
        });

		$("#tableFilter").submit(function (event) {
            event.preventDefault();

            var paymentStatus = $(".tab-link.active").attr("data-status");
            var queries = $(event.currentTarget).serialize();

            var url =
                BASE_URI + "fetch/" + $(".tableA").attr("data-package") + "&" + queries;

            dataTable.ajax.url(url).load();

            window.history.pushState(
                null,
                null,
                BASE_URI +
                    "payments/manager?status=" +
                    encodeURI(paymentStatus) +
                    "&" +
                    queries 
            );
        }).trigger('submit');

		$('#generatePayments').click(function () {

			console.log(BASE_URI);
	
			$.ajax({
				type: 'POST',
				url: BASE_URI + 'payments/generate_payments/',
				dataType: 'json',
				success: function () {
					var text = 'AR/PR Payments';
					$('.btn-process').text(text);
					$('.btn-process').css('pointer-events', 'auto');
				},
				error: function (error) {
					console.error(error);
				},
			});
		  });
	});
}(jQuery);
