<?php
class Bank extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'bank';
        parent::__construct();
    }

    /**
     * Generate bank
     */
    public function generate($name, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO bank(name, added_by, added_on)
VALUES(?, ?, ?)
ON DUPLICATE KEY UPDATE
id = LAST_INSERT_ID(id),
updated_by = VALUES(added_by),
is_deleted = 0
EOT;
        $binds = [$name, $added_by, $added_on];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
