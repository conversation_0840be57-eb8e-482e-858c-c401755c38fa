<?php
class Enrolled_check extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'enrolled_check';
        parent::__construct();
    }

    /**
     * Get Check Enrollment Batches
     */
    public function get_batch_info($bank_id, $batch_id = null)
    {
        $extra_where = '';
        $binds = [$bank_id, $bank_id];

        if ($batch_id !== null) {
            $extra_where = 'AND batch_id = ?';
            array_push($binds, $batch_id, $batch_id);
        }

        $sql = <<<EOT
SELECT enrollment_batch.bank_id, enrollment_batch.batch_id, enrollment_batch.first, enrollment_batch.last, available_doc_no.available AS remaining
FROM (
    SELECT bank_id, batch_id, MIN(doc_no) AS first, MAX(doc_no) AS last
    FROM enrolled_check
    WHERE bank_id = ?
        {$extra_where}
    GROUP BY bank_id, batch_id
) enrollment_batch
LEFT JOIN (
    SELECT bank_id, batch_id, COUNT(doc_no) AS available
    FROM enrolled_check
    WHERE bank_id = ?
        AND status = "available"
        {$extra_where}
    GROUP BY bank_id, batch_id
) available_doc_no ON (
    enrollment_batch.bank_id = available_doc_no.bank_id
    AND enrollment_batch.batch_id = available_doc_no.batch_id
)
EOT;

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
