<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Payees extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('payees/manager'));
    }

    /**
     * Add payee
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('payees/add')) && $this->_attempt_add()) {
            redirect(site_url('payees/manager'));
        } else {
            $this->title = 'Add Payee';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'payees/add', $data);
        }
    }

    /**
     * Edit payee
     */
    public function edit($payee_id)
    {
        $where = [
            'id' => $payee_id,
            'is_deleted' => 0
        ];
        $payee = $this->payee->select('', $where, 1) or show_404();

        if ($this->_validate_form($this->config->item('payees/edit')) && $this->_attempt_edit($payee)) {
            redirect(site_url('payees/manager'));
        } else {
            $this->title = 'Edit Payee';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'payee' => $payee
            ];
            $this->build_content('default', 'payees/edit', $data);
        }
    }

    /**
     * Delete payee
     */
    public function delete($payee_id)
    {
        $where = [
            'id' => $payee_id,
            'is_deleted' => 0
        ];
        if (!$payee = $this->payee->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Payee is not found.'
            ];
        } elseif (!$this->_attempt_delete($payee)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Payee is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * payees
     */
    public function manager()
    {
        $this->title = 'Payees';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/payees/manager'
        ];

        $this->build_content('default', 'payees/manager');
    }

    /**
     * Get Info of payee
     */
    public function info($payee_id = '')
    {
        $where = [
            'id' => $payee_id,
            'is_deleted' => 0
        ];
        if (!$payee = $this->payee->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Payee is not found.'
            ];
        } else {
            $response = [
                'message' => 'Payee is found.',
                'payee' => $payee
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('payee');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date("Y-m-d H:i:s")
        ];
        if (!$payee_id = $this->payee->insert($values)) {
            $this->_error = 'server_error';
        }

        return $payee_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($payee)
    {
        $where = [
            'id' => $payee->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date("Y-m-d H:i:s")
        ];

        if (!$is_updated = $this->payee->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($payee)
    {
        $where = [
            'id' => $payee->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $payee->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'update_on' => date("Y-m-d H:i:s")
        ];

        if (!$is_deleted = $this->payee->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
