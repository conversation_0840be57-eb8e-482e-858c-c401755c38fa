!function(e){"use strict";function t(t){var a=e(t),n=(a.find("thead th"),e(".tab-link.active").attr("data-status")),r=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")+"?status="+n});return r}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"schedule_haulings/receive_schedule_hauling/"+t,type:"POST",dataType:"json"})}function r(t,a){e.when(u(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function u(t){return e.ajax({url:BASE_URI+"schedule_haulings/delete/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-receive",function(t){var r=e(t.currentTarget).closest("tr.schedule_hauling").find("td"),u=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the schedule_hauling "+u+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>receive schedule_hauling</em>")}).on("click",".action-delete",function(t){var a=e(t.currentTarget).closest("tr.schedule_hauling").find("td"),u=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to delete Schedule Hauling "+u+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,n)}).setHeader("<em>Delete Schedule Hauling</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var a=BASE_URI+"fetch/schedule_haulings?status="+t.currentTarget.getAttribute("data-status"),r=BASE_URI+"schedule_haulings/manager?status="+t.currentTarget.getAttribute("data-status");console.log(a),window.history.pushState(null,null,r),n.ajax.url(a).load()})})}(jQuery);