+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			lengthChange: false,
			dom: 'Brtip',
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'text-left',
				targets: [0, 3]
			}, {
				className: 'text-right',
				targets: 2
			}, {
				className: 'dt-action',
				targets: 4
			},
      {
        'searchable'    : true,
        'targets'       : [1]
    }],
			processing: true,
			serverSide: true,
			buttons: [{
		        extend: 'print',
						className: 'btn btn-secondary',
		        footer: true,
                exportOptions: {
                    stripHtml: false
                }
			},
			{
					extend: 'excel',
			  	className: 'btn btn-success ml-2',
			  	text: 'Download Excel File',
					footer: true,
					exportOptions: {
				 		columns: 'th'
					}
			}],
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package'),
			footerCallback: function (row, data, start, end, display) {
				var api = this.api();
				var colSum = $table.attr("data-col-sum");

				if (typeof colSum !== "undefined" && colSum > -1) {
					// returns the numeric value of data
					var intVal = function (i) {
						return typeof i === "string"
							? i.replace(/[\$,]/g, "") * 1
							: typeof i === "number"
							? i
							: 0;
					};

					// total of all data in table
					var grandTotal = api
						.column(colSum)
						.data()
						.reduce(function (a, b) {
							return intVal(a) + intVal(b);
						}, 0);

					$(api.column(colSum).footer()).html(
						$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
					);
				}
			},
		});

		return dataTable;
	}

	$(function () {
		let dataTable = createDataTable('.tableA');

    $('#tableFilter')
      .submit(function (event) {
        event.preventDefault();

        var queries = $(event.currentTarget).serialize();

        var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + queries;
        dataTable.ajax.url(url).load();

        window.history.pushState(null, null, BASE_URI + 'so_payments/manager?' + queries);
      }).trigger('submit');
	});
}(jQuery);
