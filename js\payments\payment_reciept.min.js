!function(n){"use strict";function e(e,t){return n.ajax({url:BASE_URI+"payments/mark_payment/",type:"POST",data:{payment_id:e,pr_no:t},dataType:"json"})}n(function(){n(".action-print").click(function(){var t=n('input[name="payment_id"]').val(),r=n('input[name="pr_no"]').val();alertify.confirm("Are you sure you want to print?",function(n){n?e(t,r).done(function(){window.print(),window.location.reload()}).fail(function(n,e,t){console.error("Error during mark_acknowledge:",e,t);let r=JSON.parse(n.responseText);alertify.error(r.message)}):alertify.closeLogOnClick(!0)}).setting({title:"Confirm Payment Receipt!",message:'<div style="text-align: center;">Are you sure you want to print this payment receipt?</div>'})})})}(jQuery);