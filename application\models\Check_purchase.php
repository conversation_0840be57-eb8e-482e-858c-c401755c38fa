<?php
class Check_purchase extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'check_purchase';
        parent::__construct();
    }

    /**
     * Generate Checked Purchase
     */
    public function generate($id, $check_id, $purchase_id, $amount, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO check_purchase(id, check_id, purchase_id, amount, added_by, added_on) (
    SELECT ? AS id, purchase.id AS check_id, purchase.id AS purchase_id, ? AS amount, ? AS added_by, ? AS added_on
    FROM check_slip
    CROSS JOIN purchase
    WHERE check_slip.id = ?
        AND check_slip.is_deleted = 0
        AND purchase.id = ?
        AND purchase.status IN ("approved", "printed")
        AND purchase.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    check_purchase.amount = VALUES(check_purchase.amount),
    check_purchase.updated_by = VALUES(check_purchase.added_by),
    check_purchase.updated_on = VALUES(check_purchase.added_on),
    check_purchase.is_deleted = 0
EOT;
        $binds = [$id, $amount, $added_by, $added_on, $check_id, $purchase_id];

        return $this->advanced_query($sql, $binds);
    }
        /**
     * Generate Checked PO
     */
    public function get_by_check($check_id)
    {
        $sql = <<<EOT
    SELECT * FROM check_purchase
    LEFT JOIN purchase ON purchase.id = check_purchase.purchase_id
    WHERE check_purchase.check_id = ?
    AND check_purchase.is_deleted = 0
EOT;
        $binds = [$check_id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
