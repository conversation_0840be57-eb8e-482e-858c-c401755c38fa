+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			},
			{
                targets: 4,
				render: function (data) {
					return data == 1 ? '<span style="color: green;">Active</span>' : '<span style="color: red;">Inactive</span>';
				}
            }],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(supplierId, dataTable) {
		$.when(deleteRow(supplierId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}


	function inactiveAndReload(supplierId, dataTable) {
		$.when(inactiveRow(supplierId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}


	function deleteRow(supplierId) {
		return $.ajax({
			url: BASE_URI + 'suppliers/delete/' + supplierId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function inactiveRow(supplierId) {
		return $.ajax({
			url: BASE_URI + 'suppliers/inactive/' + supplierId,
			type: 'POST',
			dataType: 'json'
		});
	}


	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete, .action-inactive', function (event) {
			var cols = $(event.currentTarget).closest('tr.supplier').find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';
			var actionType = $(event.currentTarget).hasClass('action-delete') ? 'delete' : 'inactive';
			if (actionType === 'delete') {
				alertify.confirm(
					'Are you sure you want to delete ' + name + '?',
					function () {
						var supplierId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(supplierId, dataTable);
					}
				).setHeader('<em>Delete Supplier</em>');
			} else if (actionType === 'inactive') {
				var buttonText = $(event.currentTarget).text().trim();
            	var statusAction = buttonText === 'Set Active' ? 'ACTIVE' : 'INACTIVE';
				var statusColor = buttonText === 'Set Active' ? 'green' : 'red';
				alertify.confirm(
					'Are you sure you want to set status of supplier ' + name + ' to <span style="color:' + statusColor + ';">' + statusAction + '</span>?',
					function () {
						var supplierId = event.currentTarget.getAttribute('data-id');
						inactiveAndReload(supplierId, dataTable);
					}
				).setHeader('<em>Set Status Supplier</em>');
			}
		});

		$('select[name^="po_id"]').on('change', function (event) {
			var poId = event.currentTarget.value;
			$('a.btn_go').attr('href', BASE_URI + 'supplier_invoices/issue/' + poId);
		});
	});
}(jQuery);
