<!DOCTYPE html>
<html>

<head>
    <title>BDO Print Check (no Voucher)</title>

    <style>
        @media screen {
            html, body {
                font-size: 10pt!important;
            }
        }

        @media print {
            @page {
                size: 8in 3in;
                size: portrait;
                margin: 0;
            }

            html, body {
                font-size: 10pt!important;
                width: 8in;
                height: 3in;
            }
        }
	</style>
</head>

<body>
    <div style="position: absolute; margin-top: 10mm; margin-right: 16mm; width: 45mm; text-align: left; padding-left: 10mm; right: 0;">
        <?php
            $check_date = new DateTime($check->check_date);
            // Get the formatted date
            $formatted_date = $check_date->format('m/d/Y');
            
            // Get the year and add spaces between each character
            $year = $check_date->format('Y');
            $day = $check_date->format('d');
            $month = $check_date->format('m');
            $spaced_year = implode(' ', str_split($year));
            $spaced_day = implode(' ', str_split($day));
            $spaced_month = implode(' ', str_split($month));
            
        // Output the formatted date with CSS classes
        echo '<span class="date-part">' . $month . '</span>';
        echo '<span class="date-part dash-white">-</span>'; // Adding a separator with spaces
        echo '<span class="date-part">' . $day . '</span>';
        echo '<span class="date-part dash-white">-</span>'; // Adding a separator with spaces
        echo '<span class="date-part">' . $year . '</span>';
        ?>
    </div>
    <style>
        .date-part {
            letter-spacing: 0.8em; /* Adjust the value as needed */
        }
        .dash-white {
            color: white; /* Set the color to white */
        }
    </style>

    <div style="position: absolute; margin-top: 20mm; margin-left: 30mm; width: 115mm;">
        <?php
            echo '*** ' . $check->supplier . ' ***';
        ?>
    </div>

    <div style="position: absolute; margin-top: 20mm; margin-right: 10mm; width: 45mm; text-align: left; padding-left: 10mm; right: 0;">
        <?php
            echo number_format($check->amount, 2);
        ?>
    </div>

    <div style="position: absolute; margin-top: 28mm; margin-left: 30mm; width: 180mm;">
        <?php
            $whole = floor($check->amount);
            $decimal = round(($check->amount - $whole) * 100);
            $figures = array_filter([
                $whole > 0 ? spellout($whole) : null,
                $decimal ? $decimal . '/100' : null
            ]);

            echo '*** ' . implode(' and ', $figures) . ' pesos only' . ' ***';
        ?>
    </div>

    <div style="position: absolute; margin-top: 48mm; margin-right: 50mm; width: 55mm; text-align: left; padding-left: 10mm; right: 0;">
        <?=strtoupper($check->sig_1);?>
    </div>

    <div style="position: absolute; margin-top: 48mm; width: 55mm; text-align: left; padding-left: 10mm; right: 0;">
        <?=strtoupper($check->sig_2);?>
    </div>

	<script src="<?=base_url('lib/jquery/jquery-3.3.1.min.js');?>"></script>

    <script>
        function markPrinted() {
            $.ajax({
                url: '<?=site_url('checks/printed/' . $check->id);?>',
                type: 'POST'
            })
            .done(function () {
                window.print();
            })
            .fail(function () {
                alert('Something went wrong. Please refresh page.');
            });
        }

        markPrinted();
    </script>
</body>

</html>
