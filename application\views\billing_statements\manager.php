<div class="tab">
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_AUDIT])): ?>
		<button type="button" class="tab-link <?=$status === 'approved' ? 'active' : '';?>" data-status="approved">Approved</button>
		<button type="button" class="tab-link <?=$status === 'cancelled' ? 'active' : '';?>" data-status="cancelled">Cancelled</button>
	<?php else:?>
		<button type="button" class="tab-link <?=$status === 'approved' ? 'active' : '';?>" data-status="approved">Approved</button>
	<?php endif;?>
</div>
<div class="col topbtn">
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_BILLING])): ?>
		<div class="dropdown float-right">
			<a href="<?= site_url('billing_statements/add'); ?>" class="btn btn-primary">Add New Billing Statement</a>
		</div>
	<?php endif;?>

	<div class="title"><?= $title; ?></div>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="billing_statements">
		<table class="table table-hover tableA" data-package="billing_statements">
			<thead>
				<tr>
					<th>Billing No.</th>
					<th>Customer Name</th>
					<th>Billing Date</th>
					<th>Attachments</th>
					<th>Remarks</th>
					<th>Grand Total</th>
					<th></th>
				</tr>
			</thead>

			<tbody></tbody>
		</table>
	</div>
</div>
