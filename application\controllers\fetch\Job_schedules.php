<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Job_schedules extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch job_schedules
     */
    public function index()
    {
        $db_config = 'mysql:host=' . $this->db->hostname . ';' . 'dbname=' . $this->db->database;
                  
        $connect = new PDO($db_config, $this->db->username , $this->db->password);

        $query =  ('SELECT 
                        job_schedule.id,  
                        customer.name, 
                        CAST(CAST(date as datetime) + time_start as datetime) as start, 
                        CAST(CAST(date as datetime) + time_end as datetime) as end,
                        qty_birds,
                        area,
                        details
                    FROM `job_schedule` 
                    LEFT JOIN `customer`
                    ON customer.id = job_schedule.customer_id
                    WHERE job_schedule.is_deleted = 0'
        );
    
        $statement = $connect->prepare($query);
    
        $statement->execute();
    
        $result = $statement->fetchAll();
    
        $data = array();
        
        foreach($result as $row) {
            $data[] = array(
            'id'   => $row["id"],
            'title'   => $row["name"],
            'start'   => $row["start"],
            'end'   => $row["end"],
            'qty_birds'   => $row["qty_birds"],
            'area'   => $row["area"],
            'details'   => $row["details"]
            );
        }
    
        echo json_encode($data);
    }
}
