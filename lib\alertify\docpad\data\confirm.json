[{"type": "p", "name": "message", "label": "{ String or DOMElement }", "desc": "Sets the dialog contents", "defaultValue": "undefined", "script": "alertify.confirm().set('message', 'This is a new message!').show();"}, {"type": "p", "name": "labels", "label": "{ Object }", "desc": "Sets the OK/Cancel button's labels", "defaultValue": "{ok: alertify.defaults.glossary.ok, cancel: alertify.defaults.glossary.cancel}", "script": "alertify.confirm('labels changed!').set('labels', {ok:'Alright!', cancel:'Naa!'});"}, {"type": "p", "name": "defaultFocus", "label": "{ String }", "desc": "Gets or sets the name of the button to recieve default focus.", "defaultValue": "'ok'", "script": "alertify.confirm('Cancel button is focused by default.').set('defaultFocus', 'cancel');"}, {"type": "p", "name": "reverseButtons", "label": "{ <PERSON><PERSON><PERSON> }", "desc": "Gets or sets a value indicating whether to reverse the dialog buttons order.", "defaultValue": "false", "script": "alertify.confirm('Buttons are reversed').set('reverseButtons', true);", "toggle": {"value": true}}, {"type": "e", "name": "onok", "label": "{ Function }", "desc": "Gets or sets a callback function to be invoked when the user clicks 'Ok' button.", "defaultValue": "undefined", "script": "alertify.confirm('a callback will be invoked on ok.').set('onok', function(closeEvent){ alertify.success('Ok');} );", "remark": "To prevent the dialog from closing, either set <code>closeEvent.cancel = true</code> or make your callback return <code>false</code> ."}, {"type": "e", "name": "oncancel", "label": "{ Function }", "desc": "Gets or sets a callback function to be invoked when the user clicks 'Cancel' button or closes the dialog.", "defaultValue": "undefined", "script": "alertify.confirm('a callback will be invoked on cancel.').set('oncancel', function(closeEvent){ alertify.error('Cancel');} );", "remark": "To prevent the dialog from closing, either set <code>closeEvent.cancel = true</code> or make your callback return <code>false</code> ."}, {"type": "m", "name": "autoOk", "label": "chainable", "desc": "Automatically confirms the dialog after n seconds.", "params": [{"name": "duration", "type": "Number", "desc": "time in seconds."}], "script": "alertify.confirm('Will be confirmed in 5 seconds', function(){alertify.success('confirmed')}).autoOk(5);"}, {"type": "m", "name": "autoCancel", "label": "chainable", "desc": "Automatically cancels the dialog after n seconds.", "params": [{"name": "duration", "type": "Number", "desc": "time in seconds."}], "script": "alertify.confirm('Will be declined in 5 seconds').autoCancel(5).set('oncancel', function(){alertify.error('declined');});"}]