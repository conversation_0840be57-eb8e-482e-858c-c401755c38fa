!function(t){"use strict";function e(e){var a=t(e),r=a.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],buttons:[{extend:"print",footer:!0,exportOptions:{stripHtml:!1}}],scrollX:!0,autoWidth:!1,paging:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package"),footerCallback:function(e,r,o,n,i){var u=this.api(),l=a.attr("data-col-sum");if(void 0!==l&&l>-1){var c=function(t){return"string"==typeof t?1*t.replace(/[\$,]/g,""):"number"==typeof t?t:0},f=u.column(l).data().reduce(function(t,e){return c(t)+c(e)},0);t(u.column(l).footer()).html(t.fn.addNumericCommas(parseFloat(f).toFixed(2)))}}});return r}t(function(){setTimeout(()=>{t(".form-control").attr("autocomplete","off")},500);var a=e(".tableA");t("#tableFilter").submit(function(e){e.preventDefault();var r=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+t(e.currentTarget).serialize();a.ajax.url(r).load()})})}(jQuery);