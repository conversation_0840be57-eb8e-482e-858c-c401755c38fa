<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	theme: "light2",
	title: {
		text: "Daily Temperature Variation in Bengaluru - July 2017"
	},
	axisX: {
		valueFormatString: "DD MMM"
	},
	axisY: {
		title: "Temperature (in °C)"
	},
	toolTip: {
		shared: true
	},
	legend: {
		dockInsidePlotArea: true,
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [{
		type: "rangeArea",
		markerSize: 0,
		name: "Temperature Range",
		showInLegend: true,
		toolTipContent: "{x}<br><span style='color:#6D77AC'>{name}</span><br>Min: {y[1]} °C<br>Max: {y[0]} °C",
		dataPoints: [
			{ x: new Date(2017, 6, 1), y: [30, 19] },
			{ x: new Date(2017, 6, 2), y: [30, 21] },
			{ x: new Date(2017, 6, 3), y: [29, 21] },
			{ x: new Date(2017, 6, 4), y: [28, 20] },
			{ x: new Date(2017, 6, 5), y: [29, 20] },
			{ x: new Date(2017, 6, 6), y: [29, 20] },
			{ x: new Date(2017, 6, 7), y: [27, 21] },
			{ x: new Date(2017, 6, 8), y: [26, 20] },
			{ x: new Date(2017, 6, 9), y: [30, 20] },
			{ x: new Date(2017, 6, 10), y: [30, 21] },
			{ x: new Date(2017, 6, 11), y: [30, 21] },
			{ x: new Date(2017, 6, 12),y: [29, 21] },
			{ x: new Date(2017, 6, 13),y: [27, 20] },
			{ x: new Date(2017, 6, 14),y: [27, 20] },
			{ x: new Date(2017, 6, 15),y: [25, 20] },
			{ x: new Date(2017, 6, 16),y: [29, 20] },
			{ x: new Date(2017, 6, 17),y: [28, 20] },
			{ x: new Date(2017, 6, 18),y: [27, 21] },
			{ x: new Date(2017, 6, 19),y: [27, 21] },
			{ x: new Date(2017, 6, 20),y: [29, 21] },
			{ x: new Date(2017, 6, 21),y: [29, 20] },
			{ x: new Date(2017, 6, 22),y: [31, 20] },
			{ x: new Date(2017, 6, 23),y: [30, 21] },
			{ x: new Date(2017, 6, 24),y: [30, 20] },
			{ x: new Date(2017, 6, 25),y: [31, 21] },
			{ x: new Date(2017, 6, 26),y: [30, 21] },
			{ x: new Date(2017, 6, 27),y: [31, 21] },
			{ x: new Date(2017, 6, 28),y: [31, 21] },
			{ x: new Date(2017, 6, 29),y: [31, 21] },
			{ x: new Date(2017, 6, 30), y: [31, 21] },
			{ x: new Date(2017, 6, 31), y: [31, 22] }
		]
	}]
});
chart.render();

addAverages();

function addAverages() {
	var dps = [];
	for(var i = 0; i < chart.options.data[0].dataPoints.length; i++) {
		dps.push({
			x: chart.options.data[0].dataPoints[i].x,
			y: (chart.options.data[0].dataPoints[i].y[0] + chart.options.data[0].dataPoints[i].y[1]) / 2
		});
	}
	chart.options.data.push({
		type: "line",
		name: "Average",
		showInLegend: true,
		markerType: "triangle",
		markerSize: 0,
		yValueFormatString: "##.0 °C",
		dataPoints: dps
	});
	chart.render();
}

function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;
	}
	e.chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>