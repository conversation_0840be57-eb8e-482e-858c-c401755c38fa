+function ($) {
    'use strict';

    function createDataTable(selector) {
        var $table = $(selector);

        var dataTable = $table.DataTable({
            destroy: true, // Allow reinitialization
            language: {
                infoFiltered: ''
            },
            lengthChange: false,
            info: false,
            paging: false,
            searching: true,
            ordering: true,
            order: [[0, 'asc']],
            processing: true,
            serverSide: true,
            dom: 'frt',
            ajax: {
                url: BASE_URI + 'fetch/Carcasses',
                type: 'POST',
                dataSrc: 'data',
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX error:', error, thrown);
                    console.log('Response:', xhr.responseText);
                    alertify.error('Error loading data. Please try again.');
                }
            },
            columnDefs: [
                { targets: 0, data: 'sku', title: 'SKU' },
                { targets: 1, data: 'size', title: 'Size', className: 'text-center' },
                { targets: 2, data: 'range', title: 'Range', className: 'text-center' },
                { targets: 3, data: 'no', title: 'NO', className: 'text-center' },
                { targets: 4, data: 'pcs', title: 'PCS', className: 'text-center' },
                { targets: 5, data: 'kgs', title: 'KGS', className: 'text-center' },
                { targets: 6, data: 'actions', title: '', orderable: false, searchable: false, className: 'text-right' }
            ]
        });

        return dataTable;
    }

    function deleteAndReload(carcassId, dataTable) {
        $.when(deleteRow(carcassId)).then(
            function () {
                dataTable.ajax.reload(null, false);
            },
            function (jqXHR) {
                dataTable.ajax.reload(null, false);
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message || 'Delete failed.');
            }
        );
    }

    function deleteRow(carcassId) {
        return $.ajax({
            url: BASE_URI + 'Carcasses/delete/' + carcassId,
            type: 'POST',
            dataType: 'json'
        });
    }

    $(function () {
        var dataTable = createDataTable('#carcasses-table');

        $('#carcasses-table').on('click', '.action-delete', function (event) {
            var $row = $(event.currentTarget).closest('tr');
            var sku = $row.find('td:first').text(); // First column = SKU
            var carcassId = $(event.currentTarget).data('id');

            alertify.confirm(
                'Are you sure you want to delete carcass record for SKU "' + sku + '"?',
                function () {
                    deleteAndReload(carcassId, dataTable);
                }
            ).setHeader('<em>Delete Carcass</em>');
        });
    });

}(jQuery);