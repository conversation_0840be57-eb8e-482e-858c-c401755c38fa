<!DOCTYPE html>
<html lang="ar">
<!-- <html lang="ar"> for arabic only -->
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">

    <style>
        @media print {
            @page {
                margin: 0; /* important to logo margin */
                size: 10mm 100mm; /* adjust the size according to your printer's paper size */
            }
            html {
                direction: ltr;
            }
            html,body{margin:0;padding:0}
            #printContainer {
                width: 50%;
                text-align: justify;
                position: relative;
            }

           .text-center{text-align: center;}

           .no-print, .no-print *{
               display: none !important;
           }
        }

        .btn-dark {
            color: #fff;
            background-color: #343a40;
            border-color: #343a40;
        }

        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        }

        a {
        text-decoration: none !important;
        }
    </style>

</head>
<body onload="window.print()">
<h5 id="logo" style="margin-top:0" class="text-center"><span></span></h5>

<div id='printContainer'>
    <table style="width:100%">
        <tbody>
        <tr>
            <td class="text-left" style="text-align:left;"><b><?=date('n/j/Y h:i A', strtotime($pos_invoice->added_on));?></b></td>
            <td class="text-right" style="text-align:right;"><b>Sales Receipt # <?=$pos_invoice->id?></b></td>
        </tr>
        </tbody>
    </table>

    <br>
    <div id="slogan" class="text-center mt-0"><b>LEYTE AGRI VENTURES CORPORATION</b></div>
    <div class="text-center m-0 p-0">BRGY. 69 ABINONG</div>
    <div class="text-center m-0 p-0">TACLOBAN CITY</div>
    <div class="text-center m-0 p-0">0939 917 2550</div>
    <br />
    <div class="text-left m-0 p-0">Bill To: <?=$pos_invoice->customer !== null ? $pos_invoice->customer : $pos_invoice->customer_name?></div>
    <div class="text-left m-0 p-0">Cashier: <?=$pos_invoice->cashier?></div>
    

    <table style="width:100%">
        <tbody>
        <tr>
            <td><b>Item Name</b></td>
            <td class="text-right" style="text-align:right;"><b>Qty</b></td>
            <td class="text-right" style="text-align:right;"><b>Price</b></td>
            <td class="text-right" style="text-align:right;"><b>Ext Price</b></td>
        </tr>
        <tr>
            <td colspan="4"><hr></td>
        </tr>
        <?php foreach ($pos_invoice_items as $pos_invoice_item): ?>
        <tr>
            <td class="text-left" style="text-align:left;"><?=$pos_invoice_item->item?></td>
            <td class="text-right" style="text-align:right;"><?=$pos_invoice_item->qty?></td>
            <td class="text-right" style="text-align:right;">P<?=number_format($pos_invoice_item->amount, 2)?></td>
            <td class="text-right" style="text-align:right;">P<?=number_format($pos_invoice_item->amount, 2)?></td>
        </tr>
        <?php endforeach;?>
        <br>
        </tbody>
        <tfoot>
        <tr>
            <td colspan="4"><hr></td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td class="text-right" style="text-align:right;">Subtotal:</td>
            <td class="text-right" style="text-align:right;">P<?=number_format($pos_invoice->subtotal, 2)?></td>
        </tr>
        <tr>
            <td colspan="2"  class="text-right" style="text-align:right;">Local Sales Tax</td>
            <td class="text-right" style="text-align:right;">0 % Tax:</td>
            <td class="text-right" style="text-align:right;">+ P0.00</td>
        </tr>
        <tr>
            <td colspan="3" class="text-right" style="text-align:right;"><b>RECEIPT TOTAL:</b></td>
            <td class="text-right" style="text-align:right;"><b>P<?=number_format($pos_invoice->total - $pos_invoice->discount, 2)?></b></td>
        </tr>
        <tr>
            <td><br></td>
            <td><br></td>
        </tr>
        <tr>
            <td colspan="2" class="text-right" style="text-align:right;">Amount Tendered:</td>
            <td class="text-left" style="text-align:left;">P<?=number_format($pos_invoice->total_paid, 2)?></td>
            
            <td></td>
        </tr>
        <?php if ($pos_invoice->terms === 'cash'): ?>
        <tr>
            <td colspan="2" class="text-right" style="text-align:right;">Change Given:</td>
            <td class="text-left" style="text-align:left;">P<?=number_format($pos_invoice->total_change, 2)?></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="2" class="text-right" style="text-align:right;">Cash:</td>
            <td class="text-left" style="text-align:left;">P<?=number_format($pos_invoice->total_paid, 2)?></td>
            <td></td>
            <td></td>
        </tr>
        <?php endif;?>
        </tfoot>
    </table>

    <div>
        <p style="text-align:center;">
        Thanks for shopping with us!<br>
        </p>
    </div>
    <br><br><br>
    <hr>
    <hr>
    <hr>

</div>

<a href="<?=base_url('pos_invoices/' . $pos_invoice->terms)?>" class="btn btn-dark no-print">Close</a>

</body>
</html>
