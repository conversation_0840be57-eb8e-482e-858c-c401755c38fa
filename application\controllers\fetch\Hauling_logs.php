<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Hauling_logs extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch hauling logs data for DataTable
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'hauling_log';
        $primary_key = 'hauling_log.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_grower_farm(),
            $this->_get_trip_no(),
            $this->_get_waf_rs_no(),
            $this->_get_truck_name(),
            $this->_get_truck_plate(),
            $this->_get_eta(),
            $this->_get_ata_time(),
            $this->_get_weighed_time(),
            $this->_get_remarks(),
            $this->_get_gross_weight(),
            $this->_get_tare_weight(),
            $this->_get_net_weight(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id AND schedule_list.is_deleted = 0
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id AND schedule.is_deleted = 0
LEFT JOIN customer ON customer.id = schedule.customer_id AND customer.is_deleted = 0
EOT;

        $where = <<<EOT
hauling_log.is_deleted = 0
EOT;

        // Add date filter 
        $date_filter = $this->input->get('date_filter', true);
        if ($date_filter) {
            $filter_date = DateTime::createFromFormat('m/d/Y', $date_filter);
            if ($filter_date) {
                $formatted_date = $filter_date->format('Y-m-d');
                $where .= <<<EOT

AND DATE(hauling_log.added_on) = '{$formatted_date}'
EOT;
            }
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    protected function _get_id()
    {
        return [
            'db' => 'hauling_log.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    protected function _get_class()
    {
        return [
            'db' => 'hauling_log.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'hauling_log';
            }
        ];
    }

    protected function _get_grower_farm()
    {
        return [
            'db' => 'customer.name',
            'as' => 'grower_farm',
            'dt' => 0,
            'field' => 'grower_farm'
        ];
    }

    protected function _get_trip_no()
    {
        return [
            'db' => 'schedule_list.trip_no',
            'dt' => 1,
            'field' => 'trip_no'
        ];
    }

    protected function _get_waf_rs_no()
    {
        return [
            'db' => 'schedule.waf_rs_no',
            'dt' => 2,
            'field' => 'waf_rs_no'
        ];
    }

    protected function _get_truck_name()
    {
        return [
            'db' => 'hauling_log.truck_name',
            'dt' => 3,
            'field' => 'truck_name'
        ];
    }

    protected function _get_truck_plate()
    {
        return [
            'db' => 'hauling_log.truck_plate',
            'dt' => 4,
            'field' => 'truck_plate'
        ];
    }

    protected function _get_eta()
    {
        return [
            'db' => 'CONCAT(schedule.eta_date, " ", schedule_list.eta_time)',
            'as' => 'eta',
            'dt' => 5,
            'field' => 'eta',
            'formatter' => function ($d, $row) {
                return $d ? date('F j, Y g:i A', strtotime($d)) : '';
            }
        ];
    }

    protected function _get_ata_time()
    {
        return [
            'db' => 'hauling_log.ata_time',
            'dt' => 6,
            'field' => 'ata_time',
            'formatter' => function ($d, $row) {
                return $d ? date('g:i A', strtotime($d)) : '';
            }
        ];
    }

    protected function _get_weighed_time()
    {
        return [
            'db' => 'hauling_log.weighed_time',
            'dt' => 7,
            'field' => 'weighed_time',
            'formatter' => function ($d, $row) {
                return $d ? date('g:i A', strtotime($d)) : '';
            }
        ];
    }

    protected function _get_remarks()
    {
        return [
            'db' => 'hauling_log.remarks',
            'dt' => 8,
            'field' => 'remarks'
        ];
    }

    protected function _get_gross_weight()
    {
        return [
            'db' => 'hauling_log.gross_weight',
            'dt' => 9,
            'field' => 'gross_weight',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    protected function _get_tare_weight()
    {
        return [
            'db' => 'hauling_log.tare_weight',
            'dt' => 10,
            'field' => 'tare_weight',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    protected function _get_net_weight()
    {
        return [
            'db' => 'hauling_log.net_weight',
            'dt' => 11,
            'field' => 'net_weight',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    protected function _get_actions()
    {
        return [
            'db' => 'hauling_log.id',
            'as' => 'actions',
            'dt' => 12,
            'field' => 'id',
            'formatter' => function ($d, $row) {
                return $this->_get_actions_html($d);
            }
        ];
    }

    protected function _get_actions_html($hauling_log_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';
        if (isset($_SESSION['user']->role_id) && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $hauling_log_id . '">Delete</button>';
        }
        $res .= '</div>';
        $res .= '</div>';
        return $res;
    }
}