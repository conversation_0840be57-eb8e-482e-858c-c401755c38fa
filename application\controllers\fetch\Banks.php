<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Banks extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch banks
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'bank';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_print_template(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
bank
LEFT JOIN check_template ON check_template.id = bank.print_template
EOT;

        $where = <<<EOT
bank.is_deleted = 0
EOT;

        exit(json_encode(SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)));
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'bank.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'bank.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'bank';
            }
        ];
    }

    /**
     * Get name
     */
    protected function _get_name()
    {
        return [
            'db' => 'bank.name',
            'as' => 'bank',
            'dt' => 0,
            'field' => 'bank'
        ];
    }

    /**
     * Get print template
     */
    protected function _get_print_template()
    {
        return [
            'db' => 'check_template.name',
            'as' => 'print_template',
            'dt' => 1,
            'field' => 'print_template'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'bank.id',
            'as' => 'actions',
            'dt' => 2,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res  = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-boundary=".dt-asc-wrapper">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                    $res .= anchor('checks/enroll/' . $d, 'Check Enrollment', 'title="Check Enrollment" class="dropdown-item text-left"');
                    $res .= anchor('banks/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
