!function(t,e){"use strict";function n(e){return t.ajax({url:BASE_URI+"invoices/partially_paid/"+e,type:"GET",dataType:"json"})}function a(e){var n=t(i());l(n,e);var a=t(".item-table"),o=a.find(".paid_invoice");0===o.length&&a.find(".no-item").remove(),a.find("tbody").append(n)}function i(){var t=o();return'<tr class="paid_invoice"><td><input type="hidden" name="paid_invoice_'+t+'" value="'+t+'"><select name="invoice_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="customer_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="invoice_date_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="terms_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="number" name="total_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="paid_amount_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="balance_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-dr">Delete</button></td></tr>'}function o(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function l(e,n){e.find('select[name^="invoice"]').select2({data:n,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function r(e){return t.ajax({url:BASE_URI+"invoices/info/"+e,type:"GET",dataType:"json"})}function c(t,n){var a="",i="",o="",l=0,r=0,c=0;null!==n&&(a=n.customer,i=e.format(e.parse(n.invoice_date),"MMM DD, YYYY"),o=n.terms,l=parseFloat(n.total),isNaN(l)&&(l=0),r=parseFloat(n.paid_amount),isNaN(r)&&(r=0),c=l-r,isNaN(c)&&(c=0)),t.find('input[name^="customer_"]').val(a),t.find('input[name^="invoice_date"]').val(i),t.find('input[name^="terms"]').val(o),t.find('input[name^="total"]').val(l>0?l.toFixed(2):0),t.find('input[name^="paid_amount"]').val(r>0?r.toFixed(2):0),t.find('input[name^="balance"]').val(c>0?c.toFixed(2):0),t.find('input[name^="amount"]').val(c>0?c.toFixed(2):0)}function d(e){t(e.currentTarget).closest(".paid_invoice").remove();var n=t(e.delegateTarget),a=n.find(".paid_invoice");if(a.length<1){var i=t(u());n.find("tbody").html(i)}m()}function u(){return'<tr class="no-item"><td class="text-center" colspan="9">No invoice added yet.</td></tr>'}function m(){var e=0,n=0,a=0,i=0;t(".item-table").find(".paid_invoice").each(function(o,l){var r=s(t(l));e+=r.total,n+=r.paidAmount,a+=r.balance,i+=r.amount}),t('input[name="z_total"]').val(e.toFixed(2)),t('input[name="z_paid_amount"]').val(n.toFixed(2)),t('input[name="z_balance"]').val(a.toFixed(2)),t('input[name="z_amount"]').val(i.toFixed(2)),t('input[name="cash_amount"]').val(i.toFixed(2)),t('input[name="check_amount"]').val(i.toFixed(2))}function s(t){var e=parseFloat(t.find('input[name^="total"]').val());isNaN(e)&&(e=0);var n=parseFloat(t.find('input[name^="paid_amount"]').val());isNaN(n)&&(n=0);var a=parseFloat(t.find('input[name^="balance"]').val());isNaN(a)&&(a=0);var i=parseFloat(t.find('input[name^="amount"]').val());return isNaN(i)&&(i=0),{total:e,paidAmount:n,balance:a,amount:i}}t(function(){function e(){t(".item-table").find('select[name^="invoice"]').select2({data:i,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}var i=[{id:"",text:""}];e(),t('select[name="customer"]').on("change",function(a){var o=t('select[name="customer"]').val();t(".item-table").find('select[name^="invoice"]').empty().trigger("change"),i=[{id:"",text:""}],t.when(n(o)).then(function(n){n.invoices.forEach(function(t){t.text=t.invoice_no,i.push(t)}),t(".item-table").find('select[name^="invoice"]').select2("destroy"),e()})}),t(".action-add-dr").click(function(){a(i)}),t(".item-table").on("change",'select[name^="invoice"]',function(e){var n=t(e.delegateTarget).find('select[name^="invoice"] option[value="'+e.currentTarget.value+'"]:selected');if(n.length>1)return alert("Invoice No already selected."),void t(e.currentTarget).val("").trigger("change");var a=t(e.currentTarget).closest(".paid_invoice");t.when(r(e.currentTarget.value)).then(function(t){c(a,t.invoice),m()},function(){c(a,null),m()})}).on("click",".action-delete-dr",function(t){d(t)}).on("change",'input[name^="amount"]',m).trigger("change"),t('input[name="type"]').change(function(e){t(".payment-type").addClass("d-none");var n=e.currentTarget.value;t("#"+n).removeClass("d-none")}),m()})}(jQuery,dateFns);