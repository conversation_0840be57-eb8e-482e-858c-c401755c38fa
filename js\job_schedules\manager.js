document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');

    var calendar = new FullCalendar.Calendar(calendarEl, {
      initialDate: new Date(),
      initialView: 'timeGridDay',
      nowIndicator: true,
      headerToolbar: {
        left: 'prev,next today addEventButton',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
      },
      
      events: BASE_URI + 'fetch/job_schedules',
      navLinks: true, // can click day/week names to navigate views
      editable: false,
      selectable: true,
      dayMaxEvents: true, // allow "more" link when too many events

      customButtons: {
          addEventButton: {
            text: 'add new schedule',
            click: function() {
              window.location.href = BASE_URI + "job_schedules/add";
            }
          }
      },
      
      eventClick: function(info) {
        var scheduleId = info.event.id;
        window.showOption = function() {
          alertify.alert (
              '<div class="button"><a href="javascript:showUpdate();">Update</a></div>' + 
              '<br>' + 
              '<div class="button"><a href="javascript:showDelete();">Delete</a></div>' +
              '<br>' + 
              '<div class="button"><a href="javascript:showDetails();">Details</a></div>'
          ).setHeader('<em>Choose An Option</em>');
        }

        window.showUpdate = function() {
          alertify.confirm("Are you sure you want to update schedule?",
          function(){
              window.location.href = BASE_URI + "job_schedules/edit/" + scheduleId;
          }).setHeader('<em>Update Schedule</em>');
        }
      
        window.showDelete = function() {
          alertify.confirm("Are you sure you want to delete schedule?",
          function(){
            $.ajax({
              url: BASE_URI + 'job_schedules/delete/' + scheduleId,
              type: 'POST',
              dataType: 'json'
            });
            location.reload();
          }).setHeader('<em>Delete Schedule</em>');
        }

        window.showDetails = function() {
          alertify.alert(
                        '<br>' + '<strong>Area:</strong> ' + info.event.extendedProps.area  + '<br>' +
                        '<strong>Customer Name:</strong> ' + info.event.title + '<br>' +
                        '<strong>Number of Birds:</strong> ' + info.event.extendedProps.qty_birds + '<br>' +
                        '<strong>Details:</strong> ' + info.event.extendedProps.details
                        ).setHeader('<em>Details</em>');
        }
        
        window.showOption();
      },

      eventDidMount:  function(info) { 
        $(info.el).find('.fc-event-title').html("<strong>"+info.event.title+"</strong>");
        $(info.el).find('.fc-event-title').append('<br>' + '<strong>Area:</strong> ' + info.event.extendedProps.area  + '<br>'); 
        $(info.el).find('.fc-event-title').append('<strong>Customer Name:</strong> ' + info.event.title + '<br>'); 
        $(info.el).find('.fc-event-title').append('<strong>Number of Birds:</strong> ' + info.event.extendedProps.qty_birds + '<br>'); 
        $(info.el).find('.fc-event-title').append('<strong>Details:</strong> ' + info.event.extendedProps.details);
      }


    });
      
    calendar.render();
  });