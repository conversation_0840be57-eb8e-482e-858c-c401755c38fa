+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function receiveAndReload(sales_orderId, dataTable) {
		$.when(receiveRow(sales_orderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(sales_orderId) {
		return $.ajax({
			url: BASE_URI + 'sales_orders/receive_sales_order/' + sales_orderId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(sales_orderId, dataTable) {
		$.when(deleteRow(sales_orderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(sales_orderId) {
		return $.ajax({
			url: BASE_URI + 'sales_orders/delete/' + sales_orderId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function getSalesOrder(salesOrderId) {
		return $.ajax({
		  url: BASE_URI + 'sales_orders/info/' + salesOrderId,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function submitForm(action, data) {
		return $.ajax({
		  url: action,
		  method: 'POST',
		  data: data,
		  processData: false,
		  contentType: false,
		  dataType: 'json',
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.sales_order').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the sales_order ' + docNo + '?',
					function () {
						var sales_orderId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(sales_orderId, dataTable);
					}
				).setHeader('<em>receive sales_order</em>');
			})
// Delete
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.sales_order').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete Sales Order ' + docNo + '?',
					function () {
						var sales_orderId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(sales_orderId, dataTable);
					}
				).setHeader('<em>Delete Sales Order</em>');
			})			
// Payment
			.on('click', '.action-payment', function (event) {
	        var dates = [];
	        var salesOrderId = event.currentTarget.getAttribute('data-id');

	        $.when(getSalesOrder(salesOrderId)).then(
	          function (response) {
	            var $modal = $('#addPayment');
	            var action = BASE_URI + 'sales_orders/payment/' + salesOrderId;
	            var total = response.sales_order.grand_total - response.sales_order.paid_amount;

	            // For some reason, d-none for id=others disappears upon closing modal
	            $('#others').addClass('d-none');

	            $modal.find('form').attr('action', action);
	            $modal.find('input[name="cash_amount"]').val(total);
	            $modal.find('input[name="check_amount"]').val(total);
	            $modal.modal('show');
	          },
	          function (jqXHR) {
	            var response = jqXHR.responseJSON;
	            alertify.error(response.message);
	          }
	        );
      	});

	    $('#addPayment')
	      .on('change', 'input[name="payment_type"]', function (event) {

	        var type = event.currentTarget.value;

	        if (type === 'check') {
	          $('#check').removeClass('d-none');
	          $('#others').addClass('d-none');
	        }else if(type === 'others'){
	          $('#others').removeClass('d-none');
	          $('#check').addClass('d-none');
	        }else{
	          $('#others').addClass('d-none');
	          $('#check').addClass('d-none');
	        }
	      })
	      .on('submit', 'form', function (event) {
	        event.preventDefault();

	        var form = event.currentTarget;
	        var action = form.getAttribute('action');
	        var formData = new FormData(form);
	        console.log(formData);
	        var paymentType = $('input[name="payment_type"]:checked').val();
	        console.log(paymentType);
	        $.when(submitForm(action, formData)).then(
	          function () {
	            $(event.delegateTarget).modal('hide');
	            dataTable.ajax.reload(null, false);
	          },
	          function (jqXHR) {
	            var response = jqXHR.responseJSON;
	            // $(form).find('.error').html(response.message);
	            dataTable.ajax.reload(null, false);
	          }
	        );
	      });

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/sales_orders?status=' + event.currentTarget.getAttribute('data-status');
			dataTable.ajax.url(url).load();
		});

		$('.pending').click();
	});
}(jQuery);
