<?php
class Schedule extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'schedule';
        parent::__construct();
    }

    /**
     * Get events
     */
    public function get_events()
    {
        $sql = <<<EOT
SELECT *
FROM schedule
WHERE is_deleted = 0
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return [];
        }
    }

    /**
     * Get grower farm names from schedules for the dropdown
     */
    public function get_grower_farms_from_schedules()
    {
        $sql = <<<EOT
SELECT DISTINCT
    customer.id,
    customer.id as customer_id,
    customer.name as grower_farm_name
FROM schedule
INNER JOIN customer ON customer.id = schedule.customer_id
WHERE customer.is_deleted = 0
AND schedule.is_deleted = 0
ORDER BY customer.name
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return [];
        }
    }

    /**
     * Check for duplicate schedule with same customer, date, and time
     */
    public function check_duplicate_schedule($customer_id, $eta_date, $eta_time, $exclude_id = null)
    {
        $date_only = date('Y-m-d', strtotime($eta_date));

        $sql = "SELECT s.id, s.waf_rs_no, sl.eta_time
                FROM schedule s
                INNER JOIN schedule_list sl ON s.id = sl.schedule_id
                WHERE s.customer_id = ?
                AND DATE(s.eta_date) = ?
                AND sl.eta_time = ?
                AND s.is_deleted = 0
                AND sl.is_deleted = 0";

        $params = [$customer_id, $date_only, $eta_time];

        if ($exclude_id) {
            $sql .= " AND s.id != ?";
            $params[] = $exclude_id;
        }

        $sql .= " LIMIT 1";

        $result = $this->advanced_query($sql, $params);
        return $result ? $result[0] : false;
    }



    /**
     * Get trips for a specific customer (combining all schedules for that customer)
     */
    public function get_trips_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT
    schedule_list.id,
    schedule_list.trip_no,
    schedule.waf_rs_no,
    CONCAT(schedule.eta_date, ' ', schedule_list.eta_time) as eta,
    DATE_FORMAT(schedule.eta_date, '%M %d, %Y') as formatted_date,
    DATE_FORMAT(schedule_list.eta_time, '%l:%i %p') as formatted_time
FROM schedule_list
INNER JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN hauling_log ON hauling_log.schedule_list_id = schedule_list.id
    AND hauling_log.is_deleted = 0
WHERE schedule.customer_id = ?
AND schedule_list.is_deleted = 0
AND schedule.is_deleted = 0
AND hauling_log.id IS NULL
ORDER BY schedule_list.trip_no
EOT;

        $binds = [$customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return [];
        }
    }
}

