<div class="col-md-12 sect-jr">
	<div class="receipt">
		<form role="form" method="post" id="formpurchase_order" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

            <fieldset>
            	<h5 class="crud-subtitle"><span>Purchase Order Info</span></h5>
                <br>

				<div class="row">
                    
                    <!-- <div class="col-md-4 form-group">
						<label for="customer" class="font-weight-bold float-left">Customer</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->customer;?>" readonly>
					</div> -->

					<div class="col-md-4 form-group">
						<label for="purchase_order_date" class="font-weight-bold float-left">Purchase Date</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=date('m/d/Y', strtotime($purchase_order->purchase_order_date));?>" readonly>
					</div>

                    <div class="col-md-4 form-group">
						<label for="purchase_order_date" class="font-weight-bold float-left">Date Covered</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=date('m/d/Y', strtotime($purchase_order->date_covered));?>" readonly>
					</div>

                    <div class="col-md-4 form-group">
						<label for="supplier" class="font-weight-bold float-left">Supplier</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->supplier;?>" readonly>
					</div>

					<div class="col-md-4 form-group">
						<label for="usage" class="font-weight-bold float-left">Usage</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->usage_name;?>" readonly>
					</div>

                    <div class="col-md-4 form-group">
						<label for="usage" class="font-weight-bold float-left">Sub-Usage</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->sub_usage_name;?>" readonly>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="font-weight-bold float-left">Remarks</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->remarks;?>" readonly>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="font-weight-bold float-left">Requisitioner</label>
                        <input type="text" name="customer" class="form-control text-center" value="<?=$purchase_order->requisitioner;?>" readonly>
					</div>

					<div class="col-md-4 form-group">
						<label for="received_on" class="font-weight-bold float-left">Date Received</label>

						<input type="text" name="received_on" class="form-control datepicker text-center" value="<?=set_value('received_on', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('received_on');?>
						</div>
					</div>

				</div>

            	<h5 class="crud-subtitle"><span>List of Purchase Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>

									<th class="text-center" style="width: 10%;">Pref. Qty</th>

									<th class="text-center" style="width: 10%;">Unit</th>

									<th class="text-center" style="width: 10%;">Pref. ALW</th>

									<th class="text-center" style="width: 10%;">Current Price</th>

									<th class="text-center" style="width: 10%;">Est. Due Amount</th>

									<th class="text-center" style="width: 10%;">Act. Qty</th>

									<th class="text-center" style="width: 10%;">Act. ALW</th>

									<th class="text-center" style="width: 10%;">Act. Due Amount</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($purchase_order_items)):
                                	$counter = 0;
                                    foreach ($purchase_order_items as $i => $purchase_order_item):
                                    $purchase_order_item_id = $purchase_order_item->id;
                            ?>
										<tr class="purchase_order_item">
											<td>
												<?php $fn_purchase_order_item = 'purchase_order_item_' . $purchase_order_item_id;?>
												<input type="hidden" name=<?=$fn_purchase_order_item?> value="<?=set_value($fn_purchase_order_item, $purchase_order_item_id);?>">

												<?php $fdn_item = 'item_' . $purchase_order_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$purchase_order_item->item_id?>><?=$purchase_order_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, sprintf("%.2f", $purchase_order_item->qty));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $purchase_order_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $purchase_order_item->unit);?>" data-suggest="units" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_pref_alw = 'pref_alw_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_pref_alw;?>" class="form-control" value="<?=set_value($fn_pref_alw, sprintf("%.2f", $purchase_order_item->pref_alw));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_pref_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_current_price = 'current_price_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_current_price;?>" class="form-control" value="<?=set_value($fn_current_price, sprintf("%.2f", $purchase_order_item->unit_price));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_current_price);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount, sprintf("%.2f", $purchase_order_item->amount));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_est_due_amount);?>
												</div>
											</td>

											<td>
												<?php $fn_act_qty = 'act_qty_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_act_qty;?>" class="form-control" value="<?=set_value($fn_act_qty, sprintf("%.2f", $purchase_order_item->act_qty));?>" step="any" >

												<div class="red-text flash-message">
													<?=form_error($fn_act_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_act_alw = 'act_alw_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_act_alw;?>" class="form-control" value="<?=set_value($fn_act_alw, sprintf("%.2f", $purchase_order_item->act_alw));?>" step="any" >

												<div class="red-text flash-message">
													<?=form_error($fn_act_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_act_due_amount = 'act_due_amount_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_act_due_amount;?>" class="form-control" value="<?=set_value($fn_act_due_amount, sprintf("%.2f", $purchase_order_item->act_due_amount));?>" step="any" >

												<div class="red-text flash-message">
													<?=form_error($fn_act_due_amount);?>
												</div>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<!-- <button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

				<?php if(!empty($purchase_order->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>Purchase Order Attachments</span></h5>
						<?php $attachments = explode('🔥',$purchase_order->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/purchase_orders/'.$purchase_order->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<?php if(!empty($or_attachments)):?>
					<div class="col-md-12 sect-jr">
						<h5 class="crud-subtitle"><span>OR Attachments</span></h5>
						<table class="table table-bordered">
						<tbody>

						<?php foreach($or_attachments as $or_attachment):?>
						<tr>
							<td><a href="<?=base_url('assets/purchase_order_or/'.$purchase_order->id.'/'.$or_attachment->file_name)?>" target="_blank"><?=$or_attachment->file_name?></a></td>
						</tr>
						<?php endforeach;?>

						</tbody>
						</table>
					</div>
				<?php endif;?>

				<br>
				<br>

				<!-- <div class="row">
					<div class="col-md-12">
						<p class="text-justify"><strong>Dear Supplier:</strong></p>
						<p class="text-justify">Please honor this request slip as PURCHASE ORDER (Proof of Purchase) for the items listed above. Should you have any verifications, don&apos;t hesitate to call me at (053) 321-1235.</p>
						<p class="text-justify">Thank you and God bless!</p>
						<br>
						<p class="text-justify">Please attach this original PO upon billing. No PO, no pay policy.</p>
						<p class="text-danger text-left">This PO is only valid for 3 days upon the date of issuance.</p>

					</div>
				</div> -->

				<!-- <div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$issuer->full_name;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Purchase Order Created By</p>
					</div>
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?php if (!empty($purchase->author_sig_url)): ?>
						<img src="<?=base_url('assets/asylum/signatures/' . $purchase->author_id . '/' . $purchase->author_sig_url);?>" class="img-responsive" style="height: 100px;">
						<?php endif;?>
						<br>
						<?=$purchase->author;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Prepared by</p>
					</div>

					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?php if (!empty($purchase->recommender_sig_url)): ?>
						<img src="<?=base_url('assets/asylum/signatures/' . $purchase->recommender_id . '/' . $purchase->recommender_sig_url);?>" class="img-responsive" style="height: 100px;">
						<?php endif;?>
						<br>
						<?=$purchase->recommender;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Approved by</p>
					</div>

					<div class="col-sm-3 text-center mt-auto" style="margin-top: -47px;">
						<img src="<?=base_url('assets/asylum/signatures/' . $purchase->approver_id . '/' . $purchase->approver_sig_url);?>" class="img-responsive" style="height: 100px;">
						<br>
						<?=$purchase->approved_on?>
						<hr class="signature-line" style="margin: 5px">
						<p>Verified by</p>

					</div>
				</div> -->

				<div class="clearfix text-center">
					<!-- <?php if ($_SESSION['user']->role_id !== '5' || $purchase_order->status !== 'printed'): ?>
						<?php if($type === 'Audit'){
							$type_name = 'Audited';
						}else{
							$type_name = $type;
						}?>

						<button type="button" class="btn btn-primary action-print" data-type="<?=$type?>" data-id="<?=$purchase_order->id;?>"><?=$type_name?></button>
					<?php endif;?> -->
					<button type="submit" name="submit" class="btn btn-primary">Confirm</button>
					<!-- <a class="btn btn btn-primary action-submit" href="<?=site_url('purchase_orders/receive_manager');?>">Confirm</a> -->
					<a class="btn btn-light" href="<?=site_url('purchase_orders/manager?status=approved');?>">Close</a>
				</div>
			</div>
			<input type="hidden" name="purchase_order_id" value="<?=$purchase_order->id?>">
		</div>
		</form>
	</div>
</div>