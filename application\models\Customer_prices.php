<?php
class Customer_prices extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'customer_prices';
        parent::__construct();
    }

    /**
     * Create New Item
     */
    public function generate($customer_id, $customer_price, $item_id)
    {
        $sql = <<<EOT
INSERT INTO item(customer_id, customer_price, item_id) VALUES(?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    customer_price = VALUES(customer_price),
    is_deleted = 0
EOT;
        $binds = [$customer_id, $customer_price, $item_id];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */