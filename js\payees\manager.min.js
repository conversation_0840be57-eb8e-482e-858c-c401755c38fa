!function(n){"use strict";function r(e,t){n.when(n.ajax({url:BASE_URI+"payees/delete/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}n(function(){t=(e=n(e=".tableA")).find("thead th");var e,t,a=e.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:t.length-1}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+e.attr("data-package")});n(".tableA").on("click",".action-delete",function(e){var t=n(e.currentTarget).closest("tr.customer").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+t+"?",function(){r(e.currentTarget.getAttribute("data-id"),a)}).setHeader("<em>Delete Payee</em>")})})}(jQuery);