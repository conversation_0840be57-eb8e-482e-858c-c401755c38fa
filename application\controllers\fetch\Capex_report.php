<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Capex_report extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Purchase Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase';
        $primary_key = 'purchase.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_date(),
            $this->_get_po_no(),
            $this->_get_supplier(),
            $this->_get_item_name(),
            $this->_get_amount(),
            $this->_get_total_amount(),
        ];

        $joins = <<<EOT
purchase
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN purchase_item ON purchase_item.purchase_id = purchase.id
LEFT JOIN item ON item.id = purchase_item.item_id
EOT;

        $where = <<<EOT
purchase.is_deleted = 0
AND purchase.status <> 'pending'
AND purchase_item.is_deleted = 0
AND purchase_item.account_type = 'CAPEX Purchases'
EOT;

        $date_from = $this->input->get('from', true) ? date("Y-m-d", strtotime(urldecode($this->input->get('from', true)))) : null;
        $date_to = $this->input->get('to', true) ? date("Y-m-d", strtotime(urldecode($this->input->get('to', true)))) : null;

        if (!empty($date_from) && !empty($date_to)) {
            $where .= <<<EOT

AND purchase.purchase_date BETWEEN "{$date_from}" AND "{$date_to}"
EOT;
        }

        $group_by = "purchase.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        ); 
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase';
            }
        ];
    }

    /**
     * Get Purchase Date
     */
    protected function _get_date()
    {
        return [
            'db' => 'purchase.purchase_date',
            'as' => 'purchase_date',
            'dt' => 0,
            'field' => 'purchase_date',
            'formatter' => function ($d, $row) {
                return date('M d, Y', strtotime($d));
            },
        ];
    }

    /**
     * Get PO Number
     */
    protected function _get_po_no()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'purchase_id',
            'dt' => 1,
            'field' => 'purchase_id',
        ];
    }

    /**
     * Get Supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier_name',
            'dt' => 2,
            'field' => 'supplier_name'
        ];
    }

    /**
     * Get Item Names
     */
    protected function _get_item_name()
    {
        return [
            'db' => 'GROUP_CONCAT(CONCAT("• ", item.name) SEPARATOR "<br>")',
            'as' => 'item_name',
            'dt' => 3,
            'field' => 'item_name'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'GROUP_CONCAT(CONCAT("• ", purchase_item.amount) SEPARATOR "<br>")',
            'as' => 'amount',
            'dt' => 4,
            'field' => 'amount'
        ];
    }

    /**
     * Get Total Amount
     */
    protected function _get_total_amount()
    {
        return [
            'db' => 'SUM(purchase_item.amount)',
            'as' => 'total_amount',
            'dt' => 5,
            'field' => 'total_amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            },
        ];
    }
}