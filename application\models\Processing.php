<?php
class Processing extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'processing';
        parent::__construct();
    }

    /**
     * Get processing By ID
     */
    public function get_by_id($processing_id)
    {
        $sql = <<<EOT
SELECT processing.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, issuer.full_name AS issuer
FROM processing
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing.added_by
WHERE processing.id = ?
    AND processing.is_deleted = 0
EOT;
        $binds = [$processing_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM processing
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid processings
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT processing.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM processing
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing.added_by
WHERE processing.paid_amount < processing.total
    AND processing.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get processings Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT processing.id, processing.processing_date, processing.customer_id, processing.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM processing
LEFT JOIN (
    SELECT payment.payment_date, paid_processing.processing_id, paid_processing.amount
    FROM payment
    LEFT JOIN paid_processing ON paid_processing.payment_id = payment.id
    LEFT JOIN processing ON processing.id = paid_processing.processing_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND processing.customer_id = ?
) payment ON payment.processing_id = processing.id
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing.added_by
WHERE processing.processing_date < ?
    AND customer.id = ?
    AND processing.is_deleted = 0
GROUP BY processing.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get processings Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT processing.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM processing
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing.added_by
WHERE processing.processing_date BETWEEN ? AND ?
    AND processing.customer_id = ?
    AND processing.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get processings BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT processing.id, processing.processing_no, processing.terms, processing.processing_date, processing.customer_id, processing.total AS charge, processing.total, processing.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM processing
LEFT JOIN (
    SELECT payment.payment_date, paid_processing.processing_id, paid_processing.amount
    FROM payment
    LEFT JOIN paid_processing ON paid_processing.payment_id = payment.id
    LEFT JOIN processing ON processing.id = paid_processing.processing_id
    WHERE payment.payment_date
        AND processing.customer_id = ?
) payment ON payment.processing_id = processing.id
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing.added_by
WHERE customer.id = ?
    AND processing.is_deleted = 0
GROUP BY processing.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */