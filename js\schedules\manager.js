+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[2, 'desc'] // Order by ETA Date descending
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(tripId, dataTable) {
		$.when(deleteRow(tripId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(tripId) {
		return $.ajax({
			url: BASE_URI + 'schedules/delete/' + tripId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.schedule').find('td');
			var tripNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';
			var customerName = typeof cols[1] !== 'undefined' ? cols[1].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete Trip ' + tripNo + ' for ' + customerName + '?',
				function () {
					var tripId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(tripId, dataTable);
				}
			).setHeader('<em>Delete Trip</em>');
		});
	});

}(jQuery);