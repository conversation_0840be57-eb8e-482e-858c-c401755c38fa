<?php
class Module_2307_model extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'form_2307';
        parent::__construct();
    }

    /**
     * Get 2307 by ID
     */
    public function get_by_id($_2307_id)
    {
        $sql = <<<EOT
SELECT 
    form_2307.*,
    supplier.address AS supplier_address,
    supplier.name AS supplier_name, 
    supplier.tin AS supplier_tin,
    supplier.zip_code AS supplier_zip_code,
    issuer.full_name AS issuer_name
FROM `form_2307`
LEFT JOIN supplier ON supplier.id = `form_2307`.suppliers_id
LEFT JOIN `user` AS issuer ON issuer.id = `form_2307`.added_by
WHERE `form_2307`.is_deleted = 0
    AND `form_2307`.id = ?;
EOT;
        $binds = [$_2307_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }


    public function specialInsert($values)
        {
            $query = $this->db->insert('`' . $this->table_name . '`', $values);

            if (!$query) {
                log_message('error', 'Experiencing query error:');
                log_message('error', $this->db->error());
                log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
                log_message('error', 'SQL Query:');
                log_message('error', $this->db->last_query());

                return false;
            } else {
                if ($this->db->db_debug) {
                    log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
                    log_message('debug', $this->db->last_query());
                }

                return $this->db->insert_id();
            }
        }
        
                
    }
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */