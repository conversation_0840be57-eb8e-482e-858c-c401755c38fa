<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Carcass Controller
 */
class Carcasses extends MYT_Controller
{
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('carcass');
        $this->lang->load('error_messages', 'english');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Carcass';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Carcass/manager'
        ];
        $this->build_content('default', 'Carcasses/manager');
    }

    /**
     * Add carcass records (multi-row)
     */
    public function add()
    {
        if ($this->input->post()) {
            $skus = $this->input->post('sku');
            $sizes = $this->input->post('size');
            $ranges = $this->input->post('range');
            $nos = $this->input->post('no');
            $pcs = $this->input->post('pcs');
            $kgs = $this->input->post('kgs');

            $success_count = 0;
            for ($i = 0; $i < count($skus); $i++) {
                if (empty($skus[$i]) && empty($sizes[$i]) && empty($ranges[$i])) {
                    continue;
                }
                $data = [
                    'sku' => $skus[$i],
                    'size' => $sizes[$i],
                    'range' => $ranges[$i],
                    'no' => $nos[$i],
                    'pcs' => $pcs[$i],
                    'kgs' => $kgs[$i]
                ];
                $this->carcass->insert_carcass($data);
                $success_count++;
            }
            if ($success_count > 0) {
                redirect('Carcasses');
            }
        }

        $this->title = 'Add Carcass';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Carcass/add'
        ];

        $data = [];

        $this->build_content('default', 'Carcasses/add', $data);
    }

    /**
     * Edit a single carcass record
     */
    public function edit($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
            redirect('Carcasses');
        }
        $this->title = 'Edit Carcass';
        $data['carcass'] = $carcass;
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Update a single carcass record
     */
    public function update($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
            redirect('Carcasses');
        }
        if ($this->input->post()) {
            $data = [
                'sku' => $this->input->post('sku'),
                'size' => $this->input->post('size'),
                'range' => $this->input->post('range'),
                'no' => $this->input->post('no'),
                'pcs' => $this->input->post('pcs'),
                'kgs' => $this->input->post('kgs')
            ];
            $this->carcass->update_carcass($id, $data);
            redirect('Carcasses');
        }
        $this->title = 'Edit Carcass';
        $data['carcass'] = $carcass;
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Soft delete a carcass record
     */
    public function delete($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
        } else {
            $this->carcass->soft_delete_carcass($id, $_SESSION['user']->id ?? 1);
            $this->session->set_flashdata('success', 'Carcass deleted.');
        }
        redirect('Carcasses');
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'transaction_failed':
                return '<p>Transaction failed. Please try again.</p>';

            case 'server_error':
                return;

            default:
                // Handle database_error and other custom errors
                if (strpos($this->_error, 'database_error:') === 0) {
                    return '<p>' . $this->_error . '</p>';
                }

                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}
