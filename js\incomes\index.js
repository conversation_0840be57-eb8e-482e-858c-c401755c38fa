+ function ($) {
    'use strict';
	var total = 0.00;
    var ctx = document.getElementById('myChart');
    ctx.width = 600;
    ctx.height = 200;
    var chart_obj;

    function createDataTable(selector) {
        var $table = $(selector);
        var cols = $table.find('thead th');
        // console.log($table.attr('data-package'));
        var dataTable = $table.DataTable({
            dom: 'lrtip',
            lengthChange: true,
            info: false,
            pagingType: 'full_numbers',
            paging: false,
            order: [
                [0, 'asc']
            ],
            columnDefs: [{
                className: 'text-right',
                targets: cols.length - 1
            }],
            autoWidth: false,
            processing: true,
            serverSide: true,
            ajax: {
                url: BASE_URI + 'fetch/' + $table.attr('data-package'),
                data: function (data) {
                    // Add date filter values to the request data
                    data.from = $('#from').val();
                    data.to = $('#to').val();
                }
            },
            footerCallback: function (row, data, start, end, display) {
              var api = this.api();
              var colSum = $table.attr("data-col-sum");

              if (typeof colSum !== "undefined" && colSum > -1) {
                  // Function to remove HTML and extract numeric value
                  var intVal = function (i) {
                      return typeof i === 'string' ?
                          i.replace(/<.*?>/g, '').replace(/[\$,]/g, '') * 1 :
                          typeof i === 'number' ?
                          i : 0;
                  };

                  // Total of all data in table
                  var grandTotal = api
                      .column(colSum)
                      .data()
                      .reduce(function (a, b) {
                          return intVal(a) + intVal(b);
                      }, 0);

                  $(api.column(colSum).footer()).html(
                      $.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
                  );

                  if (selector === ".tableA") {
                      $('#totalRevenue').html(
                          $.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
                      );
                      total += parseFloat(grandTotal);
                  } else if (selector === ".tableB") {
                      $('#totalExpense').html(
                          $.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
                      );
                      total -= parseFloat(grandTotal);
                  }
                  $(".totalIncome").html(
                      $.fn.addNumericCommas(parseFloat(total).toFixed(2))
                  );
              }
          },
        });

        return dataTable;
    }

    function initializeChart() {
        chart_obj = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Revenue',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Blue'
                    },
                    {
                        label: 'Expense',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Red'
                    },
                    {
                        label: 'Income',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Green'
                    },
                ]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    initializeChart();

    $(function () {
        var dataTableA = createDataTable('.tableA');
        var dataTableB = createDataTable('.tableB');

        $('#tableFilter').submit(function (event) {
            event.preventDefault();
            total = 0.00;

            var fromDate = $('#from').val();
            var toDate = $('#to').val();

            $.ajax({
                type: 'POST',
                url: BASE_URI + 'incomes/chart_data',
                data: { fromDate: fromDate, toDate: toDate },
                dataType: 'json',
                success: function (response) {
                    chart_obj.destroy();
                    let revenue_label = [];
                    let revenue_data = [];
                    let expense_label = [];
                    let expense_data = [];
                    let income_data = [];
                    let data_revenue = response.data.revenue;
                    let data_expense = response.data.expense;
                    for (let i = 0; i < data_revenue.length; i++) {
                        revenue_label.push(data_revenue[i].revenue_date);
                        revenue_data.push(parseFloat(data_revenue[i].total_revenue_per_day));
                        expense_label.push(data_expense[i].expense_date);

                        expense_data.push(parseFloat(data_expense[i].total_expense_per_day));
                        income_data.push(parseFloat(data_revenue[i].total_revenue_per_day - data_expense[i].total_expense_per_day));
                    }

                    chart_obj = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: revenue_label,
                            datasets: [
                                {
                                    label: 'Revenue',
                                    data: revenue_data,
                                    borderWidth: 1,
                                    borderColor: 'blue'
                                },
                                {
                                    label: 'Expense',
                                    data: expense_data,
                                    borderWidth: 1,
                                    borderColor: 'red'
                                },
                                {
                                    label: 'Income',
                                    data: income_data,
                                    borderWidth: 1,
                                    borderColor: 'green'
                                },
                            ]
                        },
                        options: {
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                },
                error: function (error) {
                    console.error(error);
                }
            });

            dataTableA.ajax.reload();
            dataTableB.ajax.reload();
        }).trigger("submit");
    });

    $('#exportToExcel').click(function () {
        // Get the date filter values
        var fromDate = $('#from').val();
        var toDate = $('#to').val();

        // Trigger an AJAX request to fetch data based on the date filter
        $.ajax({
            type: 'POST',
            url: BASE_URI + 'incomes/export_excel',
            data: { fromDate: fromDate, toDate: toDate },
            dataType: 'json',
            success: function (data) {
                window.open(BASE_URI + 'assets/' + data.filename);
                var text = 'Download Excel By Filter';
                $('.btn-process').text(text);
                $('.btn-process').css('pointer-events', 'auto');
            },
            error: function (error) {
                console.error(error);
            }
        });
    });

    $('#exportByYear').click(function () {

        // Get the date filter values
        var fromDate = $('#from').val();

        // Trigger an AJAX request to fetch data based on the date filter
        $.ajax({
            type: 'POST',
            url: BASE_URI + 'incomes/export_excel_by_year',
            data: { fromDate: fromDate },
            dataType: 'json',
            success: function (data) {
                window.open(BASE_URI + 'assets/' + data.filename);
                var text = 'Download Excel By Year';
                $('.btn-process').text(text);
                $('.btn-process').css('pointer-events', 'auto');
            },
            error: function (error) {
                console.error(error);
            }
        });
    });
}(jQuery);
