+ function ($) {
    'use strict';
 
    function promptAuditRemarks(paymentId) {
        return new Promise((resolve, reject) => {
            var html = '<input type="radio" name="auditStatus" value="Complete" checked> Complete &nbsp; &nbsp;';
            html += '<input type="radio" name="auditStatus" value="Incomplete"> Incomplete<br>';
            html += '&nbsp;';
            html += '<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100"></textarea>';
            html += '<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>'; // Added file input with ID 'attachments'

            alertify.confirm(html,
                function (evt, value) {
                    var auditRemarks = $('#auditRemarks').val();
                    var auditStatus = $('input[name="auditStatus"]:checked').val();
                    var files = $('#attachments')[0].files; // Get uploaded files
                    if (auditRemarks.trim() !== '') {
                        resolve({ remarks: auditRemarks, status: auditStatus, files: files }); // Pass files to resolve
                    } else {
                        reject('Missing Remarks: Audit remarks cannot be empty.');
                    }
                },
                function () {
                    reject('Audit canceled.');
                }
            ).setting({
                'reverseButtons': true,
                'closable': false,
                'title': 'Audit Payment'
            }).show();
        });
    }

    function markAudited(paymentId, auditRemarks, auditStatus, files) { // Updated to accept files parameter
        var formData = new FormData(); // Create form data object
        formData.append('payment_id', paymentId);
        formData.append('audit_remarks', auditRemarks);
        formData.append('audit_status', auditStatus);
        for (var i = 0; i < files.length; i++) { // Append each file to form data
            formData.append('attachments[]', files[i]);
        }

        return $.ajax({
            url: BASE_URI + 'payments/mark_audited/',
            type: 'POST',
            data: formData, // Send form data instead of plain object
            processData: false, // Prevent jQuery from processing the data
            contentType: false, // Prevent jQuery from setting contentType
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'Payment Audited.'
            };
        });
    }

    $(function () {
        $('#auditBtn').click(function () {
            var paymentId = $('input[name="payment_id"]').val();

            promptAuditRemarks(paymentId)
                .then(({ remarks, status, files }) => {
                    return markAudited(paymentId, remarks, status, files); // Pass files to markAudited
                })
                .then(response => {
                    alertify.success(response.message);
                    window.location.reload();
                })
                .catch(error => {
                    alertify.error(error);
                });
        });
    });

    function promptAuditUpdateRemarks(paymentId, auditRemarks) {
        return new Promise((resolve, reject) => {
            var html = '<input type="radio" name="auditStatus" value="Complete"> Complete &nbsp; &nbsp;';
            html += '<input type="radio" name="auditStatus" value="Incomplete" checked> Incomplete<br>';
            html += '&nbsp;';
            html += '<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100">' + auditRemarks + '</textarea>';
            html += '<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>'; // Added file input with ID 'attachments'

            alertify.confirm(html,
                function (evt, value) {
                    var auditRemarks = $('#auditRemarks').val();
                    var auditStatus = $('input[name="auditStatus"]:checked').val();
                    var files = $('#attachments')[0].files; // Get uploaded files
                    if (auditRemarks.trim() !== '') {
                        resolve({ remarks: auditRemarks, status: auditStatus, files: files }); // Pass files to resolve
                    } else {
                        reject('Missing Remarks: Audit remarks cannot be empty.');
                    }
                },
                function () {
                    reject('Audit canceled.');
                }
            ).setting({
                'reverseButtons': true,
                'closable': false,
                'title': 'Audit Payment'
            }).show();
        });
    }

    function updateMarkAudited(paymentId, auditRemarks, auditStatus, files) { // Updated to accept files parameter
        var formData = new FormData(); // Create form data object
        formData.append('payment_id', paymentId);
        formData.append('audit_remarks', auditRemarks);
        formData.append('audit_status', auditStatus);
        for (var i = 0; i < files.length; i++) { // Append each file to form data
            formData.append('attachments[]', files[i]);
        }

        return $.ajax({
            url: BASE_URI + 'payments/update_mark_audited/',
            type: 'POST',
            data: formData, // Send form data instead of plain object
            processData: false, // Prevent jQuery from processing the data
            contentType: false, // Prevent jQuery from setting contentType
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'Payment Audited.'
            };
        });
    }

    $(function () {
        $('#auditUpdateBtn').click(function () {
            var paymentId = $('input[name="payment_id"]').val();
            var auditRemarks = $('input[name="audit_remarks"]').val();
    
            promptAuditUpdateRemarks(paymentId, auditRemarks)
                .then(({ remarks, status, files }) => { // Include the 'status' parameter
                    return updateMarkAudited(paymentId, remarks, status, files);
                })
                .then(response => {
                    alertify.success(response.message);
                    window.location.reload();
                })
                .catch(error => {
                    alertify.error(error);
                });
        });
    });
    
   

}(jQuery);