<?php
class Sales_order extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'sales_order';
        parent::__construct();
    }

    /**
     * Get sales_order By ID
     */
    public function get_by_id($sales_order_id)
    {
        $sql = <<<EOT
SELECT sales_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM sales_order
LEFT JOIN customer ON customer.id = sales_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order.added_by
WHERE sales_order.id = ?
    AND sales_order.is_deleted = 0
EOT;
        $binds = [$sales_order_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM sales_order
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid sales_orders
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT sales_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM sales_order
LEFT JOIN customer ON customer.id = sales_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order.added_by
WHERE sales_order.paid_amount < sales_order.total
    AND sales_order.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get sales_orders Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT sales_order.id, sales_order.sales_order_date, sales_order.customer_id, sales_order.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM sales_order
LEFT JOIN (
    SELECT payment.payment_date, paid_sales_order.sales_order_id, paid_sales_order.amount
    FROM payment
    LEFT JOIN paid_sales_order ON paid_sales_order.payment_id = payment.id
    LEFT JOIN sales_order ON sales_order.id = paid_sales_order.sales_order_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND sales_order.customer_id = ?
) payment ON payment.sales_order_id = sales_order.id
LEFT JOIN customer ON customer.id = sales_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order.added_by
WHERE sales_order.sales_order_date < ?
    AND customer.id = ?
    AND sales_order.is_deleted = 0
GROUP BY sales_order.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get sales_orders Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT sales_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM sales_order
LEFT JOIN customer ON customer.id = sales_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order.added_by
WHERE sales_order.sales_order_date BETWEEN ? AND ?
    AND sales_order.customer_id = ?
    AND sales_order.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get sales_orders BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT sales_order.id, sales_order.sales_order_no, sales_order.terms, sales_order.sales_order_date, sales_order.customer_id, sales_order.total AS charge, sales_order.total, sales_order.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM sales_order
LEFT JOIN (
    SELECT payment.payment_date, paid_sales_order.sales_order_id, paid_sales_order.amount
    FROM payment
    LEFT JOIN paid_sales_order ON paid_sales_order.payment_id = payment.id
    LEFT JOIN sales_order ON sales_order.id = paid_sales_order.sales_order_id
    WHERE payment.payment_date
        AND sales_order.customer_id = ?
) payment ON payment.sales_order_id = sales_order.id
LEFT JOIN customer ON customer.id = sales_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order.added_by
WHERE customer.id = ?
    AND sales_order.is_deleted = 0
GROUP BY sales_order.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */