<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp..
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class Incomes extends MYT_Controller
{

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('purchase_item');
        $this->load->model('invoice_item');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Income Statement Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'js/incomes/index'
        ];

        $this->build_content('default', 'incomes/index');
    }

    /**
     * Chart Data
     */
    public function chart_data()
    {
        $this->title = 'Income Statement Report';
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/canvasjs/canvasjs-chart-3.7.21/canvasjs.min',
            'js/incomes/index'
        ];

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('fromDate'));
        $date_from = $date_from ? $date_from->format('Y-m-d') : date('Y-m-d');
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->post('toDate'));
        $date_to = $date_to ? $date_to->format('Y-m-d') : date('Y-m-d');

        // Fetch expense data using the model
        $expenseData = $this->purchase_item->get_expense_breakdown_by_day($date_from, $date_to);

        // Fetch revenue data using the model
        $revenueData= $this->invoice_item->get_revenue_breakdown_by_day($date_from, $date_to);
        $values = [
            'revenue' => $revenueData,
            'expense' => $expenseData
        ];
        echo json_encode(['status' => 200, 'data' => $values]);
    }

    /**
     * Export data to Excel
     */
    public function export_excel()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $borderMedium = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ]; 
        
        $borderThin = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $borderDouble = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DOUBLE,
                ],
            ],
        ];

        //Bottom Borders
        $sheet->getStyle('A3:C3')->applyFromArray($borderMedium);
        $sheet->getStyle('A5:C5')->applyFromArray($borderMedium);
        $sheet->getStyle('A6:C6')->applyFromArray($borderThin);

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('fromDate'));
        $date_from = $date_from->format('Y-m-d');
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->post('toDate'));
        $date_to = $date_to->format('Y-m-d');

        // Fetch revenue data using the model
        $expenseData = $this->purchase_item->get_by_expense($date_from, $date_to);

        // Fetch expense data using the model
        $revenueData= $this->invoice_item->get_by_revenue($date_from, $date_to);

        // Create a worksheet for revenue data
        // $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Income Statement Report');

        $sheet->setCellValue('A1', 'Leyte Agri Ventures Corp.');
        $sheet->getStyle('A1')->getFont()->setSize(14)->setBold(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A1:C1");

        $sheet->setCellValue('A2', 'INCOME STATEMENT REPORT');
        $sheet->getStyle('A2')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A2:C2");

        $start_date = "";
        $end_date = "";

        if ($date_from != "") {
            $start_date = date("F j, Y", strtotime($date_from));
        }
        
        if ($date_to != "") {
            $end_date = date("F j, Y", strtotime($date_to));
        }

        $sheet->setCellValue('A3', "As of " . $start_date . " to " . $end_date);
        $sheet->getStyle('A3')->getFont()->setSize(12)->setItalic(true);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 
        $sheet->mergeCells("A3:C3"); 

        // Set headers for revenue data
        $sheet->setCellValue('A5', 'Revenue Report');
        $sheet->getRowDimension('5')->setRowHeight(21);
        $sheet->getStyle('A5')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A5:C5");

        $sheet->setCellValue('A6', 'Account Name');
        $sheet->getStyle('A6')->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A6:B6");

        $sheet->setCellValue('C6', 'Amount');
        $sheet->getStyle('C6')->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        // Populate revenue data
        $total_revenue = 0;
        $row = 7;
        foreach ($revenueData as $item) {
            $sheet->setCellValue('B' . $row, $item->account_name ? : 'No Account Name'); 
            $sheet->setCellValue('C' . $row, "₱ ".number_format($item->amount, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $total_revenue += $item->amount;
            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;
        
        $sheet->setCellValue('B'.$row, 'Total');
        $sheet->getStyle('B'.$row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C'.$row, "₱ ".number_format($total_revenue, 2));
        $sheet->getStyle('C'.$row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for expense data
        $sheet->setCellValue('A' . $row, 'Exense Report');
        $sheet->getStyle("A$row:C$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:C$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Account Name');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->getStyle('C' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderThin);

        // Populate expense data
        $total_expense = 0;
        $row++;
        foreach ($expenseData as $item) {
            $sheet->setCellValue('B' . $row, $item->account_name ? : 'No Account Name');
            $sheet->setCellValue('C' . $row,  "₱ ". number_format($item->amount, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $total_expense += $item->amount;
            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_expense, 2));
        $sheet->getStyle('C'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for Income Statement
        $sheet->setCellValue('A' . $row, 'Income Statement');
        $sheet->getStyle("A$row:C$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:C$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Statement Report');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->getStyle('C' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderThin);

        // Populate Income Statement
        $row++;
        
        $sheet->setCellValue('B' . $row, 'Revenue');
        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_revenue, 2));
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row++;
        
        $sheet->setCellValue('B' . $row, 'Expense');
        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_expense, 2));
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->getStyle("A$row:C$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total Income');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format(($total_revenue - $total_expense), 2));
        $sheet->getStyle('C'. $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        foreach ($sheet->getColumnIterator() as $column)
            $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);

        // Create a writer and send the Excel file as a download
        $writer = new Xlsx($spreadsheet);

        // Set the file name
        $filename = 'Income Statement ' . date('Y_m_d', strtotime($date_from)) . ' ' . date('Y_m_d', strtotime($date_to)) . ' .xlsx';

        // Send the Excel file to the browser
        $writer->save(FCPATH . 'assets/' . $filename);
        echo json_encode(['status' => 200, 'filename' => $filename]);
    }

    /**
     * Export data by Year to Excel
     */
    public function export_excel_by_year()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $borderMedium = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ]; 
        
        $borderThin = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $borderDouble = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DOUBLE,
                ],
            ],
        ];

        //Bottom Borders
        $sheet->getStyle('A3:N3')->applyFromArray($borderMedium);
        $sheet->getStyle('A5:N5')->applyFromArray($borderMedium);
        $sheet->getStyle('A6:N6')->applyFromArray($borderThin);

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('fromDate'));
        $year = $date_from->format('Y');

        // Get Data Results by Query
        $expenseData = $this->purchase_item->get_expense_by_year($year);
        $revenueData= $this->invoice_item->get_revenue_by_year($year);

        $sheet->setTitle('Income Statement Report');

        $sheet->setCellValue('A1', 'Leyte Agri Ventures Corp.');
        $sheet->getStyle('A1')->getFont()->setSize(14)->setBold(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A1:N1");

        $sheet->setCellValue('A2', 'INCOME STATEMENT REPORT BY YEAR');
        $sheet->getStyle('A2')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  
        $sheet->mergeCells("A2:N2");

        $sheet->setCellValue('A3', "For the Year " . $year);
        $sheet->getStyle('A3')->getFont()->setSize(12)->setItalic(true);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 
        $sheet->mergeCells("A3:N3"); 

        // Set headers for revenue data
        $sheet->setCellValue('A5', 'Revenue Report');
        $sheet->getRowDimension('5')->setRowHeight(21);
        $sheet->getStyle('A5')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A5:N5");

        $sheet->setCellValue('A6', 'Account Name');
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A6:B6");

        $sheet->setCellValue('C6', 'January');
        $sheet->setCellValue('D6', 'February');
        $sheet->setCellValue('E6', 'March');
        $sheet->setCellValue('F6', 'April');
        $sheet->setCellValue('G6', 'May');
        $sheet->setCellValue('H6', 'June');
        $sheet->setCellValue('I6', 'July');
        $sheet->setCellValue('J6', 'August');
        $sheet->setCellValue('K6', 'September');
        $sheet->setCellValue('L6', 'October');
        $sheet->setCellValue('M6', 'November');
        $sheet->setCellValue('N6', 'December');
        $sheet->getStyle('C6:N6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle('A6:N6')->getFont()->setSize(10)->setBold(true);

        $row = 7;

        $total_jan_revenue = 0;
        $total_feb_revenue = 0;
        $total_mar_revenue = 0;
        $total_apr_revenue = 0;
        $total_may_revenue = 0;
        $total_jun_revenue = 0;
        $total_jul_revenue = 0;
        $total_aug_revenue = 0;
        $total_sep_revenue = 0;
        $total_oct_revenue = 0;
        $total_nov_revenue = 0;
        $total_dec_revenue = 0;

        // Populate revenue data
        foreach ($revenueData as $item) {
            $total_jan_revenue += $item->jan;
            $total_feb_revenue += $item->feb;
            $total_mar_revenue += $item->mar;
            $total_apr_revenue += $item->apr;
            $total_may_revenue += $item->may;
            $total_jun_revenue += $item->jun;
            $total_jul_revenue += $item->jul;
            $total_aug_revenue += $item->aug;
            $total_sep_revenue += $item->sep;
            $total_oct_revenue += $item->oct;
            $total_nov_revenue += $item->nov;
            $total_dec_revenue += $item->dec;

            $sheet->setCellValue('B' . $row, $item->account_name ?: 'No Account Name'); 
            $sheet->setCellValue('C' . $row, "₱ ".number_format($item->jan, 2));
            $sheet->setCellValue('D' . $row, "₱ ".number_format($item->feb, 2));
            $sheet->setCellValue('E' . $row, "₱ ".number_format($item->mar, 2));
            $sheet->setCellValue('F' . $row, "₱ ".number_format($item->apr, 2));
            $sheet->setCellValue('G' . $row, "₱ ".number_format($item->may, 2));
            $sheet->setCellValue('H' . $row, "₱ ".number_format($item->jun, 2));
            $sheet->setCellValue('I' . $row, "₱ ".number_format($item->jul, 2));
            $sheet->setCellValue('J' . $row, "₱ ".number_format($item->aug, 2));
            $sheet->setCellValue('K' . $row, "₱ ".number_format($item->sep, 2));
            $sheet->setCellValue('L' . $row, "₱ ".number_format($item->oct, 2));
            $sheet->setCellValue('M' . $row, "₱ ".number_format($item->nov, 2));
            $sheet->setCellValue('N' . $row, "₱ ".number_format($item->dec, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:N$row")->applyFromArray($borderDouble);

        $row++;
        
        $sheet->setCellValue('B'.$row, 'Total');
        $sheet->getStyle('B'.$row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('C'.$row, "₱ ".number_format($total_jan_revenue, 2));
        $sheet->setCellValue('D'.$row, "₱ ".number_format($total_feb_revenue, 2));
        $sheet->setCellValue('E'.$row, "₱ ".number_format($total_mar_revenue, 2));
        $sheet->setCellValue('F'.$row, "₱ ".number_format($total_apr_revenue, 2));
        $sheet->setCellValue('G'.$row, "₱ ".number_format($total_may_revenue, 2));
        $sheet->setCellValue('H'.$row, "₱ ".number_format($total_jun_revenue, 2));
        $sheet->setCellValue('I'.$row, "₱ ".number_format($total_jul_revenue, 2));
        $sheet->setCellValue('J'.$row, "₱ ".number_format($total_aug_revenue, 2));
        $sheet->setCellValue('K'.$row, "₱ ".number_format($total_sep_revenue, 2));
        $sheet->setCellValue('L'.$row, "₱ ".number_format($total_oct_revenue, 2));
        $sheet->setCellValue('M'.$row, "₱ ".number_format($total_nov_revenue, 2));
        $sheet->setCellValue('N'.$row, "₱ ".number_format($total_dec_revenue, 2));
        
        $sheet->getStyle("C$row:N$row")->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for expense data
        $sheet->setCellValue('A' . $row, 'Exense Report');
        $sheet->getStyle("A$row:N$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:N$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Account Name');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C'.$row, 'January');
        $sheet->setCellValue('D'.$row, 'February');
        $sheet->setCellValue('E'.$row, 'March');
        $sheet->setCellValue('F'.$row, 'April');
        $sheet->setCellValue('G'.$row, 'May');
        $sheet->setCellValue('H'.$row, 'June');
        $sheet->setCellValue('I'.$row, 'July');
        $sheet->setCellValue('J'.$row, 'August');
        $sheet->setCellValue('K'.$row, 'September');
        $sheet->setCellValue('L'.$row, 'October');
        $sheet->setCellValue('M'.$row, 'November');
        $sheet->setCellValue('N'.$row, 'December');

        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 
        $sheet->getStyle("C$row:N$row")->getFont()->setSize(10)->setBold(true);

        $sheet->getStyle("A$row:N$row")->applyFromArray($borderThin);

        $row++;

        $total_jan_expense = 0;
        $total_feb_expense = 0;
        $total_mar_expense = 0;
        $total_apr_expense = 0;
        $total_may_expense = 0;
        $total_jun_expense = 0;
        $total_jul_expense = 0;
        $total_aug_expense = 0;
        $total_sep_expense = 0;
        $total_oct_expense = 0;
        $total_nov_expense = 0;
        $total_dec_expense = 0;

        // Populate expense data
        foreach ($expenseData as $item) {
            $total_jan_expense += $item->jan;
            $total_feb_expense += $item->feb;
            $total_mar_expense += $item->mar;
            $total_apr_expense += $item->apr;
            $total_may_expense += $item->may;
            $total_jun_expense += $item->jun;
            $total_jul_expense += $item->jul;
            $total_aug_expense += $item->aug;
            $total_sep_expense += $item->sep;
            $total_oct_expense += $item->oct;
            $total_nov_expense += $item->nov;
            $total_dec_expense += $item->dec;

            $sheet->setCellValue('B' . $row, $item->account_name ? : 'No Account Name');
            $sheet->setCellValue('C' . $row, "₱ ".number_format($item->jan, 2));
            $sheet->setCellValue('D' . $row, "₱ ".number_format($item->feb, 2));
            $sheet->setCellValue('E' . $row, "₱ ".number_format($item->mar, 2));
            $sheet->setCellValue('F' . $row, "₱ ".number_format($item->apr, 2));
            $sheet->setCellValue('G' . $row, "₱ ".number_format($item->may, 2));
            $sheet->setCellValue('H' . $row, "₱ ".number_format($item->jun, 2));
            $sheet->setCellValue('I' . $row, "₱ ".number_format($item->jul, 2));
            $sheet->setCellValue('J' . $row, "₱ ".number_format($item->aug, 2));
            $sheet->setCellValue('K' . $row, "₱ ".number_format($item->sep, 2));
            $sheet->setCellValue('L' . $row, "₱ ".number_format($item->oct, 2));
            $sheet->setCellValue('M' . $row, "₱ ".number_format($item->nov, 2));
            $sheet->setCellValue('N' . $row, "₱ ".number_format($item->dec, 2));
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
            $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $row++;
        }

        $row--;

        $sheet->getStyle("A$row:N$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('C'.$row, "₱ ".number_format($total_jan_expense, 2));
        $sheet->setCellValue('D'.$row, "₱ ".number_format($total_feb_expense, 2));
        $sheet->setCellValue('E'.$row, "₱ ".number_format($total_mar_expense, 2));
        $sheet->setCellValue('F'.$row, "₱ ".number_format($total_apr_expense, 2));
        $sheet->setCellValue('G'.$row, "₱ ".number_format($total_may_expense, 2));
        $sheet->setCellValue('H'.$row, "₱ ".number_format($total_jun_expense, 2));
        $sheet->setCellValue('I'.$row, "₱ ".number_format($total_jul_expense, 2));
        $sheet->setCellValue('J'.$row, "₱ ".number_format($total_aug_expense, 2));
        $sheet->setCellValue('K'.$row, "₱ ".number_format($total_sep_expense, 2));
        $sheet->setCellValue('L'.$row, "₱ ".number_format($total_oct_expense, 2));
        $sheet->setCellValue('M'.$row, "₱ ".number_format($total_nov_expense, 2));
        $sheet->setCellValue('N'.$row, "₱ ".number_format($total_dec_expense, 2));
        
        $sheet->getStyle("C$row:N$row")->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row+= 3;
        
        // Set headers for Income Statement
        $sheet->setCellValue('A' . $row, 'Income Statement');
        $sheet->getStyle("A$row:N$row")->applyFromArray($borderMedium);
        $sheet->getRowDimension($row)->setRowHeight(21);
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:N$row");

        $row++;

        $sheet->setCellValue('A' . $row, 'Statement Report');
        $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->mergeCells("A$row:B$row");

        $sheet->setCellValue('C' . $row, 'January');
        $sheet->setCellValue('D' . $row, 'February');
        $sheet->setCellValue('E' . $row, 'March');
        $sheet->setCellValue('F' . $row, 'April');
        $sheet->setCellValue('G' . $row, 'May');
        $sheet->setCellValue('H' . $row, 'Jun');
        $sheet->setCellValue('I' . $row, 'July');
        $sheet->setCellValue('J' . $row, 'August');
        $sheet->setCellValue('K' . $row, 'September');
        $sheet->setCellValue('L' . $row, 'October');
        $sheet->setCellValue('M' . $row, 'November');
        $sheet->setCellValue('N' . $row, 'December');
        $sheet->getStyle("C$row:N$row")->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); 

        $sheet->getStyle("A$row:N$row")->applyFromArray($borderThin);

        $row++;

        // Populate Income Statement
        $sheet->setCellValue('B' . $row, 'Revenue');
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_jan_revenue, 2));
        $sheet->setCellValue('D' . $row,  "₱ ". number_format($total_feb_revenue, 2));
        $sheet->setCellValue('E' . $row,  "₱ ". number_format($total_mar_revenue, 2));
        $sheet->setCellValue('F' . $row,  "₱ ". number_format($total_apr_revenue, 2));
        $sheet->setCellValue('G' . $row,  "₱ ". number_format($total_may_revenue, 2));
        $sheet->setCellValue('H' . $row,  "₱ ". number_format($total_jun_revenue, 2));
        $sheet->setCellValue('I' . $row,  "₱ ". number_format($total_jul_revenue, 2));
        $sheet->setCellValue('J' . $row,  "₱ ". number_format($total_aug_revenue, 2));
        $sheet->setCellValue('K' . $row,  "₱ ". number_format($total_sep_revenue, 2));
        $sheet->setCellValue('L' . $row,  "₱ ". number_format($total_oct_revenue, 2));
        $sheet->setCellValue('M' . $row,  "₱ ". number_format($total_nov_revenue, 2));
        $sheet->setCellValue('N' . $row,  "₱ ". number_format($total_dec_revenue, 2));
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $row++;
        
        $sheet->setCellValue('B' . $row, 'Expense');
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format($total_jan_expense, 2));
        $sheet->setCellValue('D' . $row,  "₱ ". number_format($total_feb_expense, 2));
        $sheet->setCellValue('E' . $row,  "₱ ". number_format($total_mar_expense, 2));
        $sheet->setCellValue('F' . $row,  "₱ ". number_format($total_apr_expense, 2));
        $sheet->setCellValue('G' . $row,  "₱ ". number_format($total_may_expense, 2));
        $sheet->setCellValue('H' . $row,  "₱ ". number_format($total_jun_expense, 2));
        $sheet->setCellValue('I' . $row,  "₱ ". number_format($total_jul_expense, 2));
        $sheet->setCellValue('J' . $row,  "₱ ". number_format($total_aug_expense, 2));
        $sheet->setCellValue('K' . $row,  "₱ ". number_format($total_sep_expense, 2));
        $sheet->setCellValue('L' . $row,  "₱ ". number_format($total_oct_expense, 2));
        $sheet->setCellValue('M' . $row,  "₱ ". number_format($total_nov_expense, 2));
        $sheet->setCellValue('N' . $row,  "₱ ". number_format($total_dec_expense, 2));
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->getStyle("A$row:N$row")->applyFromArray($borderDouble);

        $row++;

        $sheet->setCellValue('B'. $row, 'Total Income');
        $sheet->getStyle('B'. $row)->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('C' . $row,  "₱ ". number_format(($total_jan_revenue - $total_jan_expense), 2));
        $sheet->setCellValue('D' . $row,  "₱ ". number_format(($total_feb_revenue - $total_feb_expense), 2));
        $sheet->setCellValue('E' . $row,  "₱ ". number_format(($total_mar_revenue - $total_mar_expense), 2));
        $sheet->setCellValue('F' . $row,  "₱ ". number_format(($total_apr_revenue - $total_apr_expense), 2));
        $sheet->setCellValue('G' . $row,  "₱ ". number_format(($total_may_revenue - $total_may_expense), 2));
        $sheet->setCellValue('H' . $row,  "₱ ". number_format(($total_jun_revenue - $total_jun_expense), 2));
        $sheet->setCellValue('I' . $row,  "₱ ". number_format(($total_jul_revenue - $total_jul_expense), 2));
        $sheet->setCellValue('J' . $row,  "₱ ". number_format(($total_aug_revenue - $total_aug_expense), 2));
        $sheet->setCellValue('K' . $row,  "₱ ". number_format(($total_sep_revenue - $total_sep_expense), 2));
        $sheet->setCellValue('L' . $row,  "₱ ". number_format(($total_oct_revenue - $total_oct_expense), 2));
        $sheet->setCellValue('M' . $row,  "₱ ". number_format(($total_nov_revenue - $total_nov_expense), 2));
        $sheet->setCellValue('N' . $row,  "₱ ". number_format(($total_dec_revenue - $total_dec_expense), 2));
        $sheet->getStyle("C$row:N$row")->getFont()->setSize(10)->setBold(true);
        $sheet->getStyle("C$row:N$row")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        foreach ($sheet->getColumnIterator() as $column)
            $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);

        // Create a writer and send the Excel file as a download
        $writer = new Xlsx($spreadsheet);

        // Set the file name
        $filename = 'Income Statement by Year ' . date('Y_m_d_H_i_s') . ' .xlsx';

        // Send the Excel file to the browser
        $writer->save(FCPATH . 'assets/' . $filename);
        echo json_encode(['status' => 200, 'filename' => $filename]);
    }
}
