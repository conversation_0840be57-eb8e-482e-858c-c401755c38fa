<?php
class User extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'user';
        parent::__construct();
    }

    /**
     * Get all users
     */
    public function get_all()
    {
        $sql = <<<EOT
SELECT user.*, role.name AS role
FROM user
LEFT JOIN role ON role.id = user.role_id
WHERE user.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
