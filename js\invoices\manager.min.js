!function(e){"use strict";function t(t){var a=e(t),r=a.find("thead th"),n=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:r.length-3},{className:"dt-action",targets:r.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(t,a){e.when(r(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function r(t){return e.ajax({url:BASE_URI+"invoices/for_approval/"+t,type:"POST",dataType:"json"})}function n(t,a,r){e.when(l(t,a)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function o(t,a){e.when(i(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function i(t,a){return e.ajax({url:BASE_URI+"invoices/bad_debt/"+t,type:"POST",dataType:"json"})}function l(t,a){return e.ajax({url:BASE_URI+"invoices/delete/"+t,type:"POST",dataType:"json",data:{reason:a}})}e(function(){var r=t(".tableA");e(".tableA").on("click",".action-for-approval",function(t){var n=e(t.currentTarget).closest("tr.invoice").find("td"),o=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to recommend for approval the Billing No. "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,r)}).setHeader("<em>Recommend for Approval</em>")}).on("click",".action-delete",function(t){var a=e(t.currentTarget).closest("tr.invoice").find("td"),o=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to cancel the Billing No. "+o+'?<br><br><label for="reason">Reason for cancelation:</label><br><input type="text" id="deletion-reason" name="reason" style="width: 100%;"><div id="reason-error" style="color: red; display: none;">Please provide a reason.</div>',function(){var e=document.getElementById("deletion-reason").value;if(!e)return document.getElementById("reason-error").style.display="block",!1;var a=t.currentTarget.getAttribute("data-id");n(a,e,r)}).setHeader("<em>Delete Invoice</em>")}).on("click",".action-bad-debt",function(t){var a=e(t.currentTarget).closest("tr.invoice").find("td"),n=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you mark Billing No."+n+"as Bad Debt?<br><br>",function(){var e=t.currentTarget.getAttribute("data-id");o(e,r)}).setHeader("<em>Mark as Bad Debt</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var a=BASE_URI+"fetch/invoices?status="+t.currentTarget.getAttribute("data-status");r.ajax.url(a).load()})})}(jQuery);