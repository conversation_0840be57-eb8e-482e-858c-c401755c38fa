+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(purchaseOrderId, dataTable) {
		$.when(receiveRow(purchaseOrderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + 'purchase_orders/receive_purchase_order/' + purchaseOrderId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(purchaseOrderId, dataTable) {
		$.when(deleteRow(purchaseOrderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + 'purchase_orders/delete/' + purchaseOrderId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function pendingAndReload(purchaseOrderId, dataTable) {
		$.when(pendingRow(purchaseOrderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/return_to_pending/" + purchaseOrderId,
			type: "POST",
			dataType: "json",
		});
	}

	function urgentAndReload(purchaseOrderId, dataTable) {
		$.when(urgentRow(purchaseOrderId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function urgentRow(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/mark_urgent/" + purchaseOrderId,
			type: "POST",
			dataType: "json",
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the purchase_order ' + docNo + '?',
					function () {
						var purchaseOrderId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(purchaseOrderId, dataTable);
					}
				).setHeader('<em>receive purchase_order</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete purchase order ' + docNo + '?',
					function () {
						var purchaseOrderId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(purchaseOrderId, dataTable);
					}
				).setHeader('<em>Delete Purchase Order</em>');
			})
			.on("click", ".action-pending", function (event) {
				var cols = $(event.currentTarget).closest("tr").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						'Are you sure you want to return purchase order ' + docNo + ' to pending?',
						function () {
							var purchaseOrderId = event.currentTarget.getAttribute("data-id");
							pendingAndReload(purchaseOrderId, dataTable);
						}
					)
					.setHeader("<em>Return to Pending</em>");
			})
			.on("click", ".action-urgent", function (event) {
				var cols = $(event.currentTarget).closest("tr").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						'Are you sure you want to mark purchase order ' + docNo + ' as urgent?',
						function () {
							var purchaseOrderId = event.currentTarget.getAttribute("data-id");
							urgentAndReload(purchaseOrderId, dataTable);
						}
					)
					.setHeader("<em>Mark as Urgent</em>");
			})

		// $('.tab-link').click(function(event) {
		// 	$('.tab-link.active').removeClass('active');
		// 	event.currentTarget.classList.add('active');

		// 	var url = BASE_URI + 'fetch/purchase_orders?status=' + event.currentTarget.getAttribute('data-status');
		// 	var manager_url = BASE_URI + 'purchase_orders/manager?status=' + event.currentTarget.getAttribute('data-status');
		// 	console.log(url);
		// 	window.history.pushState(
		// 		null,
		// 		null,
		// 		manager_url
		// 	);
		// 	dataTable.ajax.url(url).load();
		// })

		$(".tab-link").click(function (event) {
			$(".tab-link.active").removeClass("active");
			event.currentTarget.classList.add("active");

			var purchaseStatus = event.currentTarget.getAttribute("data-status");
			var address =
				BASE_URI + "purchase_orders/manager?status=" + encodeURI(purchaseStatus);
			window.location.href = address;
		});
	});
}(jQuery);
