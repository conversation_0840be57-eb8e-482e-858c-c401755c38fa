{"version": 3, "sources": ["../../rollupPluginBabelHelpers", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/util.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/index.js"], "names": ["_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_extends", "assign", "arguments", "source", "hasOwnProperty", "call", "apply", "this", "$", "NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "Event", "ClassName", "<PERSON><PERSON>", "DATA_API_KEY", "Selector", "<PERSON><PERSON>", "<PERSON><PERSON>", "transition", "transitionEndEmulator", "duration", "called", "one", "TRANSITION_END", "triggerTransitionEnd", "_this", "prefix", "Math", "random", "document", "getElementById", "element", "selector", "getAttribute", "char<PERSON>t", "escapeSelector", "substr", "replace", "find", "err", "offsetHeight", "trigger", "end", "Boolean", "obj", "nodeType", "componentName", "config", "configTypes", "property", "expectedTypes", "value", "valueType", "isElement", "toString", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "window", "QUnit", "fn", "emulateTransitionEnd", "supportsTransitionEnd", "event", "special", "is", "handleObj", "handler", "_element", "close", "rootElement", "_getRootElement", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "getSelectorFromElement", "parent", "closest", "closeEvent", "CLOSE", "removeClass", "hasClass", "_destroyElement", "detach", "CLOSED", "remove", "_jQueryInterface", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "CLICK_DATA_API", "noConflict", "toggle", "triggerChangeEvent", "addAriaPressed", "input", "type", "checked", "activeElement", "hasAttribute", "classList", "contains", "focus", "setAttribute", "toggleClass", "button", "FOCUS_BLUR_DATA_API", "Carousel", "<PERSON><PERSON><PERSON>", "DefaultType", "Direction", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "_config", "_getConfig", "_indicatorsElement", "INDICATORS", "_addEventListeners", "next", "_slide", "nextWhenVisible", "hidden", "css", "prev", "pause", "NEXT_PREV", "cycle", "interval", "setInterval", "visibilityState", "bind", "to", "index", "ACTIVE_ITEM", "activeIndex", "_getItemIndex", "SLID", "direction", "off", "typeCheckConfig", "keyboard", "KEYDOWN", "_this2", "_keydown", "MOUSEENTER", "MOUSELEAVE", "documentElement", "TOUCHEND", "setTimeout", "tagName", "which", "makeArray", "ITEM", "indexOf", "_getItemByDirection", "isNextDirection", "isPrevDirection", "lastItemIndex", "wrap", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "SLIDE", "_setActiveIndicatorElement", "ACTIVE", "nextIndicator", "children", "addClass", "directionalClassName", "orderClassName", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "slidEvent", "reflow", "_this3", "action", "slide", "TypeError", "_dataApiClickHandler", "slideIndex", "DATA_SLIDE", "LOAD_DATA_API", "DATA_RIDE", "$carousel", "Collapse", "Dimension", "_isTransitioning", "_triggerArray", "id", "tab<PERSON>og<PERSON>", "DATA_TOGGLE", "elem", "filter", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "ACTIVES", "not", "startEvent", "SHOW", "dimension", "_getDimension", "style", "attr", "setTransitioning", "complete", "SHOWN", "scrollSize", "slice", "HIDE", "getBoundingClientRect", "HIDDEN", "isTransitioning", "j<PERSON>y", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "$this", "currentTarget", "$trigger", "$target", "<PERSON><PERSON><PERSON><PERSON>", "longerTimeoutBrowsers", "timeoutDuration", "navigator", "userAgent", "debounce", "Promise", "resolve", "then", "scheduled", "isFunction", "functionToCheck", "getStyleComputedProperty", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "ownerDocument", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "getOffsetParent", "offsetParent", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "element1root", "getScroll", "upperSide", "undefined", "html", "scrollingElement", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "isIE10", "isIE10$1", "appVersion", "getSize", "computedStyle", "max", "getWindowSizes", "height", "width", "classCallCheck", "instance", "createClass", "defineProperties", "getClientRect", "offsets", "right", "left", "bottom", "top", "rect", "scrollTop", "scrollLeft", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "subtract", "modifier", "includeScroll", "getBoundaries", "popper", "reference", "padding", "boundariesElement", "relativeOffset", "boundaries", "innerWidth", "innerHeight", "boundariesNode", "isFixed", "_getWindowSizes", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "keys", "map", "area", "_ref", "sort", "a", "b", "filtered<PERSON><PERSON>s", "_ref2", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "arr", "check", "Array", "runModifiers", "modifiers", "ends", "prop", "findIndex", "cur", "for<PERSON>ach", "console", "warn", "enabled", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "to<PERSON><PERSON><PERSON>", "getWindow", "defaultView", "setupEventListeners", "options", "updateBound", "addEventListener", "passive", "scrollElement", "attachToScrollParents", "callback", "scrollParents", "isBody", "eventsEnabled", "disableEventListeners", "cancelAnimationFrame", "scheduleUpdate", "removeEventListener", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "placements", "validPlacements", "clockwise", "counter", "concat", "reverse", "BEHAVIORS", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "parseOffset", "offset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "str", "toValue", "index2", "De<PERSON>ults", "removeOnDestroy", "onCreate", "onUpdate", "shift", "shiftvariation", "_data$offsets", "isVertical", "side", "shiftOffsets", "preventOverflow", "priority", "primary", "escapeWithReference", "secondary", "min", "keepTogether", "floor", "opSide", "arrow", "_data$offsets$arrow", "arrowElement", "querySelector", "len", "sideCapitalized", "altSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "round", "flip", "flipped", "originalPlacement", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "inner", "subtractLength", "bound", "attributes", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "position", "prefixedProperty", "<PERSON><PERSON><PERSON><PERSON>", "invertTop", "invertLeft", "x-placement", "arrowStyles", "applyStyle", "removeAttribute", "onLoad", "modifierOptions", "<PERSON><PERSON>", "requestAnimationFrame", "update", "isDestroyed", "isCreated", "enableEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dropdown", "REGEXP_KEYDOWN", "ARROW_UP_KEYCODE", "AttachmentMap", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "_getParentFromElement", "isActive", "_clearMenus", "showEvent", "boundary", "_getPopperConfig", "noop", "destroy", "CLICK", "stopPropagation", "constructor", "_getPlacement", "$parentDropdown", "offsetConf", "toggles", "context", "dropdownMenu", "hideEvent", "_dataApiKeydownHandler", "items", "get", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "e", "Modal", "_dialog", "DIALOG", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_originalBodyPadding", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "CLICK_DISMISS", "DATA_DISMISS", "MOUSEDOWN_DISMISS", "MOUSEUP_DISMISS", "_showBackdrop", "_showElement", "FOCUSIN", "_hideModal", "handleUpdate", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "display", "_enforceFocus", "shownEvent", "transitionComplete", "_this4", "has", "KEYDOWN_DISMISS", "RESIZE", "_this6", "_resetAdjustments", "_resetScrollbar", "_this7", "_removeBackdrop", "animate", "backdrop", "doAnimate", "createElement", "className", "appendTo", "_this8", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "paddingLeft", "paddingRight", "_getScrollbarWidth", "FIXED_CONTENT", "actualPadding", "calculatedPadding", "_this9", "STICKY_CONTENT", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "NAVBAR_TOGGLER", "margin", "scrollDiv", "scrollbarWidth", "<PERSON><PERSON><PERSON>", "BSCLS_PREFIX_REGEX", "HoverState", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "isWithContent", "isInTheDom", "tipId", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "animation", "attachment", "_getAttachment", "addAttachmentClass", "container", "INSERTED", "fallbackPlacement", "_handlePopperPlacementChange", "_fixTransition", "prevHoverState", "_TRANSITION_DURATION", "_cleanTipClass", "getTitle", "CLASS_PREFIX", "template", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "empty", "append", "text", "title", "eventIn", "eventOut", "FOCUSOUT", "_fixTitle", "titleType", "delay", "tabClass", "join", "initConfigAnimation", "Popover", "subClass", "superClass", "create", "__proto__", "_getContent", "ScrollSpy", "OffsetMethod", "_scrollElement", "NAV_LINKS", "LIST_ITEMS", "DROPDOWN_ITEMS", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "SCROLL", "_process", "refresh", "autoMethod", "offsetMethod", "method", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "pageYOffset", "_getOffsetHeight", "maxScroll", "_activate", "_clear", "queries", "$link", "DROPDOWN", "DROPDOWN_TOGGLE", "parents", "NAV_LIST_GROUP", "NAV_ITEMS", "ACTIVATE", "scrollSpys", "DATA_SPY", "$spy", "Tab", "previous", "listElement", "itemSelector", "hiddenEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "version"], "mappings": ";;;;;kOAEA,SAASA,EAAkBC,EAAQC,GACjC,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CACrC,IAAIE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWM,IAAKN,IAIlD,SAASO,EAAaC,EAAaC,EAAYC,GAG7C,OAFID,GAAYd,EAAkBa,EAAYG,UAAWF,GACrDC,GAAaf,EAAkBa,EAAaE,GACzCF,EAGT,SAASI,IAeP,OAdAA,EAAWR,OAAOS,QAAU,SAAUjB,GACpC,IAAK,IAAIE,EAAI,EAAGA,EAAIgB,UAAUf,OAAQD,IAAK,CACzC,IAAIiB,EAASD,UAAUhB,GAEvB,IAAK,IAAIQ,KAAOS,EACVX,OAAOO,UAAUK,eAAeC,KAAKF,EAAQT,KAC/CV,EAAOU,GAAOS,EAAOT,IAK3B,OAAOV,IAGOsB,MAAMC,KAAML,WCN9B,IClBA,ICCgBM,EAORC,EAEAC,EACAC,EAEAC,EAOAC,EAMAC,EAAAA,EAAAA,EAYAC,ECtCSP,EAOTC,EAEAC,EACAC,EACAK,EACAJ,EAEAE,EAAAA,EAAAA,EAMAG,EAAAA,EAAAA,EAAAA,EAAAA,EAQAJ,EAYAK,EFxCFC,EAAQ,SAACX,OAOTY,GAAa,WAgCRC,EAAsBC,cACzBC,GAAS,WAEXhB,MAAMiB,IAAIL,EAAKM,eAAgB,cACtB,eAGA,WACJF,KACEG,qBAALC,IAEDL,GAEIf,SA4BHY,kBAEY,yBAFL,SAIJS,YA3EO,IA8EGC,KAAKC,gBACXC,SAASC,eAAeJ,WAC1BA,0BATE,SAYYK,OA3BPC,EA4BVA,EAAWD,EAAQE,aAAa,eAC/BD,GAAyB,MAAbA,MACJD,EAAQE,aAAa,SAAW,IAIlB,MAAvBD,EAASE,OAAO,KAlCNF,EAmCQA,MAhCe,mBAArB1B,EAAE6B,eAAgC7B,EAAE6B,eAAeH,GAAUI,OAAO,GAClFJ,EAASK,QAAQ,sBAAuB,oBAmCtB/B,EAAEuB,UAAUS,KAAKN,GAClB/C,OAAS,EAAI+C,EAAW,KACzC,MAAOO,UACA,cA3BA,SA+BJR,UACEA,EAAQS,mCAhCN,SAmCUT,KACjBA,GAASU,QAAQvB,EAAWwB,4BApCrB,kBAwCFC,QAAQzB,cAxCN,SA2CD0B,UACAA,EAAI,IAAMA,GAAKC,0BA5Cd,SA+CKC,EAAeC,EAAQC,OAChC,IAAMC,KAAYD,KACjB1D,OAAOO,UAAUK,eAAeC,KAAK6C,EAAaC,GAAW,KACzDC,EAAgBF,EAAYC,GAC5BE,EAAgBJ,EAAOE,GACvBG,EAAgBD,GAASlC,EAAKoC,UAAUF,GAC1C,WAzHIP,EAyHeO,KAxHnBG,SAASnD,KAAKyC,GAAKW,MAAM,iBAAiB,GAAGC,mBA0H5C,IAAIC,OAAOP,GAAeQ,KAAKN,SAC5B,IAAIO,MACLb,EAAcc,cAAjB,aACWX,EADX,oBACuCG,EADvC,wBAEsBF,EAFtB,UA7HIN,cAkBQ,oBAAXiB,SAA0BA,OAAOC,aAKrC,mBAuBLC,GAAGC,qBAAuB7C,EAExBF,EAAKgD,4BACLC,MAAMC,QAAQlD,EAAKM,0BA3CXL,EAAWwB,iBACPxB,EAAWwB,WAFpB,SAGEwB,MACD5D,EAAE4D,EAAMpF,QAAQsF,GAAG/D,aACd6D,EAAMG,UAAUC,QAAQlE,MAAMC,KAAML,cA8H5CiB,EApJK,+CCCRJ,GAOEN,EAAsB,QAGtBE,EAAAA,KADAD,EAAsB,YAGtBE,GAZQJ,EAwKbA,GA5J6ByD,GAAGxD,GAO3BI,iBACqBF,kBACCA,yBACDA,EAXC,aActBG,EACI,QADJA,EAEI,OAFJA,EAGI,OASJC,wBACQkB,QACLwC,SAAWxC,6BAWlByC,MAlDkB,SAkDZzC,KACMA,GAAW1B,KAAKkE,aAEpBE,EAAcpE,KAAKqE,gBAAgB3C,GACrB1B,KAAKsE,mBAAmBF,GAE5BG,2BAIXC,eAAeJ,MAGtBK,QA/DkB,aAgEdC,WAAW1E,KAAKkE,SAAU/D,QACvB+D,SAAW,QAKlBG,gBAtEkB,SAsEF3C,OACRC,EAAWf,EAAK+D,uBAAuBjD,GACzCkD,GAAa,SAEbjD,MACO1B,EAAE0B,GAAU,IAGlBiD,MACM3E,EAAEyB,GAASmD,QAAX,IAAuBtE,GAAmB,IAG9CqE,KAGTN,mBArFkB,SAqFC5C,OACXoD,EAAa7E,EAAEK,MAAMA,EAAMyE,gBAE/BrD,GAASU,QAAQ0C,GACZA,KAGTN,eA5FkB,SA4FH9C,gBACXA,GAASsD,YAAYzE,GAElBK,EAAKgD,yBACL3D,EAAEyB,GAASuD,SAAS1E,KAKvBmB,GACCT,IAAIL,EAAKM,eAAgB,SAAC2C,UAAUzC,EAAK8D,gBAAgBxD,EAASmC,KAClEF,qBA1FqB,UAoFjBuB,gBAAgBxD,MASzBwD,gBA1GkB,SA0GFxD,KACZA,GACCyD,SACA/C,QAAQ9B,EAAM8E,QACdC,YAKEC,iBAnHW,SAmHM5C,UACf1C,KAAKuF,KAAK,eACTC,EAAWvF,EAAED,MACfyF,EAAaD,EAASC,KAAKtF,GAE1BsF,MACI,IAAIjF,EAAMR,QACRyF,KAAKtF,EAAUsF,IAGX,UAAX/C,KACGA,GAAQ1C,WAKZ0F,eAnIW,SAmIIC,UACb,SAAU9B,GACXA,KACI+B,mBAGMzB,MAAMnE,sDAjIE,mBA4I1BwB,UAAUqE,GACVvF,EAAMwF,eArII,yBAuIVtF,EAAMkF,eAAe,IAAIlF,MASzBkD,GAAGxD,GAAoBM,EAAM8E,mBAC7B5B,GAAGxD,GAAMb,YAAcmB,IACvBkD,GAAGxD,GAAM6F,WAAc,oBACrBrC,GAAGxD,GAAQG,EACNG,EAAM8E,kBAGR9E,GCxKHG,GAOET,EAAsB,SAGtBE,EAAAA,KADAD,EAAsB,aAEtBM,EAAsB,YACtBJ,GAZSJ,EAmKdA,GAvJ6ByD,GAAGxD,GAE3BK,EACK,SADLA,EAEK,MAFLA,EAGK,QAGLG,EACiB,0BADjBA,EAEiB,0BAFjBA,EAGiB,QAHjBA,EAIiB,UAJjBA,EAKiB,OAGjBJ,0BAC0BF,EAAYK,sBACpB,QAAQL,EAAYK,EAApB,QACSL,EAAYK,GASvCE,wBACQe,QACLwC,SAAWxC,6BAWlBsE,OArDmB,eAsDbC,GAAqB,EACrBC,GAAiB,EACf9B,EAAcnE,EAAED,KAAKkE,UAAUW,QACnCnE,GACA,MAEE0D,EAAa,KACT+B,EAAQlG,EAAED,KAAKkE,UAAUjC,KAAKvB,GAAgB,MAEhDyF,EAAO,IACU,UAAfA,EAAMC,QACJD,EAAME,SACRpG,EAAED,KAAKkE,UAAUe,SAAS1E,MACL,MAChB,KACC+F,EAAgBrG,EAAEmE,GAAanC,KAAKvB,GAAiB,GAEvD4F,KACAA,GAAetB,YAAYzE,MAK/B0F,EAAoB,IAClBE,EAAMI,aAAa,aACrBnC,EAAYmC,aAAa,aACzBJ,EAAMK,UAAUC,SAAS,aACzBrC,EAAYoC,UAAUC,SAAS,qBAG3BJ,SAAWpG,EAAED,KAAKkE,UAAUe,SAAS1E,KACzC4F,GAAO/D,QAAQ,YAGbsE,WACW,GAIjBR,QACGhC,SAASyC,aAAa,gBACxB1G,EAAED,KAAKkE,UAAUe,SAAS1E,IAG3B0F,KACAjG,KAAKkE,UAAU0C,YAAYrG,MAIjCkE,QAvGmB,aAwGfC,WAAW1E,KAAKkE,SAAU/D,QACvB+D,SAAW,QAKXoB,iBA9GY,SA8GK5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,GAEnBsF,MACI,IAAI9E,EAAOX,QAChBA,MAAMyF,KAAKtF,EAAUsF,IAGV,WAAX/C,KACGA,sDAhHe,mBA4H1BlB,UACCqE,GAAGvF,EAAMwF,eAAgBpF,EAA6B,SAACmD,KAChD+B,qBAEFiB,EAAShD,EAAMpF,OAEdwB,EAAE4G,GAAQ5B,SAAS1E,OACbN,EAAE4G,GAAQhC,QAAQnE,MAGtB4E,iBAAiBxF,KAAKG,EAAE4G,GAAS,YAEzChB,GAAGvF,EAAMwG,oBAAqBpG,EAA6B,SAACmD,OACrDgD,EAAS5G,EAAE4D,EAAMpF,QAAQoG,QAAQnE,GAAiB,KACtDmG,GAAQD,YAAYrG,EAAiB,eAAe8C,KAAKQ,EAAMuC,WASnE1C,GAAGxD,GAAQS,EAAO2E,mBAClB5B,GAAGxD,GAAMb,YAAcsB,IACvB+C,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACNM,EAAO2E,kBAGT3E,GCjKHoG,EAAY,SAAC9G,OAOXC,EAAyB,WAEzBC,EAAyB,cACzBC,EAAAA,IAA6BD,EAE7BE,EAAyBJ,EAAEyD,GAAGxD,GAM9B8G,YACO,cACA,SACA,QACA,cACA,GAGPC,YACO,4BACA,gBACA,yBACA,wBACA,WAGPC,EACO,OADPA,EAEO,OAFPA,EAGO,OAHPA,EAIO,QAGP5G,iBACqBF,cACDA,oBACGA,0BACGA,0BACAA,sBACFA,uBACJA,EArCK,mCAsCJA,EAtCI,aAyCzBG,EACO,WADPA,EAEO,SAFPA,EAGO,QAHPA,EAIO,sBAJPA,EAKO,qBALPA,EAMO,qBANPA,EAOO,qBAIPG,UACU,sBACA,6BACA,2BACA,sDACA,kCACA,0CACA,0BASVqG,wBACQrF,EAASgB,QACdyE,OAAqB,UACrBC,UAAqB,UACrBC,eAAqB,UAErBC,WAAqB,OACrBC,YAAqB,OAErBC,aAAqB,UAErBC,QAAqBzH,KAAK0H,WAAWhF,QACrCwB,SAAqBjE,EAAEyB,GAAS,QAChCiG,mBAAqB1H,EAAED,KAAKkE,UAAUjC,KAAKvB,EAASkH,YAAY,QAEhEC,gDAePC,KA7GqB,WA8Gd9H,KAAKuH,iBACHQ,OAAOb,MAIhBc,gBAnHqB,YAsHdxG,SAASyG,QACXhI,EAAED,KAAKkE,UAAUH,GAAG,aAAsD,WAAvC9D,EAAED,KAAKkE,UAAUgE,IAAI,oBACpDJ,UAITK,KA5HqB,WA6HdnI,KAAKuH,iBACHQ,OAAOb,MAIhBkB,MAlIqB,SAkIfvE,GACCA,SACEyD,WAAY,GAGfrH,EAAED,KAAKkE,UAAUjC,KAAKvB,EAAS2H,WAAW,IAC5CzH,EAAKgD,4BACAzC,qBAAqBnB,KAAKkE,eAC1BoE,OAAM,kBAGCtI,KAAKoH,gBACdA,UAAY,QAGnBkB,MAjJqB,SAiJfzE,GACCA,SACEyD,WAAY,GAGftH,KAAKoH,0BACOpH,KAAKoH,gBACdA,UAAY,MAGfpH,KAAKyH,QAAQc,WAAavI,KAAKsH,iBAC5BF,UAAYoB,aACdhH,SAASiH,gBAAkBzI,KAAKgI,gBAAkBhI,KAAK8H,MAAMY,KAAK1I,MACnEA,KAAKyH,QAAQc,cAKnBI,GAnKqB,SAmKlBC,mBACIvB,eAAiBpH,EAAED,KAAKkE,UAAUjC,KAAKvB,EAASmI,aAAa,OAE5DC,EAAc9I,KAAK+I,cAAc/I,KAAKqH,qBAExCuB,EAAQ5I,KAAKmH,OAAOvI,OAAS,GAAKgK,EAAQ,MAI1C5I,KAAKuH,aACLvH,KAAKkE,UAAUjD,IAAIX,EAAM0I,KAAM,kBAAM5H,EAAKuH,GAAGC,aAI7CE,IAAgBF,cACbR,kBACAE,YAIDW,EAAYL,EAAQE,EACtB5B,EACAA,OAECa,OAAOkB,EAAWjJ,KAAKmH,OAAOyB,QAGrCnE,QA9LqB,aA+LjBzE,KAAKkE,UAAUgF,IAAI9I,KACnBsE,WAAW1E,KAAKkE,SAAU/D,QAEvBgH,OAAqB,UACrBM,QAAqB,UACrBvD,SAAqB,UACrBkD,UAAqB,UACrBE,UAAqB,UACrBC,WAAqB,UACrBF,eAAqB,UACrBM,mBAAqB,QAK5BD,WA9MqB,SA8MVhF,iBAEJsE,EACAtE,KAEAyG,gBAAgBjJ,EAAMwC,EAAQuE,GAC5BvE,KAGTmF,mBAvNqB,sBAwNf7H,KAAKyH,QAAQ2B,YACbpJ,KAAKkE,UACJ2B,GAAGvF,EAAM+I,QAAS,SAACxF,UAAUyF,EAAKC,SAAS1F,KAGrB,UAAvB7D,KAAKyH,QAAQW,UACbpI,KAAKkE,UACJ2B,GAAGvF,EAAMkJ,WAAY,SAAC3F,UAAUyF,EAAKlB,MAAMvE,KAC3CgC,GAAGvF,EAAMmJ,WAAY,SAAC5F,UAAUyF,EAAKhB,MAAMzE,KAC1C,iBAAkBrC,SAASkI,mBAQ3B1J,KAAKkE,UAAU2B,GAAGvF,EAAMqJ,SAAU,aAC7BvB,QACDkB,EAAK9B,2BACM8B,EAAK9B,gBAEfA,aAAeoC,WAAW,SAAC/F,UAAUyF,EAAKhB,MAAMzE,IA9NhC,IA8NiEyF,EAAK7B,QAAQc,gBAM3GgB,SApPqB,SAoPZ1F,OACH,kBAAkBR,KAAKQ,EAAMpF,OAAOoL,gBAIhChG,EAAMiG,YA3Oa,KA6OjBlE,sBACDuC,kBA7OkB,KAgPjBvC,sBACDkC,WAMXiB,cAtQqB,SAsQPrH,eACPyF,OAASlH,EAAE8J,UAAU9J,EAAEyB,GAASkD,SAAS3C,KAAKvB,EAASsJ,OACrDhK,KAAKmH,OAAO8C,QAAQvI,MAG7BwI,oBA3QqB,SA2QDjB,EAAW3C,OACvB6D,EAAkBlB,IAAc/B,EAChCkD,EAAkBnB,IAAc/B,EAChC4B,EAAkB9I,KAAK+I,cAAczC,GACrC+D,EAAkBrK,KAAKmH,OAAOvI,OAAS,MACrBwL,GAAmC,IAAhBtB,GACnBqB,GAAmBrB,IAAgBuB,KAErCrK,KAAKyH,QAAQ6C,YAC1BhE,MAIHiE,GAAazB,GADDG,IAAc/B,GAAkB,EAAI,IACZlH,KAAKmH,OAAOvI,cAEhC,IAAf2L,EACHvK,KAAKmH,OAAOnH,KAAKmH,OAAOvI,OAAS,GAAKoB,KAAKmH,OAAOoD,MAGxDC,mBA9RqB,SA8RFC,EAAeC,OAC1BC,EAAc3K,KAAK+I,cAAc0B,GACjCG,EAAY5K,KAAK+I,cAAc9I,EAAED,KAAKkE,UAAUjC,KAAKvB,EAASmI,aAAa,IAC3EgC,EAAa5K,EAAEK,MAAMA,EAAMwK,iCAEpBJ,OACLE,KACFD,aAGJ3K,KAAKkE,UAAU9B,QAAQyI,GAElBA,KAGTE,2BA7SqB,SA6SMrJ,MACrB1B,KAAK2H,mBAAoB,GACzB3H,KAAK2H,oBACJ1F,KAAKvB,EAASsK,QACdhG,YAAYzE,OAET0K,EAAgBjL,KAAK2H,mBAAmBuD,SAC5ClL,KAAK+I,cAAcrH,IAGjBuJ,KACAA,GAAeE,SAAS5K,OAKhCwH,OA7TqB,SA6TdkB,EAAWvH,OAQZ0J,EACAC,EACAX,SATEpE,EAAgBrG,EAAED,KAAKkE,UAAUjC,KAAKvB,EAASmI,aAAa,GAC5DyC,EAAqBtL,KAAK+I,cAAczC,GACxCiF,EAAgB7J,GAAW4E,GAC/BtG,KAAKkK,oBAAoBjB,EAAW3C,GAChCkF,EAAmBxL,KAAK+I,cAAcwC,GACtCE,EAAYnJ,QAAQtC,KAAKoH,cAM3B6B,IAAc/B,KACO3G,IACNA,IACI2G,MAEE3G,IACNA,IACI2G,GAGnBqE,GAAetL,EAAEsL,GAAatG,SAAS1E,QACpCgH,YAAa,WAIDvH,KAAKwK,mBAAmBe,EAAab,GACzCnG,sBAIV+B,GAAkBiF,QAKlBhE,YAAa,EAEdkE,QACGrD,aAGF2C,2BAA2BQ,OAE1BG,EAAYzL,EAAEK,MAAMA,EAAM0I,oBACfuC,YACJb,OACLY,KACFE,IAGF5K,EAAKgD,yBACP3D,EAAED,KAAKkE,UAAUe,SAAS1E,MACxBgL,GAAaJ,SAASE,KAEnBM,OAAOJ,KAEVjF,GAAe6E,SAASC,KACxBG,GAAaJ,SAASC,KAEtB9E,GACCrF,IAAIL,EAAKM,eAAgB,aACtBqK,GACCvG,YAAeoG,EADlB,IAC0CC,GACvCF,SAAS5K,KAEV+F,GAAetB,YAAezE,EAAhC,IAAoD8K,EAApD,IAAsED,KAEjE7D,YAAa,aAEP,kBAAMtH,EAAE2L,EAAK1H,UAAU9B,QAAQsJ,IAAY,KAEvD/H,qBAzXsB,SA2XvB2C,GAAetB,YAAYzE,KAC3BgL,GAAaJ,SAAS5K,QAEnBgH,YAAa,IAChBvH,KAAKkE,UAAU9B,QAAQsJ,IAGvBD,QACGnD,YAMFhD,iBAtZc,SAsZG5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,GACpBsH,EAAAA,KACCT,EACA/G,EAAED,MAAMyF,QAGS,iBAAX/C,WAEJ+E,EACA/E,QAIDmJ,EAA2B,iBAAXnJ,EAAsBA,EAAS+E,EAAQqE,SAExDrG,MACI,IAAIsB,EAAS/G,KAAMyH,KACxBzH,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,IACJiG,GAAGjG,QACH,GAAsB,iBAAXmJ,EAAqB,IACT,oBAAjBpG,EAAKoG,SACR,IAAIE,UAAJ,oBAAkCF,EAAlC,OAEHA,UACIpE,EAAQc,aACZH,UACAE,cAKJ0D,qBA1bc,SA0bOnI,OACpBlC,EAAWf,EAAK+D,uBAAuB3E,SAExC2B,OAIClD,EAASwB,EAAE0B,GAAU,MAEtBlD,GAAWwB,EAAExB,GAAQwG,SAAS1E,QAI7BmC,EAAAA,KACDzC,EAAExB,GAAQgH,OACVxF,EAAED,MAAMyF,QAEPwG,EAAajM,KAAK4B,aAAa,iBAEjCqK,MACK1D,UAAW,KAGXjD,iBAAiBxF,KAAKG,EAAExB,GAASiE,GAEtCuJ,KACAxN,GAAQgH,KAAKtF,GAAUwI,GAAGsD,KAGxBrG,kEA/cqB,+CAgGpBoB,oBAyXTxF,UACCqE,GAAGvF,EAAMwF,eAAgBpF,EAASwL,WAAYnF,EAASiF,wBAExDxI,QAAQqC,GAAGvF,EAAM6L,cAAe,aAC9BzL,EAAS0L,WAAW7G,KAAK,eACnB8G,EAAYpM,EAAED,QACXsF,iBAAiBxF,KAAKuM,EAAWA,EAAU5G,cAUtD/B,GAAGxD,GAAQ6G,EAASzB,mBACpB5B,GAAGxD,GAAMb,YAAc0H,IACvBrD,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACN0G,EAASzB,kBAGXyB,EAxfS,CAyff9G,GCzfGqM,EAAY,SAACrM,OAOXC,EAAsB,WAEtBC,EAAsB,cACtBC,EAAAA,IAA0BD,EAE1BE,EAAsBJ,EAAEyD,GAAGxD,GAG3B8G,WACK,SACA,IAGLC,UACK,iBACA,oBAGL3G,eACoBF,gBACCA,cACDA,kBACEA,yBACDA,EAnBC,aAsBtBG,EACS,OADTA,EAES,WAFTA,EAGS,aAHTA,EAIS,YAGTgM,EACK,QADLA,EAEK,SAGL7L,WACU,iCACA,4BASV4L,wBACQ5K,EAASgB,QACd8J,kBAAmB,OACnBtI,SAAmBxC,OACnB+F,QAAmBzH,KAAK0H,WAAWhF,QACnC+J,cAAmBxM,EAAE8J,UAAU9J,EAClC,mCAAmCyB,EAAQgL,GAA3C,6CAC0ChL,EAAQgL,GADlD,eAGIC,EAAa1M,EAAES,EAASkM,aACrBjO,EAAI,EAAGA,EAAIgO,EAAW/N,OAAQD,IAAK,KACpCkO,EAAOF,EAAWhO,GAClBgD,EAAWf,EAAK+D,uBAAuBkI,GAC5B,OAAblL,GAAqB1B,EAAE0B,GAAUmL,OAAOpL,GAAS9C,OAAS,SACvDmO,UAAYpL,OACZ8K,cAAcO,KAAKH,SAIvBI,QAAUjN,KAAKyH,QAAQ7C,OAAS5E,KAAKkN,aAAe,KAEpDlN,KAAKyH,QAAQ7C,aACXuI,0BAA0BnN,KAAKkE,SAAUlE,KAAKyM,eAGjDzM,KAAKyH,QAAQzB,aACVA,oCAgBTA,OAlGqB,WAmGf/F,EAAED,KAAKkE,UAAUe,SAAS1E,QACvB6M,YAEAC,UAITA,KA1GqB,eAgHfC,EACAC,aANAvN,KAAKwM,mBACPvM,EAAED,KAAKkE,UAAUe,SAAS1E,KAOxBP,KAAKiN,SAMgB,OALbhN,EAAE8J,UACV9J,EAAED,KAAKiN,SACJhL,KAAKvB,EAAS8M,SACdV,OAFH,iBAE2B9M,KAAKyH,QAAQ7C,OAFxC,QAIUhG,WACA,QAIV0O,MACYrN,EAAEqN,GAASG,IAAIzN,KAAK+M,WAAWtH,KAAKtF,KAC/BoN,EAAYf,wBAK3BkB,EAAazN,EAAEK,MAAMA,EAAMqN,WAC/B3N,KAAKkE,UAAU9B,QAAQsL,IACrBA,EAAWnJ,sBAIX+I,MACOhI,iBAAiBxF,KAAKG,EAAEqN,GAASG,IAAIzN,KAAK+M,WAAY,QAC1DQ,KACDD,GAAS7H,KAAKtF,EAAU,WAIxByN,EAAY5N,KAAK6N,kBAErB7N,KAAKkE,UACJc,YAAYzE,GACZ4K,SAAS5K,QAEP2D,SAAS4J,MAAMF,GAAa,EAE7B5N,KAAKyM,cAAc7N,OAAS,KAC5BoB,KAAKyM,eACJzH,YAAYzE,GACZwN,KAAK,iBAAiB,QAGtBC,kBAAiB,OAEhBC,EAAW,aACb7M,EAAK8C,UACJc,YAAYzE,GACZ4K,SAAS5K,GACT4K,SAAS5K,KAEP2D,SAAS4J,MAAMF,GAAa,KAE5BI,kBAAiB,KAEpB5M,EAAK8C,UAAU9B,QAAQ9B,EAAM4N,WAG5BtN,EAAKgD,6BAMJuK,EAAAA,UADuBP,EAAU,GAAGrK,cAAgBqK,EAAUQ,MAAM,MAGxEpO,KAAKkE,UACJjD,IAAIL,EAAKM,eAAgB+M,GACzBtK,qBA5KqB,UA8KnBO,SAAS4J,MAAMF,GAAgB5N,KAAKkE,SAASiK,GAAlD,mBAGFf,KA9LqB,0BA+LfpN,KAAKwM,kBACNvM,EAAED,KAAKkE,UAAUe,SAAS1E,QAIvBmN,EAAazN,EAAEK,MAAMA,EAAM+N,WAC/BrO,KAAKkE,UAAU9B,QAAQsL,IACrBA,EAAWnJ,0BAITqJ,EAAY5N,KAAK6N,wBAElB3J,SAAS4J,MAAMF,GAAgB5N,KAAKkE,SAASoK,wBAAwBV,GAA1E,OAEKjC,OAAO3L,KAAKkE,YAEflE,KAAKkE,UACJiH,SAAS5K,GACTyE,YAAYzE,GACZyE,YAAYzE,GAEXP,KAAKyM,cAAc7N,OAAS,MACzB,IAAID,EAAI,EAAGA,EAAIqB,KAAKyM,cAAc7N,OAAQD,IAAK,KAC5CyD,EAAUpC,KAAKyM,cAAc9N,GAC7BgD,EAAWf,EAAK+D,uBAAuBvC,MAC5B,OAAbT,EACY1B,EAAE0B,GACLsD,SAAS1E,MAChB6B,GAAS+I,SAAS5K,GACjBwN,KAAK,iBAAiB,QAM5BC,kBAAiB,OAEhBC,EAAW,aACVD,kBAAiB,KACpB1E,EAAKpF,UACJc,YAAYzE,GACZ4K,SAAS5K,GACT6B,QAAQ9B,EAAMiO,cAGdrK,SAAS4J,MAAMF,GAAa,GAE5BhN,EAAKgD,0BAKR5D,KAAKkE,UACJjD,IAAIL,EAAKM,eAAgB+M,GACzBtK,qBAzOqB,cA4O1BqK,iBAzPqB,SAyPJQ,QACVhC,iBAAmBgC,KAG1B/J,QA7PqB,aA8PjBC,WAAW1E,KAAKkE,SAAU/D,QAEvBsH,QAAmB,UACnBwF,QAAmB,UACnB/I,SAAmB,UACnBuI,cAAmB,UACnBD,iBAAmB,QAK1B9E,WAzQqB,SAyQVhF,iBAEJsE,EACAtE,IAEEsD,OAAS1D,QAAQI,EAAOsD,UAC1BmD,gBAAgBjJ,EAAMwC,EAAQuE,GAC5BvE,KAGTmL,cAnRqB,kBAoRF5N,EAAED,KAAKkE,UAAUe,SAASsH,GACzBA,EAAkBA,KAGtCW,WAxRqB,sBAyRftI,EAAS,KACThE,EAAKoC,UAAUhD,KAAKyH,QAAQ7C,WACrB5E,KAAKyH,QAAQ7C,OAGoB,oBAA/B5E,KAAKyH,QAAQ7C,OAAO6J,WACpBzO,KAAKyH,QAAQ7C,OAAO,OAGtB3E,EAAED,KAAKyH,QAAQ7C,QAAQ,OAG5BjD,EAAAA,yCACqC3B,KAAKyH,QAAQ7C,OADlD,cAGJA,GAAQ3C,KAAKN,GAAU4D,KAAK,SAAC5G,EAAG+C,KAC3ByL,0BACHb,EAASoC,sBAAsBhN,IAC9BA,MAIEkD,KAGTuI,0BAlTqB,SAkTKzL,EAASiN,MAC7BjN,EAAS,KACLkN,EAAS3O,EAAEyB,GAASuD,SAAS1E,GAE/BoO,EAAa/P,OAAS,KACtB+P,GACC/H,YAAYrG,GAAsBqO,GAClCb,KAAK,gBAAiBa,OAOxBF,sBAhUc,SAgUQhN,OACrBC,EAAWf,EAAK+D,uBAAuBjD,UACtCC,EAAW1B,EAAE0B,GAAU,GAAK,QAG9B2D,iBArUc,SAqUG5C,UACf1C,KAAKuF,KAAK,eACTsJ,EAAU5O,EAAED,MACdyF,EAAYoJ,EAAMpJ,KAAKtF,GACrBsH,EAAAA,KACDT,EACA6H,EAAMpJ,OACY,iBAAX/C,GAAuBA,OAG9B+C,GAAQgC,EAAQzB,QAAU,YAAY3C,KAAKX,OACtCsD,QAAS,GAGdP,MACI,IAAI6G,EAAStM,KAAMyH,KACpBhC,KAAKtF,EAAUsF,IAGD,iBAAX/C,EAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,uDApVe,+CAqFjBsE,oBA2QTxF,UAAUqE,GAAGvF,EAAMwF,eAAgBpF,EAASkM,YAAa,SAAU/I,GAE/B,MAAhCA,EAAMiL,cAAcjF,WAChBjE,qBAGFmJ,EAAW9O,EAAED,MACb2B,EAAWf,EAAK+D,uBAAuB3E,QAC3C2B,GAAU4D,KAAK,eACTyJ,EAAU/O,EAAED,MAEZ0C,EADUsM,EAAQvJ,KAAKtF,GACN,SAAW4O,EAAStJ,SAClCH,iBAAiBxF,KAAKkP,EAAStM,SAU1CgB,GAAGxD,GAAQoM,EAAShH,mBACpB5B,GAAGxD,GAAMb,YAAciN,IACvB5I,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACNiM,EAAShH,kBAGXgH,EArYS,CAsYfrM,GLxXCgP,EAA8B,oBAAXzL,QAA8C,oBAAbhC,SACpD0N,GAAyB,OAAQ,UAAW,WAC5CC,EAAkB,EACbxQ,EAAI,EAAGA,EAAIuQ,EAAsBtQ,OAAQD,GAAK,EACrD,GAAIsQ,GAAaG,UAAUC,UAAUpF,QAAQiF,EAAsBvQ,KAAO,EAAG,CAC3EwQ,EAAkB,EAClB,MA+BJ,IAWIG,EAXqBL,GAAazL,OAAO+L,QA3B7C,SAA2B7L,GACzB,IAAI1C,GAAS,EACb,OAAO,WACDA,IAGJA,GAAS,EACTwC,OAAO+L,QAAQC,UAAUC,KAAK,WAC5BzO,GAAS,EACT0C,SAKN,SAAsBA,GACpB,IAAIgM,GAAY,EAChB,OAAO,WACAA,IACHA,GAAY,EACZ9F,WAAW,WACT8F,GAAY,EACZhM,KACCyL,MAyBT,SAASQ,EAAWC,GAElB,OAAOA,GAA8D,yBAAnC3M,SAASnD,KAAK8P,GAUlD,SAASC,EAAyBnO,EAASkB,GACzC,GAAyB,IAArBlB,EAAQc,SACV,SAGF,IAAI0F,EAAM4H,iBAAiBpO,EAAS,MACpC,OAAOkB,EAAWsF,EAAItF,GAAYsF,EAUpC,SAAS6H,EAAcrO,GACrB,MAAyB,SAArBA,EAAQsO,SACHtO,EAEFA,EAAQuO,YAAcvO,EAAQwO,KAUvC,SAASC,EAAgBzO,GAEvB,IAAKA,EACH,OAAOF,SAAS4O,KAGlB,OAAQ1O,EAAQsO,UACd,IAAK,OACL,IAAK,OACH,OAAOtO,EAAQ2O,cAAcD,KAC/B,IAAK,YACH,OAAO1O,EAAQ0O,KAKnB,IAAIE,EAAwBT,EAAyBnO,GACjD6O,EAAWD,EAAsBC,SACjCC,EAAYF,EAAsBE,UAClCC,EAAYH,EAAsBG,UAEtC,MAAI,gBAAgBpN,KAAKkN,EAAWE,EAAYD,GACvC9O,EAGFyO,EAAgBJ,EAAcrO,IAUvC,SAASgP,EAAgBhP,GAEvB,IAAIiP,EAAejP,GAAWA,EAAQiP,aAClCX,EAAWW,GAAgBA,EAAaX,SAE5C,OAAKA,GAAyB,SAAbA,GAAoC,SAAbA,GAUgB,KAAnD,KAAM,SAAS/F,QAAQ0G,EAAaX,WAA2E,WAAvDH,EAAyBc,EAAc,YAC3FD,EAAgBC,GAGlBA,EAbDjP,EACKA,EAAQ2O,cAAc3G,gBAGxBlI,SAASkI,gBA4BpB,SAASkH,EAAQC,GACf,OAAwB,OAApBA,EAAKZ,WACAW,EAAQC,EAAKZ,YAGfY,EAWT,SAASC,EAAuBC,EAAUC,GAExC,KAAKD,GAAaA,EAASvO,UAAawO,GAAaA,EAASxO,UAC5D,OAAOhB,SAASkI,gBAIlB,IAAIuH,EAAQF,EAASG,wBAAwBF,GAAYG,KAAKC,4BAC1DC,EAAQJ,EAAQF,EAAWC,EAC3B3O,EAAM4O,EAAQD,EAAWD,EAGzBO,EAAQ9P,SAAS+P,cACrBD,EAAME,SAASH,EAAO,GACtBC,EAAMG,OAAOpP,EAAK,GAClB,IA/CyBX,EACrBsO,EA8CA0B,EAA0BJ,EAAMI,wBAIpC,GAAIX,IAAaW,GAA2BV,IAAaU,GAA2BL,EAAM5K,SAASpE,GACjG,MAjDe,UAFb2N,GADqBtO,EAoDDgQ,GAnDD1B,WAKH,SAAbA,GAAuBU,EAAgBhP,EAAQiQ,qBAAuBjQ,EAkDpEgP,EAAgBgB,GAHdA,EAOX,IAAIE,EAAehB,EAAQG,GAC3B,OAAIa,EAAa1B,KACRY,EAAuBc,EAAa1B,KAAMc,GAE1CF,EAAuBC,EAAUH,EAAQI,GAAUd,MAY9D,SAAS2B,EAAUnQ,GACjB,IAEIoQ,EAAqB,SAFdnS,UAAUf,OAAS,QAAsBmT,IAAjBpS,UAAU,GAAmBA,UAAU,GAAK,OAE9C,YAAc,aAC3CqQ,EAAWtO,EAAQsO,SAEvB,GAAiB,SAAbA,GAAoC,SAAbA,EAAqB,CAC9C,IAAIgC,EAAOtQ,EAAQ2O,cAAc3G,gBAEjC,OADuBhI,EAAQ2O,cAAc4B,kBAAoBD,GACzCF,GAG1B,OAAOpQ,EAAQoQ,GAmCjB,SAASI,EAAeC,EAAQC,GAC9B,IAAIC,EAAiB,MAATD,EAAe,OAAS,MAChCE,EAAkB,SAAVD,EAAmB,QAAU,SAEzC,OAAOE,WAAWJ,EAAO,SAAWE,EAAQ,SAAU,IAAME,WAAWJ,EAAO,SAAWG,EAAQ,SAAU,IAS7G,IAAIE,OAAST,EAETU,EAAW,WAIb,YAHeV,IAAXS,IACFA,GAAsD,IAA7CpD,UAAUsD,WAAWzI,QAAQ,YAEjCuI,GAGT,SAASG,EAAQP,EAAMhC,EAAM4B,EAAMY,GACjC,OAAOtR,KAAKuR,IAAIzC,EAAK,SAAWgC,GAAOhC,EAAK,SAAWgC,GAAOJ,EAAK,SAAWI,GAAOJ,EAAK,SAAWI,GAAOJ,EAAK,SAAWI,GAAOK,IAAaT,EAAK,SAAWI,GAAQQ,EAAc,UAAqB,WAATR,EAAoB,MAAQ,SAAWQ,EAAc,UAAqB,WAATR,EAAoB,SAAW,UAAY,GAGhT,SAASU,IACP,IAAI1C,EAAO5O,SAAS4O,KAChB4B,EAAOxQ,SAASkI,gBAChBkJ,EAAgBH,KAAc3C,iBAAiBkC,GAEnD,OACEe,OAAQJ,EAAQ,SAAUvC,EAAM4B,EAAMY,GACtCI,MAAOL,EAAQ,QAASvC,EAAM4B,EAAMY,IAIxC,IAAIK,GAAiB,SAAUC,EAAU7T,GACvC,KAAM6T,aAAoB7T,GACxB,MAAM,IAAI0M,UAAU,sCAIpBoH,GAAc,WAChB,SAASC,EAAiB3U,EAAQC,GAChC,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CACrC,IAAIE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWM,IAAKN,IAIlD,OAAO,SAAUQ,EAAaC,EAAYC,GAGxC,OAFID,GAAY8T,EAAiB/T,EAAYG,UAAWF,GACpDC,GAAa6T,EAAiB/T,EAAaE,GACxCF,GAdO,GAsBdH,GAAiB,SAAUqD,EAAKpD,EAAK2D,GAYvC,OAXI3D,KAAOoD,EACTtD,OAAOC,eAAeqD,EAAKpD,GACzB2D,MAAOA,EACPhE,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZuD,EAAIpD,GAAO2D,EAGNP,GAGL9C,GAAWR,OAAOS,QAAU,SAAUjB,GACxC,IAAK,IAAIE,EAAI,EAAGA,EAAIgB,UAAUf,OAAQD,IAAK,CACzC,IAAIiB,EAASD,UAAUhB,GAEvB,IAAK,IAAIQ,KAAOS,EACVX,OAAOO,UAAUK,eAAeC,KAAKF,EAAQT,KAC/CV,EAAOU,GAAOS,EAAOT,IAK3B,OAAOV,GAUT,SAAS4U,GAAcC,GACrB,OAAO7T,MAAa6T,GAClBC,MAAOD,EAAQE,KAAOF,EAAQN,MAC9BS,OAAQH,EAAQI,IAAMJ,EAAQP,SAWlC,SAASzE,GAAsB5M,GAC7B,IAAIiS,KAKJ,GAAIlB,IACF,IACEkB,EAAOjS,EAAQ4M,wBACf,IAAIsF,EAAY/B,EAAUnQ,EAAS,OAC/BmS,EAAahC,EAAUnQ,EAAS,QACpCiS,EAAKD,KAAOE,EACZD,EAAKH,MAAQK,EACbF,EAAKF,QAAUG,EACfD,EAAKJ,OAASM,EACd,MAAO3R,SAETyR,EAAOjS,EAAQ4M,wBAGjB,IAAIwF,GACFN,KAAMG,EAAKH,KACXE,IAAKC,EAAKD,IACVV,MAAOW,EAAKJ,MAAQI,EAAKH,KACzBT,OAAQY,EAAKF,OAASE,EAAKD,KAIzBK,EAA6B,SAArBrS,EAAQsO,SAAsB8C,OACtCE,EAAQe,EAAMf,OAAStR,EAAQsS,aAAeF,EAAOP,MAAQO,EAAON,KACpET,EAASgB,EAAMhB,QAAUrR,EAAQuS,cAAgBH,EAAOL,OAASK,EAAOJ,IAExEQ,EAAiBxS,EAAQyS,YAAcnB,EACvCoB,EAAgB1S,EAAQS,aAAe4Q,EAI3C,GAAImB,GAAkBE,EAAe,CACnC,IAAIjC,EAAStC,EAAyBnO,GACtCwS,GAAkBhC,EAAeC,EAAQ,KACzCiC,GAAiBlC,EAAeC,EAAQ,KAExC2B,EAAOd,OAASkB,EAChBJ,EAAOf,QAAUqB,EAGnB,OAAOf,GAAcS,GAGvB,SAASO,GAAqCnJ,EAAUtG,GACtD,IAAI4N,EAASC,IACT6B,EAA6B,SAApB1P,EAAOoL,SAChBuE,EAAejG,GAAsBpD,GACrCsJ,EAAalG,GAAsB1J,GACnC6P,EAAetE,EAAgBjF,GAE/BiH,EAAStC,EAAyBjL,GAClC8P,EAAiBnC,WAAWJ,EAAOuC,eAAgB,IACnDC,EAAkBpC,WAAWJ,EAAOwC,gBAAiB,IAErDrB,EAAUD,IACZK,IAAKa,EAAab,IAAMc,EAAWd,IAAMgB,EACzClB,KAAMe,EAAaf,KAAOgB,EAAWhB,KAAOmB,EAC5C3B,MAAOuB,EAAavB,MACpBD,OAAQwB,EAAaxB,SASvB,GAPAO,EAAQsB,UAAY,EACpBtB,EAAQuB,WAAa,GAMhBrC,GAAU8B,EAAQ,CACrB,IAAIM,EAAYrC,WAAWJ,EAAOyC,UAAW,IACzCC,EAAatC,WAAWJ,EAAO0C,WAAY,IAE/CvB,EAAQI,KAAOgB,EAAiBE,EAChCtB,EAAQG,QAAUiB,EAAiBE,EACnCtB,EAAQE,MAAQmB,EAAkBE,EAClCvB,EAAQC,OAASoB,EAAkBE,EAGnCvB,EAAQsB,UAAYA,EACpBtB,EAAQuB,WAAaA,EAOvB,OAJIrC,EAAS5N,EAAO6B,SAASgO,GAAgB7P,IAAW6P,GAA0C,SAA1BA,EAAazE,YACnFsD,EAlOJ,SAAuBK,EAAMjS,GAC3B,IAAIoT,EAAWnV,UAAUf,OAAS,QAAsBmT,IAAjBpS,UAAU,IAAmBA,UAAU,GAE1EiU,EAAY/B,EAAUnQ,EAAS,OAC/BmS,EAAahC,EAAUnQ,EAAS,QAChCqT,EAAWD,GAAY,EAAI,EAK/B,OAJAnB,EAAKD,KAAOE,EAAYmB,EACxBpB,EAAKF,QAAUG,EAAYmB,EAC3BpB,EAAKH,MAAQK,EAAakB,EAC1BpB,EAAKJ,OAASM,EAAakB,EACpBpB,EAwNKqB,CAAc1B,EAAS1O,IAG5B0O,EAmDT,SAAS2B,GAAcC,EAAQC,EAAWC,EAASC,GAEjD,IAlDqD3T,EACjDsQ,EACAsD,EACAtC,EACAD,EAEAa,EACAC,EA2CA0B,GAAe7B,IAAK,EAAGF,KAAM,GAC7B7C,EAAeG,EAAuBoE,EAAQC,GAGlD,GAA0B,aAAtBE,EArDArD,GADiDtQ,EAuDQiP,GAtD1CN,cAAc3G,gBAC7B4L,EAAiBjB,GAAqC3S,EAASsQ,GAC/DgB,EAAQ1R,KAAKuR,IAAIb,EAAKgC,YAAaxQ,OAAOgS,YAAc,GACxDzC,EAASzR,KAAKuR,IAAIb,EAAKiC,aAAczQ,OAAOiS,aAAe,GAE3D7B,EAAY/B,EAAUG,GACtB6B,EAAahC,EAAUG,EAAM,QAgD/BuD,EAvCKlC,IANLK,IAAKE,EAAY0B,EAAe5B,IAAM4B,EAAeV,UACrDpB,KAAMK,EAAayB,EAAe9B,KAAO8B,EAAeT,WACxD7B,MAAOA,EACPD,OAAQA,QA2CH,CAEL,IAAI2C,OAAiB,EACK,iBAAtBL,EAE8B,UADhCK,EAAiBvF,EAAgBJ,EAAcoF,KAC5BnF,WACjB0F,EAAiBR,EAAO7E,cAAc3G,iBAGxCgM,EAD+B,WAAtBL,EACQH,EAAO7E,cAAc3G,gBAErB2L,EAGnB,IAAI/B,EAAUe,GAAqCqB,EAAgB/E,GAGnE,GAAgC,SAA5B+E,EAAe1F,UA9CvB,SAAS2F,EAAQjU,GACf,IAAIsO,EAAWtO,EAAQsO,SACvB,MAAiB,SAAbA,GAAoC,SAAbA,IAG2B,UAAlDH,EAAyBnO,EAAS,aAG/BiU,EAAQ5F,EAAcrO,KAsCgBiU,CAAQhF,GAWjD4E,EAAajC,MAXmD,CAChE,IAAIsC,EAAkB9C,IAClBC,EAAS6C,EAAgB7C,OACzBC,EAAQ4C,EAAgB5C,MAE5BuC,EAAW7B,KAAOJ,EAAQI,IAAMJ,EAAQsB,UACxCW,EAAW9B,OAASV,EAASO,EAAQI,IACrC6B,EAAW/B,MAAQF,EAAQE,KAAOF,EAAQuB,WAC1CU,EAAWhC,MAAQP,EAAQM,EAAQE,MAavC,OALA+B,EAAW/B,MAAQ4B,EACnBG,EAAW7B,KAAO0B,EAClBG,EAAWhC,OAAS6B,EACpBG,EAAW9B,QAAU2B,EAEdG,EAmBT,SAASM,GAAqBC,EAAWC,EAASb,EAAQC,EAAWE,GACnE,IAAID,EAAUzV,UAAUf,OAAS,QAAsBmT,IAAjBpS,UAAU,GAAmBA,UAAU,GAAK,EAElF,IAAmC,IAA/BmW,EAAU7L,QAAQ,QACpB,OAAO6L,EAGT,IAAIP,EAAaN,GAAcC,EAAQC,EAAWC,EAASC,GAEvDW,GACFtC,KACEV,MAAOuC,EAAWvC,MAClBD,OAAQgD,EAAQrC,IAAM6B,EAAW7B,KAEnCH,OACEP,MAAOuC,EAAWhC,MAAQwC,EAAQxC,MAClCR,OAAQwC,EAAWxC,QAErBU,QACET,MAAOuC,EAAWvC,MAClBD,OAAQwC,EAAW9B,OAASsC,EAAQtC,QAEtCD,MACER,MAAO+C,EAAQvC,KAAO+B,EAAW/B,KACjCT,OAAQwC,EAAWxC,SAInBkD,EAAchX,OAAOiX,KAAKF,GAAOG,IAAI,SAAUhX,GACjD,OAAOM,IACLN,IAAKA,GACJ6W,EAAM7W,IACPiX,MAhDWC,EAgDGL,EAAM7W,GA/CZkX,EAAKrD,MACJqD,EAAKtD,UAFpB,IAAiBsD,IAkDZC,KAAK,SAAUC,EAAGC,GACnB,OAAOA,EAAEJ,KAAOG,EAAEH,OAGhBK,EAAgBR,EAAYnJ,OAAO,SAAU4J,GAC/C,IAAI1D,EAAQ0D,EAAM1D,MACdD,EAAS2D,EAAM3D,OACnB,OAAOC,GAASkC,EAAOlB,aAAejB,GAAUmC,EAAOjB,eAGrD0C,EAAoBF,EAAc7X,OAAS,EAAI6X,EAAc,GAAGtX,IAAM8W,EAAY,GAAG9W,IAErFyX,EAAYd,EAAUe,MAAM,KAAK,GAErC,OAAOF,GAAqBC,EAAY,IAAMA,EAAY,IAY5D,SAASE,GAAoBC,EAAO7B,EAAQC,GAE1C,OAAOd,GAAqCc,EADnBrE,EAAuBoE,EAAQC,IAW1D,SAAS6B,GAActV,GACrB,IAAIyQ,EAASrC,iBAAiBpO,GAC1BuV,EAAI1E,WAAWJ,EAAOyC,WAAarC,WAAWJ,EAAO+E,cACrDC,EAAI5E,WAAWJ,EAAO0C,YAActC,WAAWJ,EAAOiF,aAK1D,OAHEpE,MAAOtR,EAAQyS,YAAcgD,EAC7BpE,OAAQrR,EAAQS,aAAe8U,GAYnC,SAASI,GAAqBvB,GAC5B,IAAIwB,GAAS9D,KAAM,QAASD,MAAO,OAAQE,OAAQ,MAAOC,IAAK,UAC/D,OAAOoC,EAAU9T,QAAQ,yBAA0B,SAAUuV,GAC3D,OAAOD,EAAKC,KAchB,SAASC,GAAiBtC,EAAQuC,EAAkB3B,GAClDA,EAAYA,EAAUe,MAAM,KAAK,GAGjC,IAAIa,EAAaV,GAAc9B,GAG3ByC,GACF3E,MAAO0E,EAAW1E,MAClBD,OAAQ2E,EAAW3E,QAIjB6E,GAAoD,KAAzC,QAAS,QAAQ3N,QAAQ6L,GACpC+B,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAStC,OAPAD,EAAcE,GAAYJ,EAAiBI,GAAYJ,EAAiBM,GAAe,EAAIL,EAAWK,GAAe,EAEnHJ,EAAcG,GADZhC,IAAcgC,EACeL,EAAiBK,GAAiBJ,EAAWM,GAE7CP,EAAiBJ,GAAqBS,IAGhEH,EAYT,SAAS1V,GAAKgW,EAAKC,GAEjB,OAAIC,MAAM3Y,UAAUyC,KACXgW,EAAIhW,KAAKiW,GAIXD,EAAInL,OAAOoL,GAAO,GAqC3B,SAASE,GAAaC,EAAW5S,EAAM6S,GAoBrC,YAnB8BvG,IAATuG,EAAqBD,EAAYA,EAAUjK,MAAM,EA1BxE,SAAmB6J,EAAKM,EAAMzV,GAE5B,GAAIqV,MAAM3Y,UAAUgZ,UAClB,OAAOP,EAAIO,UAAU,SAAUC,GAC7B,OAAOA,EAAIF,KAAUzV,IAKzB,IAAII,EAAQjB,GAAKgW,EAAK,SAAU1V,GAC9B,OAAOA,EAAIgW,KAAUzV,IAEvB,OAAOmV,EAAIhO,QAAQ/G,GAcsDsV,CAAUH,EAAW,OAAQC,KAEvFI,QAAQ,SAAU3D,GAC3BA,EAAmB,UAErB4D,QAAQC,KAAK,yDAEf,IAAIlV,EAAKqR,EAAmB,UAAKA,EAASrR,GACtCqR,EAAS8D,SAAWlJ,EAAWjM,KAIjC+B,EAAK6N,QAAQ4B,OAAS7B,GAAc5N,EAAK6N,QAAQ4B,QACjDzP,EAAK6N,QAAQ6B,UAAY9B,GAAc5N,EAAK6N,QAAQ6B,WAEpD1P,EAAO/B,EAAG+B,EAAMsP,MAIbtP,EA2DT,SAASqT,GAAkBT,EAAWU,GACpC,OAAOV,EAAUW,KAAK,SAAU3C,GAC9B,IAAI4C,EAAO5C,EAAK4C,KAEhB,OADc5C,EAAKwC,SACDI,IAASF,IAW/B,SAASG,GAAyBtW,GAIhC,IAHA,IAAIuW,IAAY,EAAO,KAAM,SAAU,MAAO,KAC1CC,EAAYxW,EAASf,OAAO,GAAG0B,cAAgBX,EAASwL,MAAM,GAEzDzP,EAAI,EAAGA,EAAIwa,EAASva,OAAS,EAAGD,IAAK,CAC5C,IAAI0C,EAAS8X,EAASxa,GAClB0a,EAAUhY,EAAS,GAAKA,EAAS+X,EAAYxW,EACjD,GAA4C,oBAAjCpB,SAAS4O,KAAKtC,MAAMuL,GAC7B,OAAOA,EAGX,OAAO,KAmCT,SAASC,GAAU5X,GACjB,IAAI2O,EAAgB3O,EAAQ2O,cAC5B,OAAOA,EAAgBA,EAAckJ,YAAc/V,OAoBrD,SAASgW,GAAoBrE,EAAWsE,EAAS1C,EAAO2C,GAEtD3C,EAAM2C,YAAcA,EACpBJ,GAAUnE,GAAWwE,iBAAiB,SAAU5C,EAAM2C,aAAeE,SAAS,IAG9E,IAAIC,EAAgB1J,EAAgBgF,GAKpC,OA5BF,SAAS2E,EAAsBrF,EAAc5Q,EAAOkW,EAAUC,GAC5D,IAAIC,EAAmC,SAA1BxF,EAAazE,SACtBvR,EAASwb,EAASxF,EAAapE,cAAckJ,YAAc9E,EAC/DhW,EAAOkb,iBAAiB9V,EAAOkW,GAAYH,SAAS,IAE/CK,GACHH,EAAsB3J,EAAgB1R,EAAOwR,YAAapM,EAAOkW,EAAUC,GAE7EA,EAAchN,KAAKvO,GAgBnBqb,CAAsBD,EAAe,SAAU9C,EAAM2C,YAAa3C,EAAMiD,eACxEjD,EAAM8C,cAAgBA,EACtB9C,EAAMmD,eAAgB,EAEfnD,EA6CT,SAASoD,KAxBT,IAA8BhF,EAAW4B,EAyBnC/W,KAAK+W,MAAMmD,gBACbE,qBAAqBpa,KAAKqa,gBAC1Bra,KAAK+W,OA3BqB5B,EA2BQnV,KAAKmV,UA3BF4B,EA2Ba/W,KAAK+W,MAzBzDuC,GAAUnE,GAAWmF,oBAAoB,SAAUvD,EAAM2C,aAGzD3C,EAAMiD,cAActB,QAAQ,SAAUja,GACpCA,EAAO6b,oBAAoB,SAAUvD,EAAM2C,eAI7C3C,EAAM2C,YAAc,KACpB3C,EAAMiD,iBACNjD,EAAM8C,cAAgB,KACtB9C,EAAMmD,eAAgB,EACfnD,IAwBT,SAASwD,GAAUC,GACjB,MAAa,KAANA,IAAaC,MAAMlI,WAAWiI,KAAOE,SAASF,GAWvD,SAASG,GAAUjZ,EAASyQ,GAC1BlT,OAAOiX,KAAK/D,GAAQuG,QAAQ,SAAUH,GACpC,IAAIqC,EAAO,IAEkE,KAAxE,QAAS,SAAU,MAAO,QAAS,SAAU,QAAQ3Q,QAAQsO,IAAgBgC,GAAUpI,EAAOoG,MACjGqC,EAAO,MAETlZ,EAAQoM,MAAMyK,GAAQpG,EAAOoG,GAAQqC,IAuLzC,SAASC,GAAmBxC,EAAWyC,EAAgBC,GACrD,IAAIC,EAAa/Y,GAAKoW,EAAW,SAAUhC,GAEzC,OADWA,EAAK4C,OACA6B,IAGdG,IAAeD,GAAc3C,EAAUW,KAAK,SAAUjE,GACxD,OAAOA,EAASkE,OAAS8B,GAAiBhG,EAAS8D,SAAW9D,EAAS9D,MAAQ+J,EAAW/J,QAG5F,IAAKgK,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtCpC,QAAQC,KAAKuC,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,KAEnJ,OAAOD,EAoIT,IAAIG,IAAc,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,cAGhLC,GAAkBD,GAAWhN,MAAM,GAYvC,SAASkN,GAAUxF,GACjB,IAAIyF,EAAU5b,UAAUf,OAAS,QAAsBmT,IAAjBpS,UAAU,IAAmBA,UAAU,GAEzEiJ,EAAQyS,GAAgBpR,QAAQ6L,GAChCmC,EAAMoD,GAAgBjN,MAAMxF,EAAQ,GAAG4S,OAAOH,GAAgBjN,MAAM,EAAGxF,IAC3E,OAAO2S,EAAUtD,EAAIwD,UAAYxD,EAGnC,IAAIyD,IACFC,KAAM,OACNC,UAAW,YACXC,iBAAkB,oBA0LpB,SAASC,GAAYC,EAAQpE,EAAeF,EAAkBuE,GAC5D,IAAI1I,GAAW,EAAG,GAKd2I,GAA0D,KAA7C,QAAS,QAAQhS,QAAQ+R,GAItCE,EAAYH,EAAOlF,MAAM,WAAWV,IAAI,SAAUgG,GACpD,OAAOA,EAAKC,SAKVC,EAAUH,EAAUjS,QAAQhI,GAAKia,EAAW,SAAUC,GACxD,OAAgC,IAAzBA,EAAKG,OAAO,WAGjBJ,EAAUG,KAAiD,IAArCH,EAAUG,GAASpS,QAAQ,MACnD0O,QAAQC,KAAK,gFAKf,IAAI2D,EAAa,cACbC,GAAmB,IAAbH,GAAkBH,EAAU9N,MAAM,EAAGiO,GAASb,QAAQU,EAAUG,GAASxF,MAAM0F,GAAY,MAAOL,EAAUG,GAASxF,MAAM0F,GAAY,IAAIf,OAAOU,EAAU9N,MAAMiO,EAAU,MAAQH,GAqC9L,OAlCAM,EAAMA,EAAIrG,IAAI,SAAUsG,EAAI7T,GAE1B,IAAImP,GAAyB,IAAVnP,GAAeqT,EAAYA,GAAa,SAAW,QAClES,GAAoB,EACxB,OAAOD,EAGNE,OAAO,SAAUpG,EAAGC,GACnB,MAAwB,KAApBD,EAAEA,EAAE3X,OAAS,KAAwC,KAA1B,IAAK,KAAKqL,QAAQuM,IAC/CD,EAAEA,EAAE3X,OAAS,GAAK4X,EAClBkG,GAAoB,EACbnG,GACEmG,GACTnG,EAAEA,EAAE3X,OAAS,IAAM4X,EACnBkG,GAAoB,EACbnG,GAEAA,EAAEiF,OAAOhF,QAInBL,IAAI,SAAUyG,GACb,OAxGN,SAAiBA,EAAK7E,EAAaJ,EAAeF,GAEhD,IAAIZ,EAAQ+F,EAAI1Z,MAAM,6BAClBJ,GAAS+T,EAAM,GACf+D,EAAO/D,EAAM,GAGjB,IAAK/T,EACH,OAAO8Z,EAGT,GAA0B,IAAtBhC,EAAK3Q,QAAQ,KAAY,CAC3B,IAAIvI,OAAU,EACd,OAAQkZ,GACN,IAAK,KACHlZ,EAAUiW,EACV,MACF,IAAK,IACL,IAAK,KACL,QACEjW,EAAU+V,EAId,OADWpE,GAAc3R,GACbqW,GAAe,IAAMjV,EAC5B,GAAa,OAAT8X,GAA0B,OAATA,EAQ1B,OALa,OAATA,EACKtZ,KAAKuR,IAAIrR,SAASkI,gBAAgBuK,aAAczQ,OAAOiS,aAAe,GAEtEnU,KAAKuR,IAAIrR,SAASkI,gBAAgBsK,YAAaxQ,OAAOgS,YAAc,IAE/D,IAAM1S,EAIpB,OAAOA,EAmEE+Z,CAAQD,EAAK7E,EAAaJ,EAAeF,QAKhDiB,QAAQ,SAAU+D,EAAI7T,GACxB6T,EAAG/D,QAAQ,SAAUyD,EAAMW,GACrBvC,GAAU4B,KACZ7I,EAAQ1K,IAAUuT,GAA2B,MAAnBM,EAAGK,EAAS,IAAc,EAAI,QAIvDxJ,EAuNT,IAkVIyJ,IAKFjH,UAAW,SAMXoE,eAAe,EAOf8C,iBAAiB,EAQjBC,SAAU,aAUVC,SAAU,aAOV7E,WApXA8E,OAEElM,MAAO,IAEP4H,SAAS,EAETnV,GA9HJ,SAAe+B,GACb,IAAIqQ,EAAYrQ,EAAKqQ,UACjBkG,EAAgBlG,EAAUe,MAAM,KAAK,GACrCuG,EAAiBtH,EAAUe,MAAM,KAAK,GAG1C,GAAIuG,EAAgB,CAClB,IAAIC,EAAgB5X,EAAK6N,QACrB6B,EAAYkI,EAAclI,UAC1BD,EAASmI,EAAcnI,OAEvBoI,GAA2D,KAA7C,SAAU,OAAOrT,QAAQ+R,GACvCuB,EAAOD,EAAa,OAAS,MAC7BvF,EAAcuF,EAAa,QAAU,SAErCE,GACFnM,MAAOnS,MAAmBqe,EAAMpI,EAAUoI,IAC1Clb,IAAKnD,MAAmBqe,EAAMpI,EAAUoI,GAAQpI,EAAU4C,GAAe7C,EAAO6C,KAGlFtS,EAAK6N,QAAQ4B,OAASzV,MAAayV,EAAQsI,EAAaJ,IAG1D,OAAO3X,IAgJPsW,QAEE9K,MAAO,IAEP4H,SAAS,EAETnV,GAzQJ,SAAgB+B,EAAM4Q,GACpB,IAAI0F,EAAS1F,EAAK0F,OACdjG,EAAYrQ,EAAKqQ,UACjBuH,EAAgB5X,EAAK6N,QACrB4B,EAASmI,EAAcnI,OACvBC,EAAYkI,EAAclI,UAE1B6G,EAAgBlG,EAAUe,MAAM,KAAK,GAErCvD,OAAU,EAsBd,OApBEA,EADEiH,IAAWwB,KACDA,EAAQ,GAEVD,GAAYC,EAAQ7G,EAAQC,EAAW6G,GAG7B,SAAlBA,GACF9G,EAAOxB,KAAOJ,EAAQ,GACtB4B,EAAO1B,MAAQF,EAAQ,IACI,UAAlB0I,GACT9G,EAAOxB,KAAOJ,EAAQ,GACtB4B,EAAO1B,MAAQF,EAAQ,IACI,QAAlB0I,GACT9G,EAAO1B,MAAQF,EAAQ,GACvB4B,EAAOxB,KAAOJ,EAAQ,IACK,WAAlB0I,IACT9G,EAAO1B,MAAQF,EAAQ,GACvB4B,EAAOxB,KAAOJ,EAAQ,IAGxB7N,EAAKyP,OAASA,EACPzP,GA8OLsW,OAAQ,GAoBV0B,iBAEExM,MAAO,IAEP4H,SAAS,EAETnV,GA9PJ,SAAyB+B,EAAMgU,GAC7B,IAAIpE,EAAoBoE,EAAQpE,mBAAqB3E,EAAgBjL,EAAKyN,SAASgC,QAK/EzP,EAAKyN,SAASiC,YAAcE,IAC9BA,EAAoB3E,EAAgB2E,IAGtC,IAAIE,EAAaN,GAAcxP,EAAKyN,SAASgC,OAAQzP,EAAKyN,SAASiC,UAAWsE,EAAQrE,QAASC,GAC/FoE,EAAQlE,WAAaA,EAErB,IAAItE,EAAQwI,EAAQiE,SAChBxI,EAASzP,EAAK6N,QAAQ4B,OAEtBgD,GACFyF,QAAS,SAAiB7H,GACxB,IAAIhT,EAAQoS,EAAOY,GAInB,OAHIZ,EAAOY,GAAaP,EAAWO,KAAe2D,EAAQmE,sBACxD9a,EAAQxB,KAAKuR,IAAIqC,EAAOY,GAAYP,EAAWO,KAE1C5W,MAAmB4W,EAAWhT,IAEvC+a,UAAW,SAAmB/H,GAC5B,IAAI+B,EAAyB,UAAd/B,EAAwB,OAAS,MAC5ChT,EAAQoS,EAAO2C,GAInB,OAHI3C,EAAOY,GAAaP,EAAWO,KAAe2D,EAAQmE,sBACxD9a,EAAQxB,KAAKwc,IAAI5I,EAAO2C,GAAWtC,EAAWO,IAA4B,UAAdA,EAAwBZ,EAAOlC,MAAQkC,EAAOnC,UAErG7T,MAAmB2Y,EAAU/U,KAWxC,OAPAmO,EAAMyH,QAAQ,SAAU5C,GACtB,IAAIyH,GAA+C,KAAvC,OAAQ,OAAOtT,QAAQ6L,GAAoB,UAAY,YACnEZ,EAASzV,MAAayV,EAAQgD,EAAMqF,GAAMzH,MAG5CrQ,EAAK6N,QAAQ4B,OAASA,EAEfzP,GA2NLiY,UAAW,OAAQ,QAAS,MAAO,UAOnCtI,QAAS,EAMTC,kBAAmB,gBAYrB0I,cAEE9M,MAAO,IAEP4H,SAAS,EAETnV,GA9eJ,SAAsB+B,GACpB,IAAI4X,EAAgB5X,EAAK6N,QACrB4B,EAASmI,EAAcnI,OACvBC,EAAYkI,EAAclI,UAE1BW,EAAYrQ,EAAKqQ,UAAUe,MAAM,KAAK,GACtCmH,EAAQ1c,KAAK0c,MACbV,GAAuD,KAAzC,MAAO,UAAUrT,QAAQ6L,GACvCyH,EAAOD,EAAa,QAAU,SAC9BW,EAASX,EAAa,OAAS,MAC/BvF,EAAcuF,EAAa,QAAU,SASzC,OAPIpI,EAAOqI,GAAQS,EAAM7I,EAAU8I,MACjCxY,EAAK6N,QAAQ4B,OAAO+I,GAAUD,EAAM7I,EAAU8I,IAAW/I,EAAO6C,IAE9D7C,EAAO+I,GAAUD,EAAM7I,EAAUoI,MACnC9X,EAAK6N,QAAQ4B,OAAO+I,GAAUD,EAAM7I,EAAUoI,KAGzC9X,IAwePyY,OAEEjN,MAAO,IAEP4H,SAAS,EAETnV,GAzvBJ,SAAe+B,EAAMgU,GACnB,IAAI0E,EAGJ,IAAKtD,GAAmBpV,EAAKyN,SAASmF,UAAW,QAAS,gBACxD,OAAO5S,EAGT,IAAI2Y,EAAe3E,EAAQ/X,QAG3B,GAA4B,iBAAjB0c,GAIT,KAHAA,EAAe3Y,EAAKyN,SAASgC,OAAOmJ,cAAcD,IAIhD,OAAO3Y,OAKT,IAAKA,EAAKyN,SAASgC,OAAOzO,SAAS2X,GAEjC,OADAzF,QAAQC,KAAK,iEACNnT,EAIX,IAAIqQ,EAAYrQ,EAAKqQ,UAAUe,MAAM,KAAK,GACtCwG,EAAgB5X,EAAK6N,QACrB4B,EAASmI,EAAcnI,OACvBC,EAAYkI,EAAclI,UAE1BmI,GAAuD,KAAzC,OAAQ,SAASrT,QAAQ6L,GAEvCwI,EAAMhB,EAAa,SAAW,QAC9BiB,EAAkBjB,EAAa,MAAQ,OACvCC,EAAOgB,EAAgBpb,cACvBqb,EAAUlB,EAAa,OAAS,MAChCW,EAASX,EAAa,SAAW,QACjCmB,EAAmBzH,GAAcoH,GAAcE,GAQ/CnJ,EAAU8I,GAAUQ,EAAmBvJ,EAAOqI,KAChD9X,EAAK6N,QAAQ4B,OAAOqI,IAASrI,EAAOqI,IAASpI,EAAU8I,GAAUQ,IAG/DtJ,EAAUoI,GAAQkB,EAAmBvJ,EAAO+I,KAC9CxY,EAAK6N,QAAQ4B,OAAOqI,IAASpI,EAAUoI,GAAQkB,EAAmBvJ,EAAO+I,IAE3ExY,EAAK6N,QAAQ4B,OAAS7B,GAAc5N,EAAK6N,QAAQ4B,QAGjD,IAAIwJ,EAASvJ,EAAUoI,GAAQpI,EAAUmJ,GAAO,EAAIG,EAAmB,EAInEvW,EAAM2H,EAAyBpK,EAAKyN,SAASgC,QAC7CyJ,EAAmBpM,WAAWrK,EAAI,SAAWqW,GAAkB,IAC/DK,EAAmBrM,WAAWrK,EAAI,SAAWqW,EAAkB,SAAU,IACzEM,EAAYH,EAASjZ,EAAK6N,QAAQ4B,OAAOqI,GAAQoB,EAAmBC,EAQxE,OALAC,EAAYvd,KAAKuR,IAAIvR,KAAKwc,IAAI5I,EAAOoJ,GAAOG,EAAkBI,GAAY,GAE1EpZ,EAAK2Y,aAAeA,EACpB3Y,EAAK6N,QAAQ4K,OAAmChf,GAA1Bif,KAA8DZ,EAAMjc,KAAKwd,MAAMD,IAAa3f,GAAeif,EAAqBK,EAAS,IAAKL,GAE7J1Y,GAmrBL/D,QAAS,aAcXqd,MAEE9N,MAAO,IAEP4H,SAAS,EAETnV,GAjnBJ,SAAc+B,EAAMgU,GAElB,GAAIX,GAAkBrT,EAAKyN,SAASmF,UAAW,SAC7C,OAAO5S,EAGT,GAAIA,EAAKuZ,SAAWvZ,EAAKqQ,YAAcrQ,EAAKwZ,kBAE1C,OAAOxZ,EAGT,IAAI8P,EAAaN,GAAcxP,EAAKyN,SAASgC,OAAQzP,EAAKyN,SAASiC,UAAWsE,EAAQrE,QAASqE,EAAQpE,mBAEnGS,EAAYrQ,EAAKqQ,UAAUe,MAAM,KAAK,GACtCqI,EAAoB7H,GAAqBvB,GACzCc,EAAYnR,EAAKqQ,UAAUe,MAAM,KAAK,IAAM,GAE5CsI,KAEJ,OAAQ1F,EAAQ2F,UACd,KAAK1D,GAAUC,KACbwD,GAAarJ,EAAWoJ,GACxB,MACF,KAAKxD,GAAUE,UACbuD,EAAY7D,GAAUxF,GACtB,MACF,KAAK4F,GAAUG,iBACbsD,EAAY7D,GAAUxF,GAAW,GACjC,MACF,QACEqJ,EAAY1F,EAAQ2F,SAkDxB,OA/CAD,EAAUzG,QAAQ,SAAU2G,EAAMzW,GAChC,GAAIkN,IAAcuJ,GAAQF,EAAUvgB,SAAWgK,EAAQ,EACrD,OAAOnD,EAGTqQ,EAAYrQ,EAAKqQ,UAAUe,MAAM,KAAK,GACtCqI,EAAoB7H,GAAqBvB,GAEzC,IArH0Bc,EAqHtBe,EAAgBlS,EAAK6N,QAAQ4B,OAC7BoK,EAAa7Z,EAAK6N,QAAQ6B,UAG1B6I,EAAQ1c,KAAK0c,MACbuB,EAA4B,SAAdzJ,GAAwBkI,EAAMrG,EAAcpE,OAASyK,EAAMsB,EAAW9L,OAAuB,UAAdsC,GAAyBkI,EAAMrG,EAAcnE,MAAQwK,EAAMsB,EAAW/L,QAAwB,QAAduC,GAAuBkI,EAAMrG,EAAclE,QAAUuK,EAAMsB,EAAW5L,MAAsB,WAAdoC,GAA0BkI,EAAMrG,EAAcjE,KAAOsK,EAAMsB,EAAW7L,QAEjU+L,EAAgBxB,EAAMrG,EAAcnE,MAAQwK,EAAMzI,EAAW/B,MAC7DiM,EAAiBzB,EAAMrG,EAAcpE,OAASyK,EAAMzI,EAAWhC,OAC/DmM,EAAe1B,EAAMrG,EAAcjE,KAAOsK,EAAMzI,EAAW7B,KAC3DiM,EAAkB3B,EAAMrG,EAAclE,QAAUuK,EAAMzI,EAAW9B,QAEjEmM,EAAoC,SAAd9J,GAAwB0J,GAA+B,UAAd1J,GAAyB2J,GAAgC,QAAd3J,GAAuB4J,GAA8B,WAAd5J,GAA0B6J,EAG3KrC,GAAuD,KAAzC,MAAO,UAAUrT,QAAQ6L,GACvC+J,IAAqBpG,EAAQqG,iBAAmBxC,GAA4B,UAAd1G,GAAyB4I,GAAiBlC,GAA4B,QAAd1G,GAAuB6I,IAAmBnC,GAA4B,UAAd1G,GAAyB8I,IAAiBpC,GAA4B,QAAd1G,GAAuB+I,IAE7PJ,GAAeK,GAAuBC,KAExCpa,EAAKuZ,SAAU,GAEXO,GAAeK,KACjB9J,EAAYqJ,EAAUvW,EAAQ,IAG5BiX,IACFjJ,EA/IY,SADUA,EAgJWA,GA9I9B,QACgB,UAAdA,EACF,MAEFA,GA6IHnR,EAAKqQ,UAAYA,GAAac,EAAY,IAAMA,EAAY,IAI5DnR,EAAK6N,QAAQ4B,OAASzV,MAAagG,EAAK6N,QAAQ4B,OAAQsC,GAAiB/R,EAAKyN,SAASgC,OAAQzP,EAAK6N,QAAQ6B,UAAW1P,EAAKqQ,YAE5HrQ,EAAO2S,GAAa3S,EAAKyN,SAASmF,UAAW5S,EAAM,WAGhDA,GAwiBL2Z,SAAU,OAKVhK,QAAS,EAOTC,kBAAmB,YAUrB0K,OAEE9O,MAAO,IAEP4H,SAAS,EAETnV,GArPJ,SAAe+B,GACb,IAAIqQ,EAAYrQ,EAAKqQ,UACjBkG,EAAgBlG,EAAUe,MAAM,KAAK,GACrCwG,EAAgB5X,EAAK6N,QACrB4B,EAASmI,EAAcnI,OACvBC,EAAYkI,EAAclI,UAE1ByC,GAAwD,KAA7C,OAAQ,SAAS3N,QAAQ+R,GAEpCgE,GAA6D,KAA3C,MAAO,QAAQ/V,QAAQ+R,GAO7C,OALA9G,EAAO0C,EAAU,OAAS,OAASzC,EAAU6G,IAAkBgE,EAAiB9K,EAAO0C,EAAU,QAAU,UAAY,GAEvHnS,EAAKqQ,UAAYuB,GAAqBvB,GACtCrQ,EAAK6N,QAAQ4B,OAAS7B,GAAc6B,GAE7BzP,IAkPP2H,MAEE6D,MAAO,IAEP4H,SAAS,EAETnV,GA9SJ,SAAc+B,GACZ,IAAKoV,GAAmBpV,EAAKyN,SAASmF,UAAW,OAAQ,mBACvD,OAAO5S,EAGT,IAAIsQ,EAAUtQ,EAAK6N,QAAQ6B,UACvB8K,EAAQhe,GAAKwD,EAAKyN,SAASmF,UAAW,SAAUtD,GAClD,MAAyB,oBAAlBA,EAASkE,OACf1D,WAEH,GAAIQ,EAAQtC,OAASwM,EAAMvM,KAAOqC,EAAQvC,KAAOyM,EAAM1M,OAASwC,EAAQrC,IAAMuM,EAAMxM,QAAUsC,EAAQxC,MAAQ0M,EAAMzM,KAAM,CAExH,IAAkB,IAAd/N,EAAK2H,KACP,OAAO3H,EAGTA,EAAK2H,MAAO,EACZ3H,EAAKya,WAAW,uBAAyB,OACpC,CAEL,IAAkB,IAAdza,EAAK2H,KACP,OAAO3H,EAGTA,EAAK2H,MAAO,EACZ3H,EAAKya,WAAW,wBAAyB,EAG3C,OAAOza,IAoSP0a,cAEElP,MAAO,IAEP4H,SAAS,EAETnV,GAv9BJ,SAAsB+B,EAAMgU,GAC1B,IAAIxC,EAAIwC,EAAQxC,EACZE,EAAIsC,EAAQtC,EACZjC,EAASzP,EAAK6N,QAAQ4B,OAItBkL,EAA8Bne,GAAKwD,EAAKyN,SAASmF,UAAW,SAAUtD,GACxE,MAAyB,eAAlBA,EAASkE,OACfoH,qBACiCtO,IAAhCqO,GACFzH,QAAQC,KAAK,iIAEf,IAAIyH,OAAkDtO,IAAhCqO,EAA4CA,EAA8B3G,EAAQ4G,gBAGpGC,EAAmBhS,GADJoC,EAAgBjL,EAAKyN,SAASgC,SAI7C/C,GACFoO,SAAUrL,EAAOqL,UAIfjN,GACFE,KAAMlS,KAAK0c,MAAM9I,EAAO1B,MACxBE,IAAKpS,KAAK0c,MAAM9I,EAAOxB,KACvBD,OAAQnS,KAAK0c,MAAM9I,EAAOzB,QAC1BF,MAAOjS,KAAK0c,MAAM9I,EAAO3B,QAGvBlB,EAAc,WAAN4E,EAAiB,MAAQ,SACjC3E,EAAc,UAAN6E,EAAgB,OAAS,QAKjCqJ,EAAmBtH,GAAyB,aAW5C1F,OAAO,EACPE,OAAM,EAWV,GATEA,EADY,WAAVrB,GACKiO,EAAiBvN,OAASO,EAAQG,OAEnCH,EAAQI,IAGdF,EADY,UAAVlB,GACMgO,EAAiBtN,MAAQM,EAAQC,MAElCD,EAAQE,KAEb6M,GAAmBG,EACrBrO,EAAOqO,GAAoB,eAAiBhN,EAAO,OAASE,EAAM,SAClEvB,EAAOE,GAAS,EAChBF,EAAOG,GAAS,EAChBH,EAAOsO,WAAa,gBACf,CAEL,IAAIC,EAAsB,WAAVrO,GAAsB,EAAI,EACtCsO,EAAuB,UAAVrO,GAAqB,EAAI,EAC1CH,EAAOE,GAASqB,EAAMgN,EACtBvO,EAAOG,GAASkB,EAAOmN,EACvBxO,EAAOsO,WAAapO,EAAQ,KAAOC,EAIrC,IAAI4N,GACFU,cAAenb,EAAKqQ,WAQtB,OAJArQ,EAAKya,WAAazgB,MAAaygB,EAAYza,EAAKya,YAChDza,EAAK0M,OAAS1S,MAAa0S,EAAQ1M,EAAK0M,QACxC1M,EAAKob,YAAcphB,MAAagG,EAAK6N,QAAQ4K,MAAOzY,EAAKob,aAElDpb,GAy4BL4a,iBAAiB,EAMjBpJ,EAAG,SAMHE,EAAG,SAkBL2J,YAEE7P,MAAO,IAEP4H,SAAS,EAETnV,GAvjCJ,SAAoB+B,GApBpB,IAAuB/D,EAASwe,EAoC9B,OAXAvF,GAAUlV,EAAKyN,SAASgC,OAAQzP,EAAK0M,QAzBhBzQ,EA6BP+D,EAAKyN,SAASgC,OA7BEgL,EA6BMza,EAAKya,WA5BzCjhB,OAAOiX,KAAKgK,GAAYxH,QAAQ,SAAUH,IAE1B,IADF2H,EAAW3H,GAErB7W,EAAQiF,aAAa4R,EAAM2H,EAAW3H,IAEtC7W,EAAQqf,gBAAgBxI,KA0BxB9S,EAAK2Y,cAAgBnf,OAAOiX,KAAKzQ,EAAKob,aAAajiB,QACrD+b,GAAUlV,EAAK2Y,aAAc3Y,EAAKob,aAG7Bpb,GAyiCLub,OA5hCJ,SAA0B7L,EAAWD,EAAQuE,EAASwH,EAAiBlK,GAErE,IAAIU,EAAmBX,GAAoBC,EAAO7B,EAAQC,GAKtDW,EAAYD,GAAqB4D,EAAQ3D,UAAW2B,EAAkBvC,EAAQC,EAAWsE,EAAQpB,UAAU0G,KAAK1J,kBAAmBoE,EAAQpB,UAAU0G,KAAK3J,SAQ9J,OANAF,EAAOvO,aAAa,cAAemP,GAInC6E,GAAUzF,GAAUqL,SAAU,aAEvB9G,GAohCL4G,qBAAiBtO,KAiGjBmP,GAAS,WASX,SAASA,EAAO/L,EAAWD,GACzB,IAAI9T,EAAQpB,KAERyZ,EAAU9Z,UAAUf,OAAS,QAAsBmT,IAAjBpS,UAAU,GAAmBA,UAAU,MAC7EsT,GAAejT,KAAMkhB,GAErBlhB,KAAKqa,eAAiB,WACpB,OAAO8G,sBAAsB/f,EAAMggB,SAIrCphB,KAAKohB,OAAS9R,EAAStP,KAAKohB,OAAO1Y,KAAK1I,OAGxCA,KAAKyZ,QAAUha,MAAayhB,EAAOnE,SAAUtD,GAG7CzZ,KAAK+W,OACHsK,aAAa,EACbC,WAAW,EACXtH,kBAIFha,KAAKmV,UAAYA,GAAaA,EAAU1G,OAAS0G,EAAU,GAAKA,EAChEnV,KAAKkV,OAASA,GAAUA,EAAOzG,OAASyG,EAAO,GAAKA,EAGpDlV,KAAKyZ,QAAQpB,aACbpZ,OAAOiX,KAAKzW,MAAayhB,EAAOnE,SAAS1E,UAAWoB,EAAQpB,YAAYK,QAAQ,SAAUO,GACxF7X,EAAMqY,QAAQpB,UAAUY,GAAQxZ,MAAayhB,EAAOnE,SAAS1E,UAAUY,OAAaQ,EAAQpB,UAAYoB,EAAQpB,UAAUY,SAI5HjZ,KAAKqY,UAAYpZ,OAAOiX,KAAKlW,KAAKyZ,QAAQpB,WAAWlC,IAAI,SAAU8C,GACjE,OAAOxZ,IACLwZ,KAAMA,GACL7X,EAAMqY,QAAQpB,UAAUY,MAG5B3C,KAAK,SAAUC,EAAGC,GACjB,OAAOD,EAAEtF,MAAQuF,EAAEvF,QAOrBjR,KAAKqY,UAAUK,QAAQ,SAAUuI,GAC3BA,EAAgBpI,SAAWlJ,EAAWsR,EAAgBD,SACxDC,EAAgBD,OAAO5f,EAAM+T,UAAW/T,EAAM8T,OAAQ9T,EAAMqY,QAASwH,EAAiB7f,EAAM2V,SAKhG/W,KAAKohB,SAEL,IAAIlH,EAAgBla,KAAKyZ,QAAQS,cAC7BA,GAEFla,KAAKuhB,uBAGPvhB,KAAK+W,MAAMmD,cAAgBA,EAqD7B,OA9CA/G,GAAY+N,IACV/hB,IAAK,SACL2D,MAAO,WACL,OAh/CN,WAEE,IAAI9C,KAAK+W,MAAMsK,YAAf,CAIA,IAAI5b,GACFyN,SAAUlT,KACVmS,UACA0O,eACAX,cACAlB,SAAS,EACT1L,YAIF7N,EAAK6N,QAAQ6B,UAAY2B,GAAoB9W,KAAK+W,MAAO/W,KAAKkV,OAAQlV,KAAKmV,WAK3E1P,EAAKqQ,UAAYD,GAAqB7V,KAAKyZ,QAAQ3D,UAAWrQ,EAAK6N,QAAQ6B,UAAWnV,KAAKkV,OAAQlV,KAAKmV,UAAWnV,KAAKyZ,QAAQpB,UAAU0G,KAAK1J,kBAAmBrV,KAAKyZ,QAAQpB,UAAU0G,KAAK3J,SAG9L3P,EAAKwZ,kBAAoBxZ,EAAKqQ,UAG9BrQ,EAAK6N,QAAQ4B,OAASsC,GAAiBxX,KAAKkV,OAAQzP,EAAK6N,QAAQ6B,UAAW1P,EAAKqQ,WACjFrQ,EAAK6N,QAAQ4B,OAAOqL,SAAW,WAG/B9a,EAAO2S,GAAapY,KAAKqY,UAAW5S,GAI/BzF,KAAK+W,MAAMuK,UAIdthB,KAAKyZ,QAAQyD,SAASzX,IAHtBzF,KAAK+W,MAAMuK,WAAY,EACvBthB,KAAKyZ,QAAQwD,SAASxX,MA28CN3F,KAAKE,SAGrBb,IAAK,UACL2D,MAAO,WACL,OAl6CN,WAmBE,OAlBA9C,KAAK+W,MAAMsK,aAAc,EAGrBvI,GAAkB9Y,KAAKqY,UAAW,gBACpCrY,KAAKkV,OAAO6L,gBAAgB,eAC5B/gB,KAAKkV,OAAOpH,MAAM0F,KAAO,GACzBxT,KAAKkV,OAAOpH,MAAMyS,SAAW,GAC7BvgB,KAAKkV,OAAOpH,MAAM4F,IAAM,GACxB1T,KAAKkV,OAAOpH,MAAMoL,GAAyB,cAAgB,IAG7DlZ,KAAKma,wBAIDna,KAAKyZ,QAAQuD,iBACfhd,KAAKkV,OAAOjF,WAAWuR,YAAYxhB,KAAKkV,QAEnClV,MA+4CYF,KAAKE,SAGtBb,IAAK,uBACL2D,MAAO,WACL,OAl2CN,WACO9C,KAAK+W,MAAMmD,gBACdla,KAAK+W,MAAQyC,GAAoBxZ,KAAKmV,UAAWnV,KAAKyZ,QAASzZ,KAAK+W,MAAO/W,KAAKqa,kBAg2ClDva,KAAKE,SAGnCb,IAAK,wBACL2D,MAAO,WACL,OAAOqX,GAAsBra,KAAKE,UA4B/BkhB,EA7HI,GAqJbA,GAAOO,OAA2B,oBAAXje,OAAyBA,OAASke,QAAQC,YACjET,GAAO9F,WAAaA,GACpB8F,GAAOnE,SAAWA,GMt3ElB,IAAM6E,GAAY,SAAC3hB,OAOXC,EAA2B,WAE3BC,EAA2B,cAC3BC,EAAAA,IAA+BD,EAE/BE,EAA2BJ,EAAEyD,GAAGxD,GAOhC2hB,EAA2B,IAAIze,OAAU0e,YAEzCxhB,eACsBF,kBACEA,cACFA,gBACCA,gBACAA,yBACAA,EAhBI,uCAiBFA,EAjBE,mCAkBJA,EAlBI,aAqB3BG,EACQ,WADRA,EAEQ,OAFRA,EAGQ,SAHRA,EAIQ,YAJRA,EAKQ,WALRA,EAMQ,sBANRA,EAOQ,qBAPRA,EAQc,kBAGdG,EACY,2BADZA,EAEY,iBAFZA,EAGY,iBAHZA,EAIY,cAJZA,EAKY,+CAGZqhB,EACQ,YADRA,EAEQ,UAFRA,EAGQ,eAHRA,EAIQ,aAJRA,EAKQ,cALRA,EAOQ,aAIR/a,UACU,QACA,WACA,gBAGVC,UACU,gCACA,mBACA,oBASV2a,wBACQlgB,EAASgB,QACdwB,SAAYxC,OACZsgB,QAAY,UACZva,QAAYzH,KAAK0H,WAAWhF,QAC5Buf,MAAYjiB,KAAKkiB,uBACjBC,UAAYniB,KAAKoiB,qBAEjBva,gDAmBP7B,OA3GqB,eA4GfhG,KAAKkE,SAASme,WAAYpiB,EAAED,KAAKkE,UAAUe,SAAS1E,QAIlDqE,EAAWgd,EAASU,sBAAsBtiB,KAAKkE,UAC/Cqe,EAAWtiB,EAAED,KAAKiiB,OAAOhd,SAAS1E,QAE/BiiB,eAELD,OAIE9X,iBACWzK,KAAKkE,UAEhBue,EAAYxiB,EAAEK,MAAMA,EAAMqN,KAAMlD,QAEpC7F,GAAQxC,QAAQqgB,IAEdA,EAAUle,0BAKTvE,KAAKmiB,UAAW,IAKG,oBAAXjB,SACH,IAAInV,UAAU,oEAElBrK,EAAU1B,KAAKkE,SAEfjE,EAAE2E,GAAQK,SAAS1E,KACjBN,EAAED,KAAKiiB,OAAOhd,SAAS1E,IAAuBN,EAAED,KAAKiiB,OAAOhd,SAAS1E,QAC7DqE,GAMgB,iBAA1B5E,KAAKyH,QAAQib,YACb9d,GAAQuG,SAAS5K,QAEhByhB,QAAU,IAAId,GAAOxf,EAAS1B,KAAKiiB,MAAOjiB,KAAK2iB,oBAOlD,iBAAkBnhB,SAASkI,iBACsB,IAAlDzJ,EAAE2E,GAAQC,QAAQnE,GAAqB9B,UACtC,QAAQsM,WAAWrF,GAAG,YAAa,KAAM5F,EAAE2iB,WAG1C1e,SAASwC,aACTxC,SAASyC,aAAa,iBAAiB,KAE1C3G,KAAKiiB,OAAOrb,YAAYrG,KACxBqE,GACCgC,YAAYrG,GACZ6B,QAAQnC,EAAEK,MAAMA,EAAM4N,MAAOzD,UAGlChG,QA/KqB,aAgLjBC,WAAW1E,KAAKkE,SAAU/D,KAC1BH,KAAKkE,UAAUgF,IAAI9I,QAChB8D,SAAW,UACX+d,MAAQ,KACQ,OAAjBjiB,KAAKgiB,eACFA,QAAQa,eACRb,QAAU,SAInBZ,OA1LqB,gBA2Lde,UAAYniB,KAAKoiB,gBACD,OAAjBpiB,KAAKgiB,cACFA,QAAQ3H,oBAMjBxS,mBAnMqB,wBAoMjB7H,KAAKkE,UAAU2B,GAAGvF,EAAMwiB,MAAO,SAACjf,KAC1B+B,mBACAmd,oBACD/c,cAIT0B,WA3MqB,SA2MVhF,iBAEJ1C,KAAKgjB,YAAYhc,QACjB/G,EAAED,KAAKkE,UAAUuB,OACjB/C,KAGAyG,gBACHjJ,EACAwC,EACA1C,KAAKgjB,YAAY/b,aAGZvE,KAGTwf,gBA3NqB,eA4NdliB,KAAKiiB,MAAO,KACTrd,EAASgd,EAASU,sBAAsBtiB,KAAKkE,eAC9C+d,MAAQhiB,EAAE2E,GAAQ3C,KAAKvB,GAAe,UAEtCV,KAAKiiB,SAGdgB,cAnOqB,eAoObC,EAAkBjjB,EAAED,KAAKkE,UAAUU,SACrCkR,EAAYiM,SAGZmB,EAAgBje,SAAS1E,MACfwhB,EACR9hB,EAAED,KAAKiiB,OAAOhd,SAAS1E,OACbwhB,IAELmB,EAAgBje,SAAS1E,KACtBwhB,EACHmB,EAAgBje,SAAS1E,KACtBwhB,EACH9hB,EAAED,KAAKiiB,OAAOhd,SAAS1E,OACpBwhB,GAEPjM,KAGTsM,cAvPqB,kBAwPZniB,EAAED,KAAKkE,UAAUW,QAAQ,WAAWjG,OAAS,KAGtD+jB,iBA3PqB,sBA4PbQ,WAC6B,mBAAxBnjB,KAAKyH,QAAQsU,SACXrY,GAAK,SAAC+B,YACV6N,QAAL7T,KACKgG,EAAK6N,QACLhK,EAAK7B,QAAQsU,OAAOtW,EAAK6N,cAEvB7N,KAGEsW,OAAS/b,KAAKyH,QAAQsU,kBAGtB/b,KAAKijB,kCAENE,gBAEGnjB,KAAKyH,QAAQsX,yCAGH/e,KAAKyH,QAAQib,eAUjCpd,iBA1Rc,SA0RG5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,MAGnBsF,MACI,IAAImc,EAAS5hB,KAHY,iBAAX0C,EAAsBA,EAAS,QAIlD1C,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,EAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,WAKJ8f,YA7Sc,SA6SF3e,OACbA,GA5RyB,IA4RfA,EAAMiG,QACH,UAAfjG,EAAMuC,MAhSqB,IAgSDvC,EAAMiG,eAI5BsZ,EAAUnjB,EAAE8J,UAAU9J,EAAES,IACrB/B,EAAI,EAAGA,EAAIykB,EAAQxkB,OAAQD,IAAK,KACjCiG,EAASgd,EAASU,sBAAsBc,EAAQzkB,IAChD0kB,EAAUpjB,EAAEmjB,EAAQzkB,IAAI8G,KAAKtF,GAC7BsK,iBACW2Y,EAAQzkB,OAGpB0kB,OAICC,EAAeD,EAAQpB,SACxBhiB,EAAE2E,GAAQK,SAAS1E,MAIpBsD,IAAyB,UAAfA,EAAMuC,MAChB,kBAAkB/C,KAAKQ,EAAMpF,OAAOoL,UAA2B,UAAfhG,EAAMuC,MAtT/B,IAsTmDvC,EAAMiG,QAChF7J,EAAEwG,SAAS7B,EAAQf,EAAMpF,cAIvB8kB,EAAYtjB,EAAEK,MAAMA,EAAM+N,KAAM5D,KACpC7F,GAAQxC,QAAQmhB,GACdA,EAAUhf,uBAMV,iBAAkB/C,SAASkI,mBAC3B,QAAQwB,WAAWhC,IAAI,YAAa,KAAMjJ,EAAE2iB,QAGxCjkB,GAAGgI,aAAa,gBAAiB,WAEvC2c,GAActe,YAAYzE,KAC1BqE,GACCI,YAAYzE,GACZ6B,QAAQnC,EAAEK,MAAMA,EAAMiO,OAAQ9D,WAI9B6X,sBA/Vc,SA+VQ5gB,OACvBkD,EACEjD,EAAWf,EAAK+D,uBAAuBjD,UAEzCC,MACO1B,EAAE0B,GAAU,IAGhBiD,GAAUlD,EAAQuO,cAIpBuT,uBA3Wc,SA2WS3f,OAQxB,kBAAkBR,KAAKQ,EAAMpF,OAAOoL,WArWX,KAsWzBhG,EAAMiG,OAvWmB,KAuWQjG,EAAMiG,QAnWd,KAoW1BjG,EAAMiG,OArWoB,KAqWYjG,EAAMiG,OAC3C7J,EAAE4D,EAAMpF,QAAQoG,QAAQnE,GAAe9B,SAAWijB,EAAexe,KAAKQ,EAAMiG,YAI1ElE,mBACAmd,mBAEF/iB,KAAKqiB,WAAYpiB,EAAED,MAAMiF,SAAS1E,SAIhCqE,EAAWgd,EAASU,sBAAsBtiB,MAC1CuiB,EAAWtiB,EAAE2E,GAAQK,SAAS1E,OAE/BgiB,GAvXwB,KAuXX1e,EAAMiG,OAtXK,KAsXuBjG,EAAMiG,UACrDyY,GAxXwB,KAwXX1e,EAAMiG,OAvXK,KAuXuBjG,EAAMiG,YAUpD2Z,EAAQxjB,EAAE2E,GAAQ3C,KAAKvB,GAAwBgjB,SAEhC,IAAjBD,EAAM7kB,YAINgK,EAAQ6a,EAAMxZ,QAAQpG,EAAMpF,QArYH,KAuYzBoF,EAAMiG,OAA8BlB,EAAQ,OAtYnB,KA0YzB/E,EAAMiG,OAAgClB,EAAQ6a,EAAM7kB,OAAS,OAI7DgK,EAAQ,MACF,KAGJA,GAAOlC,iBAtZgB,KAyXvB7C,EAAMiG,MAA0B,KAC5B9D,EAAS/F,EAAE2E,GAAQ3C,KAAKvB,GAAsB,KAClDsF,GAAQ5D,QAAQ,WAGlBpC,MAAMoC,QAAQ,0DAnYW,+CA0FtB4E,6CAIAC,oBAuUTzF,UACCqE,GAAGvF,EAAMqjB,iBAAkBjjB,EAAsBkhB,EAAS4B,wBAC1D3d,GAAGvF,EAAMqjB,iBAAkBjjB,EAAekhB,EAAS4B,wBACnD3d,GAAMvF,EAAMwF,eAHf,IAGiCxF,EAAMsjB,eAAkBhC,EAASY,aAC/D3c,GAAGvF,EAAMwF,eAAgBpF,EAAsB,SAAUmD,KAClD+B,mBACAmd,oBACGzd,iBAAiBxF,KAAKG,EAAED,MAAO,YAEzC6F,GAAGvF,EAAMwF,eAAgBpF,EAAqB,SAACmjB,KAC5Cd,sBASJrf,GAAGxD,GAAQ0hB,EAAStc,mBACpB5B,GAAGxD,GAAMb,YAAcuiB,IACvBle,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACNuhB,EAAStc,kBAGXsc,EAvcS,CAwcf3hB,GCzcG6jB,GAAS,SAAC7jB,OASRE,EAA+B,WAC/BC,EAAAA,IAAmCD,EAEnCE,EAA+BJ,EAAEyD,GAAF,MAK/BsD,aACO,YACA,SACA,QACA,GAGPC,YACO,4BACA,gBACA,eACA,WAGP3G,eACuBF,kBACEA,cACFA,gBACCA,oBACEA,kBACDA,gCACOA,oCACEA,oCACAA,wCACEA,4CAIpCG,EACiB,0BADjBA,EAEiB,iBAFjBA,EAGiB,aAHjBA,EAIiB,OAJjBA,EAKiB,OAGjBG,UACiB,4BACA,qCACA,uCACA,mEACA,6BACA,mBASjBojB,wBACQpiB,EAASgB,QACd+E,QAAuBzH,KAAK0H,WAAWhF,QACvCwB,SAAuBxC,OACvBqiB,QAAuB9jB,EAAEyB,GAASO,KAAKvB,EAASsjB,QAAQ,QACxDC,UAAuB,UACvBC,UAAuB,OACvBC,oBAAuB,OACvBC,sBAAuB,OACvBC,qBAAuB,OACvBC,gBAAuB,6BAe9Bte,OA7FkB,SA6FXyE,UACEzK,KAAKkkB,SAAWlkB,KAAKoN,OAASpN,KAAKqN,KAAK5C,MAGjD4C,KAjGkB,SAiGb5C,kBACCzK,KAAKwM,mBAAoBxM,KAAKkkB,UAI9BtjB,EAAKgD,yBAA2B3D,EAAED,KAAKkE,UAAUe,SAAS1E,UACvDiM,kBAAmB,OAGpBiW,EAAYxiB,EAAEK,MAAMA,EAAMqN,0BAI9B3N,KAAKkE,UAAU9B,QAAQqgB,GAErBziB,KAAKkkB,UAAYzB,EAAUle,4BAI1B2f,UAAW,OAEXK,uBACAC,qBAEAC,kBAEHjjB,SAAS4O,MAAMjF,SAAS5K,QAErBmkB,uBACAC,oBAEH3kB,KAAKkE,UAAU2B,GACfvF,EAAMskB,cACNlkB,EAASmkB,aACT,SAAChhB,UAAUzC,EAAKgM,KAAKvJ,OAGrB7D,KAAK+jB,SAASle,GAAGvF,EAAMwkB,kBAAmB,aACxC1jB,EAAK8C,UAAUjD,IAAIX,EAAMykB,gBAAiB,SAAClhB,GACvC5D,EAAE4D,EAAMpF,QAAQsF,GAAG3C,EAAK8C,cACrBkgB,sBAAuB,YAK7BY,cAAc,kBAAM5jB,EAAK6jB,aAAaxa,UAG7C2C,KAjJkB,SAiJbvJ,iBACCA,KACI+B,kBAGJ5F,KAAKwM,kBAAqBxM,KAAKkkB,cAI7BX,EAAYtjB,EAAEK,MAAMA,EAAM+N,WAE9BrO,KAAKkE,UAAU9B,QAAQmhB,GAEpBvjB,KAAKkkB,WAAYX,EAAUhf,2BAI3B2f,UAAW,MAEVrjB,EAAaD,EAAKgD,yBAA2B3D,EAAED,KAAKkE,UAAUe,SAAS1E,GAEzEM,SACG2L,kBAAmB,QAGrBkY,uBACAC,oBAEHnjB,UAAU0H,IAAI5I,EAAM4kB,WAEpBllB,KAAKkE,UAAUc,YAAYzE,KAE3BP,KAAKkE,UAAUgF,IAAI5I,EAAMskB,iBACzB5kB,KAAK+jB,SAAS7a,IAAI5I,EAAMwkB,mBAEtBjkB,IACAb,KAAKkE,UACJjD,IAAIL,EAAKM,eAAgB,SAAC2C,UAAUyF,EAAK6b,WAAWthB,KACpDF,qBA1K4B,UA4K1BwhB,kBAIT1gB,QA7LkB,aA8LdC,WAAW1E,KAAKkE,SAAU/D,KAE1BqD,OAAQhC,SAAUxB,KAAKkE,SAAUlE,KAAKikB,WAAW/a,IAAI9I,QAElDqH,QAAuB,UACvBvD,SAAuB,UACvB6f,QAAuB,UACvBE,UAAuB,UACvBC,SAAuB,UACvBC,mBAAuB,UACvBC,qBAAuB,UACvBE,gBAAuB,QAG9Bc,aA5MkB,gBA6MXX,mBAKP/c,WAlNkB,SAkNPhF,iBAEJsE,EACAtE,KAEAyG,gBAhN4B,QAgNNzG,EAAQuE,GAC5BvE,KAGTuiB,aA3NkB,SA2NLxa,cACL5J,EAAaD,EAAKgD,yBACtB3D,EAAED,KAAKkE,UAAUe,SAAS1E,GAEvBP,KAAKkE,SAAS+L,YAChBjQ,KAAKkE,SAAS+L,WAAWzN,WAAa2O,KAAKkU,uBAEnCjV,KAAKkV,YAAYtlB,KAAKkE,eAG5BA,SAAS4J,MAAMyX,QAAU,aACzBrhB,SAAS6c,gBAAgB,oBACzB7c,SAAS0P,UAAY,EAEtB/S,KACG8K,OAAO3L,KAAKkE,YAGjBlE,KAAKkE,UAAUiH,SAAS5K,GAEtBP,KAAKyH,QAAQf,YACV8e,oBAGDC,EAAaxlB,EAAEK,MAAMA,EAAM4N,yBAI3BwX,EAAqB,WACrB9Z,EAAKnE,QAAQf,SACVxC,SAASwC,UAEX8F,kBAAmB,IACtBZ,EAAK1H,UAAU9B,QAAQqjB,IAGvB5kB,IACAb,KAAK+jB,SACJ9iB,IAAIL,EAAKM,eAAgBwkB,GACzB/hB,qBArP4B,YA2PnC6hB,cAxQkB,wBAyQdhkB,UACC0H,IAAI5I,EAAM4kB,SACVrf,GAAGvF,EAAM4kB,QAAS,SAACrhB,GACdrC,WAAaqC,EAAMpF,QACnBknB,EAAKzhB,WAAaL,EAAMpF,QACsB,IAA9CwB,EAAE0lB,EAAKzhB,UAAU0hB,IAAI/hB,EAAMpF,QAAQG,UAChCsF,SAASwC,aAKtBge,gBApRkB,sBAqRZ1kB,KAAKkkB,UAAYlkB,KAAKyH,QAAQ2B,WAC9BpJ,KAAKkE,UAAU2B,GAAGvF,EAAMulB,gBAAiB,SAAChiB,GAvQb,KAwQzBA,EAAMiG,UACFlE,mBACDwH,UAGCpN,KAAKkkB,YACblkB,KAAKkE,UAAUgF,IAAI5I,EAAMulB,oBAI/BlB,gBAjSkB,sBAkSZ3kB,KAAKkkB,WACL1gB,QAAQqC,GAAGvF,EAAMwlB,OAAQ,SAACjiB,UAAUkiB,EAAKX,aAAavhB,OAEtDL,QAAQ0F,IAAI5I,EAAMwlB,WAIxBX,WAzSkB,2BA0SXjhB,SAAS4J,MAAMyX,QAAU,YACzBrhB,SAASyC,aAAa,eAAe,QACrC6F,kBAAmB,OACnBwY,cAAc,aACfxjB,SAAS4O,MAAMpL,YAAYzE,KACxBylB,sBACAC,oBACHC,EAAKhiB,UAAU9B,QAAQ9B,EAAMiO,aAInC4X,gBArTkB,WAsTZnmB,KAAKikB,cACLjkB,KAAKikB,WAAW5e,cACb4e,UAAY,SAIrBe,cA5TkB,SA4TJjL,cACNqM,EAAUnmB,EAAED,KAAKkE,UAAUe,SAAS1E,GACtCA,EAAiB,MAEjBP,KAAKkkB,UAAYlkB,KAAKyH,QAAQ4e,SAAU,KACpCC,EAAY1lB,EAAKgD,yBAA2BwiB,UAE7CnC,UAAYziB,SAAS+kB,cAAc,YACnCtC,UAAUuC,UAAYjmB,EAEvB6lB,KACApmB,KAAKikB,WAAW9Y,SAASib,KAG3BpmB,KAAKikB,WAAWwC,SAASjlB,SAAS4O,QAElCpQ,KAAKkE,UAAU2B,GAAGvF,EAAMskB,cAAe,SAAC/gB,GACpC6iB,EAAKtC,uBACFA,sBAAuB,EAG1BvgB,EAAMpF,SAAWoF,EAAMiL,gBAGG,WAA1B4X,EAAKjf,QAAQ4e,WACVniB,SAASwC,UAET0G,UAILkZ,KACG3a,OAAO3L,KAAKikB,aAGjBjkB,KAAKikB,WAAW9Y,SAAS5K,IAEtBwZ,aAIAuM,oBAKHtmB,KAAKikB,WACJhjB,IAAIL,EAAKM,eAAgB6Y,GACzBpW,qBA9V4B,UA+V1B,IAAK3D,KAAKkkB,UAAYlkB,KAAKikB,UAAW,GACzCjkB,KAAKikB,WAAWjf,YAAYzE,OAExBomB,EAAiB,aAChBR,kBACDpM,QAKFnZ,EAAKgD,yBACN3D,EAAED,KAAKkE,UAAUe,SAAS1E,KACzBP,KAAKikB,WACJhjB,IAAIL,EAAKM,eAAgBylB,GACzBhjB,qBA7W0B,cAiXtBoW,UAUb0K,cAzYkB,eA0YVmC,EACJ5mB,KAAKkE,SAAS2iB,aAAerlB,SAASkI,gBAAgBuK,cAEnDjU,KAAKmkB,oBAAsByC,SACzB1iB,SAAS4J,MAAMgZ,YAAiB9mB,KAAKskB,gBAA1C,MAGEtkB,KAAKmkB,qBAAuByC,SACzB1iB,SAAS4J,MAAMiZ,aAAkB/mB,KAAKskB,gBAA3C,SAIJ0B,kBAtZkB,gBAuZX9hB,SAAS4J,MAAMgZ,YAAc,QAC7B5iB,SAAS4J,MAAMiZ,aAAe,MAGrCxC,gBA3ZkB,eA4ZV5Q,EAAOnS,SAAS4O,KAAK9B,6BACtB6V,mBAAqBxQ,EAAKH,KAAOG,EAAKJ,MAAQ/P,OAAOgS,gBACrD8O,gBAAkBtkB,KAAKgnB,wBAG9BxC,cAjakB,yBAkaZxkB,KAAKmkB,mBAAoB,GAKzBzjB,EAASumB,eAAe1hB,KAAK,SAACqD,EAAOlH,OAC/BwlB,EAAgBjnB,EAAEyB,GAAS,GAAGoM,MAAMiZ,aACpCI,EAAoBlnB,EAAEyB,GAASwG,IAAI,mBACvCxG,GAAS+D,KAAK,gBAAiByhB,GAAehf,IAAI,gBAAoBqK,WAAW4U,GAAqBC,EAAK9C,gBAA7G,UAIA5jB,EAAS2mB,gBAAgB9hB,KAAK,SAACqD,EAAOlH,OAChC4lB,EAAernB,EAAEyB,GAAS,GAAGoM,MAAMsJ,YACnCmQ,EAAmBtnB,EAAEyB,GAASwG,IAAI,kBACtCxG,GAAS+D,KAAK,eAAgB6hB,GAAcpf,IAAI,eAAmBqK,WAAWgV,GAAoBH,EAAK9C,gBAAzG,UAIA5jB,EAAS8mB,gBAAgBjiB,KAAK,SAACqD,EAAOlH,OAChC4lB,EAAernB,EAAEyB,GAAS,GAAGoM,MAAMsJ,YACnCmQ,EAAmBtnB,EAAEyB,GAASwG,IAAI,kBACtCxG,GAAS+D,KAAK,eAAgB6hB,GAAcpf,IAAI,eAAmBqK,WAAWgV,GAAoBH,EAAK9C,gBAAzG,YAII4C,EAAgB1lB,SAAS4O,KAAKtC,MAAMiZ,aACpCI,EAAoBlnB,EAAE,QAAQiI,IAAI,mBACtC,QAAQzC,KAAK,gBAAiByhB,GAAehf,IAAI,gBAAoBqK,WAAW4U,GAAqBnnB,KAAKskB,gBAA5G,UAIJ2B,gBAlckB,aAocdvlB,EAASumB,eAAe1hB,KAAK,SAACqD,EAAOlH,OAC/B0T,EAAUnV,EAAEyB,GAAS+D,KAAK,iBACT,oBAAZ2P,KACP1T,GAASwG,IAAI,gBAAiBkN,GAAS1Q,WAAW,qBAKnDhE,EAAS2mB,eAAd,KAAiC3mB,EAAS8mB,gBAAkBjiB,KAAK,SAACqD,EAAOlH,OACjE+lB,EAASxnB,EAAEyB,GAAS+D,KAAK,gBACT,oBAAXgiB,KACP/lB,GAASwG,IAAI,eAAgBuf,GAAQ/iB,WAAW,sBAKhD0Q,EAAUnV,EAAE,QAAQwF,KAAK,iBACR,oBAAZ2P,KACP,QAAQlN,IAAI,gBAAiBkN,GAAS1Q,WAAW,oBAIvDsiB,mBA1dkB,eA2dVU,EAAYlmB,SAAS+kB,cAAc,SAC/BC,UAAYjmB,WACb6P,KAAKkV,YAAYoC,OACpBC,EAAiBD,EAAUpZ,wBAAwB0E,MAAQ0U,EAAU1T,4BAClE5D,KAAKoR,YAAYkG,GACnBC,KAKFriB,iBAreW,SAqeM5C,EAAQ+H,UACvBzK,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,GAClBsH,EAAAA,KACDqc,EAAM9c,QACN/G,EAAED,MAAMyF,OACU,iBAAX/C,GAAuBA,MAG9B+C,MACI,IAAIqe,EAAM9jB,KAAMyH,KACrBzH,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,EAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,GAAQ+H,QACJhD,EAAQ4F,QACZA,KAAK5C,oDAjfmB,+CAgF1BzD,oBA6aTxF,UAAUqE,GAAGvF,EAAMwF,eAAgBpF,EAASkM,YAAa,SAAU/I,OAC/DpF,SACEkD,EAAWf,EAAK+D,uBAAuB3E,MAEzC2B,MACO1B,EAAE0B,GAAU,QAGjBe,EAASzC,EAAExB,GAAQgH,KAAKtF,GAC1B,SADWV,KAERQ,EAAExB,GAAQgH,OACVxF,EAAED,MAAMyF,QAGM,MAAjBzF,KAAK6J,SAAoC,SAAjB7J,KAAK6J,WACzBjE,qBAGFoJ,EAAU/O,EAAExB,GAAQwC,IAAIX,EAAMqN,KAAM,SAAC8U,GACrCA,EAAUle,wBAKNtD,IAAIX,EAAMiO,OAAQ,WACpBtO,EAAAA,GAAQ8D,GAAG,eACR2C,cAKLpB,iBAAiBxF,KAAKG,EAAExB,GAASiE,EAAQ1C,UAS/C0D,GAAF,MAAaogB,EAAMxe,mBACjB5B,GAAF,MAAWrE,YAAcykB,IACvBpgB,GAAF,MAAWqC,WAAa,oBACpBrC,GAAF,MAAarD,EACNyjB,EAAMxe,kBAGRwe,EApjBM,CAqjBZ7jB,GCpjBG2nB,GAAW,SAAC3nB,OAOVC,EAAsB,UAEtBC,EAAsB,aACtBC,EAAAA,IAA0BD,EAC1BE,EAAsBJ,EAAEyD,GAAGxD,GAG3B2nB,EAAqB,IAAIzkB,OAAJ,wBAAyC,KAE9D6D,aACkB,mBACA,eACA,oCACA,eACA,uBACA,mBACA,6BACA,2BACA,4BACA,6CACA,0BACA,oBAGlB8a,QACK,WACA,YACA,eACA,cACA,QAGL/a,cACkB,WACA,+GAGA,oBACA,SACA,QACA,YACA,YACA,aACA,aACA,oBACA,gBACA,gBAGlB8gB,EACG,OADHA,EAEG,MAGHxnB,eACgBF,kBACEA,cACFA,gBACCA,sBACGA,gBACHA,oBACEA,sBACCA,0BACEA,0BACAA,GAGtBG,EACG,OADHA,EAEG,OAGHG,EAEY,iBAFZA,EAGY,SAGZqnB,EACK,QADLA,EAEK,QAFLA,EAGK,QAHLA,EAIK,SAULH,wBACQlmB,EAASgB,MAKG,oBAAXwe,SACH,IAAInV,UAAU,qEAIjBic,YAAiB,OACjBC,SAAiB,OACjBC,YAAiB,QACjBC,uBACAnG,QAAiB,UAGjBtgB,QAAUA,OACVgB,OAAU1C,KAAK0H,WAAWhF,QAC1B0lB,IAAU,UAEVC,2CAmCPC,OA5JoB,gBA6JbN,YAAa,KAGpBO,QAhKoB,gBAiKbP,YAAa,KAGpBQ,cApKoB,gBAqKbR,YAAchoB,KAAKgoB,cAG1BhiB,OAxKoB,SAwKbnC,MACA7D,KAAKgoB,cAINnkB,EAAO,KACH4kB,EAAUzoB,KAAKgjB,YAAY7iB,SAC7BkjB,EAAUpjB,EAAE4D,EAAMiL,eAAerJ,KAAKgjB,GAErCpF,MACO,IAAIrjB,KAAKgjB,YACjBnf,EAAMiL,cACN9O,KAAK0oB,wBAEL7kB,EAAMiL,eAAerJ,KAAKgjB,EAASpF,MAG/B8E,eAAeQ,OAAStF,EAAQ8E,eAAeQ,MAEnDtF,EAAQuF,yBACFC,OAAO,KAAMxF,KAEbyF,OAAO,KAAMzF,OAElB,IACDpjB,EAAED,KAAK+oB,iBAAiB9jB,SAAS1E,oBAC9BuoB,OAAO,KAAM9oB,WAIf6oB,OAAO,KAAM7oB,UAItByE,QA1MoB,wBA2MLzE,KAAKioB,YAEhBvjB,WAAW1E,KAAK0B,QAAS1B,KAAKgjB,YAAY7iB,YAE1CH,KAAK0B,SAASwH,IAAIlJ,KAAKgjB,YAAY5iB,aACnCJ,KAAK0B,SAASmD,QAAQ,UAAUqE,IAAI,iBAElClJ,KAAKooB,OACLpoB,KAAKooB,KAAK/iB,cAGT2iB,WAAiB,UACjBC,SAAiB,UACjBC,YAAiB,UACjBC,eAAiB,KACD,OAAjBnoB,KAAKgiB,cACFA,QAAQa,eAGVb,QAAU,UACVtgB,QAAU,UACVgB,OAAU,UACV0lB,IAAU,QAGjB/a,KApOoB,yBAqOqB,SAAnCpN,EAAED,KAAK0B,SAASwG,IAAI,iBAChB,IAAI5E,MAAM,2CAGZmf,EAAYxiB,EAAEK,MAAMN,KAAKgjB,YAAY1iB,MAAMqN,SAC7C3N,KAAKgpB,iBAAmBhpB,KAAKgoB,WAAY,GACzChoB,KAAK0B,SAASU,QAAQqgB,OAElBwG,EAAahpB,EAAEwG,SACnBzG,KAAK0B,QAAQ2O,cAAc3G,gBAC3B1J,KAAK0B,YAGH+gB,EAAUle,uBAAyB0kB,aAIjCb,EAAQpoB,KAAK+oB,gBACbG,EAAQtoB,EAAKuoB,OAAOnpB,KAAKgjB,YAAY9iB,QAEvCyG,aAAa,KAAMuiB,QAClBxnB,QAAQiF,aAAa,mBAAoBuiB,QAEzCE,aAEDppB,KAAK0C,OAAO2mB,aACZjB,GAAKjd,SAAS5K,OAGZuV,EAA8C,mBAA1B9V,KAAK0C,OAAOoT,UAClC9V,KAAK0C,OAAOoT,UAAUhW,KAAKE,KAAMooB,EAAKpoB,KAAK0B,SAC3C1B,KAAK0C,OAAOoT,UAEVwT,EAAatpB,KAAKupB,eAAezT,QAClC0T,mBAAmBF,OAElBG,GAAsC,IAA1BzpB,KAAK0C,OAAO+mB,UAAsBjoB,SAAS4O,KAAOnQ,EAAED,KAAK0C,OAAO+mB,aAEhFrB,GAAK3iB,KAAKzF,KAAKgjB,YAAY7iB,SAAUH,MAElCC,EAAEwG,SAASzG,KAAK0B,QAAQ2O,cAAc3G,gBAAiB1J,KAAKooB,QAC7DA,GAAK3B,SAASgD,KAGhBzpB,KAAK0B,SAASU,QAAQpC,KAAKgjB,YAAY1iB,MAAMopB,eAE1C1H,QAAU,IAAId,GAAOlhB,KAAK0B,QAAS0mB,aAC3BkB,4BAGCtpB,KAAK0C,OAAOqZ,uBAGV/b,KAAK0C,OAAOinB,kCAGbjpB,sCAGUV,KAAK0C,OAAOggB,oBAGzB,SAACjd,GACLA,EAAKwZ,oBAAsBxZ,EAAKqQ,aAC7B8T,6BAA6BnkB,aAG5B,SAACA,KACJmkB,6BAA6BnkB,QAIpC2iB,GAAKjd,SAAS5K,GAMZ,iBAAkBiB,SAASkI,mBAC3B,QAAQwB,WAAWrF,GAAG,YAAa,KAAM5F,EAAE2iB,UAGzC3U,EAAW,WACX7M,EAAKsB,OAAO2mB,aACTQ,qBAEDC,EAAiB1oB,EAAK8mB,cACvBA,YAAkB,OAErB9mB,EAAKM,SAASU,QAAQhB,EAAK4hB,YAAY1iB,MAAM4N,OAE3C4b,IAAmBhC,KAChBgB,OAAO,KAAZ1nB,IAIAR,EAAKgD,yBAA2B3D,EAAED,KAAKooB,KAAKnjB,SAAS1E,KACrDP,KAAKooB,KACJnnB,IAAIL,EAAKM,eAAgB+M,GACzBtK,qBAAqBikB,EAAQmC,8BAOtC3c,KA/UoB,SA+Uf2M,cACGqO,EAAYpoB,KAAK+oB,gBACjBxF,EAAYtjB,EAAEK,MAAMN,KAAKgjB,YAAY1iB,MAAM+N,MAC3CJ,EAAW,WACX3E,EAAK4e,cAAgBJ,GAAmBM,EAAInY,cAC1CA,WAAWuR,YAAY4G,KAGxB4B,mBACAtoB,QAAQqf,gBAAgB,sBAC3BzX,EAAK5H,SAASU,QAAQkH,EAAK0Z,YAAY1iB,MAAMiO,QAC1B,OAAjBjF,EAAK0Y,WACFA,QAAQa,UAGX9I,UAKJ/Z,KAAK0B,SAASU,QAAQmhB,GAEpBA,EAAUhf,yBAIZ6jB,GAAKpjB,YAAYzE,GAIf,iBAAkBiB,SAASkI,mBAC3B,QAAQwB,WAAWhC,IAAI,YAAa,KAAMjJ,EAAE2iB,WAG3CuF,eAAeJ,IAAiB,OAChCI,eAAeJ,IAAiB,OAChCI,eAAeJ,IAAiB,EAEjCnnB,EAAKgD,yBACL3D,EAAED,KAAKooB,KAAKnjB,SAAS1E,KACrB6nB,GACCnnB,IAAIL,EAAKM,eAAgB+M,GACzBtK,qBA7WmB,cAkXnBukB,YAAc,OAGrB9G,OAjYoB,WAkYG,OAAjBphB,KAAKgiB,cACFA,QAAQ3H,oBAMjB2O,cAzYoB,kBA0YX1mB,QAAQtC,KAAKiqB,eAGtBT,mBA7YoB,SA6YDF,KACftpB,KAAK+oB,iBAAiB5d,SAAY+e,cAAgBZ,MAGtDP,cAjZoB,uBAkZbX,IAAMpoB,KAAKooB,KAAOnoB,EAAED,KAAK0C,OAAOynB,UAAU,GACxCnqB,KAAKooB,OAGdgB,WAtZoB,eAuZZgB,EAAOnqB,EAAED,KAAK+oB,sBACfsB,kBAAkBD,EAAKnoB,KAAKvB,GAAyBV,KAAKiqB,cAC1DjlB,YAAezE,EAApB,IAAsCA,MAGxC8pB,kBA5ZoB,SA4ZF7kB,EAAU8kB,OACpBtY,EAAOhS,KAAK0C,OAAOsP,KACF,iBAAZsY,IAAyBA,EAAQ9nB,UAAY8nB,EAAQ7b,QAE1DuD,EACG/R,EAAEqqB,GAAS1lB,SAASb,GAAGyB,MACjB+kB,QAAQC,OAAOF,KAGjBG,KAAKxqB,EAAEqqB,GAASG,UAGlBzY,EAAO,OAAS,QAAQsY,MAIrCL,SA5aoB,eA6adS,EAAQ1qB,KAAK0B,QAAQE,aAAa,8BAEjC8oB,MACkC,mBAAtB1qB,KAAK0C,OAAOgoB,MACvB1qB,KAAK0C,OAAOgoB,MAAM5qB,KAAKE,KAAK0B,SAC5B1B,KAAK0C,OAAOgoB,OAGXA,KAKTnB,eA1boB,SA0bLzT,UACNiM,EAAcjM,EAAUvS,kBAGjC8kB,cA9boB,sBA+bDroB,KAAK0C,OAAON,QAAQyU,MAAM,KAElC6B,QAAQ,SAACtW,MACA,UAAZA,IACAwJ,EAAKlK,SAASmE,GACd+F,EAAKoX,YAAY1iB,MAAMwiB,MACvBlX,EAAKlJ,OAAOf,SACZ,SAACkC,UAAU+H,EAAK5F,OAAOnC,UAEpB,GAAIzB,IAAY2lB,EAAgB,KAC/B4C,EAAUvoB,IAAY2lB,EACxBnc,EAAKoX,YAAY1iB,MAAMkJ,WACvBoC,EAAKoX,YAAY1iB,MAAM4kB,QACrB0F,EAAWxoB,IAAY2lB,EACzBnc,EAAKoX,YAAY1iB,MAAMmJ,WACvBmC,EAAKoX,YAAY1iB,MAAMuqB,WAEzBjf,EAAKlK,SACJmE,GACC8kB,EACA/e,EAAKlJ,OAAOf,SACZ,SAACkC,UAAU+H,EAAKid,OAAOhlB,KAExBgC,GACC+kB,EACAhf,EAAKlJ,OAAOf,SACZ,SAACkC,UAAU+H,EAAKkd,OAAOjlB,OAI3B+H,EAAKlK,SAASmD,QAAQ,UAAUgB,GAChC,gBACA,kBAAM+F,EAAKwB,WAIXpN,KAAK0C,OAAOf,cACTe,OAALjD,KACKO,KAAK0C,gBACC,kBACC,UAGPooB,eAITA,UA9eoB,eA+eZC,SAAmB/qB,KAAK0B,QAAQE,aAAa,wBAC/C5B,KAAK0B,QAAQE,aAAa,UACb,WAAdmpB,UACIrpB,QAAQiF,aACX,sBACA3G,KAAK0B,QAAQE,aAAa,UAAY,SAEnCF,QAAQiF,aAAa,QAAS,QAIvCkiB,OA1foB,SA0fbhlB,EAAOwf,OACNoF,EAAUzoB,KAAKgjB,YAAY7iB,YAEvBkjB,GAAWpjB,EAAE4D,EAAMiL,eAAerJ,KAAKgjB,QAGrC,IAAIzoB,KAAKgjB,YACjBnf,EAAMiL,cACN9O,KAAK0oB,wBAEL7kB,EAAMiL,eAAerJ,KAAKgjB,EAASpF,IAGnCxf,MACMskB,eACS,YAAftkB,EAAMuC,KAAqB2hB,EAAgBA,IACzC,GAGF9nB,EAAEojB,EAAQ0F,iBAAiB9jB,SAAS1E,IACrC8iB,EAAQ6E,cAAgBJ,IACjBI,YAAcJ,gBAIXzE,EAAQ4E,YAEbC,YAAcJ,EAEjBzE,EAAQ3gB,OAAOsoB,OAAU3H,EAAQ3gB,OAAOsoB,MAAM3d,OAK3C4a,SAAWre,WAAW,WACxByZ,EAAQ6E,cAAgBJ,KAClBza,QAETgW,EAAQ3gB,OAAOsoB,MAAM3d,QARdA,WAWZyb,OAniBoB,SAmiBbjlB,EAAOwf,OACNoF,EAAUzoB,KAAKgjB,YAAY7iB,YAEvBkjB,GAAWpjB,EAAE4D,EAAMiL,eAAerJ,KAAKgjB,QAGrC,IAAIzoB,KAAKgjB,YACjBnf,EAAMiL,cACN9O,KAAK0oB,wBAEL7kB,EAAMiL,eAAerJ,KAAKgjB,EAASpF,IAGnCxf,MACMskB,eACS,aAAftkB,EAAMuC,KAAsB2hB,EAAgBA,IAC1C,GAGF1E,EAAQuF,sCAICvF,EAAQ4E,YAEbC,YAAcJ,EAEjBzE,EAAQ3gB,OAAOsoB,OAAU3H,EAAQ3gB,OAAOsoB,MAAM5d,OAK3C6a,SAAWre,WAAW,WACxByZ,EAAQ6E,cAAgBJ,KAClB1a,QAETiW,EAAQ3gB,OAAOsoB,MAAM5d,QARdA,WAWZwb,qBA1kBoB,eA2kBb,IAAMxmB,KAAWpC,KAAKmoB,kBACrBnoB,KAAKmoB,eAAe/lB,UACf,SAIJ,KAGTsF,WAplBoB,SAolBThF,SAOmB,wBALvB1C,KAAKgjB,YAAYhc,QACjB/G,EAAED,KAAK0B,SAAS+D,OAChB/C,IAGasoB,UACTA,YACCtoB,EAAOsoB,WACPtoB,EAAOsoB,QAIW,iBAAjBtoB,EAAOgoB,UACTA,MAAQhoB,EAAOgoB,MAAMznB,YAGA,iBAAnBP,EAAO4nB,YACTA,QAAU5nB,EAAO4nB,QAAQrnB,cAG7BkG,gBACHjJ,EACAwC,EACA1C,KAAKgjB,YAAY/b,aAGZvE,KAGTgmB,mBAnnBoB,eAonBZhmB,QAEF1C,KAAK0C,WACF,IAAMvD,KAAOa,KAAK0C,OACjB1C,KAAKgjB,YAAYhc,QAAQ7H,KAASa,KAAK0C,OAAOvD,OACzCA,GAAOa,KAAK0C,OAAOvD,WAKzBuD,KAGTsnB,eAjoBoB,eAkoBZI,EAAOnqB,EAAED,KAAK+oB,iBACdkC,EAAWb,EAAKrc,KAAK,SAAS7K,MAAM2kB,GACzB,OAAboD,GAAqBA,EAASrsB,OAAS,KACpCoG,YAAYimB,EAASC,KAAK,QAInCtB,6BAzoBoB,SAyoBSnkB,QACtBukB,sBACAR,mBAAmBxpB,KAAKupB,eAAe9jB,EAAKqQ,eAGnD+T,eA9oBoB,eA+oBZzB,EAAMpoB,KAAK+oB,gBACXoC,EAAsBnrB,KAAK0C,OAAO2mB,UACA,OAApCjB,EAAIxmB,aAAa,mBAGnBwmB,GAAKpjB,YAAYzE,QACdmC,OAAO2mB,WAAY,OACnBjc,YACAC,YACA3K,OAAO2mB,UAAY8B,MAKnB7lB,iBA7pBa,SA6pBI5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,GAClBsH,EAA4B,iBAAX/E,GAAuBA,MAEzC+C,IAAQ,eAAepC,KAAKX,MAI5B+C,MACI,IAAImiB,EAAQ5nB,KAAMyH,KACvBzH,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,GAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,uDAvqBe,+CA2HjBsE,sCAIA9G,0CAIAC,uCAIAG,2CAIAF,6CAIA6G,oBAoiBTvD,GAAGxD,GAAQ0nB,EAAQtiB,mBACnB5B,GAAGxD,GAAMb,YAAcuoB,IACvBlkB,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACNunB,EAAQtiB,kBAGVsiB,EAlsBQ,CAmsBd3nB,GCpsBGmrB,GAAW,SAACnrB,OAOVC,EAAsB,UAEtBC,EAAsB,aACtBC,EAAAA,IAA0BD,EAC1BE,EAAsBJ,EAAEyD,GAAGxD,GAE3B2nB,EAAsB,IAAIzkB,OAAJ,wBAAyC,KAE/D4D,EAAAA,KACD4gB,GAAQ5gB,mBACC,gBACA,gBACA,YACA,wIAMRC,EAAAA,KACD2gB,GAAQ3gB,qBACD,8BAGN1G,EACG,OADHA,EAEG,OAGHG,EACM,kBADNA,EAEM,gBAGNJ,eACgBF,kBACEA,cACFA,gBACCA,sBACGA,gBACHA,oBACEA,sBACCA,0BACEA,0BACAA,GAStBgrB,cVlCR,IAAwBC,EAAUC,oDAAAA,KAAVD,KACb7rB,UAAYP,OAAOssB,OAAOD,EAAW9rB,WAC9C6rB,EAAS7rB,UAAUwjB,YAAcqI,EACjCA,EAASG,UAAYF,6BUgEnBtC,cA7FoB,kBA8FXhpB,KAAKiqB,YAAcjqB,KAAKyrB,iBAGjCjC,mBAjGoB,SAiGDF,KACftpB,KAAK+oB,iBAAiB5d,SAAY+e,cAAgBZ,MAGtDP,cArGoB,uBAsGbX,IAAMpoB,KAAKooB,KAAOnoB,EAAED,KAAK0C,OAAOynB,UAAU,GACxCnqB,KAAKooB,OAGdgB,WA1GoB,eA2GZgB,EAAOnqB,EAAED,KAAK+oB,sBAGfsB,kBAAkBD,EAAKnoB,KAAKvB,GAAiBV,KAAKiqB,gBACnDK,EAAUtqB,KAAKyrB,cACI,mBAAZnB,MACCA,EAAQxqB,KAAKE,KAAK0B,eAEzB2oB,kBAAkBD,EAAKnoB,KAAKvB,GAAmB4pB,KAE/CtlB,YAAezE,EAApB,IAAsCA,MAKxCkrB,YA1HoB,kBA2HXzrB,KAAK0B,QAAQE,aAAa,iBAC/B5B,KAAK0C,OAAO4nB,WAGhBN,eA/HoB,eAgIZI,EAAOnqB,EAAED,KAAK+oB,iBACdkC,EAAWb,EAAKrc,KAAK,SAAS7K,MAAM2kB,GACzB,OAAboD,GAAqBA,EAASrsB,OAAS,KACpCoG,YAAYimB,EAASC,KAAK,QAM5B5lB,iBAzIa,SAyII5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,GAClBsH,EAA4B,iBAAX/E,EAAsBA,EAAS,SAEjD+C,IAAQ,eAAepC,KAAKX,MAI5B+C,MACI,IAAI2lB,EAAQprB,KAAMyH,KACvBzH,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,GAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,uDAnJe,+CA4DjBsE,sCAIA9G,0CAIAC,uCAIAG,2CAIAF,6CAIA6G,SA5BW2gB,aA2GpBlkB,GAAGxD,GAAQkrB,EAAQ9lB,mBACnB5B,GAAGxD,GAAMb,YAAc+rB,IACvB1nB,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACN+qB,EAAQ9lB,kBAGV8lB,EA9KQ,CA+KdnrB,GC/KGyrB,GAAa,SAACzrB,OAOZC,EAAqB,YAErBC,EAAqB,eACrBC,EAAAA,IAAyBD,EAEzBE,EAAqBJ,EAAEyD,GAAGxD,GAE1B8G,UACK,UACA,cACA,IAGLC,UACK,gBACA,gBACA,oBAGL3G,uBACuBF,kBACFA,uBACFA,EAlBE,aAqBrBG,EACY,gBADZA,EAGY,SAGZG,YACc,6BACA,yBACA,8BACA,sBACA,uBACA,4BACA,2BACA,iCACA,oBAGdirB,EACO,SADPA,EAEO,WASPD,wBACQhqB,EAASgB,mBACdwB,SAAiBxC,OACjBkqB,eAAqC,SAApBlqB,EAAQmI,QAAqBrG,OAAS9B,OACvD+F,QAAiBzH,KAAK0H,WAAWhF,QACjCqK,UAAoB/M,KAAKyH,QAAQhJ,OAAhB,IAA0BiC,EAASmrB,UAAnC,IACG7rB,KAAKyH,QAAQhJ,OADhB,IAC0BiC,EAASorB,WADnC,IAEG9rB,KAAKyH,QAAQhJ,OAFhB,IAE0BiC,EAASqrB,oBACpDC,iBACAC,iBACAC,cAAiB,UACjBC,cAAiB,IAEpBnsB,KAAK4rB,gBAAgB/lB,GAAGvF,EAAM8rB,OAAQ,SAACvoB,UAAUzC,EAAKirB,SAASxoB,UAE5DyoB,eACAD,sCAePC,QA5FsB,sBA6FdC,EAAavsB,KAAK4rB,iBAAmB5rB,KAAK4rB,eAAepoB,OAC3DmoB,EAAsBA,EAEpBa,EAAuC,SAAxBxsB,KAAKyH,QAAQglB,OAC9BF,EAAavsB,KAAKyH,QAAQglB,OAExBC,EAAaF,IAAiBb,EAChC3rB,KAAK2sB,gBAAkB,OAEtBX,iBACAC,iBAEAE,cAAgBnsB,KAAK4sB,mBAEV3sB,EAAE8J,UAAU9J,EAAED,KAAK+M,YAGhCoJ,IAAI,SAACzU,OACAjD,EACEouB,EAAiBjsB,EAAK+D,uBAAuBjD,MAE/CmrB,MACO5sB,EAAE4sB,GAAgB,IAGzBpuB,EAAQ,KACJquB,EAAYruB,EAAO6P,2BACrBwe,EAAU9Z,OAAS8Z,EAAU/Z,cAG7B9S,EAAExB,GAAQ+tB,KAAgB9Y,IAAMgZ,EAChCG,UAIC,OAER/f,OAAO,SAACigB,UAASA,IACjBzW,KAAK,SAACC,EAAGC,UAAMD,EAAE,GAAKC,EAAE,KACxBkC,QAAQ,SAACqU,KACHf,SAAShf,KAAK+f,EAAK,MACnBd,SAASjf,KAAK+f,EAAK,SAI9BtoB,QA1IsB,aA2IlBC,WAAW1E,KAAKkE,SAAU/D,KAC1BH,KAAK4rB,gBAAgB1iB,IAAI9I,QAEtB8D,SAAiB,UACjB0nB,eAAiB,UACjBnkB,QAAiB,UACjBsF,UAAiB,UACjBif,SAAiB,UACjBC,SAAiB,UACjBC,cAAiB,UACjBC,cAAiB,QAKxBzkB,WA1JsB,SA0JXhF,MAMoB,wBAJxBsE,EACAtE,IAGajE,OAAqB,KACjCiO,EAAKzM,EAAEyC,EAAOjE,QAAQsP,KAAK,MAC1BrB,MACE9L,EAAKuoB,OAAOjpB,KACfwC,EAAOjE,QAAQsP,KAAK,KAAMrB,MAEvBjO,OAAP,IAAoBiO,WAGjBvD,gBAAgBjJ,EAAMwC,EAAQuE,GAE5BvE,KAGTiqB,cA9KsB,kBA+Kb3sB,KAAK4rB,iBAAmBpoB,OAC3BxD,KAAK4rB,eAAeoB,YAAchtB,KAAK4rB,eAAehY,aAG5DgZ,iBAnLsB,kBAoLb5sB,KAAK4rB,eAAe/E,cAAgBvlB,KAAKuR,IAC9CrR,SAAS4O,KAAKyW,aACdrlB,SAASkI,gBAAgBmd,iBAI7BoG,iBA1LsB,kBA2LbjtB,KAAK4rB,iBAAmBpoB,OAC3BA,OAAOiS,YAAczV,KAAK4rB,eAAetd,wBAAwByE,UAGvEsZ,SA/LsB,eAgMdzY,EAAe5T,KAAK2sB,gBAAkB3sB,KAAKyH,QAAQsU,OACnD8K,EAAe7mB,KAAK4sB,mBACpBM,EAAeltB,KAAKyH,QAAQsU,OAChC8K,EACA7mB,KAAKitB,sBAEHjtB,KAAKmsB,gBAAkBtF,QACpByF,UAGH1Y,GAAasZ,OACTzuB,EAASuB,KAAKisB,SAASjsB,KAAKisB,SAASrtB,OAAS,GAEhDoB,KAAKksB,gBAAkBztB,QACpB0uB,UAAU1uB,WAKfuB,KAAKksB,eAAiBtY,EAAY5T,KAAKgsB,SAAS,IAAMhsB,KAAKgsB,SAAS,GAAK,cACtEE,cAAgB,eAChBkB,aAIF,IAAIzuB,EAAIqB,KAAKgsB,SAASptB,OAAQD,KAAM,CAChBqB,KAAKksB,gBAAkBlsB,KAAKisB,SAASttB,IACxDiV,GAAa5T,KAAKgsB,SAASrtB,KACM,oBAAzBqB,KAAKgsB,SAASrtB,EAAI,IACtBiV,EAAY5T,KAAKgsB,SAASrtB,EAAI,UAG/BwuB,UAAUntB,KAAKisB,SAASttB,SAKnCwuB,UArOsB,SAqOZ1uB,QACHytB,cAAgBztB,OAEhB2uB,aAEDC,EAAUrtB,KAAK+M,UAAU8J,MAAM,OAEzBwW,EAAQlX,IAAI,SAACxU,UACXA,EAAH,iBAA4BlD,EAA5B,MACGkD,EADH,UACqBlD,EADrB,WAIH6uB,EAAQrtB,EAAEotB,EAAQnC,KAAK,MAEzBoC,EAAMroB,SAAS1E,MACXsE,QAAQnE,EAAS6sB,UAAUtrB,KAAKvB,EAAS8sB,iBAAiBriB,SAAS5K,KACnE4K,SAAS5K,OAGT4K,SAAS5K,KAGTktB,QAAQ/sB,EAASgtB,gBAAgBvlB,KAAQzH,EAASmrB,UAAxD,KAAsEnrB,EAASorB,YAAc3gB,SAAS5K,KAEhGktB,QAAQ/sB,EAASgtB,gBAAgBvlB,KAAKzH,EAASitB,WAAWziB,SAASxK,EAASmrB,WAAW1gB,SAAS5K,MAGtGP,KAAK4rB,gBAAgBxpB,QAAQ9B,EAAMstB,wBACpBnvB,OAInB2uB,OArQsB,aAsQlBptB,KAAK+M,WAAWD,OAAOpM,EAASsK,QAAQhG,YAAYzE,MAKjD+E,iBA3Qe,SA2QE5C,UACf1C,KAAKuF,KAAK,eACXE,EAAOxF,EAAED,MAAMyF,KAAKtF,MAGnBsF,MACI,IAAIimB,EAAU1rB,KAHW,iBAAX0C,GAAuBA,KAI1C1C,MAAMyF,KAAKtF,EAAUsF,IAGH,iBAAX/C,EAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,uDAjRc,+CA+EhBsE,oBA8MTxD,QAAQqC,GAAGvF,EAAM6L,cAAe,mBAC1B0hB,EAAa5tB,EAAE8J,UAAU9J,EAAES,EAASotB,WAEjCnvB,EAAIkvB,EAAWjvB,OAAQD,KAAM,KAC9BovB,EAAO9tB,EAAE4tB,EAAWlvB,MAChB2G,iBAAiBxF,KAAKiuB,EAAMA,EAAKtoB,aAU7C/B,GAAGxD,GAAQwrB,EAAUpmB,mBACrB5B,GAAGxD,GAAMb,YAAcqsB,IACvBhoB,GAAGxD,GAAM6F,WAAa,oBACpBrC,GAAGxD,GAAQG,EACNqrB,EAAUpmB,kBAGZomB,EA3TU,CA4ThBzrB,GC5TG+tB,GAAO,SAAC/tB,OAUNG,EAAAA,UAEAC,EAAsBJ,EAAEyD,GAAF,IAGtBpD,eACoBF,kBACEA,cACFA,gBACCA,0CAIrBG,EACY,gBADZA,EAEY,SAFZA,EAGY,WAHZA,EAIY,OAJZA,EAKY,OAGZG,EACoB,YADpBA,EAEoB,oBAFpBA,EAGoB,UAHpBA,EAIoB,iBAJpBA,EAKoB,kEALpBA,EAMoB,mBANpBA,EAOoB,2BASpBstB,wBACQtsB,QACLwC,SAAWxC,6BAWlB2L,KA5DgB,2BA6DVrN,KAAKkE,SAAS+L,YACdjQ,KAAKkE,SAAS+L,WAAWzN,WAAa2O,KAAKkU,cAC3CplB,EAAED,KAAKkE,UAAUe,SAAS1E,IAC1BN,EAAED,KAAKkE,UAAUe,SAAS1E,SAI1B9B,EACAwvB,EACEC,EAAcjuB,EAAED,KAAKkE,UAAUW,QAAQnE,GAAyB,GAChEiB,EAAWf,EAAK+D,uBAAuB3E,KAAKkE,aAE9CgqB,EAAa,KACTC,EAAwC,OAAzBD,EAAYle,SAAoBtP,EAAqBA,OAC/DT,EAAE8J,UAAU9J,EAAEiuB,GAAajsB,KAAKksB,KACvBF,EAASrvB,OAAS,OAGlC2kB,EAAYtjB,EAAEK,MAAMA,EAAM+N,oBACfrO,KAAKkE,WAGhBue,EAAYxiB,EAAEK,MAAMA,EAAMqN,oBACfsgB,OAGbA,KACAA,GAAU7rB,QAAQmhB,KAGpBvjB,KAAKkE,UAAU9B,QAAQqgB,IAErBA,EAAUle,uBACXgf,EAAUhf,sBAIT5C,MACO1B,EAAE0B,GAAU,SAGlBwrB,UACHntB,KAAKkE,SACLgqB,OAGIjgB,EAAW,eACTmgB,EAAcnuB,EAAEK,MAAMA,EAAMiO,sBACjBnN,EAAK8C,WAGhBuhB,EAAaxlB,EAAEK,MAAMA,EAAM4N,qBAChB+f,MAGfA,GAAU7rB,QAAQgsB,KAClBhtB,EAAK8C,UAAU9B,QAAQqjB,IAGvBhnB,OACG0uB,UAAU1uB,EAAQA,EAAOwR,WAAYhC,YAM9CxJ,QA/HgB,aAgIZC,WAAW1E,KAAKkE,SAvHM,eAwHnBA,SAAW,QAKlBipB,UAtIgB,SAsINzrB,EAAS+nB,EAAW1P,cAQtBsU,GANqB,OAAvB5E,EAAUzZ,SACK/P,EAAEwpB,GAAWxnB,KAAKvB,GAElBT,EAAEwpB,GAAWve,SAASxK,IAGX,GACxB8N,EAAkBuL,GACtBnZ,EAAKgD,yBACJyqB,GAAUpuB,EAAEouB,GAAQppB,SAAS1E,GAE1B0N,EAAW,kBAAM3E,EAAKglB,oBAC1B5sB,EACA2sB,EACAtU,IAGEsU,GAAU7f,IACV6f,GACCptB,IAAIL,EAAKM,eAAgB+M,GACzBtK,qBA/ImB,YAqJ1B2qB,oBAlKgB,SAkKI5sB,EAAS2sB,EAAQtU,MAC/BsU,EAAQ,GACRA,GAAQrpB,YAAezE,EAAzB,IAA2CA,OAErCguB,EAAgBtuB,EAAEouB,EAAOpe,YAAYhO,KACzCvB,GACA,GAEE6tB,KACAA,GAAevpB,YAAYzE,GAGK,QAAhC8tB,EAAOzsB,aAAa,WACf+E,aAAa,iBAAiB,QAIvCjF,GAASyJ,SAAS5K,GACiB,QAAjCmB,EAAQE,aAAa,WACf+E,aAAa,iBAAiB,KAGnCgF,OAAOjK,KACVA,GAASyJ,SAAS5K,GAEhBmB,EAAQuO,YACRhQ,EAAEyB,EAAQuO,YAAYhL,SAAS1E,GAA0B,KACrDiuB,EAAkBvuB,EAAEyB,GAASmD,QAAQnE,GAAmB,GAC1D8tB,KACAA,GAAiBvsB,KAAKvB,GAA0ByK,SAAS5K,KAGrDoG,aAAa,iBAAiB,GAGpCoT,UAOCzU,iBA5MS,SA4MQ5C,UACf1C,KAAKuF,KAAK,eACTsJ,EAAQ5O,EAAED,MACZyF,EAAOoJ,EAAMpJ,KAtMK,aAwMjBA,MACI,IAAIuoB,EAAIhuB,QACTyF,KA1Mc,SA0MCA,IAGD,iBAAX/C,EAAqB,IACF,oBAAjB+C,EAAK/C,SACR,IAAIqJ,UAAJ,oBAAkCrJ,EAAlC,OAEHA,uDAlNe,0BA8N1BlB,UACCqE,GAAGvF,EAAMwF,eAAgBpF,EAAsB,SAAUmD,KAClD+B,mBACFN,iBAAiBxF,KAAKG,EAAED,MAAO,YASrC0D,GAAF,IAAasqB,EAAI1oB,mBACf5B,GAAF,IAAWrE,YAAc2uB,IACvBtqB,GAAF,IAAWqC,WAAa,oBACpBrC,GAAF,IAAarD,EACN2tB,EAAI1oB,kBAGN0oB,EAzPI,CA0PV/tB,IChPH,SAAEA,MACiB,oBAANA,QACH,IAAI8L,UAAU,sGAGhB0iB,EAAUxuB,EAAEyD,GAAG+K,OAAOoI,MAAM,KAAK,GAAGA,MAAM,QAO5C4X,EAAQ,GALI,GAKYA,EAAQ,GAJnB,GAFA,IAMoCA,EAAQ,IAJ5C,IAI+DA,EAAQ,IAAmBA,EAAQ,GAHlG,GAGmHA,EAAQ,IAF3H,QAGT,IAAInrB,MAAM,+EAbpB,CAeGrD", "sourcesContent": ["export { _createClass as createClass, _extends as extends, _inherits<PERSON>oose as inherits<PERSON><PERSON>e };\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.12.9\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nvar timeoutDuration = 0;\nfor (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var css = getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  var offsetParent = element && element.offsetParent;\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    if (element) {\n      return element.ownerDocument.documentElement;\n    }\n\n    return document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width'], 10) + parseFloat(styles['border' + sideB + 'Width'], 10);\n}\n\n/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nvar isIE10 = undefined;\n\nvar isIE10$1 = function () {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n};\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE10$1() ? html['offset' + axis] + computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')] + computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')] : 0);\n}\n\nfunction getWindowSizes() {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE10$1() && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10$1()) {\n    try {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  var width = sizes.width || element.clientWidth || result.right - result.left;\n  var height = sizes.height || element.clientHeight || result.bottom - result.top;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var isIE10 = isIE10$1();\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop, 10);\n    var marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = getScroll(html);\n  var scrollLeft = getScroll(html, 'left');\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  // NOTE: 1 DOM access here\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var styles = getComputedStyle(element);\n  var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length - 1; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  // floor sides to avoid blurry text\n  var offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right)\n  };\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized], 10);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width'], 10);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    var flippedVariation = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement);\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport'\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedule an update, it will run on the next UI update available\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Util = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Private TransitionEnd Helpers\n   * ------------------------------------------------------------------------\n   */\n\n  let transition = false\n\n  const MAX_UID = 1000000\n\n  // Shoutout AngusCroll (https://goo.gl/pxwQGp)\n  function toType(obj) {\n    return {}.toString.call(obj).match(/\\s([a-zA-Z]+)/)[1].toLowerCase()\n  }\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: transition.end,\n      delegateType: transition.end,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n        }\n        return undefined // eslint-disable-line no-undefined\n      }\n    }\n  }\n\n  function transitionEndTest() {\n    if (typeof window !== 'undefined' && window.QUnit) {\n      return false\n    }\n\n    return {\n      end: 'transitionend'\n    }\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true\n    })\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this)\n      }\n    }, duration)\n\n    return this\n  }\n\n  function setTransitionEndSupport() {\n    transition = transitionEndTest()\n\n    $.fn.emulateTransitionEnd = transitionEndEmulator\n\n    if (Util.supportsTransitionEnd()) {\n      $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n    }\n  }\n\n  function escapeId(selector) {\n    // We escape IDs in case of special selectors (selector = '#myId:something')\n    // $.escapeSelector does not exist in jQuery < 3\n    selector = typeof $.escapeSelector === 'function' ? $.escapeSelector(selector).substr(1)\n      : selector.replace(/(:|\\.|\\[|\\]|,|=|@)/g, '\\\\$1')\n\n    return selector\n  }\n\n  /**\n   * --------------------------------------------------------------------------\n   * Public Util Api\n   * --------------------------------------------------------------------------\n   */\n\n  const Util = {\n\n    TRANSITION_END: 'bsTransitionEnd',\n\n    getUID(prefix) {\n      do {\n        // eslint-disable-next-line no-bitwise\n        prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n      } while (document.getElementById(prefix))\n      return prefix\n    },\n\n    getSelectorFromElement(element) {\n      let selector = element.getAttribute('data-target')\n      if (!selector || selector === '#') {\n        selector = element.getAttribute('href') || ''\n      }\n\n      // If it's an ID\n      if (selector.charAt(0) === '#') {\n        selector = escapeId(selector)\n      }\n\n      try {\n        const $selector = $(document).find(selector)\n        return $selector.length > 0 ? selector : null\n      } catch (err) {\n        return null\n      }\n    },\n\n    reflow(element) {\n      return element.offsetHeight\n    },\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(transition.end)\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(transition)\n    },\n\n    isElement(obj) {\n      return (obj[0] || obj).nodeType\n    },\n\n    typeCheckConfig(componentName, config, configTypes) {\n      for (const property in configTypes) {\n        if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n          const expectedTypes = configTypes[property]\n          const value         = config[property]\n          const valueType     = value && Util.isElement(value)\n            ? 'element' : toType(value)\n\n          if (!new RegExp(expectedTypes).test(valueType)) {\n            throw new Error(\n              `${componentName.toUpperCase()}: ` +\n              `Option \"${property}\" provided type \"${valueType}\" ` +\n              `but expected type \"${expectedTypes}\".`)\n          }\n        }\n      }\n    }\n  }\n\n  setTransitionEndSupport()\n\n  return Util\n})($)\n\nexport default Util\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Alert = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'alert'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.alert'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Selector = {\n    DISMISS : '[data-dismiss=\"alert\"]'\n  }\n\n  const Event = {\n    CLOSE          : `close${EVENT_KEY}`,\n    CLOSED         : `closed${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    ALERT : 'alert',\n    FADE  : 'fade',\n    SHOW  : 'show'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Alert {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    close(element) {\n      element = element || this._element\n\n      const rootElement = this._getRootElement(element)\n      const customEvent = this._triggerCloseEvent(rootElement)\n\n      if (customEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._removeElement(rootElement)\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Private\n\n    _getRootElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      let parent     = false\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      if (!parent) {\n        parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n      }\n\n      return parent\n    }\n\n    _triggerCloseEvent(element) {\n      const closeEvent = $.Event(Event.CLOSE)\n\n      $(element).trigger(closeEvent)\n      return closeEvent\n    }\n\n    _removeElement(element) {\n      $(element).removeClass(ClassName.SHOW)\n\n      if (!Util.supportsTransitionEnd() ||\n          !$(element).hasClass(ClassName.FADE)) {\n        this._destroyElement(element)\n        return\n      }\n\n      $(element)\n        .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    _destroyElement(element) {\n      $(element)\n        .detach()\n        .trigger(Event.CLOSED)\n        .remove()\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $element = $(this)\n        let data       = $element.data(DATA_KEY)\n\n        if (!data) {\n          data = new Alert(this)\n          $element.data(DATA_KEY, data)\n        }\n\n        if (config === 'close') {\n          data[config](this)\n        }\n      })\n    }\n\n    static _handleDismiss(alertInstance) {\n      return function (event) {\n        if (event) {\n          event.preventDefault()\n        }\n\n        alertInstance.close(this)\n      }\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(\n    Event.CLICK_DATA_API,\n    Selector.DISMISS,\n    Alert._handleDismiss(new Alert())\n  )\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Alert._jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert._jQueryInterface\n  }\n\n  return Alert\n})($)\n\nexport default Alert\n", "import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Button = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'button'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.button'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const ClassName = {\n    ACTIVE : 'active',\n    BUTTON : 'btn',\n    FOCUS  : 'focus'\n  }\n\n  const Selector = {\n    DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n    DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n    INPUT              : 'input',\n    ACTIVE             : '.active',\n    BUTTON             : '.btn'\n  }\n\n  const Event = {\n    CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                            `blur${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Button {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    toggle() {\n      let triggerChangeEvent = true\n      let addAriaPressed = true\n      const rootElement = $(this._element).closest(\n        Selector.DATA_TOGGLE\n      )[0]\n\n      if (rootElement) {\n        const input = $(this._element).find(Selector.INPUT)[0]\n\n        if (input) {\n          if (input.type === 'radio') {\n            if (input.checked &&\n              $(this._element).hasClass(ClassName.ACTIVE)) {\n              triggerChangeEvent = false\n            } else {\n              const activeElement = $(rootElement).find(Selector.ACTIVE)[0]\n\n              if (activeElement) {\n                $(activeElement).removeClass(ClassName.ACTIVE)\n              }\n            }\n          }\n\n          if (triggerChangeEvent) {\n            if (input.hasAttribute('disabled') ||\n              rootElement.hasAttribute('disabled') ||\n              input.classList.contains('disabled') ||\n              rootElement.classList.contains('disabled')) {\n              return\n            }\n            input.checked = !$(this._element).hasClass(ClassName.ACTIVE)\n            $(input).trigger('change')\n          }\n\n          input.focus()\n          addAriaPressed = false\n        }\n      }\n\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !$(this._element).hasClass(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Button(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (config === 'toggle') {\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      event.preventDefault()\n\n      let button = event.target\n\n      if (!$(button).hasClass(ClassName.BUTTON)) {\n        button = $(button).closest(Selector.BUTTON)\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    })\n    .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      const button = $(event.target).closest(Selector.BUTTON)[0]\n      $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Button._jQueryInterface\n  $.fn[NAME].Constructor = Button\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button._jQueryInterface\n  }\n\n  return Button\n})($)\n\nexport default Button\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Carousel = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                   = 'carousel'\n  const VERSION                = '4.0.0'\n  const DATA_KEY               = 'bs.carousel'\n  const EVENT_KEY              = `.${DATA_KEY}`\n  const DATA_API_KEY           = '.data-api'\n  const JQUERY_NO_CONFLICT     = $.fn[NAME]\n  const TRANSITION_DURATION    = 600\n  const ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\n  const ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\n  const TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\n  const Default = {\n    interval : 5000,\n    keyboard : true,\n    slide    : false,\n    pause    : 'hover',\n    wrap     : true\n  }\n\n  const DefaultType = {\n    interval : '(number|boolean)',\n    keyboard : 'boolean',\n    slide    : '(boolean|string)',\n    pause    : '(string|boolean)',\n    wrap     : 'boolean'\n  }\n\n  const Direction = {\n    NEXT     : 'next',\n    PREV     : 'prev',\n    LEFT     : 'left',\n    RIGHT    : 'right'\n  }\n\n  const Event = {\n    SLIDE          : `slide${EVENT_KEY}`,\n    SLID           : `slid${EVENT_KEY}`,\n    KEYDOWN        : `keydown${EVENT_KEY}`,\n    MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n    TOUCHEND       : `touchend${EVENT_KEY}`,\n    LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    CAROUSEL : 'carousel',\n    ACTIVE   : 'active',\n    SLIDE    : 'slide',\n    RIGHT    : 'carousel-item-right',\n    LEFT     : 'carousel-item-left',\n    NEXT     : 'carousel-item-next',\n    PREV     : 'carousel-item-prev',\n    ITEM     : 'carousel-item'\n  }\n\n  const Selector = {\n    ACTIVE      : '.active',\n    ACTIVE_ITEM : '.active.carousel-item',\n    ITEM        : '.carousel-item',\n    NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n    INDICATORS  : '.carousel-indicators',\n    DATA_SLIDE  : '[data-slide], [data-slide-to]',\n    DATA_RIDE   : '[data-ride=\"carousel\"]'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Carousel {\n    constructor(element, config) {\n      this._items             = null\n      this._interval          = null\n      this._activeElement     = null\n\n      this._isPaused          = false\n      this._isSliding         = false\n\n      this.touchTimeout       = null\n\n      this._config            = this._getConfig(config)\n      this._element           = $(element)[0]\n      this._indicatorsElement = $(this._element).find(Selector.INDICATORS)[0]\n\n      this._addEventListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    next() {\n      if (!this._isSliding) {\n        this._slide(Direction.NEXT)\n      }\n    }\n\n    nextWhenVisible() {\n      // Don't call next when the page isn't visible\n      // or the carousel or its parent isn't visible\n      if (!document.hidden &&\n        ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n        this.next()\n      }\n    }\n\n    prev() {\n      if (!this._isSliding) {\n        this._slide(Direction.PREV)\n      }\n    }\n\n    pause(event) {\n      if (!event) {\n        this._isPaused = true\n      }\n\n      if ($(this._element).find(Selector.NEXT_PREV)[0] &&\n        Util.supportsTransitionEnd()) {\n        Util.triggerTransitionEnd(this._element)\n        this.cycle(true)\n      }\n\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    cycle(event) {\n      if (!event) {\n        this._isPaused = false\n      }\n\n      if (this._interval) {\n        clearInterval(this._interval)\n        this._interval = null\n      }\n\n      if (this._config.interval && !this._isPaused) {\n        this._interval = setInterval(\n          (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n          this._config.interval\n        )\n      }\n    }\n\n    to(index) {\n      this._activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n\n      const activeIndex = this._getItemIndex(this._activeElement)\n\n      if (index > this._items.length - 1 || index < 0) {\n        return\n      }\n\n      if (this._isSliding) {\n        $(this._element).one(Event.SLID, () => this.to(index))\n        return\n      }\n\n      if (activeIndex === index) {\n        this.pause()\n        this.cycle()\n        return\n      }\n\n      const direction = index > activeIndex\n        ? Direction.NEXT\n        : Direction.PREV\n\n      this._slide(direction, this._items[index])\n    }\n\n    dispose() {\n      $(this._element).off(EVENT_KEY)\n      $.removeData(this._element, DATA_KEY)\n\n      this._items             = null\n      this._config            = null\n      this._element           = null\n      this._interval          = null\n      this._isPaused          = null\n      this._isSliding         = null\n      this._activeElement     = null\n      this._indicatorsElement = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _addEventListeners() {\n      if (this._config.keyboard) {\n        $(this._element)\n          .on(Event.KEYDOWN, (event) => this._keydown(event))\n      }\n\n      if (this._config.pause === 'hover') {\n        $(this._element)\n          .on(Event.MOUSEENTER, (event) => this.pause(event))\n          .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n        if ('ontouchstart' in document.documentElement) {\n          // If it's a touch-enabled device, mouseenter/leave are fired as\n          // part of the mouse compatibility events on first tap - the carousel\n          // would stop cycling until user tapped out of it;\n          // here, we listen for touchend, explicitly pause the carousel\n          // (as if it's the second time we tap on it, mouseenter compat event\n          // is NOT fired) and after a timeout (to allow for mouse compatibility\n          // events to fire) we explicitly restart cycling\n          $(this._element).on(Event.TOUCHEND, () => {\n            this.pause()\n            if (this.touchTimeout) {\n              clearTimeout(this.touchTimeout)\n            }\n            this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n          })\n        }\n      }\n    }\n\n    _keydown(event) {\n      if (/input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      switch (event.which) {\n        case ARROW_LEFT_KEYCODE:\n          event.preventDefault()\n          this.prev()\n          break\n        case ARROW_RIGHT_KEYCODE:\n          event.preventDefault()\n          this.next()\n          break\n        default:\n      }\n    }\n\n    _getItemIndex(element) {\n      this._items = $.makeArray($(element).parent().find(Selector.ITEM))\n      return this._items.indexOf(element)\n    }\n\n    _getItemByDirection(direction, activeElement) {\n      const isNextDirection = direction === Direction.NEXT\n      const isPrevDirection = direction === Direction.PREV\n      const activeIndex     = this._getItemIndex(activeElement)\n      const lastItemIndex   = this._items.length - 1\n      const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                              isNextDirection && activeIndex === lastItemIndex\n\n      if (isGoingToWrap && !this._config.wrap) {\n        return activeElement\n      }\n\n      const delta     = direction === Direction.PREV ? -1 : 1\n      const itemIndex = (activeIndex + delta) % this._items.length\n\n      return itemIndex === -1\n        ? this._items[this._items.length - 1] : this._items[itemIndex]\n    }\n\n    _triggerSlideEvent(relatedTarget, eventDirectionName) {\n      const targetIndex = this._getItemIndex(relatedTarget)\n      const fromIndex = this._getItemIndex($(this._element).find(Selector.ACTIVE_ITEM)[0])\n      const slideEvent = $.Event(Event.SLIDE, {\n        relatedTarget,\n        direction: eventDirectionName,\n        from: fromIndex,\n        to: targetIndex\n      })\n\n      $(this._element).trigger(slideEvent)\n\n      return slideEvent\n    }\n\n    _setActiveIndicatorElement(element) {\n      if (this._indicatorsElement) {\n        $(this._indicatorsElement)\n          .find(Selector.ACTIVE)\n          .removeClass(ClassName.ACTIVE)\n\n        const nextIndicator = this._indicatorsElement.children[\n          this._getItemIndex(element)\n        ]\n\n        if (nextIndicator) {\n          $(nextIndicator).addClass(ClassName.ACTIVE)\n        }\n      }\n    }\n\n    _slide(direction, element) {\n      const activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n      const activeElementIndex = this._getItemIndex(activeElement)\n      const nextElement   = element || activeElement &&\n        this._getItemByDirection(direction, activeElement)\n      const nextElementIndex = this._getItemIndex(nextElement)\n      const isCycling = Boolean(this._interval)\n\n      let directionalClassName\n      let orderClassName\n      let eventDirectionName\n\n      if (direction === Direction.NEXT) {\n        directionalClassName = ClassName.LEFT\n        orderClassName = ClassName.NEXT\n        eventDirectionName = Direction.LEFT\n      } else {\n        directionalClassName = ClassName.RIGHT\n        orderClassName = ClassName.PREV\n        eventDirectionName = Direction.RIGHT\n      }\n\n      if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n        this._isSliding = false\n        return\n      }\n\n      const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n      if (slideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (!activeElement || !nextElement) {\n        // Some weirdness is happening, so we bail\n        return\n      }\n\n      this._isSliding = true\n\n      if (isCycling) {\n        this.pause()\n      }\n\n      this._setActiveIndicatorElement(nextElement)\n\n      const slidEvent = $.Event(Event.SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n\n      if (Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.SLIDE)) {\n        $(nextElement).addClass(orderClassName)\n\n        Util.reflow(nextElement)\n\n        $(activeElement).addClass(directionalClassName)\n        $(nextElement).addClass(directionalClassName)\n\n        $(activeElement)\n          .one(Util.TRANSITION_END, () => {\n            $(nextElement)\n              .removeClass(`${directionalClassName} ${orderClassName}`)\n              .addClass(ClassName.ACTIVE)\n\n            $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n            this._isSliding = false\n\n            setTimeout(() => $(this._element).trigger(slidEvent), 0)\n          })\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        $(activeElement).removeClass(ClassName.ACTIVE)\n        $(nextElement).addClass(ClassName.ACTIVE)\n\n        this._isSliding = false\n        $(this._element).trigger(slidEvent)\n      }\n\n      if (isCycling) {\n        this.cycle()\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        let _config = {\n          ...Default,\n          ...$(this).data()\n        }\n\n        if (typeof config === 'object') {\n          _config = {\n            ..._config,\n            ...config\n          }\n        }\n\n        const action = typeof config === 'string' ? config : _config.slide\n\n        if (!data) {\n          data = new Carousel(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'number') {\n          data.to(config)\n        } else if (typeof action === 'string') {\n          if (typeof data[action] === 'undefined') {\n            throw new TypeError(`No method named \"${action}\"`)\n          }\n          data[action]()\n        } else if (_config.interval) {\n          data.pause()\n          data.cycle()\n        }\n      })\n    }\n\n    static _dataApiClickHandler(event) {\n      const selector = Util.getSelectorFromElement(this)\n\n      if (!selector) {\n        return\n      }\n\n      const target = $(selector)[0]\n\n      if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n        return\n      }\n\n      const config = {\n        ...$(target).data(),\n        ...$(this).data()\n      }\n      const slideIndex = this.getAttribute('data-slide-to')\n\n      if (slideIndex) {\n        config.interval = false\n      }\n\n      Carousel._jQueryInterface.call($(target), config)\n\n      if (slideIndex) {\n        $(target).data(DATA_KEY).to(slideIndex)\n      }\n\n      event.preventDefault()\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    $(Selector.DATA_RIDE).each(function () {\n      const $carousel = $(this)\n      Carousel._jQueryInterface.call($carousel, $carousel.data())\n    })\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Carousel._jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel._jQueryInterface\n  }\n\n  return Carousel\n})($)\n\nexport default Carousel\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>tra<PERSON> (v4.0.0): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Collapse = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'collapse'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.collapse'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 600\n\n  const Default = {\n    toggle : true,\n    parent : ''\n  }\n\n  const DefaultType = {\n    toggle : 'boolean',\n    parent : '(string|element)'\n  }\n\n  const Event = {\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SHOW       : 'show',\n    COLLAPSE   : 'collapse',\n    COLLAPSING : 'collapsing',\n    COLLAPSED  : 'collapsed'\n  }\n\n  const Dimension = {\n    WIDTH  : 'width',\n    HEIGHT : 'height'\n  }\n\n  const Selector = {\n    ACTIVES     : '.show, .collapsing',\n    DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Collapse {\n    constructor(element, config) {\n      this._isTransitioning = false\n      this._element         = element\n      this._config          = this._getConfig(config)\n      this._triggerArray    = $.makeArray($(\n        `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n        `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n      ))\n      const tabToggles = $(Selector.DATA_TOGGLE)\n      for (let i = 0; i < tabToggles.length; i++) {\n        const elem = tabToggles[i]\n        const selector = Util.getSelectorFromElement(elem)\n        if (selector !== null && $(selector).filter(element).length > 0) {\n          this._selector = selector\n          this._triggerArray.push(elem)\n        }\n      }\n\n      this._parent = this._config.parent ? this._getParent() : null\n\n      if (!this._config.parent) {\n        this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n      }\n\n      if (this._config.toggle) {\n        this.toggle()\n      }\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    toggle() {\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        this.hide()\n      } else {\n        this.show()\n      }\n    }\n\n    show() {\n      if (this._isTransitioning ||\n        $(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      let actives\n      let activesData\n\n      if (this._parent) {\n        actives = $.makeArray(\n          $(this._parent)\n            .find(Selector.ACTIVES)\n            .filter(`[data-parent=\"${this._config.parent}\"]`)\n        )\n        if (actives.length === 0) {\n          actives = null\n        }\n      }\n\n      if (actives) {\n        activesData = $(actives).not(this._selector).data(DATA_KEY)\n        if (activesData && activesData._isTransitioning) {\n          return\n        }\n      }\n\n      const startEvent = $.Event(Event.SHOW)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (actives) {\n        Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n        if (!activesData) {\n          $(actives).data(DATA_KEY, null)\n        }\n      }\n\n      const dimension = this._getDimension()\n\n      $(this._element)\n        .removeClass(ClassName.COLLAPSE)\n        .addClass(ClassName.COLLAPSING)\n\n      this._element.style[dimension] = 0\n\n      if (this._triggerArray.length > 0) {\n        $(this._triggerArray)\n          .removeClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', true)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .addClass(ClassName.SHOW)\n\n        this._element.style[dimension] = ''\n\n        this.setTransitioning(false)\n\n        $(this._element).trigger(Event.SHOWN)\n      }\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n      const scrollSize = `scroll${capitalizedDimension}`\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n\n      this._element.style[dimension] = `${this._element[scrollSize]}px`\n    }\n\n    hide() {\n      if (this._isTransitioning ||\n        !$(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      const startEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      const dimension = this._getDimension()\n\n      this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n      Util.reflow(this._element)\n\n      $(this._element)\n        .addClass(ClassName.COLLAPSING)\n        .removeClass(ClassName.COLLAPSE)\n        .removeClass(ClassName.SHOW)\n\n      if (this._triggerArray.length > 0) {\n        for (let i = 0; i < this._triggerArray.length; i++) {\n          const trigger = this._triggerArray[i]\n          const selector = Util.getSelectorFromElement(trigger)\n          if (selector !== null) {\n            const $elem = $(selector)\n            if (!$elem.hasClass(ClassName.SHOW)) {\n              $(trigger).addClass(ClassName.COLLAPSED)\n                .attr('aria-expanded', false)\n            }\n          }\n        }\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        this.setTransitioning(false)\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .trigger(Event.HIDDEN)\n      }\n\n      this._element.style[dimension] = ''\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    setTransitioning(isTransitioning) {\n      this._isTransitioning = isTransitioning\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      this._config          = null\n      this._parent          = null\n      this._element         = null\n      this._triggerArray    = null\n      this._isTransitioning = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      config.toggle = Boolean(config.toggle) // Coerce string values\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _getDimension() {\n      const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n      return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n    }\n\n    _getParent() {\n      let parent = null\n      if (Util.isElement(this._config.parent)) {\n        parent = this._config.parent\n\n        // It's a jQuery object\n        if (typeof this._config.parent.jquery !== 'undefined') {\n          parent = this._config.parent[0]\n        }\n      } else {\n        parent = $(this._config.parent)[0]\n      }\n\n      const selector =\n        `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n      $(parent).find(selector).each((i, element) => {\n        this._addAriaAndCollapsedClass(\n          Collapse._getTargetFromElement(element),\n          [element]\n        )\n      })\n\n      return parent\n    }\n\n    _addAriaAndCollapsedClass(element, triggerArray) {\n      if (element) {\n        const isOpen = $(element).hasClass(ClassName.SHOW)\n\n        if (triggerArray.length > 0) {\n          $(triggerArray)\n            .toggleClass(ClassName.COLLAPSED, !isOpen)\n            .attr('aria-expanded', isOpen)\n        }\n      }\n    }\n\n    // Static\n\n    static _getTargetFromElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      return selector ? $(selector)[0] : null\n    }\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this   = $(this)\n        let data      = $this.data(DATA_KEY)\n        const _config = {\n          ...Default,\n          ...$this.data(),\n          ...typeof config === 'object' && config\n        }\n\n        if (!data && _config.toggle && /show|hide/.test(config)) {\n          _config.toggle = false\n        }\n\n        if (!data) {\n          data = new Collapse(this, _config)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n    if (event.currentTarget.tagName === 'A') {\n      event.preventDefault()\n    }\n\n    const $trigger = $(this)\n    const selector = Util.getSelectorFromElement(this)\n    $(selector).each(function () {\n      const $target = $(this)\n      const data    = $target.data(DATA_KEY)\n      const config  = data ? 'toggle' : $trigger.data()\n      Collapse._jQueryInterface.call($target, config)\n    })\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Collapse._jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse._jQueryInterface\n  }\n\n  return Collapse\n})($)\n\nexport default Collapse\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Dropdown = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                     = 'dropdown'\n  const VERSION                  = '4.0.0'\n  const DATA_KEY                 = 'bs.dropdown'\n  const EVENT_KEY                = `.${DATA_KEY}`\n  const DATA_API_KEY             = '.data-api'\n  const JQUERY_NO_CONFLICT       = $.fn[NAME]\n  const ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\n  const SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\n  const TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\n  const ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\n  const ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\n  const RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\n  const REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\n  const Event = {\n    HIDE             : `hide${EVENT_KEY}`,\n    HIDDEN           : `hidden${EVENT_KEY}`,\n    SHOW             : `show${EVENT_KEY}`,\n    SHOWN            : `shown${EVENT_KEY}`,\n    CLICK            : `click${EVENT_KEY}`,\n    CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n    KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n    KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DISABLED  : 'disabled',\n    SHOW      : 'show',\n    DROPUP    : 'dropup',\n    DROPRIGHT : 'dropright',\n    DROPLEFT  : 'dropleft',\n    MENURIGHT : 'dropdown-menu-right',\n    MENULEFT  : 'dropdown-menu-left',\n    POSITION_STATIC : 'position-static'\n  }\n\n  const Selector = {\n    DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n    FORM_CHILD    : '.dropdown form',\n    MENU          : '.dropdown-menu',\n    NAVBAR_NAV    : '.navbar-nav',\n    VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled)'\n  }\n\n  const AttachmentMap = {\n    TOP       : 'top-start',\n    TOPEND    : 'top-end',\n    BOTTOM    : 'bottom-start',\n    BOTTOMEND : 'bottom-end',\n    RIGHT     : 'right-start',\n    RIGHTEND  : 'right-end',\n    LEFT      : 'left-start',\n    LEFTEND   : 'left-end'\n  }\n\n  const Default = {\n    offset      : 0,\n    flip        : true,\n    boundary    : 'scrollParent'\n  }\n\n  const DefaultType = {\n    offset      : '(number|string|function)',\n    flip        : 'boolean',\n    boundary    : '(string|element)'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Dropdown {\n    constructor(element, config) {\n      this._element  = element\n      this._popper   = null\n      this._config   = this._getConfig(config)\n      this._menu     = this._getMenuElement()\n      this._inNavbar = this._detectNavbar()\n\n      this._addEventListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Public\n\n    toggle() {\n      if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this._element)\n      const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n      Dropdown._clearMenus()\n\n      if (isActive) {\n        return\n      }\n\n      const relatedTarget = {\n        relatedTarget: this._element\n      }\n      const showEvent = $.Event(Event.SHOW, relatedTarget)\n\n      $(parent).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      // Disable totally Popper.js for Dropdown in Navbar\n      if (!this._inNavbar) {\n        /**\n         * Check for Popper dependency\n         * Popper - https://popper.js.org\n         */\n        if (typeof Popper === 'undefined') {\n          throw new TypeError('Bootstrap dropdown require Popper.js (https://popper.js.org)')\n        }\n        let element = this._element\n        // For dropup with alignment we use the parent as popper container\n        if ($(parent).hasClass(ClassName.DROPUP)) {\n          if ($(this._menu).hasClass(ClassName.MENULEFT) || $(this._menu).hasClass(ClassName.MENURIGHT)) {\n            element = parent\n          }\n        }\n        // If boundary is not `scrollParent`, then set position to `static`\n        // to allow the menu to \"escape\" the scroll parent's boundaries\n        // https://github.com/twbs/bootstrap/issues/24251\n        if (this._config.boundary !== 'scrollParent') {\n          $(parent).addClass(ClassName.POSITION_STATIC)\n        }\n        this._popper = new Popper(element, this._menu, this._getPopperConfig())\n      }\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement &&\n         $(parent).closest(Selector.NAVBAR_NAV).length === 0) {\n        $('body').children().on('mouseover', null, $.noop)\n      }\n\n      this._element.focus()\n      this._element.setAttribute('aria-expanded', true)\n\n      $(this._menu).toggleClass(ClassName.SHOW)\n      $(parent)\n        .toggleClass(ClassName.SHOW)\n        .trigger($.Event(Event.SHOWN, relatedTarget))\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._element).off(EVENT_KEY)\n      this._element = null\n      this._menu = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    update() {\n      this._inNavbar = this._detectNavbar()\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // Private\n\n    _addEventListeners() {\n      $(this._element).on(Event.CLICK, (event) => {\n        event.preventDefault()\n        event.stopPropagation()\n        this.toggle()\n      })\n    }\n\n    _getConfig(config) {\n      config = {\n        ...this.constructor.Default,\n        ...$(this._element).data(),\n        ...config\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getMenuElement() {\n      if (!this._menu) {\n        const parent = Dropdown._getParentFromElement(this._element)\n        this._menu = $(parent).find(Selector.MENU)[0]\n      }\n      return this._menu\n    }\n\n    _getPlacement() {\n      const $parentDropdown = $(this._element).parent()\n      let placement = AttachmentMap.BOTTOM\n\n      // Handle dropup\n      if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n        placement = AttachmentMap.TOP\n        if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n          placement = AttachmentMap.TOPEND\n        }\n      } else if ($parentDropdown.hasClass(ClassName.DROPRIGHT)) {\n        placement = AttachmentMap.RIGHT\n      } else if ($parentDropdown.hasClass(ClassName.DROPLEFT)) {\n        placement = AttachmentMap.LEFT\n      } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.BOTTOMEND\n      }\n      return placement\n    }\n\n    _detectNavbar() {\n      return $(this._element).closest('.navbar').length > 0\n    }\n\n    _getPopperConfig() {\n      const offsetConf = {}\n      if (typeof this._config.offset === 'function') {\n        offsetConf.fn = (data) => {\n          data.offsets = {\n            ...data.offsets,\n            ...this._config.offset(data.offsets) || {}\n          }\n          return data\n        }\n      } else {\n        offsetConf.offset = this._config.offset\n      }\n      const popperConfig = {\n        placement: this._getPlacement(),\n        modifiers: {\n          offset: offsetConf,\n          flip: {\n            enabled: this._config.flip\n          },\n          preventOverflow: {\n            boundariesElement: this._config.boundary\n          }\n        }\n      }\n\n      return popperConfig\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data) {\n          data = new Dropdown(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n    static _clearMenus(event) {\n      if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n        event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n        return\n      }\n\n      const toggles = $.makeArray($(Selector.DATA_TOGGLE))\n      for (let i = 0; i < toggles.length; i++) {\n        const parent = Dropdown._getParentFromElement(toggles[i])\n        const context = $(toggles[i]).data(DATA_KEY)\n        const relatedTarget = {\n          relatedTarget: toggles[i]\n        }\n\n        if (!context) {\n          continue\n        }\n\n        const dropdownMenu = context._menu\n        if (!$(parent).hasClass(ClassName.SHOW)) {\n          continue\n        }\n\n        if (event && (event.type === 'click' &&\n            /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n            $.contains(parent, event.target)) {\n          continue\n        }\n\n        const hideEvent = $.Event(Event.HIDE, relatedTarget)\n        $(parent).trigger(hideEvent)\n        if (hideEvent.isDefaultPrevented()) {\n          continue\n        }\n\n        // If this is a touch-enabled device we remove the extra\n        // empty mouseover listeners we added for iOS support\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().off('mouseover', null, $.noop)\n        }\n\n        toggles[i].setAttribute('aria-expanded', 'false')\n\n        $(dropdownMenu).removeClass(ClassName.SHOW)\n        $(parent)\n          .removeClass(ClassName.SHOW)\n          .trigger($.Event(Event.HIDDEN, relatedTarget))\n      }\n    }\n\n    static _getParentFromElement(element) {\n      let parent\n      const selector = Util.getSelectorFromElement(element)\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      return parent || element.parentNode\n    }\n\n    // eslint-disable-next-line complexity\n    static _dataApiKeydownHandler(event) {\n      // If not input/textarea:\n      //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n      // If input/textarea:\n      //  - If space key => not a dropdown command\n      //  - If key is other than escape\n      //    - If key is not up or down => not a dropdown command\n      //    - If trigger inside the menu => not a dropdown command\n      if (/input|textarea/i.test(event.target.tagName)\n        ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n        (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n          $(event.target).closest(Selector.MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n        return\n      }\n\n      event.preventDefault()\n      event.stopPropagation()\n\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      if (!isActive && (event.which !== ESCAPE_KEYCODE || event.which !== SPACE_KEYCODE) ||\n           isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n        if (event.which === ESCAPE_KEYCODE) {\n          const toggle = $(parent).find(Selector.DATA_TOGGLE)[0]\n          $(toggle).trigger('focus')\n        }\n\n        $(this).trigger('click')\n        return\n      }\n\n      const items = $(parent).find(Selector.VISIBLE_ITEMS).get()\n\n      if (items.length === 0) {\n        return\n      }\n\n      let index = items.indexOf(event.target)\n\n      if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n        index--\n      }\n\n      if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n        index++\n      }\n\n      if (index < 0) {\n        index = 0\n      }\n\n      items[index].focus()\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n    .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      event.stopPropagation()\n      Dropdown._jQueryInterface.call($(this), 'toggle')\n    })\n    .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n      e.stopPropagation()\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Dropdown._jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown._jQueryInterface\n  }\n\n  return Dropdown\n})($, Popper)\n\nexport default Dropdown\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Modal = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                         = 'modal'\n  const VERSION                      = '4.0.0'\n  const DATA_KEY                     = 'bs.modal'\n  const EVENT_KEY                    = `.${DATA_KEY}`\n  const DATA_API_KEY                 = '.data-api'\n  const JQUERY_NO_CONFLICT           = $.fn[NAME]\n  const TRANSITION_DURATION          = 300\n  const BACKDROP_TRANSITION_DURATION = 150\n  const ESCAPE_KEYCODE               = 27 // KeyboardEvent.which value for Escape (Esc) key\n\n  const Default = {\n    backdrop : true,\n    keyboard : true,\n    focus    : true,\n    show     : true\n  }\n\n  const DefaultType = {\n    backdrop : '(boolean|string)',\n    keyboard : 'boolean',\n    focus    : 'boolean',\n    show     : 'boolean'\n  }\n\n  const Event = {\n    HIDE              : `hide${EVENT_KEY}`,\n    HIDDEN            : `hidden${EVENT_KEY}`,\n    SHOW              : `show${EVENT_KEY}`,\n    SHOWN             : `shown${EVENT_KEY}`,\n    FOCUSIN           : `focusin${EVENT_KEY}`,\n    RESIZE            : `resize${EVENT_KEY}`,\n    CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n    KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n    MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n    MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n    CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n    BACKDROP           : 'modal-backdrop',\n    OPEN               : 'modal-open',\n    FADE               : 'fade',\n    SHOW               : 'show'\n  }\n\n  const Selector = {\n    DIALOG             : '.modal-dialog',\n    DATA_TOGGLE        : '[data-toggle=\"modal\"]',\n    DATA_DISMISS       : '[data-dismiss=\"modal\"]',\n    FIXED_CONTENT      : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n    STICKY_CONTENT     : '.sticky-top',\n    NAVBAR_TOGGLER     : '.navbar-toggler'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Modal {\n    constructor(element, config) {\n      this._config              = this._getConfig(config)\n      this._element             = element\n      this._dialog              = $(element).find(Selector.DIALOG)[0]\n      this._backdrop            = null\n      this._isShown             = false\n      this._isBodyOverflowing   = false\n      this._ignoreBackdropClick = false\n      this._originalBodyPadding = 0\n      this._scrollbarWidth      = 0\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    toggle(relatedTarget) {\n      return this._isShown ? this.hide() : this.show(relatedTarget)\n    }\n\n    show(relatedTarget) {\n      if (this._isTransitioning || this._isShown) {\n        return\n      }\n\n      if (Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n      }\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget\n      })\n\n      $(this._element).trigger(showEvent)\n\n      if (this._isShown || showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = true\n\n      this._checkScrollbar()\n      this._setScrollbar()\n\n      this._adjustDialog()\n\n      $(document.body).addClass(ClassName.OPEN)\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(this._element).on(\n        Event.CLICK_DISMISS,\n        Selector.DATA_DISMISS,\n        (event) => this.hide(event)\n      )\n\n      $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n        $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n          if ($(event.target).is(this._element)) {\n            this._ignoreBackdropClick = true\n          }\n        })\n      })\n\n      this._showBackdrop(() => this._showElement(relatedTarget))\n    }\n\n    hide(event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      if (this._isTransitioning || !this._isShown) {\n        return\n      }\n\n      const hideEvent = $.Event(Event.HIDE)\n\n      $(this._element).trigger(hideEvent)\n\n      if (!this._isShown || hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = false\n\n      const transition = Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)\n\n      if (transition) {\n        this._isTransitioning = true\n      }\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(document).off(Event.FOCUSIN)\n\n      $(this._element).removeClass(ClassName.SHOW)\n\n      $(this._element).off(Event.CLICK_DISMISS)\n      $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n      if (transition) {\n        $(this._element)\n          .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        this._hideModal()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      $(window, document, this._element, this._backdrop).off(EVENT_KEY)\n\n      this._config              = null\n      this._element             = null\n      this._dialog              = null\n      this._backdrop            = null\n      this._isShown             = null\n      this._isBodyOverflowing   = null\n      this._ignoreBackdropClick = null\n      this._scrollbarWidth      = null\n    }\n\n    handleUpdate() {\n      this._adjustDialog()\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _showElement(relatedTarget) {\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n\n      if (!this._element.parentNode ||\n         this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // Don't move modal's DOM position\n        document.body.appendChild(this._element)\n      }\n\n      this._element.style.display = 'block'\n      this._element.removeAttribute('aria-hidden')\n      this._element.scrollTop = 0\n\n      if (transition) {\n        Util.reflow(this._element)\n      }\n\n      $(this._element).addClass(ClassName.SHOW)\n\n      if (this._config.focus) {\n        this._enforceFocus()\n      }\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget\n      })\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.focus()\n        }\n        this._isTransitioning = false\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (transition) {\n        $(this._dialog)\n          .one(Util.TRANSITION_END, transitionComplete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        transitionComplete()\n      }\n    }\n\n    _enforceFocus() {\n      $(document)\n        .off(Event.FOCUSIN) // Guard against infinite focus loop\n        .on(Event.FOCUSIN, (event) => {\n          if (document !== event.target &&\n              this._element !== event.target &&\n              $(this._element).has(event.target).length === 0) {\n            this._element.focus()\n          }\n        })\n    }\n\n    _setEscapeEvent() {\n      if (this._isShown && this._config.keyboard) {\n        $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n          if (event.which === ESCAPE_KEYCODE) {\n            event.preventDefault()\n            this.hide()\n          }\n        })\n      } else if (!this._isShown) {\n        $(this._element).off(Event.KEYDOWN_DISMISS)\n      }\n    }\n\n    _setResizeEvent() {\n      if (this._isShown) {\n        $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n      } else {\n        $(window).off(Event.RESIZE)\n      }\n    }\n\n    _hideModal() {\n      this._element.style.display = 'none'\n      this._element.setAttribute('aria-hidden', true)\n      this._isTransitioning = false\n      this._showBackdrop(() => {\n        $(document.body).removeClass(ClassName.OPEN)\n        this._resetAdjustments()\n        this._resetScrollbar()\n        $(this._element).trigger(Event.HIDDEN)\n      })\n    }\n\n    _removeBackdrop() {\n      if (this._backdrop) {\n        $(this._backdrop).remove()\n        this._backdrop = null\n      }\n    }\n\n    _showBackdrop(callback) {\n      const animate = $(this._element).hasClass(ClassName.FADE)\n        ? ClassName.FADE : ''\n\n      if (this._isShown && this._config.backdrop) {\n        const doAnimate = Util.supportsTransitionEnd() && animate\n\n        this._backdrop = document.createElement('div')\n        this._backdrop.className = ClassName.BACKDROP\n\n        if (animate) {\n          $(this._backdrop).addClass(animate)\n        }\n\n        $(this._backdrop).appendTo(document.body)\n\n        $(this._element).on(Event.CLICK_DISMISS, (event) => {\n          if (this._ignoreBackdropClick) {\n            this._ignoreBackdropClick = false\n            return\n          }\n          if (event.target !== event.currentTarget) {\n            return\n          }\n          if (this._config.backdrop === 'static') {\n            this._element.focus()\n          } else {\n            this.hide()\n          }\n        })\n\n        if (doAnimate) {\n          Util.reflow(this._backdrop)\n        }\n\n        $(this._backdrop).addClass(ClassName.SHOW)\n\n        if (!callback) {\n          return\n        }\n\n        if (!doAnimate) {\n          callback()\n          return\n        }\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callback)\n          .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n      } else if (!this._isShown && this._backdrop) {\n        $(this._backdrop).removeClass(ClassName.SHOW)\n\n        const callbackRemove = () => {\n          this._removeBackdrop()\n          if (callback) {\n            callback()\n          }\n        }\n\n        if (Util.supportsTransitionEnd() &&\n           $(this._element).hasClass(ClassName.FADE)) {\n          $(this._backdrop)\n            .one(Util.TRANSITION_END, callbackRemove)\n            .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n        } else {\n          callbackRemove()\n        }\n      } else if (callback) {\n        callback()\n      }\n    }\n\n    // ----------------------------------------------------------------------\n    // the following methods are used to handle overflowing modals\n    // todo (fat): these should probably be refactored out of modal.js\n    // ----------------------------------------------------------------------\n\n    _adjustDialog() {\n      const isModalOverflowing =\n        this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!this._isBodyOverflowing && isModalOverflowing) {\n        this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n      }\n\n      if (this._isBodyOverflowing && !isModalOverflowing) {\n        this._element.style.paddingRight = `${this._scrollbarWidth}px`\n      }\n    }\n\n    _resetAdjustments() {\n      this._element.style.paddingLeft = ''\n      this._element.style.paddingRight = ''\n    }\n\n    _checkScrollbar() {\n      const rect = document.body.getBoundingClientRect()\n      this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n      this._scrollbarWidth = this._getScrollbarWidth()\n    }\n\n    _setScrollbar() {\n      if (this._isBodyOverflowing) {\n        // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n        //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n        // Adjust fixed content padding\n        $(Selector.FIXED_CONTENT).each((index, element) => {\n          const actualPadding = $(element)[0].style.paddingRight\n          const calculatedPadding = $(element).css('padding-right')\n          $(element).data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust sticky content margin\n        $(Selector.STICKY_CONTENT).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n        })\n\n        // Adjust navbar-toggler margin\n        $(Selector.NAVBAR_TOGGLER).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust body padding\n        const actualPadding = document.body.style.paddingRight\n        const calculatedPadding = $('body').css('padding-right')\n        $('body').data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      }\n    }\n\n    _resetScrollbar() {\n      // Restore fixed content padding\n      $(Selector.FIXED_CONTENT).each((index, element) => {\n        const padding = $(element).data('padding-right')\n        if (typeof padding !== 'undefined') {\n          $(element).css('padding-right', padding).removeData('padding-right')\n        }\n      })\n\n      // Restore sticky content and navbar-toggler margin\n      $(`${Selector.STICKY_CONTENT}, ${Selector.NAVBAR_TOGGLER}`).each((index, element) => {\n        const margin = $(element).data('margin-right')\n        if (typeof margin !== 'undefined') {\n          $(element).css('margin-right', margin).removeData('margin-right')\n        }\n      })\n\n      // Restore body padding\n      const padding = $('body').data('padding-right')\n      if (typeof padding !== 'undefined') {\n        $('body').css('padding-right', padding).removeData('padding-right')\n      }\n    }\n\n    _getScrollbarWidth() { // thx d.walsh\n      const scrollDiv = document.createElement('div')\n      scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n      document.body.appendChild(scrollDiv)\n      const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n      document.body.removeChild(scrollDiv)\n      return scrollbarWidth\n    }\n\n    // Static\n\n    static _jQueryInterface(config, relatedTarget) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = {\n          ...Modal.Default,\n          ...$(this).data(),\n          ...typeof config === 'object' && config\n        }\n\n        if (!data) {\n          data = new Modal(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config](relatedTarget)\n        } else if (_config.show) {\n          data.show(relatedTarget)\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    let target\n    const selector = Util.getSelectorFromElement(this)\n\n    if (selector) {\n      target = $(selector)[0]\n    }\n\n    const config = $(target).data(DATA_KEY)\n      ? 'toggle' : {\n        ...$(target).data(),\n        ...$(this).data()\n      }\n\n    if (this.tagName === 'A' || this.tagName === 'AREA') {\n      event.preventDefault()\n    }\n\n    const $target = $(target).one(Event.SHOW, (showEvent) => {\n      if (showEvent.isDefaultPrevented()) {\n        // Only register focus restorer if modal will actually get shown\n        return\n      }\n\n      $target.one(Event.HIDDEN, () => {\n        if ($(this).is(':visible')) {\n          this.focus()\n        }\n      })\n    })\n\n    Modal._jQueryInterface.call($(target), config, this)\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Modal._jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal._jQueryInterface\n  }\n\n  return Modal\n})($)\n\nexport default Modal\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tooltip = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tooltip'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.tooltip'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n  const CLASS_PREFIX        = 'bs-tooltip'\n  const BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const DefaultType = {\n    animation           : 'boolean',\n    template            : 'string',\n    title               : '(string|element|function)',\n    trigger             : 'string',\n    delay               : '(number|object)',\n    html                : 'boolean',\n    selector            : '(string|boolean)',\n    placement           : '(string|function)',\n    offset              : '(number|string)',\n    container           : '(string|element|boolean)',\n    fallbackPlacement   : '(string|array)',\n    boundary            : '(string|element)'\n  }\n\n  const AttachmentMap = {\n    AUTO   : 'auto',\n    TOP    : 'top',\n    RIGHT  : 'right',\n    BOTTOM : 'bottom',\n    LEFT   : 'left'\n  }\n\n  const Default = {\n    animation           : true,\n    template            : '<div class=\"tooltip\" role=\"tooltip\">' +\n                        '<div class=\"arrow\"></div>' +\n                        '<div class=\"tooltip-inner\"></div></div>',\n    trigger             : 'hover focus',\n    title               : '',\n    delay               : 0,\n    html                : false,\n    selector            : false,\n    placement           : 'top',\n    offset              : 0,\n    container           : false,\n    fallbackPlacement   : 'flip',\n    boundary            : 'scrollParent'\n  }\n\n  const HoverState = {\n    SHOW : 'show',\n    OUT  : 'out'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TOOLTIP       : '.tooltip',\n    TOOLTIP_INNER : '.tooltip-inner',\n    ARROW         : '.arrow'\n  }\n\n  const Trigger = {\n    HOVER  : 'hover',\n    FOCUS  : 'focus',\n    CLICK  : 'click',\n    MANUAL : 'manual'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tooltip {\n    constructor(element, config) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap tooltips require Popper.js (https://popper.js.org)')\n      }\n\n      // private\n      this._isEnabled     = true\n      this._timeout       = 0\n      this._hoverState    = ''\n      this._activeTrigger = {}\n      this._popper        = null\n\n      // Protected\n      this.element = element\n      this.config  = this._getConfig(config)\n      this.tip     = null\n\n      this._setListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Public\n\n    enable() {\n      this._isEnabled = true\n    }\n\n    disable() {\n      this._isEnabled = false\n    }\n\n    toggleEnabled() {\n      this._isEnabled = !this._isEnabled\n    }\n\n    toggle(event) {\n      if (!this._isEnabled) {\n        return\n      }\n\n      if (event) {\n        const dataKey = this.constructor.DATA_KEY\n        let context = $(event.currentTarget).data(dataKey)\n\n        if (!context) {\n          context = new this.constructor(\n            event.currentTarget,\n            this._getDelegateConfig()\n          )\n          $(event.currentTarget).data(dataKey, context)\n        }\n\n        context._activeTrigger.click = !context._activeTrigger.click\n\n        if (context._isWithActiveTrigger()) {\n          context._enter(null, context)\n        } else {\n          context._leave(null, context)\n        }\n      } else {\n        if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n          this._leave(null, this)\n          return\n        }\n\n        this._enter(null, this)\n      }\n    }\n\n    dispose() {\n      clearTimeout(this._timeout)\n\n      $.removeData(this.element, this.constructor.DATA_KEY)\n\n      $(this.element).off(this.constructor.EVENT_KEY)\n      $(this.element).closest('.modal').off('hide.bs.modal')\n\n      if (this.tip) {\n        $(this.tip).remove()\n      }\n\n      this._isEnabled     = null\n      this._timeout       = null\n      this._hoverState    = null\n      this._activeTrigger = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      this._popper = null\n      this.element = null\n      this.config  = null\n      this.tip     = null\n    }\n\n    show() {\n      if ($(this.element).css('display') === 'none') {\n        throw new Error('Please use show on visible elements')\n      }\n\n      const showEvent = $.Event(this.constructor.Event.SHOW)\n      if (this.isWithContent() && this._isEnabled) {\n        $(this.element).trigger(showEvent)\n\n        const isInTheDom = $.contains(\n          this.element.ownerDocument.documentElement,\n          this.element\n        )\n\n        if (showEvent.isDefaultPrevented() || !isInTheDom) {\n          return\n        }\n\n        const tip   = this.getTipElement()\n        const tipId = Util.getUID(this.constructor.NAME)\n\n        tip.setAttribute('id', tipId)\n        this.element.setAttribute('aria-describedby', tipId)\n\n        this.setContent()\n\n        if (this.config.animation) {\n          $(tip).addClass(ClassName.FADE)\n        }\n\n        const placement  = typeof this.config.placement === 'function'\n          ? this.config.placement.call(this, tip, this.element)\n          : this.config.placement\n\n        const attachment = this._getAttachment(placement)\n        this.addAttachmentClass(attachment)\n\n        const container = this.config.container === false ? document.body : $(this.config.container)\n\n        $(tip).data(this.constructor.DATA_KEY, this)\n\n        if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n          $(tip).appendTo(container)\n        }\n\n        $(this.element).trigger(this.constructor.Event.INSERTED)\n\n        this._popper = new Popper(this.element, tip, {\n          placement: attachment,\n          modifiers: {\n            offset: {\n              offset: this.config.offset\n            },\n            flip: {\n              behavior: this.config.fallbackPlacement\n            },\n            arrow: {\n              element: Selector.ARROW\n            },\n            preventOverflow: {\n              boundariesElement: this.config.boundary\n            }\n          },\n          onCreate: (data) => {\n            if (data.originalPlacement !== data.placement) {\n              this._handlePopperPlacementChange(data)\n            }\n          },\n          onUpdate: (data) => {\n            this._handlePopperPlacementChange(data)\n          }\n        })\n\n        $(tip).addClass(ClassName.SHOW)\n\n        // If this is a touch-enabled device we add extra\n        // empty mouseover listeners to the body's immediate children;\n        // only needed because of broken event delegation on iOS\n        // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().on('mouseover', null, $.noop)\n        }\n\n        const complete = () => {\n          if (this.config.animation) {\n            this._fixTransition()\n          }\n          const prevHoverState = this._hoverState\n          this._hoverState     = null\n\n          $(this.element).trigger(this.constructor.Event.SHOWN)\n\n          if (prevHoverState === HoverState.OUT) {\n            this._leave(null, this)\n          }\n        }\n\n        if (Util.supportsTransitionEnd() && $(this.tip).hasClass(ClassName.FADE)) {\n          $(this.tip)\n            .one(Util.TRANSITION_END, complete)\n            .emulateTransitionEnd(Tooltip._TRANSITION_DURATION)\n        } else {\n          complete()\n        }\n      }\n    }\n\n    hide(callback) {\n      const tip       = this.getTipElement()\n      const hideEvent = $.Event(this.constructor.Event.HIDE)\n      const complete = () => {\n        if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n          tip.parentNode.removeChild(tip)\n        }\n\n        this._cleanTipClass()\n        this.element.removeAttribute('aria-describedby')\n        $(this.element).trigger(this.constructor.Event.HIDDEN)\n        if (this._popper !== null) {\n          this._popper.destroy()\n        }\n\n        if (callback) {\n          callback()\n        }\n      }\n\n      $(this.element).trigger(hideEvent)\n\n      if (hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      $(tip).removeClass(ClassName.SHOW)\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $('body').children().off('mouseover', null, $.noop)\n      }\n\n      this._activeTrigger[Trigger.CLICK] = false\n      this._activeTrigger[Trigger.FOCUS] = false\n      this._activeTrigger[Trigger.HOVER] = false\n\n      if (Util.supportsTransitionEnd() &&\n          $(this.tip).hasClass(ClassName.FADE)) {\n        $(tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        complete()\n      }\n\n      this._hoverState = ''\n    }\n\n    update() {\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // Protected\n\n    isWithContent() {\n      return Boolean(this.getTitle())\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n      this.setElementContent($tip.find(Selector.TOOLTIP_INNER), this.getTitle())\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    setElementContent($element, content) {\n      const html = this.config.html\n      if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n        // Content is a DOM node or a jQuery\n        if (html) {\n          if (!$(content).parent().is($element)) {\n            $element.empty().append(content)\n          }\n        } else {\n          $element.text($(content).text())\n        }\n      } else {\n        $element[html ? 'html' : 'text'](content)\n      }\n    }\n\n    getTitle() {\n      let title = this.element.getAttribute('data-original-title')\n\n      if (!title) {\n        title = typeof this.config.title === 'function'\n          ? this.config.title.call(this.element)\n          : this.config.title\n      }\n\n      return title\n    }\n\n    // Private\n\n    _getAttachment(placement) {\n      return AttachmentMap[placement.toUpperCase()]\n    }\n\n    _setListeners() {\n      const triggers = this.config.trigger.split(' ')\n\n      triggers.forEach((trigger) => {\n        if (trigger === 'click') {\n          $(this.element).on(\n            this.constructor.Event.CLICK,\n            this.config.selector,\n            (event) => this.toggle(event)\n          )\n        } else if (trigger !== Trigger.MANUAL) {\n          const eventIn = trigger === Trigger.HOVER\n            ? this.constructor.Event.MOUSEENTER\n            : this.constructor.Event.FOCUSIN\n          const eventOut = trigger === Trigger.HOVER\n            ? this.constructor.Event.MOUSELEAVE\n            : this.constructor.Event.FOCUSOUT\n\n          $(this.element)\n            .on(\n              eventIn,\n              this.config.selector,\n              (event) => this._enter(event)\n            )\n            .on(\n              eventOut,\n              this.config.selector,\n              (event) => this._leave(event)\n            )\n        }\n\n        $(this.element).closest('.modal').on(\n          'hide.bs.modal',\n          () => this.hide()\n        )\n      })\n\n      if (this.config.selector) {\n        this.config = {\n          ...this.config,\n          trigger: 'manual',\n          selector: ''\n        }\n      } else {\n        this._fixTitle()\n      }\n    }\n\n    _fixTitle() {\n      const titleType = typeof this.element.getAttribute('data-original-title')\n      if (this.element.getAttribute('title') ||\n         titleType !== 'string') {\n        this.element.setAttribute(\n          'data-original-title',\n          this.element.getAttribute('title') || ''\n        )\n        this.element.setAttribute('title', '')\n      }\n    }\n\n    _enter(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n        ] = true\n      }\n\n      if ($(context.getTipElement()).hasClass(ClassName.SHOW) ||\n         context._hoverState === HoverState.SHOW) {\n        context._hoverState = HoverState.SHOW\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.SHOW\n\n      if (!context.config.delay || !context.config.delay.show) {\n        context.show()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.SHOW) {\n          context.show()\n        }\n      }, context.config.delay.show)\n    }\n\n    _leave(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n        ] = false\n      }\n\n      if (context._isWithActiveTrigger()) {\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.OUT\n\n      if (!context.config.delay || !context.config.delay.hide) {\n        context.hide()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.OUT) {\n          context.hide()\n        }\n      }, context.config.delay.hide)\n    }\n\n    _isWithActiveTrigger() {\n      for (const trigger in this._activeTrigger) {\n        if (this._activeTrigger[trigger]) {\n          return true\n        }\n      }\n\n      return false\n    }\n\n    _getConfig(config) {\n      config = {\n        ...this.constructor.Default,\n        ...$(this.element).data(),\n        ...config\n      }\n\n      if (typeof config.delay === 'number') {\n        config.delay = {\n          show: config.delay,\n          hide: config.delay\n        }\n      }\n\n      if (typeof config.title === 'number') {\n        config.title = config.title.toString()\n      }\n\n      if (typeof config.content === 'number') {\n        config.content = config.content.toString()\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getDelegateConfig() {\n      const config = {}\n\n      if (this.config) {\n        for (const key in this.config) {\n          if (this.constructor.Default[key] !== this.config[key]) {\n            config[key] = this.config[key]\n          }\n        }\n      }\n\n      return config\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    _handlePopperPlacementChange(data) {\n      this._cleanTipClass()\n      this.addAttachmentClass(this._getAttachment(data.placement))\n    }\n\n    _fixTransition() {\n      const tip = this.getTipElement()\n      const initConfigAnimation = this.config.animation\n      if (tip.getAttribute('x-placement') !== null) {\n        return\n      }\n      $(tip).removeClass(ClassName.FADE)\n      this.config.animation = false\n      this.hide()\n      this.show()\n      this.config.animation = initConfigAnimation\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data && /dispose|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Tooltip(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Tooltip._jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip._jQueryInterface\n  }\n\n  return Tooltip\n})($, Popper)\n\nexport default Tooltip\n", "import $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Popover = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'popover'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.popover'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const CLASS_PREFIX        = 'bs-popover'\n  const BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const Default = {\n    ...Tooltip.Default,\n    placement : 'right',\n    trigger   : 'click',\n    content   : '',\n    template  : '<div class=\"popover\" role=\"tooltip\">' +\n                '<div class=\"arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n                '<div class=\"popover-body\"></div></div>'\n  }\n\n  const DefaultType = {\n    ...Tooltip.DefaultType,\n    content : '(string|element|function)'\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TITLE   : '.popover-header',\n    CONTENT : '.popover-body'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Popover extends Tooltip {\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Overrides\n\n    isWithContent() {\n      return this.getTitle() || this._getContent()\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      // We use append for html objects to maintain js events\n      this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n      let content = this._getContent()\n      if (typeof content === 'function') {\n        content = content.call(this.element)\n      }\n      this.setElementContent($tip.find(Selector.CONTENT), content)\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    // Private\n\n    _getContent() {\n      return this.element.getAttribute('data-content') ||\n        this.config.content\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data && /destroy|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Popover(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Popover._jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover._jQueryInterface\n  }\n\n  return Popover\n})($)\n\nexport default Popover\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ScrollSpy = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME               = 'scrollspy'\n  const VERSION            = '4.0.0'\n  const DATA_KEY           = 'bs.scrollspy'\n  const EVENT_KEY          = `.${DATA_KEY}`\n  const DATA_API_KEY       = '.data-api'\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n\n  const Default = {\n    offset : 10,\n    method : 'auto',\n    target : ''\n  }\n\n  const DefaultType = {\n    offset : 'number',\n    method : 'string',\n    target : '(string|element)'\n  }\n\n  const Event = {\n    ACTIVATE      : `activate${EVENT_KEY}`,\n    SCROLL        : `scroll${EVENT_KEY}`,\n    LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_ITEM : 'dropdown-item',\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active'\n  }\n\n  const Selector = {\n    DATA_SPY        : '[data-spy=\"scroll\"]',\n    ACTIVE          : '.active',\n    NAV_LIST_GROUP  : '.nav, .list-group',\n    NAV_LINKS       : '.nav-link',\n    NAV_ITEMS       : '.nav-item',\n    LIST_ITEMS      : '.list-group-item',\n    DROPDOWN        : '.dropdown',\n    DROPDOWN_ITEMS  : '.dropdown-item',\n    DROPDOWN_TOGGLE : '.dropdown-toggle'\n  }\n\n  const OffsetMethod = {\n    OFFSET   : 'offset',\n    POSITION : 'position'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class ScrollSpy {\n    constructor(element, config) {\n      this._element       = element\n      this._scrollElement = element.tagName === 'BODY' ? window : element\n      this._config        = this._getConfig(config)\n      this._selector      = `${this._config.target} ${Selector.NAV_LINKS},` +\n                            `${this._config.target} ${Selector.LIST_ITEMS},` +\n                            `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n      this._offsets       = []\n      this._targets       = []\n      this._activeTarget  = null\n      this._scrollHeight  = 0\n\n      $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n      this.refresh()\n      this._process()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    refresh() {\n      const autoMethod = this._scrollElement === this._scrollElement.window\n        ? OffsetMethod.OFFSET : OffsetMethod.POSITION\n\n      const offsetMethod = this._config.method === 'auto'\n        ? autoMethod : this._config.method\n\n      const offsetBase = offsetMethod === OffsetMethod.POSITION\n        ? this._getScrollTop() : 0\n\n      this._offsets = []\n      this._targets = []\n\n      this._scrollHeight = this._getScrollHeight()\n\n      const targets = $.makeArray($(this._selector))\n\n      targets\n        .map((element) => {\n          let target\n          const targetSelector = Util.getSelectorFromElement(element)\n\n          if (targetSelector) {\n            target = $(targetSelector)[0]\n          }\n\n          if (target) {\n            const targetBCR = target.getBoundingClientRect()\n            if (targetBCR.width || targetBCR.height) {\n              // TODO (fat): remove sketch reliance on jQuery position/offset\n              return [\n                $(target)[offsetMethod]().top + offsetBase,\n                targetSelector\n              ]\n            }\n          }\n          return null\n        })\n        .filter((item) => item)\n        .sort((a, b) => a[0] - b[0])\n        .forEach((item) => {\n          this._offsets.push(item[0])\n          this._targets.push(item[1])\n        })\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._scrollElement).off(EVENT_KEY)\n\n      this._element       = null\n      this._scrollElement = null\n      this._config        = null\n      this._selector      = null\n      this._offsets       = null\n      this._targets       = null\n      this._activeTarget  = null\n      this._scrollHeight  = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n\n      if (typeof config.target !== 'string') {\n        let id = $(config.target).attr('id')\n        if (!id) {\n          id = Util.getUID(NAME)\n          $(config.target).attr('id', id)\n        }\n        config.target = `#${id}`\n      }\n\n      Util.typeCheckConfig(NAME, config, DefaultType)\n\n      return config\n    }\n\n    _getScrollTop() {\n      return this._scrollElement === window\n        ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n    }\n\n    _getScrollHeight() {\n      return this._scrollElement.scrollHeight || Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight\n      )\n    }\n\n    _getOffsetHeight() {\n      return this._scrollElement === window\n        ? window.innerHeight : this._scrollElement.getBoundingClientRect().height\n    }\n\n    _process() {\n      const scrollTop    = this._getScrollTop() + this._config.offset\n      const scrollHeight = this._getScrollHeight()\n      const maxScroll    = this._config.offset +\n        scrollHeight -\n        this._getOffsetHeight()\n\n      if (this._scrollHeight !== scrollHeight) {\n        this.refresh()\n      }\n\n      if (scrollTop >= maxScroll) {\n        const target = this._targets[this._targets.length - 1]\n\n        if (this._activeTarget !== target) {\n          this._activate(target)\n        }\n        return\n      }\n\n      if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n        this._activeTarget = null\n        this._clear()\n        return\n      }\n\n      for (let i = this._offsets.length; i--;) {\n        const isActiveTarget = this._activeTarget !== this._targets[i] &&\n            scrollTop >= this._offsets[i] &&\n            (typeof this._offsets[i + 1] === 'undefined' ||\n                scrollTop < this._offsets[i + 1])\n\n        if (isActiveTarget) {\n          this._activate(this._targets[i])\n        }\n      }\n    }\n\n    _activate(target) {\n      this._activeTarget = target\n\n      this._clear()\n\n      let queries = this._selector.split(',')\n      // eslint-disable-next-line arrow-body-style\n      queries = queries.map((selector) => {\n        return `${selector}[data-target=\"${target}\"],` +\n               `${selector}[href=\"${target}\"]`\n      })\n\n      const $link = $(queries.join(','))\n\n      if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n        $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        $link.addClass(ClassName.ACTIVE)\n      } else {\n        // Set triggered link as active\n        $link.addClass(ClassName.ACTIVE)\n        // Set triggered links parents as active\n        // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n        $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n        // Handle special case when .nav-link is inside .nav-item\n        $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n      }\n\n      $(this._scrollElement).trigger(Event.ACTIVATE, {\n        relatedTarget: target\n      })\n    }\n\n    _clear() {\n      $(this._selector).filter(Selector.ACTIVE).removeClass(ClassName.ACTIVE)\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data) {\n          data = new ScrollSpy(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    const scrollSpys = $.makeArray($(Selector.DATA_SPY))\n\n    for (let i = scrollSpys.length; i--;) {\n      const $spy = $(scrollSpys[i])\n      ScrollSpy._jQueryInterface.call($spy, $spy.data())\n    }\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = ScrollSpy._jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy._jQueryInterface\n  }\n\n  return ScrollSpy\n})($)\n\nexport default ScrollSpy\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tab = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tab'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.tab'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Event = {\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active',\n    DISABLED      : 'disabled',\n    FADE          : 'fade',\n    SHOW          : 'show'\n  }\n\n  const Selector = {\n    DROPDOWN              : '.dropdown',\n    NAV_LIST_GROUP        : '.nav, .list-group',\n    ACTIVE                : '.active',\n    ACTIVE_UL             : '> li > .active',\n    DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n    DROPDOWN_TOGGLE       : '.dropdown-toggle',\n    DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tab {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    show() {\n      if (this._element.parentNode &&\n          this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n          $(this._element).hasClass(ClassName.ACTIVE) ||\n          $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      let target\n      let previous\n      const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n      const selector = Util.getSelectorFromElement(this._element)\n\n      if (listElement) {\n        const itemSelector = listElement.nodeName === 'UL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n        previous = $.makeArray($(listElement).find(itemSelector))\n        previous = previous[previous.length - 1]\n      }\n\n      const hideEvent = $.Event(Event.HIDE, {\n        relatedTarget: this._element\n      })\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget: previous\n      })\n\n      if (previous) {\n        $(previous).trigger(hideEvent)\n      }\n\n      $(this._element).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented() ||\n         hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (selector) {\n        target = $(selector)[0]\n      }\n\n      this._activate(\n        this._element,\n        listElement\n      )\n\n      const complete = () => {\n        const hiddenEvent = $.Event(Event.HIDDEN, {\n          relatedTarget: this._element\n        })\n\n        const shownEvent = $.Event(Event.SHOWN, {\n          relatedTarget: previous\n        })\n\n        $(previous).trigger(hiddenEvent)\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (target) {\n        this._activate(target, target.parentNode, complete)\n      } else {\n        complete()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Private\n\n    _activate(element, container, callback) {\n      let activeElements\n      if (container.nodeName === 'UL') {\n        activeElements = $(container).find(Selector.ACTIVE_UL)\n      } else {\n        activeElements = $(container).children(Selector.ACTIVE)\n      }\n\n      const active = activeElements[0]\n      const isTransitioning = callback &&\n        Util.supportsTransitionEnd() &&\n        (active && $(active).hasClass(ClassName.FADE))\n\n      const complete = () => this._transitionComplete(\n        element,\n        active,\n        callback\n      )\n\n      if (active && isTransitioning) {\n        $(active)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        complete()\n      }\n    }\n\n    _transitionComplete(element, active, callback) {\n      if (active) {\n        $(active).removeClass(`${ClassName.SHOW} ${ClassName.ACTIVE}`)\n\n        const dropdownChild = $(active.parentNode).find(\n          Selector.DROPDOWN_ACTIVE_CHILD\n        )[0]\n\n        if (dropdownChild) {\n          $(dropdownChild).removeClass(ClassName.ACTIVE)\n        }\n\n        if (active.getAttribute('role') === 'tab') {\n          active.setAttribute('aria-selected', false)\n        }\n      }\n\n      $(element).addClass(ClassName.ACTIVE)\n      if (element.getAttribute('role') === 'tab') {\n        element.setAttribute('aria-selected', true)\n      }\n\n      Util.reflow(element)\n      $(element).addClass(ClassName.SHOW)\n\n      if (element.parentNode &&\n          $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n        const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n        if (dropdownElement) {\n          $(dropdownElement).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        }\n\n        element.setAttribute('aria-expanded', true)\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this = $(this)\n        let data = $this.data(DATA_KEY)\n\n        if (!data) {\n          data = new Tab(this)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      Tab._jQueryInterface.call($(this), 'show')\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Tab._jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab._jQueryInterface\n  }\n\n  return Tab\n})($)\n\nexport default Tab\n", "import $ from 'jquery'\nimport Al<PERSON> from './alert'\nimport <PERSON><PERSON> from './button'\nimport Carousel from './carousel'\nimport Collapse from './collapse'\nimport Dropdown from './dropdown'\nimport Modal from './modal'\nimport Popover from './popover'\nimport Scrollspy from './scrollspy'\nimport Tab from './tab'\nimport Tooltip from './tooltip'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n(($) => {\n  if (typeof $ === 'undefined') {\n    throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n  }\n\n  const version = $.fn.jquery.split(' ')[0].split('.')\n  const minMajor = 1\n  const ltMajor = 2\n  const minMinor = 9\n  const minPatch = 1\n  const maxMajor = 4\n\n  if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n    throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n  }\n})($)\n\nexport {\n  Util,\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  Scrollspy,\n  Tab,\n  Tooltip\n}\n"]}