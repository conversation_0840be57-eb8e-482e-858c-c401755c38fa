<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><b><large>Customer : </large></b> <?=$sales_order->customer;?></p>
					<p class="text-left"><b><large>Remarks. : </large></b> <?=$sales_order->remarks;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><b><large>Order Confirmation Date : </large></b> <?=date('m/d/Y', strtotime($sales_order->sales_order_date));?></p>
				</div>
			</div>

        	<h5 class="crud-subtitle"><span>Sales Order Details</span></h5>

			<div class="row sect-jr">
				<div class="col-sm-4">
					<p class="text-left"><b>Total Live Arrived : </b><?=$sales_order->total_live_arrived;?></p>
					<p class="text-left"><b>Total Death on Arrival : </b><?=$sales_order->total_death_on_arrival;?></p>
					<p class="text-left"><b>Total Death After Arrival : </b><?=$sales_order->total_death_after_arrival;?></p>
					<p class="text-left"><b>Total Trips : </b><?=$sales_order->total_trips;?></p>
				</div>

				<div class="col-sm-4">
					<p class="text-left"><b>Heads : </b><?=$sales_order->total_live_arrived_heads;?></p>
					<p class="text-left"><b>Heads : </b><?=$sales_order->total_death_on_arrival_heads;?></p>
					<p class="text-left"><b>Heads : </b><?=$sales_order->total_death_after_arrival_heads;?></p>
					<p class="text-left"><b>ALW : </b><?=$sales_order->alw;?></p>
				</div>

				<div class="col-sm-4">
					<p class="text-left"><b>Kilos : </b><?=$sales_order->total_live_arrived_kilos;?></p>
					<p class="text-left"><b>Kilos : </b><?=$sales_order->total_death_on_arrival_kilos;?></p>
					<p class="text-left"><b>Kilos : </b><?=$sales_order->total_death_after_arrival_kilos;?></p>
					<p class="text-left"><b>Coops Loaded : </b><?=$sales_order->coops_loaded;?></p>
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>List of Sales Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Qty</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_actual_live_weight = 0;
								if (!empty($sales_order_item_ids)):
                                	$counter = 0;
                                    foreach ($sales_order_item_ids as $i => $sales_order_item_id):
										$total_actual_live_weight += $retain_items[$counter]['actual_live_weight'];
							?>
										<tr class="sales_order_item">
											<td><?=$retain_items[$counter]['item_name'];?></td>

											<td><?=$retain_items[$counter]['unit'];?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['actual_live_weight']);?></td>
										</tr>
							<?php
									$counter++;
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
									<tr class="order_confirmation_item">
										<td></td>

										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_actual_live_weight);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Payments</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Payment Date</th>

									<th class="text-center" style="width: 15%;">Paid Amount</th>

									<th class="text-center" style="width: 15%;">Prepared By</th>

									<th class="text-center" style="width: 15%;">Prepared On</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_payment = 0;
								if (!empty($sales_order_payments)):
									foreach ($sales_order_payments as $sales_order_payment):
										$total_payment += $sales_order_payment->amount;
							?>
										<tr class="sales_order_payment">
											<td><?=$sales_order_payment->payment_date;?></td>

											<td><?=$sales_order_payment->amount;?></td>

											<td class="text-right"><?=$sales_order_payment->issuer?></td>

											<td class="text-right"><?=$sales_order_payment->added_on?></td>
										</tr>
							<?php
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
									<tr class="sales_order_item">
										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_payment);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($sales_order->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>sales_order Attachments</span></h5>
						<?php $attachments = explode('🔥',$sales_order->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/sales_orders/'.$sales_order->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$sales_order->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Order Confirmed By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('sales_orders');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="sales_order_id" value="<?=$sales_order->id?>">
		</form>
	</div>
</div>