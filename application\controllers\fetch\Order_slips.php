<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Order_slips extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('order_slip');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch order_slips
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'order_slip';
        $primary_key = 'order_slip.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_date(),
            $this->_get_order_slip_number(),
            $this->_get_customer(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_total(),
            $this->_get_balance(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
order_slip
LEFT JOIN customer ON customer.id = order_slip.customer_id
LEFT JOIN user ON user.id = order_slip.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        $where = <<<EOT
order_slip.is_deleted = 0
EOT;

        if ($status !== 'all') {
            $where .= <<<EOT

AND order_slip.status = "$status"
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'order_slip.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'order_slip.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'order_slip';
            }
        ];
    }

    /**
     * Get order_slip date
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'order_slip.id',
            'as' => 'order_slip_number',
            'dt' => 0,
            'field' => 'order_slip_number'
        ];
    }

    /**
     * Get order_slip date
     */
    protected function _get_order_slip_date()
    {
        return [
            'db' => 'DATE_FORMAT(order_slip.order_slip_date, "%b %d, %Y")',
            'as' => 'order_slip_date',
            'dt' => 1,
            'field' => 'order_slip_date'
        ];
    }

    /**
     * Get order_slip customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 2,
            'field' => 'customer'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'order_slip.remarks',
            'dt' => 3,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(order_slip.is_deleted = 0, order_slip.status, "cancelled")',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get Total
     */
    protected function _get_total()
    {
        return [
            'db' => 'order_slip.grand_total',
            'dt' => 4,
            'field' => 'grand_total',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Balance
     */
    protected function _get_balance()
    {
        return [
            'db' => 'order_slip.paid_amount',
            'dt' => 5,
            'field' => 'paid_amount',
            'formatter' => function ($d, $row) {
                $grandTotal = $row['grand_total'];
                $balance = $grandTotal - $d;
                return number_format($balance, 2);
            }
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 6,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'order_slip.id',
            'as' => 'file',
            'dt' => 7,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->order_slip->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/order_slips/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'order_slip.id',
            'as' => 'actions',
            'dt' => 7,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        // $res .= anchor('sales_orders/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        $res .= anchor('purchase_orders/add/' . $d, 'Proceed to PO', 'title="Proceed to PO" class="dropdown-item text-left green-text"');
                        $res .= anchor('order_slips/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('order_slips/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } else {
                        $res .= anchor('order_slips/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
