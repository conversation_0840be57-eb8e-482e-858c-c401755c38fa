<?php
class Item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'item';
        parent::__construct();
    }

    /**
     * Create New Item
     */
    public function generate($name, $unit, $price, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO item(name, unit, price, added_by, added_on) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    unit = VALUES(unit),
    price = VALUES(price),
    is_deleted = 0
EOT;
        $binds = [$name, $unit, $price, $added_by, $added_on];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }

    public function get_by_id($item_id, $customer_id = null)
    {
        $sql = <<<EOT
SELECT item.*, IFNULL(customer_prices.customer_price, item.price) AS price
FROM item
LEFT JOIN customer_prices ON customer_prices.item_id = item.id AND customer_prices.is_deleted = 0
WHERE item.id = ?
 AND item.is_deleted = 0
EOT;
        $binds = [$item_id];

        if($customer_id !== null){
        $sql .= <<<EOT

AND customer_prices.customer_id = ?
EOT;

        $binds[] = $customer_id;

        }

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */