!function(t){"use strict";function e(e){var a=t(e),n=a.find("thead th"),r=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-right",targets:n.length-3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(e){var a=t(n());r(a,e);var c=t(".item-table"),i=c.find(".purchase_item");0===i.length&&c.find(".no-item").remove(),c.find("tbody").append(a)}function n(){var t=c();return'<tr class="purchase_item"><td><input type="hidden" name="purchase_item_'+t+'" value="'+t+'"><input type="hidden" name="status_'+t+'" value="pending"><input type="text" name="item_'+t+'" class="form-control go-suggest" value="" data-suggest="items"></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units"></td><td><input type="number" name="price_'+t+'" class="form-control" value="" step="any"></td><td><input type="number" name="amount_'+t+'" class="form-control-plaintext" value="" step="any" readonly></td><td><select name="account_type_'+t+'" class="dropsearch form-control" data-placeholder="" style="width:100%;"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function r(e,a){e.find('select[name^="account_type"]').select2({data:a,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%",sorter:function(t){return t.sort(function(t,e){return t.text.localeCompare(e.text)})}})}function c(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(){return'<tr class="no-item"><td class="text-center" colspan="6">No items added yet.</td></tr>'}function u(t){var e=parseFloat(t.find('input[name^="qty"]').val());isNaN(e)&&(e=0);var a=parseFloat(t.find('input[name^="price"]').val());isNaN(a)&&(a=0);var n=a*e;return t.find('input[name^="amount"]').val(n.toFixed(2)),n}function o(){var e=0;t(".item-table").find(".purchase_item").each(function(a,n){e+=u(t(n))}),t(".total-amount").text(e.toFixed(2))}function s(e){t(e.currentTarget).closest(".purchase_item").remove();var a=t(e.delegateTarget),n=a.find(".purchase_item");if(n.length<1){var r=t(i());a.find("tbody").html(r)}o()}function l(e){var a=t('select[name="sub_usage"]');a.find("option").each(function(t,a){var n=a.text;n&&e.includes(n)?a.disabled=!1:a.disabled=!0}),a.select2();var n=a.attr("data-default");a.attr("data-default",""),a.val(n).trigger("change")}function d(e,a){return t.ajax({url:BASE_URI+"purchases/delete_attachment/"+e,type:"POST",data:{file_name:a},dataType:"json"})}function m(){return t.ajax({url:BASE_URI+"account_types/all/purchase_order",type:"POST",dataType:"json"})}t(function(){var n=[{id:"",text:""}];t.when(m()).then(function(t){t.account_types.forEach(function(t){t.text=t.account_name,t.id=t.account_name,n.push(t)})}),t(".action-add-item").click(function(){a(n)});var r=e(".tableA");t('input[name="main_usage"], select[name="main_usage"]').change(function(t){var e=BASE_URI+"fetch/purchases_by_usage?usage="+t.currentTarget.value;r.ajax.url(e).load()}).trigger("change"),t('select[name="main_usage"]').change(function(e){t.ajax({url:BASE_URI+"sub_usages/subs_of?usage="+encodeURIComponent(e.currentTarget.value),type:"POST",dataType:"json"}).done(function(t){var e=[];Array.isArray(t.sub_usages)&&(e=t.sub_usages),l(e)}).fail(function(){l([])})}),t(".item-table").on("click",".action-delete-item",function(t){s(t)}).on("change",'input[name^="qty"]',o).on("change",'input[name^="price"]',o),o(),t(".attachment-table").on("click",".action-delete-attachment",function(e){var a=t(e.currentTarget).closest("tr.attachment").find("td"),n=void 0!==a[0]?a[0].innerHTML:"";let r=t(this);alertify.confirm("Are you sure you want to delete "+n+"?",function(){var a=e.currentTarget.getAttribute("data-id"),n=e.currentTarget.getAttribute("data-filename");t.when(d(a,n)).then(function(t){console.log(r.html()),r.parent().parent().remove()})}).setHeader("<em>Delete Attachment</em>")}),t(".toggle-usage-mode").click(function(e){var a=t(e.currentTarget).closest(".usage-mode"),n=a.siblings(".usage-mode");a.addClass("d-none"),a.find("input,select").attr("disabled",!0).trigger("change"),n.removeClass("d-none"),n.find("input,select").attr("disabled",!1).trigger("change")})})}(jQuery);