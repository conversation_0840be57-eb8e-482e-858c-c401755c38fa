<?php
class Purchase extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'purchase';
        parent::__construct();
    }

    /**
     * Get Purchase Order By ID
     */
    public function get_by_id($receive_id)
    {
        $sql = <<<EOT
SELECT purchase.*,
    purchase.requisitioner AS requisitioner,
    CONCAT(author.full_name, "<br>", DATE_FORMAT(purchase.added_on, "%b %d, %Y %h:%i %p")) AS author,
    CONCAT(recommender.full_name, "<br>", DATE_FORMAT(recommended_on.added_on, "%b %d, %Y %h:%i %p")) AS recommender,
    CONCAT(approver.full_name, "<br>", DATE_FORMAT(approved_on.added_on, "%b %d, %Y %h:%i %p")) AS approved_on,
    supplier.name AS supplier,
    approver.sig_url AS approver_sig_url,
    approver.id AS approver_id,
    author.sig_url AS author_sig_url,
    author.id AS author_id,
    recommender.sig_url AS recommender_sig_url,
    recommender.id AS recommender_id
FROM purchase
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN user AS author ON author.id = purchase.added_by
LEFT JOIN user AS recommender ON recommender.id = purchase.recommended_by
LEFT JOIN user AS approver ON approver.id = purchase.approved_by
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND trail.col_name = "status"
    AND trail.new_value = "for approval"
) as recommended_on ON recommended_on.source_id = purchase.id
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND trail.col_name = "status"
    AND trail.new_value = "approved"
) as approved_on ON approved_on.source_id = purchase.id
WHERE purchase.id = ?
    AND purchase.is_deleted = 0
EOT;

        $binds = [$receive_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Grand Total
     */
    public function get_total($purchase_id)
    {
        $sql = <<<EOT
SELECT IF(purchase.status = "pending", purchase.total, IFNULL(SUM(purchase_item.amount), 0)) AS total
FROM purchase
LEFT JOIN purchase_item ON purchase_item.purchase_id = purchase.id
WHERE purchase.id = ?
    AND purchase_item.status = "approved"
    AND purchase_item.is_deleted = 0
GROUP BY purchase.id
EOT;
        $binds = [$purchase_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0]->total;
        } else {
            return 0;
        }
    }

    /**
     * Get Added Checks
     */
    public function get_added_checks($receive_id)
    {
        $sql = <<<EOT
SELECT check_invoice.*, CONCAT(bank.name, " - ", check_slip.check_no) AS check_no
FROM check_invoice
LEFT JOIN check_slip ON check_slip.id = check_invoice.check_id
LEFT JOIN bank ON bank.id = check_slip.bank_id
WHERE check_invoice.receive_id = ?
    AND check_invoice.is_deleted = 0
    AND check_slip.is_deleted = 0
EOT;
        $binds = [$receive_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Check Nos of purchase
     */
    public function get_check_no($receive_id)
    {
        $sql = <<<EOT
SELECT CONCAT(bank.name, " - ", check_slip.check_no) AS check_no, check_invoice.check_id, check_invoice.receive_id
FROM purchase
LEFT JOIN check_invoice ON check_invoice.receive_id = purchase.id
LEFT JOIN check_slip ON check_slip.id = check_invoice.check_id
LEFT JOIN bank ON bank.id = check_slip.bank_id
WHERE purchase.id = ?
    AND purchase.is_deleted = 0
    AND check_slip.is_deleted = 0
    AND check_invoice.is_deleted = 0
EOT;
        $binds = [$receive_id];

        if (!in_array($_SESSION['user']->role_id, ['1', '5'])) {
            $sql .= <<<EOT

    AND purchase.added_by = ?
EOT;
            $binds[] = $_SESSION['user']->id;
        }

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Paid Amount of Purchase Order
     */
    public function get_paid_amount($receive_id)
    {
        $sql = <<<EOT
SELECT IFNULL(SUM(check_invoice.amount), 0) AS paid_amount
FROM check_invoice
LEFT JOIN check_slip ON check_slip.id = check_invoice.check_id
LEFT JOIN purchase ON purchase.id = check_invoice.receive_id
WHERE check_invoice.receive_id = ?
    AND check_slip.is_deleted = 0
    AND check_invoice.is_deleted = 0
EOT;
        $binds = [$receive_id];

        if (!in_array($_SESSION['user']->role_id, ['1', '5'])) {
            $sql .= <<<EOT

    AND purchase.added_by = ?
EOT;
            $binds[] = $_SESSION['user']->id;
        }

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0]->paid_amount;
        } else {
            return 0;
        }
    }
    /**
     * Get Count Urgent
     */
    public function get_count_urgent($supplier_id = null)
    {
        $sql = <<<EOT
SELECT count(is_urgent) as urgent
FROM purchase
WHERE is_urgent = 1
EOT;
        // $binds = [$receive_id];

        if ($res = $this->advanced_query($sql)) {
            return $res[0]->urgent;
        } else {
            return 0;
        }
    }

    /**
     * Get Approved POs
     */
    public function get_approved($supplier_id = null)
    {
        $sql = <<<EOT
SELECT purchase.*,
    IF(purchase.status = "pending", purchase.total, IFNULL(purchase_item.total, 0)) AS total,
    IFNULL(check_invoice.paid_amount, 0) AS paid_amount
FROM purchase
LEFT JOIN (
    SELECT receive_id, SUM(amount) AS total
    FROM purchase_item
    WHERE status = "approved"
        AND is_deleted = 0
    GROUP BY receive_id
) purchase_item ON purchase_item.receive_id = purchase.id
LEFT JOIN (
    SELECT check_invoice.receive_id, SUM(check_invoice.amount) AS paid_amount
    FROM check_invoice
    LEFT JOIN check_slip ON check_slip.id = check_invoice.check_id
    WHERE check_slip.is_deleted = 0
        AND check_invoice.is_deleted = 0
    GROUP BY check_invoice.receive_id
) check_invoice ON check_invoice.receive_id = purchase.id
WHERE purchase.status IN("approved", "printed")
    AND purchase.is_deleted = 0
EOT;
        $binds = [];

        if ($supplier_id !== null) {
            $sql .= <<<EOT

    AND purchase.supplier_id = ?
EOT;
            $binds[] = $supplier_id;
        }

        return $this->advanced_query($sql, $binds);
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM purchase
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Total Expense
     */
    public function get_total_monthly_expenses($month = null, $year = null)
    {
        if(!empty($month) && !empty($year)){
            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_expenses
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase_item.is_deleted = 0
    AND purchase.is_deleted = 0
    AND purchase.status IN ('approved','printed','for release','released','audited')
    AND purchase_item.status = "approved"
    AND MONTH(purchase.purchase_date) = ?
    AND YEAR(purchase.purchase_date) = ?
EOT;
            $binds = [$month, $year];
        } elseif(empty($month) && !empty($year)) {
            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_expenses
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase_item.is_deleted = 0
    AND purchase.is_deleted = 0
    AND purchase.status IN ('approved','printed','for release','released','audited')
    AND purchase_item.status = "approved"
    AND YEAR(purchase.purchase_date) = ?
EOT;
            $binds = [$year];
        } else {
            $firstDayOfMonth = date('Y-m-01');
            $date_today = date('Y-m-d');

            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_expenses
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase_item.is_deleted = 0
    AND purchase.is_deleted = 0
    AND purchase.status IN ('approved','printed','for release','released','audited')
    AND purchase_item.status = "approved"
    AND purchase.purchase_date BETWEEN ? AND ?
EOT;
            $binds = [$firstDayOfMonth, $date_today];
        }

        
        $res = $this->advanced_query($sql, $binds);

        if(!empty($res[0])){
            return $res[0]->total_monthly_expenses;
        }else{
            return 0;
        }
    }

    /**
     * Get Total Monthly Capex
     */
    public function get_total_monthly_capex($month = null, $year = null)
    {
        if(!empty($month) && !empty($year)){
            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_capex
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase.is_deleted = 0
    AND purchase.status <> 'pending'
    AND purchase_item.is_deleted = 0
    AND purchase_item.account_type = 'CAPEX Purchases'
    AND MONTH(purchase.purchase_date) = ?
    AND YEAR(purchase.purchase_date) = ?
EOT;
            $binds = [$month, $year];
        } elseif(empty($month) && !empty($year)) {
            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_capex
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase.is_deleted = 0
    AND purchase.status <> 'pending'
    AND purchase_item.is_deleted = 0
    AND purchase_item.account_type = 'CAPEX Purchases'
    AND YEAR(purchase.purchase_date) = ?
EOT;
            $binds = [$year];
        } else {
            $firstDayOfMonth = date('Y-m-01');
            $date_today = date('Y-m-d');

            $sql = <<<EOT
SELECT SUM(purchase_item.amount) AS total_monthly_capex
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase.is_deleted = 0
    AND purchase.status <> 'pending'
    AND purchase_item.is_deleted = 0
    AND purchase_item.account_type = 'CAPEX Purchases'
    AND purchase.purchase_date BETWEEN ? AND ?
EOT;
            $binds = [$firstDayOfMonth, $date_today];
        }

        
        $res = $this->advanced_query($sql, $binds);

        if(!empty($res[0])){
            return $res[0]->total_monthly_capex;
        }else{
            return 0;
        }
    }

    /**
     * Get Total Expense
     */
    public function export_excel($status = null, $item_name = null, $date_from = null, $date_to = null)
    {
        $binds = [];
        $sql = <<<EOT
SELECT 
    purchase.id AS doc_no, 
    purchase_item.items AS item_name, 
    supplier.name AS supplier, 
    purchase.purchase_date, 
    purchase.plate_no, 
    purchase.total, 
    IFNULL(purchase.requisitioner, author.full_name) AS requisitioner, 
    CONCAT(author.full_name, " - ", DATE_FORMAT(purchase.added_on, "%b %d, %Y %h:%i %p")) AS prepared_by, 
    CONCAT(recommender.full_name, " - ", DATE_FORMAT(recommended_on.added_on, "%b %d, %Y %h:%i %p")) AS recommended_by, 
    CONCAT(approver.full_name, " - ", DATE_FORMAT(approved_on.added_on, "%b %d, %Y %h:%i %p")) AS approved_by, 
    CONCAT(printer.full_name, " - ", DATE_FORMAT(printed_on.added_on, "%b %d, %Y %h:%i %p")) AS printed_by
FROM purchase
LEFT JOIN (
    SELECT purchase_id, GROUP_CONCAT(CONCAT('• ',item.name) SEPARATOR '<br>') AS items
    FROM purchase_item
    LEFT JOIN item ON item.id = purchase_item.item_id
    WHERE purchase_item.is_deleted = 0
    GROUP BY purchase_id
) purchase_item ON purchase_item.purchase_id = purchase.id
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN user AS author ON author.id = purchase.added_by
LEFT JOIN user AS recommender ON recommender.id = purchase.recommended_by
LEFT JOIN user AS approver ON approver.id = purchase.approved_by
LEFT JOIN user AS printer ON printer.id = purchase.printed_by
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "for approval"
) as recommended_on ON recommended_on.source_id = purchase.id
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "approved"
) as approved_on ON approved_on.source_id = purchase.id
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "printed"
) as printed_on ON printed_on.source_id = purchase.id
LEFT JOIN (
    SELECT DISTINCT item_id, purchase_id
    FROM purchase_item
    WHERE is_deleted = 0
) item ON item.purchase_id = purchase.id
EOT;

        if(!empty($status)){
            switch($status){
                case 'deleted':
                    $sql .= <<<EOT

WHERE purchase.is_deleted = 1
EOT;
                    break;

                case 'urgent':
                    $sql .= <<<EOT

WHERE purchase.is_deleted = 0
AND purchase.is_urgent = 1
AND purchase.status != 'audited'
EOT;
                    break;

                case 'incomplete audited':
                    $sql .= <<<EOT

WHERE purchase.is_deleted = 0
AND purchase.audit_status = 'incomplete'
EOT;
                    break;

                case 'complete audited':
                    $sql .= <<<EOT

WHERE purchase.is_deleted = 0
AND purchase.audit_status = 'complete'
EOT;
                    break;

                case 'for approval':
                    $sql .= <<<EOT

WHERE purchase.is_deleted = 0
AND purchase.is_verified = 0
AND purchase.printed_by > 0
EOT;
                    break;

                case 'all':
                    $sql .= <<<EOT

WHERE 1
EOT;
                    break;

                default:
                    $sql .= <<<EOT

WHERE purchase.status = ?
AND purchase.is_deleted = 0
EOT;
                    $binds[] = $status;
                    break;
            }
        }

        if(!empty($item_name)){
            $sql .= <<<EOT

AND (
    purchase_item.items LIKE ?
    OR supplier.name LIKE ?
    OR purchase.id LIKE ?
    OR purchase.plate_no LIKE ?
)
EOT;
            $binds[] = '%'. $item_name . '%';
            $binds[] = '%'. $item_name . '%';
            $binds[] = '%'. $item_name . '%';
            $binds[] = '%'. $item_name . '%';
        }

        if(!empty($date_from) && !empty($date_to)){
            $sql .= <<<EOT

AND purchase.purchase_date BETWEEN ? AND ?
EOT;
            $binds[] = $date_from;
            $binds[] = $date_to;
        } elseif (empty($date_from) && !empty($date_to)) {
            $sql .= <<<EOT

AND purchase.purchase_date <= ?
EOT;
            $binds[] = $date_to;
        } elseif (!empty($date_from) && empty($date_to)) {
            $sql .= <<<EOT

AND purchase.purchase_date >= ?
EOT;
            $binds[] = $date_from;
        }

        $sql .= <<<EOT

GROUP BY purchase.id
EOT;
        
        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)){
            return $res;
        }else{
            return false;
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
