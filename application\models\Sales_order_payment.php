<?php
class Sales_order_payment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'sales_order_payment';
        parent::__construct();
    }

    /**
     * Get sales_order_payment By ID
     */
    public function get_by_id($sales_order_payment_id)
    {
        $sql = <<<EOT
SELECT sales_order_payment.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE sales_order_payment.id = ?
    AND sales_order_payment.is_deleted = 0
EOT;
        $binds = [$sales_order_payment_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get sales_order_payment By ID
     */
    public function get_by_sales_order($sales_order_id)
    {
        $sql = <<<EOT
SELECT sales_order_payment.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE sales_order_payment.sales_order_id = ?
    AND sales_order_payment.is_deleted = 0
EOT;
        $binds = [$sales_order_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM sales_order_payment
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid sales_order_payments
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT sales_order_payment.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE sales_order_payment.paid_amount < sales_order_payment.total
    AND sales_order_payment.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get sales_order_payments Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT sales_order_payment.id, sales_order_payment.sales_order_payment_date, sales_order_payment.customer_id, sales_order_payment.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN (
    SELECT payment.payment_date, paid_sales_order_payment.sales_order_payment_id, paid_sales_order_payment.amount
    FROM payment
    LEFT JOIN paid_sales_order_payment ON paid_sales_order_payment.payment_id = payment.id
    LEFT JOIN sales_order_payment ON sales_order_payment.id = paid_sales_order_payment.sales_order_payment_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND sales_order_payment.customer_id = ?
) payment ON payment.sales_order_payment_id = sales_order_payment.id
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE sales_order_payment.sales_order_payment_date < ?
    AND customer.id = ?
    AND sales_order_payment.is_deleted = 0
GROUP BY sales_order_payment.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get sales_order_payments Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT sales_order_payment.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE sales_order_payment.sales_order_payment_date BETWEEN ? AND ?
    AND sales_order_payment.customer_id = ?
    AND sales_order_payment.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get sales_order_payments BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT sales_order_payment.id, sales_order_payment.sales_order_payment_no, sales_order_payment.terms, sales_order_payment.sales_order_payment_date, sales_order_payment.customer_id, sales_order_payment.total AS charge, sales_order_payment.total, sales_order_payment.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM sales_order_payment
LEFT JOIN (
    SELECT payment.payment_date, paid_sales_order_payment.sales_order_payment_id, paid_sales_order_payment.amount
    FROM payment
    LEFT JOIN paid_sales_order_payment ON paid_sales_order_payment.payment_id = payment.id
    LEFT JOIN sales_order_payment ON sales_order_payment.id = paid_sales_order_payment.sales_order_payment_id
    WHERE payment.payment_date
        AND sales_order_payment.customer_id = ?
) payment ON payment.sales_order_payment_id = sales_order_payment.id
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order_payment.customer_id
LEFT JOIN user AS issuer ON issuer.id = sales_order_payment.added_by
WHERE customer.id = ?
    AND sales_order_payment.is_deleted = 0
GROUP BY sales_order_payment.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */