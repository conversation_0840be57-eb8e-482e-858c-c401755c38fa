<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Expenses extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Purchase Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase';
        $primary_key = 'purchase.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_doc_no(),
            $this->_get_supplier(),
            $this->_get_purchase_date(),
            $this->_get_main_usage(),
            $this->_get_sub_usage(),
            $this->_get_items(),
            $this->_get_requisitioner(),
            $this->_get_approved_by(),
            $this->_get_printed_by(),
            $this->_get_amount()
        ];


        $joins = <<<EOT
purchase
LEFT JOIN purchase_item ON purchase_item.purchase_id = purchase.id
    AND purchase_item.status = 'approved'
    AND purchase_item.is_deleted = 0
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN user AS approver ON approver.id = purchase.approved_by
LEFT JOIN user AS printer ON printer.id = purchase.printed_by
EOT;

        $where = <<<EOT
purchase.is_deleted = 0
AND purchase.audit_status = 'complete'
EOT;

        if ($supplier_id = addslashes($this->input->get('supplier', true))  and $supplier_id !== 'All') {
            $where .= <<<EOT

    AND purchase.supplier_id = {$supplier_id}
EOT;
        }

        if ($main_usage = addslashes($this->input->get('main_usage', true))) {
            $where .= <<<EOT

    AND purchase.main_usage = "{$main_usage}"
EOT;
        }

        if ($sub_usage = addslashes($this->input->get('sub_usage', true))) {
            $where .= <<<EOT

    AND purchase.sub_usage = "{$sub_usage}"
EOT;
        }

        if ($from_date = date("Y-m-d", strtotime(urldecode($this->input->get('from', true)))) and
            $to_date = date("Y-m-d", strtotime(urldecode($this->input->get('to', true))))
        ) {
            $where .= <<<EOT

    AND purchase.purchase_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        //$filters = [
            //empty($supplier_id) ? 'purchase.supplier_id' : null,
            //empty($main_usage) ? 'purchase.main_usage' : null
            //empty($main_usage) ? 'purchase.id' : null
        //];
        //$group_by = implode(', ', array_filter($filters));
        $group_by = "purchase.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
            //SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase';
            }
        ];
    }

    /**
     * Get PO document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get PO supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 1,
            'field' => 'supplier'
        ];
    }


    /**
     * Get PO Date
     */
    protected function _get_purchase_date()
    {
        return [
            'db' => 'DATE_FORMAT(purchase.purchase_date, "%b %d, %Y")',
            'as' => 'purchase_date',
            'dt' => 2,
            'field' => 'purchase_date'
        ];
    }

    /**
     * Get main usage
     */
    protected function _get_main_usage()
    {
        return [
            'db' => 'purchase.main_usage',
            'dt' => 3,
            'field' => 'main_usage'
        ];
    }

    /**
     * Get sub usage
     */
    protected function _get_sub_usage()
    {
        return [
            'db' => 'purchase.sub_usage',
            'dt' => 4,
            'field' => 'sub_usage'
        ];
    }
    /**
     * Get Items
     */
    protected function _get_items()
    {
        return [
            'db' => '(
                SELECT GROUP_CONCAT(CONCAT(purchase_item.qty, " ", purchase_item.unit, " ", item.name) SEPARATOR "🔥")
                FROM purchase_item
                LEFT JOIN item ON item.id = purchase_item.item_id
                WHERE purchase_item.status = "approved"
                    AND purchase_item.purchase_id = purchase.id
                GROUP BY purchase_item.purchase_id
            )',
            'as' => 'items',
            'dt' => 5,
            'field' => 'items',
            'formatter' => function ($d, $row) {
                $res = '<ul>';

                $items = explode('🔥', $d);
                $items = array_map('trim', $items);
                $items=  array_filter($items);

                foreach ($items as $item) {
                    $res .= '<li>' . $item . '</li>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get Requisitioner
     */
    protected function _get_requisitioner()
    {
        return [
            'db' => 'purchase.requisitioner',
            'as' => 'requisitioner',
            'dt' => 6,
            'field' => 'requisitioner'
        ];
    }

    /**
     * Get Approved By
     */
    protected function _get_approved_by()
    {
        return [
            'db' => 'approver.full_name',
            'as' => 'approved_by',
            'dt' => 7,
            'field' => 'approved_by'
        ];
    }

    /**
     * Get Printed By
     */
    protected function _get_printed_by()
    {
        return [
            'db' => 'printer.full_name',
            'as' => 'printed_by',
            'dt' => 8,
            'field' => 'printed_by'
        ];
    }


    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'FORMAT(IFNULL(SUM(IF(purchase_item.status = "approved",purchase_item.amount,0)), 0), 2)',
            'as' => 'total',
            'dt' => 9,
            'field' => 'total'
        ];
    }

}
