<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Giblets extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Giblets
     */
    public function index()
    {
        // Get request parameters
        $draw = intval($this->input->get_post('draw'));
        $start = intval($this->input->get_post('start'));
        $length = intval($this->input->get_post('length'));
        $search_value = $this->input->get_post('search')['value'];

        // Get all giblets records
        $this->db->select('
            giblets.id as giblets_id,
            giblets.sku,
            giblets.size,
            giblets.range,
            giblets.no,
            giblets.pcs,
            giblets.kgs,
            giblets.added_on,
            giblets.added_by
        ');
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        $this->db->order_by('giblets.added_on DESC');

        // Apply search filter
        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('giblets.sku', $search_value);
            $this->db->or_like('giblets.size', $search_value);
            $this->db->or_like('giblets.range', $search_value);
            $this->db->group_end();
        }

        // Get total count before pagination
        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results();

        // Apply pagination - if length is -1, return all records
        if ($length > 0) {
            $this->db->limit($length, $start);
        }

        // Execute query
        $query = $this->db->get();
        $data = [];

        if ($query->num_rows() > 0) {
            $grouped_data = [];
            $sku_order = []; // Track the order SKUs first appear

            // Group by SKU while preserving order
            foreach ($query->result() as $row) {
                $sku_key = $row->sku;

                if (!isset($grouped_data[$sku_key])) {
                    $grouped_data[$sku_key] = [];
                    $sku_order[] = $sku_key; // Remember the order
                }

                $grouped_data[$sku_key][] = $row;
            }

            // Process each SKU group in the order they first appeared
            foreach ($sku_order as $sku_key) {
                $records = $grouped_data[$sku_key];

                // Sort records within each SKU group by submission order (added_on, added_by, id)
                usort($records, function($a, $b) {
                    $time_diff = strtotime($a->added_on) - strtotime($b->added_on);
                    if ($time_diff != 0) return $time_diff;

                    $user_diff = $a->added_by - $b->added_by;
                    if ($user_diff != 0) return $user_diff;

                    return $a->giblets_id - $b->giblets_id;
                });

                $first_record = $records[0];

                // First row: Show SKU and first record's data
                $data[] = [
                    'DT_RowId' => 'giblets_' . $first_record->giblets_id,
                    'DT_RowClass' => 'giblets',
                    'sku' => $first_record->sku,
                    'size' => $first_record->size,
                    'range' => $first_record->range,
                    'no' => $first_record->no,
                    'pcs' => $first_record->pcs,
                    'kgs' => $first_record->kgs,
                    'actions' => $this->_generate_actions($first_record->giblets_id)
                ];

                // Additional rows: Show only Size, Range, NO, PCS, KGS (empty SKU)
                for ($i = 1; $i < count($records); $i++) {
                    $record = $records[$i];
                    $data[] = [
                        'DT_RowId' => 'giblets_' . $record->giblets_id,
                        'DT_RowClass' => 'giblets',
                        'sku' => '', // Empty SKU for additional rows
                        'size' => $record->size,
                        'range' => $record->range,
                        'no' => $record->no,
                        'pcs' => $record->pcs,
                        'kgs' => $record->kgs,
                        'actions' => $this->_generate_actions($record->giblets_id)
                    ];
                }
            }
        }

        // Return DataTables response
        $response = [
            'draw' => $draw,
            'recordsTotal' => $this->_get_total_giblets(),
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        exit(json_encode($response));
    }

    /**
     * Generate actions for a giblets record
     */
    protected function _generate_actions($giblets_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])) {
            $res .= anchor('Giblets/edit/' . $giblets_id, 'Edit', 'title="Edit" class="dropdown-item text-left"');
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $giblets_id . '">Delete</button>';
        }

        $res .= '</div></div>';
        return $res;
    }

    /**
     * Get total giblets records
     */
    protected function _get_total_giblets()
    {
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        return $this->db->count_all_results();
    }
}
