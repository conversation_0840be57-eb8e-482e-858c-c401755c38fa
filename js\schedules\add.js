+function ($) {
	'use strict';

	// ========================
	// Row Management
	// ========================

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	function itemRow() {
		var rowId = makeRandomId();
		return `
			<tr class="trip_item">
				<td>
					<input type="hidden" name="trip_item_${rowId}" value="${rowId}">
					<input type="number" name="trip_no_${rowId}" class="form-control-plaintext" value="" readonly>
				</td>
				<td>
					<input type="time" name="eta_time_${rowId}" class="form-control" value="">
				</td>
				<td class="text-center">
					<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
				</td>
			</tr>`;
	}

	function emptyRow() {
		return `<tr class="no-item">
			<td class="text-center" colspan="3">No trips added yet.</td>
		</tr>`;
	}

	function addItemRow() {
		var $newRow = $(itemRow());
		var $itemTable = $('.item-table');
		var tripItems = $itemTable.find('.trip_item');

		if (tripItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}

		$itemTable.find('tbody').append($newRow);
		setRowNumbers();
	}

	function setRowNumbers() {
		var customerId = $('select[name="customer"]').val();
		var etaDate = $('input[name="eta_date"]').val();

		if (customerId && etaDate) {
			$.ajax({
				url: BASE_URI + 'schedules/get_next_trip_number',
				type: 'POST',
				data: {
					customer_id: customerId,
					eta_date: etaDate
				},
				dataType: 'json',
				success: function(response) {
					if (response.next_trip_no) {
						var $itemTable = $('.item-table');
						var tripItems = $itemTable.find('.trip_item');
						var startingTripNo = response.next_trip_no;

						tripItems.each(function (index, row) {
							$(row).find('input[name^="trip_no_"]').val(startingTripNo + index);
						});
					}
				},
				error: function() {
					var $itemTable = $('.item-table');
					var tripItems = $itemTable.find('.trip_item');

					tripItems.each(function (index, row) {
						$(row).find('input[name^="trip_no_"]').val(index + 1);
					});
				}
			});
		} else {
			var $itemTable = $('.item-table');
			var tripItems = $itemTable.find('.trip_item');

			tripItems.each(function (index, row) {
				$(row).find('input[name^="trip_no_"]').val(index + 1);
			});
		}
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.trip_item').remove();

		var $itemTable = $(event.delegateTarget);
		var tripItems = $itemTable.find('.trip_item');

		if (tripItems.length < 1) {
			$itemTable.find('tbody').html($(emptyRow()));
		}

		setRowNumbers();
	}

	function checkDuplicateTripTimes() {
		var etaTimes = [];
		var hasDuplicates = false;

		$('.item-table input[name^="eta_time_"]').each(function () {
			if ($(this).val()) etaTimes.push($(this).val());
		});

		var uniqueTimes = [...new Set(etaTimes)];
		if (uniqueTimes.length !== etaTimes.length) {
			hasDuplicates = true;
		}

		$('.duplicate-time-error').remove();
		$('.item-table input[name^="eta_time_"]').removeClass('is-invalid');

		if (hasDuplicates) {
			var errorMsg = $(`<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">
				Duplicate ETA times are not allowed. Each trip must have a unique time.
			</div>`);
			$('.action-add-item').closest('.form-group').append(errorMsg);

			var timeCounts = {};
			etaTimes.forEach(time => timeCounts[time] = (timeCounts[time] || 0) + 1);

			$('.item-table input[name^="eta_time_"]').each(function () {
				if (timeCounts[$(this).val()] > 1) {
					$(this).addClass('is-invalid');
				}
			});
		}
	}

	function checkDuplicateSchedule() {
		var customerId = $('select[name="customer"]').val();
		var etaDate = $('input[name="eta_date"]').val();

		if (customerId && etaDate) {
			$.ajax({
				url: BASE_URI + 'schedules/check_duplicate_schedule',
				type: 'POST',
				data: { customer_id: customerId, eta_date: etaDate },
				dataType: 'json',
				success: function (response) {
					$('.duplicate-schedule-error').remove();

					if (response.is_duplicate) {
						var errorMsg = $('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');
						$('input[name="eta_date"]').closest('.form-group').append(errorMsg);
						$('input[name="eta_date"]').addClass('is-invalid');
					} else {
						$('input[name="eta_date"]').removeClass('is-invalid');
					}
				}
			});
		} else {
			$('.duplicate-schedule-error').remove();
			$('input[name="eta_date"]').removeClass('is-invalid');
		}
	}

	$(function () {
		addItemRow();

		var today = new Date().toISOString().split('T')[0];
		var $etaDate = $('input[name="eta_date"]');
		$etaDate.attr('min', today);

		$etaDate.on('input change blur', function () {
			if ($(this).val() && $(this).val() < today) {
				$(this).val(today);
			}
		});

		$etaDate.on('focus click', function () {
			$(this).attr('min', today);
		});

		$('.action-add-item').click(addItemRow);
		$('.item-table').on('click', '.action-delete-item', removeItemRow);
		$('select[name="customer"], input[name="eta_date"]').on('change', function () {
			setRowNumbers();
			checkDuplicateSchedule();
		});

		$('.item-table').on('change input', 'input[name^="eta_time_"]', checkDuplicateTripTimes);

		$('form.crud-box').on('submit', function (e) {
			var etaTimes = [];
			var hasEmpty = false;
			var hasDuplicate = false;

			$('.item-table input[name^="eta_time_"]').each(function () {
				if (!$(this).val()) {
					hasEmpty = true;
					return false;
				}
				etaTimes.push($(this).val());
			});

			if (!hasEmpty && new Set(etaTimes).size !== etaTimes.length) {
				hasDuplicate = true;
			}

			$('.eta-time-error, .duplicate-time-error').remove();
			$('.item-table input[name^="eta_time_"]').removeClass('is-invalid');

			if (hasEmpty) {
				var err = $('<div class="eta-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">ETA Time is required for all trips.</div>');
				$('.action-add-item').closest('.form-group').append(err);
				e.preventDefault();
			}

			if (hasDuplicate) {
				var err = $('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');
				$('.action-add-item').closest('.form-group').append(err);

				let counts = {};
				etaTimes.forEach(time => counts[time] = (counts[time] || 0) + 1);

				$('.item-table input[name^="eta_time_"]').each(function () {
					if (counts[$(this).val()] > 1) {
						$(this).addClass('is-invalid');
					}
				});
				e.preventDefault();
			}

			var customerId = $('select[name="customer"]').val();
			var etaDate = $('input[name="eta_date"]').val();
			if (customerId && etaDate) {
				var foundDuplicate = false;
				$.ajax({
					url: BASE_URI + 'schedules/check_duplicate_schedule',
					type: 'POST',
					data: { customer_id: customerId, eta_date: etaDate },
					dataType: 'json',
					async: false,
					success: function (response) {
						if (response.is_duplicate) {
							foundDuplicate = true;
							$('.duplicate-schedule-error').remove();
							var msg = $('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');
							$('input[name="eta_date"]').closest('.form-group').append(msg);
						}
					}
				});
				if (foundDuplicate) e.preventDefault();
			}
		});
	});

}(jQuery);
