<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Carcass Controller
 */
class Carcasses extends MYT_Controller
{
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('carcass');
        $this->lang->load('error_messages', 'english');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Carcass';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Carcass/manager'
        ];
        $this->build_content('default', 'Carcasses/manager');
    }

    /**
     * Add carcass records (multi-row)
     */
    public function add()
    {
        if ($this->input->post()) {
            $skus = $this->input->post('sku') ?: [];
            $sizes = $this->input->post('size') ?: [];
            $ranges = $this->input->post('range') ?: [];
            $nos = $this->input->post('no') ?: [];
            $pcs = $this->input->post('pcs') ?: [];
            $kgs = $this->input->post('kgs') ?: [];

            // Ensure all inputs are arrays
            if (!is_array($skus)) $skus = [$skus];
            if (!is_array($sizes)) $sizes = [$sizes];
            if (!is_array($ranges)) $ranges = [$ranges];
            if (!is_array($nos)) $nos = [$nos];
            if (!is_array($pcs)) $pcs = [$pcs];
            if (!is_array($kgs)) $kgs = [$kgs];

            $success_count = 0;
            $max_count = max(count($skus), count($sizes), count($ranges), count($nos), count($pcs), count($kgs));

            for ($i = 0; $i < $max_count; $i++) {
                $sku = isset($skus[$i]) ? trim($skus[$i]) : '';
                $size = isset($sizes[$i]) ? trim($sizes[$i]) : '';
                $range = isset($ranges[$i]) ? trim($ranges[$i]) : '';
                $no = isset($nos[$i]) ? trim($nos[$i]) : '';
                $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
                $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

                // Skip empty rows
                if (empty($sku) && empty($size) && empty($range) && empty($no) && empty($pcs_value) && empty($kgs_value)) {
                    continue;
                }

                $data = [
                    'sku' => $sku,
                    'size' => $size,
                    'range' => $range,
                    'no' => !empty($no) ? (int)$no : null,
                    'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
                    'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
                ];
                $this->carcass->insert_carcass($data);
                $success_count++;
            }
            if ($success_count > 0) {
                $this->session->set_flashdata('carcass_success', 'Carcass records added successfully.');
                redirect('Carcasses');
            } else {
                $this->session->set_flashdata('carcass_error', 'No valid data to save.');
                redirect('Carcasses/add');
            }
        }

        $this->title = 'Add Carcass';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Carcass/add'
        ];

        $data = [];

        $this->build_content('default', 'Carcasses/add', $data);
    }

    /**
     * Edit a single carcass record
     */
    public function edit($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
            redirect('Carcasses');
        }
        $this->title = 'Edit Carcass';
        $data['carcass'] = $carcass;
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Update a single carcass record
     */
    public function update($id)
    {
        $carcass = $this->carcass->get_carcass_by_id($id);
        if (!$carcass) {
            $this->session->set_flashdata('error', 'Carcass not found.');
            redirect('Carcasses');
        }
        if ($this->input->post()) {
            $data = [
                'sku' => $this->input->post('sku'),
                'size' => $this->input->post('size'),
                'range' => $this->input->post('range'),
                'no' => $this->input->post('no'),
                'pcs' => $this->input->post('pcs'),
                'kgs' => $this->input->post('kgs')
            ];
            $this->carcass->update_carcass($id, $data);
            redirect('Carcasses');
        }
        $this->title = 'Edit Carcass';
        $data['carcass'] = $carcass;
        $this->build_content('default', 'Carcasses/edit', $data);
    }

    /**
     * Delete carcass record (AJAX)
     */
    public function delete($id)
    {
        // Check if any delete session is active
        if ($this->_is_delete_session_active()) {
            $deleting_carcass_id = $this->session->userdata('deleting_carcass_id');
            http_response_code(400);
            $response = [
                'message' => 'Another carcass deletion is in progress (Carcass ID: ' . $deleting_carcass_id . '). Only one delete operation is allowed at a time.'
            ];
            echo json_encode($response);
            return;
        }

        $where = [
            'id' => $id,
            'is_deleted' => 0
        ];
        if (!$carcass = $this->carcass->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Carcass record not found.'
            ];
        } else {
            // Set delete session lock
            $this->_set_delete_session_lock($id);

            if (!$this->_attempt_delete($carcass)) {
                http_response_code(400);
                $response = [
                    'message' => $this->_error_msg()
                ];
            } else {
                $response = [
                    'message' => 'Carcass record successfully deleted.'
                ];
            }

            // Clear delete session lock
            $this->_clear_delete_session_lock();
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($carcass)
    {
        // Start transaction
        $this->db->trans_start();

        try {
            // Use model method to delete carcass
            $result = $this->carcass->soft_delete_carcass($carcass->id, $_SESSION['user']->id ?? 1);

            if (!$result) {
                $this->_error = 'Failed to delete carcass record';
                $this->db->trans_rollback();
                return false;
            }

            // Complete transaction
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE) {
                $this->_error = 'transaction_failed';
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            $this->_error = 'database_error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Check if a delete session is currently active
     */
    protected function _is_delete_session_active()
    {
        $deleting_carcass_id = $this->session->userdata('deleting_carcass_id');
        $delete_timestamp = $this->session->userdata('delete_timestamp');
        if (empty($deleting_carcass_id) || empty($delete_timestamp)) {
            return false;
        }
        $timeout = 5 * 60; // 5 minutes
        if (time() - $delete_timestamp > $timeout) {
            $this->_clear_delete_session_lock();
            return false;
        }

        return true;
    }

    /**
     * Set delete session lock
     */
    protected function _set_delete_session_lock($carcass_id)
    {
        $this->session->set_userdata([
            'deleting_carcass_id' => $carcass_id,
            'delete_timestamp' => time()
        ]);
    }

    /**
     * Clear delete session lock
     */
    protected function _clear_delete_session_lock()
    {
        $this->session->unset_userdata(['deleting_carcass_id', 'delete_timestamp']);
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'transaction_failed':
                return '<p>Transaction failed. Please try again.</p>';

            case 'server_error':
                return;

            default:
                // Handle database_error and other custom errors
                if (strpos($this->_error, 'database_error:') === 0) {
                    return '<p>' . $this->_error . '</p>';
                }

                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}
