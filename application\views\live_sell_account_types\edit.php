<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="applied_to">Applied to</label>

						<select name="applied_to" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('applied_to');?>">
							<option value=""></option>
							<option value="Sales Income" <?=set_select('applied_to', 'Sales Income', $live_sell_account_type->applied_to==='Sales Income');?>>Sales Income</option>
							<option value="Purchase Order" <?=set_select('applied_to', 'Purchase Order', $live_sell_account_type->applied_to==='Purchase Order');?>>Purchase Order</option>
						</select>	

						<div class="red-text flash-message">
							<?=form_error('applied_to');?>
						</div>
					</div>

					
					<div class="col-md-6 form-group">
						<label for="account_type">Account Type</label>
						<?php if($live_sell_account_type->applied_to==='Sales Income'):?>
							<select name="account_type" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('account_type', $live_sell_account_type->account_type);?>">
								<option value=""></option>
								<option value="Revenue" <?=set_select('account_type', 'Revenue', $live_sell_account_type->live_sell_account_type==='Revenue');?>>Revenue</option>
							</select>
						<?php elseif ($live_sell_account_type->applied_to==='Purchase Order'):?>
							<select name="account_type" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('account_type', $live_sell_account_type->account_type);?>">
								<option value=""></option>
								<option value="Cost of sales" <?=set_select('account_type', 'Cost of sales', $live_sell_account_type->live_sell_account_type==='Cost of sales');?>>Cost of sales</option>
								<option value="Current assets" <?=set_select('account_type', 'Current assets', $live_sell_account_type->live_sell_account_type==='Current assets');?>>Current assets</option>
								<option value="Current liabilities" <?=set_select('account_type', 'Current liabilities', $live_sell_account_type->live_sell_account_type==='Current liabilities');?>>Current liabilities</option>
								<option value="Equity" <?=set_select('account_type', 'Equity', $live_sell_account_type->live_sell_account_type==='Equity');?>>Equity</option>
								<option value="Fixed asset" <?=set_select('account_type', 'Fixed asset', $live_sell_account_type->live_sell_account_type==='Fixed asset');?>>Fixed asset</option>
								<option value="Operating expenses" <?=set_select('account_type', 'Operating expenses', $live_sell_account_type->live_sell_account_type==='Operating expenses');?>>Operating expenses</option>
								<option value="Other current assets" <?=set_select('account_type', 'Other current assets', $live_sell_account_type->live_sell_account_type==='Other current assets');?>>Other current assets</option>
								<option value="Other current liabilities" <?=set_select('account_type', 'Other current liabilities', $live_sell_account_type->live_sell_account_type==='Other current liabilities');?>>Other current liabilities</option>
							</select>
						<?php endif;?>
						

						<div class="red-text flash-message">
							<?=form_error('account_type');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="financial_statement">Financial Statement</label>

						<select name="financial_statement" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('financial_statement');?>">
							<option value=""></option>
							<option value="Balance sheet" <?=set_select('financial_statement', 'Balance sheet', $live_sell_account_type->financial_statement==='Balance sheet');?>>Balance sheet</option>
							<option value="Income statement" <?=set_select('financial_statement', 'Income statement', $live_sell_account_type->financial_statement==='Income statement');?>>Income statement</option>
						</select>

						<div class="red-text flash-message">
							<?=form_error('financial_statement');?>
						</div>
					</div>
					<div class="col-md-6 form-group">
						<label for="account_nature">Nature of Account</label>

						<select name="account_nature" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('account_nature');?>">
							<option value=""></option>
							<option value="Credit" <?=set_select('account_nature', 'Credit', $live_sell_account_type->account_nature==='Credit');?>>Credit</option>
							<option value="Debit" <?=set_select('account_nature', 'Debit', $live_sell_account_type->account_nature==='Debit');?>>Debit</option>
						</select>

						<div class="red-text flash-message">
							<?=form_error('account_nature');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col form-group">
						<label for="balance_sheet_category">Balance Sheet Category</label>

						<?php if($live_sell_account_type->financial_statement==='Income statement'):?>
							<select name="balance_sheet_category" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('balance_sheet_category', $live_sell_account_type->balance_sheet_category);?>">
								<option value=""></option>
							</select>
						<?php elseif ($live_sell_account_type->financial_statement==='Balance sheet'):?>
							<select name="balance_sheet_category" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('balance_sheet_category', $live_sell_account_type->balance_sheet_category);?>">
								<option value=""></option>
								<option value="Cash on hand and in bank" <?=set_select('balance_sheet_category', 'Cash on hand and in bank', $live_sell_account_type->balance_sheet_category==='Cash on hand and in bank');?>>Cash on hand and in bank</option>
								<option value="Cumulative earnings - Appropriated" <?=set_select('balance_sheet_category', 'Cumulative earnings - Appropriated', $live_sell_account_type->balance_sheet_category==='Cumulative earnings - Appropriated');?>>Cumulative earnings - Appropriated</option>
								<option value="Income tax payables" <?=set_select('balance_sheet_category', 'Income tax payables', $live_sell_account_type->balance_sheet_category==='Income tax payables');?>>Income tax payables</option>
								<option value="Inventories" <?=set_select('balance_sheet_category', 'Inventories', $live_sell_account_type->balance_sheet_category==='Inventories');?>>Inventories</option>
								<option value="Other assets" <?=set_select('balance_sheet_category', 'Other assets', $live_sell_account_type->balance_sheet_category==='Other assets');?>>Other assets</option>
								<option value="Other current assets" <?=set_select('balance_sheet_category', 'Other current assets', $live_sell_account_type->balance_sheet_category==='Other current assets');?>>Other current assets</option>
								<option value="Property and equipment - Net" <?=set_select('balance_sheet_category', 'Property and equipment - Net', $live_sell_account_type->balance_sheet_category==='Property and equipment - Net');?>>Property and equipment - Net</option>
								<option value="Share capital" <?=set_select('balance_sheet_category', 'Share capital', $live_sell_account_type->balance_sheet_category==='Share capital');?>>Share capital</option>
								<option value="Trade and other payables" <?=set_select('balance_sheet_category', 'Trade and other payables', $live_sell_account_type->balance_sheet_category==='Trade and other payables');?>>Trade and other payables</option>
								<option value="Trade and other receivables" <?=set_select('balance_sheet_category', 'Trade and other receivables', $live_sell_account_type->balance_sheet_category==='Trade and other receivables');?>>Trade and other receivables</option>
							</select>
						<?php endif;?>

						<div class="red-text flash-message">
							<?=form_error('balance_sheet_category');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12 form-group">
						<label for="account_name">Account Name</label>

						<input type="text" name="account_name" class="form-control" value="<?=set_value('account_name', $live_sell_account_type->account_name);?>">

						<div class="red-text flash-message">
							<?=form_error('account_name');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('live_sell_account_types')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
