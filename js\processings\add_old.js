+ function ($) {
	'use strict';

	function addItemRow(e) {
		var $newRow = $(itemRow());

	    setItemList($newRow);

		var $itemTable = $('.item-table');
		var order_slipItems = $itemTable.find('.order_slip_item');

		if (order_slipItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}

		// $($newRow).css("display", "none");
		// console.log($(e.target).closest('.sub_items'))
		$(e.target).parent().parent().parent().find('.sub-items').append($newRow).hide().fadeIn(300);
		// $("#asdasd").append($newRow);
		// $itemTable.find('tbody').append($newRow);

		// setRowNumbers();
	}

	function setItem(order_slip_item, item_id=null, item_name=null) {
		var $newOption = $("<option selected='selected'></option>").val(item_id).text(item_name);
		order_slip_item.find('select[name^="item"]').append($newOption);
	}

	function setItemList(order_slip_item) {
		order_slip_item.find('select[name^="item"]').select2({
		  tags: false,
		  allowClear: true,
		  placeholder: $(this).attr('data-placeholder'),
		  minimumInputLength: 3,
		  width: '70px',
		  minimumResultsForSearch: 10,
		  ajax: {
		      url: BASE_URI + 'live_sell_items/search_by_item',
		      dataType: "json",
		      type: "GET",
		      data: function (params) {
		          var queryParameters = {
		              term: params.term
		          }
		          return queryParameters;
		      },
		      processResults: function (data) {
		          return {
		              results: $.map(data, function (item) {
		                  return {
		                      text: item.name,
		                      id: item.id
		                  }
		              })
		          };
		      }
		  } 
		});
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="order_slip_item">' +
				'<td>' +
				'<input type="hidden" name="order_slip_item_' +
				rowId +
				'" value="' +
				rowId +
				'">' +
				'<select name="item_' +
				rowId +
				'" class="form-control dropsearch" data-placeholder="Item"></select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="crate_' + rowId + '" class="form-control" value="" min="0" step="any" placeholder="Crates">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="head_' + rowId + '" class="form-control" value="" min="0" step="any" placeholder="Heads">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="head_' + rowId + '" class="form-control" value="" min="0" step="any" placeholder="Kilos">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="recover_' + rowId + '" class="form-control" value="" min="0" step="any" placeholder="Recover %">' +
				'</td>' +
				'<td class="text-center">' +
					'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
				'</td>' +
			'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="7">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var order_slipItems = $itemTable.find('.order_slip_item');

		order_slipItems.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function setUnit(order_slip_item, available=null) {
		order_slip_item.find('input[name^="unit_"]').val(available);
	}

	function getUnit(itemId) {
		return $.ajax({
		  url: BASE_URI + 'live_sell_items/unit_of/' + itemId,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function getCustomerData(customerId) {
		return $.ajax({
		  url: BASE_URI + 'live_sell_customers/get_data/' + customerId,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.order_slip_item').remove();

		var $itemTable = $(event.delegateTarget);
		var order_slipItems = $itemTable.find('.order_slip_item');
		// if (order_slipItems.length < 1) {
		// 	var $emptyRow = $(emptyRow());
		// 	$itemTable.find('tbody').html($emptyRow);
		// }

		setRowNumbers();
	}

	$(function () {
		$('.action-add-item').on("click", function(e) {
			console.log($(e.target).closest('tbody'));
			addItemRow(e)
		}).click();

		// $('.action-add-item').click();

		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			}).on('change', 'select[name^="item"]', function (event) {
		        var order_slip_item = $(event.currentTarget).closest('.order_slip_item');
		        var itemId = event.currentTarget.value;

			    $.when(getUnit(itemId)).then(
			      function (response) {
			        setUnit(order_slip_item, response.unit);
			      },
			      function () {
			        setUnit(order_slip_item, null);
			      }
			    );
		        console.log(itemId);
			});



		$('select[name="customer"]').on('change', function(event) {
	        var order_slip_item = $(event.currentTarget).closest('.order_slip_item');
	        var customerId = event.currentTarget.value;
		    $.when(getCustomerData(customerId)).then(
		      function (response) {
		      	console.log(response);
		      	$('input[name="address"]').val(response.address);
		      	$('input[name="contact_number"]').val(response.contact_number);
		      	$('input[name="email"]').val(response.email);
		      	$('input[name="available_credit"]').val(response.available_credit);
		        // setUnit(order_slip_item, response.unit);
		      },
		      function () {
		      	$('input[name="address"]').val("");
		      	$('input[name="contact_number"]').val("");
		      	$('input[name="email"]').val("");
		      	$('input[name="available_credit"]').val("");
		      }
		    );
	        console.log(customerId);
		})

	    $('.order_slip_item').each(function(i, obj) {
	    	$(obj).find('option').not(':selected').remove();
			setItemList($(obj));
		});
	});
}(jQuery);
