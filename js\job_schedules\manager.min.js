!function(e){"use strict";function t(t){var a=e(t),r=a.find("thead th"),n=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:r.length-3},{className:"dt-action",targets:r.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(t,a){e.when(r(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function r(t){return e.ajax({url:BASE_URI+"invoices/for_approval/"+t,type:"POST",dataType:"json"})}function n(t,a){e.when(i(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function i(t){return e.ajax({url:BASE_URI+"invoices/delete/"+t,type:"POST",dataType:"json"})}e(function(){var r=t(".tableA");e(".tableA").on("click",".action-for-approval",function(t){var n=e(t.currentTarget).closest("tr.invoice").find("td"),i=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to recommend for approval the Billing No. "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,r)}).setHeader("<em>Recommend for Approval</em>")}).on("click",".action-delete",function(t){var a=e(t.currentTarget).closest("tr.invoice").find("td"),i=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to delete the Billing No. "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,r)}).setHeader("<em>Delete Invoice</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var a=BASE_URI+"fetch/invoices?status="+t.currentTarget.getAttribute("data-status");r.ajax.url(a).load()})})}(jQuery);