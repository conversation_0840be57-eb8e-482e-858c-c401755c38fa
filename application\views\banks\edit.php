<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="crud-title"><?=$title;?></div>

			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="name">Name of Bank</label>

						<input type="text" name="name" value="<?=set_value('name', $bank->name);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="print_template">Print Template</label>

						<select name="print_template" class="form-control dropsearch" data-placeholder="">
							<option></option>
						<?php foreach ($check_templates as $check_template): ?>
							<option value="<?=$check_template->id;?>" <?=set_select('print_template', $check_template->id, $check_template->id === $bank->print_template);?>><?=$check_template->name;?></option>
						<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('print_template');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save Changes</button>

				<a href="<?=site_url('banks/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>
