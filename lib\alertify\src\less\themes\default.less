.alertify {

    .dialog {
        background-color: white;
        box-shadow: 0px 15px 20px 0px rgba(0, 0, 0, 0.25);
        border-radius: 2px;
    }

    .header {
        color: black;
        font-weight: bold;
        background: #fafafa;
        border-bottom: #eee 1px solid;
        border-radius: 2px 2px 0 0;
    }

    .body {
        color: black;

        .content {

            .input {
                display: block;
                width: 100%;
                padding: 8px;
                margin: 4px;
                border-radius: 2px;
                border: 1px solid #CCC;
            }

            p {
                margin: 0;
            }
        }
    }

    .footer {
        background: #fbfbfb;
        border-top: #eee 1px solid;
        border-radius: 0 0 2px 2px;

        .buttons {
            .button {
                background-color: transparent;
                color: #000;
                border: 0;
                font-size: 14px;
                font-weight: bold;
                text-transform: uppercase;
                //&:focus{
                //	outline:auto 5px #498EFF;
                //	box-shadow: 0px 0px 4px 0px #498EFF;
                //}
                &.ok {
                    color: #3593D2;
                }
            }
        }
    }
}

/***************
 notifier
***************/

.alertify-notifier {
    .message {
        background: rgba( 255, 255, 255, .95);
        color: #000;
        text-align: center;
        border: solid 1px #ddd;
        border-radius: 2px;

        &.success {
            color: #fff;
            background: rgba(91, 189, 114,.95);
            text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.5);
        }

        &.error {
            color: #fff;
            background: rgba(217, 92, 92,.95);
            text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.5);
        }

        &.warning {
            background: rgba(252, 248, 215, 0.95);
            border-color: #999;
        }
    }
}
