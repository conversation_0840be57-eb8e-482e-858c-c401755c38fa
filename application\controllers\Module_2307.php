<?php
defined('BASEPATH') or exit('No direct script access allowed');


/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Module_2307 extends MYT_Controller    
{
    protected $_error = '';

    protected $_uploaded = [];


    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }


    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {   
        $this->load->model('supplier');
        $this->load->model('module_2307_model');
        $this->load->model('module_2307_attachment');
        $this->load->model('module_2307_detail');
        $this->load->model('user');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }


    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('module_2307/manager'));
    }


    /**
     * 2307 Manager
     */
    public function manager()
    {
        $this->title = "2307";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/module_2307/manager'
        ];

        $this->build_content('default', 'module_2307/manager');
    }
    

    /**    
     * Add 2307
     */
    public function add()
    {   
        $_2307_detail_ids = $this->_get_2307_detail_ids();
        $rules = array_merge($this->config->item('module_2307/add'), $this->_get_2307_detail_rules($_2307_detail_ids));

        $config = [
            'upload_path' => FCPATH . 'assets/',
            'allowed_types' => 'jpg|jpeg|png|pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }
    
        if ($this->_validate_form($rules) AND $this->_attempt_upload($config, 'attachments', true) AND $id = $this->_attempt_add() AND $this->_add_attachment($id)) {

            $new_path = FCPATH. 'assets/module_2307/'.$id.'/';
            $this->_move_uploaded($new_path);

            redirect(site_url('module_2307/manager'));
        } else {
            $this->_delete_uploaded();

            $this->title = 'Add Form 2307';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/module_2307/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $suppliers = $this->supplier->select('', ['is_deleted' => 0, 'is_active' => 1], null, 'name ASC');

            $data = [
                'form_error'    => $form_error,
                '_2307_detail_ids'  => $_2307_detail_ids,
                'suppliers'     => $suppliers,
            ];

            $this->build_content('default', 'module_2307/add', $data);
        }
    }


    /**
     * Attempt Add 2307
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $supplier_id = $this->input->post('supplier');

        // Validate the period dates
        if (!$this->_validate_dates()) {
            $this->_error = 'Invalid date selection. Please ensure dates are within the same quarter.';
            return false;
        }

        if (!$_2307_details = $this->_get_2307_detail_data()) {
            $this->_error = 'No 2307 detail added';

            return false;
        }

        if (!$_2307_id = $this->_generate_2307($supplier_id) or !$this->_generate_2307_details($_2307_id, $_2307_details)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';

            return false;
        } else {
            $this->db->trans_commit();

            return $_2307_id;
        }
    }


    /**
     * Generate 2307
     */
    protected function _generate_2307($supplier_id)
    {
        $period_from       = DateTime::createFromFormat('m/d/Y', $this->input->post('period_from'));
        $period_to         = DateTime::createFromFormat('m/d/Y', $this->input->post('period_to'));

        $values = [
            'suppliers_id'       => $supplier_id,
            'period_from'        => $period_from->format('Y-m-d'),
            'period_to'          => $period_to->format('Y-m-d'),
            'added_by'           => $_SESSION['user']->id,
            'added_on'           => date('Y-m-d H:i:s')
        ];

        if(!$id = $this->module_2307_model->specialInsert($values)) {
            return false;
        } else {
            return $id;
        }
    }


    /**
     * Generate 2307 Details
     */
    protected function _generate_2307_details($_2307_id, $_2307_details)
    {
        foreach ($_2307_details as $_2307_detail) {
            $values = [
                '2307_id'                       => $_2307_id,
                'income_payment_subject'       => $_2307_detail['income_payment_subject'],
                'atc'                           => $_2307_detail['atc'],
                'total_income_payment'          => $_2307_detail['total_income_payment'],
                'tax_withheld'                  => $_2307_detail['tax_withheld'],
                'added_by'                      => $_SESSION['user']->id,
                'added_on'                      => date('Y-m-d H:i:s')
            ];

            if (!$this->module_2307_detail->insert($values)) {
                return false;
            }
        }

        return true;
    }


    /**
     * Get 2307 Detail Data
     */
    protected function _get_2307_detail_data()
    {
        $data = [];
        $_2307_detail_ids = $this->_get_2307_detail_ids();

        foreach ($_2307_detail_ids as $_2307_detail_id) {
            $data[] = [
                '2307_id'                       => $_2307_detail_id,
                'income_payment_subject'       => $this->input->post('income_payment_subject_'.$_2307_detail_id),
                'atc'                           => $this->input->post('atc_'.$_2307_detail_id),
                'total_income_payment'          => $this->input->post('total_amount_of_income_'.$_2307_detail_id),
                'tax_withheld'                  => $this->input->post('tax_withheld_'.$_2307_detail_id),
                'added_by'                      => $_SESSION['user']->id,
                'added_on'                      => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }


    /**
     * Get Input Suffices
     */
    protected function _get_2307_detail_ids()
    {
        $_2307_detail_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, '_2307_detail_') === 0) {
                $_2307_detail_ids[] = $value;
            }
        }

        return $_2307_detail_ids;
    }


    /**
     * Get 2307 Detail Rules
     */
    protected function _get_2307_detail_rules($_2307_detail_ids)
    {
        $rules = [];

        foreach ($_2307_detail_ids as $_2307_detail_id) {
            $rules[] = [
                'field' => 'income_payment_subject_' . $_2307_detail_id,
                'label' => 'Income Payment Subject',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'total_amount_of_income_' . $_2307_detail_id,
                'label' => 'Total Amount of Income',
                'rules' => 'required|trim|numeric'
            ];
        }

        return $rules;
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';

            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
            * Because CI Upload Library does not support multiple upload,
            * trick it by setting individual file upload as $_FILES['userfile'].
            *
            */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);
        
            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);
        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }
    

    /**
     * Attempt add attachment
     */
    protected function _add_attachment($id)
    {
        if (!empty($this->_uploaded)) {
            $values = [];

            foreach ($this->_uploaded as $uploaded) {
                $values[] = [
                    '2307_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];
            }

            if (!$attachment_id = $this->module_2307_attachment->insert_batch($values)) {
                $this->_error = 'server_error';

                return false;
            }

            return $attachment_id;
        }
        else {
            return true;
        }
    }


    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {   
        write_file($path . '/index.html', 'Directory access if forbidden');
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) { 
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Edit 2307
     */
    public function edit($_2307_id)
    {   
        $module_2307 = $this->module_2307_model->get_by_id($_2307_id) or show_404();
        $module_2307_details = $this->module_2307_detail->get_by_2307_detail_id($_2307_id);
        $module_2307_attachments = $this->module_2307_attachment->get_by_2307_attachment_id($_2307_id);
        
        if (!$_2307_detail_ids = $this->_get_2307_detail_ids()) {
            foreach($module_2307_details AS $module_2307_detail) {
                $_2307_detail_ids[] = $module_2307_detail->id;
            }

            $existing_ids = array_map(function ($module_2307_detail) {
                return $module_2307_detail->id;
            }, $module_2307_details);

            $item_rules = $this->_get_2307_detail_rules($existing_ids);
        } 
        else {
            $item_rules = $this->_get_2307_detail_rules($_2307_detail_ids);
        }

        $rules = array_merge($this->config->item('module_2307/edit'), $item_rules);

        $config = [
            'upload_path' => FCPATH . 'assets/',
            'allowed_types' => 'jpg|jpeg|png|pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachments') && $this->_attempt_edit($module_2307) && $this->_add_attachment($_2307_id)) {
            // Move uploaded files to the new path
            $new_path = FCPATH . 'assets/module_2307/' . $_2307_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('module_2307/manager'));
        } else {
            // On failure: delete uploaded files
            $this->_delete_uploaded();

            $this->title = 'Edit Form 2307';
            $this->css   = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/alertify/build/css/alertify',
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/alertify/build/alertify',
                'js/module_2307/add'
            ];

            // Handle form errors
            $form_error = ($this->_error_msg() === validation_errors()) ? '' : $this->_error_msg();

            // Fetch suppliers
            $suppliers = $this->supplier->select('', ['is_deleted' => 0], null, 'name ASC');

            // Prepare data for the view
            $data = [
                'form_error'               => $form_error,
                '_2307_detail_ids'         => $_2307_detail_ids,
                'suppliers'                => $suppliers,
                'module_2307'              => $module_2307,
                'module_2307_details'      => $module_2307_details,
                'module_2307_attachments'  => $module_2307_attachments,
            ];

            $this->build_content('default', 'module_2307/edit', $data);
        }
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($module_2307)
    {
        $this->db->trans_begin();

        $where = [
            '2307_id' => $module_2307->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        // Validate the period dates
        if (!$this->_validate_dates()) {
            $this->_error = 'Invalid date selection. Please ensure dates are within the same quarter.';
            return false;
        }

        if (!$this->module_2307_detail->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$module_2307_details = $this->_get_2307_detail_data()) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif(!$this->_attempt_delete_items($_2307_id)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$this->_revise_2307($module_2307->id, $module_2307_details) || !$this->_insert_2307_details($module_2307->id, $module_2307_details)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise 2307
     */
    protected function _revise_2307($module_2307_id, $module_2307_details)
    {
        $period_from = DateTime::createFromFormat('m/d/Y', $this->input->post('period_from'));
        $period_to = DateTime::createFromFormat('m/d/Y', $this->input->post('period_to'));

        $where = [
            'id' => $module_2307_id
        ];

        $supplier_id = $this->input->post('supplier');

        $values = [
            'suppliers_id' => $supplier_id,
            'period_from' => $period_from->format('Y-m-d'),
            'period_to' => $period_to->format('Y-m-d'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->module_2307_model->update($where, $values);
    }

    /**
     * Insert 2307 Details
     */
    protected function _insert_2307_details($module_2307_id, $module_2307_details)
    {
        foreach ($module_2307_details as $module_2307_detail) {
            $values = [
                '2307_id' => $module_2307_id,
                'income_payment_subject' => $module_2307_detail['income_payment_subject'],
                'atc' => $module_2307_detail['atc'],
                'total_income_payment' => $module_2307_detail['total_income_payment'],
                'tax_withheld' => $module_2307_detail['tax_withheld'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s'),
            ];

            if (!$this->module_2307_detail->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Attempt Delete Detail 2307
     */
    protected function _attempt_delete_items($module_2307_id)
    {
        $where = [
            '2307_id' => $module_2307_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->module_2307_detail->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }


    public function delete($module_2307_model_id = null)
    {
        $where = [
            'id' => $module_2307_model_id,
            'is_deleted' => 0
        ];

        if (!$module_2307_model = $this->module_2307_model->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Item is not found.'
            ];
        } elseif (!$this->_attempt_delete($module_2307_model)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Item is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Attempt Delete 2307
     */
    protected function _attempt_delete($module_2307_model)
    {
        $where = ['id' => $module_2307_model->id];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->module_2307_model->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }


    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {   
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }


    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }


    /**
     * Delete Module 2307 Attachment
     */
    public function delete_attachment($attachment_id = null)
    {
        if (!$attachment_id) {
            http_response_code(400);
            echo json_encode(['message' => 'Invalid attachment ID.']);
            return;
        }
    
        $where = [
            'id' => $attachment_id,
            'is_deleted' => 0
        ];
    
        $module_2307_attachment = $this->module_2307_attachment->select('*', $where, 1);

        if (!$module_2307_attachment) {
            http_response_code(400);
            $response = [
                'message' => 'Module 2307 Attachment is not found.',
            ];
        } elseif (!$this->_attempt_delete_attachment($module_2307_attachment)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Module 2307 Attachment is successfully removed.',
            ];
        }
    
        echo json_encode($response);
    }

    /**
     * Attempt Delete Attachment
     */
    protected function _attempt_delete_attachment($module_2307_attachment)

    {
        $where = [
            'id' => $module_2307_attachment->id,
            'is_deleted' => 0,
        ];
    
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];
    
        if (!$is_deleted = $this->module_2307_attachment->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        } else {
            $path = FCPATH . 'assets/module_2307/' . $module_2307_attachment->{"2307_id"} . '/' . $module_2307_attachment->file_name;
            
            if (file_exists($path)) {
                unlink($path);
            } else {
                // Optionally handle the case where the file does not exist
                log_message('error', 'File not found: ' . $path);
            }
        }
    
        return $is_deleted;
    }

    protected function _validate_dates()
    {
        $period_from = $this->input->post('period_from');
        $period_to = $this->input->post('period_to');

        if ($period_from && $period_to) {
            $from_date = new DateTime($period_from);
            $to_date = new DateTime($period_to);
            
            // Ensure that the 'Period To' date is not before 'Period From' date
            if ($to_date < $from_date) {
                return false; // 'Period To' is before 'Period From'
            }

            // Calculate the quarter for the 'from' date
            $quarter_start_month = (floor(($from_date->format('n') - 1) / 3) * 3) + 1;
            $quarter_end_month = $quarter_start_month + 2;

            $quarter_start = new DateTime($from_date->format('Y') . '-' . $quarter_start_month . '-01');
            $quarter_end = new DateTime($from_date->format('Y') . '-' . $quarter_end_month . '-' . $quarter_start->format('t'));

            // Validate that the 'to' date is within the quarter
            if ($to_date < $quarter_start || $to_date > $quarter_end) {
                return false; // Date is out of quarterly bounds
            }
        }
        return true; // Dates are valid
    }

    /** 
     * Preview
    */
    public function preview($_2307_id) {
    
        $module_2307 = $this->module_2307_model->get_by_id($_2307_id) or show_404();
        $module_2307_details = $this->module_2307_detail->get_by_2307_detail_id($_2307_id);
        
        $this->_add_text_to_image($_2307_id); 
        
        $data['module_2307'] = $module_2307; 
        $data['module_2307_details'] = $module_2307_details;
        $data['generated_image'] = $this->session->userdata('generated_image_path'); 
    
        
        $this->title = 'Print Preview: Module 2307 No. ' . $module_2307->id;
        $this->css = ['lib/alertify/build/css/alertify'];
        $this->javascript = ['lib/alertify/build/alertify', 'js/module_2307/preview'];

        $this->build_content('default', 'module_2307/preview', $data);
    }


    public function _add_text_to_image($_2307_id) {
        $module_2307 = $this->module_2307_model->get_by_id($_2307_id) or show_404();
        $module_2307_details = $this->module_2307_detail->get_by_2307_detail_id($_2307_id);
        $creator_sig = $this->user->select('sig_url', ['id' => $module_2307->added_by], 1)?->sig_url;
        $signature = $creator_sig ? base_url('assets/asylum/signatures/' . $module_2307->added_by . '/' . $creator_sig) : '';
    
        $image_path = FCPATH . 'img/icons/form2307A.png'; 
        $output_path = FCPATH . 'assets/module_2307/' . $_2307_id . '/form2307_' . $_2307_id . '.png'; 
    
        $supplier_name = strtoupper($module_2307->supplier_name);
        $supplier_tin = $this->_format_tin($module_2307->supplier_tin);
        $supplier_address = strtoupper($module_2307->supplier_address);
        $supplier_zip = strtoupper($module_2307->supplier_zip_code);
        $supplier_zip = preg_replace('/(\d)(?=\d)/', '$1 ', $supplier_zip);
    
        $period_from = DateTime::createFromFormat('Y-m-d', $module_2307->period_from)->format('m d Y');
        $period_to = DateTime::createFromFormat('Y-m-d', $module_2307->period_to)->format('m d Y');
        $period_from = preg_replace('/(\d)/', '$1 ', $period_from);
        $period_to = preg_replace('/(\d)/', '$1 ', $period_to);

        $lavc_tin = $this->_format_tin("0054593180000");
        $lavc_name = "LEYTE AGR-VENTURES CORPORATION";
        $lavc_address = "No. 8 Happyland, Brgy. 69 Anibong,Tacloban City";
        $lavc_zip = preg_replace('/(\d)(?=\d)/', '$1 ', "6500");
    
        $font_path = FCPATH . 'fonts/FiraCode-Regular.ttf'; 
        $supplier_name_font_size = 24; 
        $supplier_tin_font_size = 25; 
        $supplier_address_font_size = 21; 
        $supplier_zip_font_size = 21; 
        $period_font_size = 20.6; 
        $lavc_tin_font_size = 25;
        $lavc_name_font_size = 24;
        $lavc_address_font_size = 21;
        $lavc_zip_font_size = 21;


        $income_payment_subject_font_size = 11; 
        $atc_font_size = 18;
        $total_income_payment_font_size = 16;
        $overall_total_income_payment_font_size = 16; 
        $total_quarter_font_size = 16; 
        $tax_withheld_font_size = 16;
        $total_tax_withheld_font_size = 16; 
        $color = [0, 0, 0]; 
    
        if (!file_exists($image_path)) {
            show_error('Image file does not exist: ' . $image_path);
            return;
        }
    
        $image = imagecreatefromstring(file_get_contents($image_path));
        if (!$image) {
            show_error('Failed to create image from the given path: ' . $image_path);
            return;
        }
    
        if (!file_exists($font_path)) {
            show_error('Font file does not exist: ' . $font_path);
            imagedestroy($image);
            return;
        }
    
        $text_color = imagecolorallocate($image, $color[0], $color[1], $color[2]);
        if ($text_color === false) {
            show_error('Failed to allocate text color.');
            imagedestroy($image);
            return;
        }
    
        $supplier_name_ay = 495; 
        $supplier_name_ax = 100;
        $supplier_tin_y = 420; 
        $supplier_tin_x = 578; 
        $supplier_address_y = 570; 
        $supplier_address_x = 100;
        $supplier_zip_y = 570; 
        $supplier_zip_x = 1513; 

        $period_from_x = 430; 
        $period_from_y = 330; 
        $period_to_x = 1115;   
        $period_to_y = 330;  

        $lavc_tin_y = 740;
        $lavc_tin_x = 583;
        $lavc_name_ay = 810;
        $lavc_name_ax = 100;
        $lavc_address_x = 100;
        $lavc_address_y = 885;
        $lavc_name_by = 2080;
        $lavc_name_bx = 550;
        $lavc_zip_y = 890;
        $lavc_zip_x = 1513;

        $supplier_name_by = 2290; 
        //To make the position x to be at the center
        $bbox = imagettfbbox($supplier_name_font_size, 0, $font_path, $supplier_name);
        $text_width = $bbox[2] - $bbox[0]; 
        $supplier_name_bx = (1690 - $text_width) / 2; 

    
        if (!imagettftext($image, $supplier_name_font_size, 0, $supplier_name_ax, $supplier_name_ay, $text_color, $font_path, $supplier_name)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $supplier_name);
            return;
        }
    
        if (!imagettftext($image, $supplier_tin_font_size, 0, $supplier_tin_x, $supplier_tin_y, $text_color, $font_path, $supplier_tin)) {
            imagedestroy($image);
            show_error('Failed to add supplier TIN to image: ' . $supplier_tin);
            return;
        }
    
        if (!imagettftext($image, $supplier_address_font_size, 0, $supplier_address_x, $supplier_address_y, $text_color, $font_path, $supplier_address)) {
            imagedestroy($image);
            show_error('Failed to add supplier address to image: ' . $supplier_address);
            return;
        }
    
        if (!imagettftext($image, $supplier_zip_font_size, 0, $supplier_zip_x, $supplier_zip_y, $text_color, $font_path, $supplier_zip)) {
            imagedestroy($image);
            show_error('Failed to add supplier ZIP code to image: ' . $supplier_zip);
            return;
        }
    
        if (!imagettftext($image, $period_font_size, 0, $period_from_x, $period_from_y, $text_color, $font_path, $period_from)) {
            imagedestroy($image);
            show_error('Failed to add period_from to image.');
            return;
        }
    
        if (!imagettftext($image, $period_font_size, 0, $period_to_x, $period_to_y, $text_color, $font_path, $period_to)) {
            imagedestroy($image);
            show_error('Failed to add period_to to image.');
            return;
        }

            //Lavc information
        if (!imagettftext($image, $lavc_tin_font_size, 0, $lavc_tin_x, $lavc_tin_y, $text_color, $font_path, $lavc_tin)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $lavc_tin);
            return;
        }

        if (!imagettftext($image, $lavc_name_font_size, 0, $lavc_name_ax, $lavc_name_ay, $text_color, $font_path, $lavc_name)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $lavc_name);
            return;
        }

        if (!imagettftext($image, $lavc_address_font_size, 0, $lavc_address_x, $lavc_address_y, $text_color, $font_path, $lavc_address)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $lavc_address);
            return;
        }

        if (!imagettftext($image, $lavc_zip_font_size, 0, $lavc_zip_x, $lavc_zip_y, $text_color, $font_path, $lavc_zip)) {
            imagedestroy($image);
            show_error('Filed to add supplier name to image: ' . $lavc_zip);
            return;
        }

        if (!imagettftext($image, $lavc_name_font_size, 0, $lavc_name_bx, $lavc_name_by, $text_color, $font_path, $lavc_name)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $lavc_name);
            return;
        }


        if (!imagettftext($image, $supplier_name_font_size, 0, $supplier_name_bx, $supplier_name_by, $text_color, $font_path, $supplier_name)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $supplier_name);
            return;
        }

        
        if (!imagettftext($image, $supplier_name_font_size, 0, $supplier_name_bx, $supplier_name_by, $text_color, $font_path, $supplier_name)) {
            imagedestroy($image);
            show_error('Failed to add supplier name to image: ' . $supplier_name);
            return;
        }

    
        $payment_details = [
            'goods' => ['text' => 'Payment of goods', 'wc' => 'WC 158'],
            'services' => ['text' => 'Payment of services', 'wc' => 'WC 160'],
            'rentals' => ['text' => 'Payment of rental', 'wc' => 'WC 100'],
        ];
    
        $payment_text_y = 1030;
        $payment_text_x = 100;

        //Income payment subject
        foreach ($module_2307_details as $detail) {
            $income_payment_subject = strtolower($detail->income_payment_subject);
            error_log("Processing: " . $income_payment_subject); 
        
            if (array_key_exists($income_payment_subject, $payment_details)) {
                $payment_text = $payment_details[$income_payment_subject]['text'];
                $wc_text = $payment_details[$income_payment_subject]['wc'];
        
                if (!imagettftext($image, $income_payment_subject_font_size, 0, $payment_text_x, $payment_text_y, $text_color, $font_path, $payment_text)) {
                    imagedestroy($image);
                    show_error('Failed to add payment text to image: ' . $payment_text);
                    return;
                }
        
                $wc_text_y = $payment_text_y + 15; 
                if (!imagettftext($image, $income_payment_subject_font_size, 0, $payment_text_x, $wc_text_y, $text_color, $font_path, $wc_text)) {
                    imagedestroy($image);
                    show_error('Failed to add WC text to image: ' . $wc_text);
                    return;
                }
        
                $payment_text_y += 40; 
            } else {
                error_log("Missing payment details for: " . $income_payment_subject);
            }
        }
        
        // ATC
        $atc_text_x = 500; 
        $atc_text_y = 1047; 
    
        foreach ($module_2307_details as $detail) {
            $atc_text = strtoupper($detail->atc);
            if (!imagettftext($image, $atc_font_size, 0, $atc_text_x, $atc_text_y, $text_color, $font_path, $atc_text)) {
                imagedestroy($image);
                show_error('Failed to add ATC text to image: ' . $atc_text);
                return;
            }
        
            $atc_text_y += 37; 
        }
        
        //Total Income Payment
        $total_income_payment_y = 1050; 
        $total_income_payment_x = $this->_get_quarter_x_position($module_2307->period_from, $module_2307->period_to); 
        $overall_total_income_payment_y = 1420; 
        $overall_total_income_payment_x = $total_income_payment_x;
        
        // Calculate overall total income payment
        $overall_total_income_payment = 0; 
        
        foreach ($module_2307_details as $detail) {
            $total_income_payment = number_format($detail->total_income_payment, 2); 
            if (!imagettftext($image, $total_income_payment_font_size, 0, $total_income_payment_x, $total_income_payment_y, $text_color, $font_path, $total_income_payment)) {
                imagedestroy($image);
                show_error('Failed to add total_income_payment text to image: ' . $total_income_payment);
                return;
            }
            $total_income_payment_y += 37; 
            $overall_total_income_payment += $detail->total_income_payment; 
        }

        
        $total_quarter_y = 1050; 
        $total_quarter_x = 1230; 
        
        foreach ($module_2307_details as $detail) {
            $total_quarter = number_format($detail->total_income_payment, 2); 
        
            if (!imagettftext($image, $total_quarter_font_size, 0, $total_quarter_x, $total_quarter_y, $text_color, $font_path, $total_quarter)) {
                imagedestroy($image);
                show_error('Failed to add total_quarter text to image: ' . $total_quarter);
                return;
            }
    
            $total_quarter_y += 37; 
        }
        
        if (!imagettftext($image, $overall_total_income_payment_font_size, 0, $overall_total_income_payment_x, $overall_total_income_payment_y, $text_color, $font_path, number_format($overall_total_income_payment, 2))) {
            imagedestroy($image);
            show_error('Failed to add overall total income payment to image.');
            return;
        }
        
        $tax_withheld_x = 1450; 
        $tax_withheld_y = 1047; 
        $total_tax_withheld_y = 1420;
        $total_tax_withheld_x = 1450; 
        $total_tax_withheld = 0; 
        
        foreach ($module_2307_details as $detail) {
            $tax_withheld = number_format($detail->tax_withheld, 2); 
            if (!imagettftext($image, $tax_withheld_font_size, 0, $tax_withheld_x, $tax_withheld_y, $text_color, $font_path, $tax_withheld)) {
                imagedestroy($image);
                show_error('Failed to add tax_withheld text to image: ' . $tax_withheld);
                return;
            }
            $tax_withheld_y += 37; 
            $total_tax_withheld += $detail->tax_withheld; 
        }

        if (!imagettftext($image, $total_tax_withheld_font_size, 0, $total_tax_withheld_x, $total_tax_withheld_y, $text_color, $font_path, number_format($total_tax_withheld, 2))) {
            imagedestroy($image);
            show_error('Failed to add total tax withheld to image.');
            return;
        }

        $signature_x = 740;
        $signature_y = 1980;
        if ($signature) {
            $image_info = getimagesize($signature); // Get image metadata
            $mime_type = $image_info['mime']; // Extract MIME type (e.g., 'image/png')
            
            // Load the image based on its MIME type
            switch ($mime_type) {
                case 'image/png':
                    $signature_img = imagecreatefrompng($signature);
                    break;
                case 'image/jpeg':
                case 'image/jpg':
                    $signature_img = imagecreatefromjpeg($signature);
                    break;
                case 'image/gif':
                    $signature_img = imagecreatefromgif($signature);
                    break;
                case 'image/webp':
                    $signature_img = imagecreatefromwebp($signature);
                    break;
                default:
                    show_error('Unsupported image type.');
                    return;
            }
        
            if ($signature_img) {
                // Define the fixed dimensions
                $fixed_width = 200;  // Fixed width for the signature image
                $fixed_height = 100; // Fixed height for the signature image
            
                // Create a new true color image with the fixed dimensions
                $rescaled_signature_img = imagecreatetruecolor($fixed_width, $fixed_height);
            
                // Preserve transparency in PNG images
                if ($mime_type === 'image/png') {
                    // Enable saving alpha channel to preserve transparency
                    imagesavealpha($rescaled_signature_img, true);
                    
                    // Allocate a fully transparent color (alpha = 127)
                    $transparent = imagecolorallocatealpha($rescaled_signature_img, 0, 0, 0, 127);
                    
                    // Fill the image with the transparent color (this ensures the background is transparent)
                    imagefill($rescaled_signature_img, 0, 0, $transparent);
                    
                    // Set the transparent color in the signature image
                    imagecolortransparent($rescaled_signature_img, $transparent);
                }
            
                // Rescale the image to the fixed width and height
                imagecopyresampled($rescaled_signature_img, $signature_img, 0, 0, 0, 0, $fixed_width, $fixed_height, imagesx($signature_img), imagesy($signature_img));
            
                // Ensure the main image has transparency support (if PNG)
                if ($mime_type === 'image/png') {
                    imagesavealpha($image, true);  // Enable alpha channel saving on the main image
                    imagealphablending($image, false);  // Disable blending to preserve transparency
                }
            
                // Overlay the resized signature image onto the main image, maintaining transparency
                imagecopy($image, $rescaled_signature_img, $signature_x, $signature_y, 0, 0, $fixed_width, $fixed_height);
            
                // Clean up
                imagedestroy($signature_img);
                imagedestroy($rescaled_signature_img);
            } else {
                imagedestroy($image);
                show_error('Failed to load signature image.');
                return;
            }     
        }
    
        if (!imagepng($image, $output_path)) {
            imagedestroy($image);
            show_error('Failed to save the modified image: ' . $output_path);
            return;
        }
    
        imagedestroy($image);
    
        $this->session->set_userdata('generated_image_path', base_url('assets/module_2307/' . $_2307_id . '/form2307_' . $_2307_id . '.png'));
    }
    
    // Function to format the TIN
    private function _format_tin($tin) {
        $tin = preg_replace('/\D/', '', $tin);
        if (strlen($tin) > 14) {
            return 'Invalid TIN'; 
        }
        return substr($tin, 0, 1) . ' ' . 
            substr($tin, 1, 1) . ' ' . 
            substr($tin, 2, 1) . '  ' . 
            substr($tin, 3, 1) . ' ' . 
            substr($tin, 4, 1) . ' ' . 
            substr($tin, 5, 1) . '  ' . 
            substr($tin, 6, 1) . ' ' . 
            substr($tin, 7, 1) . ' ' . 
            substr($tin, 8, 1) . '   ' . 
            substr($tin, 9, 1) . ' ' . 
            substr($tin, 10, 1) . ' ' . 
            substr($tin, 11, 1) . ' ' . 
            substr($tin, 12, 1) . ' ' . 
            substr($tin, 13, 1);
    }
    
    
    private function _get_quarter_x_position($period_from, $period_to) {
        $month_from = (int)date('m', strtotime($period_from));
        if ($month_from === 1) { // January (1st month of Q1)
            return 620; 
        } elseif ($month_from === 2) { // February (2nd month of Q1)
            return 850; 
        } elseif ($month_from === 3) { // March (3rd month of Q1)
            return 1050; 
        } elseif ($month_from === 4) { // April (1st month of Q2)
            return 620; 
        } elseif ($month_from === 5) { // May (2nd month of Q2)
            return 850; 
        } elseif ($month_from === 6) { // June (3rd month of Q2)
            return 1050; 
        } elseif ($month_from === 7) { // July (1st month of Q3)
            return 620; 
        } elseif ($month_from === 8) { // August (2nd month of Q3)
            return 850; 
        } elseif ($month_from === 9) { // September (3rd month of Q3)
            return 1050; 
        } elseif ($month_from === 10) { // October (1st month of Q4)
            return 620; 
        } elseif ($month_from === 11) { // November (2nd month of Q4)
            return 850; 
        } elseif ($month_from === 12) { // December (3rd month of Q4)
            return 1050; 
        }
        return 1230; 
    }

    
}
