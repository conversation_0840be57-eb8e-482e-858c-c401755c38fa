<?php
defined('BASEPATH') or exit('No direct script access allowed');
// require 'vendor/autoload.php';
// use PhpOffice\PhpSpreadsheet\Spreadsheet;
// use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
// use PhpOffice\PhpSpreadsheet\Style\Alignment;
// use PhpOffice\PhpSpreadsheet\Style\Border;

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Development & Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_account_types extends MYT_Controller
{

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->live_sell_account_type_account_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_account_types/manager'));
    }

    /**
     * Get All Account Types
     */

     public function all($applied_to)
     {
        $applied_to = str_replace("_", " ", $applied_to);
        $where = [
            'applied_to' => $applied_to,
            'is_deleted' => 0
        ];

        if (!$live_sell_account_types = $this->live_sell_account_type->select('', $where)) {
            http_response_code(400);
            $response = [
                'message' => 'No account type is found.'
            ];
        } else {
            $response = [
                'message' => 'Account types are found.',
                'live_sell_account_types' => $live_sell_account_types
            ];
        }

        echo json_encode($response);
     }

    /**
     * Add Account Type
     */
    public function add()
    {
        $rules = $this->config->item('live_sell_account_types/add');
        if ($this->input->post("financial_statement") == "Income statement") {
            array_pop($rules);
        }

        $this->_validate_form($rules);

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('live_sell_account_types/manager'));
        } else {
            $this->title = 'Add Account Name';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
                'js/live_sell_account_types/add'
            ];
            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'live_sell_account_types/add', $data);
        }
    }

    /**
     * Edit Account Type
     */
    public function edit($live_sell_account_type_id)
    {
        $rules = $this->config->item('live_sell_account_types/edit');
        if ($this->input->post("financial_statement") == "Income statement") {
            array_pop($rules);
        }

        $where = [
            'id' => $live_sell_account_type_id,
            'is_deleted' => 0
        ];
        $live_sell_account_type = $this->live_sell_account_type->select('', $where, 1) or show_404();

        $this->live_sell_account_type_account_name = $live_sell_account_type->account_name;

        if ($this->_validate_form($rules) && $this->_attempt_edit($live_sell_account_type)) {
            redirect(site_url('live_sell_account_types/manager'));
        } else {
            $this->title = 'Edit Account Name';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
                'js/live_sell_account_types/add'
            ];
            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            // var_dump($live_sell_account_type);
            // die();
            $data = [
                'form_error' => $form_error,
                'live_sell_account_type' => $live_sell_account_type
            ];
            $this->build_content('default', 'live_sell_account_types/edit', $data);
        }
    }

    /**
     * Callback: Check if account type name exists
     */
    public function _is_account_type($account_type_account_name)
    {

        // var_dump($account_type_account_name);
        // die();
        $where = [
            'account_name' => $account_type_account_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->live_sell_account_type_account_name) === strtolower($account_type_account_name)) {
            return true;
        }

        return empty($this->live_sell_account_type->select('', $where, 1));
    }

    /**
     * Delete Account Type
     */
    public function delete($live_sell_account_type_id)
    {
        $where = [
            'id' => $live_sell_account_type_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_account_type = $this->live_sell_account_type->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Account type is not found.'
            ];
        } elseif (!$this->_attempt_delete($live_sell_account_type)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Account type is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Account Type
     */
    public function manager()
    {
        $this->title = 'Chart of Accounts';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_account_types/manager'
        ];
        $this->build_content('default', 'live_sell_account_types/manager');
    }

    /**
     * Expenses Report By Account Type
     */
    public function purchase()
    {
        $this->title = 'Expenses By Account Name';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/expenses/live_sell_account_types'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $data = [
            'suppliers' => $suppliers
        ];
        
        $this->build_content('default', 'expenses/live_sell_account_type', $data);
    }

    /**
     * Expenses Report Balance Sheet
     */
    public function balance_sheet()
    {
        // die("hello");
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Times New Roman');
        $sheet->getColumnDimension('A')->setWidth(1.78);

        $borderMedium = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ]; 
        
        $borderThin = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $borderDouble = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DOUBLE,
                ],
            ],
        ];

        $sheet->getStyle('A1:H1')->applyFromArray($borderMedium);
        $sheet->getStyle('A6:H6')->applyFromArray($borderMedium);
        $sheet->getStyle('A12:H12')->applyFromArray($borderThin);
        $sheet->getStyle('A13:H13')->applyFromArray($borderThin);
        $sheet->getStyle('A16:H16')->applyFromArray($borderThin);
        $sheet->getStyle('A17:H17')->applyFromArray($borderThin);
        $sheet->getStyle('A18:H18')->applyFromArray($borderDouble);
        $sheet->getStyle('A19:H19')->applyFromArray($borderMedium);
        $sheet->getStyle('A24:H24')->applyFromArray($borderThin);
        $sheet->getStyle('A25:H25')->applyFromArray($borderThin);
        $sheet->getStyle('A26:H26')->applyFromArray($borderThin);
        $sheet->getStyle('A30:H30')->applyFromArray($borderThin);
        $sheet->getStyle('A31:H31')->applyFromArray($borderThin);
        $sheet->getStyle('A32:H32')->applyFromArray($borderDouble);
        $sheet->getStyle('A33:H33')->applyFromArray($borderMedium);
        $sheet->getStyle('D38:F38')->applyFromArray($borderThin);
        $sheet->getStyle('D39:F39')->applyFromArray($borderThin);
        $sheet->getStyle('H38:J38')->applyFromArray($borderThin);
        $sheet->getStyle('H39:J39')->applyFromArray($borderThin);
        $sheet->getStyle('D40:F40')->applyFromArray($borderThin);

        $sheet->setCellValue('A1', 'Leyte Agri Ventures Corporations');
        $sheet->getStyle('A1')->getFont()->setSize(14)->setBold(true);

        $sheet->setCellValue('A2', 'STATEMENTS OF FINANCIAL POSITION');
        $sheet->getStyle('A2')->getFont()->setSize(12)->setBold(true);

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->post('dateFrom'));
        $date_from = $date_from->format('Y-m-d');
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->post('dateTo'));
        $date_to = $date_to->format('Y-m-d');

        $subtitle = "";
        if ($date_to != "") {
            $subtitle = date("F j, Y", strtotime($date_to));
        }

        $sheet->setCellValue('A3', "As of " . $subtitle);
        $sheet->getStyle('A3')->getFont()->setSize(10)->setItalic(true);

        $year_to = explode("-", $date_to);
        $sheet->setCellValue('G6', $year_to[0]);
        $sheet->getColumnDimension('G')->setWidth(30);
        $sheet->getStyle('G6')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('A7', 'A S S E T S');
        $sheet->getRowDimension('7')->setRowHeight(21);
        $sheet->mergeCells("A7:D7");
        $sheet->getStyle('A7')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);  

        $sheet->setCellValue('A8', 'Current Assets');
        $sheet->getRowDimension('8')->setRowHeight(21);
        $sheet->getStyle('A8')->getFont()->setSize(11)->setBold(true);


        $totalCurrentAssets = 0;
        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Cash on hand and in bank', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B9', 'Cash on hand and in bank');
        $sheet->mergeCells("B9:D9");
        $sheet->getStyle('B9')->getFont()->setSize(11);
        $sheet->setCellValue('G9', "₱ " . number_format($amount, 2));
        $sheet->getStyle('G9')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G9')->getFont()->setSize(11)->setBold(true);
        $totalCurrentAssets += $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Trade and other receivables', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B10', 'Trade and other receivables');
        $sheet->mergeCells("B10:D10");
        $sheet->getStyle('B10')->getFont()->setSize(11);
        $sheet->setCellValue('G10', " " . number_format($amount, 2));
        $sheet->getStyle('G10')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G10')->getFont()->setSize(11)->setBold(true);
        $totalCurrentAssets += $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Inventories', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B11', 'Inventories');
        $sheet->mergeCells("B11:D11");
        $sheet->getStyle('B11')->getFont()->setSize(11);
        $sheet->setCellValue('G11', " " . number_format($amount, 2));
        $sheet->getStyle('G11')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G11')->getFont()->setSize(11)->setBold(true);
        $totalCurrentAssets += $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Other current assets', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B12', 'Other current assets');
        $sheet->mergeCells("B12:D12");
        $sheet->getStyle('B12')->getFont()->setSize(11);
        $sheet->setCellValue('G12', " " . number_format($amount, 2));
        $sheet->getStyle('G12')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G12')->getFont()->setSize(11)->setBold(true);
        $totalCurrentAssets += $amount;

        $sheet->setCellValue('B13', 'Total Current Assets');
        $sheet->getRowDimension('13')->setRowHeight(18);
        $sheet->mergeCells("B13:D13");
        $sheet->getStyle('B13')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('B13')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('G13', " " . number_format($totalCurrentAssets, 2));
        $sheet->getStyle('G13')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G13')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('A14', 'Noncurrent Assets');
        $sheet->getRowDimension('14')->setRowHeight(21);
        $sheet->getStyle('A14')->getFont()->setSize(11)->setBold(true);


        $totalNoncurrentAssets = 0;
        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Property and equipment - net', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B15', 'Property and equipment - net');
        $sheet->mergeCells("B15:D15");
        $sheet->getStyle('B15')->getFont()->setSize(11);
        $sheet->setCellValue('G15', " " . number_format( $amount, 2));
        $sheet->getStyle('G15')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G15')->getFont()->setSize(11)->setBold(true);
        $totalNoncurrentAssets +=  $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Other assets', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B16', 'Other assets');
        $sheet->mergeCells("B16:D16");
        $sheet->getStyle('B16')->getFont()->setSize(11);
        $sheet->setCellValue('G16', " " . number_format($amount, 2));
        $sheet->getStyle('G16')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G16')->getFont()->setSize(11)->setBold(true);
        $totalNoncurrentAssets += $amount;

        $sheet->setCellValue('B17', 'Total Noncurrent Assets');
        $sheet->getRowDimension('17')->setRowHeight(18);
        $sheet->mergeCells("B17:D17");
        $sheet->getStyle('B17')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('B17')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('G17', " " . number_format($totalNoncurrentAssets, 2));
        $sheet->getStyle('G17')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G17')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('A18', 'TOTAL ASSETS');
        $sheet->getRowDimension('18')->setRowHeight(21);
        $sheet->mergeCells("A18:D18");
        $sheet->getStyle('A18')->getFont()->setSize(11)->setBold(true);

        $totalAssets = $totalCurrentAssets + $totalNoncurrentAssets;
        $sheet->setCellValue('G18', "₱ " . number_format($totalAssets, 2));
        $sheet->getStyle('G18')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G18')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('A20', 'LIABILITIES AND EQUITY');
        $sheet->getRowDimension('20')->setRowHeight(21);
        $sheet->mergeCells("A20:D20");
        $sheet->getStyle('A20')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A20')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A21', 'L I A B I L I T I E S');
        $sheet->getRowDimension('21')->setRowHeight(21);
        $sheet->mergeCells("A21:D21");
        $sheet->getStyle('A21')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A21')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);  

        $sheet->setCellValue('A22', 'Noncurrent Liabilities');
        $sheet->getRowDimension('22')->setRowHeight(21);
        $sheet->getStyle('A22')->getFont()->setSize(11)->setBold(true);


        $totalNoncurrentLiabilities = 0;
        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Trade and other payables', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B23', 'Trade and other payables');
        $sheet->mergeCells("B23:D23");
        $sheet->getStyle('B23')->getFont()->setSize(11);
        $sheet->setCellValue('G23', "₱ " . number_format($amount, 2));
        $sheet->getStyle('G23')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G23')->getFont()->setSize(11)->setBold(true);
        $totalNoncurrentLiabilities += $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Income tax payables', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B24', 'Income tax payables');
        $sheet->mergeCells("B24:D24");
        $sheet->getStyle('B24')->getFont()->setSize(11);
        $sheet->setCellValue('G24', " " . number_format($amount, 2));
        $sheet->getStyle('G24')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G24')->getFont()->setSize(11)->setBold(true);
        $totalNoncurrentLiabilities += $amount;

        $sheet->setCellValue('B25', 'Total Noncurrent Liabilities');
        $sheet->getRowDimension('25')->setRowHeight(18);
        $sheet->mergeCells("B25:D25");
        $sheet->getStyle('B25')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('B25')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('G25', " " . number_format($totalNoncurrentLiabilities, 2));
        $sheet->getStyle('G25')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G25')->getFont()->setSize(11)->setBold(true);

        $sheet->getRowDimension('19')->setRowHeight(10);

        $sheet->setCellValue('A26', 'TOTAL LIABILITIES');
        $sheet->getRowDimension('26')->setRowHeight(21);
        $sheet->mergeCells("A26:D26");
        $sheet->getStyle('A26')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('G26', " " . number_format($totalNoncurrentLiabilities, 2));
        $sheet->getStyle('G26')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('G26')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->setCellValue('A27', 'E Q U I T Y');
        $sheet->getRowDimension('27')->setRowHeight(21);
        $sheet->mergeCells("A27:D27");
        $sheet->getStyle('A27')->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle('A27')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $totalEquity = 0;
        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Share capital', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B28', 'Share capital');
        $sheet->mergeCells("B28:D28");
        $sheet->getStyle('B28')->getFont()->setSize(11);
        $sheet->setCellValue('G28', " " . number_format($amount, 2));
        $sheet->getStyle('G28')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G28')->getFont()->setSize(11)->setBold(true);
        $totalEquity += $amount;
        
        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Cumulative earnings - appropriated', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B29', 'Cumulative earnings - appropriated');
        $sheet->mergeCells("B29:E29");
        $sheet->getStyle('B29')->getFont()->setSize(11);
        $sheet->setCellValue('G29', " " . number_format($amount, 2));
        $sheet->getStyle('G29')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G29')->getFont()->setSize(11)->setBold(true);
        $totalEquity += $amount;

        if($balance_sheets = $this->purchase_item->get_by_balance_sheet('Cumulative earnings - unappropriated', $date_from, $date_to)){
            $amount = $balance_sheets[0]->total;
        }else {
            $amount = 0;
        }
        $sheet->setCellValue('B30', 'Cumulative earnings - unappropriated');
        $sheet->mergeCells("B30:E30");
        $sheet->getStyle('B30')->getFont()->setSize(11);
        $sheet->setCellValue('G30', " " . number_format($amount, 2));
        $sheet->getStyle('G30')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G30')->getFont()->setSize(11)->setBold(true);
        $totalEquity += $amount;

        $sheet->setCellValue('B31', 'Total Equity');
        $sheet->getRowDimension('31')->setRowHeight(18);
        $sheet->mergeCells("B31:D31");
        $sheet->getStyle('B31')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('B31')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('G31', " " . number_format($totalEquity, 2));
        $sheet->getStyle('G31')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G31')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('A32', 'TOTAL LIABILITIES AND EQUITY');
        $sheet->getRowDimension('32')->setRowHeight(21);
        $sheet->mergeCells("A32:E32");
        $sheet->getStyle('A32')->getFont()->setSize(11)->setBold(true);

        $totalLiabilitiesAndEquity = $totalNoncurrentLiabilities + $totalEquity;
        $sheet->setCellValue('G32', "₱ " . number_format($totalLiabilitiesAndEquity, 2));
        $sheet->getStyle('G32')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('G32')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->getRowDimension('33')->setRowHeight(10);

        $sheet->setCellValue('B38', 'Client name: ');
        $sheet->mergeCells("B38:C38");
        $sheet->getStyle('B38')->getFont()->setSize(11)->setItalic(true)->setBold(true);

        $sheet->setCellValue('B39', 'Year end: ');
        $sheet->mergeCells("B39:C39");
        $sheet->getStyle('B39')->getFont()->setSize(11)->setItalic(true)->setBold(true);

        $sheet->setCellValue('B40', 'Section: ');
        $sheet->mergeCells("B40:C40");
        $sheet->getStyle('B40')->getFont()->setSize(11)->setItalic(true)->setBold(true);

        $sheet->setCellValue('D38', 'Leyte Agri Ventures Corporations');
        $sheet->mergeCells("D38:F38");
        $sheet->getRowDimension('38')->setRowHeight(25);
        $sheet->getStyle('D38')->getFont()->setSize(11)->setBold(true);
        $sheet->getStyle('D38')->getAlignment()->setWrapText(true);

        $sheet->setCellValue('D39', $subtitle);
        $sheet->mergeCells("D39:F39");
        $sheet->getStyle('D39')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('D40', 'BALANCE SHEET');
        $sheet->mergeCells("D40:F40");
        $sheet->getStyle('D40')->getFont()->setSize(11)->setBold(true);

        $sheet->setCellValue('G38', 'Prepared by/Date: ');
        $sheet->getStyle('G38')->getFont()->setSize(11)->setItalic(true)->setBold(true);
        $sheet->getStyle('G38')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->setCellValue('G39', 'Reviewed by/Date: ');
        $sheet->getStyle('G39')->getFont()->setSize(11)->setItalic(true)->setBold(true);
        $sheet->getStyle('G39')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $writer = new Xlsx($spreadsheet);
        $writer->save(FCPATH . 'assets/Balance Sheet.xlsx');
        // die("es");
        echo json_encode(['status' => 200, 'filename' => 'Balance Sheet.xlsx']);
    }

    /**
     * Sales Report By Account Type
     */

    public function sales()
    {
        $this->title = 'Sales By Account Name';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/sales/live_sell_account_types'
        ];


        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $data = [
            'suppliers' => $suppliers
        ];
        $this->build_content('default', 'sales/live_sell_account_type', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('live_sell_account_type');
        $this->load->model('supplier');
        $this->load->model('purchase_item');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return $this->upload->display_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'applied_to' => $this->input->post('applied_to'),
            'account_name' => $this->input->post('account_name'),
            'account_type' => $this->input->post('account_type'),
            'financial_statement' => $this->input->post('financial_statement'),
            'account_nature' => $this->input->post('account_nature'),
            'balance_sheet_category' => $this->input->post('balance_sheet_category'),
            'added_by' => $_SESSION['user']->id
        ];

        if (!$insert_id = $this->live_sell_account_type->insert($values)) {
            $this->_error = 'server_error';
        }

        return $insert_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($live_sell_account_type)
    {
        $where = [
            'id' => $live_sell_account_type->id
        ];

        $values = [
            'applied_to' => $this->input->post('applied_to'),
            'account_name' => $this->input->post('account_name'),
            'account_type' => $this->input->post('account_type'),
            'financial_statement' => $this->input->post('financial_statement'),
            'account_nature' => $this->input->post('account_nature'),
            'balance_sheet_category' => $this->input->post('balance_sheet_category'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->live_sell_account_type->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($live_sell_account_type)
    {
        $where = [
            'id' => $live_sell_account_type->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->live_sell_account_type->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Get Info of Sub-usage
     */
    public function info($live_sell_account_type_id = null)
    {
        $where = [
            'id' => $live_sell_account_type_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_account_type = $this->live_sell_account_type->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Account Type is not found.'
            ];
        } else {
            $response = [
                'message' => 'Account Type is found.',
                'live_sell_account_type' => $live_sell_account_type
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of live sell sub-usage by name
     */
    public function info_by_name($live_sell_account_type_account_name = '')
    {
        $where = [
            'account_name' => urldecode($live_sell_account_type_account_name),
            'is_deleted' => 0
        ];

        if (!$live_sell_account_type = $this->live_sell_account_type->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Account Type is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Account Type is found.',
                'live_sell_sub_usage' => $live_sell_account_type
            ];
        }

        echo json_encode($response);
    }
}
