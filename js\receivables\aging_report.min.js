!function(t){"use strict";function e(e){return t.fn.addNumericCommas(e)}function n(n){var r=t(n),a=(r.find("thead th"),r.DataTable({lengthChange:!0,language:{infoFiltered:""},dom:"Bfrtlip",pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[2,3,4,5,6]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2 mr-2",text:"Download Excel",footer:!0,exportOptions:{columns:"th"}},{extend:"excel",className:"btn btn-success ml-2 mr-2",text:"Download A/R Report",footer:!0,exportOptions:{columns:function(t){return 1!==t}}}],autoWidth:!1,processing:!0,serverSide:!0,lengthMenu:[[10,25,50,100,-1],[10,25,50,100,"All"]],ajax:BASE_URI+"fetch/"+r.attr("data-package"),footerCallback:function(n,r,a,o,l){var u=this.api(),c=function(t){return"string"==typeof t&&(t=t.replace(/[\$,]/g,"")),isNaN(t)||""===t||null===t?0:parseFloat(t)},i=function(t){return u.column(t,{page:"current"}).data().reduce(function(t,e){return c(t)+c(e)},0)};[2,3,4,5,6].forEach(function(n){t(u.column(n).footer()).html(e(i(n).toFixed(2)))})}}));return a}t(function(){let e=n(".tableA");t("#tableFilter").submit(function(n){n.preventDefault();var r=t(n.currentTarget).serialize(),a=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+r;e.ajax.url(a).load(),window.history.pushState(null,null,BASE_URI+"receivables/aging_report?"+r)}).trigger("submit")})}(jQuery);