<?php
class Poc_model extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'poc';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($poc_id)
    {
        $sql = <<<EOT
SELECT 
    poc.*,
    issuer.full_name AS issuer_name,
    issuer.sig_url AS issuer_signature,
    auditor.full_name AS auditor_name,
    auditor.sig_url AS auditor_signature,
    approver.full_name AS approver_name,
    approver.sig_url AS approver_signature,
    disapprover.full_name AS disapprover_name,
    disapprover.sig_url AS disapprover_signature
FROM poc
LEFT JOIN user AS issuer ON issuer.id = poc.added_by
LEFT JOIN user AS auditor ON auditor.id = poc.audited_by
LEFT JOIN user AS approver ON approver.id = poc.approved_by
LEFT JOIN user AS disapprover ON disapprover.id = poc.disapproved_by
WHERE poc.is_deleted = 0
    AND poc.id = ?
EOT;
        $binds = [$poc_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */