<?php
class Cash_bond_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'cash_bond_item';
        parent::__construct();
    }

    /**
     * Generate Cash Bond Item
     */
    public function generate($id, $rs, $alw, $cash_bond_id, $item_id, $qty, $unit, $price, $amount, $account_type, $added_by)
    {
        $sql = <<<EOT
INSERT INTO cash_bond_item(id, rs, alw, cash_bond_id, item_id, qty, unit, price, amount, account_type, added_by) (
    SELECT ? AS id, ? AS rs, ? AS alw, invoice.id AS cash_bond_id, sales_item.id AS item_id, ? AS qty, ? AS unit, ? AS price, ? AS amount, ? AS account_type, ? AS added_by
    FROM invoice
    CROSS JOIN sales_item
    WHERE invoice.id = ?
        AND invoice.is_deleted = 0
        AND sales_item.id = ?
        AND sales_item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    cash_bond_item.id = LAST_INSERT_ID(cash_bond_item.id),
    cash_bond_item.rs = VALUES(cash_bond_item.rs),
    cash_bond_item.alw = VALUES(cash_bond_item.alw),
    cash_bond_item.qty = VALUES(cash_bond_item.qty),
    cash_bond_item.unit = VALUES(cash_bond_item.unit),
    cash_bond_item.price = VALUES(cash_bond_item.price),
    cash_bond_item.amount = VALUES(cash_bond_item.amount),
    cash_bond_item.account_type = VALUES(cash_bond_item.account_type),
    cash_bond_item.updated_by = VALUES(cash_bond_item.added_by),
    cash_bond_item.is_deleted = 0
EOT;
        $binds = [$id, $rs, $alw, $qty, $unit, $price, $amount, $account_type, $added_by, $cash_bond_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Cash Bond Item By ID
     */
    public function get_by_cash_bond_id($cash_bond_id)
    {
        $sql = <<<EOT
SELECT 
    cash_bond_item.*, 
    CASE
        WHEN COUNT(invoice.invoice_no) > 1 THEN GROUP_CONCAT(CONCAT_WS(', ', invoice.invoice_no))
        ELSE MAX(invoice.invoice_no)
    END AS concatenated_invoice_no
FROM cash_bond_item
LEFT JOIN invoice ON invoice.id = cash_bond_item.invoice_id
WHERE cash_bond_item.cash_bond_id = ?
    AND cash_bond_item.is_deleted = 0
EOT;
        $binds = [$cash_bond_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Cash Bond Items by Cash Bond
     */
    public function get_by_invoice($cash_bond_id)
    {
        $sql = <<<EOT
SELECT 
    cash_bond_item.*, 
    invoice.invoice_no
FROM cash_bond_item
LEFT JOIN invoice ON invoice.id = cash_bond_item.invoice_id
WHERE cash_bond_item.cash_bond_id = ?
    AND cash_bond_item.is_deleted = 0
    AND invoice.is_deleted = 0
EOT;
        $binds = [$cash_bond_id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
