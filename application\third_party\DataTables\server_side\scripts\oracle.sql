--
-- DataTables Ajax and server-side processing database (Oracle)
--
BEGIN
	EditorDelObject('datatables_demo', 'TABLE');
	EditorDelObject('datatables_demo_seq', 'SEQUENCE');
END;

CREATE TABLE datatables_demo (
	id INT PRIMARY KEY NOT NULL,
	first_name <PERSON><PERSON><PERSON><PERSON>(250),
	last_name  <PERSON><PERSON><PERSON><PERSON>(250),
	position   VARCHAR(250),
	email      VARCHAR(250),
	office     VARCHAR(250),
	start_date DATE,
	age        INT,
	salary     INT,
	seq        INT,
	extn       VARCHAR(8)
);

CREATE SEQUENCE datatables_demo_seq;

CREATE OR REPLACE TRIGGER datatables_demo_on_insert
	BEFORE INSERT ON datatables_demo
	FOR EACH ROW
	BEGIN
		SELECT datatables_demo_seq.nextval
		INTO :new.id
		FROM dual;
	END;
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( '<PERSON>', '<PERSON>', 61, 'System Architect', 320800, '25-Apr-2011', 5421, '<EMAIL>', 'Edinburgh', 2 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Garrett', 'Winters', 63, 'Accountant', 170750, '25-Jul-2011', 8422, '<EMAIL>', 'Tokyo', 22 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Ashton', 'Cox', 66, 'Junior Technical Author', 86000, '12-Jan-2009', 1562, '<EMAIL>', 'San Francisco', 6 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Cedric', 'Kelly', 22, 'Senior Javascript Developer', 433060, '29-Mar-2012', 6224, '<EMAIL>', 'Edinburgh', 41 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Airi', 'Satou', 33, 'Accountant', 162700, '28-Nov-2008', 5407, '<EMAIL>', 'Tokyo', 55 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Brielle', 'Williamson', 61, 'Integration Specialist', 372000, '02-Dec-2012', 4804, '<EMAIL>', 'New York', 21 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Herrod', 'Chandler', 59, 'Sales Assistant', 137500, '06-Aug-2012', 9608, '<EMAIL>', 'San Francisco', 46 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Rhona', 'Davidson', 55, 'Integration Specialist', 327900, '14-Oct-2010', 6200, '<EMAIL>', 'Tokyo', 50 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Colleen', 'Hurst', 39, 'Javascript Developer', 205500, '15-Sep-2009', 2360, '<EMAIL>', 'San Francisco', 26 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Sonya', 'Frost', 23, 'Software Engineer', 103600, '13-Dec-2008', 1667, '<EMAIL>', 'Edinburgh', 18 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jena', 'Gaines', 30, 'Office Manager', 90560, '19-Dec-2008', 3814, '<EMAIL>', 'London', 13 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Quinn', 'Flynn', 22, 'Support Lead', 342000, '03-Mar-2013', 9497, '<EMAIL>', 'Edinburgh', 23 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Charde', 'Marshall', 36, 'Regional Director', 470600, '16-Oct-2008', 6741, '<EMAIL>', 'San Francisco', 14 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Haley', 'Kennedy', 43, 'Senior Marketing Designer', 313500, '18-Dec-2012', 3597, '<EMAIL>', 'London', 12 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Tatyana', 'Fitzpatrick', 19, 'Regional Director', 385750, '17-Mar-2010', 1965, '<EMAIL>', 'London', 54 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Michael', 'Silva', 66, 'Marketing Designer', 198500, '27-Nov-2012', 1581, '<EMAIL>', 'London', 37 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Paul', 'Byrd', 64, 'Chief Financial Officer (CFO)', 725000, '09-Jun-2010', 3059, '<EMAIL>', 'New York', 32 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Gloria', 'Little', 59, 'Systems Administrator', 237500, '10-Apr-2009', 1721, '<EMAIL>', 'New York', 35 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Bradley', 'Greer', 41, 'Software Engineer', 132000, '13-Oct-2012', 2558, '<EMAIL>', 'London', 48 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Dai', 'Rios', 35, 'Personnel Lead', 217500, '26-Sep-2012', 2290, '<EMAIL>', 'Edinburgh', 45 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jenette', 'Caldwell', 30, 'Development Lead', 345000, '03-Sep-2011', 1937, '<EMAIL>', 'New York', 17 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Yuri', 'Berry', 40, 'Chief Marketing Officer (CMO)', 675000, '25-Jun-2009', 6154, '<EMAIL>', 'New York', 57 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Caesar', 'Vance', 21, 'Pre-Sales Support', 106450, '12-Dec-2011', 8330, '<EMAIL>', 'New York', 29 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Doris', 'Wilder', 23, 'Sales Assistant', 85600, '20-Sep-2010', 3023, '<EMAIL>', 'Sidney', 56 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Angelica', 'Ramos', 47, 'Chief Executive Officer (CEO)', 1200000, '09-Oct-2009', 5797, '<EMAIL>', 'London', 36 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Gavin', 'Joyce', 42, 'Developer', 92575, '22-Dec-2010', 8822, '<EMAIL>', 'Edinburgh', 5 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jennifer', 'Chang', 28, 'Regional Director', 357650, '14-Nov-2010', 9239, '<EMAIL>', 'Singapore', 51 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Brenden', 'Wagner', 28, 'Software Engineer', 206850, '07-Jun-2011', 1314, '<EMAIL>', 'San Francisco', 20 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Fiona', 'Green', 48, 'Chief Operating Officer (COO)', 850000, '11-Mar-2010', 2947, '<EMAIL>', 'San Francisco', 7 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Shou', 'Itou', 20, 'Regional Marketing', 163000, '14-Aug-2011', 8899, '<EMAIL>', 'Tokyo', 1 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Michelle', 'House', 37, 'Integration Specialist', 95400, '02-Jun-2011', 2769, '<EMAIL>', 'Sidney', 39 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Suki', 'Burks', 53, 'Developer', 114500, '22-Oct-2009', 6832, '<EMAIL>', 'London', 40 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Prescott', 'Bartlett', 27, 'Technical Author', 145000, '07-May-2011', 3606, '<EMAIL>', 'London', 47 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Gavin', 'Cortez', 22, 'Team Leader', 235500, '26-Oct-2008', 2860, '<EMAIL>', 'San Francisco', 52 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Martena', 'Mccray', 46, 'Post-Sales support', 324050, '09-Mar-2011', 8240, '<EMAIL>', 'Edinburgh', 8 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Unity', 'Butler', 47, 'Marketing Designer', 85675, '09-Dec-2009', 5384, '<EMAIL>', 'San Francisco', 24 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Howard', 'Hatfield', 51, 'Office Manager', 164500, '16-Dec-2008', 7031, '<EMAIL>', 'San Francisco', 38 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Hope', 'Fuentes', 41, 'Secretary', 109850, '12-Feb-2010', 6318, '<EMAIL>', 'San Francisco', 53 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Vivian', 'Harrell', 62, 'Financial Controller', 452500, '14-Feb-2009', 9422, '<EMAIL>', 'San Francisco', 30 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Timothy', 'Mooney', 37, 'Office Manager', 136200, '11-Dec-2008', 7580, '<EMAIL>', 'London', 28 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jackson', 'Bradshaw', 65, 'Director', 645750, '26-Sep-2008', 1042, '<EMAIL>', 'New York', 34 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Olivia', 'Liang', 64, 'Support Engineer', 234500, '03-Feb-2011', 2120, '<EMAIL>', 'Singapore', 4 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Bruno', 'Nash', 38, 'Software Engineer', 163500, '03-May-2011', 6222, '<EMAIL>', 'London', 3 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Sakura', 'Yamamoto', 37, 'Support Engineer', 139575, '19-Aug-2009', 9383, '<EMAIL>', 'Tokyo', 31 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Thor', 'Walton', 61, 'Developer', 98540, '11-Aug-2013', 8327, '<EMAIL>', 'New York', 11 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Finn', 'Camacho', 47, 'Support Engineer', 87500, '07-Jul-2009', 2927, '<EMAIL>', 'San Francisco', 10 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Serge', 'Baldwin', 64, 'Data Coordinator', 138575, '09-Apr-2012', 8352, '<EMAIL>', 'Singapore', 44 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Zenaida', 'Frank', 63, 'Software Engineer', 125250, '04-Jan-2010', 7439, '<EMAIL>', 'New York', 42 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Zorita', 'Serrano', 56, 'Software Engineer', 115000, '01-Jun-2012', 4389, '<EMAIL>', 'San Francisco', 27 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jennifer', 'Acosta', 43, 'Junior Javascript Developer', 75650, '01-Feb-2013', 3431, '<EMAIL>', 'Edinburgh', 49 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Cara', 'Stevens', 46, 'Sales Assistant', 145600, '06-Dec-2011', 3990, '<EMAIL>', 'New York', 15 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Hermione', 'Butler', 47, 'Regional Director', 356250, '21-Mar-2011', 1016, '<EMAIL>', 'London', 9 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Lael', 'Greer', 21, 'Systems Administrator', 103500, '27-Feb-2009', 6733, '<EMAIL>', 'London', 25 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Jonas', 'Alexander', 30, 'Developer', 86500, '14-Jul-2010', 8196, '<EMAIL>', 'San Francisco', 33 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Shad', 'Decker', 51, 'Regional Director', 183000, '13-Nov-2008', 6373, '<EMAIL>', 'Edinburgh', 43 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Michael', 'Bruce', 29, 'Javascript Developer', 183000, '27-Jun-2011', 5384, '<EMAIL>', 'Singapore', 16 );
INSERT INTO datatables_demo ( first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) VALUES ( 'Donna', 'Snider', 27, 'Customer Support', 112000, '25-Jan-2011', 4226, '<EMAIL>', 'New York', 19 );

COMMIT;
