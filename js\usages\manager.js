+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(usageID, dataTable) {
		$.when(deleteUsage(usageID)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteUsage(usageID) {
		return $.ajax({
			url: BASE_URI + 'usages/delete/' + usageID,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			alertify.confirm(
				'Are you sure you want to delete the selected usage?',
				function () {
					var usageID = event.currentTarget.getAttribute('data-id');
					deleteAndReload(usageID, dataTable);
				}
			).setHeader('<em>Delete Usage</em>');
		});
	});
}(jQuery);
