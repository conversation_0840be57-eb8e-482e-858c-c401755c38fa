<?php
class Eod_s_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_s_attachment';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_parent_id($eodp_id)
    {
        $sql = <<<EOT
SELECT 
    eod_s_attachment.*
FROM eod_s_attachment
WHERE eod_s_attachment.is_deleted = 0
    AND eod_s_attachment.eod_s_id = ?
EOT;
        $binds = [$eodp_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return false;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */