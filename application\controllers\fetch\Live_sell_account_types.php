<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Live_sell_account_types extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Account Types
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'live_sell_account_type';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_applied_to(),
            $this->_get_account_name(),
            $this->_get_live_sell_account_type(),
            $this->_get_financial_statement(),
            $this->_get_account_nature(),
            $this->_get_balance_sheet_category(),
            $this->_get_actions()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'live_sell_account_type';
            }
        ];
    }

    /**
     * Get applied to
     */
    protected function _get_applied_to()
    {
        return [
            'db' => 'applied_to',
            'dt' => 0,
            'field' => 'applied_to'
        ];
    }

    /**
     * Get account name
     */

    protected function _get_account_name()
    {
        return [
            'db' => 'account_name',
            'dt' => 1,
            'field' => 'account_name',
        ];
    }

    /**
     * Get account type
     */
    protected function _get_live_sell_account_type()
    {
        return [
            'db' => 'account_type',
            'dt' => 2,
            'field' => 'account_type'
        ];
    }

    /**
     * Get financial statement
     */
    protected function _get_financial_statement()
    {
        return [
            'db' => 'financial_statement',
            'dt' => 3,
            'field' => 'financial_statement'
        ];
    }

    /**
     * Get account nature
     */
    protected function _get_account_nature()
    {
        return [
            'db' => 'account_nature',
            'dt' => 4,
            'field' => 'account_nature'
        ];
    }

    /**
     * Get balance sheet category
     */
    protected function _get_balance_sheet_category()
    {
        return [
            'db' => 'balance_sheet_category',
            'dt' => 5,
            'field' => 'balance_sheet_category'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('live_sell_account_types/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');

                if ($_SESSION['user']->role_id === '1') {
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}