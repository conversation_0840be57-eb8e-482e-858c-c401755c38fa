<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="applied_to">Applied to</label>

						<select name="applied_to" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('applied_to');?>">
							<option value=""></option>
							<option value="Sales Income" <?=set_select('applied_to', 'Sales Income');?>>Sales Income</option>
							<option value="Purchase Order" <?=set_select('applied_to', 'Purchase Order');?>>Purchase Order</option>
						</select>	

						<div class="red-text flash-message">
							<?=form_error('applied_to');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="account_type">Account Type</label>
						
						<select name="account_type" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('account_type');?>">
							<option value=""></option>
						</select>

						
						<div class="red-text flash-message">
							<?=form_error('account_type');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="financial_statement">Financial Statement</label>

						<select name="financial_statement" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('financial_statement');?>">
							<option value=""></option>
							<option value="Balance sheet" <?=set_select('financial_statement', 'Balance sheet');?>>Balance sheet</option>
							<option value="Income statement" <?=set_select('financial_statement', 'Income statement');?>>Income statement</option>
						</select>

						<div class="red-text flash-message">
							<?=form_error('financial_statement');?>
						</div>
					</div>
					<div class="col-md-6 form-group">
						<label for="account_nature">Nature of Account</label>

						<select name="account_nature" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('account_nature');?>">
							<option value=""></option>
							<option value="Credit" <?=set_select('account_nature', 'Credit');?>>Credit</option>
							<option value="Debit" <?=set_select('account_nature', 'Debit');?>>Debit</option>
						</select>

						<div class="red-text flash-message">
							<?=form_error('account_nature');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col form-group">
						<label for="balance_sheet_category">Balance Sheet Category</label>

						<select name="balance_sheet_category" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('balance_sheet_category');?>">
							<option value=""></option>
						</select>

						<div class="red-text flash-message">
							<?=form_error('balance_sheet_category');?>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col form-group">
						<label for="account_name">Account Name</label>

						<input type="text" name="account_name" class="form-control" value="<?=set_value('account_name');?>">

						<div class="red-text flash-message">
							<?=form_error('account_name');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
			</div>
		</form>
	</div>
</div>
