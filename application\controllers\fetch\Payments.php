<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Payments extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Payments
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'payment';
        $primary_key = 'payment.id';

        $status = addslashes(urldecode($this->input->get('status', true))) ?: 'all';

        $add_row = 0;
        $with_ar = false;
        $with_pr = false;
        $with_status = false;

        if($status === 'for_pr') {
            $add_row += 1;
            $with_ar = true;
        } elseif($status === 'for_audit' || $status === 'complete_audited' || $status === 'incomplete_audited') {
            $add_row += 2;
            $with_ar = true;
            $with_pr = true;
        } elseif($status === 'all') {
            $add_row += 1;
            $with_status = true;
        }

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_ar($with_ar),
            $this->_get_pr($with_ar, $with_pr),
            $this->_get_audit(),
            $this->_get_delete(),
            $this->_get_payment_date(),
            $this->_get_customers(),
            $this->_get_amount(),
            $this->_get_type(),
            $this->_get_status($with_status),
            $this->_get_actions($add_row, $status)
        ];

//         $joins = <<<EOT
// payment
// LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id AND paid_invoice.is_deleted = 0
// LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id AND invoice.is_deleted = 0
// LEFT JOIN customer ON customer.id = invoice.customer_id AND customer.is_deleted = 0
// LEFT JOIN payment_audit AS audit ON audit.payment_id = payment.id AND audit.is_deleted = 0
// LEFT JOIN payment_audit_attachment AS audit_attachment ON audit_attachment.payment_id = payment.id AND audit_attachment.is_deleted = 0
// EOT;

        $joins = <<<EOT
(
    SELECT 
        payment.*,
        audit.audit_remarks,
        audit.audit_status,
        customer.name AS customer_name,
        customer.id AS customer_id,
        COALESCE(user.full_name, '') as deleted_by
    FROM payment
    LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    LEFT JOIN customer ON customer.id = invoice.customer_id
    LEFT JOIN payment_audit AS audit ON audit.payment_id = payment.id AND audit.is_deleted = 0
    LEFT JOIN payment_audit_attachment AS audit_attachment ON audit_attachment.payment_id = payment.id AND audit_attachment.is_deleted = 0
    LEFT JOIN user ON user.id = payment.updated_by
    WHERE paid_invoice.doc_type = 'invoice'

    UNION ALL

    SELECT
        payment.*,
        audit.audit_remarks,
        audit.audit_status,
        customer.name AS customer_name,
        customer.id AS customer_id,
        COALESCE(user.full_name, '') as deleted_by
    FROM payment
    LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
    LEFT JOIN cash_bond ON cash_bond.id = paid_invoice.invoice_id
    LEFT JOIN customer ON customer.id = cash_bond.customer_id
    LEFT JOIN payment_audit AS audit ON audit.payment_id = payment.id AND audit.is_deleted = 0
    LEFT JOIN payment_audit_attachment AS audit_attachment ON audit_attachment.payment_id = payment.id AND audit_attachment.is_deleted = 0
    LEFT JOIN user ON user.id = payment.updated_by
    WHERE paid_invoice.doc_type = 'cash_bond'
) AS payment
EOT;

        $where = <<<EOT
1

EOT;
    
    if ($status === 'voided') {
        $where .= <<<EOT

AND payment.is_deleted = 1
EOT;
    } elseif ($status === 'for_ar') {
        $where .= <<<EOT

AND payment.type != 'cash' 
AND payment.ar_no IS NULL
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'for_pr') {
        $where .= <<<EOT

AND ((payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL))
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'acknowledgement_receipt') {
        $where .= <<<EOT

AND payment.ar_no IS NOT NULL
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'payment_receipt') {
        $where .= <<<EOT

AND payment.pr_no IS NOT NULL
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'for_audit') {
        $where .= <<<EOT

AND ((payment.ar_no IS NOT NULL AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NULL) OR (payment.type = 'cash' AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NULL))
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'incomplete_audited') {
        $where .= <<<EOT

AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NOT NULL AND payment.audit_status = 'Incomplete'
AND payment.is_deleted = 0
EOT;
    } elseif ($status === 'complete_audited') {
        $where .= <<<EOT

AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NOT NULL AND payment.audit_status = 'Complete'
AND payment.is_deleted = 0
EOT;
    }

    $from = $this->input->get('from', true) ? date("Y-m-d", strtotime($this->input->get('from', true))) : null;
    $to = $this->input->get('to', true) ? date("Y-m-d", strtotime($this->input->get('to', true))) : null;

    if (!empty($from) && !empty($to)) {
        $where .= <<<EOT

AND IFNULL(payment.payment_date, payment.added_on) BETWEEN "{$from}" AND "{$to}"
EOT;
        }

    if($type = $this->input->get('payment_type')) {
        $where .= <<<EOT

AND payment.type = "$type"
EOT;
    }

    if(!empty($customer_id = $this->input->get('customer'))) {
        $where .= <<<EOT

AND payment.customer_id = '{$customer_id}'
EOT;
    }

        $group_by = <<<EOT
payment.id
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'payment.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'payment.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'payment';
            }
        ];
    }

    /**
     * Get class
     */
    protected function _get_ar($with_ar)
    {
        return [
            'db' => 'payment.ar_no',
            'as' => 'ar_no',
            'dt' => $with_ar ? 4 : 'DT_RowAR',
            'field' => 'ar_no',
        ];
    }

    protected function _get_pr($with_pr)
    {
        return [
            'db' => 'payment.pr_no',
            'as' => 'pr_no',
            'dt' => $with_pr ? 5 :'DT_RowAR',
            'field' => 'pr_no',
        ];
    }

    protected function _get_status($with_status)
    {
        return [
            'db' => "
                        CASE 
                            WHEN payment.is_deleted = 1 THEN CONCAT_WS('<br>', 'Voided', payment.deleted_by, DATE_FORMAT(payment.updated_on, '%b %d, %Y %I:%i %p'))
                            WHEN payment.type != 'cash' AND payment.ar_no IS NULL AND payment.is_deleted = 0 THEN 'For AR'
                            WHEN ((payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL)) AND payment.is_deleted = 0 THEN 'For PR'
                            WHEN ((payment.ar_no IS NOT NULL AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NULL) OR (payment.type = 'cash' AND payment.pr_no IS NOT NULL AND payment.audit_remarks IS NULL)) AND payment.is_deleted = 0 THEN 'For Audit'
                            WHEN payment.pr_no IS NOT NULL AND payment.audit_remarks IS NOT NULL AND payment.audit_status = 'Incomplete' AND payment.is_deleted = 0 THEN 'Incomplete Audited'
                            WHEN payment.pr_no IS NOT NULL AND payment.audit_remarks IS NOT NULL AND payment.audit_status = 'Complete' AND payment.is_deleted = 0 THEN 'Complete Audited'
                        END
                    ",
            'as' => 'status',
            'dt' => $with_status ? 4 :'DT_RowStatus',
            'field' => 'status',
        ];
    }

    protected function _get_audit()
    {
        return [
            'db' => 'payment.audit_remarks',
            'as' => 'audit_remarks',
            'dt' => 'DT_RowAudit',
            'field' => 'audit_remarks',
        ];
    }

    protected function _get_delete()
    {
        return [
            'db' => 'payment.is_deleted',
            'as' => 'is_deleted',
            'dt' => 'DT_RowDeleted',
            'field' => 'is_deleted',
        ];
    }

    /**
     * Get payment date
     */
    protected function _get_payment_date()
    {
        return [
            'db' => 'payment.payment_date',
            'as' => 'payment_date',
            'dt' => 0,
            'field' => 'payment_date',
            'formatter' => function($d, $row){
                return date('F j, Y',strtotime($d));
            }

        ];
    }

    /**
     * Get Customers
     */
    protected function _get_customers()
    {
        return [
            // 'db' => 'GROUP_CONCAT(customer.name SEPARATOR "<br>")',
            'db' => 'payment.customer_name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer',
            'formatter' => function ($d, $row) {
                $res = '';
                $res .= '<ul>';

                $customers = explode('<br>', $d);
                // $customers = array_map('trim', $customers);
                // $customers=  array_filter($customers);

                foreach ($customers as $customer) {
                    $res .= '<li>' . $customer . '</li>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'FORMAT(payment.total_paid, 2)',
            'as' => 'amount',
            'dt' => 2,
            'field' => 'amount'
        ];
    }

    /**
     * Get type
     */
    protected function _get_type()
    {
        return [
            'db' => 'payment.type',
            'as' => 'payment_type',
            'dt' => 3,
            'field' => 'payment_type'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions($add_row, $status)
    {
        return [
            'db' => 'payment.id',
            'as' => 'actions',
            'dt' => 4 + $add_row,
            'field' => 'actions',
            'formatter' => function ($d, $row) use ($status) {
                if($status !== 'voided' && $row['is_deleted'] === '0'){
                    $res = '<div class="dropdown text-right">';
                    $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                    $res .= '<div class="dropdown-menu dropdown-menu-right">';

                    
                    $res .= anchor('payments/preview/' . $d, 'Preview', 'title="View" class="dropdown-item text-left"');

                    if ($row['audit_remarks'] !== null && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CASHIER])) {
                        if ($row['payment_type'] == "cash") {
                            $res .= anchor('payments/preview_payment_reciept/' . $d, 'View PR', 'title="View PR" class="dropdown-item text-left"');
                        } if ($row['payment_type'] != "cash") {
                            $res .= anchor('payments/preview_payment_proof/' . $d, 'View AR', 'title="View AR" class="dropdown-item text-left"');
                            $res .= anchor('payments/preview_payment_reciept/' . $d, 'View PR', 'title="View PR" class="dropdown-item text-left"');
                        }
                        $res .= anchor('payments/audit/' . $d, 'View Audit', 'title="View Audit" class="dropdown-item text-left"');
                        // $res .= anchor('payments/payment_or/' . $d, 'OR', 'title="OR" class="dropdown-item text-left"');
                    } else {
                        if (($row['ar_no'] == null && $row['payment_type'] != "cash") && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CASHIER])) {
                            $res .= anchor('payments/payment_proof/' . $d, 'Create AR', 'title="Create AR" class="dropdown-item text-left green-text"');
                        } elseif (($row['pr_no'] == null && $row['payment_type'] == "cash") && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CASHIER])) {
                            $res .= anchor('payments/payment_reciept/' . $d, 'Create PR', 'title="Create PR" class="dropdown-item text-left green-text"');
                        } elseif (($row['pr_no'] == null && $row['ar_no'] != null && $row['payment_type'] != "cash") && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CASHIER])) {
                            $res .= anchor('payments/payment_reciept/' . $d, 'Create PR', 'title="Create PR" class="dropdown-item text-left green-text"');
                            $res .= anchor('payments/preview_payment_proof/' . $d, 'View AR', 'title="View AR" class="dropdown-item text-left"');
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_AUDIT, ROLE_CASHIER])) {
                            if ($row['payment_type'] == "cash") {
                                $res .= anchor('payments/preview_payment_reciept/' . $d, 'View PR', 'title="View PR" class="dropdown-item text-left"');
                                $res .= anchor('payments/audit/' . $d, 'Audit', 'title="Audit" class="dropdown-item text-left green-text"');
                            } if ($row['payment_type'] != "cash") {
                                $res .= anchor('payments/preview_payment_proof/' . $d, 'View AR', 'title="View AR" class="dropdown-item text-left"');
                                $res .= anchor('payments/preview_payment_reciept/' . $d, 'View PR', 'title="View PR" class="dropdown-item text-left"');
                                $res .= anchor('payments/audit/' . $d, 'Audit', 'title="Audit" class="dropdown-item text-left green-text"');
                            }
                        }
                    }

                    if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Void</button>';
                    }
                    
                    $res .= '</div>';
                    $res .= '</div>';
                
                    return $res;
                } else {
                    return null;
                }
            }            
        ];
    }
}
