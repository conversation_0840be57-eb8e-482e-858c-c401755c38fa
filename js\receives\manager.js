+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(receiveId, dataTable) {
		$.when(deleteRow(receiveId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(receiveId) {
		return $.ajax({
			url: BASE_URI + 'receives/delete/' + receiveId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			alertify.confirm(
				'Are you sure you want to delete the selected Delivery No?',
				function () {
					var receiveId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(receiveId, dataTable);
				}
			).setHeader('<em>Delete Delivery No</em>');
		});

		$('select[name^="po_id"]').on('change', function (event) {
			var poId = event.currentTarget.value;
			$('a.btn_go').attr('href', BASE_URI + 'receives/add/' + poId);
		});
	});
}(jQuery);
