<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $purchase_order->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Farm</label>

						<select name="farm" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($farms as $farm): ?>
								<option value="<?=$farm->id;?>" <?=set_select('farm', $farm->id);?>><?=$farm->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('farm');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="schedule_hauling_date" class="control-label">Date of Hauling</label>

						<input type="text" name="schedule_hauling_date" class="form-control datepicker" value="<?=set_value('schedule_hauling_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('schedule_hauling_date');?>
						</div>
					</div>

				    <div class="col-md-4 form-group">
						<label for="schedule_hauling_time" class="control-label">Time of Hauling</label>

						<input type="time" name="schedule_hauling_time" id="salesTime" class="form-control" value="<?=set_value('schedule_hauling_time', date('H:i:s'));?>">

						<div class="red-text flash-message">
							<?=form_error('schedule_hauling_time');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="driver_name" class="control-label">Name of Driver <small class="font-italic"></small></label>

						<input type="text" name="driver_name" class="form-control" value="<?=set_value('driver_name');?>">

						<div class="red-text flash-message">
							<?=form_error('driver_name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="helper_name" class="control-label">Name of Helper <small class="font-italic"></small></label>

						<input type="text" name="helper_name" class="form-control" value="<?=set_value('helper_name');?>">

						<div class="red-text flash-message">
							<?=form_error('helper_name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Items for Hauling</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 200px;">Item</th>

									<th class="text-center" style="width: 20%;">No. of Heads</th>

									<th class="text-center" style="width: 10%;">Supplier</th>

									<th class="text-center" style="width: 10%;">Estimated ALW</th>

									<th class="text-center" style="width: 10%;">Estimated Due Amount</th>

									<th class="text-center d-none" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($purchase_order_items)):
                                	$counter = 0;
                                    foreach ($purchase_order_items as $i => $purchase_order_item):
                                    $purchase_order_item_id = $purchase_order_item->id;
                            ?>
										<tr class="schedule_hauling_item">
											<td>
												<?php $schedule_hauling_item_ = 'schedule_hauling_item_' . $purchase_order_item_id;?>
												<input type="hidden" name=<?=$schedule_hauling_item_?> value="<?=set_value('schedule_hauling_item_', $purchase_order_item_id);?>">

												<?php $fdn_item = 'item_' . $purchase_order_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$purchase_order_item->item_id?>><?=$purchase_order_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_supplier = 'suppliers_' . $purchase_order_item_id;?>

												<select name="<?=$fdn_supplier;?>" class="form-control dropsearch" data-placeholder="" style="min-width: 200px; padding-right: 0px; margin-right: 0px;">
													<?php if($purchase_order_item->supplier_name === ""):?>
														<option value="" style="min-width: 200px; padding-right: 0px; margin-right: 0px;"></option>
 													<?php foreach ($suppliers AS $supplier): ?>
														<option style="min-width: 200px; padding-right: 0px; margin-right: 0px;" value="<?=$supplier->id;?>" <?=set_select('suppliers', $supplier->id);?>><?=$supplier->name?></option>
													<?php endforeach; ?>
													<?php else:?>
														<option style="min-width: 200px; padding-right: 0px; margin-right: 0px;" selected value="<?=$purchase_order_item->supplier_id?>"><?=$purchase_order_item->supplier_name?></option>
 													<?php foreach ($suppliers AS $supplier): ?>
														<option style="min-width: 200px; padding-right: 0px; margin-right: 0px;" value="<?=$supplier->id;?>" <?=set_select('suppliers', $supplier->id);?>><?=$supplier->name?></option>
													<?php endforeach; ?>
													<?php endif;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fdn_supplier);?>
												</div>
											</td>

											<td>
												<?php $fn_est_alw = 'est_alw_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_est_alw;?>" class="form-control" value="<?=set_value($fn_est_alw);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_due_amount);?>
												</div>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<!-- <button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save and Proceed</button>
				<a href="<?=site_url('purchase_orders')?>" class="btn btn-light">Close</a>
			</div>

<!-- 			<h1 class="crud-title">Past order_confirmations</h1>

			<div class="dt-asc-wrapper row" data-display="order_confirmations_by_customer">
				<table class="table table-hover tableA" data-package="order_confirmations_by_customer">
					<thead>
						<tr>
							<th>Doc No</th>
							<th style="width: 25%;">Customer</th>
							<th>order_confirmation Date</th>
							<th>Status</th>
							<th>Items</th>
						</tr>
					</thead>

					<tbody></tbody>
				</table>
			</div> -->
		</form>
	</div>
</div>
