<div class="col topbtn">
	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<label for="from" class="ml-sm-2 mr-sm-2">Date From:</label>
		<input type="text" value="<?=set_value("from", date('m/01/Y'));?>" name="from" id="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">Date To:</label>
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" id="to" class="form-control datepicker mb-2 mr-sm-2">

		<button type="submit" class="btn btn-primary custom-filter ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col" style="margin-top: 15px;">
    <button id="exportToExcel" class="btn btn-success">Download Excel By Filter</button>
    <button id="exportByYear" class="btn btn-primary">Download Excel By Month</button>
</div>

<div>
  <canvas id="myChart"></canvas>
</div>

<div class="col" style="margin-top: 20px;">
	<h3 class="crud-subtitle"><span>Revenue</span></h3>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="revenues">
		<table class="table table-hover tableA" data-package="revenues" data-col-sum="1">
			<thead>
				<tr>
					<th class="text-center">Account Name</th>
					<th class="text-center">Amount</th>
				</tr>
			</thead>

			<tbody></tbody>

			<tfoot>
				<tr>
					<th class="text-right"><b>Total : </b></th>
					<th></th>
				</tr>
			</tfoot>
		</table>
	</div>
</div>


<div class="col" style="margin-top: 20px;">
	<h3 class="crud-subtitle"><span>Expense</span></h3>
</div>

<div class="col">
	<p class="crud-subtitle"><i>Note: All POs that are approved.</i></p>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="invoice_expenses">
		<table class="table table-hover tableB" data-package="invoice_expenses" data-col-sum="1">
			<thead>
				<tr>
					<th class="text-center">Account Name</th>
					<th class="text-center">Amount</th>
				</tr>
			</thead>

			<tbody></tbody>

			<tfoot>
				<tr>
					<th class="text-right"><b>Total : </b></th>
					<th></th>
				</tr>
			</tfoot>
		</table>
	</div>
</div>

<div class="col" style="margin-top: 20px;">
    <h3 class="crud-subtitle"><span>Income</span></h3>
</div>

<div class="col">
    <div class="dt-asc-wrapper row">
        <table class="table table-hover tableC">
            <thead>
                <tr>
                    <th class="text-center">Statement Report</th>
                    <th class="text-center">Amount</th>
                </tr>
            </thead>

            <tbody>
				<tr>
					<th>Revenue</th>
					<th id="totalRevenue" class="text-right"></th>
				</tr>
				<tr>
					<th>Expense</th>
					<th id="totalExpense" class="text-right"></th>
				</tr>
			</tbody>

            <tfoot>
                <tr>
                    <th class="text-right"><b>Income : </b></th>
                    <th class="text-right totalIncome"></th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
