!function(t){"use strict";function e(e){var a=t(i());n(a,e);var o=t(".item-table"),r=o.find(".poc_item");0===r.length&&o.find(".no-item").remove(),o.find("tbody").append(a)}function n(e,n){const a=e.find('select[name^="description"]');a.select2({data:n,placeholder:a.attr("data-placeholder"),allowClear:!0,width:"100%"}),a.on("change",function(){const a=t(this).val(),i=n.find(t=>t.id===a);if(i){const t=parseFloat(i.unit_price).toFixed(2);e.find('input[name^="unit_price"]').val(t)}else e.find('input[name^="unit_price"]').val("")})}function a(){return t.ajax({url:BASE_URI+"poc/all_item_for_poc",type:"GET",dataType:"json"})}function i(){var t=o();return'<tr class="poc_item"><td><input type="hidden" name="poc_item_'+t+'" value="'+t+'"><select name="description_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="unit_price_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="total_amount_'+t+'" class="form-control" value="" min="0" step="any" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function o(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function r(){return'<tr class="no-item"><td class="text-center" colspan="5">No items added yet.</td></tr>'}function c(t){var e=parseFloat(t.find('input[name^="qty"]').val());isNaN(e)&&(e=0);var n=parseFloat(t.find('input[name^="unit_price"]').val());isNaN(n)&&(n=0);var a=n*e;return a<0&&(a=0),t.find('input[name^="total_amount"]').val(a>0?a.toFixed(2):""),a}function l(){var e=0;t(".item-table").find(".poc_item").each(function(n,a){e+=c(t(a))}),t('input[name="grand_total"]').val(e.toFixed(4)),t(".footer_total").text(t.fn.addNumericCommas(e.toFixed(2)))}function u(e){t(e.currentTarget).closest(".poc_item").remove();var n=t(e.delegateTarget),a=n.find(".poc_item");if(a.length<1){var i=t(r());n.find("tbody").html(i)}l()}function m(e){return t.ajax({url:BASE_URI+"customers/info/"+e,type:"GET",dataType:"json"})}t(function(){var n=[{id:"",text:""}];t.when(a()).then(function(t){t.item_for_poc.forEach(function(t){t.text=t.item_name,n.push(t)})}),t('select[name="customer"]').on("change",function(e){var n=t(this).val();t('input[name="company_name"]').val(""),t('input[name="address"]').val(""),t('input[name="phone_no"]').val(""),t('input[name="email"]').val(""),t('input[name="available_credit"]').val(""),t.when(m(n)).then(function(e){var n=e.customer.available_credit;(isNaN(n)||null===n)&&(n=0),t('input[name="company_name"]').val(e.customer.company_name),t('input[name="address"]').val(e.customer.address),t('input[name="phone_no"]').val(e.customer.phone_no),t('input[name="email"]').val(e.customer.email),t('input[name="available_credit"]').val(t.fn.addNumericCommas(n))});var a=t("#btnSubmit");""!==n?(a.prop("disabled",!1),t(".item-table").find("tbody").html(t(r()))):a.prop("disabled",!0)}),t(".action-add-item").click(function(){e(n)}),t('input[name="other_services"]').on("click",function(e){var n=t('input[name="other_services_field"]'),a=t(e.currentTarget).prop("checked");a?(n.prop("readonly",!1),n.val("")):(n.prop("readonly",!0),n.val(""))}),t(".item-table").on("click",".action-delete-item",function(t){u(t)}).on("input",'input[name^="qty_"]',l).on("input",'input[name^="unit_price_"]',l),l()})}(jQuery);