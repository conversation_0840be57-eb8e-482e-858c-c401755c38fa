$(document).ready(function() {
    'use strict';

    // Add SKU row function
    function addSkuRow() {
        // Get the SKU value from the first row
        var firstRowSku = $('#skusTableBody .sku-row:first input[name="sku[]"]').val() || '';

        // Create new row with the same SKU value
        var $newRow = $(skuRowTemplate(firstRowSku));
        $('#skusTableBody').append($newRow);


    }

    // Add Giblets row function
    function addGibletsRow() {
        // Get the SKU value from the first row of Giblets table
        var firstRowSku = $('#gibletsTableBody .giblets-row:first input[name="giblets_sku[]"]').val() || '';

        // Create new row with the same SKU value
        var $newRow = $(gibletsRowTemplate(firstRowSku));
        $('#gibletsTableBody').append($newRow);


    }

    // SKU row template with auto-fill SKU value
    function skuRowTemplate(skuValue) {
        skuValue = skuValue || '';
        return '<tr class="sku-row">' +
                    '<td class="text-center" style="vertical-align: middle;">' +
                        '<input type="text" class="form-control" name="sku[]" value="' + skuValue + '">' +
                    '</td>' +
                    '<td>' +
                        '<input type="text" class="form-control" name="size[]" value="">' +
                    '</td>' +
                    '<td>' +
                        '<input type="text" class="form-control" name="range[]" value="">' +
                    '</td>' +
                    '<td>' +
                        '<input type="text" class="form-control" name="by[]" value="">' +
                    '</td>' +
                    '<td class="text-center" style="vertical-align: middle;">' +
                        '<button type="button" class="btn btn-danger btn-sm action-delete-item">Delete</button>' +
                    '</td>' +
                '</tr>';
    }

    // Giblets row template with auto-fill SKU value
    function gibletsRowTemplate(skuValue) {
        skuValue = skuValue || '';
        return '<tr class="giblets-row">' +
                    '<td class="text-center" style="vertical-align: middle;">' +
                        '<input type="text" class="form-control" name="giblets_sku[]" value="' + skuValue + '">' +
                    '</td>' +
                    '<td>' +
                        '<input type="text" class="form-control" name="giblets_by[]" value="">' +
                    '</td>' +
                    '<td class="text-center" style="vertical-align: middle;">' +
                        '<button type="button" class="btn btn-danger btn-sm action-delete-giblets">Delete</button>' +
                    '</td>' +
                '</tr>';
    }

    // Event handlers - SINGLE click handlers only
    $('#addSkuRow').on('click', function() {
        addSkuRow();
    });

    $('#addGibletsRow').on('click', function() {
        addGibletsRow();
    });

    // Event handlers for Delete buttons
    $(document).on('click', '.action-delete-item', function() {
        var skuItems = $('#skusTableBody .sku-row');
        if (skuItems.length > 1) {
            $(this).closest('.sku-row').remove();
        } else {
            alert('At least one SKU row is required.');
        }
    });

    $(document).on('click', '.action-delete-giblets', function() {
        $(this).closest('.giblets-row').remove();
    });



    // Auto-fill functionality when user types in first row
    $(document).on('input', 'input[name="sku[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');

        // If this is the first row, update all other rows in the same table
        var $currentRow = $(this).closest('.sku-row');
        var $firstRow = $('#skusTableBody .sku-row:first');

        if ($currentRow.is($firstRow)) {
            var newSkuValue = $(this).val();
            $('#skusTableBody .sku-row:not(:first) input[name="sku[]"]').val(newSkuValue);
        }
    });

    $(document).on('input', 'input[name="size[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');
    });

    $(document).on('input', 'input[name="range[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');
    });

    $(document).on('input', 'input[name="by[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');
    });

    $(document).on('input', 'input[name="giblets_sku[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');

        // If this is the first row, update all other rows in the same table
        var $currentRow = $(this).closest('.giblets-row');
        var $firstRow = $('#gibletsTableBody .giblets-row:first');

        if ($currentRow.is($firstRow)) {
            var newSkuValue = $(this).val();
            $('#gibletsTableBody .giblets-row:not(:first) input[name="giblets_sku[]"]').val(newSkuValue);
        }
    });

    $(document).on('input', 'input[name="giblets_by[]"]', function() {
        $(this).removeClass('is-invalid').removeAttr('placeholder');
    });

    // Initialize Select2 for customer dropdown
    $('.customer-select').select2({
        placeholder: 'Choose a customer...',
        allowClear: true,
        width: '100%'
    });

    // Form validation for dynamic rows
    $('#skuForm').on('submit', function(e) {
        // Clear all previous error messages and styles
        $('.form-control').removeClass('is-invalid');
        $('.validation-error').remove();
        $('#customer_id').removeClass('is-invalid');

        var hasErrors = false;

        // Validate customer selection
        var customerId = $('#customer_id').val();
        if (!customerId) {
            $('#customer_id').addClass('is-invalid');
            hasErrors = true;
        }



        // Validate Carcass table rows
        $('.sku-row').each(function(index) {
            var sku = $(this).find('input[name="sku[]"]').val().trim();
            var size = $(this).find('input[name="size[]"]').val().trim();
            var range = $(this).find('input[name="range[]"]').val().trim();
            var by = $(this).find('input[name="by[]"]').val().trim();

            if (index === 0) {
                // First row - all fields are required
                if (!sku) {
                    $(this).find('input[name="sku[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
                if (!size) {
                    $(this).find('input[name="size[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
                if (!range) {
                    $(this).find('input[name="range[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
                if (!by) {
                    $(this).find('input[name="by[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
            } else {
                // Additional rows - optional, but if any field is filled, all must be filled
                if (sku || size || range || by) {
                    if (!sku) {
                        $(this).find('input[name="sku[]"]').addClass('is-invalid');
                        hasErrors = true;
                    }
                    if (!size) {
                        $(this).find('input[name="size[]"]').addClass('is-invalid');
                        hasErrors = true;
                    }
                    if (!range) {
                        $(this).find('input[name="range[]"]').addClass('is-invalid');
                        hasErrors = true;
                    }
                    if (!by) {
                        $(this).find('input[name="by[]"]').addClass('is-invalid');
                        hasErrors = true;
                    }
                }
            }
        });



        // Validate Giblets table rows (optional but if filled, all fields required)
        $('.giblets-row').each(function() {
            var gibletsSku = $(this).find('input[name="giblets_sku[]"]').val().trim();
            var gibletsBy = $(this).find('input[name="giblets_by[]"]').val().trim();

            if (gibletsSku || gibletsBy) {
                if (!gibletsSku) {
                    $(this).find('input[name="giblets_sku[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
                if (!gibletsBy) {
                    $(this).find('input[name="giblets_by[]"]').addClass('is-invalid');
                    hasErrors = true;
                }
            }
        });

        if (hasErrors) {
            e.preventDefault();
            return false;
        }

        // Check for existing SKUs in database
        var allSkus = [];
        $('.sku-row').each(function() {
            var sku = $(this).find('input[name="sku[]"]').val().trim();
            if (sku) {
                allSkus.push(sku);
            }
        });
        $('.giblets-row').each(function() {
            var gibletsSku = $(this).find('input[name="giblets_sku[]"]').val().trim();
            if (gibletsSku) {
                allSkus.push(gibletsSku);
            }
        });

        if (allSkus.length > 0) {
            e.preventDefault(); // Prevent form submission while checking

            var existingSkuChecks = [];
            var existingSkuErrors = [];

            // Check each SKU against database
            allSkus.forEach(function(sku) {
                var checkPromise = $.ajax({
                    url: BASE_URI + 'Skus/check_existing_sku',
                    type: 'POST',
                    data: { sku: sku },
                    dataType: 'json'
                }).done(function(response) {
                    if (response.exists) {
                        existingSkuErrors.push(sku);
                    }
                });
                existingSkuChecks.push(checkPromise);
            });

            // Wait for all checks to complete
            $.when.apply($, existingSkuChecks).done(function() {
                if (existingSkuErrors.length > 0) {
                    // Mark existing SKUs as invalid
                    $('.sku-row').each(function() {
                        var sku = $(this).find('input[name="sku[]"]').val().trim();
                        if (existingSkuErrors.indexOf(sku) !== -1) {
                            $(this).find('input[name="sku[]"]').addClass('is-invalid').val('').attr('placeholder', 'Type another SKU');
                        }
                    });
                    $('.giblets-row').each(function() {
                        var gibletsSku = $(this).find('input[name="giblets_sku[]"]').val().trim();
                        if (existingSkuErrors.indexOf(gibletsSku) !== -1) {
                            $(this).find('input[name="giblets_sku[]"]').addClass('is-invalid').val('').attr('placeholder', 'Type another SKU');
                        }
                    });
                } else {
                    // No existing SKUs found, submit the form
                    $('#skuForm')[0].submit();
                }
            }).fail(function() {
                // If AJAX fails, allow form submission to proceed
                $('#skuForm')[0].submit();
            });
        } else {
            // No SKUs to check, submit the form
            $('#skuForm')[0].submit();
        }
    });

    // Clear edit session when user navigates away from edit page
    if (window.location.pathname.includes('/edit/')) {
        $(window).on('beforeunload', function() {
            // Try to clear edit session, but don't block navigation if it fails
            try {
                navigator.sendBeacon(BASE_URI + 'Skus/cancel_edit');
            } catch (error) {
                // Ignore errors - session will timeout automatically
                console.log('Edit session clear failed:', error);
            }
        });
    }
});
