<?php
class Order_slip_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'order_slip_item';
        parent::__construct();
    }

    /**
     * Generate order_slip Item
     */
    public function generate($id, $order_slip_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO order_slip_item(id, order_slip_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, order_slip.id AS order_slip_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM order_slip
    CROSS JOIN item
    WHERE order_slip.id = ?
        AND order_slip.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    order_slip_item.id = LAST_INSERT_ID(order_slip_item.id),
    order_slip_item.qty = VALUES(order_slip_item.qty),
    order_slip_item.unit = VALUES(order_slip_item.unit),
    order_slip_item.updated_by = VALUES(order_slip_item.added_by),
    order_slip_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $order_slip_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get order_slip Item By ID
     */
    public function get_by_id($order_slip_item_id)
    {
        $sql = <<<EOT
SELECT order_slip_item.*, item.name AS item
FROM order_slip_item
LEFT JOIN item ON item.id = order_slip_item.item_id
WHERE order_slip_item.id = ?
    AND order_slip_item.is_deleted = 0
EOT;
        $binds = [$order_slip_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get order_slip Items by order_slip
     */
    public function get_by_order_slip($order_slip_id)
    {
        $sql = <<<EOT
SELECT order_slip_item.*, item.name AS item
FROM order_slip_item
LEFT JOIN item ON item.id = order_slip_item.item_id
WHERE order_slip_item.order_slip_id = ?
    AND order_slip_item.is_deleted = 0
EOT;
        $binds = [$order_slip_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
