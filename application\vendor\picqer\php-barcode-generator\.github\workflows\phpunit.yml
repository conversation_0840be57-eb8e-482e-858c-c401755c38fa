name: phpunit

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: ['7.3', '7.4', '8.0', '8.1', '8.2']

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        extensions: mbstring, gd, bcmath, imagick

    - name: Validate composer.json
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Run test suite
      run: composer run-script test
