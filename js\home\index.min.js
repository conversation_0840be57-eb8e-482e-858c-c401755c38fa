+function(t){"use strict";function n(n){return t.ajax({url:BASE_URI+"menu/info/"+n,type:"GET",dataType:"json"})}function e(n){var e=t("#orderItem");e.find(".modal-title").html(n.name),e.find("form").attr("action",BASE_URI+"carts/add_item/"+n.id),e.find('input[name="unit_price"]').val(n.unit_price),e.find('input[name="quantity"]').val(1),e.find('p[name="description"]').html(n.description),e.find("#totalPrice").html(n.unit_price),e.modal("show")}function a(){var n=t("#orderItem");n.modal("hide");var e=n.find("form");e[0].reset()}function r(){var n=t("#orderItem"),e=n.find("form"),a=new FormData(e[0]);return t.ajax({url:e.attr("action"),type:"POST",data:a,processData:!1,contentType:!1,dataType:"json"})}function i(){var n=t("#orderItem"),e=n.find('input[name="quantity"]').val();e=!isNaN(e)&&e>0?e:0;var a=n.find('input[name="unit_price"]').val();a=isNaN(a)?0:a;var r=e*a;n.find("#totalPrice").html(r.toFixed(2))}function o(n){var e=t(".cart-qty");e.html(n||"")}t(function(){t(".action-show-category").click(function(n){var e=n.currentTarget.getAttribute("data-show");t(".category").hide(),t(e).show()}),t(".action-add-cart").click(function(a){var r=a.currentTarget.getAttribute("data-item");t.when(n(r)).then(function(t){e(t.item)},function(t){var n=JSON.parse(t.responseText);alertify.error(n.message)})}),t("#orderItem").on("submit","form",function(n){n.preventDefault(),t.when(r()).then(function(t){alertify.success(t.message),o(t.item_count),a()},function(t){var n=JSON.parse(t.responseText);alertify.error(n.message)})}),t('input[name="quantity"]').change(i)})}(jQuery);