+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			},
      {
        className: 'text-right',
        targets:[2,3,4]
      }],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(invoiceId, dataTable, reason) {
		$.when(deleteRow(invoiceId, reason)).then(
			function (response) {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(invoiceId, reason) {
		return $.ajax({
			url: BASE_URI + 'pos_invoices/delete',
			type: 'POST',
			dataType: 'json',
			data: {
				pos_invoice_id : invoiceId,
				reason : reason
			}
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.customer').find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to void ' + name + '? <br /> Reason: <input class="reason" name="reason" type=text />',
				function () {
					var invoiceId = event.currentTarget.getAttribute('data-id');
					var reason = $(".reason").val().replace(" ", "%20");
					deleteAndReload(invoiceId, dataTable, reason);
				}
			).setHeader('<em>Void Invoice</em>');
		});
	});
}(jQuery);
