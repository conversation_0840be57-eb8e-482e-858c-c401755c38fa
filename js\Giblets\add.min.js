$(document).ready(function(){function t(){var t=$("#gibletsTableBody .giblets-row").length+1;return'<tr class="giblets-row"><td><input type="text" class="form-control" name="no[]" value="'+t+'" readonly style="background-color: #f9f9f9; cursor: not-allowed;"></td><td><input type="text" class="form-control" name="pcs[]" value=""></td><td><input type="text" class="form-control" name="kgs[]" value=""></td><td class="text-center" style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button></td></tr>'}function e(){$("#gibletsTableBody .giblets-row").each(function(t){var e=t+1;$(this).find('input[name="no[]"]').val(e).prop("readonly",!0).css({"background-color":"#f9f9f9",cursor:"not-allowed"})}),$('#gibletsTableBody .giblets-row:first input[name="no[]"]').css("font-weight","bold")}function o(){var e=$("#gibletsTableBody .giblets-row:first"),o=e.find("td").length;$("#gibletsTableBody .giblets-row:not(:first)").each(function(){var e=$(this),n=e.find("td").length;n!==o&&e.replaceWith(t())}),n()}function n(){$("#gibletsTableBody .giblets-row").each(function(t){var e=t+1;$(this).find('input[name="no[]"]').val(e)})}$("#addGibletsRow").click(function(){var o=t();$("#gibletsTableBody").append(o),e()}),setTimeout(function(){o(),e()},100),$("#gibletsTable").on("click",".action-delete-row",function(){var t=$("#gibletsTableBody .giblets-row").length;t>1?($(this).closest(".giblets-row").remove(),n()):alert("At least one row is required.")})});