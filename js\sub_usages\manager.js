+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(subUsageID, dataTable) {
		$.when(deleteUsage(subUsageID)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteUsage(subUsageID) {
		return $.ajax({
			url: BASE_URI + 'sub_usages/delete/' + subUsageID,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			alertify.confirm(
				'Are you sure you want to delete the selected sub-usage?',
				function () {
					var subUsageID = event.currentTarget.getAttribute('data-id');
					deleteAndReload(subUsageID, dataTable);
				}
			).setHeader('<em>Delete Sub-usage</em>');
		});
	});
}(jQuery);
