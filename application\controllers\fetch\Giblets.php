<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Giblets extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Giblets
     */
    public function index()
    {
        // Get request parameters
        $draw = intval($this->input->get_post('draw'));
        $start = intval($this->input->get_post('start'));
        $length = intval($this->input->get_post('length'));
        $search_value = $this->input->get_post('search')['value'];

        // Get all giblets records
        $this->db->select('
            giblets.id as giblets_id,
            giblets.sku,
            giblets.size,
            giblets.range,
            giblets.no,
            giblets.pcs,
            giblets.kgs,
            giblets.added_on,
            giblets.added_by
        ');
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        $this->db->order_by('giblets.added_on DESC');

        // Apply search filter
        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('giblets.sku', $search_value);
            $this->db->or_like('giblets.size', $search_value);
            $this->db->or_like('giblets.range', $search_value);
            $this->db->group_end();
        }

        // Get total count before pagination
        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results();

        // Apply pagination - if length is -1, return all records
        if ($length > 0) {
            $this->db->limit($length, $start);
        }

        // Execute query
        $query = $this->db->get();
        $data = [];
        $grouped_data = [];

        if ($query->num_rows() > 0) {
            // Group data by SKU, Size, Range combination
            foreach ($query->result() as $row) {
                $key = $row->sku . '|' . $row->size . '|' . $row->range;

                if (!isset($grouped_data[$key])) {
                    // First occurrence - create new entry
                    $grouped_data[$key] = [
                        'giblets_id' => $row->giblets_id,
                        'sku' => $row->sku,
                        'size' => $row->size,
                        'range' => $row->range,
                        'no' => $row->no,
                        'pcs' => (int)$row->pcs,
                        'kgs' => (float)$row->kgs,
                        'count' => 1
                    ];
                } else {
                    // Duplicate found - merge data
                    $grouped_data[$key]['pcs'] += (int)$row->pcs;
                    $grouped_data[$key]['kgs'] += (float)$row->kgs;
                    $grouped_data[$key]['count']++;
                    // Keep the first NO value, but could be modified as needed
                }
            }

            // Convert grouped data to final format
            foreach ($grouped_data as $group) {
                $data[] = [
                    'DT_RowId' => 'giblets_' . $group['giblets_id'],
                    'DT_RowClass' => 'giblets',
                    'sku' => $group['sku'],
                    'size' => $group['size'],
                    'range' => $group['range'],
                    'no' => $group['no'],
                    'pcs' => $group['pcs'],
                    'kgs' => number_format($group['kgs'], 2),
                    'actions' => $this->_generate_actions($group['giblets_id'])
                ];
            }
        }

        // Return DataTables response
        $response = [
            'draw' => $draw,
            'recordsTotal' => $this->_get_total_giblets(),
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        exit(json_encode($response));
    }

    /**
     * Generate actions for a giblets record
     */
    protected function _generate_actions($giblets_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])) {
            $res .= anchor('Giblets/edit/' . $giblets_id, 'Edit', 'title="Edit" class="dropdown-item text-left"');
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $giblets_id . '">Delete</button>';
        }

        $res .= '</div></div>';
        return $res;
    }

    /**
     * Get total giblets records
     */
    protected function _get_total_giblets()
    {
        $this->db->from('giblets');
        $this->db->where('giblets.is_deleted', 0);
        return $this->db->count_all_results();
    }
}
