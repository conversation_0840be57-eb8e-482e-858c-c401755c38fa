<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<div class="w-100">
            	<h5 class="crud-subtitle"><span>Purchase Order Info</span></h5>

				<div class="row">
                    <!-- <div class="col-md-4 form-group">
						<label for="customer" class="font-weight-bold">Customer</label>
						<p class="underlined"><?=$purchase_order->customer;?></p>
					</div> -->

					<div class="col-md-4 form-group">
						<label for="purchase_order_date" class="font-weight-bold">Purchase Date</label>
						<p class="underlined"><?=date('m/d/Y', strtotime($purchase_order->purchase_order_date));?></p>
					</div>

                    <div class="col-md-4 form-group">
						<label for="purchase_order_date" class="font-weight-bold">Date Covered</label>
						<p class="underlined"><?=date('m/d/Y', strtotime($purchase_order->date_covered));?></p>
					</div>

                    <div class="col-md-4 form-group">
						<label for="supplier" class="font-weight-bold">Supplier</label>
						<p class="underlined"><?=$purchase_order->supplier;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="usage" class="font-weight-bold">Usage</label>
						<p class="underlined"><?=$purchase_order->usage_name;?></p>
					</div>

                    <div class="col-md-4 form-group">
						<label for="usage" class="font-weight-bold">Sub-Usage</label>
						<p class="underlined"><?=$purchase_order->sub_usage_name;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="font-weight-bold">Remarks</label>
						<p class="underlined"><?=$purchase_order->remarks;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="font-weight-bold">Requisitioner</label>
						<p class="underlined"><?=$purchase_order->requisitioner;?></p>
					</div>

					<!-- <div class="col-md-12 form-group">
						<label for="attachment" class="control-label">Attachment</label>

						<div class="card">
							<div class="card-body">
								<ul>
									<?php foreach (array_filter(explode('🔥', $purchase_order->attachment)) as $attachment): ?>
										<li>
											<a href="<?=base_url('assets/pos/' . $purchase_order->id . '/' . $attachment);?>" target="blank"><?=$attachment;?></a>
										</li>
									<?php endforeach;?>
								</ul>
							</div>
						</div>
					</div> -->
				</div>

            	<h5 class="crud-subtitle"><span>List of Purchased Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
                                    <th class="text-center" style="width: 27%;">Item</th>

                                    <th class="text-center" style="width: 10%;">Qty</th>

                                    <th class="text-center" style="width: 10%;">Unit</th>

                                    <th class="text-center" style="width: 15%;">ALW</th>

                                    <th class="text-center" style="width: 15%;">Unit Price</th>

                                    <th class="text-center" style="width: 20%;">Amount</th>

									<th class="text-center" style="width: 20%;">Account Name</th>

                                    <th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                $total = 0;
                                if (!empty($purchase_order_items)):
                                    foreach ($purchase_order_items as $purchase_order_item):
                                        if ($purchase_order_item->status !== 'disapproved') {
                                            $total += $purchase_order_item->amount;
                                        }

                                    ?>

												<tr class="purchase_order_item">
													<td><?=$purchase_order_item->item;?></td>

													<td class="text-right"><?=sprintf("%.2f", $purchase_order_item->qty);?></td>

													<td class="text-right"><?=$purchase_order_item->unit;?></td>

													<td class="text-right"><?=sprintf("%.2f", $purchase_order_item->pref_alw);?></td>

													<td class="text-right "><?=sprintf("%.2f", $purchase_order_item->unit_price);?></td>

													<td class="text-right"><?=number_format($purchase_order_item->amount, 2, '.', ',');?></td>

													<td class="text"><?=$purchase_order_item->account_type;?></td>

													<td class="text-center">
														<?php
                                                                    switch ($purchase_order_item->status):
                                                                    case 'pending':
                                                                    ?>
																<button type="button" class="btn btn-sm btn-success action-approve-item" data-id="<?=$purchase_order_item->id?>">Approve</button>
																<button type="button" class="btn btn-sm btn-danger action-disapprove-item" data-id="<?=$purchase_order_item->id?>">Disapprove</button>
														<?php
                                                                    break;

                                                                    case 'approved':
                                                                    ?>
																<span class="badge badge-success">Approved</span>
																<button type="button" class="btn btn-sm btn-warning action-pending-item" data-id="<?=$purchase_order_item->id?>">Return to Pending</button>
														<?php
                                                                    break;

                                                                    case 'disapproved':
                                                                    ?>
																<span class="badge badge-danger">Disapproved</span>
																<button type="button" class="btn btn-sm btn-warning action-pending-item" data-id="<?=$purchase_order_item->id?>">Return to Pending</button>
														<?php
                                                                    break;
                                                                        endswitch;
                                                                    ?>
													</td>
												</tr>
															<?php
                                                                    endforeach;
                                                                    else:
                                                                ?>
								<tr class="no-item">
									<td class="text-center" colspan="6">No items added yet.</td>
								</tr>
							<?php
                                endif;
                                ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
						<div class="float-right">
							<strong>Total Amount: </strong><span class="total-amount"><?=number_format($total, 2, '.', ',');?></span>
						</div>
					</div>
				</div>

				<hr>

				<div class="clearfix text-center">
				    <?php
                        if ($_SESSION['user']->role_id == 1) {?>
					    <button type="button" class="btn btn-primary action-approve-purchase_order" data-id="<?=$purchase_order->id;?>">Approve</button>
					<?php } ?>
					<a href="<?=site_url('purchase_orders/edit/' . $purchase_order->id);?>" class="btn btn-warning">Edit</a>
					<a href="<?=site_url('purchase_orders')?>" class="btn btn-light">Close</a>
				</div>
			</div>
		</form>
	</div>
</div>
