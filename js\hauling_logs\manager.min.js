!function(e){"use strict";function t(t){var a=e(t),n=a.find("thead th"),r=a.DataTable({language:{infoFiltered:""},lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-left",targets:[0,1,2,3,4,8]},{className:"text-center",targets:[5,6,7]},{className:"text-right",targets:[9,10,11]},{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:{url:BASE_URI+"fetch/"+a.attr("data-package"),data:function(t){window.dateFilterApplied&&(t.date_filter=e("#date_filter").val())},error:function(e,t,a){}}});return r}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"hauling_logs/delete/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr").find("td"),i=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete the hauling log for "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Delete Hauling Log</em>")}),window.dateFilterApplied=!1,e("#date_filter").on("change",function(){var t=e(this).val();if(t){window.dateFilterApplied=!0,n.ajax.reload(),e("#preview_btn").show();var a=BASE_URI+"hauling_logs/preview?date_filter="+encodeURIComponent(t);e("#preview_btn").attr("href",a)}else window.dateFilterApplied=!1,n.ajax.reload(),e("#preview_btn").hide()}),e("#tableFilter").on("submit",function(e){e.preventDefault()}),e("#calendar-icon").on("click",function(){e("#date_filter").focus()}),e("#date_filter").trigger("change")})}(jQuery);