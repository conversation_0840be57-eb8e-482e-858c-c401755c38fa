<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cash_ins extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Payables
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice_item';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_mop(),
            $this->_get_amount()
        ];

        $joins = <<<EOT
payment
LEFT JOIN payment_detail ON payment_detail.payment_id = payment.id
EOT;

        $where = <<<EOT
payment.is_deleted = 0
EOT;

$from = $this->input->get('from', true);
$to = $this->input->get('to', true);
if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
    isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
) {
    $where .= <<<EOT

AND payment.payment_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
}

        $group_by = <<<EOT
IF(payment.type = 'cash', 'cash', payment_detail.bank_name)
EOT;
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'payment.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'payment.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'payment';
            }
        ];
    }

    /**
     * Get Account Name
     */
    protected function _get_mop()
    {
        return [
            'db' => 'IF(payment.type = "cash", "Cash", payment_detail.bank_name)',
            'as' => 'mop',
            'dt' => 0,
            'field' => 'mop',
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'SUM(payment.total_paid)',
            'as' => 'total_paid',
            'dt' => 1,
            'field' => 'total_paid',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }
}
