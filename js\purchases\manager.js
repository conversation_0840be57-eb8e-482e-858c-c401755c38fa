+(function ($, fns) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			lengthChange: false,
			dom: 'Brtlip',
			info: false,
			pagingType: "full_numbers",
			order: [[0, "asc"]],
			columnDefs: [
				{
					className: "text-right",
					targets: 4,
				},
			],
			buttons: [
                // {
                //     extend: 'excel',
                //     className: 'btn btn-success ml-2 mr-2',
                //     text: 'Download Excel File',
                //     footer: true,
                //     exportOptions: {
                //         columns: [0,1,2,3,4,5,6,7,8,9,10]
                //     }
                // }
            ],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + encodeURI($table.attr("data-package")),
		});

		return dataTable;
	}

	function approveAndReload(purchaseId, dataTable) {
		$.when(approveRow(purchaseId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function verifyAndReload(purchaseId, dataTable) {
		$.when(verifyRow(purchaseId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approveRow(purchaseId) {
		return $.ajax({
			url: BASE_URI + "purchases/mark_approved/" + purchaseId,
			type: "POST",
			data: { purchase_id: purchaseId },
			dataType: "json",
		});
	}
	function verifyRow(purchaseId) {
		return $.ajax({
			url: BASE_URI + "purchases/mark_verified/" + purchaseId,
			type: "POST",
			data: { purchase_id: purchaseId },
			dataType: "json",
		});
	}

	function deleteAndReload(purchaseId, dataTable, reason) {
		$.when(deleteRow(purchaseId, reason)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(purchaseId, reason) {
		return $.ajax({
			url: BASE_URI + "purchases/delete/" + purchaseId,
			type: "POST",
			dataType: "json",
			data:[{
				reason : reason
			}]
		});
	}

	function pendingAndReload(purchaseId, dataTable) {
		$.when(pendingRow(purchaseId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(purchaseId) {
		return $.ajax({
			url: BASE_URI + "purchases/return_to_pending/" + purchaseId,
			type: "POST",
			dataType: "json",
		});
	}

	function urgentAndReload(purchaseId, dataTable) {
		$.when(urgentRow(purchaseId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function urgentRow(purchaseId) {
		return $.ajax({
			url: BASE_URI + "purchases/mark_urgent/" + purchaseId,
			type: "POST",
			dataType: "json",
		});
	}

	function getCheckDetails(checkId) {
		return $.ajax({
			url: BASE_URI + "checks/details/" + checkId,
			type: "POST",
			dataType: "json",
		});
	}

	function getPurchaseDetails(purchaseId){
		return $.ajax({
		url: BASE_URI + 'purchases/get_info/' + purchaseId,
		type: 'GET',
		dataType: "json"
		})
	}

	$(function () {
		var dataTable = createDataTable(".tableA");

		$(".tableA")
			.on("click", ".action-approve", function (event) {
				var cols = $(event.currentTarget).closest("tr.purchase").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to approve Purchase Order No. " +
							docNo +
							"?",
						function () {
							var purchaseId = event.currentTarget.getAttribute("data-id");
							approveAndReload(purchaseId, dataTable);
						}
					)
					.setHeader("<em>Approve Purchase Order</em>");
			})
			.on("click", ".action-verify", function (event) {
				var cols = $(event.currentTarget).closest("tr.purchase").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to Verify Purchase Order No. " + docNo + "?",
						function () {
							var purchaseId = event.currentTarget.getAttribute("data-id");
							verifyAndReload(purchaseId, dataTable);
						}
					)
					.setHeader("<em>Verify Purchase Order</em>");
			})
			.on("click", ".action-delete", function (event) {
				var cols = $(event.currentTarget).closest("tr.purchase").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify.prompt(
					"<em>Delete PO</em>",
					"Reason why you want to delete Purchase Order No. " + docNo + "?",
					"",
					function (evt, value) {
						var purchaseId = event.currentTarget.getAttribute("data-id");
						deleteAndReload(purchaseId, dataTable, value);
					},
					function () {
						alertify.error("Cancel");
					}
				);
			})
			.on("click", ".action-pending", function (event) {
				var cols = $(event.currentTarget).closest("tr.purchase").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to return to PENDING Purchase Order No. " +
							docNo +
							"?",
						function () {
							var purchaseId = event.currentTarget.getAttribute("data-id");
							pendingAndReload(purchaseId, dataTable);
						}
					)
					.setHeader("<em>Return to Pending</em>");
			})
			.on("click", ".action-urgent", function (event) {
				var cols = $(event.currentTarget).closest("tr.purchase").find("td");
				var docNo = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to mark Purchase Order No." +
							docNo +
							" as urgent?",
						function () {
							var purchaseId = event.currentTarget.getAttribute("data-id");
							urgentAndReload(purchaseId, dataTable);
						}
					)
					.setHeader("<em>Mark as Urgent</em>");
			})
			.on("click", ".action-modal", function (event) {
				var checkId = event.currentTarget.getAttribute("data-id");
				var checkNoModal = $("#checkNoModal");

				$.when(getCheckDetails(checkId)).then(
					function (response) {
						var res = "";
						var checkNumber = response.check.check_no;
						var bank = response.check.bank;
						var checkDate = new Date(response.check.check_date);
						var checkAmount = response.check.amount;
						var supplierName = response.check.supplier;

						response.check_pos.forEach(function (checkPo) {
							res +=
								"<tr>" +
								"<td>" +
								checkPo.purchase_id +
								"</td>" +
								"<td>" +
								checkPo.main_usage +
								"</td>" +
								"<td>" +
								checkPo.sub_usage +
								"</td>" +
								"<td>" +
								checkPo.total +
								"</td>" +
								"</tr>";
						});

						checkNoModal.find(".modalTable tbody").html(res);
						checkNoModal.find("input[name^='checkNumber']").val(checkNumber);
						checkNoModal.find("input[name^='bank']").val(bank);
						checkNoModal
							.find("input[name^='check_date']")
							.val(fns.format(checkDate, "MMM DD, YYYY"));
						checkNoModal.find("input[name^='checkAmount']").val(checkAmount);
						checkNoModal.find("input[name^='supplier']").val(supplierName);
					},
					function (jqXHR) {
						var response = JSON.parse(jqXHR.responseText);
						alertify.error(response.message);
					}
				);
			})
			.on("click", ".action-remarks", function (event) {
				var purchaseId = event.currentTarget.getAttribute("data-id");
				var remarksModal = $("#remarksModal");

				$.when(getPurchaseDetails(purchaseId)).then(
					function (response) {
						var res = "";
						var auditRemarks = response.data.audit_remarks;

						remarksModal.find(".modalTable tbody").html(res);
						remarksModal.find("#auditRemarks").html(auditRemarks);
					},
					function (jqXHR) {
						var response = JSON.parse(jqXHR.responseText);
						alertify.error(response.message);
					}
				);
			});

		$(".tab-link").click(function (event) {
			$(".tab-link.active").removeClass("active");
			event.currentTarget.classList.add("active");
			$("#item").val(null).trigger("change");

			var purchaseStatus = event.currentTarget.getAttribute("data-status");
			var address =
				BASE_URI + "purchases/manager?status=" + encodeURI(purchaseStatus);
			window.location.href = address;
		});

		$("#tableFilter").submit(function (event) {
			event.preventDefault();

			var purchaseStatus = $(".tab-link.active").attr("data-status");
			var queries = $(event.currentTarget).serialize();

			var url = BASE_URI + "fetch/" + $(".tableA").attr("data-package") + "&" + queries;

			dataTable.ajax.url(url).load();

			window.history.pushState(
				null,
				null,
				BASE_URI +
					"purchases/manager?status=" +
					encodeURI(purchaseStatus) +
					"&" +
					queries
			);
		}).trigger('submit');

		$('#excel').click(function (event) {
            var purchaseStatus = $(".tab-link.active").attr("data-status");
			var queries = $('#tableFilter').serialize();

			queries += '&status=' + encodeURIComponent(purchaseStatus);
			
            $.ajax({
                type: 'GET',
                url: BASE_URI + 'purchases/export_excel',
                data: queries,
                dataType: 'json',
                success: function (data) {
					window.open(BASE_URI + 'assets/excel/' + data.filename);
					var text = 'Download Excel';
					$('.btn-process').text(text);
					$('.btn-process').css('pointer-events', 'auto');
                },
                error: function (error) {
					console.error(error);
                },
            });
        });
	});
})(jQuery, dateFns);
