<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Account_types_sales extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Invoice Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice_item';
        $primary_key = 'invoice_item.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_account_type(),          
            $this->_get_amount()
        ];

        $joins = <<<EOT
invoice_item
LEFT JOIN `invoice` ON `invoice`.id = `invoice_item`.invoice_id

EOT;

        $where = <<<EOT
invoice_item.is_deleted = 0
AND invoice.status IN ('approved','printed','for release','released','audited')
EOT;
        
        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);
        if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
            isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
        ) {
            $where .= <<<EOT

AND invoice.invoice_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $group_by = "account_type";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'invoice_item.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice_item.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice_item';
            }
        ];
    }

    /**
     * Get Account Type
     */
    protected function _get_account_type()
    {
        return [
            'db' => 'invoice_item.account_type',
            'as' => 'account_type',
            'dt' => 0,
            'field' => 'account_type'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'FORMAT(IFNULL(SUM(invoice_item.amount), 0), 2)',
            'as' => 'total',
            'dt' => 1,
            'field' => 'total'
        ];
    }
}
