<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="dropdown float-right">
				<h1 class="crud-title">
					Purchase Order No. <?=$receive->purchase_id;?>
				</h1>
			</div>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Supplier Info</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<label for="name">Name</label>

						<p class="underlined" data-for="supplier"><?=$supplier->name;?></p>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Receive Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="purchase_date">Purchase Date</label>

						<p class="underlined" data-for="purchase_date"><?=date('m/d/Y', strtotime($receive->purchase_date));?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="receive_date" class="control-label">Date Received</label>

						<input type="text" name="receive_date" class="form-control datepicker" value="<?=set_value('receive_date', date('m/d/Y', strtotime($receive->receive_date)));?>">

						<div class="red-text flash-message">
							<?=form_error('receive_date');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Purchased Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th width="32%">Item</th>

									<th width="17%">Ordered Qty</th>

									<th width="17%" class="text-right">Total Received</th>

									<th width="17%" class="text-right">Balance</th>

									<th width="17%" class="text-right">Received Qty</th>
								</tr>
							</thead>

							<tbody>
								<?php
								if (!empty($receive_items)):
									foreach ($receive_items as $receive_item):
								?>
										<tr class="receive_item">
											<td>
												<p class="form-control-plaintext" data-for="item"><?=$receive_item->name;?></p>
											</td>

											<td>
												<p class="form-control-plaintext" data-for="ordered"><?=floatval($receive_item->ordered) . ' ' . $receive_item->unit;?></p>
											</td>

											<td>
												<p class="form-control-plaintext text-right" data-for="received"><?=floatval($receive_item->received - $receive_item->qty);?></p>
											</td>

											<td>
												<p class="form-control-plaintext text-right" data-for="balance"><?=floatval($receive_item->ordered - $receive_item->received + $receive_item->qty);?></p>
											</td>

											<td>
												<?php $field_name = 'received_' . $receive_item->purchase_item_id;?>
												<input type="number" name="<?=$field_name;?>" class="form-control" value="<?=set_value($field_name, floatval($receive_item->qty));?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($field_name);?>
												</div>
											</td>
										</tr>
								<?php
									endforeach;
								else:
								?>
									<tr>
										<td class="text-center" colspan="5">No item to receive.</td>
									</tr>
								<?php
								endif;
								?>
							</tbody>
						</table>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('purchases/receives/' . $receive->purchase_id);?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>