<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Invoices_history extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch invoices
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice';
        $primary_key = 'invoice.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_invoice_date(),
            $this->_get_invoice_no(),
            $this->_get_terms(),
            $this->_get_remarks(),
            $this->_get_due_date(),
            $this->_get_amount()
        ];

        $joins = <<<EOT
invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
EOT;

    $customer = $this->input->get('customer', true);

    $where = <<<EOT
invoice.customer_id = "{$customer}"
EOT;



        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'invoice.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(invoice.is_deleted = 1,"deleted",invoice.status)',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }



    /**
     * Get invoice date
     */
    protected function _get_invoice_date()
    {
        return [
            'db' => 'DATE_FORMAT(invoice.invoice_date, "%b %d, %Y")',
            'as' => 'invoice_date',
            'dt' => 0,
            'field' => 'invoice_date'
        ];
    }

    /**
     * Get invoice document number
     */
    protected function _get_invoice_no()
    {
        return [
            'db' => 'invoice.invoice_no',
            'as' => 'doc_no',
            'dt' => 1,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get terms
     */
    protected function _get_terms()
    {
        return [
            'db' => 'invoice.terms',
            'dt' => 2,
            'field' => 'terms',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get Remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'invoice.remarks',
            'dt' => 3,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return nl2br($d);
            }
        ];
    }

    /**
     * Get due date
     */
    protected function _get_due_date()
    {
        return [
            'db' => 'DATE_FORMAT(invoice.due_date, "%b %d, %Y")',
            'as' => 'due_date',
            'dt' => 4,
            'field' => 'due_date'
        ];
    }

    /**
     * Get Invoice Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'invoice.total',
            'as' => 'amount',
            'dt' => 5,
            'field' => 'amount',
            'formatter' => function($d, $row){
                return number_format($d,2);
            }
        ];
    }

}
