<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	theme: "light1", // "light1", "ligh2", "dark1", "dark2"
	animationEnabled: true,
	title: {
		text: "Company Finance"
	},
	axisY: {
		title: "Amount (in USD)",
		prefix: "$",
		lineThickness: 0,
		suffix: "k"
	},
	data: [{
		type: "waterfall",
		indexLabel: "{y}",
		indexLabelFontColor: "#EEEEEE",
		indexLabelPlacement: "inside",
		yValueFormatString: "#,##0k",
		dataPoints: [
			{ label: "Sales",  y: 1273 },
			{ label: "Service", y: 623 },
			{ label: "Total Revenue", isIntermediateSum: true},
			{ label: "Research", y: -150 },
			{ label: "Marketing",  y: -226 },
			{ label: "Salaries", y: -632 },
			{ label: "Operating Income", isCumulativeSum: true },
			{ label: "Taxes",  y: -264 },
			{ label: "Net Income",  isCumulativeSum: true }
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>