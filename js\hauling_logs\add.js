$(function() {

    // Function to format date and time for display
    function formatDateTime(dateTimeString) {
        if (!dateTimeString) return '';

        try {
            var date = new Date(dateTimeString);
            if (isNaN(date.getTime())) return dateTimeString; // Return original if invalid

            var options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            };

            return date.toLocaleDateString('en-US', options);
        } catch (e) {
            return dateTimeString; 
        }
    }

    // Handle grower farm selection to populate trips
    $('#schedule_id').on('change', function() {
        var customerId = $(this).val();
        var tripSelect = $('#schedule_list_id');

        // Reset dependent fields
        tripSelect.prop('disabled', true).html('<option value="">Loading trips...</option>');
        $('#waf_rs_no').val('');
        $('#eta_display').val('');
        $('#eta').val('');

        if (customerId) {
            // Fetch trips for selected customer (combining all schedules)
            $.ajax({
                url: BASE_URI + 'hauling_logs/get_trips_by_customer/' + customerId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.trips.length > 0) {
                        var options = '<option value="">Select Trip No.</option>';
                        $.each(response.trips, function(_, trip) {
                            var tripDisplay = 'Trip ' + trip.trip_no;
                            if (trip.formatted_date && trip.formatted_time) {
                                tripDisplay += ' - ' + trip.formatted_date + ' ' + trip.formatted_time;
                            }
                            options += '<option value="' + trip.id + '" data-waf="' + trip.waf_rs_no + '" data-eta="' + trip.eta + '">' + tripDisplay + '</option>';
                        });
                        tripSelect.html(options).prop('disabled', false);
                    } else {
                        tripSelect.html('<option value="">No trips available</option>');
                    }
                },
                error: function() {
                    tripSelect.html('<option value="">Error loading trips</option>');
                }
            });
        } else {
            tripSelect.html('<option value="">Select Trip No.</option>');
        }
    });

    // Handle trip selection to populate WAF/RS and ETA
    $('#schedule_list_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');

        if (selectedOption.val()) {
            var wafRsNo = selectedOption.data('waf');
            var eta = selectedOption.data('eta');

            $('#waf_rs_no').val(wafRsNo);
            $('#eta_display').val(formatDateTime(eta));
            $('#eta').val(eta);
        } else {
            $('#waf_rs_no').val('');
            $('#eta_display').val('');
            $('#eta').val('');
        }
    });



    // Function to validate and update weights
    function updateNetWeight() {
        var grossStr = $('#gross_weight_display').val().trim();
        var tareStr = $('#tare_weight_display').val().trim();
        var gross = parseFloat(grossStr);
        var tare = parseFloat(tareStr);
        var validGross = !isNaN(gross) && grossStr !== '';
        var validTare = !isNaN(tare) && tareStr !== '';

        // Set hidden fields - empty if display field is empty or invalid
        $('#gross_weight').val(validGross ? gross : '');
        $('#tare_weight').val(validTare ? tare : '');

        // Net weight calculation
        var net = (validGross ? gross : 0) - (validTare ? tare : 0);
    
        $('#net_weight_display').val(net.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }));
        $('#net_weight').val(net);
    }

    // Restrict weight fields to numbers only (no letters)
    $('#gross_weight_display, #tare_weight_display').on('input', function() {
        this.value = this.value.replace(/[^0-9.]/g, '');
        var parts = this.value.split('.');
        if (parts.length > 2) {
            this.value = parts[0] + '.' + parts.slice(1).join('');
        }
    });

    $('#gross_weight_display, #tare_weight_display').on('input', updateNetWeight);

});

