+(function ($) {
	"use strict";

	function addItemRow() {
		var $newRow = $(itemRow());

		var $itemTable = $(".item-table");
		var _2307_detail = $itemTable.find("._2307_detail");

		if (_2307_detail.length === 0) {
			$itemTable.find(".no-item").remove();
		}
		$itemTable.find("tbody").append($newRow);
	}

	function itemRow() {
		var rowId = makeRandomId();

		return (
			'<tr class="_2307_detail">' +
			"<td>" +
			'<input type="hidden" name="_2307_detail_' +
			rowId +
			'" value="' +
			rowId +
			'">' +
			'<select name="income_payment_subject_' +
			rowId +
			'" class="form-control" data-placeholder="">' +
			'<option value=""></option>' +
			'<option value="Rentals">Rentals</option>' +
			'<option value="Goods">Goods</option>' +
			'<option value="Services">Services</option>' +
			"</select>" +
			"</td>" +
			"<td>" +
			'<input type="text" name="atc_' +
			rowId +
			'" class="form-control" value="" readonly>' +
			"</td>" +
			"<td>" +
			'<input type="number" name="total_amount_of_income_' +
			rowId +
			'" class="form-control" value="" min="0" step="any">' +
			"</td>" +
			"<td>" +
			'<input type="number" name="tax_withheld_' +
			rowId +
			'" class="form-control" value="" readonly>' +
			"</td>" +
			'<td class="text-center">' +
			'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			"</td>" +
			"</tr>"
		);
	}

	function makeRandomId() {
		var text = "";
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return (
			'<tr class="no-item">' +
			'<td class="text-center" colspan="5">No items added yet.</td>' +
			"</tr>"
		);
	}

	function calculateOverallTotal() {
		var total = 0;
		$('[name^="tax_withheld"]').each(function () {
			var val = parseFloat($(this).val());
			if (!isNaN(val)) {
				total += val;
			}
		});
		$("#overall-total").text(total.toFixed(2));
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest("._2307_detail").remove();

		var $itemTable = $(event.delegateTarget);
		var _2307_detail = $itemTable.find("._2307_detail");
		if (_2307_detail.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find("tbody").html($emptyRow);
		}

		// Recalculate overall total
		calculateOverallTotal();
	}

	function getSupplierInfo(supplierId) {
		return $.ajax({
			url: BASE_URI + "suppliers/info/" + supplierId,
			type: "GET",
			dataType: "json",
		});
	}

	// Function to calculate tax withheld
	function calculateTaxWithheld($row) {
		var totalIncome = parseFloat(
			$row.find('[name^="total_amount_of_income"]').val()
		);
		var $taxWithheldField = $row.find('[name^="tax_withheld"]');
		var percentage = parseFloat($taxWithheldField.attr("data-percentage")) || 0;

		if (!isNaN(totalIncome) && percentage > 0) {
			var taxWithheld = (totalIncome / 1.12) * percentage;
			$taxWithheldField.val(taxWithheld.toFixed(2));
		} else {
			$taxWithheldField.val("");
		}

		// Recalculate overall total
		calculateOverallTotal();
	}

	function deleteAttachment(attachmentId, file_name) {
		return $.ajax({
			url: BASE_URI + 'module_2307/delete_attachment/' + attachmentId,
			type: 'POST',
			data: {
				// file_name: file_name
			},
			dataType: 'json'
		});
	}

	function toggleSubmitButton() {
        var supplierId = $('select[name="supplier"]').val();
        var btnelem = $("#btnSubmit");

        if (supplierId !== "") {
            btnelem.prop("disabled", false);
        } else {
            btnelem.prop("disabled", true);
        }
    }



	$(function () {
		$(".item-table")
			.on("click", ".action-delete-item", function (event) {
				removeItemRow(event);
			})
			.on("change", 'select[name^="income_payment_subject"]', function (event) {
				var $row = $(event.currentTarget).closest("._2307_detail");
				var selected = $(this).val();
				var $atcField = $row.find('[name^="atc"]');
				var $taxWithheldField = $row.find('[name^="tax_withheld"]');

				if (selected === "Rentals") {
					$atcField.val("WC100");
					$taxWithheldField.attr("data-percentage", 0.05);
				} else if (selected === "Goods") {
					$atcField.val("WC158");
					$taxWithheldField.attr("data-percentage", 0.01);
				} else if (selected === "Services") {
					$atcField.val("WC160");
					$taxWithheldField.attr("data-percentage", 0.02);
				} else {
					$atcField.val("");
					$taxWithheldField.attr("data-percentage", 0);
				}

				calculateTaxWithheld($row);	
			})
			.on("input", '[name^="total_amount_of_income_"]', function (event) {
				var $row = $(event.currentTarget).closest("._2307_detail");
				calculateTaxWithheld($row);
			}
		);

		toggleSubmitButton();
		$('select[name="supplier"]').on("change", function (event) {
			var supplierId = $(this).val();
	
			$('input[name="tin"]').val("");
			$('input[name="address"]').val("");
			$('input[name="zip_code"]').val("");
	
			$.when(getSupplierInfo(supplierId)).then(function (response) {
				$('input[name="tin"]').val(response.supplier.tin);
				$('input[name="address"]').val(response.supplier.address);
				$('input[name="zip_code"]').val(response.supplier.zip_code);
			});
	
			// Call the function when supplier selection changes
			toggleSubmitButton();
		});
	

		$(".action-add-item").click(function () {
			addItemRow();
		});

		$('.attachment-table').on('click', '.action-delete-attachment', function (event) {
			var $button = $(this);
			var $row = $button.closest('tr.attachment');
			var attachmentId = $button.data('id');
			var fileName = $button.data('filename');
		
			alertify.confirm(
                'Are you sure you want to delete ' + fileName + '? <br><br> This action cannot be undone.',
                function () {
                    $.when(deleteAttachment(attachmentId, fileName)).then(function (response) {
                        if (response.message === 'Module 2307 Attachment is successfully removed.') {
                            $row.remove();
                            alertify.success('Attachment deleted successfully.');
                        } else {
                            alertify.error('Failed to delete attachment: ' + response.message);
                        }
                    }).fail(function () {
                        alertify.error('An error occurred while deleting the attachment.');
                    });
                },
                function () {
                    // User canceled the deletion
                    alertify.message('Deletion canceled.');
                }
            ).setHeader('<em>Delete Attachment</em>');
		});

		// Function to initialize existing rows on page load
		function initializeExistingRows() {
			$("._2307_detail").each(function () {
				var $row = $(this);
				var selected = $row
					.find('select[name^="income_payment_subject"]')
					.val();
				var $atcField = $row.find('[name^="atc"]');
				var $taxWithheldField = $row.find('[name^="tax_withheld"]');

				if (selected === "Rentals") {
					$atcField.val("WC100");
					$taxWithheldField.attr("data-percentage", 0.05);
				} else if (selected === "Goods") {
					$atcField.val("WC158");
					$taxWithheldField.attr("data-percentage", 0.01);
				} else if (selected === "Services") {
					$atcField.val("WC160");
					$taxWithheldField.attr("data-percentage", 0.02);
				} else {
					$atcField.val("");
					$taxWithheldField.attr("data-percentage", 0);
				}

				calculateTaxWithheld($row);
			});
		}



		// Initialize existing rows
		initializeExistingRows();

		// Initial calculation of overall total
		calculateOverallTotal();
	});
})(jQuery);
