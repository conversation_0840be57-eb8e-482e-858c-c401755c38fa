(function(e){"use strict";function t(t){var a=e(t),n=a.find("thead th"),r=a.DataTable({language:{infoFiltered:""},scrollX:!0,autoWidth:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"banks/delete/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.bank").find("td"),o=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Delete Bank</em>")})})})(jQuery);