<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Usages extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->usage_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('usages/manager'));
    }

    /**
     * Add Usage
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('usages/add')) && $this->_attempt_add()) {
            redirect(site_url('usages/manager'));
        } else {
            $this->title = 'Add Usage';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'usages/add', $data);
        }
    }

    /**
     * Edit Usage
     */
    public function edit($usage_id)
    {
        $where = [
            'id' => $usage_id,
            'is_deleted' => 0
        ];
        $usage = $this->usage->select('', $where, 1) or show_404();

        $this->usage_name = $usage->name;

        if ($this->_validate_form($this->config->item('usages/edit')) && $this->_attempt_edit($usage)) {
            redirect(site_url('usages/manager'));
        } else {
            $this->title = 'Edit Usage';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'usage' => $usage
            ];
            $this->build_content('default', 'usages/edit', $data);
        }
    }

    /**
     * Delete Usage
     */
    public function delete($usage_id)
    {
        $where = [
            'id' => $usage_id,
            'is_deleted' => 0
        ];
        if (!$usage = $this->usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Usage is not found.'
            ];
        } elseif (!$this->_attempt_delete($usage)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Usage is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Callback: Check if usage name exists
     */
    public function _is_usage($usage_name)
    {
        $where = [
            'name' => $usage_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->usage_name) === strtolower($usage_name)) {
            return true;
        }

        return empty($this->usage->select('', $where, 1));
    }

    /**
     * Usages
     */
    public function manager()
    {
        $this->title = 'Usages';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/usages/manager'
        ];

        $this->build_content('default', 'usages/manager');
    }

    /**
     * Get Info of Usage
     */
    public function info($usage_id = '')
    {
        $where = [
            'id' => $usage_id,
            'is_deleted' => 0
        ];
        if (!$usage = $this->usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Usage is found.',
                'usage' => $usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of usage by name
     */
    public function info_by_name($usage_name = '')
    {
        $where = [
            'name' => urldecode($usage_name),
            'is_deleted' => 0
        ];

        if (!$usage = $this->usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Usage is found.',
                'usage' => $usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('usage');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'added_by' => $_SESSION['user']->id
        ];
        if (!$usage_id = $this->usage->insert($values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $usage_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($usage)
    {
        $where = [
            'id' => $usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->usage->update($where, $values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($usage)
    {
        $where = [
            'id' => $usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $usage->name . uniqid(),
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->usage->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
