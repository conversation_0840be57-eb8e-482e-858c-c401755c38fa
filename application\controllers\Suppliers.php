<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Suppliers extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('suppliers/manager'));
    }

    /**
     * Add Supplier
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('suppliers/add')) && $this->_attempt_add()) {
            redirect(site_url('suppliers/manager'));
        } else {
            $this->title = 'Add Supplier';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'suppliers/add', $data);
        }
    }

    /**
     * Edit Supplier
     */
    public function edit($supplier_id)
    {
        $where = [
            'id' => $supplier_id,
            'is_deleted' => 0
        ];
        $supplier = $this->supplier->select('', $where, 1) or show_404();

        if ($this->_validate_form($this->config->item('suppliers/edit')) && $this->_attempt_edit($supplier)) {
            redirect(site_url('suppliers/manager'));
        } else {
            $this->title = 'Edit Supplier';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'supplier' => $supplier
            ];
            $this->build_content('default', 'suppliers/edit', $data);
        }
    }

    /**
     * Delete Supplier
     */
    public function delete($supplier_id)
    {
        $where = [
            'id' => $supplier_id,
            'is_deleted' => 0
        ];
        if (!$supplier = $this->supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Supplier is not found.'
            ];
        } elseif (!$this->_attempt_delete($supplier)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Supplier is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    public function inactive($supplier_id)
    {
        $where = [
            'id' => $supplier_id,
            'is_deleted' => 0
        ];
        if (!$supplier = $this->supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Supplier is not found.'
            ];
        } elseif (!$this->_attempt_inactive($supplier)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Supplier is successfully updated.'
            ];
        }

        echo json_encode($response);
    }

    

    /**
     * Suppliers
     */
    public function manager()
    {
        $this->title = 'Suppliers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/suppliers/manager'
        ];

        $this->build_content('default', 'suppliers/manager');
    }

    /**
     * Get Info of Supplier
     */
    public function info($supplier_id = '')
    {
        $where = [
            'id' => $supplier_id,
            'is_deleted' => 0
        ];
        if (!$supplier = $this->supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Supplier is not found.'
            ];
        } else {
            $response = [
                'message' => 'Supplier is found.',
                'supplier' => $supplier
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('supplier');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $name = $this->input->post('name');
        $tin  = $this->input->post('tin');
        $address = $this->input->post('address');
        $zip_code = $this->input->post('zip_code');

        $user = $_SESSION['user']->id;

        if (!$is_added = $this->supplier->generate($name, $tin, $address, $zip_code, $user)) {
            $this->_error = 'server_error';
        }

        return $is_added;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($supplier)
    {
        $name = $this->input->post('name');
        $tin  = $this->input->post('tin');
        $address = $this->input->post('address');
        $zip_code = $this->input->post('zip_code');
        $user = $_SESSION['user']->id;
        
        $where = [
            'id' => $supplier->id,
            'is_deleted' => 0
        ];

        $values = [
            'name'       => $name,
            'tin'        =>  $tin,
            'address'    =>  $address,
            'zip_code'   =>  $zip_code,
            'updated_by' => $user
        ];

        if (!$is_updated = $this->supplier->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($supplier)
    {
        $where = [
            'id' => $supplier->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $supplier->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->supplier->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }



    /**
     * Attempt Inactive Status
     */
    protected function _attempt_inactive($supplier)
{
    $user = $_SESSION['user']->id;
    
    $newStatus = $supplier->is_active ? 0 : 1;

    $where = [
        'id' => $supplier->id,
        'is_deleted' => 0
    ];

    $values = [
        'is_active'  => $newStatus,
        'updated_by' => $user,
        'updated_on' => date('Y-m-d H:i:s'),
    ];

    if (!$is_updated = $this->supplier->update($where, $values)) {
        $this->_error = 'server_error';
    }

    return $is_updated;
}

}
