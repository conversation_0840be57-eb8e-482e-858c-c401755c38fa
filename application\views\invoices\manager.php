<div class="tab">
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_AUDIT])): ?>
		<!-- <button type="button" class="tab-link <?=$status === 'pending' ? 'active' : '';?>" data-status="pending">Pending</button> -->
		<!--<button type="button" class="tab-link <?=$status === 'for_approval' ? 'active' : '';?>" data-status="for_approval">For Approval</button>-->
		<button type="button" class="tab-link <?=$status === 'approved' ? 'active' : '';?>" data-status="approved">Approved</button>
		<button type="button" class="tab-link <?=$status === 'bad_debt' ? 'active' : '';?>" data-status="bad_debt">Bad Debts</button>
		<!-- <button type="button" class="tab-link <?=$status === 'for_review' ? 'active' : '';?>" data-status="for_review">For Review</button> -->
		<!-- <button type="button" class="tab-link <?=$status === 'reviewed' ? 'active' : '';?>" data-status="reviewed">Reviewed</button> -->
		<button type="button" class="tab-link <?=$status === 'deleted' ? 'active' : '';?>" data-status="deleted">Cancelled</button>
	<?php else:?>
		<button type="button" class="tab-link <?=$status === 'approved' ? 'active' : '';?>" data-status="approved">Approved</button>
	<?php endif;?>
</div>
<div class="col topbtn">
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_BILLING])): ?>
		<div class="dropdown float-right">
			<a href="<?= site_url('invoices/add'); ?>" class="btn btn-primary">Add New Invoice</a>
		</div>
	<?php endif;?>

	<div class="title"><?= $title; ?></div>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="invoices">
		<table class="table table-hover tableA" data-package="invoices">
			<thead>
				<tr>
					<th>Invoice No.</th>
					<th>Customer</th>
					<th>Invoice Date</th>
					<th>Terms</th>
					<th>Due Date</th>
					<th>Amount</th>
					<th>Issued By</th>
					<th>Approved By</th>
					<th>Attachments</th>
					<th></th>
				</tr>
			</thead>

			<tbody></tbody>
		</table>
	</div>
</div>
