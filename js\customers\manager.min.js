!function(e){"use strict";function t(t){var r=e(t),a=r.find("thead th"),n=r.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:a.length-1},{targets:6,render:function(e){return 1==e?'<span style="color: green;">Active</span>':'<span style="color: red;">Inactive</span>'}}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package")});return n}function r(t,r){e.when(n(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t,r){e.when(o(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"customers/delete/"+t,type:"POST",dataType:"json"})}function o(t){return e.ajax({url:BASE_URI+"customers/inactive/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete, .action-inactive",function(t){var o=e(t.currentTarget).closest("tr.customer").find("td"),i=void 0!==o[0]?o[0].innerHTML:"",s=e(t.currentTarget).hasClass("action-delete")?"delete":"inactive";if("delete"===s)alertify.confirm("Are you sure you want to delete "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,n)}).setHeader("<em>Delete Customer</em>");else if("inactive"===s){var u=e(t.currentTarget).text().trim(),c="Set Active"===u?"ACTIVE":"INACTIVE",l="Set Active"===u?"green":"red";alertify.confirm("Are you sure you want to set status of customer "+i+' to <span style="color:'+l+';">'+c+"</span>?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Set Status Customer</em>")}})})}(jQuery);