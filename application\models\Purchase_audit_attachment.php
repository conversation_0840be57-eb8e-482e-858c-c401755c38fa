<?php
class Purchase_audit_attachment extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'purchase_audit_attachment';
        parent::__construct();
    }

    /**
     * Get attachment by ID
     */
    public function get_attachment_by_purchase_id($purchase_id)
    {
        $sql = <<<EOT
SELECT *
FROM purchase_audit_attachment
WHERE is_deleted = 0
    AND purchase_id = ?
EOT;

        $binds = [$purchase_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }

    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
