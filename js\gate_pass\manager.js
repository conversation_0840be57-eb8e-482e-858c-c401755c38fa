+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
				{
					className: 'dt-action',
					targets: cols.length - 1
				}
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(gatePassId, dataTable) {
		$.when(deleteRow(gatePassId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(gatePassId) {
		return $.ajax({
			url: BASE_URI + 'gate_pass/delete/' + gatePassId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function calculateCreditLine() {
		var approvedCreditLine = $('.approvedCreditLine').val();
		var currentArBalance = $('.currentArBalance').val();

		let total = approvedCreditLine - currentArBalance;

		$('input[name="avCreditLine"]').val(total.toFixed(2));
	}

	function promptAudit(userName, creditLimit, totalReceivable) {
        return new Promise((resolve, reject) => {
            var html = '<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Approved Credit Line : </div>';
            html += '<div class="col-md-6 form-group" style="margin: 0;"><input type="number" class="approvedCreditLine" style="margin: 0;" name="approvedCreditLine" value="' + creditLimit + '" readonly></div></div> <br><br>';

			html += '<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Current AR Balance : </div>';
            html += '<div class="col-md-6 form-group" style="margin: 0;"><input type="number" class="currentArBalance" style="margin: 0;" name="currentArBalance" value="' + totalReceivable + '" readonly></div></div> <br><br>';

			html += '<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Available Credit Line : </div>';
            html += '<div class="col-md-6 form-group" style="margin: 0;"><input type="number" style="margin: 0;" name="avCreditLine" value="" readonly></div></div> <br><br>';

			html += '<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>CEC Officer : </div>';
            html += '<div class="col-md-6 form-group" style="margin: 0;"><input type="text" style="margin: 0;" class="form-control" name="officer" value="' + userName + '" readonly></div></div>';

            alertify.confirm(html,
                function (evt, value) {
                    var approvedCreditLine = $('input[name="approvedCreditLine"]').val();
                    var currentArBalance = $('input[name="currentArBalance"]').val();
                    var avCreditLine = $('input[name="avCreditLine"]').val();

					resolve({ credit_line: approvedCreditLine, current_balance: currentArBalance, available_credit: avCreditLine });
                },
                function () {
                    reject('Canceled');
                }
            ).setting({
                'reverseButtons': true,
                'closable': false,
                'title': 'Audit POC'
            }).show();

			$(document).on('input', '.approvedCreditLine, .currentArBalance', calculateCreditLine);
        });
    }

	function markAudited(gatePassId, credit_line, current_balance, available_credit) {
        var formData = new FormData();
        formData.append('gatePass_id', gatePassId);
        formData.append('credit_line', credit_line);
        formData.append('current_balance', current_balance);
        formData.append('available_credit', available_credit);

        return $.ajax({
            url: BASE_URI + 'gate_pass/audit/',
            type: 'POST',
            data: formData,
            processData: false, 
            contentType: false,
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'POC Audited.'
            };
        });
    }

	function getCustomerInfo(customerId) {
		return $.ajax({
			url: BASE_URI + 'customers/get_details/' + customerId,
			type: 'GET',
			dataType: 'json',
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');
		var creditLimit = 0;
		var totalReceivable = 0;

		$('.tableA')
			.on('click', '.action-audit', function (event) {
				var gatePassId = event.currentTarget.getAttribute('data-id');
				var userName = event.currentTarget.getAttribute('user-name');
				var customerId = event.currentTarget.getAttribute('customer-id');

				$.when(getCustomerInfo(customerId)).then(
					function (response) {
						creditLimit = parseFloat(response.customer.credit_limit);
						totalReceivable = parseFloat(response.customer.total_receivable);

						if(isNaN(creditLimit)){
							creditLimit = 0;
						}

						if(isNaN(totalReceivable)){
							totalReceivable = 0;
						}

						promptAudit(userName, creditLimit, totalReceivable)
							.then(({ credit_line, current_balance, available_credit }) => {
								return markAudited(gatePassId, credit_line, current_balance, available_credit);
							})
							.then(response => {
								alertify.success(response.message);
								window.location.reload();
							})
							.catch(error => {
								alertify.error(error);
							});

						calculateCreditLine();
					}
				);
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.gatePass').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete POC No. ' + docNo + '?',
					function () {
						var gatePassId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(gatePassId, dataTable);
					}
				).setHeader('<em>Delete POC</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/gatePass?status=' + event.currentTarget.getAttribute('data-status');
			dataTable.ajax.url(url).load();
		});
	});
}(jQuery);
