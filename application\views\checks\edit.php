<div class="sect">
	<div class="col-md-8 offset-md-2 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title"><?=$title;?></h1>

			<fieldset>
				<div class="form-row">
					<div class="col-md-12 form-group">
						<label for="bank">Bank</label>

						<select name="bank" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($banks as $bank): ?>
								<option value="<?=$bank->id;?>" <?=set_select('bank', $bank->id, $bank->id === $check->bank_id);?>><?=$bank->name;?></option>
							<?php endforeach;?>
						</select>


						<div class="red-text flash-message">
							<?=form_error('bank');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="check_date">Check Date</label>

						<input type="text" name="check_date" class="form-control datepicker" value="<?=set_value('check_date', date('m/d/Y', strtotime($check->check_date)));?>">

						<div class="red-text flash-message">
							<?=form_error('check_date');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="check_no">Check No.</label>


						<input type="hidden" name="check_id" value="<?=$check->id;?>">
						<input type="text" name="check_no" class="form-control" value="<?=set_value('check_no', $check->check_no);?>">


						<div class="red-text flash-message">
							<?=form_error('check_no');?>
						</div>
					</div>
				</div>

				<div class="form-row">
					<div class="col-md-12 form-group">
						<label for="payee">Payee</label>

						<select name="supplier" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php
                            foreach ($suppliers as $supplier): ?>
								<option value="<?=$supplier->id;?>" <?=set_select('supplier', $supplier->id, $supplier->id === $check->payee);?>><?=$supplier->name;?></option>
							<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('supplier');?>
						</div>
					</div>


					<div class="col-md-12 form-group">
						<label for="particulars">Particulars <small class="font-italic">(Optional)</small></label>

						<input type="text" name="particulars" class="form-control" value="<?=set_value('particulars', $check->particulars);?>">

						<div class="red-text flash-message">
							<?=form_error('particulars');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="sig_1">Signatory 1 <small class="font-italic">(Optional)</small></label>

						<input type="text" name="sig_1" class="form-control" value="<?=set_value('sig_1', $check->sig_1);?>">

						<div class="red-text flash-message">
							<?=form_error('sig_1');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="sig_2">Signatory 2 <small class="font-italic">(Optional)</small></label>

						<input type="text" name="sig_2" class="form-control" value="<?=set_value('sig_2', $check->sig_2);?>">

						<div class="red-text flash-message">
							<?=form_error('sig_2');?>
						</div>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>List of Purchase Orders</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="table table-bordered purchase-table">
							<thead>
								<tr>
									<th style="width: 40%;">Purchase Order</th>

									<th style="width: 25%;" class="text-right">Amount</th>

									<th style="width: 10%;" class="text-center">Details</th>

									<th style="width: 25%;"></th>
								</tr>
							</thead>

							<tbody>
					<?php
                        if (!empty($check_purchase_ids)):
                            foreach ($check_purchase_ids as $check_purchase_id):
                    ?>
								<tr class="check_purchase">
									<td>
										<?php $fdn_check_purchase = 'check_purchase_' . $check_purchase_id;?>

										<input type="hidden" name="<?=$fdn_check_purchase;?>" value="<?=set_value($fdn_check_purchase, $check_purchase_id);?>">

										<?php $fdn_purchase = 'purchase_' . $check_purchase_id;?>

										<select name="<?=$fdn_purchase;?>" class="form-control dropsearch" data-placeholder="" >
											<option></option>
											<?php foreach ($purchases as $purchase): ?>
												<option value="<?=$purchase->id;?>" <?=set_select($fdn_purchase, $purchase->id);?>><?='Purchase Order #' . $purchase->id . ' - Php' . number_format($purchase->total, 2, '.', '');?></option>
											<?php endforeach;?>
										</select>

										<div class="red-text flash-message">
											<?=form_error($fdn_check_purchase);?>
											<?=form_error($fdn_purchase);?>
										</div>
									</td>

									<td>
										<?php $fdn_amount = 'amount_' . $check_purchase_id;?>

										<input type="text" name="<?=$fdn_amount;?>" class="form-control text-right" value="<?=set_value($fdn_amount);?>" step="any">

										<div class="red-text flash-message">
											<?=form_error($fdn_amount);?>
										</div>
									</td>

									<td class="text-center">
										<a href="<?=base_url('purchases/preview/' . $check_purchase_id.'/Print');?>" id="view_po" target="_blank" name="details_<?=$check_purchase_id;?>">View</a>
									</td>

									<td class="text-right">
										<button type="button" class="btn btn-small btn-danger action-delete-purchase">Delete</button>
									</td>
								</tr>
					<?php
							endforeach;
						elseif (!empty($check_purchases)):
							foreach ($check_purchases as $check_purchase):
					?>
								<tr class="check_purchase">
									<td>
										<?php $fdn_check_purchase = 'check_purchase_' . $check_purchase->id;?>

										<input type="hidden" name="<?=$fdn_check_purchase;?>" value="<?=set_value($fdn_check_purchase, $check_purchase->id);?>">

										<?php $fdn_purchase = 'purchase_' . $check_purchase->id;?>

										<select name="<?=$fdn_purchase;?>" class="form-control dropsearch" data-placeholder="" >
											<option></option>
											<?php foreach ($purchases as $purchase): ?>
												<option value="<?=$purchase->id;?>" <?=set_select($fdn_purchase, $purchase->id, $purchase->id === $check_purchase->purchase_id);?>><?='Purchase Order #' . $purchase->id . ' - Php' . number_format($purchase->total, 2, '.', '');?></option>
											<?php endforeach;?>
										</select>

										<div class="red-text flash-message">
											<?=form_error($fdn_check_purchase);?>
											<?=form_error($fdn_purchase);?>
										</div>
									</td>

									<td>
										<?php $fdn_amount = 'amount_' . $check_purchase->id;?>

										<input type="text" name="<?=$fdn_amount;?>" class="form-control text-right" value="<?=set_value($fdn_amount, $check_purchase->amount);?>" step="any">

										<div class="red-text flash-message">
											<?=form_error($fdn_amount);?>
										</div>
									</td>

									<td class="text-center">

										<a href="<?=base_url('purchases/preview/' . $check_purchase->purchase_id.'/Print');?>" id="view_po" target="_blank" name="details_<?=$check_purchase->purchase_id;?>">View</a>
									</td>

									<td class="text-right">
										<button type="button" class="btn btn-small btn-danger action-delete-purchase">Delete</button>
									</td>
								</tr>
					<?php
							endforeach;
						else:
					?>
							<tr class="no-purchase">
								<td class="text-center" colspan="3">No purchase orders added yet.</td>
							</tr>
					<?php
						endif;
					?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-6 form-group">
						<button type="button" class="btn btn-sm btn-dark action-add-payment">Add Purchase Order</button>
					</div>

					<div class="col-6 form-group">
						<div class="row">
							<div class="col-6 text-right">
								<label for="total" class="control-label">Total Amount</label>
							</div>

							<div class="col-6">
								<input type="text" name="total" class="form-control-plaintext text-right" value="0.00" readonly="readonly" step="any">
							</div>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<?php if($_SESSION['user']->role_id === ROLE_ADMIN && $check->status === 'pending'):?>
				<button type="button" name="approve" class="btn btn-success action-approve" data-id="<?=$check->id?>" data-checkno="<?=$check->check_no?>">Approve</button>
				<?php endif;?>
				<a href="<?=site_url('checks/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>

<div class="modal fade" id="addBank">
    <div class="modal-md modal-dialog">
        <div class="modal-content">
            <form action="<?=site_url('banks/add_remote');?>" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Add Bank</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <fieldset>
						<div class="row">
							<div class="col-md-12 form-group">
								<label for="name">Name of Bank</label>

								<input type="text" name="name" value="" class="form-control">
							</div>
						</div>
					</fieldset>

                    <div class="error mr-auto"></div>
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="enrollCheck">
    <div class="modal-md modal-dialog">
        <div class="modal-content">
            <form action="<?=site_url('checks/enroll_remote');?>" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Enroll Check</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <fieldset>
						<div class="row">
							<div class="col-md-12 form-group">
								<label for="bank">Name of Bank</label>

								<input type="text" name="bank" value="" class="form-control-plaintext" readonly>
							</div>

							<div class="col-md-12 form-group">
								<label for="from">From</label>

								<input type="number" name="from" value="" class="form-control">
							</div>

							<div class="col-md-12 form-group">
								<label for="to">To</label>

								<input type="number" name="to" value="" class="form-control">
							</div>
						</div>
					</fieldset>

                    <div class="error mr-auto"></div>
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

