<!DOCTYPE HTML>
<html>
<head>  
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	title:{
		text: "Customer Satisfaction Based on Reviews"
	},
	axisY: {
		title: "Satisfied Customers",
		suffix: "%"
	},
	data: [{
		type: "stepArea",
		markerSize: 5,
		xValueFormatString: "YYYY",
		yValueFormatString: "#,##0.##'%'",
		dataPoints: [
			{ x: new Date(2010, 0), y: 34 },
			{ x: new Date(2011, 0), y: 73 },
			{ x: new Date(2012, 0), y: 78 },
			{ x: new Date(2013, 0), y: 82 },
			{ x: new Date(2014, 0), y: 70 },
			{ x: new Date(2015, 0), y: 86 },
			{ x: new Date(2016, 0), y: 80 }
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>