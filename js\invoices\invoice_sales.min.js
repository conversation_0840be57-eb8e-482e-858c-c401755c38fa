!function(e,t){"use strict";function a(t){var a=e(t),n=a.DataTable({lengthChange:!1,info:!1,paging:!1,searching:!1,order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:4}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+encodeURI(a.attr("data-package")),footerCallback:function(){var t=this.api(),a=4,n=function(e){if("number"==typeof e)return e;if("string"==typeof e){e=e.replace(/[$,]/g,"");var t=parseFloat(e);return isNaN(t)?0:t}return 0},r=t.column(a).data().reduce(function(e,t){return n(e)+n(t)},0);e(t.column(a).footer()).html(e.fn.addNumericCommas(parseFloat(r).toFixed(2)))},dom:"Bfrtip",buttons:[{extend:"excelHtml5",text:"Download Excel File",className:"btn btn-success",filename:"invoice_sales"}]});return n}e(function(){a(".tableA")})}(jQuery,dateFns);