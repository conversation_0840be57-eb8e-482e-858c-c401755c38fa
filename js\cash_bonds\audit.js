+ function ($) {
    'use strict';
 
    function promptAuditRemarks() {
        return new Promise((resolve, reject) => {
            var html = '<input type="radio" name="auditStatus" value="complete" checked> Complete &nbsp; &nbsp;';
            html += '<input type="radio" name="auditStatus" value="incomplete"> Incomplete<br>';
            html += '&nbsp;';
            html += '<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100"></textarea>';
            html += '<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, .jpeg, image/*" multiple>';

            alertify.confirm(html,
                function (evt, value) {
                    var auditRemarks = $('#auditRemarks').val();
                    var auditStatus = $('input[name="auditStatus"]:checked').val();
                    var files = $('#attachments')[0].files;
                    if (auditRemarks.trim() !== '') {
                        resolve({ remarks: auditRemarks, status: auditStatus, files: files });
                    } else {
                        reject('Missing Remarks: Audit remarks cannot be empty.');
                    }
                },
                function () {
                    reject('Audit canceled.');
                }
            ).setting({
                'reverseButtons': true,
                'closable': false,
                'title': 'Audit Cash Bond'
            }).show();
        });
    }

    function markAudited(cashBondId, auditRemarks, auditStatus, files) { // Updated to accept files parameter
        var formData = new FormData(); // Create form data object
        formData.append('cash_bond_id', cashBondId);
        formData.append('audit_remarks', auditRemarks);
        formData.append('audit_status', auditStatus);
        for (var i = 0; i < files.length; i++) { // Append each file to form data
            formData.append('attachments[]', files[i]);
        }

        return $.ajax({
            url: BASE_URI + 'cash_bonds/mark_audited/',
            type: 'POST',
            data: formData, // Send form data instead of plain object
            processData: false, // Prevent jQuery from processing the data
            contentType: false, // Prevent jQuery from setting contentType
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'Cash Bond Audited.'
            };
        });
    }

    function promptAuditUpdateRemarks(auditRemarks) {
        return new Promise((resolve, reject) => {
            var html = '<input type="radio" name="auditStatus" value="complete"> Complete &nbsp; &nbsp;';
            html += '<input type="radio" name="auditStatus" value="incomplete" checked> Incomplete<br>';
            html += '&nbsp;';
            html += '<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100">' + auditRemarks + '</textarea>';
            html += '<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>'; // Added file input with ID 'attachments'

            alertify.confirm(html,
                function (evt, value) {
                    var auditRemarks = $('#auditRemarks').val();
                    var auditStatus = $('input[name="auditStatus"]:checked').val();
                    var files = $('#attachments')[0].files; // Get uploaded files
                    if (auditRemarks.trim() !== '') {
                        resolve({ remarks: auditRemarks, status: auditStatus, files: files }); // Pass files to resolve
                    } else {
                        reject('Missing Remarks: Audit remarks cannot be empty.');
                    }
                },
                function () {
                    reject('Audit canceled.');
                }
            ).setting({
                'reverseButtons': true,
                'closable': false,
                'title': 'Audit Cash Bond'
            }).show();
        });
    }

    function updateMarkAudited(cashBondId, auditRemarks, auditStatus, files) { // Updated to accept files parameter
        var formData = new FormData(); // Create form data object
        formData.append('cash_bond_id', cashBondId);
        formData.append('audit_remarks', auditRemarks);
        formData.append('audit_status', auditStatus);
        for (var i = 0; i < files.length; i++) { // Append each file to form data
            formData.append('attachments[]', files[i]);
        }

        return $.ajax({
            url: BASE_URI + 'cash_bonds/update_mark_audited/',
            type: 'POST',
            data: formData, // Send form data instead of plain object
            processData: false, // Prevent jQuery from processing the data
            contentType: false, // Prevent jQuery from setting contentType
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'Cash Bond Audited.'
            };
        });
    }

    $(function () {
        $('#auditBtn').click(function () {
            var cashBondId = $('input[name="cash_bond_id"]').val();

            promptAuditRemarks()
                .then(({ remarks, status, files }) => {
                    return markAudited(cashBondId, remarks, status, files); // Pass files to markAudited
                })
                .then(response => {
                    alertify.success(response.message);
                    window.location.reload();
                })
                .catch(error => {
                    alertify.error(error);
                });
        });

        $('#auditUpdateBtn').click(function () {
            var cashBondId = $('input[name="cash_bond_id"]').val();
            var auditRemarks = $('input[name="audit_remarks"]').val();
    
            promptAuditUpdateRemarks(auditRemarks)
                .then(({ remarks, status, files }) => { // Include the 'status' parameter
                    return updateMarkAudited(cashBondId, remarks, status, files);
                })
                .then(response => {
                    alertify.success(response.message);
                    window.location.reload();
                })
                .catch(error => {
                    alertify.error(error);
                });
        });
    });
    
   

}(jQuery);