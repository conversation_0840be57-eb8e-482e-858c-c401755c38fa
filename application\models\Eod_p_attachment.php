<?php
class Eod_p_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_p_attachment';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_parent_id($eodp_id)
    {
        $sql = <<<EOT
SELECT 
    eod_p_attachment.*
FROM eod_p_attachment
WHERE eod_p_attachment.is_deleted = 0
    AND eod_p_attachment.eod_p_id = ?
EOT;
        $binds = [$eodp_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return false;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */