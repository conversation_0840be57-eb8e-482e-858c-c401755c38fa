!function(e,t){"use strict";function a(t){var a=e(t),r=a.DataTable({lengthChange:!1,dom:"Brtlip",info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:4}],buttons:[],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+encodeURI(a.attr("data-package"))});return r}function r(t,a){e.when(o(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t,a){e.when(c(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function o(t){return e.ajax({url:BASE_URI+"purchases/mark_approved/"+t,type:"POST",data:{purchase_id:t},dataType:"json"})}function c(t){return e.ajax({url:BASE_URI+"purchases/mark_verified/"+t,type:"POST",data:{purchase_id:t},dataType:"json"})}function i(t,a,r){e.when(u(t,r)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function u(t,a){return e.ajax({url:BASE_URI+"purchases/delete/"+t,type:"POST",dataType:"json",data:[{reason:a}]})}function s(t,a){e.when(l(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function l(t){return e.ajax({url:BASE_URI+"purchases/return_to_pending/"+t,type:"POST",dataType:"json"})}function d(t,a){e.when(f(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function f(t){return e.ajax({url:BASE_URI+"purchases/mark_urgent/"+t,type:"POST",dataType:"json"})}function p(t){return e.ajax({url:BASE_URI+"checks/details/"+t,type:"POST",dataType:"json"})}function h(t){return e.ajax({url:BASE_URI+"purchases/get_info/"+t,type:"GET",dataType:"json"})}e(function(){var o=a(".tableA");e(".tableA").on("click",".action-approve",function(t){var a=e(t.currentTarget).closest("tr.purchase").find("td"),n=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to approve Purchase Order No. "+n+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,o)}).setHeader("<em>Approve Purchase Order</em>")}).on("click",".action-verify",function(t){var a=e(t.currentTarget).closest("tr.purchase").find("td"),r=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to Verify Purchase Order No. "+r+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,o)}).setHeader("<em>Verify Purchase Order</em>")}).on("click",".action-delete",function(t){var a=e(t.currentTarget).closest("tr.purchase").find("td"),r=void 0!==a[0]?a[0].innerHTML:"";alertify.prompt("<em>Delete PO</em>","Reason why you want to delete Purchase Order No. "+r+"?","",function(e,a){var r=t.currentTarget.getAttribute("data-id");i(r,o,a)},function(){alertify.error("Cancel")})}).on("click",".action-pending",function(t){var a=e(t.currentTarget).closest("tr.purchase").find("td"),r=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to return to PENDING Purchase Order No. "+r+"?",function(){var e=t.currentTarget.getAttribute("data-id");s(e,o)}).setHeader("<em>Return to Pending</em>")}).on("click",".action-urgent",function(t){var a=e(t.currentTarget).closest("tr.purchase").find("td"),r=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to mark Purchase Order No."+r+" as urgent?",function(){var e=t.currentTarget.getAttribute("data-id");d(e,o)}).setHeader("<em>Mark as Urgent</em>")}).on("click",".action-modal",function(a){var r=a.currentTarget.getAttribute("data-id"),n=e("#checkNoModal");e.when(p(r)).then(function(e){var a="",r=e.check.check_no,o=e.check.bank,c=new Date(e.check.check_date),i=e.check.amount,u=e.check.supplier;e.check_pos.forEach(function(e){a+="<tr><td>"+e.purchase_id+"</td><td>"+e.main_usage+"</td><td>"+e.sub_usage+"</td><td>"+e.total+"</td></tr>"}),n.find(".modalTable tbody").html(a),n.find("input[name^='checkNumber']").val(r),n.find("input[name^='bank']").val(o),n.find("input[name^='check_date']").val(t.format(c,"MMM DD, YYYY")),n.find("input[name^='checkAmount']").val(i),n.find("input[name^='supplier']").val(u)},function(e){var t=JSON.parse(e.responseText);alertify.error(t.message)})}).on("click",".action-remarks",function(t){var a=t.currentTarget.getAttribute("data-id"),r=e("#remarksModal");e.when(h(a)).then(function(e){var t="",a=e.data.audit_remarks;r.find(".modalTable tbody").html(t),r.find("#auditRemarks").html(a)},function(e){var t=JSON.parse(e.responseText);alertify.error(t.message)})}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active"),e("#item").val(null).trigger("change");var a=t.currentTarget.getAttribute("data-status"),r=BASE_URI+"purchases/manager?status="+encodeURI(a);window.location.href=r}),e("#tableFilter").submit(function(t){t.preventDefault();var a=e(".tab-link.active").attr("data-status"),r=e(t.currentTarget).serialize(),n=BASE_URI+"fetch/"+e(".tableA").attr("data-package")+"&"+r;o.ajax.url(n).load(),window.history.pushState(null,null,BASE_URI+"purchases/manager?status="+encodeURI(a)+"&"+r)}).trigger("submit"),e("#excel").click(function(t){var a=e(".tab-link.active").attr("data-status"),r=e("#tableFilter").serialize();r+="&status="+encodeURIComponent(a),e.ajax({type:"GET",url:BASE_URI+"purchases/export_excel",data:r,dataType:"json",success:function(t){window.open(BASE_URI+"assets/excel/"+t.filename);var a="Download Excel";e(".btn-process").text(a),e(".btn-process").css("pointer-events","auto")},error:function(e){console.error(e)}})})})}(jQuery,dateFns);