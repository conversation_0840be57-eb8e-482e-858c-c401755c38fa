<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <form id="gibletsForm" method="post" action="<?= site_url('Giblets/add'); ?>" class="crud-box" accept-charset="utf-8">
                        <h1 class="crud-title">Add Giblets</h1>
                        <h5 class="crud-subtitle text-center"><span>Giblets</span></h5>
                        <br>
                        <?php if ($this->session->flashdata('error')): ?>
                            <div class="alert alert-danger">
                                <?= $this->session->flashdata('error'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="row mb-3">
                             <div class="col-md-4 form-group">
                                <label for="sku" class="control-label">SKU</label>
                                <input type="text" id="sku" class="form-control" name="sku">
                            </div>
                            <div class="col-md-4 form-group">
                                <label for="size" class="control-label">Size</label>
                                <input type="text" id="size" class="form-control" name="size">
                            </div>
                            <div class="col-md-4 form-group">
                                <label for="range" class="control-label">Range</label>
                                <input type="text" id="range" class="form-control" name="range">
                            </div>
                        </div>

                        <table class="table table-bordered item-table" id="gibletsTable" style="background-color: white;">
                            <thead>
                                <tr>
                                    <th style="width: 100px; text-align: center;">NO.</th>
                                    <th>PCS</th>
                                    <th>KGS</th>
                                    <th style="width: 90px; text-align: center;">Action</th>
                                </tr>
                            </thead>
                            <tbody id="gibletsTableBody">
                                <tr class="giblets-row">
                                    <td><input type="text" class="form-control" name="no[]" value="1"></td>
                                    <td><input type="text" class="form-control" name="pcs[]" value=""></td>
                                    <td><input type="text" class="form-control" name="kgs[]" value=""></td>
                                    <td class="text-center" style="vertical-align: middle;">
                                        <button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" class="btn btn-dark" id="addGibletsRow">Add</button>
                        <hr>
                        <div class="clearfix text-center">
                            <button type="submit" class="btn btn-primary">Save</button>
                            <a href="<?= site_url('Giblets'); ?>" class="btn btn-light">Close</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= base_url('js/Giblets/add.js') ?>"></script></script>