<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Job_schedules extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('job_schedules/manager'));
    }

    /**
     * Add Schedule
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('job_schedules/add')) && $this->_attempt_add()) {
            redirect(site_url('job_schedules/manager'));
        } else {
            $this->title = 'Add Schedule';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $job_schedules = $this->job_schedule->select('', $where);
            $customers = $this->customer->select('', $where);

            $data = [
                'form_error' => $form_error,
                'job_schedules' => $job_schedules,
                'customers' => $customers
            ];

            $this->build_content('default', 'job_schedules/add', $data);
        }
    }

    /**
     * Edit Schedule
     */
    public function edit($schedule_id)
    {
        $where = [
            'id' => $schedule_id,
            'is_deleted' => 0
        ];
        $job_schedule = $this->job_schedule->select('', $where, 1) or show_404(); 

        if ($this->_validate_form($this->config->item('job_schedules/edit')) && $this->_attempt_edit($job_schedule)) {
            redirect(site_url('job_schedules/manager'));
        } else {
            $this->title = 'Edit Schedule';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $customers = $this->customer->select('', $where);

            $data = [
                'form_error' => $form_error,
                'job_schedule' => $job_schedule,
                'customers' => $customers,
            ];

            $this->build_content('default', 'job_schedules/edit', $data);
        }
    }

    /**
     * Delete Schedule
     */
    public function delete($schedule_id)
    {
        $where = [
            'id' => $schedule_id,
            'is_deleted' => 0
        ];
        if (!$job_schedule = $this->job_schedule->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Schedule is not found.'
            ];
        } elseif (!$this->_attempt_delete($job_schedule)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Schedule is successfully removed.'
            ];
        }
        echo json_encode($response);
    }

    /**
     * Job Schedules
     */
    public function manager()
    {
        $this->title = 'Job Schedule';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify'
        ];

        $this->build_content('default', 'job_schedules/manager');
        $this->content = $this->load->view('job_schedules/manager');
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('customer');
        $this->load->model('job_schedule');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {

        $date = DateTime::createFromFormat('m/d/Y', $this->input->post('date'));
        
        $values = [
            'date' => $date->format('Y-m-d'),
            'time_start' => $this->input->post('time_start'),
            'time_end' => $this->input->post('time_end'),
            'customer_id' => $this->input->post('customer'),
            'qty_birds' => $this->input->post('qty_birds'),
            'area' => $this->input->post('area'),
            'details' => $this->input->post('details'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$schedule_id = $this->job_schedule->insert($values)) {
            $this->_error = 'server_error';
        }
    
        return $schedule_id;
    }


    /**
     * Attempt Edit
     */
    protected function _attempt_edit($job_schedule)
    {
        $where = [
            'id' => $job_schedule->id,
            'is_deleted' => 0
        ];

        $date = DateTime::createFromFormat('m/d/Y', $this->input->post('date'));
        
        $values = [
            'date' => $date->format('Y-m-d'),
            'time_start' => $this->input->post('time_start'),
            'time_end' => $this->input->post('time_end'),
            'customer_id' => $this->input->post('customer'),
            'qty_birds' => $this->input->post('qty_birds'),
            'area' => $this->input->post('area'),
            'details' => $this->input->post('details'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->job_schedule->update($where, $values)) {
            $this->_error = 'server_error';
        }
        
        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($job_schedule)
    {
        $where = [
            'id' => $job_schedule->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->job_schedule->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
