<?php
class Billing_statement extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'billing_statement';
        parent::__construct();
    }

    /**
     * Get Billing Statement By ID
     */
    public function get_by_id($billing_statement_id)
    {
        $sql = <<<EOT
SELECT 
    billing_statement.*, 
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email,
    CONCAT(creator.full_name, "<br>", DATE_FORMAT(billing_statement.added_on, "%b %d, %Y %h:%i %p")) AS prep,
    creator.sig_url AS prep_sigature
FROM billing_statement
LEFT JOIN customer ON customer.id = billing_statement.customer_id
LEFT JOIN user AS creator ON creator.id = billing_statement.added_by
WHERE billing_statement.id = ?
EOT;
    // AND invoice.is_deleted = 0
        $binds = [$billing_statement_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get All Invoices by Customer That has no Cash Bond
     */
    public function get_invoices_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT *
FROM invoice
WHERE invoice.is_deleted = 0
    AND invoice.customer_id = ?
    AND invoice.id NOT IN (
        SELECT DISTINCT invoice_id
        FROM cash_bond_item
        LEFT JOIN cash_bond ON cash_bond.id = cash_bond_item.cash_bond_id
        WHERE cash_bond_item.is_deleted = 0
            AND cash_bond.status <> 'cancelled'
    )
GROUP BY invoice.id
EOT;
        $binds = $customer_id;

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return false;
        }
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */