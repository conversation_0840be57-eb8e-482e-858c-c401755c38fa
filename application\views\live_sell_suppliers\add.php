<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class='row'>
					<div class="col-md-4 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" class="form-control" value="<?=set_value('name');?>">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address">Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no');?>">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>
				</div>

				<div class='row'>
					<div class="col-md-6 form-group">
						<label for="email">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group d-none">
						<label for="credit_limit">Credit Limit</label>

						<input type="number" step="any" name="credit_limit" class="form-control" value="<?=set_value('credit_limit');?>">

						<div class="red-text flash-message">
							<?=form_error('credit_limit');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf, image/*" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('live_sell_suppliers')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
