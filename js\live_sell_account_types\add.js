+ function ($) {
	'use strict';

	function previewDP(inputFile, defaultSrc) {
		var $preview = $('.img-preview');

		if (inputFile.files && inputFile.files[0]) {
			var reader = new FileReader();

			reader.onload = function (event) {
				$preview.attr('src', event.currentTarget.result);
			}

			reader.readAsDataURL(inputFile.files[0]);
		} else {
			$preview.attr('src', defaultSrc);
		}
	}

	$(function () {
		var $preview = $('.img-preview');
		var defaultSrc = $preview.attr('src');
		var $inputFile = $('input[name="image_upload"]');

		$preview.click(function () {
			$inputFile.click();
		});

		$inputFile.change(function (event) {
			previewDP(event.currentTarget, defaultSrc);
		});

		$("select[name = 'applied_to']").change(function(){
			let data1 = [
				{
					id: "Cost of sales",
					text: "Cost of sales",
				},
				{
					id: "Current assets",
					text: "Current assets",
				},
				{
					id: "Current liabilities",
					text: "Current liabilities",
				},
				{
					id: "Equity",
					text: "Equity"
				},
				{
					id: "Fixed asset",
					text: "Fixed asset"
				},
				{
					id: "Operating expenses",
					text: "Operating expenses"
				},
				{
					id: "Other current assets",
					text: "Other current assets"
				},
				{
					id: "Other current liabilities",
					text: "Other current liabilities"
				}
			];

			let data2 = [
				{
					id: "Revenue",
					text: "Revenue"
				}
			];
			$("select[name = 'account_type']").empty();
			if ($(this).val() == "Purchase Order") {
				$("select[name = 'account_type']").select2({
					data: data1,
					placeholder: $(this).attr('data-placeholder'),
					width: '100%',
					allowClear: true
				});
			} else if($(this).val() == "Sales Income") {
				$("select[name = 'account_type']").select2({
					data: data2,
					placeholder: $(this).attr('data-placeholder'),
					width: '100%',
					allowClear: true
				});
			}
			$("select[name = 'account_type']").val($("select[name = 'account_type']").attr("data-default")).trigger("change");
		})

		$("select[name = 'applied_to']").trigger("change");

		$("select[name = 'financial_statement']").change(function(){
			let data1 = [
				{
					id: "Cash on hand and in bank",
					text: "Cash on hand and in bank",
				},
				{
					id: "Cumulative earnings - Appropriated",
					text: "Cumulative earnings - Appropriated",
				},
				{
					id: "Cumulative earnings - Unappropriated",
					text: "Cumulative earnings - Unappropriated",
				},
				{
					id: "Income tax payables",
					text: "Income tax payables"
				},
				{
					id: "Inventories",
					text: "Inventories"
				},
				{
					id: "Other assets",
					text: "Other assets"
				},
				{
					id: "Other current assets",
					text: "Other current assets"
				},
				{
					id: "Property and equipment - Net",
					text: "Property and equipment - Net"
				},
				{
					id: "Share capital",
					text: "Share capital"
				},
				{
					id: "Trade and other payables",
					text: "Trade and other payables"
				},
				{
					id: "Trade and other receivables",
					text: "Trade and other receivables"
				}
			];

			let data2 = [
				{
					id: "",
					text: ""
				}
			];
			$("select[name = 'balance_sheet_category']").empty();
			if ($(this).val() == "Balance sheet") {
				$("select[name = 'balance_sheet_category']").select2({
					data: data1,
					placeholder: $(this).attr('data-placeholder'),
					width: '100%',
					allowClear: true
				});
			} else if($(this).val() == "Income statement") {
				$("select[name = 'balance_sheet_category']").select2({
					data: data2,
					placeholder: $(this).attr('data-placeholder'),
					width: '100%',
					allowClear: true
				});
			}
			$("select[name = 'balance_sheet_category']").val($("select[name = 'balance_sheet_category']").attr("data-default")).trigger("change");
		})

		$("select[name = 'financial_statement']").trigger("change");

	});
}(jQuery);
