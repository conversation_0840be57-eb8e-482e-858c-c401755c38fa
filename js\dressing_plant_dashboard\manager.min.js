!function(n){"use strict";n(function(){setTimeout(function(){for(let e=1;e<=7;e++){let t=n(`#spin_${e}`).attr("data-val");n(`#spin_${e}`).parent().html(`<span class='fade-value'>${t}</span>`).hide().fadeIn(),n(`#spin_${e}`).removeClass("spin")}n(".spin").parent().html("<span>0</span>"),n(".spin").removeClass("spin")},1e3),n(".card").hover(function(){const e=n(this).find(".proceed");let t=e.width()-120;n(this).find(".arrow").animate({right:t+"px"}),n(this).find(".proceed").delay(300).fadeIn(500)},function(){n(this).find(".proceed").fadeOut(300),n(this).find(".arrow").delay(300).animate({right:"5%"})})})}(jQuery);