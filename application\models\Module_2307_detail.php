<?php
class Module_2307_detail extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'form_2307_details';
        parent::__construct();
    }

    /**
     * Get Items by 2307 Detail ID 
     */
    public function get_by_2307_detail_id($module_2307_detail_id)
    {
        $sql = <<<EOT
SELECT 
    `form_2307_details`.*
FROM `form_2307_details`
WHERE `form_2307_details`.is_deleted = 0
    AND `form_2307_details`.`2307_id` = ?
EOT;
        $binds = [$module_2307_detail_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */