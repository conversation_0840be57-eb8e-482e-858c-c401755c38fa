<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="dropdown float-right">
				<h1 class="crud-title">
					Purchase Order No. <?=$release->purchase_id;?>
				</h1>
			</div>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Supplier Info</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<label for="name">Name</label>

						<p class="underlined" data-for="supplier"><?=$supplier->name;?></p>
					</div>

					<div class="col-md-12 form-group">
						<label for="address">Address</label>

						<p class="underlined" data-for="address"><?=$supplier->address;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no">Phone Number</label>

						<p class="underlined" data-for="phone_no"><?=$supplier->phone_no;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="email">Email</label>

						<p class="underlined" data-for="email"><?=$supplier->email;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="contact_person">Contact Person</label>

						<p class="underlined" data-for="contact_person"><?=$supplier->contact_person;?></p>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Release Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="purchase_date">Purchase Date</label>

						<p class="underlined" data-for="purchase_date"><?=date('m/d/Y', strtotime($release->purchase_date));?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="release_date" class="control-label">Date Released</label>

						<input type="text" name="release_date" class="form-control datepicker" value="<?=set_value('release_date', date('m/d/Y', strtotime($release->release_date)));?>">

						<div class="red-text flash-message">
							<?=form_error('release_date');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Purchased Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th width="32%">Item</th>

									<th width="17%">Ordered Qty</th>

									<th width="17%" class="text-right">Total Released</th>

									<th width="17%" class="text-right">Balance</th>

									<th width="17%" class="text-right">Released Qty</th>
								</tr>
							</thead>

							<tbody>
								<?php
								if (!empty($release_items)):
									foreach ($release_items as $release_item):
								?>
										<tr class="release_item">
											<td>
												<p class="form-control-plaintext" data-for="item"><?=$release_item->name;?></p>
											</td>

											<td>
												<p class="form-control-plaintext" data-for="ordered"><?=floatval($release_item->ordered) . ' ' . $release_item->unit;?></p>
											</td>

											<td>
												<p class="form-control-plaintext text-right" data-for="released"><?=floatval($release_item->released - $release_item->qty);?></p>
											</td>

											<td>
												<p class="form-control-plaintext text-right" data-for="balance"><?=floatval($release_item->ordered - $release_item->released + $release_item->qty);?></p>
											</td>

											<td>
												<?php $field_name = 'released_' . $release_item->purchase_item_id;?>
												<input type="number" name="<?=$field_name;?>" class="form-control" value="<?=set_value($field_name, floatval($release_item->qty));?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($field_name);?>
												</div>
											</td>
										</tr>
								<?php
									endforeach;
								else:
								?>
									<tr>
										<td class="text-center" colspan="5">No item to release.</td>
									</tr>
								<?php
								endif;
								?>
							</tbody>
						</table>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('purchases/releases/' . $release->purchase_id);?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>