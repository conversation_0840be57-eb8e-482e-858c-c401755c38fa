<?php
class Purchase_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'purchase_item';
        parent::__construct();
    }

    /**
     * Generate Purchase Order Item
     */
    public function generate($id, $purchase_id, $item_id, $qty, $unit, $price, $amount, $account_type, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO purchase_item(id, purchase_id, item_id, qty, unit, price, amount, account_type, status, added_by, added_on) (
    SELECT ? AS id, purchase.id AS purchase_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS price, ? AS amount, ? AS account_type, "pending" AS status, ? AS added_by, ? AS added_on
    FROM purchase
    CROSS JOIN item
    WHERE purchase.id = ?
        AND purchase.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    purchase_item.id = LAST_INSERT_ID(purchase_item.id),
    purchase_item.item_id = VALUES(purchase_item.item_id),
    purchase_item.qty = VALUES(purchase_item.qty),
    purchase_item.unit = VALUES(purchase_item.unit),
    purchase_item.price = VALUES(purchase_item.price),
    purchase_item.amount = VALUES(purchase_item.amount),
    purchase_item.account_type = VALUES(purchase_item.account_type),
    purchase_item.updated_by = VALUES(purchase_item.added_by),
    purchase_item.is_deleted = 0;
EOT;

        $binds = [$id, $qty, $unit, $price, $amount, $account_type, $added_by, $added_on, $purchase_id, $item_id];

        return $this->advanced_query($sql, $binds);

        // return $this->db->insert_id();
    }

    /**
     * Get Purchase Order Item By ID
     */
    public function get_by_id($purchase_item_id)
    {
        $sql = <<<EOT
SELECT purchase_item.*, item.name AS item
FROM purchase_item
LEFT JOIN item ON item.id = purchase_item.item_id
WHERE purchase_item.id = ?
    AND purchase_item.is_deleted = 0
EOT;
        $binds = [$purchase_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Purchase Order Items by Purchase Order
     */
    public function get_by_purchase($purchase_id)
    {
        $sql = <<<EOT
SELECT purchase_item.*, item.name AS item
FROM purchase_item
LEFT JOIN item ON item.id = purchase_item.item_id
WHERE purchase_item.purchase_id = ?
    AND purchase_item.is_deleted = 0
EOT;
        $binds = [$purchase_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Balance Sheet
     */
    public function get_by_balance_sheet($balance_sheet_category, $date_from, $date_to)
    {
        $sql = <<<EOT
SELECT SUM(amount) AS total, account_type.account_type, balance_sheet_category
FROM purchase_item
LEFT JOIN account_type ON account_type.account_name = purchase_item.account_type
WHERE purchase_item.is_deleted = 0
AND balance_sheet_category = ?
AND purchase_item.added_on BETWEEN ? AND ?
GROUP BY balance_sheet_category;
EOT;
        $binds = [$balance_sheet_category, $date_from, $date_to, ];
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Purchase Order Items and their State by Purchase Order
     */
    public function get_w_state_by_purchase($purchase_id)
    {
        $bodega = WAD_WAREHOUSE;

        $sql = <<<EOT
SELECT purchase_item.*, item.name, item.unit,
(
    CASE
        WHEN INSTR(purchase.main_usage, "$bodega") AND receive_item.qty IS NOT NULL THEN receive_item.qty
        ELSE 0
    END
) AS received,
(
    CASE
        WHEN NOT INSTR(purchase.main_usage, "$bodega") AND release_item.qty IS NOT NULL THEN release_item.qty
        ELSE 0
    END
) AS released
FROM purchase_item
LEFT JOIN (
    SELECT receive_item.receive_id, receive_item.purchase_item_id, IFNULL(SUM(receive_item.qty), 0) AS qty
    FROM receive_item
    LEFT JOIN receive ON receive.id = receive_item.receive_id
    WHERE receive.is_deleted = 0
        AND receive_item.is_deleted = 0
    GROUP BY receive_item.purchase_item_id
) receive_item ON receive_item.purchase_item_id = purchase_item.id
LEFT JOIN (
    SELECT release_item.release_id, release_item.purchase_item_id, IFNULL(SUM(release_item.qty), 0) AS qty
    FROM release_item
    LEFT JOIN release ON release.id = release_item.release_id
    WHERE release.is_deleted = 0
        AND release_item.is_deleted = 0
    GROUP BY release_item.purchase_item_id
) release_item ON release_item.purchase_item_id = purchase_item.id
LEFT JOIN item ON item.id = purchase_item.item_id
WHERE purchase_item.purchase_id = ?
    AND purchase_item.is_deleted = 0
EOT;
        $binds = [$purchase_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Expense Sheet
     */
    public function get_by_expense($date_from, $date_to)
    {
        $sql = <<<EOT
SELECT purchase_item.account_type AS account_name, SUM(purchase_item.amount) AS amount
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase.is_deleted = 0
    AND purchase_item.status = 'approved'
    AND purchase.purchase_date BETWEEN ? AND ?
    AND purchase_item.account_type NOT IN ('Fund Transfer', 'CAPEX Purchases')
    AND purchase.status NOT IN ('pending','for_approval')
GROUP BY purchase_item.account_type 
EOT;
        $binds = [$date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Expense Sheet
     */
    public function get_expense_by_year($year)
    {
        $sql = <<<EOT
SELECT 
    purchase_item.account_type AS account_name,
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 1 THEN purchase_item.amount END), 0) AS 'jan',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 2 THEN purchase_item.amount END), 0) AS 'feb',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 3 THEN purchase_item.amount END), 0) AS 'mar',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 4 THEN purchase_item.amount END), 0) AS 'apr',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 5 THEN purchase_item.amount END), 0) AS 'may',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 6 THEN purchase_item.amount END), 0) AS 'jun',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 7 THEN purchase_item.amount END), 0) AS 'jul',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 8 THEN purchase_item.amount END), 0) AS 'aug',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 9 THEN purchase_item.amount END), 0) AS 'sep',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 10 THEN purchase_item.amount END), 0) AS 'oct',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 11 THEN purchase_item.amount END), 0) AS 'nov',
    COALESCE(SUM(CASE WHEN MONTH(purchase.purchase_date) = 12 THEN purchase_item.amount END), 0) AS 'dec'
FROM purchase_item
LEFT JOIN purchase ON purchase.id = purchase_item.purchase_id
WHERE purchase.is_deleted = 0
    AND purchase_item.status = 'approved'
    AND YEAR(purchase.purchase_date) = ?
    AND purchase_item.account_type NOT IN ('Fund Transfer', 'CAPEX Purchases')
    AND purchase.status NOT IN ('pending', 'for_approval')
GROUP BY purchase_item.account_type
ORDER BY purchase_item.account_type
EOT;
        $binds = [$year];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get receive details by ID
     */
    public function get_expense_breakdown_by_day($date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT 
    calendar.date AS expense_date,
    COALESCE(SUM(purchase.amount), 0) AS total_expense_per_day
FROM (
    SELECT ? + INTERVAL n DAY AS date
    FROM (
        SELECT 
            a.N + b.N * 10 + c.N * 100 AS n
        FROM
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
        ) AS numbers
    WHERE ? + INTERVAL n DAY <= ?
) AS calendar
LEFT JOIN (
    SELECT purchase.purchase_date AS date, SUM(purchase_item.amount) AS amount
    FROM purchase
    LEFT JOIN purchase_item ON purchase.id = purchase_id
    WHERE purchase.is_deleted = 0
        AND purchase_item.status = 'approved'
        AND purchase_item.is_deleted = 0
        AND purchase_item.account_type NOT IN ('Fund Transfer', 'CAPEX Purchases')
        AND purchase.status NOT IN ('pending','for_approval')
    GROUP BY purchase.purchase_date
) AS purchase ON calendar.date = purchase.date
GROUP BY calendar.date
ORDER BY calendar.date
EOT;
        $binds = [$date_from, $date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
