! function(n) {
    "use strict";

    function t(t, e) {
        return n.ajax({
            url: BASE_URI + "payments/mark_audited/",
            type: "POST",
            data: {
                payment_id: t,
                audited_no: e
            },
            dataType: "json"
        })
    }
    n(function() {
        n(".action-print").click(function() {
            var e = n('input[name="payment_id"]').val(),
                i = n('input[name="audited_no"]').val();
            alertify.confirm("Are you sure you want to print?", function(n) {
                n ? (t(e, i), window.location.reload()) : alertify.closeLogOnClick(!0)
            }).setting({
                title: "Confirm Audit",
                message: '<div style="text-align: center;">Are you sure you want to audit?</div>'
            })
        })
    })
}(jQuery);