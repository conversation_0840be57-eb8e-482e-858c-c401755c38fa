+ function ($) {
	'use strict';

	function mark_audited(payment_id, audited_no) {
		return $.ajax({
			url: BASE_URI + 'payments/mark_audited/',
			type: 'POST',
			data: { payment_id: payment_id, audited_no: audited_no },
			dataType: 'json'
		});
	}

	$(function () {
		$('.action-print').click(function () {
			var paymentId = $('input[name="payment_id"]').val();
			var arNo = $('input[name="audited_no"]').val();
			alertify
				.confirm('Are you sure you want to print?', function (e) {
					if (e) {
						mark_audited(paymentId, arNo);
						//window.print();
						window.location.reload();
					} else {
						alertify.closeLogOnClick(true);
					}
            	})
				.setting({
					'title': 'Confirm Audit',
					'message': '<div style="text-align: center;">Are you sure you want to audit?</div>'
				})
		});
	});
}(jQuery);