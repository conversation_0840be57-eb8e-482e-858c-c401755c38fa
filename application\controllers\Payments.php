<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Payments extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('payments/manager'));
    }

     /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add Payment
     */
    public function add()
    {
        $paid_invoice_ids = $this->_get_invoice_inputs();
        $rules = array_merge(
            $this->config->item('payments/add'),
            $this->_get_invoice_rules('', $paid_invoice_ids),
            $this->_get_type_rules($this->input->post('type'))
        );

        $customer_id = $this->input->post('customer');

        if ($this->_validate_form($rules) && $payment_id = $this->_attempt_add()) {
            if($this->input->post('type') === 'cash') {
                redirect(site_url('payments/payment_reciept/'.$payment_id));
            } else {
                redirect(site_url('payments/payment_proof/'.$payment_id));
            }
        } else {
            $this->title = 'Add Payment';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/payments/add'
            ];

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'paid_invoice_ids' => $paid_invoice_ids,
                'invoices' => $this->invoice->get_partially_paid($customer_id)
            ];

            $this->build_content('default', 'payments/add', $data);
        }
    }

    /**
     * Proof of Payment / Acknowledgement Receipt
     */
    public function payment_proof($payment_id)
    {

        $payment = $this->payment->get_by_id($payment_id) or show_404();

        // $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $ar_no = $this->generate_ar();

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/payment_proof'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice,
            'ar_no' => $ar_no
        ];
        
        $this->build_content('default', 'payments/payment_proof', $data);
    }

    /**
     * Generate AR No.
     */
    protected function generate_ar()
    {
        $year = date("Y");
        $ar_no_prefix = $year;
        
        $last_payment = $this->payment->select('MAX(counter) AS last_counter', ['is_deleted' => 0], 1);

        $next_ar_no_digits = str_pad((int)$last_payment->last_counter + 1, 7, '0', STR_PAD_LEFT);
        $next_ar_no = $ar_no_prefix ." - ". $next_ar_no_digits;

        return $next_ar_no;
    }

    /**
     * Mark Payment as Acknowledged
     */
    public function mark_acknowledged()
    {
        $payment_id = $this->input->post('payment_id');
        $ar_no = $this->input->post('ar_no');

        $this->db->trans_begin();

        if (!$payment = $this->payment->get_by_id($payment_id)) {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => 'Payment not found.'
            ];
        } elseif (!$this->_attempt_mark_acknowledged($payment, $ar_no)) {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $this->db->trans_commit();
            $response = [
                'message' => 'Acknowledgement receipt successfully added.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Mark as Approved
     */
    protected function _attempt_mark_acknowledged($payment, $ar_no)
    {
        $last_payment = $this->payment->select('MAX(counter) AS last_counter', ['is_deleted' => 0], 1);
        
        if(empty($last_payment)) {
            $counter = 1;
        } else {
            $counter = $last_payment->last_counter + 1;
        }

        $where = [
            'id' => $payment->id
        ];

        $values = [
            'ar_no' => $ar_no,
            'counter' => $counter,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
            'ar_by' => $_SESSION['user']->id,
            'ar_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->payment->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    //PAYMENT RECIEPT ACTION HERE

    /**
     * Proof of Payment / Payment Receipt
     */
    public function payment_reciept($payment_id)
    {

        $payment = $this->payment->get_by_id($payment_id) or show_404();

        // $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $pr_no = $this->generate_pr();

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/payment_reciept'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice,
            'pr_no' => $pr_no
        ];
        
        $this->build_content('default', 'payments/payment_reciept', $data);
    }

    /**
     * Generate PR No.
     */
    protected function generate_pr()
    {
        $year = date("Y");
        $pr_no_prefix = $year;
        
        $last_payment = $this->payment->select('MAX(counter_pr) AS last_counter', ['is_deleted' => 0], 1);

        $next_pr_no_digits = str_pad((int)$last_payment->last_counter + 1, 7, '0', STR_PAD_LEFT);
        $next_pr_no = $pr_no_prefix ." - ". $next_pr_no_digits;

        return $next_pr_no;
    }

    /**
     * Mark Payment as Reciept
     */
    public function mark_payment()
    {
        $this->db->trans_begin();

        $payment_id = $this->input->post('payment_id');
        $pr_no = $this->input->post('pr_no');

        if (!$payment = $this->payment->get_by_id($payment_id)) {
            http_response_code(400);
            $response = [
                'message' => 'Payment not found.'
            ];
        } elseif (!$this->_attempt_mark_payment($payment, $pr_no)) {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } elseif (!$this->_attempt_mark_as_for_audit($payment_id)) {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $this->db->trans_commit();
            $response = [
                'message' => 'Payment receipt successfully added.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Mark Paid invoice as For Audit
     */
    protected function _attempt_mark_as_for_audit($payment_id)
    {
        $where = [
            'payment_id' => $payment_id
        ];

        $values = [
            'for_audit' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->paid_invoice->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    /**
     * Attempt Mark as Approved
     */
    protected function _attempt_mark_payment($payment, $pr_no)
    {
        $last_payment = $this->payment->select('MAX(counter_pr) AS last_counter', ['is_deleted' => 0], 1);
        
        if(empty($last_payment)) {
            $counter = 1;
        } else {
            $counter = $last_payment->last_counter + 1;
        }

        $where = [
            'id' => $payment->id
        ];

        $values = [
            'pr_no' => $pr_no,
            'counter_pr' => $counter,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
            'pr_by' => $_SESSION['user']->id,
            'pr_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->payment->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    //AUDIT ACTION HERE

    /**
     * Audit
     */
    public function audit($payment_id)
    {
        // this gets array of obejects, which mean, i can use in the view `$payment->audit_remarks` fetching the specified data in a specific row
        $payment = $this->payment->get_by_id($payment_id) or show_404();

        // $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $audit_remarks = $this->generate_audited();

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/audit'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice,
            'audit_remarks' => $audit_remarks
        ];
        
        $this->build_content('default', 'payments/audit', $data);
    }

    /**
     * Generate Audit No.
     */
    protected function generate_audited()
    {
        $month_year = date("m-Y");
        $audit_remarks_prefix = $month_year;
        
        $last_payment = $this->payment->select('MAX(counter_pr) AS last_counter', ['is_deleted' => 0], 1);

        $next_audit_remarks_digits = str_pad((int)$last_payment->last_counter + 1, 7, '0', STR_PAD_LEFT);
        $next_audit_remarks = $audit_remarks_prefix ." - ". $next_audit_remarks_digits;

        return $next_audit_remarks;
    }

    /**
     * Mark Payment as Audited
     */
    public function mark_audited()
    {
        $payment_id = $this->input->post('payment_id');
        $audit_remarks = $this->input->post('audit_remarks');
        $audit_status = $this->input->post('audit_status');
        
        // Define upload configuration
        $config = [
            'upload_path' => FCPATH . 'assets/audit/', // Specify the upload directory path
            'allowed_types' => 'pdf|jpg|jpeg|png', // Specify allowed file types
            'max_size' => 10240, // Maximum file size in kilobytes (10MB)
            'encrypt_name' => false, // Encrypt the uploaded file's name
            'overwrite' => false // Do not overwrite existing files with the same name
        ];

        // Move uploaded files to the desired directory
        $new_path = FCPATH . 'assets/audit/' . $payment_id . '/';
        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        // Attempt file upload
        if ($this->_attempt_upload($config, 'attachments')) {
            $this->_move_uploaded($new_path);

            // Update the database with the audit details
            if ($this->_attempt_mark_audited($payment_id, $audit_remarks, $audit_status)) {
                $response = [
                    'success' => true,
                    'message' => 'Payment successfully marked as audited.'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => $this->_error_msg()
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        }

        // Return the response as JSON
        echo json_encode($response);
    }

    /**
     * Attempt Mark as Audited
     */
    protected function _attempt_mark_audited($payment_id, $audit_remarks, $audit_status)
    {
        $this->db->trans_begin();

        // Insert audit record
        $audit_values = [
            'payment_id' => $payment_id,
            'audit_remarks' => $audit_remarks,
            'audit_status' => $audit_status,
            'audited_by' => $_SESSION['user']->id,
            'audited_on' => date('Y-m-d H:i:s')
        ];

        if (!$audit_id = $this->payment_audit->insert($audit_values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Handle attachments
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                $attachment_values = [
                    'payment_id' => $payment_id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];
                $attachment_id = $this->payment_audit_attachment->insert($attachment_values);
                if (!$attachment_id) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                    return false;
                }
            }
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Update Audited
     */
    public function update_mark_audited()
    {
        $payment_id = $this->input->post('payment_id');
        $audit_remarks = $this->input->post('audit_remarks');
        $audit_status = $this->input->post('audit_status');
        
        // Define upload configuration
        $config = [
            'upload_path' => FCPATH . 'assets/audit/', // Specify the upload directory path
            'allowed_types' => 'pdf|jpg|jpeg|png', // Specify allowed file types
            'max_size' => 10240, // Maximum file size in kilobytes (10MB)
            'encrypt_name' => false, // Encrypt the uploaded file's name
            'overwrite' => false // Do not overwrite existing files with the same name
        ];

        // Move uploaded files to the desired directory
        $new_path = FCPATH . 'assets/audit/' . $payment_id . '/';
        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }
        
        // Attempt file upload
        if ($this->_attempt_upload($config, 'attachments')) {
            $this->_move_uploaded($new_path);

            // Update the database with the audit details
            if ($this->_attempt_update_mark_audited($payment_id, $audit_remarks, $audit_status)) {
                $response = [
                    'success' => true,
                    'message' => 'Payment successfully marked as audited.'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => $this->_error_msg()
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        }

        // Return the response as JSON
        echo json_encode($response);
    }

    /**
     * Attempt Update Audited
     */
    protected function _attempt_update_mark_audited($payment_id, $audit_remarks, $audit_status)
    {
        $this->db->trans_begin();

        $where = [
            'payment_id' => $payment_id
        ];

        // Insert audit record
        $audit_values = [
            'audit_remarks' => $audit_remarks,
            'audit_status' => $audit_status,
            'audited_by' => $_SESSION['user']->id,
            'audited_on' => date('Y-m-d H:i:s')
        ];
        $audit_id = $this->payment_audit->update($where, $audit_values);

        if (!$audit_id) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Handle attachments
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {

                $existing_audit_attachment = $this->payment_audit_attachment->get_attachment_by_payment_id($payment_id);

                if ($existing_audit_attachment) {
                    $where = [
                        'payment_id' => $payment_id
                    ];
    
                    $attachment_values = [
                        'file_name' => $uploaded['file_name'],
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => date('Y-m-d H:i:s')
                    ];
                    $attachment_id = $this->payment_audit_attachment->update($where, $attachment_values);
                    
                    if (!$attachment_id) {
                        $this->db->trans_rollback();
                        $this->_error = 'server_error';
                        return false;
                    }
                } else {
                    $attachment_values = [
                        'payment_id' => $payment_id,
                        'file_name' => $uploaded['file_name'],
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => date('Y-m-d H:i:s')
                    ];
                    $attachment_id = $this->payment_audit_attachment->insert($attachment_values);
                    
                    if (!$attachment_id) {
                        $this->db->trans_rollback();
                        $this->_error = 'server_error';
                        return false;
                    }
                }
            }
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Generate OR
     */
    // public function payment_or($payment_id)
    // {
    //     $ar_invoice = $this->payment->get_ar_payment_by_id($payment_id);

    //     $paid_invoice = $this->paid_invoice->get_by_payment_id($payment_id);

    //     $data = [
    //         'ar_invoice' => $ar_invoice,
    //         'paid_invoice' => $paid_invoice,
    //     ];
        
    //     $this->build_content('default', 'payments/payment_or', $data);
    // }

    public function payment_or($payment_id)
    {
        $ar_invoice = $this->payment->get_ar_payment_by_id($payment_id);

        $payment = $this->payment->get_by_id($payment_id) or show_404();

        // $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/payment_or'
        ];

        $data = [
            'payment' => $payment,
            'ar_invoice' => $ar_invoice,
            'paid_invoice' => $paid_invoice
        ];
        
        $this->build_content('default', 'payments/payment_or', $data);
    }

    /**
     * Proof of Payment / Acknowledgement Receipt Preview
     */
    public function preview_payment_proof($payment_id)
    {
        $payment = $this->payment->get_by_id($payment_id) or show_404();

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/preview_payment_proof'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice
        ];
        
        $this->build_content('default', 'payments/preview_payment_proof', $data);
    }

    /**
     * Proof of Payment / Payment Receipt Preview
     */
    public function preview_payment_reciept($payment_id)
    {
        $payment = $this->payment->get_by_id($payment_id) or show_404();

        // $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoice = $this->paid_invoice->get_by_invoice_payment_id($payment->id);
        } else {
            $paid_invoice = $this->paid_invoice->get_by_cash_bond_payment_id($payment->id);
        }

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/preview_payment_reciept'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice
        ];
        
        $this->build_content('default', 'payments/preview_payment_reciept', $data);
    }

    /**
     * Audit
     */
    public function preview_audit($payment_id)
    {
        $payment = $this->payment->get_by_id($payment_id) or show_404();

        $paid_invoice = $this->paid_invoice->get_by_payment_id($payment->id);

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/preview_audit'
        ];

        $data = [
            'payment' => $payment,
            'paid_invoice' => $paid_invoice
        ];
        
        $this->build_content('default', 'payments/preview_audit', $data);
    }

    /**
     * Purchase Order Print Preview
     */
    public function preview($payment_id)
    {
        $where = [
            'id' => $payment_id,
            'is_deleted' => 0
        ];
        $payment = $this->payment->select('', $where, 1) or show_404();

        $this->title = 'Print Preview: Payment No. ' . $payment->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/payments/preview'
        ];

        $where = [
            'payment_id' => $payment->id
        ];
        $payment_detail = $this->payment_detail->select('', $where, 1);

        // $paid_invoices = $this->paid_invoice->get_by_payment($payment_id);

        $invoice = $this->paid_invoice->select('doc_type', ['payment_id' => $payment_id], 1);

        if($invoice->doc_type === 'invoice'){
            $paid_invoices = $this->paid_invoice->get_invoice_by_payment($payment->id);
        } else {
            $paid_invoices = $this->paid_invoice->get_cash_bond_by_payment($payment->id);
        }

        $data = [
            'payment' => $payment,
            'payment_detail' => $payment_detail,
            'paid_invoices' => $paid_invoices
        ];
        $this->build_content('default', 'payments/preview', $data);
    }

    /**
     * Payments
     */
    public function manager()
    {
        $this->title = 'Payments';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/payments/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'all',
            'customers' => $this->customer->select('', ['is_deleted' => 0], null, 'name ASC')
        ];

        $this->build_content('default', 'payments/manager', $data);
    }

    /**
     * Payments
     */
    public function generate_payments()
    {
        $payments = $this->payment->get_all_for_ar_and_pr();
        $payments_count = count($payments);
        $total_invoice = 0;

        $this->db->trans_begin();

        if (!$payments) {
            $this->db->trans_rollback();
            http_response_code(404);
            $response = [
                'message' => 'Payments not found.'
            ];
        } else {
            foreach($payments AS $payment){
                $ar_no = $this->generate_ar();
                $pr_no = $this->generate_pr();

                if($payment->status === "for_ar") {
                    if(!$this->_attempt_mark_acknowledged($payment, $ar_no)){
                        $this->db->trans_rollback();
                        http_response_code(400);
                        $response = [
                            'message' => 'Failed to AR payment no ' . $payment->id
                        ];
                    } elseif (!$this->_attempt_mark_payment($payment, $pr_no)) {
                        $this->db->trans_rollback();
                        http_response_code(400);
                        $response = [
                            'message' => 'Failed to PR payment no ' . $payment->id
                        ];
                    } 
                    // elseif (!$this->_attempt_check_for_audit($payment)) {
                    //     $this->db->trans_rollback();
                    //     http_response_code(400);
                    //     $response = [
                    //         'message' => 'Failed to mark payment no ' . $payment->id . ' as for audit.'
                    //     ];
                    // } 
                    else {
                        $total_invoice++;
                    }

                } elseif ($payment->status === "for_pr") {
                    if (!$this->_attempt_mark_payment($payment, $pr_no)) {
                        $this->db->trans_rollback();
                        http_response_code(400);
                        $response = [
                            'message' => 'Failed to PR payment no ' . $payment->id
                        ];
                    } 
                    // elseif (!$this->_attempt_check_for_audit($payment)) {
                    //     $this->db->trans_rollback();
                    //     http_response_code(400);
                    //     $response = [
                    //         'message' => 'Failed to mark payment no ' . $payment->id . ' as for audit.'
                    //     ];
                    // } 
                    else {
                        $total_invoice++;
                    }
                }
            }
        }

        if($payments_count === $total_invoice){
            $this->db->trans_commit();
            $response = [
                'status' => 200,
                'message' => 'Success',
            ];
        } else {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => 'Counter: ' . $total_invoice . ' does not match with the Total payments:' . $payments_count,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Only Mark Paid Invoice as For Audit it the amount + paid_amount in invoice doesn't go over the total
     */
    protected function _attempt_check_for_audit($payment)
    {
        $paid_invoices = $this->paid_invoice->get_invoice_details_by_payment_id($payment->id);

        foreach ($paid_invoices AS $paid_invoice){
            if(($paid_invoice->amount + $paid_invoice->paid_amount) <= $paid_invoice->invoice_total) {
                $where = [
                    'id' => $paid_invoice->id
                ];

                $values = [
                    'for_audit' => 1,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => date('Y-m-d H:i:s')
                ];
        
                if (!$this->paid_invoice->update($where, $values)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('customer');
        $this->load->model('payment');
        $this->load->model('payment_audit');
        $this->load->model('payment_audit_attachment');
        $this->load->model('payment_detail');
        $this->load->model('paid_invoice');
        $this->load->model('invoice');
        $this->load->model('cash_bond');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_invoice_inputs()
    {
        $paid_invoice_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'paid_invoice_') === 0) {
                $paid_invoice_ids[] = $value;
            }
        }

        return $paid_invoice_ids;
    }

    /**
     * Get Invoice Rules
     */
    protected function _get_invoice_rules($so_id, $paid_invoice_ids)
    {
        $rules = [];

        foreach ($paid_invoice_ids as $paid_invoice_id) {
            $rules[] = [
                'field' => 'paid_invoice_' . $paid_invoice_id,
                'label' => 'Paid Invoice',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'invoice_' . $paid_invoice_id,
                'label' => 'Invoice No',
                'rules' => 'required|trim|callback__is_invoice|callback__no_invoice_duplicate',
                'errors' => [
                    '_is_invoice' => 'Invoice is not found.',
                    '_no_invoice_duplicate' => 'Invoice is added more than once.'
                ]
            ];

            $where = [
                'id' => $this->input->post('invoice_' . $paid_invoice_id)
            ];
            if (!$invoice = $this->invoice->select('', $where, 1) or $invoice->total < $invoice->paid_amount) {
                $balance = 0;
            } else {
                $balance = $invoice->total - $invoice->paid_amount;
            }

            $rules[] = [
                'field' => 'amount_' . $paid_invoice_id,
                'label' => 'Amount',
                'rules' => 'required|trim|numeric|less_than_equal_to[' . $balance . ']|greater_than[0]'
            ];
        }

        return $rules;
    }

    /**
     * Get Type Rules
     */
    protected function _get_type_rules($payment_type)
    {
        switch ($payment_type) {
            case 'cash':
                return [
                    [
                        'field' => 'cash_amount',
                        'label' => 'Cash Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            case 'check':
                return [
                    [
                        'field' => 'check_date',
                        'label' => 'Check Date',
                        'rules' => 'required|trim|is_date[m/d/Y]'
                    ],
                    [
                        'field' => 'check_no',
                        'label' => 'Check No',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'bank_name',
                        'label' => 'Bank Name',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'check_amount',
                        'label' => 'Check Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            default:
                return [];
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $paid_invoices = $this->_get_invoice_data(); //get invoices

        if (empty($paid_invoices)) {
            $this->_error = 'no_paid_invoice_added';
            return false;
        }

        if (!$payment_id = $this->_add_payment($paid_invoices) or
            !$this->_add_payment_detail($payment_id) or
            !$this->_add_paid_invoices($payment_id, $paid_invoices)
        ){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $payment_id;
    }

    /**
     * Get Invoice Data
     */
    protected function _get_invoice_data()
    {
        $data = [];

        $paid_invoice_ids = $this->_get_invoice_inputs();
        foreach ($paid_invoice_ids as $paid_invoice_id) {
            $data[] = [
                'id' => is_numeric($paid_invoice_id) ? $paid_invoice_id : null,
                'invoice_id' => $this->input->post('invoice_' . $paid_invoice_id),
                'amount' => $this->input->post('amount_' . $paid_invoice_id),
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Add Payment
     */
    protected function _add_payment($paid_invoices)
    {
        $type = $this->input->post('type');
        $amount = ($type === 'check') ? floatval(str_replace(',','',$this->input->post('check_amount'))) : floatval(str_replace(',','',$this->input->post('cash_amount')));
        $item_amount = array_reduce($paid_invoices, function ($current_total, $paid_invoice) {
            return $current_total + $paid_invoice['amount'];
        });


        if (number_format($amount,2) !== number_format($item_amount,2)) {
            $this->_error = ucfirst($type) . ' amount and total amount in table does not match.';
            return false;
        }

        $payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('payment_date'));

        $values = [
            'payment_date' => $payment_date->format('Y-m-d'),
            'total_paid' => $amount,
            'type' => $type,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];


        if (!$payment_id = $this->payment->insert($values)) {
            $this->_error = 'server_error';
            return false;
        }

        return $payment_id;
    }

    /**
     * Add Payment Detail
     */
    protected function _add_payment_detail($payment_id)
    {
        $detail_id = true;

        if ($this->input->post('type') === 'check') {
            $check_date = DateTime::createFromFormat('m/d/Y', $this->input->post('check_date'));
            $values = [
                'payment_id' => $payment_id,
                'check_no' => $this->input->post('check_no'),
                'check_date' => $check_date->format('Y-m-d'),
                'bank_name' => $this->input->post('bank_name'),
                'amount' => $this->input->post('check_amount'),
                'added_by' => $_SESSION['user']->id
            ];
            if (!$detail_id = $this->payment_detail->insert($values)) {
                $this->_error = 'server_error';
                return false;
            }
        }

        return true;
    }

    /**
     * Add Paid Invoices
     */
    protected function _add_paid_invoices($payment_id, $paid_invoices)
    {
        foreach ($paid_invoices as &$paid_invoice) {
            $paid_invoice['payment_id'] = $payment_id;
            $paid_invoice['added_by'] = $_SESSION['user']->id;
            $paid_invoice['added_on'] = date('Y-m-d H:i:s');
        }
        unset($paid_invoice);

        if (!$is_inserted = $this->paid_invoice->insert_batch($paid_invoices)) {
            $this->_error = 'server_error';
            return false;
        }


        return $is_inserted;
    }

    /**
     * Callback: Check if Invoice Exists
     */
    public function _is_invoice($invoice_id)
    {
        $invoice = $this->invoice->get_by_id($invoice_id);
        return !empty($invoice);
    }

    /**
     * Callback: Check if Invoice No Has No Duplicate
     */
    public function _no_invoice_duplicate($invoice_id)
    {
        $copies = 0;

        foreach ($_POST as $field => $value) {
            if (strpos($field, 'invoice_') === 0 && $value === $invoice_id) {
                $copies++;
            }
        }

        return $copies <= 1;
    }

    /**
     * Delete Payment
     */
    public function delete($payment_id = null)
    {
        $where = [
            'id' => $payment_id,
            'is_deleted' => 0
        ];

        if (!$payment = $this->payment->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Payment not found.'
            ];
        } elseif (!$this->_attempt_delete($payment)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Payment is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($payment)
    {
        $where = [
            'id' => $payment->id
        ];

        $values = [
            'is_deleted'    => 1,
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->payment->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
