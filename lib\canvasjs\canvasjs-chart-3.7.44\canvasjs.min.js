/* 
 CanvasJS Chart - v3.7.44 GA - https://canvasjs.com/ 
 Copyright 2024 fenopix

 --------------------- License Information --------------------
 CanvasJS Chart is a commercial product which requires purchase of license. Without a commercial license you can use it for evaluation purposes for upto 30 days. Please refer to the following link for further details.
 https://canvasjs.com/license/


 ---------------------Free for Non-Commercial Use--------------------
 
 For non-commercial purposes you can use the software for free under Creative Commons Attribution-NonCommercial 3.0 License.
 A credit Link is added to the chart which should be preserved. Refer to the following link for further details on the same.
 https://creativecommons.org/licenses/by-nc/3.0/deed.en_US
 */

/*tslint:disable*/
/*eslint-disable*/
/*jshint ignore:start*/
(function(){function qa(h,n){h.prototype=db(n.prototype);h.prototype.constructor=h;h.base=n.prototype}function db(h){function n(){}n.prototype=h;return new n}function Xa(h,n,P){"millisecond"===P?h.setMilliseconds(h.getMilliseconds()+1*n):"second"===P?h.setSeconds(h.getSeconds()+1*n):"minute"===P?h.setMinutes(h.getMinutes()+1*n):"hour"===P?h.setHours(h.getHours()+1*n):"day"===P?h.setDate(h.getDate()+1*n):"week"===P?h.setDate(h.getDate()+7*n):"month"===P?h.setMonth(h.getMonth()+1*n):"year"===P&&h.setFullYear(h.getFullYear()+
1*n);return h}function ea(h,n){var P=!1;0>h&&(P=!0,h*=-1);h=""+h;for(n=n?n:1;h.length<n;)h="0"+h;return P?"-"+h:h}function Ha(h){if(!h)return h;h=h.replace(/^\s\s*/,"");for(var n=/\s/,P=h.length;n.test(h.charAt(--P)););return h.slice(0,P+1)}function Aa(h){h.roundRect=function(h,P,m,t,sa,D,z,w){z&&(this.fillStyle=z);w&&(this.strokeStyle=w);"undefined"===typeof sa&&(sa=5);this.lineWidth=D;this.beginPath();this.moveTo(h+sa,P);this.lineTo(h+m-sa,P);this.quadraticCurveTo(h+m,P,h+m,P+sa);this.lineTo(h+
m,P+t-sa);this.quadraticCurveTo(h+m,P+t,h+m-sa,P+t);this.lineTo(h+sa,P+t);this.quadraticCurveTo(h,P+t,h,P+t-sa);this.lineTo(h,P+sa);this.quadraticCurveTo(h,P,h+sa,P);this.closePath();z&&this.fill();w&&0<D&&this.stroke()}}function Ra(h,n){return h-n}function Z(h){var n=((h&16711680)>>16).toString(16),P=((h&65280)>>8).toString(16);h=((h&255)>>0).toString(16);n=2>n.length?"0"+n:n;P=2>P.length?"0"+P:P;h=2>h.length?"0"+h:h;return"#"+n+P+h}function eb(h,n){var P=this.length>>>0,m=Number(n)||0,m=0>m?Math.ceil(m):
Math.floor(m);for(0>m&&(m+=P);m<P;m++)if(m in this&&this[m]===h)return m;return-1}function m(h){return null===h||"undefined"===typeof h}function Ea(h){h.indexOf||(h.indexOf=eb);return h}function fb(h){if(xa.fSDec)h[ka("`eeDwdouMhrudods")](ka("e`u`@ohl`uhnoHuds`uhnoDoe"),function(){xa._fTWm&&xa._fTWm(h)})}function Ya(h,n,P){P=P||"normal";var m=h+"_"+n+"_"+P,t=Za[m];if(isNaN(t)){try{if(!ta){var sa=document.body;ta=document.createElement("span");ta.innerHTML="";var D=document.createTextNode("Mpgyi");
ta.appendChild(D);sa.appendChild(ta)}ta.style.display="";Y(ta,{position:"absolute",left:"0px",top:"-20000px",padding:"0px",margin:"0px",border:"none",whiteSpace:"pre",lineHeight:"normal",fontFamily:h,fontSize:n+"px",fontWeight:P});t=Math.round(ta.offsetHeight);ta.style.display="none"}catch(z){t=Math.ceil(1.1*n)}t=Math.max(t,n);Za[m]=t}return t}function H(h,n){var m=[];if(m={solid:[],shortDash:[3,1],shortDot:[1,1],shortDashDot:[3,1,1,1],shortDashDotDot:[3,1,1,1,1,1],dot:[1,2],dash:[4,2],dashDot:[4,
2,1,2],longDash:[8,2],longDashDot:[8,2,1,2],longDashDotDot:[8,2,1,2,1,2]}[h||"solid"])for(var t=0;t<m.length;t++)m[t]*=n;else m=[];return m}function S(h,n,P,t,ja){t=t||[];ja=m(ja)?gb?{passive:!1,capture:!1}:!1:ja;t.push([h,n,P,ja]);return h.addEventListener?(h.addEventListener(n,P,ja),P):h.attachEvent?(t=function(n){n=n||window.event;n.preventDefault=n.preventDefault||function(){n.returnValue=!1};n.stopPropagation=n.stopPropagation||function(){n.cancelBubble=!0};P.call(h,n)},h.attachEvent("on"+n,
t),t):!1}function hb(h){if(h._menuButton)h.exportEnabled?(Y(h._menuButton,{backgroundColor:h.toolbar.itemBackgroundColor,color:h.toolbar.fontColor}),Ma(h._menuButton),ua(h,h._menuButton,"menu")):ya(h._menuButton);else if(h.exportEnabled&&t){var n=!1;h._menuButton=document.createElement("button");ua(h,h._menuButton,"menu");h._toolBar.appendChild(h._menuButton);S(h._menuButton,"touchstart",function(h){n=!0},h.allDOMEventHandlers);S(h._menuButton,"click",function(){"none"!==h._dropdownMenu.style.display||
h._dropDownCloseTime&&500>=(new Date).getTime()-h._dropDownCloseTime.getTime()||(h._dropdownMenu.style.display="block",h._menuButton.blur(),h._dropdownMenu.focus())},h.allDOMEventHandlers,!0);S(h._menuButton,"mousemove",function(){n||(Y(h._menuButton,{backgroundColor:h.toolbar.itemBackgroundColorOnHover,color:h.toolbar.fontColorOnHover}),0>=navigator.userAgent.search("MSIE")&&Y(h._menuButton.childNodes[0],{WebkitFilter:"invert(100%)",filter:"invert(100%)"}))},h.allDOMEventHandlers,!0);S(h._menuButton,
"mouseout",function(){n||(Y(h._menuButton,{backgroundColor:h.toolbar.itemBackgroundColor,color:h.toolbar.fontColor}),0>=navigator.userAgent.search("MSIE")&&Y(h._menuButton.childNodes[0],{WebkitFilter:"invert(0%)",filter:"invert(0%)"}))},h.allDOMEventHandlers,!0)}if(h.exportEnabled&&h._dropdownMenu){Y(h._dropdownMenu,{backgroundColor:h.toolbar.itemBackgroundColor,color:h.toolbar.fontColor});for(var m=h._dropdownMenu.childNodes,$=[h._cultureInfo.printText,h._cultureInfo.saveJPGText,h._cultureInfo.savePNGText],
ja=0;ja<m.length;ja++)Y(m[ja],{backgroundColor:h.toolbar.itemBackgroundColor,color:h.toolbar.fontColor}),m[ja].innerHTML=$[ja]}else!h._dropdownMenu&&(h.exportEnabled&&t)&&(n=!1,h._dropdownMenu=document.createElement("div"),h._dropdownMenu.setAttribute("tabindex",-1),m=-1!==h.theme.indexOf("dark")?"black":"#888888",Y(h._dropdownMenu,{position:"absolute",zIndex:1,userSelect:"none",MozUserSeelct:"none",WebkitUserSelect:"none",msUserSelect:"none",cursor:"pointer",right:"0px",top:"25px",minWidth:"120px",
outline:0,fontSize:"14px",fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 0px 5px 0px",textAlign:"left",lineHeight:"10px",backgroundColor:h.toolbar.itemBackgroundColor,boxShadow:"2px 2px 10px"+m}),h._dropdownMenu.style.display="none",h._toolBar.appendChild(h._dropdownMenu),S(h._dropdownMenu,"blur",function(){ya(h._dropdownMenu);h._dropDownCloseTime=new Date},h.allDOMEventHandlers,!0),m=document.createElement("div"),Y(m,{padding:"12px 8px 12px 8px"}),m.innerHTML=h._cultureInfo.printText,m.style.backgroundColor=
h.toolbar.itemBackgroundColor,m.style.color=h.toolbar.fontColor,h._dropdownMenu.appendChild(m),S(m,"touchstart",function(h){n=!0},h.allDOMEventHandlers),S(m,"mousemove",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColorOnHover,this.style.color=h.toolbar.fontColorOnHover)},h.allDOMEventHandlers,!0),S(m,"mouseout",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColor,this.style.color=h.toolbar.fontColor)},h.allDOMEventHandlers,!0),S(m,"click",function(){h.print();
ya(h._dropdownMenu)},h.allDOMEventHandlers,!0),m=document.createElement("div"),Y(m,{padding:"12px 8px 12px 8px"}),m.innerHTML=h._cultureInfo.saveJPGText,m.style.backgroundColor=h.toolbar.itemBackgroundColor,m.style.color=h.toolbar.fontColor,h._dropdownMenu.appendChild(m),S(m,"touchstart",function(h){n=!0},h.allDOMEventHandlers),S(m,"mousemove",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColorOnHover,this.style.color=h.toolbar.fontColorOnHover)},h.allDOMEventHandlers,!0),S(m,
"mouseout",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColor,this.style.color=h.toolbar.fontColor)},h.allDOMEventHandlers,!0),S(m,"click",function(){h.exportChart({format:"jpeg",fileName:h.exportFileName});ya(h._dropdownMenu)},h.allDOMEventHandlers,!0),m=document.createElement("div"),Y(m,{padding:"12px 8px 12px 8px"}),m.innerHTML=h._cultureInfo.savePNGText,m.style.backgroundColor=h.toolbar.itemBackgroundColor,m.style.color=h.toolbar.fontColor,h._dropdownMenu.appendChild(m),S(m,
"touchstart",function(h){n=!0},h.allDOMEventHandlers),S(m,"mousemove",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColorOnHover,this.style.color=h.toolbar.fontColorOnHover)},h.allDOMEventHandlers,!0),S(m,"mouseout",function(){n||(this.style.backgroundColor=h.toolbar.itemBackgroundColor,this.style.color=h.toolbar.fontColor)},h.allDOMEventHandlers,!0),S(m,"click",function(){h.exportChart({format:"png",fileName:h.exportFileName});ya(h._dropdownMenu)},h.allDOMEventHandlers,!0))}function $a(h,
n,m){h*=na;n*=na;h=m.getImageData(h,n,2,2).data;n=!0;for(m=0;4>m;m++)if(h[m]!==h[m+4]|h[m]!==h[m+8]|h[m]!==h[m+12]){n=!1;break}return n?h[0]<<16|h[1]<<8|h[2]:0}function oa(h,n,m){return h in n?n[h]:m[h]}function Na(h,n,P,$){t&&ab?($=!m($)&&$?h.getContext("2d",{willReadFrequently:!0}):h.getContext("2d"),Oa=$.webkitBackingStorePixelRatio||$.mozBackingStorePixelRatio||$.msBackingStorePixelRatio||$.oBackingStorePixelRatio||$.backingStorePixelRatio||1,na=Sa/Oa,h.width=n*na,h.height=P*na,Sa!==Oa&&(h.style.width=
n+"px",h.style.height=P+"px",$.scale(na,na))):(h.width=n,h.height=P)}function ib(h){if(!jb){var n=!1,m=!1;"undefined"===typeof ra.Chart.creditHref?(h.creditHref=ka("iuuqr;..b`ow`rkr/bnl."),h.creditText=ka("B`ow`rKR/bnl")):(n=h.updateOption("creditText"),m=h.updateOption("creditHref"));if(h.creditHref&&h.creditText){h._creditLink||(h._creditLink=document.createElement("a"),h._creditLink.setAttribute("class","canvasjs-chart-credit"),h._creditLink.setAttribute("title","JavaScript Charts"),Y(h._creditLink,
{outline:"none",margin:"0px",position:"absolute",right:"2px",top:h.height-14+"px",color:"dimgrey",textDecoration:"none",fontSize:"11px",fontFamily:"Calibri, Lucida Grande, Lucida Sans Unicode, Arial, sans-serif"}),h._creditLink.setAttribute("tabIndex",-1),h._creditLink.setAttribute("target","_blank"));if(0===h.renderCount||n||m)h._creditLink.setAttribute("href",h.creditHref),h._creditLink.innerHTML=h.creditText;h._creditLink&&h.creditHref&&h.creditText?(h._creditLink.parentElement||h._canvasJSContainer.appendChild(h._creditLink),
h._creditLink.style.top=h.height-14+"px"):h._creditLink.parentElement&&h._canvasJSContainer.removeChild(h._creditLink)}}}function wa(h,n,m){Ia&&(this.canvasCount|=0,window.console.log(++this.canvasCount));var $=document.createElement("canvas");$.setAttribute("class","canvasjs-chart-canvas");Na($,h,n,m);t||"undefined"===typeof G_vmlCanvasManager||G_vmlCanvasManager.initElement($);return $}function Y(h,n){for(var m in n)h.style[m]=n[m]}function ua(h,n,m){n.getAttribute("state")||(n.style.backgroundColor=
h.toolbar.itemBackgroundColor,n.style.color=h.toolbar.fontColor,n.style.border="none",Y(n,{WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",userSelect:"none"}));n.getAttribute("state")!==m&&(n.setAttribute("state",m),n.setAttribute("type","button"),Y(n,{padding:"5px 12px",cursor:"pointer","float":"left",width:"40px",height:"25px",outline:"0px",verticalAlign:"baseline",lineHeight:"0"}),n.innerHTML="<img src='"+kb[m].image+"' alt='"+h._cultureInfo[m+"Text"]+"' />",Y(n.childNodes[0],
{height:"95%",pointerEvents:"none"}));n.setAttribute("title",h._cultureInfo[m+"Text"])}function Ma(){for(var h=null,n=0;n<arguments.length;n++)h=arguments[n],h.style&&(h.style.display="inline")}function ya(){for(var h=null,n=0;n<arguments.length;n++)(h=arguments[n])&&h.style&&(h.style.display="none")}function Ta(h,n,m,t,ja){if(null===h||"undefined"===typeof h)return"undefined"===typeof m?n:m;h=parseFloat(h.toString())*(0<=h.toString().indexOf("%")?n/100:1);"undefined"!==typeof t&&(h=Math.min(t,h),
"undefined"!==typeof ja&&(h=Math.max(ja,h)));return!isNaN(h)&&h<=n&&0<=h?h:"undefined"===typeof m?n:m}function L(h,n,t,$,ja){this._defaultsKey=h;this._themeOptionsKey=n;this._index=$;this.parent=ja;this._eventListeners=[];h={};this.theme&&m(this.parent)&&m(n)&&m($)?h=m(this.predefinedThemes[this.theme])?this.predefinedThemes.light1:this.predefinedThemes[this.theme]:this.parent&&(this.parent.themeOptions&&this.parent.themeOptions[n])&&(null===$?h=this.parent.themeOptions[n]:0<this.parent.themeOptions[n].length&&
($=Math.min(this.parent.themeOptions[n].length-1,$),h=this.parent.themeOptions[n][$]));this.themeOptions=h;this.options=t?t:{_isPlaceholder:!0};this.setOptions(this.options,h)}function Fa(h,n,m,t,ja){"undefined"===typeof ja&&(ja=0);this._padding=ja;this._x1=h;this._y1=n;this._x2=m;this._y2=t;this._rightOccupied=this._leftOccupied=this._bottomOccupied=this._topOccupied=this._padding}function la(h,n){la.base.constructor.call(this,"TextBlock",null,n,null,null);this.ctx=h;this._isDirty=!0;this._wrappedText=
null;this._initialize()}function Ua(h,n){Ua.base.constructor.call(this,"Toolbar","toolbar",n,null,h);this.chart=h;this.canvas=h.canvas;this.ctx=this.chart.ctx;this.optionsName="toolbar"}function Ba(h,n){Ba.base.constructor.call(this,"Title","title",n,null,h);this.chart=h;this.canvas=h.canvas;this.ctx=this.chart.ctx;this.optionsName="title";if(m(this.options.margin)&&h.options.subtitles)for(var t=h.options.subtitles,$=0;$<t.length;$++)if((m(t[$].horizontalAlign)&&"center"===this.horizontalAlign||t[$].horizontalAlign===
this.horizontalAlign)&&(m(t[$].verticalAlign)&&"top"===this.verticalAlign||t[$].verticalAlign===this.verticalAlign)&&!t[$].dockInsidePlotArea===!this.dockInsidePlotArea){this.margin=0;break}"undefined"===typeof this.options.fontSize&&(this.fontSize=this.chart.getAutoFontSize(this.fontSize));this.height=this.width=null;this.bounds={x1:null,y1:null,x2:null,y2:null}}function Ja(h,n,m){Ja.base.constructor.call(this,"Subtitle","subtitles",n,m,h);this.chart=h;this.canvas=h.canvas;this.ctx=this.chart.ctx;
this.optionsName="subtitles";this.isOptionsInArray=!0;"undefined"===typeof this.options.fontSize&&(this.fontSize=this.chart.getAutoFontSize(this.fontSize));this.height=this.width=null;this.bounds={x1:null,y1:null,x2:null,y2:null}}function Va(){this.pool=[]}function Ka(h){var n;h&&La[h]&&(n=La[h]);Ka.base.constructor.call(this,"CultureInfo",null,n,null,null)}var Ia=!1,xa={},t=!!document.createElement("canvas").getContext,ra={Chart:{width:500,height:400,zoomEnabled:!1,zoomType:"x",backgroundColor:"white",
theme:"light1",animationEnabled:!1,animationDuration:1200,dataPointWidth:null,dataPointMinWidth:null,dataPointMaxWidth:null,colorSet:"colorSet1",culture:"en",creditHref:"",creditText:"CanvasJS",interactivityEnabled:!0,exportEnabled:!1,exportFileName:"Chart",rangeChanging:null,rangeChanged:null,publicProperties:{title:"readWrite",subtitles:"readWrite",toolbar:"readWrite",toolTip:"readWrite",legend:"readWrite",axisX:"readWrite",axisY:"readWrite",axisX2:"readWrite",axisY2:"readWrite",data:"readWrite",
options:"readWrite",bounds:"readOnly",container:"readOnly",selectedColorSet:"readOnly"}},Title:{padding:0,text:null,verticalAlign:"top",horizontalAlign:"center",fontSize:20,fontFamily:"Calibri",fontWeight:"normal",fontColor:"black",fontStyle:"normal",borderThickness:0,borderColor:"black",cornerRadius:0,backgroundColor:t?"transparent":null,margin:5,wrap:!0,maxWidth:null,dockInsidePlotArea:!1,publicProperties:{options:"readWrite",bounds:"readOnly",chart:"readOnly"}},Subtitle:{padding:0,text:null,verticalAlign:"top",
horizontalAlign:"center",fontSize:14,fontFamily:"Calibri",fontWeight:"normal",fontColor:"black",fontStyle:"normal",borderThickness:0,borderColor:"black",cornerRadius:0,backgroundColor:null,margin:2,wrap:!0,maxWidth:null,dockInsidePlotArea:!1,publicProperties:{options:"readWrite",bounds:"readOnly",chart:"readOnly"}},Toolbar:{itemBackgroundColor:"white",itemBackgroundColorOnHover:"#2196f3",buttonBorderColor:"#2196f3",buttonBorderThickness:1,fontColor:"black",fontColorOnHover:"white",publicProperties:{options:"readWrite",
chart:"readOnly"}},Legend:{name:null,verticalAlign:"center",horizontalAlign:"right",fontSize:14,fontFamily:"calibri",fontWeight:"normal",fontColor:"black",fontStyle:"normal",cursor:null,itemmouseover:null,itemmouseout:null,itemmousemove:null,itemclick:null,dockInsidePlotArea:!1,reversed:!1,backgroundColor:t?"transparent":null,borderColor:t?"transparent":null,borderThickness:0,cornerRadius:0,maxWidth:null,maxHeight:null,markerMargin:null,itemMaxWidth:null,itemWidth:null,itemWrap:!0,itemTextFormatter:null,
publicProperties:{options:"readWrite",bounds:"readOnly",chart:"readOnly"}},ToolTip:{enabled:!0,shared:!1,animationEnabled:!0,content:null,contentFormatter:null,reversed:!1,backgroundColor:t?"rgba(255,255,255,.9)":"rgb(255,255,255)",borderColor:null,borderThickness:2,cornerRadius:5,fontSize:14,fontColor:"black",fontFamily:"Calibri, Arial, Georgia, serif;",fontWeight:"normal",fontStyle:"italic",updated:null,hidden:null,publicProperties:{options:"readWrite",chart:"readOnly"}},Axis:{minimum:null,maximum:null,
viewportMinimum:null,viewportMaximum:null,interval:null,intervalType:null,reversed:!1,logarithmic:!1,logarithmBase:10,title:null,titleFontColor:"black",titleFontSize:20,titleFontFamily:"arial",titleFontWeight:"normal",titleFontStyle:"normal",titleWrap:!0,titleMaxWidth:null,titleBackgroundColor:t?"transparent":null,titleBorderColor:t?"transparent":null,titleBorderThickness:0,titleCornerRadius:0,labelAngle:0,labelFontFamily:"arial",labelFontColor:"black",labelFontSize:12,labelFontWeight:"normal",labelFontStyle:"normal",
labelAutoFit:!0,labelWrap:!0,labelMaxWidth:null,labelFormatter:null,labelBackgroundColor:t?"transparent":null,labelBorderColor:t?"transparent":null,labelBorderThickness:0,labelCornerRadius:0,labelPlacement:"outside",labelTextAlign:"left",prefix:"",suffix:"",includeZero:!1,tickLength:5,tickColor:"black",tickThickness:1,tickPlacement:"outside",lineColor:"black",lineThickness:1,lineDashType:"solid",gridColor:"#A0A0A0",gridThickness:0,gridDashType:"solid",interlacedColor:t?"transparent":null,valueFormatString:null,
margin:2,publicProperties:{options:"readWrite",stripLines:"readWrite",scaleBreaks:"readWrite",crosshair:"readWrite",bounds:"readOnly",chart:"readOnly"}},StripLine:{value:null,startValue:null,endValue:null,color:"orange",opacity:null,thickness:2,lineDashType:"solid",label:"",labelPlacement:"inside",labelAlign:"far",labelWrap:!0,labelMaxWidth:null,labelBackgroundColor:null,labelBorderColor:t?"transparent":null,labelBorderThickness:0,labelCornerRadius:0,labelFontFamily:"arial",labelFontColor:"orange",
labelFontSize:12,labelFontWeight:"normal",labelFontStyle:"normal",labelFormatter:null,showOnTop:!1,publicProperties:{options:"readWrite",axis:"readOnly",bounds:"readOnly",chart:"readOnly"}},ScaleBreaks:{autoCalculate:!1,collapsibleThreshold:"25%",maxNumberOfAutoBreaks:2,spacing:8,type:"straight",color:"#FFFFFF",fillOpacity:0.9,lineThickness:2,lineColor:"#E16E6E",lineDashType:"solid",publicProperties:{options:"readWrite",customBreaks:"readWrite",axis:"readOnly",autoBreaks:"readOnly",bounds:"readOnly",
chart:"readOnly"}},Break:{startValue:null,endValue:null,spacing:8,type:"straight",color:"#FFFFFF",fillOpacity:0.9,lineThickness:2,lineColor:"#E16E6E",lineDashType:"solid",publicProperties:{options:"readWrite",scaleBreaks:"readOnly",bounds:"readOnly",chart:"readOnly"}},Crosshair:{enabled:!1,snapToDataPoint:!1,color:"grey",opacity:null,thickness:2,lineDashType:"solid",label:"",labelWrap:!0,labelMaxWidth:null,labelBackgroundColor:t?"grey":null,labelBorderColor:t?"grey":null,labelBorderThickness:0,labelCornerRadius:0,
labelFontFamily:t?"Calibri, Optima, Candara, Verdana, Geneva, sans-serif":"calibri",labelFontSize:12,labelFontColor:"#fff",labelFontWeight:"normal",labelFontStyle:"normal",labelFormatter:null,valueFormatString:null,updated:null,hidden:null,publicProperties:{options:"readWrite",axis:"readOnly",bounds:"readOnly",chart:"readOnly"}},DataSeries:{name:null,dataPoints:null,label:"",bevelEnabled:!1,highlightEnabled:!0,cursor:"default",indexLabel:"",indexLabelPlacement:"auto",indexLabelOrientation:"horizontal",
indexLabelTextAlign:"left",indexLabelFontColor:"black",indexLabelFontSize:12,indexLabelFontStyle:"normal",indexLabelFontFamily:"Arial",indexLabelFontWeight:"normal",indexLabelBackgroundColor:null,indexLabelLineColor:"gray",indexLabelLineThickness:1,indexLabelLineDashType:"solid",indexLabelMaxWidth:null,indexLabelWrap:!0,indexLabelFormatter:null,lineThickness:2,lineDashType:"solid",connectNullData:!1,nullDataLineDashType:"dash",color:null,lineColor:null,risingColor:"white",fallingColor:"red",fillOpacity:null,
startAngle:0,radius:null,innerRadius:null,neckHeight:null,neckWidth:null,reversed:!1,valueRepresents:null,linkedDataSeriesIndex:null,whiskerThickness:2,whiskerDashType:"solid",whiskerColor:null,whiskerLength:null,stemThickness:2,stemColor:null,stemDashType:"solid",upperBoxColor:"white",lowerBoxColor:"white",type:"column",xValueType:"number",axisXType:"primary",axisYType:"primary",axisXIndex:0,axisYIndex:0,xValueFormatString:null,yValueFormatString:null,zValueFormatString:null,percentFormatString:null,
showInLegend:null,legendMarkerType:null,legendMarkerColor:null,legendText:null,legendMarkerBorderColor:t?"transparent":null,legendMarkerBorderThickness:0,markerType:"circle",markerColor:null,markerSize:null,markerBorderColor:t?"transparent":null,markerBorderThickness:0,mouseover:null,mouseout:null,mousemove:null,click:null,toolTipContent:null,visible:!0,publicProperties:{options:"readWrite",axisX:"readWrite",axisY:"readWrite",chart:"readOnly"}},TextBlock:{x:0,y:0,width:null,height:null,maxWidth:null,
maxHeight:null,padding:0,angle:0,text:"",horizontalAlign:"center",textAlign:"left",fontSize:12,fontFamily:"calibri",fontWeight:"normal",fontColor:"black",fontStyle:"normal",borderThickness:0,borderColor:"black",cornerRadius:0,backgroundColor:null,textBaseline:"top"},CultureInfo:{decimalSeparator:".",digitGroupSeparator:",",zoomText:"Zoom",panText:"Pan",resetText:"Reset",menuText:"More Options",saveJPGText:"Save as JPEG",savePNGText:"Save as PNG",printText:"Print",days:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),
shortDays:"Sun Mon Tue Wed Thu Fri Sat".split(" "),months:"January February March April May June July August September October November December".split(" "),shortMonths:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" ")}},La={en:{}},z=t?"Trebuchet MS, Helvetica, sans-serif":"Arial",Ga=t?"Impact, Charcoal, sans-serif":"Arial",Ca={colorSet1:"#4F81BC #C0504E #9BBB58 #23BFAA #8064A1 #4AACC5 #F79647 #7F6084 #77A033 #33558B #E59566".split(" "),colorSet2:"#6D78AD #51CDA0 #DF7970 #4C9CA0 #AE7D99 #C9D45C #5592AD #DF874D #52BCA8 #8E7AA3 #E3CB64 #C77B85 #C39762 #8DD17E #B57952 #FCC26C".split(" "),
colorSet3:"#8CA1BC #36845C #017E82 #8CB9D0 #708C98 #94838D #F08891 #0366A7 #008276 #EE7757 #E5BA3A #F2990B #03557B #782970".split(" ")},E,da,Q,W,aa;da="#333333";Q="#000000";E="#666666";aa=W="#000000";var fa=20,w=14,Wa={colorSet:"colorSet1",backgroundColor:"#FFFFFF",title:{fontFamily:Ga,fontSize:32,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5},subtitles:[{fontFamily:Ga,fontSize:w,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5}],data:[{indexLabelFontFamily:z,indexLabelFontSize:w,
indexLabelFontColor:da,indexLabelFontWeight:"normal",indexLabelLineThickness:1}],axisX:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:da,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,
labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],axisX2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:da,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,
labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],axisY:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:da,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,
labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,
lineDashType:"solid"}}],axisY2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:da,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",
labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],legend:{fontFamily:z,fontSize:14,fontColor:da,fontWeight:"bold",verticalAlign:"bottom",horizontalAlign:"center"},toolTip:{fontFamily:z,fontSize:14,fontStyle:"normal",cornerRadius:0,borderThickness:1},toolbar:{itemBackgroundColor:"white",itemBackgroundColorOnHover:"#2196f3",buttonBorderColor:"#2196f3",buttonBorderThickness:1,fontColor:"black",
fontColorOnHover:"white"}};Q=da="#F5F5F5";E="#FFFFFF";W="#40BAF1";aa="#F5F5F5";var fa=20,w=14,bb={colorSet:"colorSet2",title:{fontFamily:z,fontSize:33,fontColor:"#3A3A3A",fontWeight:"bold",verticalAlign:"top",margin:5},subtitles:[{fontFamily:z,fontSize:w,fontColor:"#3A3A3A",fontWeight:"normal",verticalAlign:"top",margin:5}],data:[{indexLabelFontFamily:z,indexLabelFontSize:w,indexLabelFontColor:"#666666",indexLabelFontWeight:"normal",indexLabelLineThickness:1}],axisX:[{titleFontFamily:z,titleFontSize:fa,
titleFontColor:"#666666",titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:"#666666",labelFontWeight:"normal",lineThickness:1,lineColor:"#BBBBBB",tickThickness:1,tickColor:"#BBBBBB",gridThickness:1,gridColor:"#BBBBBB",stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FFA500",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FFA500",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:"black",
color:"black",thickness:1,lineDashType:"dot"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],axisX2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:"#666666",titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:"#666666",labelFontWeight:"normal",lineThickness:1,lineColor:"#BBBBBB",tickColor:"#BBBBBB",tickThickness:1,gridThickness:1,gridColor:"#BBBBBB",stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FFA500",
labelFontWeight:"normal",labelBackgroundColor:null,color:"#FFA500",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:"black",color:"black",thickness:1,lineDashType:"dot"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],axisY:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:"#666666",titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:"#666666",
labelFontWeight:"normal",lineThickness:0,lineColor:"#BBBBBB",tickColor:"#BBBBBB",tickThickness:1,gridThickness:1,gridColor:"#BBBBBB",stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FFA500",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FFA500",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:"black",color:"black",thickness:1,lineDashType:"dot"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",
lineThickness:1,lineDashType:"solid"}}],axisY2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:"#666666",titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:"#666666",labelFontWeight:"normal",lineThickness:0,lineColor:"#BBBBBB",tickColor:"#BBBBBB",tickThickness:1,gridThickness:1,gridColor:"#BBBBBB",stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FFA500",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FFA500",thickness:1}],crosshair:{labelFontFamily:z,
labelFontSize:w,labelFontColor:"#EEEEEE",labelFontWeight:"normal",labelBackgroundColor:"black",color:"black",thickness:1,lineDashType:"dot"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#BBBBBB",lineThickness:1,lineDashType:"solid"}}],legend:{fontFamily:z,fontSize:14,fontColor:"#3A3A3A",fontWeight:"bold",verticalAlign:"bottom",horizontalAlign:"center"},toolTip:{fontFamily:z,fontSize:14,fontStyle:"normal",cornerRadius:0,borderThickness:1},toolbar:{itemBackgroundColor:"white",itemBackgroundColorOnHover:"#2196f3",
buttonBorderColor:"#2196f3",buttonBorderThickness:1,fontColor:"black",fontColorOnHover:"white"}};Q=da="#F5F5F5";E="#FFFFFF";W="#40BAF1";aa="#F5F5F5";fa=20;w=14;Ga={colorSet:"colorSet12",backgroundColor:"#2A2A2A",title:{fontFamily:Ga,fontSize:32,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5},subtitles:[{fontFamily:Ga,fontSize:w,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5}],toolbar:{itemBackgroundColor:"#666666",itemBackgroundColorOnHover:"#FF7372",buttonBorderColor:"#FF7372",
buttonBorderThickness:1,fontColor:"#F5F5F5",fontColorOnHover:"#F5F5F5"},data:[{indexLabelFontFamily:z,indexLabelFontSize:w,indexLabelFontColor:Q,indexLabelFontWeight:"normal",indexLabelLineThickness:1}],axisX:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",
labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],axisX2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",
lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],
axisY:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,
color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],axisY2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,
color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],legend:{fontFamily:z,fontSize:14,fontColor:da,fontWeight:"bold",verticalAlign:"bottom",horizontalAlign:"center"},toolTip:{fontFamily:z,fontSize:14,fontStyle:"normal",cornerRadius:0,borderThickness:1,
fontColor:Q,backgroundColor:"rgba(0, 0, 0, .7)"}};E="#FFFFFF";Q=da="#FAFAFA";W="#40BAF1";aa="#F5F5F5";var fa=20,w=14,cb={light1:Wa,light2:bb,dark1:Ga,dark2:{colorSet:"colorSet2",backgroundColor:"#32373A",title:{fontFamily:z,fontSize:32,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5},subtitles:[{fontFamily:z,fontSize:w,fontColor:da,fontWeight:"normal",verticalAlign:"top",margin:5}],toolbar:{itemBackgroundColor:"#666666",itemBackgroundColorOnHover:"#FF7372",buttonBorderColor:"#FF7372",
buttonBorderThickness:1,fontColor:"#F5F5F5",fontColorOnHover:"#F5F5F5"},data:[{indexLabelFontFamily:z,indexLabelFontSize:w,indexLabelFontColor:Q,indexLabelFontWeight:"normal",indexLabelLineThickness:1}],axisX:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",
labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],axisX2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",
lineThickness:1,lineColor:E,tickThickness:1,tickColor:E,gridThickness:0,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],
axisY:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:0,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,
color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],axisY2:[{titleFontFamily:z,titleFontSize:fa,titleFontColor:Q,titleFontWeight:"normal",labelFontFamily:z,labelFontSize:w,labelFontColor:Q,labelFontWeight:"normal",lineThickness:0,lineColor:E,tickThickness:1,tickColor:E,gridThickness:1,gridColor:E,stripLines:[{labelFontFamily:z,labelFontSize:w,labelFontColor:"#FF7300",labelFontWeight:"normal",labelBackgroundColor:null,
color:"#FF7300",thickness:1}],crosshair:{labelFontFamily:z,labelFontSize:w,labelFontColor:"#000000",labelFontWeight:"normal",labelBackgroundColor:aa,color:W,thickness:1,lineDashType:"dash"},scaleBreaks:{type:"zigzag",spacing:"2%",lineColor:"#777777",lineThickness:1,lineDashType:"solid",color:"#111111"}}],legend:{fontFamily:z,fontSize:14,fontColor:da,fontWeight:"bold",verticalAlign:"bottom",horizontalAlign:"center"},toolTip:{fontFamily:z,fontSize:14,fontStyle:"normal",cornerRadius:0,borderThickness:1,
fontColor:Q,backgroundColor:"rgba(0, 0, 0, .7)"}},theme1:Wa,theme2:bb,theme3:Wa},U={numberDuration:1,yearDuration:314496E5,monthDuration:2592E6,weekDuration:6048E5,dayDuration:864E5,hourDuration:36E5,minuteDuration:6E4,secondDuration:1E3,millisecondDuration:1,dayOfWeekFromInt:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" ")};(function(){xa.fSDec=function(h){for(var n="",m=0;m<h.length;m++)n+=String.fromCharCode(Math.ceil(h.length/57/5)^h.charCodeAt(m));return n};delete ra[xa.fSDec("Bi`su")][xa.fSDec("bsdehuIsdg")];
xa.pro={sCH:ra[xa.fSDec("Bi`su")][xa.fSDec("bsdehuIsdg")]}})();var gb=function(){var h=!1;try{var n=Object.defineProperty&&Object.defineProperty({},"passive",{get:function(){h=!0;return!1}});window.addEventListener&&(window.addEventListener("test",null,n),window.removeEventListener("test",null,n))}catch(m){h=!1}return h}(),Za={},ta=null,lb=function(){this.ctx.clearRect(0,0,this.width,this.height);this.backgroundColor&&(this.ctx.fillStyle=this.backgroundColor,this.ctx.fillRect(0,0,this.width,this.height))},
za=function(h){h.width=1;h.height=1;h.getContext("2d")&&h.getContext("2d").clearRect(0,0,1,1)},mb=function(h,n,m){n=Math.min(this.width,this.height);return Math.max("theme4"===this.theme?0:300<=n?12:11,Math.round(n*(h/400)))},Da=function(){var h=/D{1,4}|M{1,4}|Y{1,4}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|f{1,3}|t{1,2}|T{1,2}|K|z{1,3}|"[^"]*"|'[^']*'/g,n="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),m="Sun Mon Tue Wed Thu Fri Sat".split(" "),t="January February March April May June July August September October November December".split(" "),
ja="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),z=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,D=/[^-+\dA-Z]/g;return function(w,H,O){var E=O?O.days:n,Z=O?O.months:t,S=O?O.shortDays:m,U=O?O.shortMonths:ja;O="";var Y=!1;w=w&&w.getTime?w:w?new Date(w):new Date;if(isNaN(w))throw SyntaxError("invalid date");"UTC:"===H.slice(0,4)&&(H=H.slice(4),Y=!0);O=Y?"getUTC":"get";var L=w[O+"Date"](),N=w[O+"Day"](),
X=w[O+"Month"](),a=w[O+"FullYear"](),d=w[O+"Hours"](),c=w[O+"Minutes"](),b=w[O+"Seconds"](),e=w[O+"Milliseconds"](),g=Y?0:w.getTimezoneOffset();return O=H.replace(h,function(r){switch(r){case "D":return L;case "DD":return ea(L,2);case "DDD":return S[N];case "DDDD":return E[N];case "M":return X+1;case "MM":return ea(X+1,2);case "MMM":return U[X];case "MMMM":return Z[X];case "Y":return parseInt(String(a).slice(-2));case "YY":return ea(String(a).slice(-2),2);case "YYY":return ea(String(a).slice(-3),
3);case "YYYY":return ea(a,4);case "h":return d%12||12;case "hh":return ea(d%12||12,2);case "H":return d;case "HH":return ea(d,2);case "m":return c;case "mm":return ea(c,2);case "s":return b;case "ss":return ea(b,2);case "f":return ea(String(e),3).slice(0,1);case "ff":return ea(String(e),3).slice(0,2);case "fff":return ea(String(e),3).slice(0,3);case "t":return 12>d?"a":"p";case "tt":return 12>d?"am":"pm";case "T":return 12>d?"A":"P";case "TT":return 12>d?"AM":"PM";case "K":return Y?"UTC":(String(w).match(z)||
[""]).pop().replace(D,"");case "z":return(0<g?"-":"+")+Math.floor(Math.abs(g)/60);case "zz":return(0<g?"-":"+")+ea(Math.floor(Math.abs(g)/60),2);case "zzz":return(0<g?"-":"+")+ea(Math.floor(Math.abs(g)/60),2)+ea(Math.abs(g)%60,2);default:return r.slice(1,r.length-1)}})}}(),nb=function(h){var n=0>h;if(1>Math.abs(h)){var m=parseInt(h.toString().split("e-")[1]);m&&(h=(n?-1*h:h)*Math.pow(10,m-1),h="0."+Array(m).join("0")+h.toString().substring(2),h=n?"-"+h:h)}else m=parseInt(h.toString().split("+")[1]),
20<m&&(m-=20,h/=Math.pow(10,m),h=h.toString()+Array(m+1).join("0"));return String(h)},ga=function(h,n,m){if(null===h)return"";if(!isFinite(h))return h;h=Number(h);var t=0>h?!0:!1;t&&(h*=-1);var z=m?m.decimalSeparator:".",w=m?m.digitGroupSeparator:",",D="";n=String(n);var D=1,H=m="",E=-1,O=[],Z=[],S=0,U=0,Y=0,L=!1,Q=0,H=n.match(/"[^"]*"|'[^']*'|[eE][+-]*[0]+|[,]+[.]|\u2030|./g);n=null;for(var N=0;H&&N<H.length;N++)if(n=H[N],"."===n&&0>E)E=N;else{if("%"===n)D*=100;else if("\u2030"===n){D*=1E3;continue}else if(","===
n[0]&&"."===n[n.length-1]){D/=Math.pow(1E3,n.length-1);E=N+n.length-1;continue}else"E"!==n[0]&&"e"!==n[0]||"0"!==n[n.length-1]||(L=!0);0>E?(O.push(n),"#"===n||"0"===n?S++:","===n&&Y++):(Z.push(n),"#"!==n&&"0"!==n||U++)}L&&(n=Math.floor(h),H=-Math.floor(Math.log(h)/Math.LN10+1),Q=0===h?0:0===n?-(S+H):nb(n).length-S,D/=Math.pow(10,Q));0>E&&(E=N);D=(h*D).toFixed(U);n=D.split(".");D=(n[0]+"").split("");h=(n[1]+"").split("");D&&"0"===D[0]&&D.shift();for(L=H=N=U=E=0;0<O.length;)if(n=O.pop(),"#"===n||"0"===
n)if(E++,E===S){var X=D,D=[];if("0"===n)for(n=S-U-(X?X.length:0);0<n;)X.unshift("0"),n--;for(;0<X.length;)m=X.pop()+m,L++,0===L%H&&(N===Y&&0<X.length)&&(m=w+m)}else 0<D.length?(m=D.pop()+m,U++,L++):"0"===n&&(m="0"+m,U++,L++),0===L%H&&(N===Y&&0<D.length)&&(m=w+m);else"E"!==n[0]&&"e"!==n[0]||"0"!==n[n.length-1]||!/[eE][+-]*[0]+/.test(n)?","===n?(N++,H=L,L=0,0<D.length&&(m=w+m)):m=1<n.length&&('"'===n[0]&&'"'===n[n.length-1]||"'"===n[0]&&"'"===n[n.length-1])?n.slice(1,n.length-1)+m:n+m:(n=0>Q?n.replace("+",
"").replace("-",""):n.replace("-",""),m+=n.replace(/[0]+/,function(a){return ea(Q,a.length)}));w="";for(O=!1;0<Z.length;)n=Z.shift(),"#"===n||"0"===n?0<h.length&&0!==Number(h.join(""))?(w+=h.shift(),O=!0):"0"===n&&(w+="0",O=!0):1<n.length&&('"'===n[0]&&'"'===n[n.length-1]||"'"===n[0]&&"'"===n[n.length-1])?w+=n.slice(1,n.length-1):"E"!==n[0]&&"e"!==n[0]||"0"!==n[n.length-1]||!/[eE][+-]*[0]+/.test(n)?w+=n:(n=0>Q?n.replace("+","").replace("-",""):n.replace("-",""),w+=n.replace(/[0]+/,function(a){return ea(Q,
a.length)}));m+=(O?z:"")+w;return t?"-"+m:m},Pa=function(h){var n=0,m=0;h=h||window.event;h.offsetX||0===h.offsetX?(n=h.offsetX,m=h.offsetY):h.layerX||0==h.layerX?(n=h.layerX,m=h.layerY):(n=h.pageX-h.target.offsetLeft,m=h.pageY-h.target.offsetTop);return{x:n,y:m}},ab=!0,Sa=window.devicePixelRatio||1,Oa=1,na=ab?Sa/Oa:1,ba=function(h,n,m,t,w,z,D,H,E,O,Z,U,S){"undefined"===typeof S&&(S=1);D=D||0;H=H||"black";var L=15<t-n&&15<w-m?8:0.35*Math.min(t-n,w-m);h.beginPath();h.moveTo(n,m);h.save();h.fillStyle=
z;h.globalAlpha=S;h.fillRect(n,m,t-n,w-m);h.globalAlpha=1;0<D&&(S=0===D%2?0:0.5,h.beginPath(),h.lineWidth=D,h.strokeStyle=H,h.moveTo(n,m),h.rect(n-S,m-S,t-n+2*S,w-m+2*S),h.stroke());h.restore();!0===E&&(h.save(),h.beginPath(),h.moveTo(n,m),h.lineTo(n+L,m+L),h.lineTo(t-L,m+L),h.lineTo(t,m),h.closePath(),D=h.createLinearGradient((t+n)/2,m+L,(t+n)/2,m),D.addColorStop(0,z),D.addColorStop(1,"rgba(255, 255, 255, .4)"),h.fillStyle=D,h.fill(),h.restore());!0===O&&(h.save(),h.beginPath(),h.moveTo(n,w),h.lineTo(n+
L,w-L),h.lineTo(t-L,w-L),h.lineTo(t,w),h.closePath(),D=h.createLinearGradient((t+n)/2,w-L,(t+n)/2,w),D.addColorStop(0,z),D.addColorStop(1,"rgba(255, 255, 255, .4)"),h.fillStyle=D,h.fill(),h.restore());!0===Z&&(h.save(),h.beginPath(),h.moveTo(n,m),h.lineTo(n+L,m+L),h.lineTo(n+L,w-L),h.lineTo(n,w),h.closePath(),D=h.createLinearGradient(n+L,(w+m)/2,n,(w+m)/2),D.addColorStop(0,z),D.addColorStop(1,"rgba(255, 255, 255, 0.1)"),h.fillStyle=D,h.fill(),h.restore());!0===U&&(h.save(),h.beginPath(),h.moveTo(t,
m),h.lineTo(t-L,m+L),h.lineTo(t-L,w-L),h.lineTo(t,w),D=h.createLinearGradient(t-L,(w+m)/2,t,(w+m)/2),D.addColorStop(0,z),D.addColorStop(1,"rgba(255, 255, 255, 0.1)"),h.fillStyle=D,D.addColorStop(0,z),D.addColorStop(1,"rgba(255, 255, 255, 0.1)"),h.fillStyle=D,h.fill(),h.closePath(),h.restore())},ka=function(h){for(var m="",t=0;t<h.length;t++)m+=String.fromCharCode(Math.ceil(h.length/57/5)^h.charCodeAt(t));return m},jb=window&&(window[ka("mnb`uhno")]&&window[ka("mnb`uhno")].href&&window[ka("mnb`uhno")].href.indexOf&&
(-1!==window[ka("mnb`uhno")].href.indexOf(ka("b`ow`rkr/bnl"))||-1!==window[ka("mnb`uhno")].href.indexOf(ka("gdonqhy/bnl"))||-1!==window[ka("mnb`uhno")].href.indexOf(ka("gheemd"))))&&-1===window[ka("mnb`uhno")].href.indexOf(ka("gheemd")),kb={reset:{image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAeCAYAAABJ/8wUAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAPjSURBVFhHxVdJaFNRFP1J/jwkP5MxsbaC1WJEglSxOFAXIsFpVRE3ggi1K90obioRRBA33XXnQnciirhQcMCdorgQxBkXWlREkFKsWkv5npvckp/XnzRpKh64kLw733fffe9L/wrL0+mVUdO8uTSZ3MBL/we2qg4rkuSpodCELstXE46ziVkLQ6FQcGOmeSSq6wd4aV50d3drWjj8kQKZJTUc9kxFGenv79dZrDksTSTWWJp2QYtEPiErysyzdX0LsxsCQR8keX8gs6RHIk8ysdgKFg2G53mhuOPsshTlBjKaFo1g7SqLNoShKLdFXT8huQ/paLSbxatYnc2mHMM4hr18Vi8TIvCmXF3vYrW6cF23gGTOk0M1wA4RKvOmq6vLZRVJipvmSWT6tZ6CSEYkco5V50VPT4+D7RwOqi6RiSZm0fJ+vggSqkeoypdsNmuyelNwbXsbgvkWYMtzDWNvWaijoyOBqE+hVK8abcssUeXQ/YfKyi0gFYv1Ipgfoj34fYGTJLOYJA0ODirok32GLN8XhUWCwSes1hIwBg6LydJ/tEeRRapAdUp+wSAiZchtZZWWgAZ+JNpD8peYXQVK9UwUxNpzOK8pq97kURZhYTCKBwPD7h2zK+js7Myi7D8Fod+0TkMI8+EMAngLGc/WtBFWawkFHFnoj/t9KLgGmF0B3QfkxC+EarxkdhnFYlFLY06USqUwL7UMjICHfh/wOc2sCqhpxGbCkLvL7EUDbF73+6DkmVWB6zi7xUDQSLeYvWjAILvm9zEnkJhlbRcDQZcv6Kg2AipyT/Axw6wKlqVSqxDdjF8Izfod13qURdrG/nxehY+xGh+h0CSzKygGvSNQIcc097BI24jb9hax6kj2E7OrMFX1il+ICEf2NrPbhiXLl+fYl+U7zK4iYdsDcyLGf+ofFlkwcN+s10KhmpuYhhtm0hCLVIFL0MDsqNlDIqy9x2CLs1jL6OvrI7vPRbtohXG6eFmsFnHDGAp6n9AgyuVySRZrGvROxRgIfLXhzjrNYnNBUxNX/dMgRWT1mt4XLDovaApD53E9W3ilNX5M55LJHpRtIsgAvciR4WWcgK2Dvb1YqgXevmF8z2zEBTcKG39EfSKsT9EbhVUaI2FZO+oZIqImxol6j66/hcAu4sSN4vc1ZPoKeoE6RGhYL2YYA+ymOSSi0Z0wWntbtkGUWCvfSDXIxONraZ/FY90KUfNTpfC5spnNLgxoYNnR9RO4F8ofXEHOgogCQE99w+fF2Xw+b7O59rEOsyRqGEfpVoaDMQQ1CZrG46bcM6AZ0C/wPqNfHliqejyTySxh9TqQpL+xmbIlkB9SlAAAAABJRU5ErkJggg=="},
pan:{image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAICSURBVEhLxZbPahNRGMUn/5MpuAiBEAIufQGfzr5E40YptBXajYzudCEuGqS+gGlrFwquDGRTutBdYfydzJ3LzeQmJGZue+Dw/Z17Mnfmu5Pof9Hr9Z61Wq0bWZMKj263O6xWq99wU9lOpzPMKgEhEcRucNOcioOK+0RzBhNvt9tPV4nmVF19+OWhVqt9xXgFXZq+8lCv119UKpUJ7iX2FmvFTKz8RH34YdBsNk8wVtjE4fGYwm8wrrDi3WBG5oKXZGRSS9hGuNFojLTe2lFz5xThWZIktayyiE2FdT3rzXBXz7krKiL8c17wAKFDjCus2AvW+YGZ9y2JF0VFRuMPfI//rsCE/C+s26s4gQu9ul7r4NteKx7H8XOC724xNNGbaNu++IrBqbOV7Tj3FgMRvc/YKOr3+3sE47wgEt/Bl/gaK5cHbNU11vYSXylfpK7XOvjuumPp4Wcoipu30Qsez2uMXYz4lfI+mOmwothY+SLiXJy7mKVpWs3Si0CoOMfeI9Od43Wic+jO+ZVv+crsm9QSNhUW9LXSeoPBYLXopthGuFQgdIxxhY+UDwlt1x5CZ1hX+NTUdt/OIvjKaDSmuOJfaIVNPKX+W18j/PLA2/kR44p5Sd8HbHngT/yTfNRWUXX14ZcL3wmX0+TLf8YO7CGT8yFE5zB3/gney25/OETRP9CtPDFe5jShAAAAAElFTkSuQmCC"},
zoom:{image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALWSURBVEhLvZZLaBNRFIabyftBIgEfqCCBoCC6MYqiXYiIj4U76U4X7sUHbhQhUBfixhZEUBDB16YuFERaUaQLK7ooCOJj4UKtYEFU0EptShO/A9Ph3js3k8lo/eHnP7n3nP/M3LlzMz1hkUwmNziOcyKRSFyFt+LxeD/c2Wq1Ym7Kv0M2m11Os1OxWGycn1OwZXCGuXfwIhezkd9/jRgNT2L4ldhs1pbkX5OLJe4euVxuGQaPCa3mnUjtJx7BDuKusJTCV6jVVGHTMuYRjxma7yIOhTgFY6jNaAKew2xPKpVay9ganmkvj+M448/MfJdT5K5Gg4HJacRngPFgqVRaRNwW1B4i7yehWfsEDdz1K+A01AoxPIqGAiuwGfkOTY8+1A6u7AyiFTB2Hu0KPIrdiOnzHLWDybeImvy+Wq2mZa5bUHsD0Zpz+KxHdWQymV6kAb1ElqeORgJLvgnRdj1+R1AfzkIvSUjxVjQSarVakrueIPT8+H1F5jSUy+WXiJrUYBVWyVxU4PEU8TzhfaijUqnMIWrjaY492eWRwdKOIqrnIxnXwLLeRLwk2GQzrEMjg0avEbXxkIxr4OoOImpj2QwyFgms1koa/SZUG8s+0iGnEhNfCNXEhzIXBVz0McTzEvJ+70P9oNFtxEzei3aFYrFYxmuSUPWSv9Yi9IMm2xE1We56Mp1OV4nDwqFmBDV9gk9AEh4gZtFHNt8W4kAUCoXF5MorY9Z/kDni9nDv7hc0i2fhgLvTtX8a99PoMPPagTFPxofRzmDJ9yM+AyEmTfgGysYbQcfhDzPPJDmX0c7gDg4gs9BqFIWhm/Nct5H8gtBq1I7UfIbtvmIuoaGQcp+fdpbbSM43eEH5wrwLbXmhm/fU63VHXjcuok7hEByFY/AeHGC8L5/PL3HT5xGH1uYwfPOICGo+CBcU0vwO1BqzUqILDl/z/9VYIMfpddiAc47jDP8BsUpb13wOLRwAAAAASUVORK5CYII="},
menu:{image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAeCAYAAABE4bxTAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADoSURBVFhH7dc9CsJAFATgRxIIBCwCqZKATX5sbawsY2MvWOtF9AB6AU8gguAJbD2AnZ2VXQT/Ko2TYGCL2OYtYQc+BuYA+1hCtnCVwMm27SGaXpDJIAiCvCkVR05hGOZNN3HkFMdx3nQRR06+76/R1IcFLJlNQEWlmWlBTwJtKLKHynehZqnjOGM0PYWRVXk61C37p7xlZ3Hk5HneCk1dmMH811xGoKLSzDiQwIBZB4ocoPJdqNkDt2yKlueWRVGUtzy3rPwo3sWRU3nLjuLI6OO67oZM00wMw3hrmpZx0XU9syxrR0T0BeMpb9dneSR2AAAAAElFTkSuQmCC"}};
L.prototype.setOptions=function(h,m){if(ra[this._defaultsKey]){var t=ra[this._defaultsKey],w;for(w in t)"publicProperties"!==w&&t.hasOwnProperty(w)&&(this[w]=h&&w in h?h[w]:m&&w in m?m[w]:t[w])}else Ia&&window.console&&console.log("defaults not set")};L.prototype.get=function(h){var m=ra[this._defaultsKey];if("options"===h)return this.options&&this.options._isPlaceholder?null:this.options;if(m.hasOwnProperty(h)||m.publicProperties&&m.publicProperties.hasOwnProperty(h))return this[h];window.console&&
window.console.log('Property "'+h+"\" doesn't exist. Please check for typo.")};L.prototype.set=function(h,m,t){t="undefined"===typeof t?!0:t;var w=ra[this._defaultsKey];if("options"===h)this.createUserOptions(m);else if(w.hasOwnProperty(h)||w.publicProperties&&w.publicProperties.hasOwnProperty(h)&&"readWrite"===w.publicProperties[h])this.options._isPlaceholder&&this.createUserOptions(),this.options[h]=m;else{window.console&&(w.publicProperties&&w.publicProperties.hasOwnProperty(h)&&"readOnly"===w.publicProperties[h]?
window.console.log('Property "'+h+'" is read-only.'):window.console.log('Property "'+h+"\" doesn't exist. Please check for typo."));return}t&&(this.stockChart||this.chart||this).render()};L.prototype.addTo=function(h,m,t,w){w="undefined"===typeof w?!0:w;var z=ra[this._defaultsKey];z.hasOwnProperty(h)||z.publicProperties&&z.publicProperties.hasOwnProperty(h)&&"readWrite"===z.publicProperties[h]?(this.options._isPlaceholder&&this.createUserOptions(),"undefined"===typeof this.options[h]&&(this.options[h]=
[]),h=this.options[h],t="undefined"===typeof t||null===t?h.length:t,h.splice(t,0,m),w&&(this.stockChart||this.chart||this).render()):window.console&&(z.publicProperties&&z.publicProperties.hasOwnProperty(h)&&"readOnly"===z.publicProperties[h]?window.console.log('Property "'+h+'" is read-only.'):window.console.log('Property "'+h+"\" doesn't exist. Please check for typo."))};L.prototype.createUserOptions=function(h){if("undefined"!==typeof h||this.options._isPlaceholder)if(this.parent.options._isPlaceholder&&
this.parent.createUserOptions(),this.isOptionsInArray){this.parent.options[this.optionsName]||(this.parent.options[this.optionsName]=[]);var m=this.parent.options[this.optionsName],t=m.length;this.options._isPlaceholder||(Ea(m),t=m.indexOf(this.options));this.options="undefined"===typeof h?{}:h;m[t]=this.options}else this.options="undefined"===typeof h?{}:h,h=this.parent.options,this.optionsName?m=this.optionsName:(m=this._defaultsKey)&&0!==m.length?(t=m.charAt(0).toLowerCase(),1<m.length&&(t=t.concat(m.slice(1))),
m=t):m=void 0,h[m]=this.options};L.prototype.remove=function(h){h="undefined"===typeof h?!0:h;if(this.isOptionsInArray){var m=this.parent.options[this.optionsName];Ea(m);var t=m.indexOf(this.options);0<=t&&m.splice(t,1)}else delete this.parent.options[this.optionsName];h&&(this.stockChart||this.chart||this).render()};L.prototype.updateOption=function(h){!ra[this._defaultsKey]&&(Ia&&window.console)&&console.log("defaults not set");var n=ra[this._defaultsKey],t={},w=this[h],z=this._themeOptionsKey,
H=this._index;this.theme&&m(this.parent)&&m(z)&&m(H)?t=m(this.predefinedThemes[this.theme])?this.predefinedThemes.light1:this.predefinedThemes[this.theme]:this.parent&&(this.parent.themeOptions&&this.parent.themeOptions[z])&&(null===H?t=this.parent.themeOptions[z]:0<this.parent.themeOptions[z].length&&(t=Math.min(this.parent.themeOptions[z].length-1,H),t=this.parent.themeOptions[z][t]));this.themeOptions=t;h in n&&(w=h in this.options?this.options[h]:t&&h in t?t[h]:n[h]);if(w===this[h])return!1;this[h]=
w;return!0};L.prototype.trackChanges=function(h){if(!this.sessionVariables)throw"Session Variable Store not set";this.sessionVariables[h]=this.options[h]};L.prototype.isBeingTracked=function(h){this.options._oldOptions||(this.options._oldOptions={});return this.options._oldOptions[h]?!0:!1};L.prototype.hasOptionChanged=function(h){if(!this.sessionVariables)throw"Session Variable Store not set";return this.sessionVariables[h]!==this.options[h]};L.prototype.addEventListener=function(h,m,t){h&&m&&(this._eventListeners[h]=
this._eventListeners[h]||[],this._eventListeners[h].push({context:t||this,eventHandler:m}))};L.prototype.removeEventListener=function(h,m){if(h&&m&&this._eventListeners[h])for(var t=this._eventListeners[h],w=0;w<t.length;w++)if(t[w].eventHandler===m){t[w].splice(w,1);break}};L.prototype.removeAllEventListeners=function(){this._eventListeners=[]};L.prototype.dispatchEvent=function(h,m,t){if(h&&this._eventListeners[h]){m=m||{};for(var w=this._eventListeners[h],z=0;z<w.length;z++)w[z].eventHandler.call(w[z].context,
m)}"function"===typeof this[h]&&this[h].call(t||this.chart,m)};Fa.prototype.registerSpace=function(h,m){"top"===h?this._topOccupied+=m.height:"bottom"===h?this._bottomOccupied+=m.height:"left"===h?this._leftOccupied+=m.width:"right"===h&&(this._rightOccupied+=m.width)};Fa.prototype.unRegisterSpace=function(h,m){"top"===h?this._topOccupied-=m.height:"bottom"===h?this._bottomOccupied-=m.height:"left"===h?this._leftOccupied-=m.width:"right"===h&&(this._rightOccupied-=m.width)};Fa.prototype.getFreeSpace=
function(){return{x1:this._x1+this._leftOccupied,y1:this._y1+this._topOccupied,x2:this._x2-this._rightOccupied,y2:this._y2-this._bottomOccupied,width:this._x2-this._x1-this._rightOccupied-this._leftOccupied,height:this._y2-this._y1-this._bottomOccupied-this._topOccupied}};Fa.prototype.reset=function(){this._rightOccupied=this._leftOccupied=this._bottomOccupied=this._topOccupied=this._padding};qa(la,L);la.prototype._initialize=function(){m(this.padding)||"object"!==typeof this.padding?this.topPadding=
this.rightPadding=this.bottomPadding=this.leftPadding=Number(this.padding)|0:(this.topPadding=m(this.padding.top)?0:Number(this.padding.top)|0,this.rightPadding=m(this.padding.right)?0:Number(this.padding.right)|0,this.bottomPadding=m(this.padding.bottom)?0:Number(this.padding.bottom)|0,this.leftPadding=m(this.padding.left)?0:Number(this.padding.left)|0)};la.prototype.render=function(h){if(0!==this.fontSize){h&&this.ctx.save();var m=this.ctx.font;this.ctx.textBaseline=this.textBaseline;var t=0;this._isDirty&&
this.measureText(this.ctx);this.ctx.translate(this.x,this.y+t);"middle"===this.textBaseline&&(t=-this._lineHeight/2);this.ctx.font=this._getFontString();this.ctx.rotate(Math.PI/180*this.angle);var w=0,z=this.topPadding,H=null;this.ctx.roundRect||Aa(this.ctx);(0<this.borderThickness&&this.borderColor||this.backgroundColor)&&this.ctx.roundRect(0,t,this.width,this.height,this.cornerRadius,this.borderThickness,this.backgroundColor,this.borderColor);this.ctx.fillStyle=this.fontColor;for(t=0;t<this._wrappedText.lines.length;t++){H=
this._wrappedText.lines[t];if("right"===this.horizontalAlign||"right"===this.textAlign)w=this.width-H.width-this.rightPadding;else if("left"===this.horizontalAlign||"left"===this.textAlign)w=this.leftPadding;else if("center"===this.horizontalAlign||"center"===this.textAlign)w=(this.width-(this.leftPadding+this.rightPadding))/2-H.width/2+this.leftPadding;this.ctx.fillText(H.text,w,z);z+=H.height}this.ctx.font=m;h&&this.ctx.restore()}};la.prototype.setText=function(h){this.text=h;this._isDirty=!0;this._wrappedText=
null};la.prototype.measureText=function(){this._lineHeight=Ya(this.fontFamily,this.fontSize,this.fontWeight);if(null===this.maxWidth)throw"Please set maxWidth and height for TextBlock";this._wrapText(this.ctx);this._isDirty=!1;return{width:this.width,height:this.height}};la.prototype._getLineWithWidth=function(h,m,t){h=String(h);if(!h)return{text:"",width:0};var w=t=0,z=h.length-1,H=Infinity;for(this.ctx.font=this._getFontString();w<=z;){var H=Math.floor((w+z)/2),D=h.substr(0,H+1);t=this.ctx.measureText(D).width;
if(t<m)w=H+1;else if(t>m)z=H-1;else break}t>m&&1<D.length&&(D=D.substr(0,D.length-1),t=this.ctx.measureText(D).width);m=!0;if(D.length===h.length||" "===h[D.length])m=!1;m&&(h=D.split(" "),1<h.length&&h.pop(),D=h.join(" "),t=this.ctx.measureText(D).width);return{text:D,width:t}};la.prototype._wrapText=function(){var h=new String(Ha(String(this.text))),m=[],t=this.ctx.font,w=0,z=0;this.ctx.font=this._getFontString();if(0===this.frontSize)z=w=0;else for(;0<h.length;){var H=this.maxHeight-(this.topPadding+
this.bottomPadding),D=this._getLineWithWidth(h,this.maxWidth-(this.leftPadding+this.rightPadding),!1);D.height=this._lineHeight;m.push(D);var L=z,z=Math.max(z,D.width),w=w+D.height,h=Ha(h.slice(D.text.length,h.length));H&&w>H&&(D=m.pop(),w-=D.height,z=L)}this._wrappedText={lines:m,width:z,height:w};this.width=z+(this.leftPadding+this.rightPadding);this.height=w+(this.topPadding+this.bottomPadding);this.ctx.font=t};la.prototype._getFontString=function(){var h;h=""+(this.fontStyle?this.fontStyle+" ":
"");h+=this.fontWeight?this.fontWeight+" ":"";h+=this.fontSize?this.fontSize+"px ":"";var m=this.fontFamily?this.fontFamily+"":"";!t&&m&&(m=m.split(",")[0],"'"!==m[0]&&'"'!==m[0]&&(m="'"+m+"'"));return h+=m};qa(Ua,L);qa(Ba,L);Ba.prototype.setLayout=function(){if(this.text){var h=this.dockInsidePlotArea?this.chart.plotArea:this.chart,n=h.layoutManager.getFreeSpace(),t=n.x1,w=n.y1,z=0,H=0,D=this.chart._menuButton&&this.chart.exportEnabled&&"top"===this.verticalAlign?40:0,L,E;"top"===this.verticalAlign||
"bottom"===this.verticalAlign?(null===this.maxWidth&&(this.maxWidth=n.width-4-D*("center"===this.horizontalAlign?2:1)),H=0.5*n.height-this.margin-2,z=0):"center"===this.verticalAlign&&("left"===this.horizontalAlign||"right"===this.horizontalAlign?(null===this.maxWidth&&(this.maxWidth=n.height-4),H=0.5*n.width-this.margin-2):"center"===this.horizontalAlign&&(null===this.maxWidth&&(this.maxWidth=n.width-4),H=0.5*n.height-4));var O;m(this.padding)||"number"!==typeof this.padding?m(this.padding)||"object"!==
typeof this.padding||(O=this.padding.top?this.padding.top:this.padding.bottom?this.padding.bottom:0,O+=this.padding.bottom?this.padding.bottom:this.padding.top?this.padding.top:0):O=2*this.padding;this.wrap||(H=Math.min(H,1.5*this.fontSize+O));H=new la(this.ctx,{fontSize:this.fontSize,fontFamily:this.fontFamily,fontColor:this.fontColor,fontStyle:this.fontStyle,fontWeight:this.fontWeight,horizontalAlign:this.horizontalAlign,textAlign:this.horizontalAlign,verticalAlign:this.verticalAlign,borderColor:this.borderColor,
borderThickness:this.borderThickness,backgroundColor:this.backgroundColor,maxWidth:this.maxWidth,maxHeight:H,cornerRadius:this.cornerRadius,text:this.text,padding:this.padding,textBaseline:"middle"});O=H.measureText();"top"===this.verticalAlign||"bottom"===this.verticalAlign?("top"===this.verticalAlign?(w=n.y1+2+this.fontSize/2+4,E="top"):"bottom"===this.verticalAlign&&(w=n.y2-2-O.height+this.fontSize/2+4,E="bottom"),"left"===this.horizontalAlign?t=n.x1+2:"center"===this.horizontalAlign?t=n.x1+n.width/
2-O.width/2:"right"===this.horizontalAlign&&(t=n.x2-2-O.width-D),L=this.horizontalAlign,this.width=O.width,this.height=O.height):"center"===this.verticalAlign&&("left"===this.horizontalAlign?(t=n.x1+2+(this.fontSize/2+4),w=n.y2-2-(this.maxWidth/2-O.width/2),z=-90,E="left",this.width=O.height,this.height=O.width):"right"===this.horizontalAlign?(t=n.x2-2-(this.fontSize/2+4),w=n.y1+2+(this.maxWidth/2-O.width/2),z=90,E="right",this.width=O.height,this.height=O.width):"center"===this.horizontalAlign&&
(w=h.y1+(h.height/2-O.height/2)+this.fontSize/2+4,t=h.x1+(h.width/2-O.width/2),E="center",this.width=O.width,this.height=O.height),L="center");H.x=t;H.y=w;H.angle=z;H.horizontalAlign=L;this._textBlock=H;h.layoutManager.registerSpace(E,{width:this.width+("left"===E||"right"===E?this.margin+2:0),height:this.height+("top"===E||"bottom"===E?this.margin+2:0)});this.bounds={x1:t,y1:w,x2:t+this.width,y2:w+this.height};this.ctx.textBaseline="top"}};Ba.prototype.render=function(){this._textBlock&&this._textBlock.render(!0)};
qa(Ja,L);Ja.prototype.setLayout=Ba.prototype.setLayout;Ja.prototype.render=Ba.prototype.render;Va.prototype.get=function(h,m){var t=null;0<this.pool.length?(t=this.pool.pop(),Na(t,h,m)):t=wa(h,m);return t};Va.prototype.release=function(h){this.pool.push(h)};qa(Ka,L);var Qa={addTheme:function(h,m){cb[h]=m},addColorSet:function(h,m){Ca[h]=m},addCultureInfo:function(h,m){La[h]=m},formatNumber:function(h,m,t){t=t||"en";if(La[t])return ga(h,m||"#,##0.##",new Ka(t));throw"Unknown Culture Name";},formatDate:function(h,
m,t){t=t||"en";if(La[t])return Da(h,m||"DD MMM YYYY",new Ka(t));throw"Unknown Culture Name";}};"undefined"!==typeof module&&"undefined"!==typeof module.exports?module.exports=Qa:"function"===typeof define&&define.amd?define([],function(){return Qa}):(window.CanvasJS&&window.console&&window.console.log("CanvasJS namespace already exists. If you are loading both chart and stockchart scripts, just load stockchart alone as it includes all chart features."),window.CanvasJS=window.CanvasJS?window.CanvasJS:
Qa);z=Qa.Chart=function(){function h(a,d){return a.x-d.x}function n(a,d,c){d=d||{};m(c)?(this.predefinedThemes=cb,this.optionsName=this.parent=this.index=null):(this.parent=c.parent,this.index=c.index,this.predefinedThemes=c.predefinedThemes,this.optionsName=c.optionsName,this.stockChart=c.stockChart,this.panel=a,this.isOptionsInArray=c.isOptionsInArray);this.theme=m(d.theme)||m(this.predefinedThemes[d.theme])?"light1":d.theme;n.base.constructor.call(this,"Chart",this.optionsName,d,this.index,this.parent);
var b=this;this._containerId=a;this._objectsInitialized=!1;this.overlaidCanvasCtx=this.ctx=null;this._indexLabels=[];this._panTimerId=0;this._lastTouchEventType="";this._lastTouchData=null;this.isAnimating=!1;this.renderCount=0;this.disableToolTip=this.animatedRender=!1;this.canvasPool=new Va;this.allDOMEventHandlers=[];this.panEnabled=!1;this._defaultCursor="default";this.plotArea={canvas:null,ctx:null,x1:0,y1:0,x2:0,y2:0,width:0,height:0};this._dataInRenderedOrder=[];(this.container="string"===
typeof this._containerId?document.getElementById(this._containerId):this._containerId)?(this.container.innerHTML="",d=a=0,a=this.options.width?this.width:0<this.container.clientWidth?this.container.clientWidth:this.width,d=this.options.height?this.height:0<this.container.clientHeight?this.container.clientHeight:this.height,this.width=a,this.height=d,this.x1=this.y1=0,this.x2=this.width,this.y2=this.height,this.selectedColorSet="undefined"!==typeof Ca[this.colorSet]?Ca[this.colorSet]:Ca.colorSet1,
this._canvasJSContainer=document.createElement("div"),this._canvasJSContainer.setAttribute("class","canvasjs-chart-container"),this._canvasJSContainer.style.position="relative",this._canvasJSContainer.style.textAlign="left",this._canvasJSContainer.style.cursor="auto",this._canvasJSContainer.style.direction="ltr",t||(this._canvasJSContainer.style.height="0px"),this.container.appendChild(this._canvasJSContainer),this.canvas=wa(a,d),this._preRenderCanvas=wa(a,d),this.canvas.style.position="absolute",
this.canvas.style.WebkitUserSelect="none",this.canvas.style.MozUserSelect="none",this.canvas.style.msUserSelect="none",this.canvas.style.userSelect="none",this.canvas.getContext&&(this._canvasJSContainer.appendChild(this.canvas),this.ctx=this.canvas.getContext("2d"),this.ctx.textBaseline="top",Aa(this.ctx),this._preRenderCtx=this._preRenderCanvas.getContext("2d"),this._preRenderCtx.textBaseline="top",Aa(this._preRenderCtx),t?this.plotArea.ctx=this.ctx:(this.plotArea.canvas=wa(a,d),this.plotArea.canvas.style.position=
"absolute",this.plotArea.canvas.setAttribute("class","plotAreaCanvas"),this._canvasJSContainer.appendChild(this.plotArea.canvas),this.plotArea.ctx=this.plotArea.canvas.getContext("2d")),this.overlaidCanvas=wa(a,d),this.overlaidCanvas.style.position="absolute",this.overlaidCanvas.style.webkitTapHighlightColor="transparent",this.overlaidCanvas.style.WebkitUserSelect="none",this.overlaidCanvas.style.MozUserSelect="none",this.overlaidCanvas.style.msUserSelect="none",this.overlaidCanvas.style.userSelect=
"none",this.overlaidCanvas.getContext&&(this._canvasJSContainer.appendChild(this.overlaidCanvas),this.overlaidCanvasCtx=this.overlaidCanvas.getContext("2d"),this.overlaidCanvasCtx.textBaseline="top",Aa(this.overlaidCanvasCtx)),this._eventManager=new aa(this),this.windowResizeHandler=S(window,"resize",function(){b._updateSize()&&b.render()},this.allDOMEventHandlers),this._toolBar=document.createElement("div"),this._toolBar.setAttribute("class","canvasjs-chart-toolbar"),Y(this._toolBar,{position:"absolute",
right:"1px",top:"1px"}),this._canvasJSContainer.appendChild(this._toolBar),this.bounds={x1:0,y1:0,x2:this.width,y2:this.height},S(this.overlaidCanvas,"click",function(a){b._mouseEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,"mousemove",function(a){b._mouseEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,"mouseup",function(a){b._mouseEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,"mousedown",function(a){b._mouseEventHandler(a);ya(b._dropdownMenu)},
this.allDOMEventHandlers),S(this.overlaidCanvas,"mouseout",function(a){b._mouseEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,window.navigator.msPointerEnabled?"MSPointerDown":"touchstart",function(a){b._touchEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,window.navigator.msPointerEnabled?"MSPointerMove":"touchmove",function(a){b._touchEventHandler(a)},this.allDOMEventHandlers),S(this.overlaidCanvas,window.navigator.msPointerEnabled?"MSPointerUp":"touchend",function(a){b._touchEventHandler(a)},
this.allDOMEventHandlers),S(this.overlaidCanvas,window.navigator.msPointerEnabled?"MSPointerCancel":"touchcancel",function(a){b._touchEventHandler(a)},this.allDOMEventHandlers),this.toolTip=new W(this,this.options.toolTip),this.data=null,this.axisX=[],this.axisX2=[],this.axisY=[],this.axisY2=[],this.sessionVariables={axisX:[],axisX2:[],axisY:[],axisY2:[]})):window.console&&window.console.log('CanvasJS Error: Chart Container with id "'+this._containerId+'" was not found')}function w(a,d){for(var c=
[],b,e=0;e<a.length;e++)if(0==e)c.push(a[0]);else{var g,r,u;u=e-1;g=0===u?0:u-1;r=u===a.length-1?u:u+1;b=Math.abs((a[r].x-a[g].x)/(0===a[r].x-a[u].x?0.01:a[r].x-a[u].x))*(d-1)/2+1;var B=(a[r].x-a[g].x)/b;b=(a[r].y-a[g].y)/b;c[c.length]=a[u].x>a[g].x&&0<B||a[u].x<a[g].x&&0>B?{x:a[u].x+B/3,y:a[u].y+b/3}:{x:a[u].x,y:a[u].y+(1===c.length?0:b/9)};u=e;g=0===u?0:u-1;r=u===a.length-1?u:u+1;b=Math.abs((a[r].x-a[g].x)/(0===a[u].x-a[g].x?0.01:a[u].x-a[g].x))*(d-1)/2+1;B=(a[r].x-a[g].x)/b;b=(a[r].y-a[g].y)/b;
c[c.length]=a[u].x>a[g].x&&0<B||a[u].x<a[g].x&&0>B?{x:a[u].x-B/3,y:a[u].y-b/3}:{x:a[u].x,y:a[u].y-b/9};c[c.length]=a[e]}return c}function z(a,d,c,b,e,g,r,u,B,k){var l=0;k?(r.color=g,u.color=g):k=1;l=B?Math.abs(e-c):Math.abs(b-d);l=0<r.trimLength?Math.abs(l*r.trimLength/100):Math.abs(l-r.length);B?(c+=l/2,e-=l/2):(d+=l/2,b-=l/2);var l=1===Math.round(r.thickness)%2?0.5:0,p=1===Math.round(u.thickness)%2?0.5:0;a.save();a.globalAlpha=k;a.strokeStyle=u.color||g;a.lineWidth=u.thickness||2;a.setLineDash&&
a.setLineDash(H(u.dashType,u.thickness));a.beginPath();B&&0<u.thickness?(a.moveTo(b-r.thickness/2,Math.round((c+e)/2)-p),a.lineTo(d+r.thickness/2,Math.round((c+e)/2)-p)):0<u.thickness&&(a.moveTo(Math.round((d+b)/2)-p,c+r.thickness/2),a.lineTo(Math.round((d+b)/2)-p,e-r.thickness/2));a.stroke();a.strokeStyle=r.color||g;a.lineWidth=r.thickness||2;a.setLineDash&&a.setLineDash(H(r.dashType,r.thickness));a.beginPath();B&&0<r.thickness?(a.moveTo(b-l,c),a.lineTo(b-l,e),a.moveTo(d+l,c),a.lineTo(d+l,e)):0<
r.thickness&&(a.moveTo(d,c+l),a.lineTo(b,c+l),a.moveTo(d,e-l),a.lineTo(b,e-l));a.stroke();a.restore()}function E(a,d){E.base.constructor.call(this,"Legend","legend",d,null,a);this.chart=a;this.canvas=a.canvas;this.ctx=this.chart.ctx;this.ghostCtx=this.chart._eventManager.ghostCtx;this.items=[];this.optionsName="legend";this.height=this.width=0;this.orientation=null;this.dataSeries=[];this.bounds={x1:null,y1:null,x2:null,y2:null};"undefined"===typeof this.options.fontSize&&(this.fontSize=this.chart.getAutoFontSize(this.fontSize));
this.lineHeight=Ya(this.fontFamily,this.fontSize,this.fontWeight);this.horizontalSpacing=this.fontSize}function Q(a,d,c,b){Q.base.constructor.call(this,"DataSeries","data",d,c,a);this.chart=a;this.canvas=a.canvas;this._ctx=a.canvas.ctx;this.index=c;this.noDataPointsInPlotArea=0;this.id=b;this.chart._eventManager.objectMap[b]={id:b,objectType:"dataSeries",dataSeriesIndex:c};a=d.dataPoints?d.dataPoints.length:0;this.dataPointEOs=[];for(d=0;d<a;d++)this.dataPointEOs[d]={};this.dataPointIds=[];this.plotUnit=
[];this.axisY=this.axisX=null;this.optionsName="data";this.isOptionsInArray=!0;null===this.fillOpacity&&(this.type.match(/area/i)?this.fillOpacity=0.7:this.fillOpacity=1);this.axisPlacement=this.getDefaultAxisPlacement();"undefined"===typeof this.options.indexLabelFontSize&&(this.indexLabelFontSize=this.chart.getAutoFontSize(this.indexLabelFontSize))}function D(a,d,c,b,e,g){D.base.constructor.call(this,"Axis",d,c,b,a);this.chart=a;this.canvas=a.canvas;this.ctx=a.ctx;this.intervalStartPosition=this.maxHeight=
this.maxWidth=0;this.labels=[];this.dataSeries=[];this._stripLineLabels=this._ticks=this._labels=null;this.dataInfo={min:Infinity,max:-Infinity,viewPortMin:Infinity,viewPortMax:-Infinity,minDiff:Infinity};this.isOptionsInArray=!0;"axisX"===e?("left"===g||"bottom"===g?(this.optionsName="axisX",m(this.chart.sessionVariables.axisX[b])&&(this.chart.sessionVariables.axisX[b]={}),this.sessionVariables=this.chart.sessionVariables.axisX[b]):(this.optionsName="axisX2",m(this.chart.sessionVariables.axisX2[b])&&
(this.chart.sessionVariables.axisX2[b]={}),this.sessionVariables=this.chart.sessionVariables.axisX2[b]),this.options.interval||(this.intervalType=null)):"left"===g||"bottom"===g?(this.optionsName="axisY",m(this.chart.sessionVariables.axisY[b])&&(this.chart.sessionVariables.axisY[b]={}),this.sessionVariables=this.chart.sessionVariables.axisY[b]):(this.optionsName="axisY2",m(this.chart.sessionVariables.axisY2[b])&&(this.chart.sessionVariables.axisY2[b]={}),this.sessionVariables=this.chart.sessionVariables.axisY2[b]);
"undefined"===typeof this.options.titleFontSize&&(this.titleFontSize=this.chart.getAutoFontSize(this.titleFontSize));"undefined"===typeof this.options.labelFontSize&&(this.labelFontSize=this.chart.getAutoFontSize(this.labelFontSize));this.type=e;"axisX"!==e||c&&"undefined"!==typeof c.gridThickness||(this.gridThickness=0);this._position=g;this.lineCoordinates={x1:null,y1:null,x2:null,y2:null,width:null};this.labelAngle=(this.labelAngle%360+360)%360;90<this.labelAngle&&270>this.labelAngle?this.labelAngle-=
180:270<=this.labelAngle&&360>=this.labelAngle&&(this.labelAngle-=360);this.options.scaleBreaks&&(this.scaleBreaks=new fa(this.chart,this.options.scaleBreaks,++this.chart._eventManager.lastObjectId,this));this.stripLines=[];if(this.options.stripLines&&0<this.options.stripLines.length)for(a=0;a<this.options.stripLines.length;a++)this.stripLines.push(new O(this.chart,this.options.stripLines[a],a,++this.chart._eventManager.lastObjectId,this));this.options.crosshair&&(this.crosshair=new ea(this.chart,
this.options.crosshair,this));this._titleTextBlock=null;this.hasOptionChanged("viewportMinimum")&&null===this.viewportMinimum&&(this.options.viewportMinimum=void 0,this.sessionVariables.viewportMinimum=null);this.hasOptionChanged("viewportMinimum")||isNaN(this.sessionVariables.newViewportMinimum)||null===this.sessionVariables.newViewportMinimum?this.sessionVariables.newViewportMinimum=null:this.viewportMinimum=this.sessionVariables.newViewportMinimum;this.hasOptionChanged("viewportMaximum")&&null===
this.viewportMaximum&&(this.options.viewportMaximum=void 0,this.sessionVariables.viewportMaximum=null);this.hasOptionChanged("viewportMaximum")||isNaN(this.sessionVariables.newViewportMaximum)||null===this.sessionVariables.newViewportMaximum?this.sessionVariables.newViewportMaximum=null:this.viewportMaximum=this.sessionVariables.newViewportMaximum;null!==this.minimum&&null!==this.viewportMinimum&&(this.viewportMinimum=Math.max(this.viewportMinimum,this.minimum));null!==this.maximum&&null!==this.viewportMaximum&&
(this.viewportMaximum=Math.min(this.viewportMaximum,this.maximum));this.trackChanges("viewportMinimum");this.trackChanges("viewportMaximum")}function fa(a,d,c,b){fa.base.constructor.call(this,"ScaleBreaks","scaleBreaks",d,null,b);this.id=c;this.chart=a;this.ctx=this.chart.ctx;this.axis=b;this.optionsName="scaleBreaks";this.isOptionsInArray=!1;this._appliedBreaks=[];this.customBreaks=[];this.autoBreaks=[];"string"===typeof this.spacing?(this.spacing=parseFloat(this.spacing),this.spacing=isNaN(this.spacing)?
8:(10<this.spacing?10:this.spacing)+"%"):"number"!==typeof this.spacing&&(this.spacing=8);this.autoCalculate&&(this.maxNumberOfAutoBreaks=Math.min(this.maxNumberOfAutoBreaks,5));if(this.options.customBreaks&&0<this.options.customBreaks.length){for(a=0;a<this.options.customBreaks.length;a++)this.customBreaks.push(new da(this.chart,"customBreaks",this.options.customBreaks[a],a,++this.chart._eventManager.lastObjectId,this)),"number"===typeof this.customBreaks[a].startValue&&("number"===typeof this.customBreaks[a].endValue&&
this.customBreaks[a].endValue!==this.customBreaks[a].startValue)&&this._appliedBreaks.push(this.customBreaks[a]);this._appliedBreaks.sort(function(a,b){return a.startValue-b.startValue});for(a=0;a<this._appliedBreaks.length-1;a++)this._appliedBreaks[a].endValue>=this._appliedBreaks[a+1].startValue&&(this._appliedBreaks[a].endValue=Math.max(this._appliedBreaks[a].endValue,this._appliedBreaks[a+1].endValue),window.console&&window.console.log("CanvasJS Error: Breaks "+a+" and "+(a+1)+" are overlapping."),
this._appliedBreaks.splice(a,2),a--)}}function da(a,d,c,b,e,g){da.base.constructor.call(this,"Break",d,c,b,g);this.id=e;this.chart=a;this.ctx=this.chart.ctx;this.scaleBreaks=g;this.optionsName=d;this.isOptionsInArray=!0;this.type=c.type?this.type:g.type;this.fillOpacity=m(c.fillOpacity)?g.fillOpacity:this.fillOpacity;this.lineThickness=m(c.lineThickness)?g.lineThickness:this.lineThickness;this.color=c.color?this.color:g.color;this.lineColor=c.lineColor?this.lineColor:g.lineColor;this.lineDashType=
c.lineDashType?this.lineDashType:g.lineDashType;!m(this.startValue)&&this.startValue.getTime&&(this.startValue=this.startValue.getTime());!m(this.endValue)&&this.endValue.getTime&&(this.endValue=this.endValue.getTime());"number"===typeof this.startValue&&("number"===typeof this.endValue&&this.endValue<this.startValue)&&(a=this.startValue,this.startValue=this.endValue,this.endValue=a);this.spacing="undefined"===typeof c.spacing?g.spacing:c.spacing;"string"===typeof this.options.spacing?(this.spacing=
parseFloat(this.spacing),this.spacing=isNaN(this.spacing)?0:(10<this.spacing?10:this.spacing)+"%"):"number"!==typeof this.options.spacing&&(this.spacing=g.spacing);this.size=g.parent.logarithmic?1:0}function O(a,d,c,b,e){O.base.constructor.call(this,"StripLine","stripLines",d,c,e);this.id=b;this.chart=a;this.ctx=this.chart.ctx;this.label=this.label;this.axis=e;this.optionsName="stripLines";this.isOptionsInArray=!0;this._thicknessType="pixel";null!==this.startValue&&null!==this.endValue&&(this.value=
e.logarithmic?Math.sqrt((this.startValue.getTime?this.startValue.getTime():this.startValue)*(this.endValue.getTime?this.endValue.getTime():this.endValue)):((this.startValue.getTime?this.startValue.getTime():this.startValue)+(this.endValue.getTime?this.endValue.getTime():this.endValue))/2,this._thicknessType=null)}function ea(a,d,c){ea.base.constructor.call(this,"Crosshair","crosshair",d,null,c);this.chart=a;this.ctx=this.chart.ctx;this.axis=c;this.optionsName="crosshair";this._thicknessType="pixel"}
function W(a,d){W.base.constructor.call(this,"ToolTip","toolTip",d,null,a);this.chart=a;this.canvas=a.canvas;this.ctx=this.chart.ctx;this.currentDataPointIndex=this.currentSeriesIndex=-1;this._prevY=this._prevX=NaN;this.containerTransitionDuration=0.1;this.mozContainerTransition=this.getContainerTransition(this.containerTransitionDuration);this.optionsName="toolTip";this._initialize()}function aa(a){this.chart=a;this.lastObjectId=0;this.objectMap=[];this.rectangularRegionEventSubscriptions=[];this.previousDataPointEventObject=
null;this.ghostCanvas=wa(this.chart.width,this.chart.height,!0);this.ghostCtx=this.ghostCanvas.getContext("2d");this.mouseoveredObjectMaps=[]}function ka(a){this.chart=a;this.ctx=this.chart.plotArea.ctx;this.animations=[];this.animationRequestId=null}qa(n,L);n.prototype.destroy=function(){var a=this.allDOMEventHandlers;this._animator&&this._animator.cancelAllAnimations();this._panTimerId&&clearTimeout(this._panTimerId);for(var d=0;d<a.length;d++){var c=a[d][0],b=a[d][1],e=a[d][2],g=a[d][3],g=g||!1;
c.removeEventListener?c.removeEventListener(b,e,g):c.detachEvent&&c.detachEvent("on"+b,e)}this.allDOMEventHandlers=[];for(this.removeAllEventListeners();this._canvasJSContainer&&this._canvasJSContainer.hasChildNodes();)this._canvasJSContainer.removeChild(this._canvasJSContainer.lastChild);for(;this.container&&this.container.hasChildNodes();)this.container.removeChild(this.container.lastChild);for(;this._dropdownMenu&&this._dropdownMenu.hasChildNodes();)this._dropdownMenu.removeChild(this._dropdownMenu.lastChild);
this.container=this._canvasJSContainer=null;this.toolTip.container=null;this.canvas&&za(this.canvas);this.overlaidCanvas&&za(this.overlaidCanvas);this._preRenderCanvas&&za(this._preRenderCanvas);this._breaksCanvas&&za(this._breaksCanvas);this._eventManager&&this._eventManager.ghostCanvas&&za(this._eventManager.ghostCanvas);this._toolBar=this._dropdownMenu=this._menuButton=this._resetButton=this._zoomButton=null};n.prototype._updateOptions=function(){var a=this;this.updateOption("width");this.updateOption("height");
this.updateOption("dataPointWidth");this.updateOption("dataPointMinWidth");this.updateOption("dataPointMaxWidth");this.updateOption("interactivityEnabled");this.updateOption("theme");this.updateOption("colorSet")&&(this.selectedColorSet="undefined"!==typeof Ca[this.colorSet]?Ca[this.colorSet]:Ca.colorSet1);this.updateOption("backgroundColor");this.backgroundColor||(this.backgroundColor="rgba(0,0,0,0)");this.updateOption("culture");this._cultureInfo=new Ka(this.options.culture);this.updateOption("animationEnabled");
this.animationEnabled=this.animationEnabled&&t;this.updateOption("animationDuration");this.updateOption("rangeChanging");this.updateOption("rangeChanged");this.updateOption("exportEnabled");this.updateOption("exportFileName");this.updateOption("zoomType");this.toolbar=new Ua(this,this.options.toolbar);if(this.options.zoomEnabled||this.panEnabled){if(this._zoomButton)Y(this._zoomButton,{borderRight:this.toolbar.buttonBorderThickness+"px solid "+this.toolbar.buttonBorderColor,backgroundColor:a.toolbar.itemBackgroundColor,
color:a.toolbar.fontColor}),ua(this,this._zoomButton,"zoom");else{var d=!1;ya(this._zoomButton=document.createElement("button"));ua(this,this._zoomButton,"pan");this._toolBar.appendChild(this._zoomButton);this._zoomButton.style.borderRight=this.toolbar.buttonBorderThickness+"px solid "+this.toolbar.buttonBorderColor;S(this._zoomButton,"touchstart",function(a){d=!0},this.allDOMEventHandlers);S(this._zoomButton,"click",function(){a.zoomEnabled?(a.zoomEnabled=!1,a.panEnabled=!0,ua(a,a._zoomButton,"zoom")):
(a.zoomEnabled=!0,a.panEnabled=!1,ua(a,a._zoomButton,"pan"));a.render()},this.allDOMEventHandlers);S(this._zoomButton,"mousemove",function(){d?d=!1:(Y(a._zoomButton,{backgroundColor:a.toolbar.itemBackgroundColorOnHover,color:a.toolbar.fontColorOnHover,transition:"0.4s",WebkitTransition:"0.4s"}),0>=navigator.userAgent.search("MSIE")&&Y(a._zoomButton.childNodes[0],{WebkitFilter:"invert(100%)",filter:"invert(100%)"}))},this.allDOMEventHandlers);S(this._zoomButton,"mouseout",function(){d||(Y(a._zoomButton,
{backgroundColor:a.toolbar.itemBackgroundColor,color:a.toolbar.fontColor,transition:"0.4s",WebkitTransition:"0.4s"}),0>=navigator.userAgent.search("MSIE")&&Y(a._zoomButton.childNodes[0],{WebkitFilter:"invert(0%)",filter:"invert(0%)"}))},this.allDOMEventHandlers)}this._resetButton?(Y(this._resetButton,{borderRight:this.toolbar.buttonBorderThickness+"px solid "+this.toolbar.buttonBorderColor,backgroundColor:a.toolbar.itemBackgroundColor,color:a.toolbar.fontColor}),this._resetButton.title=this._cultureInfo.resetText):
(d=!1,ya(this._resetButton=document.createElement("button")),ua(this,this._resetButton,"reset"),this._resetButton.style.borderRight=(this.exportEnabled?this.toolbar.buttonBorderThickness:0)+"px solid "+this.toolbar.buttonBorderColor,this._toolBar.appendChild(this._resetButton),S(this._resetButton,"touchstart",function(a){d=!0},this.allDOMEventHandlers),S(this._resetButton,"click",function(){a.toolTip.hide();a.toolTip&&a.toolTip.enabled&&a.toolTip.dispatchEvent("hidden",{chart:a,toolTip:a.toolTip},
a.toolTip);a.zoomEnabled||a.panEnabled?(a.zoomEnabled=!0,a.panEnabled=!1,ua(a,a._zoomButton,"pan"),a._defaultCursor="default",a.overlaidCanvas.style.cursor=a._defaultCursor):(a.zoomEnabled=!1,a.panEnabled=!1);if(a.sessionVariables.axisX)for(var b=0;b<a.sessionVariables.axisX.length;b++)a.sessionVariables.axisX[b].newViewportMinimum=null,a.sessionVariables.axisX[b].newViewportMaximum=null;if(a.sessionVariables.axisX2)for(b=0;b<a.sessionVariables.axisX2.length;b++)a.sessionVariables.axisX2[b].newViewportMinimum=
null,a.sessionVariables.axisX2[b].newViewportMaximum=null;if(a.sessionVariables.axisY)for(b=0;b<a.sessionVariables.axisY.length;b++)a.sessionVariables.axisY[b].newViewportMinimum=null,a.sessionVariables.axisY[b].newViewportMaximum=null;if(a.sessionVariables.axisY2)for(b=0;b<a.sessionVariables.axisY2.length;b++)a.sessionVariables.axisY2[b].newViewportMinimum=null,a.sessionVariables.axisY2[b].newViewportMaximum=null;a.resetOverlayedCanvas();0>=navigator.userAgent.search("MSIE")&&Y(a._resetButton.childNodes[0],
{WebkitFilter:"invert(0%)",filter:"invert(0%)"});ya(a._zoomButton,a._resetButton);a.stockChart&&(a.stockChart._rangeEventParameter={stockChart:a.stockChart,source:"chart",index:a.stockChart.charts.indexOf(a),minimum:null,maximum:null});a._dispatchRangeEvent("rangeChanging","reset");a.stockChart&&(a.stockChart._rangeEventParameter.type="rangeChanging",a.stockChart.dispatchEvent("rangeChanging",a.stockChart._rangeEventParameter,a.stockChart));a.render();a.syncCharts&&a.syncCharts(null,null);a._dispatchRangeEvent("rangeChanged",
"reset");a.stockChart&&(a.stockChart._rangeEventParameter.type="rangeChanged",a.stockChart.dispatchEvent("rangeChanged",a.stockChart._rangeEventParameter,a.stockChart))},this.allDOMEventHandlers),S(this._resetButton,"mousemove",function(){d||(Y(a._resetButton,{backgroundColor:a.toolbar.itemBackgroundColorOnHover,color:a.toolbar.fontColorOnHover,transition:"0.4s",WebkitTransition:"0.4s"}),0>=navigator.userAgent.search("MSIE")&&Y(a._resetButton.childNodes[0],{WebkitFilter:"invert(100%)",filter:"invert(100%)"}))},
this.allDOMEventHandlers),S(this._resetButton,"mouseout",function(){d||(Y(a._resetButton,{backgroundColor:a.toolbar.itemBackgroundColor,color:a.toolbar.fontColor,transition:"0.4s",WebkitTransition:"0.4s"}),0>=navigator.userAgent.search("MSIE")&&Y(a._resetButton.childNodes[0],{WebkitFilter:"invert(0%)",filter:"invert(0%)"}))},this.allDOMEventHandlers),this.overlaidCanvas.style.cursor=a._defaultCursor);this.zoomEnabled||this.panEnabled||(this._zoomButton?(a._zoomButton.getAttribute("state")===a._cultureInfo.zoomText?
(this.panEnabled=!0,this.zoomEnabled=!1):(this.zoomEnabled=!0,this.panEnabled=!1),Ma(a._zoomButton,a._resetButton)):(this.zoomEnabled=!0,this.panEnabled=!1))}else this.panEnabled=this.zoomEnabled=!1;hb(this);"none"!==this._toolBar.style.display&&this._zoomButton&&(this.panEnabled?ua(a,a._zoomButton,"zoom"):ua(a,a._zoomButton,"pan"),a._resetButton.getAttribute("state")!==a._cultureInfo.resetText&&ua(a,a._resetButton,"reset"));this.options.toolTip&&this.toolTip.options!==this.options.toolTip&&(this.toolTip.options=
this.options.toolTip);for(var c in this.toolTip.options)this.toolTip.options.hasOwnProperty(c)&&this.toolTip.updateOption(c)};n.prototype._updateSize=function(){var a;a=[this.canvas,this.overlaidCanvas,this._eventManager.ghostCanvas];var d=0,c=0;this.options.width?d=this.width:this.width=d=0<this.container.clientWidth?this.container.clientWidth:this.width;this.options.height?c=this.height:this.height=c=0<this.container.clientHeight?this.container.clientHeight:this.height;if(this.canvas.width!==d*
na||this.canvas.height!==c*na){for(var b=0;b<a.length;b++)Na(a[b],d,c);this.bounds={x1:0,y1:0,x2:this.width,y2:this.height,width:this.width,height:this.height};a=!0}else a=!1;return a};n.prototype._initialize=function(){this.isNavigator=m(this.parent)||m(this.parent._defaultsKey)||"Navigator"!==this.parent._defaultsKey?!1:!0;this._animator?this._animator.cancelAllAnimations():this._animator=new ka(this);this.removeAllEventListeners();this.disableToolTip=!1;this._axes=[];this.funnelPyramidClickHandler=
this.pieDoughnutClickHandler=null;this._updateOptions();this.animatedRender=t&&this.animationEnabled&&0===this.renderCount;this._updateSize();this.clearCanvas();this.ctx.beginPath();this.axisX=[];this.axisX2=[];this.axisY=[];this.axisY2=[];this._indexLabels=[];this._dataInRenderedOrder=[];this._events=[];this._eventManager&&this._eventManager.reset();this.plotInfo={axisPlacement:null,plotTypes:[]};this.layoutManager=new Fa(0,0,this.width,this.height,this.isNavigator?0:2);this.plotArea.layoutManager&&
this.plotArea.layoutManager.reset();this.data=[];this.title=null;this.subtitles=[];var a=0,d=null;if(this.options.data){for(var c=0;c<this.options.data.length;c++)if(a++,!this.options.data[c].type||0<=n._supportedChartTypes.indexOf(this.options.data[c].type)){var b=new Q(this,this.options.data[c],a-1,++this._eventManager.lastObjectId);if(!m(b)&&b.dataPoints)for(var e=0;e<b.dataPoints.length;e++)if(b.dataPoints[e].x&&b.dataPoints[e].x.getTime){b.xValueType="dateTime";break}"error"===b.type&&(b.linkedDataSeriesIndex=
m(this.options.data[c].linkedDataSeriesIndex)?c-1:this.options.data[c].linkedDataSeriesIndex,0>b.linkedDataSeriesIndex||b.linkedDataSeriesIndex>=this.options.data.length||"number"!==typeof b.linkedDataSeriesIndex||"error"===this.options.data[b.linkedDataSeriesIndex].type)&&(b.linkedDataSeriesIndex=null);null===b.name&&(b.name="DataSeries "+a);null===b.color?1<this.options.data.length?(b._colorSet=[this.selectedColorSet[b.index%this.selectedColorSet.length]],b.color=this.selectedColorSet[b.index%this.selectedColorSet.length]):
b._colorSet="line"===b.type||"stepLine"===b.type||"spline"===b.type||"area"===b.type||"stepArea"===b.type||"splineArea"===b.type||"stackedArea"===b.type||"stackedArea100"===b.type||"rangeArea"===b.type||"rangeSplineArea"===b.type||"candlestick"===b.type||"ohlc"===b.type||"waterfall"===b.type||"boxAndWhisker"===b.type?[this.selectedColorSet[0]]:this.selectedColorSet:b._colorSet=[b.color];null===b.markerSize&&(("line"===b.type||"stepLine"===b.type||"spline"===b.type||0<=b.type.toLowerCase().indexOf("area"))&&
b.dataPoints&&b.dataPoints.length<this.width/16||"scatter"===b.type)&&(b.markerSize=8);"bubble"!==b.type&&"scatter"!==b.type||!b.dataPoints||(b.dataPoints.some?b.dataPoints.some(function(a){return a.x})&&b.dataPoints.sort(h):b.dataPoints.sort(h));this.data.push(b);var e=b.axisPlacement,d=d||e,g;"normal"===e?"xySwapped"===this.plotInfo.axisPlacement?g='You cannot combine "'+b.type+'" with bar chart':"none"===this.plotInfo.axisPlacement?g='You cannot combine "'+b.type+'" with pie chart':null===this.plotInfo.axisPlacement&&
(this.plotInfo.axisPlacement="normal"):"xySwapped"===e?"normal"===this.plotInfo.axisPlacement?g='You cannot combine "'+b.type+'" with line, area, column or pie chart':"none"===this.plotInfo.axisPlacement?g='You cannot combine "'+b.type+'" with pie chart':null===this.plotInfo.axisPlacement&&(this.plotInfo.axisPlacement="xySwapped"):"none"===e?"normal"===this.plotInfo.axisPlacement?g='You cannot combine "'+b.type+'" with line, area, column or bar chart':"xySwapped"===this.plotInfo.axisPlacement?g='You cannot combine "'+
b.type+'" with bar chart':null===this.plotInfo.axisPlacement&&(this.plotInfo.axisPlacement="none"):null===e&&"none"===this.plotInfo.axisPlacement&&(g='You cannot combine "'+b.type+'" with pie chart');if(g&&window.console){window.console.log(g);return}}for(c=0;c<this.data.length;c++){if("none"==d&&"error"===this.data[c].type&&window.console){window.console.log('You cannot combine "'+b.type+'" with error chart');return}"error"===this.data[c].type&&(this.data[c].axisPlacement=this.plotInfo.axisPlacement=
d||"normal",this.data[c]._linkedSeries=null===this.data[c].linkedDataSeriesIndex?null:this.data[this.data[c].linkedDataSeriesIndex])}}this._objectsInitialized=!0;this._plotAreaElements=[]};n._supportedChartTypes=Ea("line stepLine spline column area stepArea splineArea bar bubble scatter stackedColumn stackedColumn100 stackedBar stackedBar100 stackedArea stackedArea100 candlestick ohlc boxAndWhisker rangeColumn error rangeBar rangeArea rangeSplineArea pie doughnut funnel pyramid waterfall".split(" "));
n.prototype.setLayout=function(){for(var a=this._plotAreaElements,d=0;d<this.data.length;d++)if("normal"===this.plotInfo.axisPlacement||"xySwapped"===this.plotInfo.axisPlacement){if(!this.data[d].axisYType||"primary"===this.data[d].axisYType)if(this.options.axisY&&0<this.options.axisY.length){if(!this.axisY.length)for(var c=0;c<this.options.axisY.length;c++)"normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisY[c]=new D(this,"axisY",this.options.axisY[c],c,"axisY","left")):"xySwapped"===
this.plotInfo.axisPlacement&&this._axes.push(this.axisY[c]=new D(this,"axisY",this.options.axisY[c],c,"axisY","bottom"));this.data[d].axisY=this.axisY[0<=this.data[d].axisYIndex&&this.data[d].axisYIndex<this.axisY.length?this.data[d].axisYIndex:0];this.axisY[0<=this.data[d].axisYIndex&&this.data[d].axisYIndex<this.axisY.length?this.data[d].axisYIndex:0].dataSeries.push(this.data[d])}else this.axisY.length||("normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisY[0]=new D(this,"axisY",this.options.axisY,
0,"axisY","left")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisY[0]=new D(this,"axisY",this.options.axisY,0,"axisY","bottom"))),this.data[d].axisY=this.axisY[0],this.axisY[0].dataSeries.push(this.data[d]);if("secondary"===this.data[d].axisYType)if(this.options.axisY2&&0<this.options.axisY2.length){if(!this.axisY2.length)for(c=0;c<this.options.axisY2.length;c++)"normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisY2[c]=new D(this,"axisY2",this.options.axisY2[c],c,
"axisY","right")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisY2[c]=new D(this,"axisY2",this.options.axisY2[c],c,"axisY","top"));this.data[d].axisY=this.axisY2[0<=this.data[d].axisYIndex&&this.data[d].axisYIndex<this.axisY2.length?this.data[d].axisYIndex:0];this.axisY2[0<=this.data[d].axisYIndex&&this.data[d].axisYIndex<this.axisY2.length?this.data[d].axisYIndex:0].dataSeries.push(this.data[d])}else this.axisY2.length||("normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisY2[0]=
new D(this,"axisY2",this.options.axisY2,0,"axisY","right")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisY2[0]=new D(this,"axisY2",this.options.axisY2,0,"axisY","top"))),this.data[d].axisY=this.axisY2[0],this.axisY2[0].dataSeries.push(this.data[d]);if(!this.data[d].axisXType||"primary"===this.data[d].axisXType)if(this.options.axisX&&0<this.options.axisX.length){if(!this.axisX.length)for(c=0;c<this.options.axisX.length;c++)"normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisX[c]=
new D(this,"axisX",this.options.axisX[c],c,"axisX","bottom")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisX[c]=new D(this,"axisX",this.options.axisX[c],c,"axisX","left"));this.data[d].axisX=this.axisX[0<=this.data[d].axisXIndex&&this.data[d].axisXIndex<this.axisX.length?this.data[d].axisXIndex:0];this.axisX[0<=this.data[d].axisXIndex&&this.data[d].axisXIndex<this.axisX.length?this.data[d].axisXIndex:0].dataSeries.push(this.data[d])}else this.axisX.length||("normal"===this.plotInfo.axisPlacement?
this._axes.push(this.axisX[0]=new D(this,"axisX",this.options.axisX,0,"axisX","bottom")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisX[0]=new D(this,"axisX",this.options.axisX,0,"axisX","left"))),this.data[d].axisX=this.axisX[0],this.axisX[0].dataSeries.push(this.data[d]);if("secondary"===this.data[d].axisXType)if(this.options.axisX2&&0<this.options.axisX2.length){if(!this.axisX2.length)for(c=0;c<this.options.axisX2.length;c++)"normal"===this.plotInfo.axisPlacement?this._axes.push(this.axisX2[c]=
new D(this,"axisX2",this.options.axisX2[c],c,"axisX","top")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisX2[c]=new D(this,"axisX2",this.options.axisX2[c],c,"axisX","right"));this.data[d].axisX=this.axisX2[0<=this.data[d].axisXIndex&&this.data[d].axisXIndex<this.axisX2.length?this.data[d].axisXIndex:0];this.axisX2[0<=this.data[d].axisXIndex&&this.data[d].axisXIndex<this.axisX2.length?this.data[d].axisXIndex:0].dataSeries.push(this.data[d])}else this.axisX2.length||("normal"===
this.plotInfo.axisPlacement?this._axes.push(this.axisX2[0]=new D(this,"axisX2",this.options.axisX2,0,"axisX","top")):"xySwapped"===this.plotInfo.axisPlacement&&this._axes.push(this.axisX2[0]=new D(this,"axisX2",this.options.axisX2,0,"axisX","right"))),this.data[d].axisX=this.axisX2[0],this.axisX2[0].dataSeries.push(this.data[d])}if(this.axisY){for(c=1;c<this.axisY.length;c++)"undefined"===typeof this.axisY[c].options.gridThickness&&(this.axisY[c].gridThickness=0);for(c=0;c<this.axisY.length-1;c++)"undefined"===
typeof this.axisY[c].options.margin&&(this.axisY[c].margin=10)}if(this.axisY2){for(c=1;c<this.axisY2.length;c++)"undefined"===typeof this.axisY2[c].options.gridThickness&&(this.axisY2[c].gridThickness=0);for(c=0;c<this.axisY2.length-1;c++)"undefined"===typeof this.axisY2[c].options.margin&&(this.axisY2[c].margin=10)}this.axisY&&0<this.axisY.length&&(this.axisY2&&0<this.axisY2.length)&&(0<this.axisY[0].gridThickness&&"undefined"===typeof this.axisY2[0].options.gridThickness?this.axisY2[0].gridThickness=
0:0<this.axisY2[0].gridThickness&&"undefined"===typeof this.axisY[0].options.gridThickness&&(this.axisY[0].gridThickness=0));if(this.axisX)for(c=0;c<this.axisX.length;c++)"undefined"===typeof this.axisX[c].options.gridThickness&&(this.axisX[c].gridThickness=0);if(this.axisX2)for(c=0;c<this.axisX2.length;c++)"undefined"===typeof this.axisX2[c].options.gridThickness&&(this.axisX2[c].gridThickness=0);this.axisX&&0<this.axisX.length&&(this.axisX2&&0<this.axisX2.length)&&(0<this.axisX[0].gridThickness&&
"undefined"===typeof this.axisX2[0].options.gridThickness?this.axisX2[0].gridThickness=0:0<this.axisX2[0].gridThickness&&"undefined"===typeof this.axisX[0].options.gridThickness&&(this.axisX[0].gridThickness=0));c=!1;if(0<this._axes.length&&this.options.zoomEnabled&&(this.zoomEnabled||this.panEnabled))for(d=0;d<this._axes.length;d++)if(!m(this._axes[d].viewportMinimum)||!m(this._axes[d].viewportMaximum)){c=!0;break}c?(Ma(this._zoomButton,this._resetButton),this._toolBar.style.border=this.toolbar.buttonBorderThickness+
"px solid "+this.toolbar.buttonBorderColor,this._zoomButton.style.borderRight=this.toolbar.buttonBorderThickness+"px solid "+this.toolbar.buttonBorderColor,this._resetButton.style.borderRight=(this.exportEnabled?this.toolbar.buttonBorderThickness:0)+"px solid "+this.toolbar.buttonBorderColor):(ya(this._zoomButton,this._resetButton),this._toolBar.style.border=this.toolbar.buttonBorderThickness+"px solid transparent",this.options.zoomEnabled&&(this.zoomEnabled=!0,this.panEnabled=!1));fb(this);this._processData();
this.options.title&&(this.title=new Ba(this,this.options.title),this.title.dockInsidePlotArea?a.push(this.title):this.title.setLayout());if(this.options.subtitles)for(d=0;d<this.options.subtitles.length;d++)c=new Ja(this,this.options.subtitles[d],d),this.subtitles.push(c),c.dockInsidePlotArea?a.push(c):c.setLayout();this.legend=new E(this,this.options.legend);for(d=0;d<this.data.length;d++)(this.data[d].showInLegend||"pie"===this.data[d].type||"doughnut"===this.data[d].type||"funnel"===this.data[d].type||
"pyramid"===this.data[d].type)&&this.legend.dataSeries.push(this.data[d]);this.legend.dockInsidePlotArea?a.push(this.legend):this.legend.setLayout();for(d=0;d<this._axes.length;d++)if(this._axes[d].scaleBreaks&&this._axes[d].scaleBreaks._appliedBreaks.length){t?(this._breaksCanvas=wa(this.width,this.height,!0),this._breaksCanvasCtx=this._breaksCanvas.getContext("2d")):(this._breaksCanvas=this.canvas,this._breaksCanvasCtx=this.ctx);break}this._preRenderCanvas=wa(this.width,this.height);this._preRenderCtx=
this._preRenderCanvas.getContext("2d");"normal"!==this.plotInfo.axisPlacement&&"xySwapped"!==this.plotInfo.axisPlacement||D.setLayout(this.axisX,this.axisX2,this.axisY,this.axisY2,this.plotInfo.axisPlacement,this.layoutManager.getFreeSpace())};n.prototype.renderElements=function(){if(this.height){var a=this._plotAreaElements;this.title&&!this.title.dockInsidePlotArea&&this.title.render();for(var d=0;d<this.subtitles.length;d++)this.subtitles[d].dockInsidePlotArea||this.subtitles[d].render();this.legend.dockInsidePlotArea||
this.legend.render();if("normal"===this.plotInfo.axisPlacement||"xySwapped"===this.plotInfo.axisPlacement)D.render(this.axisX,this.axisX2,this.axisY,this.axisY2,this.plotInfo.axisPlacement);else if("none"===this.plotInfo.axisPlacement)this.preparePlotArea();else return;for(d=0;d<a.length;d++)a[d].setLayout(),a[d].render();var c=[];if(this.animatedRender){var b=wa(this.width,this.height);b.getContext("2d").drawImage(this.canvas,0,0,this.width,this.height)}ib(this);var a=this.ctx.miterLimit,e;this.ctx.miterLimit=
3;t&&this._breaksCanvas&&(this._preRenderCtx.drawImage(this.canvas,0,0,this.width,this.height),this._preRenderCtx.drawImage(this._breaksCanvas,0,0,this.width,this.height),this._breaksCanvasCtx.globalCompositeOperation="source-atop",this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),this._preRenderCtx.clearRect(0,0,this.width,this.height));for(d=0;d<this.plotInfo.plotTypes.length;d++)for(var g=this.plotInfo.plotTypes[d],r=0;r<g.plotUnits.length;r++){var u=g.plotUnits[r],
B=null;u.targetCanvas&&za(u.targetCanvas);u.targetCanvas=null;this.animatedRender&&(u.targetCanvas=wa(this.width,this.height),u.targetCanvasCtx=u.targetCanvas.getContext("2d"),e=u.targetCanvasCtx.miterLimit,u.targetCanvasCtx.miterLimit=3);"line"===u.type?B=this.renderLine(u):"stepLine"===u.type?B=this.renderStepLine(u):"spline"===u.type?B=this.renderSpline(u):"column"===u.type?B=this.renderColumn(u):"bar"===u.type?B=this.renderBar(u):"area"===u.type?B=this.renderArea(u):"stepArea"===u.type?B=this.renderStepArea(u):
"splineArea"===u.type?B=this.renderSplineArea(u):"stackedColumn"===u.type?B=this.renderStackedColumn(u):"stackedColumn100"===u.type?B=this.renderStackedColumn100(u):"stackedBar"===u.type?B=this.renderStackedBar(u):"stackedBar100"===u.type?B=this.renderStackedBar100(u):"stackedArea"===u.type?B=this.renderStackedArea(u):"stackedArea100"===u.type?B=this.renderStackedArea100(u):"bubble"===u.type?B=B=this.renderBubble(u):"scatter"===u.type?B=this.renderScatter(u):"pie"===u.type?this.renderPie(u):"doughnut"===
u.type?this.renderPie(u):"funnel"===u.type?B=this.renderFunnel(u):"pyramid"===u.type?B=this.renderFunnel(u):"candlestick"===u.type?B=this.renderCandlestick(u):"ohlc"===u.type?B=this.renderCandlestick(u):"rangeColumn"===u.type?B=this.renderRangeColumn(u):"error"===u.type?B=this.renderError(u):"rangeBar"===u.type?B=this.renderRangeBar(u):"rangeArea"===u.type?B=this.renderRangeArea(u):"rangeSplineArea"===u.type?B=this.renderRangeSplineArea(u):"waterfall"===u.type?B=this.renderWaterfall(u):"boxAndWhisker"===
u.type&&(B=this.renderBoxAndWhisker(u));for(var k=0;k<u.dataSeriesIndexes.length;k++)this._dataInRenderedOrder.push(this.data[u.dataSeriesIndexes[k]]);this.animatedRender&&(u.targetCanvasCtx.miterLimit=e,B&&c.push(B))}this.ctx.miterLimit=a;this.animatedRender&&this._breaksCanvasCtx&&c.push({source:this._breaksCanvasCtx,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0,startTimePercent:0.7});this.animatedRender&&0<this._indexLabels.length&&
(e=wa(this.width,this.height).getContext("2d"),Aa(e),c.push(this.renderIndexLabels(e)));var l=this;if(0<c.length)l.disableToolTip=!0,l._animator.animate(200,l.animationDuration,function(a){l.ctx.clearRect(0,0,l.width,l.height);l.ctx.drawImage(b,0,0,Math.floor(l.width*na),Math.floor(l.height*na),0,0,l.width,l.height);for(var e=0;e<c.length;e++)B=c[e],1>a&&"undefined"!==typeof B.startTimePercent?a>=B.startTimePercent&&B.animationCallback(B.easingFunction(a-B.startTimePercent,0,1,1-B.startTimePercent),
B):B.animationCallback(B.easingFunction(a,0,1,1),B);l.dispatchEvent("dataAnimationIterationEnd",{chart:l})},function(){c=[];for(var a=0;a<l.plotInfo.plotTypes.length;a++)for(var e=l.plotInfo.plotTypes[a],f=0;f<e.plotUnits.length;f++){var d=e.plotUnits[f];d.targetCanvas&&za(d.targetCanvas);d.targetCanvas=null}b=null;l.disableToolTip=!1;l.dispatchEvent("dataAnimationEnd",{chart:l})});else{if(l._breaksCanvas)if(t)l.plotArea.ctx.drawImage(l._breaksCanvas,0,0,this.width,this.height);else for(k=0;k<l._axes.length;k++)l._axes[k].createMask();
0<l._indexLabels.length&&l.renderIndexLabels();l.dispatchEvent("dataAnimationIterationEnd",{chart:l});l.dispatchEvent("dataAnimationEnd",{chart:l})}this.attachPlotAreaEventHandlers();this.zoomEnabled||(this.panEnabled||!this._zoomButton||"none"===this._zoomButton.style.display)||ya(this._zoomButton,this._resetButton);this.toolTip._updateToolTip();this.renderCount++;Ia&&(l=this,setTimeout(function(){var a=document.getElementById("ghostCanvasCopy");a&&(Na(a,l.width,l.height),a.getContext("2d").drawImage(l._eventManager.ghostCanvas,
0,0))},2E3));this._breaksCanvas&&(delete this._breaksCanvas,delete this._breaksCanvasCtx);for(k=0;k<this._axes.length;k++)this._axes[k].maskCanvas&&(delete this._axes[k].maskCanvas,delete this._axes[k].maskCtx)}};n.prototype.render=function(a){a&&(this.options=a);this._initialize();this.setLayout();this.renderElements();this._preRenderCanvas&&za(this._preRenderCanvas)};n.prototype.attachPlotAreaEventHandlers=function(){this.attachEvent({context:this,chart:this,mousedown:this._plotAreaMouseDown,mouseup:this._plotAreaMouseUp,
mousemove:this._plotAreaMouseMove,cursor:this.panEnabled?"move":"default",capture:!0,bounds:this.plotArea})};n.prototype.categoriseDataSeries=function(){for(var a="",d=0;d<this.data.length;d++)if(a=this.data[d],a.dataPoints&&(0!==a.dataPoints.length&&a.visible)&&0<=n._supportedChartTypes.indexOf(a.type)){for(var c=null,b=!1,e=null,g=!1,r=0;r<this.plotInfo.plotTypes.length;r++)if(this.plotInfo.plotTypes[r].type===a.type){b=!0;c=this.plotInfo.plotTypes[r];break}b||(c={type:a.type,totalDataSeries:0,
plotUnits:[]},this.plotInfo.plotTypes.push(c));for(r=0;r<c.plotUnits.length;r++)if(c.plotUnits[r].axisYType===a.axisYType&&c.plotUnits[r].axisXType===a.axisXType&&c.plotUnits[r].axisYIndex===a.axisYIndex&&c.plotUnits[r].axisXIndex===a.axisXIndex){g=!0;e=c.plotUnits[r];break}g||(e={type:a.type,previousDataSeriesCount:0,index:c.plotUnits.length,plotType:c,axisXType:a.axisXType,axisYType:a.axisYType,axisYIndex:a.axisYIndex,axisXIndex:a.axisXIndex,axisY:"primary"===a.axisYType?this.axisY[0<=a.axisYIndex&&
a.axisYIndex<this.axisY.length?a.axisYIndex:0]:this.axisY2[0<=a.axisYIndex&&a.axisYIndex<this.axisY2.length?a.axisYIndex:0],axisX:"primary"===a.axisXType?this.axisX[0<=a.axisXIndex&&a.axisXIndex<this.axisX.length?a.axisXIndex:0]:this.axisX2[0<=a.axisXIndex&&a.axisXIndex<this.axisX2.length?a.axisXIndex:0],dataSeriesIndexes:[],yTotals:[],yAbsTotals:[]},c.plotUnits.push(e));c.totalDataSeries++;e.dataSeriesIndexes.push(d);a.plotUnit=e}for(d=0;d<this.plotInfo.plotTypes.length;d++)for(c=this.plotInfo.plotTypes[d],
r=a=0;r<c.plotUnits.length;r++)c.plotUnits[r].previousDataSeriesCount=a,a+=c.plotUnits[r].dataSeriesIndexes.length};n.prototype.assignIdToDataPoints=function(){for(var a=0;a<this.data.length;a++){var d=this.data[a];if(d.dataPoints)for(var c=d.dataPoints.length,b=0;b<c;b++)d.dataPointIds[b]=++this._eventManager.lastObjectId}};n.prototype._processData=function(){this.assignIdToDataPoints();this.categoriseDataSeries();for(var a=0;a<this.plotInfo.plotTypes.length;a++)for(var d=this.plotInfo.plotTypes[a],
c=0;c<d.plotUnits.length;c++){var b=d.plotUnits[c];"line"===b.type||"stepLine"===b.type||"spline"===b.type||"column"===b.type||"area"===b.type||"stepArea"===b.type||"splineArea"===b.type||"bar"===b.type||"bubble"===b.type||"scatter"===b.type?this._processMultiseriesPlotUnit(b):"stackedColumn"===b.type||"stackedBar"===b.type||"stackedArea"===b.type?this._processStackedPlotUnit(b):"stackedColumn100"===b.type||"stackedBar100"===b.type||"stackedArea100"===b.type?this._processStacked100PlotUnit(b):"candlestick"===
b.type||"ohlc"===b.type||"rangeColumn"===b.type||"rangeBar"===b.type||"rangeArea"===b.type||"rangeSplineArea"===b.type||"error"===b.type||"boxAndWhisker"===b.type?this._processMultiYPlotUnit(b):"waterfall"===b.type&&this._processSpecificPlotUnit(b)}this.calculateAutoBreaks()};n.prototype._processMultiseriesPlotUnit=function(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length))for(var d=a.axisY.dataInfo,c=a.axisX.dataInfo,b,e,g=!1,r=0;r<a.dataSeriesIndexes.length;r++){var u=this.data[a.dataSeriesIndexes[r]],
B=0,k=!1,l=!1,p;if("normal"===u.axisPlacement||"xySwapped"===u.axisPlacement)var q=a.axisX.sessionVariables.newViewportMinimum?a.axisX.sessionVariables.newViewportMinimum:a.axisX.options&&a.axisX.options.viewportMinimum?a.axisX.options.viewportMinimum:a.axisX.options&&a.axisX.options.minimum?a.axisX.options.minimum:a.axisX.logarithmic?0:-Infinity,f=a.axisX.sessionVariables.newViewportMaximum?a.axisX.sessionVariables.newViewportMaximum:a.axisX.options&&a.axisX.options.viewportMaximum?a.axisX.options.viewportMaximum:
a.axisX.options&&a.axisX.options.maximum?a.axisX.options.maximum:Infinity;if(u.dataPoints[B].x&&u.dataPoints[B].x.getTime||"dateTime"===u.xValueType)g=!0;for(B=0;B<u.dataPoints.length;B++){"undefined"===typeof u.dataPoints[B].x&&(u.dataPoints[B].x=B+(a.axisX.logarithmic?1:0));u.dataPoints[B].x.getTime?(g=!0,b=u.dataPoints[B].x.getTime()):b=u.dataPoints[B].x;e=u.dataPoints[B].y;b<c.min&&(c.min=b);b>c.max&&(c.max=b);e<d.min&&"number"===typeof e&&(d.min=e);e>d.max&&"number"===typeof e&&(d.max=e);if(0<
B){if(a.axisX.logarithmic){var V=b/u.dataPoints[B-1].x;1>V&&(V=1/V);c.minDiff>V&&1!==V&&(c.minDiff=V)}else V=b-u.dataPoints[B-1].x,0>V&&(V*=-1),c.minDiff>V&&0!==V&&(c.minDiff=V);null!==e&&null!==u.dataPoints[B-1].y&&(a.axisY.logarithmic?(V=e/u.dataPoints[B-1].y,1>V&&(V=1/V),d.minDiff>V&&1!==V&&(d.minDiff=V)):(V=e-u.dataPoints[B-1].y,0>V&&(V*=-1),d.minDiff>V&&0!==V&&(d.minDiff=V)))}if(b<q&&!k)null!==e&&(p=b);else{if(!k&&(k=!0,0<B)){B-=2;continue}if(b>f&&!l)l=!0;else if(b>f&&l)continue;u.dataPoints[B].label&&
(a.axisX.labels[b]=u.dataPoints[B].label);b<c.viewPortMin&&(c.viewPortMin=b);b>c.viewPortMax&&(c.viewPortMax=b);null===e?c.viewPortMin===b&&p<b&&(c.viewPortMin=p):(e<d.viewPortMin&&"number"===typeof e&&(d.viewPortMin=e),e>d.viewPortMax&&"number"===typeof e&&(d.viewPortMax=e))}}u.axisX.valueType=u.xValueType=g?"dateTime":"number"}};n.prototype._processStackedPlotUnit=function(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length)){for(var d=a.axisY.dataInfo,c=a.axisX.dataInfo,b,e,g=!1,r=[],u=[],
B=Infinity,k=-Infinity,l={},p=0;p<a.dataSeriesIndexes.length;p++){var q=this.data[a.dataSeriesIndexes[p]],f=0,V=!1,h=!1,x;if("normal"===q.axisPlacement||"xySwapped"===q.axisPlacement)var s=a.axisX.sessionVariables.newViewportMinimum?a.axisX.sessionVariables.newViewportMinimum:a.axisX.options&&a.axisX.options.viewportMinimum?a.axisX.options.viewportMinimum:a.axisX.options&&a.axisX.options.minimum?a.axisX.options.minimum:-Infinity,y=a.axisX.sessionVariables.newViewportMaximum?a.axisX.sessionVariables.newViewportMaximum:
a.axisX.options&&a.axisX.options.viewportMaximum?a.axisX.options.viewportMaximum:a.axisX.options&&a.axisX.options.maximum?a.axisX.options.maximum:Infinity;if(q.dataPoints[f].x&&q.dataPoints[f].x.getTime||"dateTime"===q.xValueType)g=!0;for(f=0;f<q.dataPoints.length;f++){"undefined"===typeof q.dataPoints[f].x&&(q.dataPoints[f].x=f+(a.axisX.logarithmic?1:0));q.dataPoints[f].x.getTime?(g=!0,b=q.dataPoints[f].x.getTime()):b=q.dataPoints[f].x;e=m(q.dataPoints[f].y)?0:q.dataPoints[f].y;b<c.min&&(c.min=b);
b>c.max&&(c.max=b);if(0<f){if(a.axisX.logarithmic){var v=b/q.dataPoints[f-1].x;1>v&&(v=1/v);c.minDiff>v&&1!==v&&(c.minDiff=v)}else v=b-q.dataPoints[f-1].x,0>v&&(v*=-1),c.minDiff>v&&0!==v&&(c.minDiff=v);null!==e&&null!==q.dataPoints[f-1].y&&(a.axisY.logarithmic?0<e&&(v=e/q.dataPoints[f-1].y,1>v&&(v=1/v),d.minDiff>v&&1!==v&&(d.minDiff=v)):(v=e-q.dataPoints[f-1].y,0>v&&(v*=-1),d.minDiff>v&&0!==v&&(d.minDiff=v)))}if(b<s&&!V)null!==q.dataPoints[f].y&&(x=b);else{if(!V&&(V=!0,0<f)){f-=2;continue}if(b>y&&
!h)h=!0;else if(b>y&&h)continue;q.dataPoints[f].label&&(a.axisX.labels[b]=q.dataPoints[f].label);b<c.viewPortMin&&(c.viewPortMin=b);b>c.viewPortMax&&(c.viewPortMax=b);null===q.dataPoints[f].y?c.viewPortMin===b&&x<b&&(c.viewPortMin=x):(l[b]=(l[b]||0)+q.dataPoints[f].y,q.dataPointEOs[f].cumulativeY=l[b],a.yTotals[b]=(a.yTotals[b]?a.yTotals[b]:0)+e,a.yAbsTotals[b]=(a.yAbsTotals[b]?a.yAbsTotals[b]:0)+Math.abs(e),0<=e?r[b]?r[b]+=e:(r[b]=e,B=Math.min(e,B)):u[b]?u[b]+=e:(u[b]=e,k=Math.max(e,k)))}}a.axisY.scaleBreaks&&
(a.axisY.scaleBreaks.autoCalculate&&1<=a.axisY.scaleBreaks.maxNumberOfAutoBreaks)&&(d.dataPointYPositiveSums?(d.dataPointYPositiveSums.push.apply(d.dataPointYPositiveSums,r),d.dataPointYNegativeSums.push.apply(d.dataPointYPositiveSums,u)):(d.dataPointYPositiveSums=r,d.dataPointYNegativeSums=u));q.axisX.valueType=q.xValueType=g?"dateTime":"number"}for(f in r)r.hasOwnProperty(f)&&!isNaN(f)&&(a=r[f],a<d.min&&(d.min=Math.min(a,B)),a>d.max&&(d.max=a),f<c.viewPortMin||f>c.viewPortMax||(a<d.viewPortMin&&
(d.viewPortMin=Math.min(a,B)),a>d.viewPortMax&&(d.viewPortMax=a)));for(f in u)u.hasOwnProperty(f)&&!isNaN(f)&&(a=u[f],a<d.min&&(d.min=a),a>d.max&&(d.max=Math.max(a,k)),f<c.viewPortMin||f>c.viewPortMax||(a<d.viewPortMin&&(d.viewPortMin=a),a>d.viewPortMax&&(d.viewPortMax=Math.max(a,k))))}};n.prototype._processStacked100PlotUnit=function(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length)){for(var d=a.axisY.dataInfo,c=a.axisX.dataInfo,b,e,g=!1,r=!1,u=!1,B={},k=[],l=0;l<a.dataSeriesIndexes.length;l++){var p=
this.data[a.dataSeriesIndexes[l]],q=0,f=!1,V=!1,h;if("normal"===p.axisPlacement||"xySwapped"===p.axisPlacement)var x=a.axisX.sessionVariables.newViewportMinimum?a.axisX.sessionVariables.newViewportMinimum:a.axisX.options&&a.axisX.options.viewportMinimum?a.axisX.options.viewportMinimum:a.axisX.options&&a.axisX.options.minimum?a.axisX.options.minimum:-Infinity,s=a.axisX.sessionVariables.newViewportMaximum?a.axisX.sessionVariables.newViewportMaximum:a.axisX.options&&a.axisX.options.viewportMaximum?a.axisX.options.viewportMaximum:
a.axisX.options&&a.axisX.options.maximum?a.axisX.options.maximum:Infinity;if(p.dataPoints[q].x&&p.dataPoints[q].x.getTime||"dateTime"===p.xValueType)g=!0;for(q=0;q<p.dataPoints.length;q++){"undefined"===typeof p.dataPoints[q].x&&(p.dataPoints[q].x=q+(a.axisX.logarithmic?1:0));p.dataPoints[q].x.getTime?(g=!0,b=p.dataPoints[q].x.getTime()):b=p.dataPoints[q].x;e=m(p.dataPoints[q].y)?null:p.dataPoints[q].y;b<c.min&&(c.min=b);b>c.max&&(c.max=b);if(0<q){if(a.axisX.logarithmic){var y=b/p.dataPoints[q-1].x;
1>y&&(y=1/y);c.minDiff>y&&1!==y&&(c.minDiff=y)}else y=b-p.dataPoints[q-1].x,0>y&&(y*=-1),c.minDiff>y&&0!==y&&(c.minDiff=y);m(e)||null===p.dataPoints[q-1].y||(a.axisY.logarithmic?0<e&&(y=e/p.dataPoints[q-1].y,1>y&&(y=1/y),d.minDiff>y&&1!==y&&(d.minDiff=y)):(y=e-p.dataPoints[q-1].y,0>y&&(y*=-1),d.minDiff>y&&0!==y&&(d.minDiff=y)))}if(b<x&&!f)null!==e&&(h=b);else{if(!f&&(f=!0,0<q)){q-=2;continue}if(b>s&&!V)V=!0;else if(b>s&&V)continue;p.dataPoints[q].label&&(a.axisX.labels[b]=p.dataPoints[q].label);b<
c.viewPortMin&&(c.viewPortMin=b);b>c.viewPortMax&&(c.viewPortMax=b);null===e?c.viewPortMin===b&&h<b&&(c.viewPortMin=h):(B[b]=(B[b]||0)+p.dataPoints[q].y,p.dataPointEOs[q].cumulativeY=B[b],a.yTotals[b]=(a.yTotals[b]?a.yTotals[b]:0)+e,a.yAbsTotals[b]=(a.yAbsTotals[b]?a.yAbsTotals[b]:0)+Math.abs(e),0<=e?r=!0:0>e&&(u=!0),k[b]=k[b]?k[b]+Math.abs(e):Math.abs(e))}}p.axisX.valueType=p.xValueType=g?"dateTime":"number"}a.axisY.logarithmic?(d.max=m(d.viewPortMax)?99*Math.pow(a.axisY.logarithmBase,-0.05):Math.max(d.viewPortMax,
99*Math.pow(a.axisY.logarithmBase,-0.05)),d.min=m(d.viewPortMin)?1:Math.min(d.viewPortMin,1)):r&&!u?(d.max=m(d.viewPortMax)?99:Math.max(d.viewPortMax,99),d.min=m(d.viewPortMin)?1:Math.min(d.viewPortMin,1)):r&&u?(d.max=m(d.viewPortMax)?99:Math.max(d.viewPortMax,99),d.min=m(d.viewPortMin)?-99:Math.min(d.viewPortMin,-99)):!r&&u&&(d.max=m(d.viewPortMax)?-1:Math.max(d.viewPortMax,-1),d.min=m(d.viewPortMin)?-99:Math.min(d.viewPortMin,-99));d.viewPortMin=d.min;d.viewPortMax=d.max;a.dataPointYSums=k}};n.prototype._processMultiYPlotUnit=
function(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length))for(var d=a.axisY.dataInfo,c=a.axisX.dataInfo,b,e,g,r,u=!1,B=0;B<a.dataSeriesIndexes.length;B++){var k=this.data[a.dataSeriesIndexes[B]],l=0,p=!1,q=!1,f,V,h;if("normal"===k.axisPlacement||"xySwapped"===k.axisPlacement)var m=a.axisX.sessionVariables.newViewportMinimum?a.axisX.sessionVariables.newViewportMinimum:a.axisX.options&&a.axisX.options.viewportMinimum?a.axisX.options.viewportMinimum:a.axisX.options&&a.axisX.options.minimum?
a.axisX.options.minimum:a.axisX.logarithmic?0:-Infinity,s=a.axisX.sessionVariables.newViewportMaximum?a.axisX.sessionVariables.newViewportMaximum:a.axisX.options&&a.axisX.options.viewportMaximum?a.axisX.options.viewportMaximum:a.axisX.options&&a.axisX.options.maximum?a.axisX.options.maximum:Infinity;if(k.dataPoints[l].x&&k.dataPoints[l].x.getTime||"dateTime"===k.xValueType)u=!0;for(l=0;l<k.dataPoints.length;l++){"undefined"===typeof k.dataPoints[l].x&&(k.dataPoints[l].x=l+(a.axisX.logarithmic?1:0));
k.dataPoints[l].x.getTime?(u=!0,b=k.dataPoints[l].x.getTime()):b=k.dataPoints[l].x;if((e=k.dataPoints[l].y)&&e.length){g=Math.min.apply(null,e);r=Math.max.apply(null,e);V=!0;for(var y=0;y<e.length;y++)null===e.k&&(V=!1);V&&(p||(h=f),f=b)}b<c.min&&(c.min=b);b>c.max&&(c.max=b);g<d.min&&(d.min=g);r>d.max&&(d.max=r);0<l&&(a.axisX.logarithmic?(V=b/k.dataPoints[l-1].x,1>V&&(V=1/V),c.minDiff>V&&1!==V&&(c.minDiff=V)):(V=b-k.dataPoints[l-1].x,0>V&&(V*=-1),c.minDiff>V&&0!==V&&(c.minDiff=V)),e&&(null!==e[0]&&
k.dataPoints[l-1].y&&null!==k.dataPoints[l-1].y[0])&&(a.axisY.logarithmic?(V=e[0]/k.dataPoints[l-1].y[0],1>V&&(V=1/V),d.minDiff>V&&1!==V&&(d.minDiff=V)):(V=e[0]-k.dataPoints[l-1].y[0],0>V&&(V*=-1),d.minDiff>V&&0!==V&&(d.minDiff=V))));if(!(b<m)||p){if(!p&&(p=!0,0<l)){l-=2;f=h;continue}if(b>s&&!q)q=!0;else if(b>s&&q)continue;k.dataPoints[l].label&&(a.axisX.labels[b]=k.dataPoints[l].label);b<c.viewPortMin&&(c.viewPortMin=b);b>c.viewPortMax&&(c.viewPortMax=b);if(c.viewPortMin===b&&e)for(y=0;y<e.length;y++)if(null===
e[y]&&f<b){c.viewPortMin=f;break}null===e?c.viewPortMin===b&&f<b&&(c.viewPortMin=f):(g<d.viewPortMin&&(d.viewPortMin=g),r>d.viewPortMax&&(d.viewPortMax=r))}}k.axisX.valueType=k.xValueType=u?"dateTime":"number"}};n.prototype._processSpecificPlotUnit=function(a){if("waterfall"===a.type&&a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length))for(var d=a.axisY.dataInfo,c=a.axisX.dataInfo,b,e,g=!1,r=0;r<a.dataSeriesIndexes.length;r++){var u=this.data[a.dataSeriesIndexes[r]],B=0,k=!1,l=!1,p=b=0;if("normal"===
u.axisPlacement||"xySwapped"===u.axisPlacement)var q=a.axisX.sessionVariables.newViewportMinimum?a.axisX.sessionVariables.newViewportMinimum:a.axisX.options&&a.axisX.options.viewportMinimum?a.axisX.options.viewportMinimum:a.axisX.options&&a.axisX.options.minimum?a.axisX.options.minimum:a.axisX.logarithmic?0:-Infinity,f=a.axisX.sessionVariables.newViewportMaximum?a.axisX.sessionVariables.newViewportMaximum:a.axisX.options&&a.axisX.options.viewportMaximum?a.axisX.options.viewportMaximum:a.axisX.options&&
a.axisX.options.maximum?a.axisX.options.maximum:Infinity;if(u.dataPoints[B].x&&u.dataPoints[B].x.getTime||"dateTime"===u.xValueType)g=!0;for(B=0;B<u.dataPoints.length;B++)"undefined"!==typeof u.dataPoints[B].isCumulativeSum&&!0===u.dataPoints[B].isCumulativeSum?(u.dataPointEOs[B].cumulativeSumYStartValue=0,u.dataPointEOs[B].cumulativeSum=0===B?0:u.dataPointEOs[B-1].cumulativeSum,u.dataPoints[B].y=0===B?0:u.dataPointEOs[B-1].cumulativeSum):"undefined"!==typeof u.dataPoints[B].isIntermediateSum&&!0===
u.dataPoints[B].isIntermediateSum?(u.dataPointEOs[B].cumulativeSumYStartValue=p,u.dataPointEOs[B].cumulativeSum=0===B?0:u.dataPointEOs[B-1].cumulativeSum,u.dataPoints[B].y=0===B?0:b,p=0===B?0:u.dataPointEOs[B-1].cumulativeSum,b=0):(e="number"!==typeof u.dataPoints[B].y?0:u.dataPoints[B].y,u.dataPointEOs[B].cumulativeSumYStartValue=0===B?0:u.dataPointEOs[B-1].cumulativeSum,u.dataPointEOs[B].cumulativeSum=0===B?e:u.dataPointEOs[B-1].cumulativeSum+e,b+=e);for(B=0;B<u.dataPoints.length;B++)if("undefined"===
typeof u.dataPoints[B].x&&(u.dataPoints[B].x=B+(a.axisX.logarithmic?1:0)),u.dataPoints[B].x.getTime?(g=!0,b=u.dataPoints[B].x.getTime()):b=u.dataPoints[B].x,e=u.dataPoints[B].y,b<c.min&&(c.min=b),b>c.max&&(c.max=b),u.dataPointEOs[B].cumulativeSum<d.min&&(d.min=u.dataPointEOs[B].cumulativeSum),u.dataPointEOs[B].cumulativeSum>d.max&&(d.max=u.dataPointEOs[B].cumulativeSum),0<B&&(a.axisX.logarithmic?(p=b/u.dataPoints[B-1].x,1>p&&(p=1/p),c.minDiff>p&&1!==p&&(c.minDiff=p)):(p=b-u.dataPoints[B-1].x,0>p&&
(p*=-1),c.minDiff>p&&0!==p&&(c.minDiff=p)),null!==e&&null!==u.dataPoints[B-1].y&&(a.axisY.logarithmic?(e=u.dataPointEOs[B].cumulativeSum/u.dataPointEOs[B-1].cumulativeSum,1>e&&(e=1/e),d.minDiff>e&&1!==e&&(d.minDiff=e)):(e=u.dataPointEOs[B].cumulativeSum-u.dataPointEOs[B-1].cumulativeSum,0>e&&(e*=-1),d.minDiff>e&&0!==e&&(d.minDiff=e)))),!(b<q)||k){if(!k&&(k=!0,0<B)){B-=2;continue}if(b>f&&!l)l=!0;else if(b>f&&l)continue;u.dataPoints[B].label&&(a.axisX.labels[b]=u.dataPoints[B].label);b<c.viewPortMin&&
(c.viewPortMin=b);b>c.viewPortMax&&(c.viewPortMax=b);0<B&&(u.dataPointEOs[B-1].cumulativeSum<d.viewPortMin&&(d.viewPortMin=u.dataPointEOs[B-1].cumulativeSum),u.dataPointEOs[B-1].cumulativeSum>d.viewPortMax&&(d.viewPortMax=u.dataPointEOs[B-1].cumulativeSum));u.dataPointEOs[B].cumulativeSum<d.viewPortMin&&(d.viewPortMin=u.dataPointEOs[B].cumulativeSum);u.dataPointEOs[B].cumulativeSum>d.viewPortMax&&(d.viewPortMax=u.dataPointEOs[B].cumulativeSum)}u.axisX.valueType=u.xValueType=g?"dateTime":"number"}};
n.prototype.calculateAutoBreaks=function(){function a(a,b,c,e){if(e)return c=Math.pow(Math.min(c*a/b,b/a),0.2),1>=c&&(c=Math.pow(1>a?1/a:Math.min(b/a,a),0.25)),{startValue:a*c,endValue:b/c};c=0.2*Math.min(c-b+a,b-a);0>=c&&(c=0.25*Math.min(b-a,Math.abs(a)));return{startValue:a+c,endValue:b-c}}function d(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length)){var b=a.axisX.scaleBreaks&&a.axisX.scaleBreaks.autoCalculate&&1<=a.axisX.scaleBreaks.maxNumberOfAutoBreaks,c=a.axisY.scaleBreaks&&a.axisY.scaleBreaks.autoCalculate&&
1<=a.axisY.scaleBreaks.maxNumberOfAutoBreaks;if(b||c)for(var d=a.axisY.dataInfo,f=a.axisX.dataInfo,g,r=f.min,k=f.max,l=d.min,q=d.max,f=f._dataRanges,d=d._dataRanges,p,u=0,B=0;B<a.dataSeriesIndexes.length;B++){var h=e.data[a.dataSeriesIndexes[B]];if(!(4>h.dataPoints.length))for(u=0;u<h.dataPoints.length;u++)if(b&&(p=(k+1-r)*Math.max(parseFloat(a.axisX.scaleBreaks.collapsibleThreshold)||10,10)/100,g=h.dataPoints[u].x.getTime?h.dataPoints[u].x.getTime():h.dataPoints[u].x,p=Math.floor((g-r)/p),g<f[p].min&&
(f[p].min=g),g>f[p].max&&(f[p].max=g)),c){var t=(q+1-l)*Math.max(parseFloat(a.axisY.scaleBreaks.collapsibleThreshold)||10,10)/100;if((g="waterfall"===a.type?h.dataPointEOs[u].cumulativeSum:h.dataPoints[u].y)&&g.length)for(var n=0;n<g.length;n++)p=Math.floor((g[n]-l)/t),g[n]<d[p].min&&(d[p].min=g[n]),g[n]>d[p].max&&(d[p].max=g[n]);else m(g)||(p=Math.floor((g-l)/t),g<d[p].min&&(d[p].min=g),g>d[p].max&&(d[p].max=g))}}}}function c(a){if(a.dataSeriesIndexes&&!(1>a.dataSeriesIndexes.length)&&a.axisX.scaleBreaks&&
a.axisX.scaleBreaks.autoCalculate&&1<=a.axisX.scaleBreaks.maxNumberOfAutoBreaks)for(var b=a.axisX.dataInfo,c=b.min,d=b.max,f=b._dataRanges,g,r=0,k=0;k<a.dataSeriesIndexes.length;k++){var l=e.data[a.dataSeriesIndexes[k]];if(!(4>l.dataPoints.length))for(r=0;r<l.dataPoints.length;r++)g=(d+1-c)*Math.max(parseFloat(a.axisX.scaleBreaks.collapsibleThreshold)||10,10)/100,b=l.dataPoints[r].x.getTime?l.dataPoints[r].x.getTime():l.dataPoints[r].x,g=Math.floor((b-c)/g),b<f[g].min&&(f[g].min=b),b>f[g].max&&(f[g].max=
b)}}for(var b,e=this,g=!1,r=0;r<this._axes.length;r++)if(this._axes[r].scaleBreaks&&this._axes[r].scaleBreaks.autoCalculate&&1<=this._axes[r].scaleBreaks.maxNumberOfAutoBreaks){g=!0;this._axes[r].dataInfo._dataRanges=[];for(var u=0;u<100/Math.max(parseFloat(this._axes[r].scaleBreaks.collapsibleThreshold)||10,10);u++)this._axes[r].dataInfo._dataRanges.push({min:Infinity,max:-Infinity})}if(g){for(r=0;r<this.plotInfo.plotTypes.length;r++)for(g=this.plotInfo.plotTypes[r],u=0;u<g.plotUnits.length;u++)b=
g.plotUnits[u],"line"===b.type||"stepLine"===b.type||"spline"===b.type||"column"===b.type||"area"===b.type||"stepArea"===b.type||"splineArea"===b.type||"bar"===b.type||"bubble"===b.type||"scatter"===b.type||"candlestick"===b.type||"ohlc"===b.type||"rangeColumn"===b.type||"rangeBar"===b.type||"rangeArea"===b.type||"rangeSplineArea"===b.type||"waterfall"===b.type||"error"===b.type||"boxAndWhisker"===b.type?d(b):0<=b.type.indexOf("stacked")&&c(b);for(r=0;r<this._axes.length;r++)if(this._axes[r].dataInfo._dataRanges){var B=
this._axes[r].dataInfo.min;b=(this._axes[r].dataInfo.max+1-B)*Math.max(parseFloat(this._axes[r].scaleBreaks.collapsibleThreshold)||10,10)/100;var k=this._axes[r].dataInfo._dataRanges,l,p,g=[];if(this._axes[r].dataInfo.dataPointYPositiveSums){var q=this._axes[r].dataInfo.dataPointYPositiveSums;l=k;for(u in q)if(q.hasOwnProperty(u)&&!isNaN(u)&&(p=q[u],!m(p))){var f=Math.floor((p-B)/b);p<l[f].min&&(l[f].min=p);p>l[f].max&&(l[f].max=p)}delete this._axes[r].dataInfo.dataPointYPositiveSums}if(this._axes[r].dataInfo.dataPointYNegativeSums){q=
this._axes[r].dataInfo.dataPointYNegativeSums;l=k;for(u in q)q.hasOwnProperty(u)&&!isNaN(u)&&(p=-1*q[u],m(p)||(f=Math.floor((p-B)/b),p<l[f].min&&(l[f].min=p),p>l[f].max&&(l[f].max=p)));delete this._axes[r].dataInfo.dataPointYNegativeSums}for(u=0;u<k.length-1;u++)if(l=k[u].max,isFinite(l))for(;u<k.length-1;)if(B=k[u+1].min,isFinite(B)){p=B-l;p>b&&g.push({diff:p,start:l,end:B});break}else u++;if(this._axes[r].scaleBreaks.customBreaks)for(u=0;u<this._axes[r].scaleBreaks.customBreaks.length;u++)for(b=
0;b<g.length;b++)if(this._axes[r].scaleBreaks.customBreaks[u].startValue<=g[b].start&&g[b].start<=this._axes[r].scaleBreaks.customBreaks[u].endValue||this._axes[r].scaleBreaks.customBreaks[u].startValue<=g[b].start&&g[b].start<=this._axes[r].scaleBreaks.customBreaks[u].endValue||g[b].start<=this._axes[r].scaleBreaks.customBreaks[u].startValue&&this._axes[r].scaleBreaks.customBreaks[u].startValue<=g[b].end||g[b].start<=this._axes[r].scaleBreaks.customBreaks[u].endValue&&this._axes[r].scaleBreaks.customBreaks[u].endValue<=
g[b].end)g.splice(b,1),b--;g.sort(function(a,b){return b.diff-a.diff});for(u=0;u<Math.min(g.length,this._axes[r].scaleBreaks.maxNumberOfAutoBreaks);u++)b=a(g[u].start,g[u].end,this._axes[r].logarithmic?this._axes[r].dataInfo.max/this._axes[r].dataInfo.min:this._axes[r].dataInfo.max-this._axes[r].dataInfo.min,this._axes[r].logarithmic),this._axes[r].scaleBreaks.autoBreaks.push(new da(this,"autoBreaks",b,u,++this._eventManager.lastObjectId,this._axes[r].scaleBreaks)),this._axes[r].scaleBreaks._appliedBreaks.push(this._axes[r].scaleBreaks.autoBreaks[this._axes[r].scaleBreaks.autoBreaks.length-
1]);this._axes[r].scaleBreaks._appliedBreaks.sort(function(a,b){return a.startValue-b.startValue})}}};n.prototype.renderCrosshairs=function(a){for(var d=0;d<this.axisX.length;d++)this.axisX[d]!=a&&(this.axisX[d].crosshair&&this.axisX[d].crosshair.enabled&&!this.axisX[d].crosshair._hidden)&&this.axisX[d].showCrosshair(this.axisX[d].crosshair._updatedValue);for(d=0;d<this.axisX2.length;d++)this.axisX2[d]!=a&&(this.axisX2[d].crosshair&&this.axisX2[d].crosshair.enabled&&!this.axisX2[d].crosshair._hidden)&&
this.axisX2[d].showCrosshair(this.axisX2[d].crosshair._updatedValue);for(d=0;d<this.axisY.length;d++)this.axisY[d]!=a&&(this.axisY[d].crosshair&&this.axisY[d].crosshair.enabled&&!this.axisY[d].crosshair._hidden)&&this.axisY[d].showCrosshair(this.axisY[d].crosshair._updatedValue);for(d=0;d<this.axisY2.length;d++)this.axisY2[d]!=a&&(this.axisY2[d].crosshair&&this.axisY2[d].crosshair.enabled&&!this.axisY2[d].crosshair._hidden)&&this.axisY2[d].showCrosshair(this.axisY2[d].crosshair._updatedValue)};n.prototype.getDataPointAtXY=
function(a,d,c){c=c||!1;for(var b=[],e=this._dataInRenderedOrder.length-1;0<=e;e--){var g=null;(g=this._dataInRenderedOrder[e].getDataPointAtXY(a,d,c))&&b.push(g)}a=null;d=!1;for(c=0;c<b.length;c++)if("line"===b[c].dataSeries.type||"stepLine"===b[c].dataSeries.type||"area"===b[c].dataSeries.type||"stepArea"===b[c].dataSeries.type)if(e=oa("markerSize",b[c].dataPoint,b[c].dataSeries)||8,b[c].distance<=e/2){d=!0;break}for(c=0;c<b.length;c++)d&&"line"!==b[c].dataSeries.type&&"stepLine"!==b[c].dataSeries.type&&
"area"!==b[c].dataSeries.type&&"stepArea"!==b[c].dataSeries.type||(a?b[c].distance<=a.distance&&(a=b[c]):a=b[c]);return a};n.prototype.getObjectAtXY=function(a,d,c){var b=null;if(c=this.getDataPointAtXY(a,d,c||!1))b=c.dataSeries.dataPointIds[c.dataPointIndex];else if(t)b=$a(a,d,this._eventManager.ghostCtx);else for(c=0;c<this.legend.items.length;c++){var e=this.legend.items[c];a>=e.x1&&(a<=e.x2&&d>=e.y1&&d<=e.y2)&&(b=e.id)}return b};n.prototype.getAutoFontSize=mb;n.prototype.resetOverlayedCanvas=
function(){this.overlaidCanvasCtx.clearRect(0,0,this.width,this.height)};n.prototype.clearCanvas=lb;n.prototype.attachEvent=function(a){this._events.push(a)};n.prototype._touchEventHandler=function(a){if(a.changedTouches&&this.interactivityEnabled){var d=[],c=a.changedTouches,b=c?c[0]:a,e=null;switch(a.type){case "touchstart":case "MSPointerDown":d=["mousemove","mousedown"];this._lastTouchData=Pa(b);this._lastTouchData.time=new Date;break;case "touchmove":case "MSPointerMove":d=["mousemove"];break;
case "touchend":case "MSPointerUp":var g=this._lastTouchData&&this._lastTouchData.time?new Date-this._lastTouchData.time:0,d="touchstart"===this._lastTouchEventType||"MSPointerDown"===this._lastTouchEventType||300>g?["mouseup","click"]:["mouseup"];break;default:return}if(!(c&&1<c.length)){e=Pa(b);e.time=new Date;try{var r=e.y-this._lastTouchData.y,g=e.time-this._lastTouchData.time;if(1<Math.abs(r)&&this._lastTouchData.scroll||5<Math.abs(r)&&250>g)this._lastTouchData.scroll=!0}catch(u){}this._lastTouchEventType=
a.type;if(this._lastTouchData.scroll&&this.zoomEnabled)this.isDrag&&this.resetOverlayedCanvas(),this.isDrag=!1;else for(c=0;c<d.length;c++)if(e=d[c],r=document.createEvent("MouseEvent"),r.initMouseEvent(e,!0,!0,window,1,b.screenX,b.screenY,b.clientX,b.clientY,!1,!1,!1,!1,0,null),b.target.dispatchEvent(r),!m(this._lastTouchData.scroll)&&!this._lastTouchData.scroll||!this._lastTouchData.scroll&&250<g||"click"===e)a.preventManipulation&&a.preventManipulation(),a.preventDefault&&a.cancelable&&a.preventDefault()}}};
n.prototype._dispatchRangeEvent=function(a,d){var c={chart:this};c.type=a;c.trigger=d;var b=[];this.axisX&&0<this.axisX.length&&b.push("axisX");this.axisX2&&0<this.axisX2.length&&b.push("axisX2");this.axisY&&0<this.axisY.length&&b.push("axisY");this.axisY2&&0<this.axisY2.length&&b.push("axisY2");for(var e=0;e<b.length;e++)if(m(c[b[e]])&&(c[b[e]]=[]),"axisY"===b[e])for(var g=0;g<this.axisY.length;g++)c[b[e]].push({viewportMinimum:this[b[e]][g].sessionVariables.newViewportMinimum,viewportMaximum:this[b[e]][g].sessionVariables.newViewportMaximum});
else if("axisY2"===b[e])for(g=0;g<this.axisY2.length;g++)c[b[e]].push({viewportMinimum:this[b[e]][g].sessionVariables.newViewportMinimum,viewportMaximum:this[b[e]][g].sessionVariables.newViewportMaximum});else if("axisX"===b[e])for(g=0;g<this.axisX.length;g++)c[b[e]].push({viewportMinimum:this[b[e]][g].sessionVariables.newViewportMinimum,viewportMaximum:this[b[e]][g].sessionVariables.newViewportMaximum});else if("axisX2"===b[e])for(g=0;g<this.axisX2.length;g++)c[b[e]].push({viewportMinimum:this[b[e]][g].sessionVariables.newViewportMinimum,
viewportMaximum:this[b[e]][g].sessionVariables.newViewportMaximum});this.dispatchEvent(a,c,this)};n.prototype._mouseEventHandler=function(a){function d(){n.capturedEventParam&&(e=n.capturedEventParam,r=e.bounds,"mouseup"===b&&(n.capturedEventParam=null,e.chart.overlaidCanvas.releaseCapture?e.chart.overlaidCanvas.releaseCapture():document.documentElement.removeEventListener("mouseup",e.chart._mouseEventHandler,!1)),e.hasOwnProperty(b)&&("mouseup"!==b||e.chart.overlaidCanvas.releaseCapture?a.target!==
e.chart.overlaidCanvas&&t||e[b].call(e.context,c.x,c.y):a.target!==e.chart.overlaidCanvas&&(e.chart.isDrag=!1)))}"undefined"===typeof a.target&&a.srcElement&&(a.target=a.srcElement);var c=Pa(a),b=a.type,e,g;a.which?g=3==a.which:a.button&&(g=2==a.button);if(this._ignoreNextEvent)d(),this._ignoreNextEvent=!1;else if(d(),this.interactivityEnabled){a.preventManipulation&&a.preventManipulation();a.preventDefault&&a.preventDefault();var r;Ia&&window.console&&(window.console.log(b+" --\x3e x: "+c.x+"; y:"+
c.y),g&&window.console.log(a.which),"mouseup"===b&&window.console.log("mouseup"));if(!g){if(!n.capturedEventParam&&this._events){for(g=0;g<this._events.length;g++)if(this._events[g].hasOwnProperty(b))if(e=this._events[g],r=e.bounds,c.x>=r.x1&&c.x<=r.x2&&c.y>=r.y1&&c.y<=r.y2){e[b].call(e.context,c.x,c.y);"mousedown"===b&&!0===e.capture?(n.capturedEventParam=e,this.overlaidCanvas.setCapture?this.overlaidCanvas.setCapture():document.documentElement.addEventListener("mouseup",this._mouseEventHandler,
!1)):"mouseup"===b&&(e.chart.overlaidCanvas.releaseCapture?e.chart.overlaidCanvas.releaseCapture():document.documentElement.removeEventListener("mouseup",this._mouseEventHandler,!1));break}else e=null;a.target.style.cursor=e&&e.cursor?e.cursor:this._defaultCursor}g=this.plotArea;if(c.x<g.x1||c.x>g.x2||c.y<g.y1||c.y>g.y2){this.toolTip&&this.toolTip.enabled?(this.toolTip.hide(),this.toolTip.dispatchEvent("hidden",{chart:this,toolTip:this.toolTip},this.toolTip)):this.resetOverlayedCanvas();for(g=0;g<
this.axisX.length;g++)this.axisX[g].crosshair&&this.axisX[g].crosshair.enabled&&(this.axisX[g].crosshair.hide(),this.axisX[g].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisX[g].options},this.axisX[g].crosshair));for(g=0;g<this.axisX2.length;g++)this.axisX2[g].crosshair&&this.axisX2[g].crosshair.enabled&&(this.axisX2[g].crosshair.hide(),this.axisX2[g].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisX2[g].options},this.axisX2[g].crosshair));for(g=0;g<this.axisY.length;g++)this.axisY[g].crosshair&&
this.axisY[g].crosshair.enabled&&(this.axisY[g].crosshair.hide(),this.axisY[g].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisY[g].options},this.axisY[g].crosshair));for(g=0;g<this.axisY2.length;g++)this.axisY2[g].crosshair&&this.axisY2[g].crosshair.enabled&&(this.axisY2[g].crosshair.hide(),this.axisY2[g].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisY2[g].options},this.axisY2[g].crosshair))}this.isDrag&&this.zoomEnabled||!this._eventManager||this._eventManager.mouseEventHandler(a)}}};
n.prototype._plotAreaMouseDown=function(a,d){this.isDrag=!0;this.dragStartPoint={x:a,y:d}};n.prototype._plotAreaMouseUp=function(a,d){if(("normal"===this.plotInfo.axisPlacement||"xySwapped"===this.plotInfo.axisPlacement)&&this.isDrag){var c=d-this.dragStartPoint.y,b=a-this.dragStartPoint.x,e=0<=this.zoomType.indexOf("x"),g=0<=this.zoomType.indexOf("y"),r=!1;this.resetOverlayedCanvas();if("xySwapped"===this.plotInfo.axisPlacement)var u=g,g=e,e=u;if(this.panEnabled||this.zoomEnabled){if(this.panEnabled)for(e=
g=0;e<this._axes.length;e++)c=this._axes[e],c.logarithmic?c.viewportMinimum<c.minimum?(g=c.minimum/c.viewportMinimum,c.sessionVariables.newViewportMinimum=c.viewportMinimum*g,c.sessionVariables.newViewportMaximum=c.viewportMaximum*g,r=!0):c.viewportMaximum>c.maximum&&(g=c.viewportMaximum/c.maximum,c.sessionVariables.newViewportMinimum=c.viewportMinimum/g,c.sessionVariables.newViewportMaximum=c.viewportMaximum/g,r=!0):c.viewportMinimum<c.minimum?(g=c.minimum-c.viewportMinimum,c.sessionVariables.newViewportMinimum=
c.viewportMinimum+g,c.sessionVariables.newViewportMaximum=c.viewportMaximum+g,r=!0):c.viewportMaximum>c.maximum&&(g=c.viewportMaximum-c.maximum,c.sessionVariables.newViewportMinimum=c.viewportMinimum-g,c.sessionVariables.newViewportMaximum=c.viewportMaximum-g,r=!0);else if((!e||2<Math.abs(b))&&(!g||2<Math.abs(c))&&this.zoomEnabled){if(!this.dragStartPoint)return;c=e?this.dragStartPoint.x:this.plotArea.x1;b=g?this.dragStartPoint.y:this.plotArea.y1;e=e?a:this.plotArea.x2;g=g?d:this.plotArea.y2;2<Math.abs(c-
e)&&2<Math.abs(b-g)&&this._zoomPanToSelectedRegion(c,b,e,g)&&(r=!0)}r&&(this._ignoreNextEvent=!0,this._dispatchRangeEvent("rangeChanging","zoom"),this.stockChart&&(this.stockChart.navigator&&this.stockChart.navigator.enabled)&&(this.stockChart._rangeEventParameter||(this.stockChart._rangeEventParameter={stockChart:this.stockChart,source:"chart",index:this.stockChart.charts.indexOf(this),minimum:this.stockChart.sessionVariables._axisXMin,maximum:this.stockChart.sessionVariables._axisXMax}),this.stockChart._rangeEventParameter.type=
"rangeChanging",this.stockChart.dispatchEvent("rangeChanging",this.stockChart._rangeEventParameter,this.stockChart)),this.render(),this._dispatchRangeEvent("rangeChanged","zoom"),this.stockChart&&(this.stockChart.navigator&&this.stockChart.navigator.enabled)&&(this.stockChart._rangeEventParameter.type="rangeChanged",this.stockChart.dispatchEvent("rangeChanged",this.stockChart._rangeEventParameter,this.stockChart)),r&&(this.zoomEnabled&&"none"===this._zoomButton.style.display)&&(Ma(this._zoomButton,
this._resetButton),ua(this,this._zoomButton,"pan"),ua(this,this._resetButton,"reset")))}}this.isDrag=!1;if("none"!==this.plotInfo.axisPlacement){this.resetOverlayedCanvas();if(this.axisX&&0<this.axisX.length)for(r=0;r<this.axisX.length;r++)this.axisX[r].crosshair&&this.axisX[r].crosshair.enabled&&this.axisX[r].renderCrosshair(a,d);if(this.axisX2&&0<this.axisX2.length)for(r=0;r<this.axisX2.length;r++)this.axisX2[r].crosshair&&this.axisX2[r].crosshair.enabled&&this.axisX2[r].renderCrosshair(a,d);if(this.axisY&&
0<this.axisY.length)for(r=0;r<this.axisY.length;r++)this.axisY[r].crosshair&&this.axisY[r].crosshair.enabled&&this.axisY[r].renderCrosshair(a,d);if(this.axisY2&&0<this.axisY2.length)for(r=0;r<this.axisY2.length;r++)this.axisY2[r].crosshair&&this.axisY2[r].crosshair.enabled&&this.axisY2[r].renderCrosshair(a,d);if(this.axisX&&0<this.axisX.length)for(r=0;r<this.axisX.length;r++)this.axisX[r].crosshair&&this.axisX[r].crosshair.enabled&&this.axisX[r].crosshair.renderLabel();if(this.axisX2&&0<this.axisX2.length)for(r=
0;r<this.axisX2.length;r++)this.axisX2[r].crosshair&&this.axisX2[r].crosshair.enabled&&this.axisX2[r].crosshair.renderLabel();if(this.axisY&&0<this.axisY.length)for(r=0;r<this.axisY.length;r++)this.axisY[r].crosshair&&this.axisY[r].crosshair.enabled&&this.axisY[r].crosshair.renderLabel();if(this.axisY2&&0<this.axisY2.length)for(r=0;r<this.axisY2.length;r++)this.axisY2[r].crosshair&&this.axisY2[r].crosshair.enabled&&this.axisY2[r].crosshair.renderLabel()}};n.prototype._plotAreaMouseMove=function(a,
d){if(this.isDrag&&"none"!==this.plotInfo.axisPlacement){var c=0,b=0,e=c=null,e=0<=this.zoomType.indexOf("x"),g=0<=this.zoomType.indexOf("y"),r=this;"xySwapped"===this.plotInfo.axisPlacement&&(c=g,g=e,e=c);c=this.dragStartPoint.x-a;b=this.dragStartPoint.y-d;if(2<Math.abs(c)&&8>Math.abs(c)&&(this.panEnabled||this.zoomEnabled)){this.toolTip.hide();this.toolTip&&this.toolTip.enabled&&this.toolTip.dispatchEvent("hidden",{chart:this,toolTip:this.toolTip},this.toolTip);for(var u=0;u<this.axisX.length;u++)this.axisX[u].crosshair&&
this.axisX[u].crosshair.enabled&&(this.axisX[u].crosshair.hide(),this.axisX[u].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisX[u].options},this.axisX[u].crosshair));for(u=0;u<this.axisX2.length;u++)this.axisX2[u].crosshair&&this.axisX2[u].crosshair.enabled&&(this.axisX2[u].crosshair.hide(),this.axisX2[u].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisX2[u].options},this.axisX2[u].crosshair));for(u=0;u<this.axisY.length;u++)this.axisY[u].crosshair&&this.axisY[u].crosshair.enabled&&
(this.axisY[u].crosshair.hide(),this.axisY[u].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisY[u].options},this.axisY[u].crosshair));for(u=0;u<this.axisY2.length;u++)this.axisY2[u].crosshair&&this.axisY2[u].crosshair.enabled&&(this.axisY2[u].crosshair.hide(),this.axisY2[u].crosshair.dispatchEvent("hidden",{chart:this,axis:this.axisY2[u].options},this.axisY2[u].crosshair))}else this.panEnabled||this.zoomEnabled||this.toolTip.mouseMoveHandler(a,d);if((!e||2<Math.abs(c)||!g||2<Math.abs(b))&&
(this.panEnabled||this.zoomEnabled))if(this.panEnabled)e={x1:e?this.plotArea.x1+c:this.plotArea.x1,y1:g?this.plotArea.y1+b:this.plotArea.y1,x2:e?this.plotArea.x2+c:this.plotArea.x2,y2:g?this.plotArea.y2+b:this.plotArea.y2},clearTimeout(r._panTimerId),r._panTimerId=setTimeout(function(b,c,e,f){return function(){r._zoomPanToSelectedRegion(b,c,e,f,!0)&&(r._dispatchRangeEvent("rangeChanging","pan"),r.stockChart&&(r.stockChart.navigator&&r.stockChart.navigator.enabled)&&(r.stockChart._rangeEventParameter.type=
"rangeChanging",r.stockChart.dispatchEvent("rangeChanging",r.stockChart._rangeEventParameter,r.stockChart)),r.render(),r._dispatchRangeEvent("rangeChanged","pan"),r.stockChart&&(r.stockChart.navigator&&r.stockChart.navigator.enabled)&&(r.stockChart._rangeEventParameter.type="rangeChanged",r.stockChart.dispatchEvent("rangeChanged",r.stockChart._rangeEventParameter,r.stockChart)),r.dragStartPoint.x=a,r.dragStartPoint.y=d)}}(e.x1,e.y1,e.x2,e.y2),0);else if(this.zoomEnabled){this.resetOverlayedCanvas();
c=this.overlaidCanvasCtx.globalAlpha;this.overlaidCanvasCtx.fillStyle="#A89896";var b=e?this.dragStartPoint.x:this.plotArea.x1,u=g?this.dragStartPoint.y:this.plotArea.y1,B=e?a-this.dragStartPoint.x:this.plotArea.x2-this.plotArea.x1,k=g?d-this.dragStartPoint.y:this.plotArea.y2-this.plotArea.y1;this.validateRegion(b,u,e?a:this.plotArea.x2-this.plotArea.x1,g?d:this.plotArea.y2-this.plotArea.y1,"xy"!==this.zoomType).isValid&&(this.resetOverlayedCanvas(),this.overlaidCanvasCtx.fillStyle="#99B2B5");this.overlaidCanvasCtx.globalAlpha=
0.7;this.overlaidCanvasCtx.fillRect(b,u,B,k);this.overlaidCanvasCtx.globalAlpha=c}}else if(this.toolTip.mouseMoveHandler(a,d),"none"!==this.plotInfo.axisPlacement){if(this.axisX&&0<this.axisX.length)for(e=0;e<this.axisX.length;e++)this.axisX[e].crosshair&&this.axisX[e].crosshair.enabled&&this.axisX[e].renderCrosshair(a,d);if(this.axisX2&&0<this.axisX2.length)for(e=0;e<this.axisX2.length;e++)this.axisX2[e].crosshair&&this.axisX2[e].crosshair.enabled&&this.axisX2[e].renderCrosshair(a,d);if(this.axisY&&
0<this.axisY.length)for(e=0;e<this.axisY.length;e++)this.axisY[e].crosshair&&this.axisY[e].crosshair.enabled&&this.axisY[e].renderCrosshair(a,d);if(this.axisY2&&0<this.axisY2.length)for(e=0;e<this.axisY2.length;e++)this.axisY2[e].crosshair&&this.axisY2[e].crosshair.enabled&&this.axisY2[e].renderCrosshair(a,d);if(this.axisX&&0<this.axisX.length)for(e=0;e<this.axisX.length;e++)this.axisX[e].crosshair&&this.axisX[e].crosshair.enabled&&this.axisX[e].crosshair.renderLabel();if(this.axisX2&&0<this.axisX2.length)for(e=
0;e<this.axisX2.length;e++)this.axisX2[e].crosshair&&this.axisX2[e].crosshair.enabled&&this.axisX2[e].crosshair.renderLabel();if(this.axisY&&0<this.axisY.length)for(e=0;e<this.axisY.length;e++)this.axisY[e].crosshair&&this.axisY[e].crosshair.enabled&&this.axisY[e].crosshair.renderLabel();if(this.axisY2&&0<this.axisY2.length)for(e=0;e<this.axisY2.length;e++)this.axisY2[e].crosshair&&this.axisY2[e].crosshair.enabled&&this.axisY2[e].crosshair.renderLabel()}};n.prototype._zoomPanToSelectedRegion=function(a,
d,c,b,e){a=this.validateRegion(a,d,c,b,e);d=a.axesWithValidRange;c=a.axesRanges;if(a.isValid)for(b=0;b<d.length;b++)e=c[b],d[b].setViewPortRange(e.val1,e.val2),this.syncCharts&&"y"!=this.zoomType&&this.syncCharts(e.val1,e.val2),this.stockChart&&(this.stockChart._rangeEventParameter={stockChart:this.stockChart,source:"chart",index:this.stockChart.charts.indexOf(this),minimum:e.val1,maximum:e.val2});return a.isValid};n.prototype.validateRegion=function(a,d,c,b,e){e=e||!1;for(var g=0<=this.zoomType.indexOf("x"),
r=0<=this.zoomType.indexOf("y"),u=!1,B=[],k=[],l=[],p=0;p<this._axes.length;p++)("axisX"===this._axes[p].type&&g||"axisY"===this._axes[p].type&&r)&&k.push(this._axes[p]);for(r=0;r<k.length;r++){var p=k[r],g=!1,q=p.convertPixelToValue({x:a,y:d}),f=p.convertPixelToValue({x:c,y:b});if(q>f)var h=f,f=q,q=h;if(p.scaleBreaks)for(h=0;!g&&h<p.scaleBreaks._appliedBreaks.length;h++)g=p.scaleBreaks._appliedBreaks[h].startValue<=q&&p.scaleBreaks._appliedBreaks[h].endValue>=f;if(isFinite(p.dataInfo.minDiff))if(h=
p.getApparentDifference(q,f,null,!0),!(g||!(this.panEnabled&&p.scaleBreaks&&p.scaleBreaks._appliedBreaks.length)&&(p.logarithmic&&h<Math.pow(p.dataInfo.minDiff,3)||!p.logarithmic&&h<3*Math.abs(p.dataInfo.minDiff))||q<p.minimum||f>p.maximum))B.push(p),l.push({val1:q,val2:f}),u=!0;else if(!e){u=!1;break}}return{isValid:u,axesWithValidRange:B,axesRanges:l}};n.prototype.preparePlotArea=function(){var a=this.plotArea;!t&&(0<a.x1||0<a.y1)&&a.ctx.translate(a.x1,a.y1);if((this.axisX[0]||this.axisX2[0])&&
(this.axisY[0]||this.axisY2[0])){var d=this.axisX[0]?this.axisX[0].lineCoordinates:this.axisX2[0].lineCoordinates;if(this.axisY&&0<this.axisY.length&&this.axisY[0]){var c=this.axisY[0];a.x1=d.x1<d.x2?d.x1:c.lineCoordinates.x1;a.y1=d.y1<c.lineCoordinates.y1?d.y1:c.lineCoordinates.y1;a.x2=d.x2>c.lineCoordinates.x2?d.x2:c.lineCoordinates.x2;a.y2=d.y1>c.lineCoordinates.y2?d.y1:c.lineCoordinates.y2;a.width=a.x2-a.x1;a.height=a.y2-a.y1}this.axisY2&&0<this.axisY2.length&&this.axisY2[0]&&(c=this.axisY2[0],
a.x1=d.x1<d.x2?d.x1:c.lineCoordinates.x1,a.y1=d.y1<c.lineCoordinates.y1?d.y1:c.lineCoordinates.y1,a.x2=d.x2>c.lineCoordinates.x2?d.x2:c.lineCoordinates.x2,a.y2=d.y2>c.lineCoordinates.y2?d.y2:c.lineCoordinates.y2,a.width=a.x2-a.x1,a.height=a.y2-a.y1)}else d=this.layoutManager.getFreeSpace(),a.x1=d.x1,a.x2=d.x2,a.y1=d.y1,a.y2=d.y2,a.width=d.width,a.height=d.height;t||(a.canvas.width=a.width,a.canvas.height=a.height,a.canvas.style.left=a.x1+"px",a.canvas.style.top=a.y1+"px",(0<a.x1||0<a.y1)&&a.ctx.translate(-a.x1,
-a.y1));a.layoutManager=new Fa(a.x1,a.y1,a.x2,a.y2,2)};n.prototype.renderIndexLabels=function(a){var d=a||this.plotArea.ctx,c=this.plotArea,b=0,e=0,g=0,r=g=e=0,u=0,B=b=0,k=0;for(a=0;a<this._indexLabels.length;a++){var l=this._indexLabels[a],p=l.chartType.toLowerCase(),q,f,r=oa("indexLabelFontColor",l.dataPoint,l.dataSeries),h=oa("indexLabelFontSize",l.dataPoint,l.dataSeries),u=oa("indexLabelFontFamily",l.dataPoint,l.dataSeries),B=oa("indexLabelFontStyle",l.dataPoint,l.dataSeries),k=oa("indexLabelFontWeight",
l.dataPoint,l.dataSeries),J=oa("indexLabelBackgroundColor",l.dataPoint,l.dataSeries);q=oa("indexLabelMaxWidth",l.dataPoint,l.dataSeries);f=oa("indexLabelWrap",l.dataPoint,l.dataSeries);var x=oa("indexLabelLineDashType",l.dataPoint,l.dataSeries),s=oa("indexLabelLineColor",l.dataPoint,l.dataSeries),y=m(l.dataPoint.indexLabelLineThickness)?m(l.dataSeries.options.indexLabelLineThickness)?0:l.dataSeries.options.indexLabelLineThickness:l.dataPoint.indexLabelLineThickness,b=0<y?Math.min(10,("normal"===this.plotInfo.axisPlacement?
this.plotArea.height:this.plotArea.width)<<0):0,v={percent:null,total:null},n=null;if(0<=l.dataSeries.type.indexOf("stacked")||"pie"===l.dataSeries.type||"doughnut"===l.dataSeries.type)v=this.getPercentAndTotal(l.dataSeries,l.dataPoint);if(l.dataSeries.indexLabelFormatter||l.dataPoint.indexLabelFormatter)n={chart:this,dataSeries:l.dataSeries,dataPoint:l.dataPoint,index:l.indexKeyword,total:v.total,percent:v.percent};var A=l.dataPoint.indexLabelFormatter?l.dataPoint.indexLabelFormatter(n):l.dataPoint.indexLabel?
this.replaceKeywordsWithValue(l.dataPoint.indexLabel,l.dataPoint,l.dataSeries,null,l.indexKeyword):l.dataSeries.indexLabelFormatter?l.dataSeries.indexLabelFormatter(n):l.dataSeries.indexLabel?this.replaceKeywordsWithValue(l.dataSeries.indexLabel,l.dataPoint,l.dataSeries,null,l.indexKeyword):null;if(null!==A&&""!==A){var v=oa("indexLabelPlacement",l.dataPoint,l.dataSeries),n=oa("indexLabelOrientation",l.dataPoint,l.dataSeries),C=oa("indexLabelTextAlign",l.dataPoint,l.dataSeries),w=l.direction,e=l.dataSeries.axisX,
g=l.dataSeries.axisY,z=!1,J=new la(d,{x:0,y:0,maxWidth:q?q:0.5*this.width,maxHeight:f?5*h:1.5*h,angle:"horizontal"===n?0:-90,text:A,padding:0,backgroundColor:J,textAlign:C,fontSize:h,fontFamily:u,fontWeight:k,fontColor:r,fontStyle:B,textBaseline:"middle"});J.measureText();l.dataSeries.indexLabelMaxWidth=J.maxWidth;if("stackedarea100"===p){if(l.point.x<c.x1||l.point.x>c.x2||l.point.y<c.y1-1||l.point.y>c.y2+1)continue}else if("rangearea"===p||"rangesplinearea"===p){if(l.dataPoint.x<e.viewportMinimum||
l.dataPoint.x>e.viewportMaximum||Math.max.apply(null,l.dataPoint.y)<g.viewportMinimum||Math.min.apply(null,l.dataPoint.y)>g.viewportMaximum)continue}else if(0<=p.indexOf("line")||0<=p.indexOf("area")||0<=p.indexOf("bubble")||0<=p.indexOf("scatter")){if(l.dataPoint.x<e.viewportMinimum||l.dataPoint.x>e.viewportMaximum||l.dataPoint.y<g.viewportMinimum||l.dataPoint.y>g.viewportMaximum)continue}else if(0<=p.indexOf("column")||"waterfall"===p||"error"===p&&!l.axisSwapped){if(l.dataPoint.x<e.viewportMinimum||
l.dataPoint.x>e.viewportMaximum||l.bounds.y1>c.y2||l.bounds.y2<c.y1)continue}else if(0<=p.indexOf("bar")||"error"===p){if(l.dataPoint.x<e.viewportMinimum||l.dataPoint.x>e.viewportMaximum||l.bounds.x1>c.x2||l.bounds.x2<c.x1)continue}else if("candlestick"===p||"ohlc"===p){if(l.dataPoint.x<e.viewportMinimum||l.dataPoint.x>e.viewportMaximum||Math.max.apply(null,l.dataPoint.y)<g.viewportMinimum||Math.min.apply(null,l.dataPoint.y)>g.viewportMaximum)continue}else if(l.dataPoint.x<e.viewportMinimum||l.dataPoint.x>
e.viewportMaximum)continue;r=u=2;"horizontal"===n?(B=J.width,k=J.height):(k=J.width,B=J.height);if("normal"===this.plotInfo.axisPlacement){if(0<=p.indexOf("line")||0<=p.indexOf("area"))v="auto",u=4;else if(0<=p.indexOf("stacked"))"auto"===v&&(v="inside");else if("bubble"===p||"scatter"===p)v="inside";q=l.point.x-("horizontal"===n?B/2:B/2-h/2);"inside"!==v?(e=c.y1,g=c.y2,0<w?(f=l.point.y+("horizontal"===n?h/2:0)-k-u-b,f<e&&(f="auto"===v?Math.max(l.point.y,e)+h/2+u:e+h/2+u,z=f+k>l.point.y)):(f=l.point.y+
h/2+u+b,f>g-k&&(f="auto"===v?Math.min(l.point.y,g)+h/2-k-u:g+h/2-k,z=f<l.point.y))):(e=Math.max(l.bounds.y1,c.y1),g=Math.min(l.bounds.y2,c.y2-k+h/2),b=0<=p.indexOf("range")||"error"===p?0<w?Math.max(l.bounds.y1,c.y1)+h/2+u:Math.min(l.bounds.y2,c.y2)+h/2-k+u:(Math.max(l.bounds.y1,c.y1)+Math.min(l.bounds.y2,c.y2))/2-k/2+h/2+("horizontal"===n?u:0),0<w?(f=Math.max(l.point.y,b),f<e&&("bubble"===p||"scatter"===p)&&(f=Math.max(l.point.y-k-u,c.y1+u))):(f=Math.min(l.point.y,b),f>g-k-u&&("bubble"===p||"scatter"===
p)&&(f=Math.min(l.point.y+u,c.y2-k-u))),f=Math.min(f,g))}else 0<=p.indexOf("line")||0<=p.indexOf("area")||0<=p.indexOf("scatter")?(v="auto",r=4):0<=p.indexOf("stacked")?"auto"===v&&(v="inside"):"bubble"===p&&(v="inside"),f=l.point.y+h/2-k/2+u,"inside"!==v?(e=c.x1,g=c.x2,0>w?(q=l.point.x-("horizontal"===n?B:B-h/2)-r-b,q<e&&(q="auto"===v?Math.max(l.point.x,e)+r:e+r,z=q+B>l.point.x)):(q=l.point.x+("horizontal"===n?0:h/2)+r+b,q>g-B-r-b&&(q="auto"===v?Math.min(l.point.x,g)-("horizontal"===n?B:B/2)-r:g-
B-r,z=q<l.point.x))):(e=Math.max(l.bounds.x1,c.x1),Math.min(l.bounds.x2,c.x2),b=0<=p.indexOf("range")||"error"===p?0>w?Math.max(l.bounds.x1,c.x1)+h/2+r:Math.min(l.bounds.x2,c.x2)-B/2-r+("horizontal"===n?0:h/2):(Math.max(l.bounds.x1,c.x1)+Math.min(l.bounds.x2,c.x2))/2+("horizontal"===n?0:h/2),q=0>w?Math.max(l.point.x,b)-("horizontal"===n?B/2:0):Math.min(l.point.x,b)-B/2,q=Math.max(q,e));"vertical"===n&&(f+=k-h/2);J.x=q;J.y=f;J.render(!0);y&&("inside"!==v&&(0>p.indexOf("bar")&&("error"!==p||!l.axisSwapped)&&
l.point.x>c.x1&&l.point.x<c.x2||!z)&&(0>p.indexOf("column")&&("error"!==p||l.axisSwapped)&&l.point.y>c.y1&&l.point.y<c.y2||!z))&&(d.lineWidth=y,d.strokeStyle=s?s:"gray",d.setLineDash&&d.setLineDash(H(x,y)),d.beginPath(),d.moveTo(l.point.x,l.point.y),0<=p.indexOf("bar")||"error"===p&&l.axisSwapped?d.lineTo(q+(0<l.direction?-r:B+r)+("vertical"===n?-h/2:0),f+("vertical"===n?-k/2:k/2-h/2)-u):0<=p.indexOf("column")||"error"===p&&!l.axisSwapped?d.lineTo(q+B/2-("horizontal"===n?0:h/2),f+("vertical"===n?
(f-k<l.point.y?0:-k)+u:(f-h/2<l.point.y?k:0)-h/2)):0<=p.indexOf("waterfall")?d.lineTo(q+B/2-("horizontal"===n?0:h/2),"vertical"===n?0<w&&f<l.point.y?f:0>w&&f-k>l.point.y?f-k:l.point.y:0<w&&f+k-h/2<l.point.y?f+k-h/2:0>w&&f-h/2>l.point.y?f-h/2-2:l.point.y):d.lineTo(q+B/2-("horizontal"===n?0:h/2),f+("vertical"===n?f-k<l.point.y?0:-k:(f+k<l.point.y?k:0)-h/2)),d.stroke())}}d={source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0,startTimePercent:0.7};
for(a=0;a<this._indexLabels.length;a++)l=this._indexLabels[a],J=oa("indexLabelBackgroundColor",l.dataPoint,l.dataSeries),l.dataSeries.indexLabelBackgroundColor=m(J)?t?"transparent":null:J;return d};n.prototype.renderLine=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=this._eventManager.ghostCtx;c.save();var e=this.plotArea;c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();for(var g=[],r,u=0;u<a.dataSeriesIndexes.length;u++){var B=
a.dataSeriesIndexes[u],k=this.data[B];c.lineWidth=k.lineThickness;var l=k.dataPoints,p="solid";if(c.setLineDash){var q=H(k.nullDataLineDashType,k.lineThickness),p=k.lineDashType,f=H(p,k.lineThickness);c.setLineDash(f)}var h=k.id;this._eventManager.objectMap[h]={objectType:"dataSeries",dataSeriesIndex:B};h=Z(h);b.strokeStyle=h;b.lineWidth=0<k.lineThickness?Math.max(k.lineThickness,4):0;var h=k._colorSet,m=h=k.lineColor=k.options.lineColor?k.options.lineColor:h[0];c.strokeStyle=h;var x=!0,s=0,n,v;c.beginPath();
if(0<l.length){for(var I=!1,s=0;s<l.length;s++)if(n=l[s].x.getTime?l[s].x.getTime():l[s].x,!(n<a.axisX.dataInfo.viewPortMin||n>a.axisX.dataInfo.viewPortMax&&(!k.connectNullData||!I)))if("number"!==typeof l[s].y)0<s&&!(k.connectNullData||I||x)&&(c.stroke(),t&&b.stroke()),I=!0;else{n=a.axisX.convertValueToPixel(n);v=a.axisY.convertValueToPixel(l[s].y);var A=k.dataPointIds[s];this._eventManager.objectMap[A]={id:A,objectType:"dataPoint",dataSeriesIndex:B,dataPointIndex:s,x1:n,y1:v};x||I?(!x&&k.connectNullData?
(c.setLineDash&&(k.options.nullDataLineDashType||p===k.lineDashType&&k.lineDashType!==k.nullDataLineDashType)&&(c.stroke(),c.beginPath(),c.moveTo(r.x,r.y),p=k.nullDataLineDashType,c.setLineDash(q)),c.lineTo(n,v),t&&b.lineTo(n,v)):(c.beginPath(),c.moveTo(n,v),t&&(b.beginPath(),b.moveTo(n,v))),I=x=!1):(c.lineTo(n,v),t&&b.lineTo(n,v),0==s%500&&(c.stroke(),c.beginPath(),c.moveTo(n,v),t&&(b.stroke(),b.beginPath(),b.moveTo(n,v))));r={x:n,y:v};s<l.length-1&&(m!==(l[s].lineColor||h)||p!==(l[s].lineDashType||
k.lineDashType))&&(c.stroke(),c.beginPath(),c.moveTo(n,v),m=l[s].lineColor||h,c.strokeStyle=m,c.setLineDash&&(l[s].lineDashType?(p=l[s].lineDashType,c.setLineDash(H(p,k.lineThickness))):(p=k.lineDashType,c.setLineDash(f))));if(0!==l[s].markerSize&&(0<l[s].markerSize||0<k.markerSize)){var C=k.getMarkerProperties(s,n,v,c);g.push(C);A=Z(A);t&&g.push({x:n,y:v,ctx:b,type:C.type,size:C.size,color:A,borderColor:A,borderThickness:C.borderThickness})}(l[s].indexLabel||k.indexLabel||l[s].indexLabelFormatter||
k.indexLabelFormatter)&&this._indexLabels.push({chartType:"line",dataPoint:l[s],dataSeries:k,point:{x:n,y:v},direction:0>l[s].y===a.axisY.reversed?1:-1,color:h})}c.stroke();t&&b.stroke()}}X.drawMarkers(g);t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,
0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),b.beginPath());c.restore();c.beginPath();return{source:d,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderStepLine=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=this._eventManager.ghostCtx;c.save();var e=this.plotArea;c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();for(var g=
[],r,u=0;u<a.dataSeriesIndexes.length;u++){var h=a.dataSeriesIndexes[u],k=this.data[h];c.lineWidth=k.lineThickness;var l=k.dataPoints,p="solid";if(c.setLineDash){var q=H(k.nullDataLineDashType,k.lineThickness),p=k.lineDashType,f=H(p,k.lineThickness);c.setLineDash(f)}var m=k.id;this._eventManager.objectMap[m]={objectType:"dataSeries",dataSeriesIndex:h};m=Z(m);b.strokeStyle=m;b.lineWidth=0<k.lineThickness?Math.max(k.lineThickness,4):0;var m=k._colorSet,n=m=k.lineColor=k.options.lineColor?k.options.lineColor:
m[0];c.strokeStyle=m;var x=!0,s=0,y,v;c.beginPath();if(0<l.length){for(var I=!1,s=0;s<l.length;s++)if(y=l[s].getTime?l[s].x.getTime():l[s].x,!(y<a.axisX.dataInfo.viewPortMin||y>a.axisX.dataInfo.viewPortMax&&(!k.connectNullData||!I)))if("number"!==typeof l[s].y)0<s&&!(k.connectNullData||I||x)&&(c.stroke(),t&&b.stroke()),I=!0;else{var A=v;y=a.axisX.convertValueToPixel(y);v=a.axisY.convertValueToPixel(l[s].y);var C=k.dataPointIds[s];this._eventManager.objectMap[C]={id:C,objectType:"dataPoint",dataSeriesIndex:h,
dataPointIndex:s,x1:y,y1:v};x||I?(!x&&k.connectNullData?(c.setLineDash&&(k.options.nullDataLineDashType||p===k.lineDashType&&k.lineDashType!==k.nullDataLineDashType)&&(c.stroke(),c.beginPath(),c.moveTo(r.x,r.y),p=k.nullDataLineDashType,c.setLineDash(q)),c.lineTo(y,A),c.lineTo(y,v),t&&(b.lineTo(y,A),b.lineTo(y,v))):(c.beginPath(),c.moveTo(y,v),t&&(b.beginPath(),b.moveTo(y,v))),I=x=!1):(c.lineTo(y,A),t&&b.lineTo(y,A),c.lineTo(y,v),t&&b.lineTo(y,v),0==s%500&&(c.stroke(),c.beginPath(),c.moveTo(y,v),t&&
(b.stroke(),b.beginPath(),b.moveTo(y,v))));r={x:y,y:v};s<l.length-1&&(n!==(l[s].lineColor||m)||p!==(l[s].lineDashType||k.lineDashType))&&(c.stroke(),c.beginPath(),c.moveTo(y,v),n=l[s].lineColor||m,c.strokeStyle=n,c.setLineDash&&(l[s].lineDashType?(p=l[s].lineDashType,c.setLineDash(H(p,k.lineThickness))):(p=k.lineDashType,c.setLineDash(f))));0!==l[s].markerSize&&(0<l[s].markerSize||0<k.markerSize)&&(A=k.getMarkerProperties(s,y,v,c),g.push(A),C=Z(C),t&&g.push({x:y,y:v,ctx:b,type:A.type,size:A.size,
color:C,borderColor:C,borderThickness:A.borderThickness}));(l[s].indexLabel||k.indexLabel||l[s].indexLabelFormatter||k.indexLabelFormatter)&&this._indexLabels.push({chartType:"stepLine",dataPoint:l[s],dataSeries:k,point:{x:y,y:v},direction:0>l[s].y===a.axisY.reversed?1:-1,color:m})}c.stroke();t&&b.stroke()}}X.drawMarkers(g);t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),
a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),b.beginPath());c.restore();c.beginPath();return{source:d,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderSpline=function(a){function d(a){a=w(a,2);if(0<a.length){b.beginPath();t&&e.beginPath();b.moveTo(a[0].x,
a[0].y);a[0].newStrokeStyle&&(b.strokeStyle=a[0].newStrokeStyle);a[0].newLineDashArray&&b.setLineDash(a[0].newLineDashArray);t&&e.moveTo(a[0].x,a[0].y);for(var c=0;c<a.length-3;c+=3)if(b.bezierCurveTo(a[c+1].x,a[c+1].y,a[c+2].x,a[c+2].y,a[c+3].x,a[c+3].y),t&&e.bezierCurveTo(a[c+1].x,a[c+1].y,a[c+2].x,a[c+2].y,a[c+3].x,a[c+3].y),0<c&&0===c%3E3||a[c+3].newStrokeStyle||a[c+3].newLineDashArray)b.stroke(),b.beginPath(),b.moveTo(a[c+3].x,a[c+3].y),a[c+3].newStrokeStyle&&(b.strokeStyle=a[c+3].newStrokeStyle),
a[c+3].newLineDashArray&&b.setLineDash(a[c+3].newLineDashArray),t&&(e.stroke(),e.beginPath(),e.moveTo(a[c+3].x,a[c+3].y));b.stroke();t&&e.stroke()}}var c=a.targetCanvasCtx||this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=this._eventManager.ghostCtx;b.save();var g=this.plotArea;b.beginPath();b.rect(g.x1,g.y1,g.width,g.height);b.clip();for(var r=[],u=0;u<a.dataSeriesIndexes.length;u++){var h=a.dataSeriesIndexes[u],k=this.data[h];b.lineWidth=k.lineThickness;var l=
k.dataPoints,p="solid";if(b.setLineDash){var q=H(k.nullDataLineDashType,k.lineThickness),p=k.lineDashType,f=H(p,k.lineThickness);b.setLineDash(f)}var m=k.id;this._eventManager.objectMap[m]={objectType:"dataSeries",dataSeriesIndex:h};m=Z(m);e.strokeStyle=m;e.lineWidth=0<k.lineThickness?Math.max(k.lineThickness,4):0;var m=k._colorSet,n=m=k.lineColor=k.options.lineColor?k.options.lineColor:m[0];b.strokeStyle=m;var x=0,s,y,v=[];b.beginPath();if(0<l.length)for(y=!1,x=0;x<l.length;x++)if(s=l[x].getTime?
l[x].x.getTime():l[x].x,!(s<a.axisX.dataInfo.viewPortMin||s>a.axisX.dataInfo.viewPortMax&&(!k.connectNullData||!y)))if("number"!==typeof l[x].y)0<x&&!y&&(k.connectNullData?b.setLineDash&&(0<v.length&&(k.options.nullDataLineDashType||!l[x-1].lineDashType))&&(v[v.length-1].newLineDashArray=q,p=k.nullDataLineDashType):(d(v),v=[])),y=!0;else{s=a.axisX.convertValueToPixel(s);y=a.axisY.convertValueToPixel(l[x].y);var I=k.dataPointIds[x];this._eventManager.objectMap[I]={id:I,objectType:"dataPoint",dataSeriesIndex:h,
dataPointIndex:x,x1:s,y1:y};v[v.length]={x:s,y:y};x<l.length-1&&(n!==(l[x].lineColor||m)||p!==(l[x].lineDashType||k.lineDashType))&&(n=l[x].lineColor||m,v[v.length-1].newStrokeStyle=n,b.setLineDash&&(l[x].lineDashType?(p=l[x].lineDashType,v[v.length-1].newLineDashArray=H(p,k.lineThickness)):(p=k.lineDashType,v[v.length-1].newLineDashArray=f)));if(0!==l[x].markerSize&&(0<l[x].markerSize||0<k.markerSize)){var A=k.getMarkerProperties(x,s,y,b);r.push(A);I=Z(I);t&&r.push({x:s,y:y,ctx:e,type:A.type,size:A.size,
color:I,borderColor:I,borderThickness:A.borderThickness})}(l[x].indexLabel||k.indexLabel||l[x].indexLabelFormatter||k.indexLabelFormatter)&&this._indexLabels.push({chartType:"spline",dataPoint:l[x],dataSeries:k,point:{x:s,y:y},direction:0>l[x].y===a.axisY.reversed?1:-1,color:m});y=!1}d(v)}X.drawMarkers(r);t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&
b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.clearRect(g.x1,g.y1,g.width,g.height),e.beginPath());b.restore();b.beginPath();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderColumn=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=
null,e=this.plotArea,g=0,r,u,h,k=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),g=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,l=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:Math.min(0.15*this.width,0.9*(this.plotArea.width/a.plotType.totalDataSeries))<<0,p=a.axisX.dataInfo.minDiff;isFinite(p)||(p=0.3*Math.abs(a.axisX.range));p=this.dataPointWidth=this.options.dataPointWidth?
this.dataPointWidth:0.9*(e.width*(a.axisX.logarithmic?Math.log(p)/Math.log(a.axisX.range):Math.abs(p)/Math.abs(a.axisX.range))/a.plotType.totalDataSeries)<<0;this.dataPointMaxWidth&&g>l&&(g=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,l));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&l<g)&&(l=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,g));p<g&&(p=g);p>l&&(p=l);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);
c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(l=0;l<a.dataSeriesIndexes.length;l++){var q=a.dataSeriesIndexes[l],f=this.data[q],m=f.dataPoints;if(0<m.length)for(var n=5<p&&f.bevelEnabled?!0:!1,g=0;g<m.length;g++)if(m[g].getTime?h=m[g].x.getTime():h=m[g].x,!(h<a.axisX.dataInfo.viewPortMin||h>a.axisX.dataInfo.viewPortMax)&&"number"===typeof m[g].y){r=a.axisX.convertValueToPixel(h);u=a.axisY.convertValueToPixel(m[g].y);
r=a.axisX.reversed?r+a.plotType.totalDataSeries*p/2-(a.previousDataSeriesCount+l)*p<<0:r-a.plotType.totalDataSeries*p/2+(a.previousDataSeriesCount+l)*p<<0;var x=a.axisX.reversed?r-p<<0:r+p<<0,s;0<=m[g].y?s=k:(s=u,u=k);u>s&&(b=u,u=s,s=b);b=m[g].color?m[g].color:f._colorSet[g%f._colorSet.length];ba(c,a.axisX.reversed?x:r,u,a.axisX.reversed?r:x,s,b,0,null,n&&(a.axisY.reversed?0>m[g].y:0<=m[g].y),(a.axisY.reversed?0<=m[g].y:0>m[g].y)&&n,!1,!1,f.fillOpacity);b=f.dataPointIds[g];this._eventManager.objectMap[b]=
{id:b,objectType:"dataPoint",dataSeriesIndex:q,dataPointIndex:g,x1:r,y1:u,x2:x,y2:s};b=Z(b);t&&ba(this._eventManager.ghostCtx,a.axisX.reversed?x:r,u,a.axisX.reversed?r:x,s,b,0,null,!1,!1,!1,!1);(m[g].indexLabel||f.indexLabel||m[g].indexLabelFormatter||f.indexLabelFormatter)&&this._indexLabels.push({chartType:"column",dataPoint:m[g],dataSeries:f,point:{x:r+(x-r)/2,y:0>m[g].y===a.axisY.reversed?u:s},direction:0>m[g].y===a.axisY.reversed?1:-1,bounds:{x1:r,y1:Math.min(u,s),x2:x,y2:Math.max(u,s)},color:b})}}t&&
(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.yScaleAnimation,
easingFunction:N.easing.easeOutQuart,animationBase:k<a.axisY.bounds.y1?a.axisY.bounds.y1:k>a.axisY.bounds.y2?a.axisY.bounds.y2:k}}};n.prototype.renderStackedColumn=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=[],r=[],u=[],h=[],k=0,l,p,q=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),k=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:
1,f=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.15*this.width<<0,m=a.axisX.dataInfo.minDiff;isFinite(m)||(m=0.3*Math.abs(a.axisX.range));m=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.width*(a.axisX.logarithmic?Math.log(m)/Math.log(a.axisX.range):Math.abs(m)/Math.abs(a.axisX.range))/a.plotType.plotUnits.length)<<0;this.dataPointMaxWidth&&k>f&&(k=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,f));!this.dataPointMaxWidth&&
(this.dataPointMinWidth&&f<k)&&(f=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,k));m<k&&(m=k);m>f&&(m=f);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(f=0;f<a.dataSeriesIndexes.length;f++){var n=a.dataSeriesIndexes[f],x=this.data[n],s=x.dataPoints;if(0<s.length){var y=5<m&&x.bevelEnabled?
!0:!1;c.strokeStyle="#4572A7 ";for(k=0;k<s.length;k++)if(b=s[k].x.getTime?s[k].x.getTime():s[k].x,!(b<a.axisX.dataInfo.viewPortMin||b>a.axisX.dataInfo.viewPortMax)&&"number"===typeof s[k].y){l=a.axisX.convertValueToPixel(b);l=l-a.plotType.plotUnits.length*m/2+a.index*m<<0;var v=l+m<<0,I;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&0<s[k].y)u[b]=s[k].y+(u[b]?u[b]:0),0<u[b]&&(p=a.axisY.convertValueToPixel(u[b]),I="undefined"!==typeof g[b]?g[b]:q,g[b]=p);
else if(a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&0>=s[k].y)h[b]=s[k].y+(h[b]?h[b]:0),I=a.axisY.convertValueToPixel(h[b]),p="undefined"!==typeof r[b]?r[b]:q,r[b]=I;else if(p=a.axisY.convertValueToPixel(s[k].y),0<=s[k].y){var A="undefined"!==typeof g[b]?g[b]:0;p-=A;I=q-A;g[b]=A+(I-p)}else A=r[b]?r[b]:0,I=p+A,p=q+A,r[b]=A+(I-p);b=s[k].color?s[k].color:x._colorSet[k%x._colorSet.length];ba(c,l,a.axisY.reversed?I:p,v,a.axisY.reversed?p:I,b,0,null,y&&(a.axisY.reversed?0>s[k].y:0<=
s[k].y),(a.axisY.reversed?0<=s[k].y:0>s[k].y)&&y,!1,!1,x.fillOpacity);b=x.dataPointIds[k];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:n,dataPointIndex:k,x1:l,y1:p,x2:v,y2:I};b=Z(b);t&&ba(this._eventManager.ghostCtx,l,p,v,I,b,0,null,!1,!1,!1,!1);(s[k].indexLabel||x.indexLabel||s[k].indexLabelFormatter||x.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedColumn",dataPoint:s[k],dataSeries:x,point:{x:l+(v-l)/2,y:0<=s[k].y?p:I},direction:0>s[k].y===a.axisY.reversed?
1:-1,bounds:{x1:l,y1:Math.min(p,I),x2:v,y2:Math.max(p,I)},color:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());
c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.yScaleAnimation,easingFunction:N.easing.easeOutQuart,animationBase:q<a.axisY.bounds.y1?a.axisY.bounds.y1:q>a.axisY.bounds.y2?a.axisY.bounds.y2:q}}};n.prototype.renderStackedColumn100=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=[],r=[],u=[],h=[],k=0,l,p,q=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),
k=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,f=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.15*this.width<<0,m=a.axisX.dataInfo.minDiff;isFinite(m)||(m=0.3*Math.abs(a.axisX.range));m=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.width*(a.axisX.logarithmic?Math.log(m)/Math.log(a.axisX.range):Math.abs(m)/Math.abs(a.axisX.range))/a.plotType.plotUnits.length)<<0;this.dataPointMaxWidth&&
k>f&&(k=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,f));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&f<k)&&(f=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,k));m<k&&(m=k);m>f&&(m=f);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(f=0;f<a.dataSeriesIndexes.length;f++){var n=
a.dataSeriesIndexes[f],x=this.data[n],s=x.dataPoints;if(0<s.length)for(var y=5<m&&x.bevelEnabled?!0:!1,k=0;k<s.length;k++)if(b=s[k].x.getTime?s[k].x.getTime():s[k].x,!(b<a.axisX.dataInfo.viewPortMin||b>a.axisX.dataInfo.viewPortMax)&&"number"===typeof s[k].y){l=a.axisX.convertValueToPixel(b);p=0!==a.dataPointYSums[b]?100*(s[k].y/a.dataPointYSums[b]):0;l=l-a.plotType.plotUnits.length*m/2+a.index*m<<0;var v=l+m<<0,I;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&
0<s[k].y){u[b]=p+("undefined"!==typeof u[b]?u[b]:0);if(0>=u[b])continue;p=a.axisY.convertValueToPixel(u[b]);I=g[b]?g[b]:q;g[b]=p}else if(a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&0>=s[k].y)h[b]=p+("undefined"!==typeof h[b]?h[b]:0),I=a.axisY.convertValueToPixel(h[b]),p=r[b]?r[b]:q,r[b]=I;else if(p=a.axisY.convertValueToPixel(p),0<=s[k].y){var A="undefined"!==typeof g[b]?g[b]:0;p-=A;I=q-A;a.dataSeriesIndexes.length-1===f&&1>=Math.abs(e.y1-p)&&(p=e.y1);g[b]=A+(I-p)}else A="undefined"!==
typeof r[b]?r[b]:0,I=p+A,p=q+A,a.dataSeriesIndexes.length-1===f&&1>=Math.abs(e.y2-I)&&(I=e.y2),r[b]=A+(I-p);b=s[k].color?s[k].color:x._colorSet[k%x._colorSet.length];ba(c,l,a.axisY.reversed?I:p,v,a.axisY.reversed?p:I,b,0,null,y&&(a.axisY.reversed?0>s[k].y:0<=s[k].y),(a.axisY.reversed?0<=s[k].y:0>s[k].y)&&y,!1,!1,x.fillOpacity);b=x.dataPointIds[k];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:n,dataPointIndex:k,x1:l,y1:p,x2:v,y2:I};b=Z(b);t&&ba(this._eventManager.ghostCtx,
l,p,v,I,b,0,null,!1,!1,!1,!1);(s[k].indexLabel||x.indexLabel||s[k].indexLabelFormatter||x.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedColumn100",dataPoint:s[k],dataSeries:x,point:{x:l+(v-l)/2,y:0<=s[k].y?p:I},direction:0>s[k].y===a.axisY.reversed?1:-1,bounds:{x1:l,y1:Math.min(p,I),x2:v,y2:Math.max(p,I)},color:b})}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,
this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.yScaleAnimation,easingFunction:N.easing.easeOutQuart,animationBase:q<a.axisY.bounds.y1?a.axisY.bounds.y1:q>a.axisY.bounds.y2?a.axisY.bounds.y2:q}}};n.prototype.renderBar=
function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=0,r,u,h,k=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),g=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,l=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:Math.min(0.15*this.height,0.9*(this.plotArea.height/a.plotType.totalDataSeries))<<
0,p=a.axisX.dataInfo.minDiff;isFinite(p)||(p=0.3*Math.abs(a.axisX.range));p=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.height*(a.axisX.logarithmic?Math.log(p)/Math.log(a.axisX.range):Math.abs(p)/Math.abs(a.axisX.range))/a.plotType.totalDataSeries)<<0;this.dataPointMaxWidth&&g>l&&(g=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,l));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&l<g)&&(l=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,g));p<g&&(p=
g);p>l&&(p=l);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(l=0;l<a.dataSeriesIndexes.length;l++){var q=a.dataSeriesIndexes[l],f=this.data[q],m=f.dataPoints;if(0<m.length){var n=5<p&&f.bevelEnabled?!0:!1;c.strokeStyle="#4572A7 ";for(g=0;g<m.length;g++)if(m[g].getTime?h=m[g].x.getTime():h=m[g].x,!(h<
a.axisX.dataInfo.viewPortMin||h>a.axisX.dataInfo.viewPortMax)&&"number"===typeof m[g].y){u=a.axisX.convertValueToPixel(h);r=a.axisY.convertValueToPixel(m[g].y);u=a.axisX.reversed?u+a.plotType.totalDataSeries*p/2-(a.previousDataSeriesCount+l)*p<<0:u-a.plotType.totalDataSeries*p/2+(a.previousDataSeriesCount+l)*p<<0;var x=a.axisX.reversed?u-p<<0:u+p<<0,s;0<=m[g].y?s=k:(s=r,r=k);b=m[g].color?m[g].color:f._colorSet[g%f._colorSet.length];ba(c,a.axisY.reversed?r:s,a.axisX.reversed?x:u,a.axisY.reversed?s:
r,a.axisX.reversed?u:x,b,0,null,n,!1,!1,!1,f.fillOpacity);b=f.dataPointIds[g];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:q,dataPointIndex:g,x1:s,y1:u,x2:r,y2:x};b=Z(b);t&&ba(this._eventManager.ghostCtx,s,a.axisX.reversed?x:u,r,a.axisX.reversed?u:x,b,0,null,!1,!1,!1,!1);(m[g].indexLabel||f.indexLabel||m[g].indexLabelFormatter||f.indexLabelFormatter)&&this._indexLabels.push({chartType:"bar",dataPoint:m[g],dataSeries:f,point:{x:0<=m[g].y?r:s,y:u+(x-u)/2},direction:0>
m[g].y===a.axisY.reversed?1:-1,bounds:{x1:Math.min(s,r),y1:u,x2:Math.max(s,r),y2:x},color:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),
this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.xScaleAnimation,easingFunction:N.easing.easeOutQuart,animationBase:k<a.axisY.bounds.x1?a.axisY.bounds.x1:k>a.axisY.bounds.x2?a.axisY.bounds.x2:k}}};n.prototype.renderStackedBar=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=[],r=[],u=[],h=[],k=0,l,p,q=a.axisY.convertValueToPixel(a.axisY.logarithmic?
a.axisY.viewportMinimum:0),k=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,f=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.15*this.height<<0,m=a.axisX.dataInfo.minDiff;isFinite(m)||(m=0.3*Math.abs(a.axisX.range));m=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.height*(a.axisX.logarithmic?Math.log(m)/Math.log(a.axisX.range):Math.abs(m)/Math.abs(a.axisX.range))/a.plotType.plotUnits.length)<<
0;this.dataPointMaxWidth&&k>f&&(k=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,f));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&f<k)&&(f=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,k));m<k&&(m=k);m>f&&(m=f);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());
for(f=0;f<a.dataSeriesIndexes.length;f++){var n=a.dataSeriesIndexes[f],x=this.data[n],s=x.dataPoints;if(0<s.length){var y=5<m&&x.bevelEnabled?!0:!1;c.strokeStyle="#4572A7 ";for(k=0;k<s.length;k++)if(b=s[k].x.getTime?s[k].x.getTime():s[k].x,!(b<a.axisX.dataInfo.viewPortMin||b>a.axisX.dataInfo.viewPortMax)&&"number"===typeof s[k].y){p=a.axisX.convertValueToPixel(b);p=p-a.plotType.plotUnits.length*m/2+a.index*m<<0;var v=p+m<<0,I;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&
0<s[k].y)u[b]=s[k].y+(u[b]?u[b]:0),0<u[b]&&(I=g[b]?g[b]:q,g[b]=l=a.axisY.convertValueToPixel(u[b]));else if(a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&0>=s[k].y)h[b]=s[k].y+(h[b]?h[b]:0),l=r[b]?r[b]:q,r[b]=I=a.axisY.convertValueToPixel(h[b]);else if(l=a.axisY.convertValueToPixel(s[k].y),0<=s[k].y){var A=g[b]?g[b]:0;I=q+A;l+=A;g[b]=A+(l-I)}else A=r[b]?r[b]:0,I=l-A,l=q-A,r[b]=A+(l-I);b=s[k].color?s[k].color:x._colorSet[k%x._colorSet.length];ba(c,a.axisY.reversed?l:I,p,a.axisY.reversed?
I:l,v,b,0,null,y,!1,!1,!1,x.fillOpacity);b=x.dataPointIds[k];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:n,dataPointIndex:k,x1:I,y1:p,x2:l,y2:v};b=Z(b);t&&ba(this._eventManager.ghostCtx,I,p,l,v,b,0,null,!1,!1,!1,!1);(s[k].indexLabel||x.indexLabel||s[k].indexLabelFormatter||x.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedBar",dataPoint:s[k],dataSeries:x,point:{x:0<=s[k].y?l:I,y:p+(v-p)/2},direction:0>s[k].y===a.axisY.reversed?1:-1,bounds:{x1:Math.min(I,
l),y1:p,x2:Math.max(I,l),y2:v},color:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());c.restore();
return{source:d,dest:this.plotArea.ctx,animationCallback:N.xScaleAnimation,easingFunction:N.easing.easeOutQuart,animationBase:q<a.axisY.bounds.x1?a.axisY.bounds.x1:q>a.axisY.bounds.x2?a.axisY.bounds.x2:q}}};n.prototype.renderStackedBar100=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=[],r=[],u=[],h=[],k=0,l,p,q=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),k=this.options.dataPointMinWidth?
this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,f=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.15*this.height<<0,m=a.axisX.dataInfo.minDiff;isFinite(m)||(m=0.3*Math.abs(a.axisX.range));m=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.height*(a.axisX.logarithmic?Math.log(m)/Math.log(a.axisX.range):Math.abs(m)/Math.abs(a.axisX.range))/a.plotType.plotUnits.length)<<0;this.dataPointMaxWidth&&k>f&&(k=Math.min(this.options.dataPointWidth?
this.dataPointWidth:Infinity,f));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&f<k)&&(f=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,k));m<k&&(m=k);m>f&&(m=f);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(f=0;f<a.dataSeriesIndexes.length;f++){var n=a.dataSeriesIndexes[f],
x=this.data[n],s=x.dataPoints;if(0<s.length){var y=5<m&&x.bevelEnabled?!0:!1;c.strokeStyle="#4572A7 ";for(k=0;k<s.length;k++)if(b=s[k].x.getTime?s[k].x.getTime():s[k].x,!(b<a.axisX.dataInfo.viewPortMin||b>a.axisX.dataInfo.viewPortMax)&&"number"===typeof s[k].y){p=a.axisX.convertValueToPixel(b);var v;v=0!==a.dataPointYSums[b]?100*(s[k].y/a.dataPointYSums[b]):0;p=p-a.plotType.plotUnits.length*m/2+a.index*m<<0;var I=p+m<<0;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&
0<s[k].y){u[b]=v+(u[b]?u[b]:0);if(0>=u[b])continue;v=g[b]?g[b]:q;g[b]=l=a.axisY.convertValueToPixel(u[b])}else if(a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length&&0>=s[k].y)h[b]=v+(h[b]?h[b]:0),l=r[b]?r[b]:q,r[b]=v=a.axisY.convertValueToPixel(h[b]);else if(l=a.axisY.convertValueToPixel(v),0<=s[k].y){var A=g[b]?g[b]:0;v=q+A;l+=A;a.dataSeriesIndexes.length-1===f&&1>=Math.abs(e.x2-l)&&(l=e.x2);g[b]=A+(l-v)}else A=r[b]?r[b]:0,v=l-A,l=q-A,a.dataSeriesIndexes.length-1===f&&1>=Math.abs(e.x1-
v)&&(v=e.x1),r[b]=A+(l-v);b=s[k].color?s[k].color:x._colorSet[k%x._colorSet.length];ba(c,a.axisY.reversed?l:v,p,a.axisY.reversed?v:l,I,b,0,null,y,!1,!1,!1,x.fillOpacity);b=x.dataPointIds[k];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:n,dataPointIndex:k,x1:v,y1:p,x2:l,y2:I};b=Z(b);t&&ba(this._eventManager.ghostCtx,v,p,l,I,b,0,null,!1,!1,!1,!1);(s[k].indexLabel||x.indexLabel||s[k].indexLabelFormatter||x.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedBar100",
dataPoint:s[k],dataSeries:x,point:{x:0<=s[k].y?l:v,y:p+(I-p)/2},direction:0>s[k].y===a.axisY.reversed?1:-1,bounds:{x1:Math.min(v,l),y1:p,x2:Math.max(v,l),y2:I},color:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,
0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.xScaleAnimation,easingFunction:N.easing.easeOutQuart,animationBase:q<a.axisY.bounds.x1?a.axisY.bounds.x1:q>a.axisY.bounds.x2?a.axisY.bounds.x2:q}}};n.prototype.renderArea=function(a){var d,c;function b(){A&&(0<f.lineThickness&&g.stroke(),a.axisY.logarithmic||0>=a.axisY.viewportMinimum&&0<=a.axisY.viewportMaximum?I=v:0>a.axisY.viewportMaximum?
I=u.y1:0<a.axisY.viewportMinimum&&(I=v),g.lineTo(x,I),g.lineTo(A.x,I),g.closePath(),g.globalAlpha=f.fillOpacity,g.fill(),g.globalAlpha=1,t&&(r.lineTo(x,I),r.lineTo(A.x,I),r.closePath(),r.fill()),g.beginPath(),g.moveTo(x,s),r.beginPath(),r.moveTo(x,s),A={x:x,y:s})}var e=a.targetCanvasCtx||this.plotArea.ctx,g=t?this._preRenderCtx:e;if(!(0>=a.dataSeriesIndexes.length)){var r=this._eventManager.ghostCtx,u=a.axisY.lineCoordinates,h=[],k=this.plotArea,l;g.save();t&&r.save();g.beginPath();g.rect(k.x1,k.y1,
k.width,k.height);g.clip();t&&(r.beginPath(),r.rect(k.x1,k.y1,k.width,k.height),r.clip());for(var p=0;p<a.dataSeriesIndexes.length;p++){var q=a.dataSeriesIndexes[p],f=this.data[q],m=f.dataPoints,h=f.id;this._eventManager.objectMap[h]={objectType:"dataSeries",dataSeriesIndex:q};h=Z(h);r.fillStyle=h;h=[];d=!0;var n=0,x,s,y,v=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),I,A=null;if(0<m.length){var C=f._colorSet[n%f._colorSet.length],w=f.lineColor=f.options.lineColor||C,
z=w;g.fillStyle=C;g.strokeStyle=w;g.lineWidth=f.lineThickness;c="solid";if(g.setLineDash){var K=H(f.nullDataLineDashType,f.lineThickness);c=f.lineDashType;var T=H(c,f.lineThickness);g.setLineDash(T)}for(var ha=!0;n<m.length;n++)if(y=m[n].x.getTime?m[n].x.getTime():m[n].x,!(y<a.axisX.dataInfo.viewPortMin||y>a.axisX.dataInfo.viewPortMax&&(!f.connectNullData||!ha)))if("number"!==typeof m[n].y)f.connectNullData||(ha||d)||b(),ha=!0;else{x=a.axisX.convertValueToPixel(y);s=a.axisY.convertValueToPixel(m[n].y);
d||ha?(!d&&f.connectNullData?(g.setLineDash&&(f.options.nullDataLineDashType||c===f.lineDashType&&f.lineDashType!==f.nullDataLineDashType)&&(d=x,c=s,x=l.x,s=l.y,b(),g.moveTo(l.x,l.y),x=d,s=c,A=l,c=f.nullDataLineDashType,g.setLineDash(K)),g.lineTo(x,s),t&&r.lineTo(x,s)):(g.beginPath(),g.moveTo(x,s),t&&(r.beginPath(),r.moveTo(x,s)),A={x:x,y:s}),ha=d=!1):(g.lineTo(x,s),t&&r.lineTo(x,s),0==n%250&&b());l={x:x,y:s};n<m.length-1&&(z!==(m[n].lineColor||w)||c!==(m[n].lineDashType||f.lineDashType))&&(b(),z=
m[n].lineColor||w,g.strokeStyle=z,g.setLineDash&&(m[n].lineDashType?(c=m[n].lineDashType,g.setLineDash(H(c,f.lineThickness))):(c=f.lineDashType,g.setLineDash(T))));var ca=f.dataPointIds[n];this._eventManager.objectMap[ca]={id:ca,objectType:"dataPoint",dataSeriesIndex:q,dataPointIndex:n,x1:x,y1:s};0!==m[n].markerSize&&(0<m[n].markerSize||0<f.markerSize)&&(y=f.getMarkerProperties(n,x,s,g),h.push(y),ca=Z(ca),t&&h.push({x:x,y:s,ctx:r,type:y.type,size:y.size,color:ca,borderColor:ca,borderThickness:y.borderThickness}));
(m[n].indexLabel||f.indexLabel||m[n].indexLabelFormatter||f.indexLabelFormatter)&&this._indexLabels.push({chartType:"area",dataPoint:m[n],dataSeries:f,point:{x:x,y:s},direction:0>m[n].y===a.axisY.reversed?1:-1,color:C})}b();X.drawMarkers(h)}}t&&(e.drawImage(this._preRenderCanvas,0,0,this.width,this.height),g.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&g.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&g.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),
this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),g.clearRect(k.x1,k.y1,k.width,k.height),this._eventManager.ghostCtx.restore());g.restore();return{source:e,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderSplineArea=function(a){function d(){var c=w(y,2);if(0<c.length){if(0<l.lineThickness){b.beginPath();b.moveTo(c[0].x,c[0].y);c[0].newStrokeStyle&&(b.strokeStyle=c[0].newStrokeStyle);
c[0].newLineDashArray&&b.setLineDash(c[0].newLineDashArray);for(var d=0;d<c.length-3;d+=3)if(b.bezierCurveTo(c[d+1].x,c[d+1].y,c[d+2].x,c[d+2].y,c[d+3].x,c[d+3].y),t&&e.bezierCurveTo(c[d+1].x,c[d+1].y,c[d+2].x,c[d+2].y,c[d+3].x,c[d+3].y),c[d+3].newStrokeStyle||c[d+3].newLineDashArray)b.stroke(),b.beginPath(),b.moveTo(c[d+3].x,c[d+3].y),c[d+3].newStrokeStyle&&(b.strokeStyle=c[d+3].newStrokeStyle),c[d+3].newLineDashArray&&b.setLineDash(c[d+3].newLineDashArray);b.stroke()}b.beginPath();b.moveTo(c[0].x,
c[0].y);t&&(e.beginPath(),e.moveTo(c[0].x,c[0].y));for(d=0;d<c.length-3;d+=3)b.bezierCurveTo(c[d+1].x,c[d+1].y,c[d+2].x,c[d+2].y,c[d+3].x,c[d+3].y),t&&e.bezierCurveTo(c[d+1].x,c[d+1].y,c[d+2].x,c[d+2].y,c[d+3].x,c[d+3].y);a.axisY.logarithmic||0>=a.axisY.viewportMinimum&&0<=a.axisY.viewportMaximum?x=n:0>a.axisY.viewportMaximum?x=g.y1:0<a.axisY.viewportMinimum&&(x=n);s={x:c[0].x,y:c[0].y};b.lineTo(c[c.length-1].x,x);b.lineTo(s.x,x);b.closePath();b.globalAlpha=l.fillOpacity;b.fill();b.globalAlpha=1;
t&&(e.lineTo(c[c.length-1].x,x),e.lineTo(s.x,x),e.closePath(),e.fill())}}var c=a.targetCanvasCtx||this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=this._eventManager.ghostCtx,g=a.axisY.lineCoordinates,r=[],u=this.plotArea;b.save();t&&e.save();b.beginPath();b.rect(u.x1,u.y1,u.width,u.height);b.clip();t&&(e.beginPath(),e.rect(u.x1,u.y1,u.width,u.height),e.clip());for(var h=0;h<a.dataSeriesIndexes.length;h++){var k=a.dataSeriesIndexes[h],l=this.data[k],p=l.dataPoints,
r=l.id;this._eventManager.objectMap[r]={objectType:"dataSeries",dataSeriesIndex:k};r=Z(r);e.fillStyle=r;var r=[],q=0,f,m,n=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),x,s=null,y=[];if(0<p.length){var v=l._colorSet[q%l._colorSet.length],I=l.lineColor=l.options.lineColor||v,A=I;b.fillStyle=v;b.strokeStyle=I;b.lineWidth=l.lineThickness;var C="solid";if(b.setLineDash){var z=H(l.nullDataLineDashType,l.lineThickness),C=l.lineDashType,D=H(C,l.lineThickness);b.setLineDash(D)}for(m=
!1;q<p.length;q++)if(f=p[q].x.getTime?p[q].x.getTime():p[q].x,!(f<a.axisX.dataInfo.viewPortMin||f>a.axisX.dataInfo.viewPortMax&&(!l.connectNullData||!m)))if("number"!==typeof p[q].y)0<q&&!m&&(l.connectNullData?b.setLineDash&&(0<y.length&&(l.options.nullDataLineDashType||!p[q-1].lineDashType))&&(y[y.length-1].newLineDashArray=z,C=l.nullDataLineDashType):(d(),y=[])),m=!0;else{f=a.axisX.convertValueToPixel(f);m=a.axisY.convertValueToPixel(p[q].y);var K=l.dataPointIds[q];this._eventManager.objectMap[K]=
{id:K,objectType:"dataPoint",dataSeriesIndex:k,dataPointIndex:q,x1:f,y1:m};y[y.length]={x:f,y:m};q<p.length-1&&(A!==(p[q].lineColor||I)||C!==(p[q].lineDashType||l.lineDashType))&&(A=p[q].lineColor||I,y[y.length-1].newStrokeStyle=A,b.setLineDash&&(p[q].lineDashType?(C=p[q].lineDashType,y[y.length-1].newLineDashArray=H(C,l.lineThickness)):(C=l.lineDashType,y[y.length-1].newLineDashArray=D)));if(0!==p[q].markerSize&&(0<p[q].markerSize||0<l.markerSize)){var T=l.getMarkerProperties(q,f,m,b);r.push(T);
K=Z(K);t&&r.push({x:f,y:m,ctx:e,type:T.type,size:T.size,color:K,borderColor:K,borderThickness:T.borderThickness})}(p[q].indexLabel||l.indexLabel||p[q].indexLabelFormatter||l.indexLabelFormatter)&&this._indexLabels.push({chartType:"splineArea",dataPoint:p[q],dataSeries:l,point:{x:f,y:m},direction:0>p[q].y===a.axisY.reversed?1:-1,color:v});m=!1}d();X.drawMarkers(r)}}t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,
0,0,this.width,this.height),a.axisY.maskCanvas&&b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.clearRect(u.x1,u.y1,u.width,u.height),this._eventManager.ghostCtx.restore());b.restore();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderStepArea=function(a){var d,c;function b(){A&&(0<f.lineThickness&&
g.stroke(),a.axisY.logarithmic||0>=a.axisY.viewportMinimum&&0<=a.axisY.viewportMaximum?I=v:0>a.axisY.viewportMaximum?I=u.y1:0<a.axisY.viewportMinimum&&(I=v),g.lineTo(x,I),g.lineTo(A.x,I),g.closePath(),g.globalAlpha=f.fillOpacity,g.fill(),g.globalAlpha=1,t&&(r.lineTo(x,I),r.lineTo(A.x,I),r.closePath(),r.fill()),g.beginPath(),g.moveTo(x,s),r.beginPath(),r.moveTo(x,s),A={x:x,y:s})}var e=a.targetCanvasCtx||this.plotArea.ctx,g=t?this._preRenderCtx:e;if(!(0>=a.dataSeriesIndexes.length)){var r=this._eventManager.ghostCtx,
u=a.axisY.lineCoordinates,h=[],k=this.plotArea,l;g.save();t&&r.save();g.beginPath();g.rect(k.x1,k.y1,k.width,k.height);g.clip();t&&(r.beginPath(),r.rect(k.x1,k.y1,k.width,k.height),r.clip());for(var p=0;p<a.dataSeriesIndexes.length;p++){var q=a.dataSeriesIndexes[p],f=this.data[q],m=f.dataPoints,h=f.id;this._eventManager.objectMap[h]={objectType:"dataSeries",dataSeriesIndex:q};h=Z(h);r.fillStyle=h;h=[];d=!0;var n=0,x,s,y,v=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),
I,A=null;c=!1;if(0<m.length){var C=f._colorSet[n%f._colorSet.length],w=f.lineColor=f.options.lineColor||C,z=w;g.fillStyle=C;g.strokeStyle=w;g.lineWidth=f.lineThickness;var K="solid";if(g.setLineDash){var T=H(f.nullDataLineDashType,f.lineThickness),K=f.lineDashType,D=H(K,f.lineThickness);g.setLineDash(D)}for(;n<m.length;n++)if(y=m[n].x.getTime?m[n].x.getTime():m[n].x,!(y<a.axisX.dataInfo.viewPortMin||y>a.axisX.dataInfo.viewPortMax&&(!f.connectNullData||!c))){var ca=s;"number"!==typeof m[n].y?(f.connectNullData||
(c||d)||b(),c=!0):(x=a.axisX.convertValueToPixel(y),s=a.axisY.convertValueToPixel(m[n].y),d||c?(!d&&f.connectNullData?(g.setLineDash&&(f.options.nullDataLineDashType||K===f.lineDashType&&f.lineDashType!==f.nullDataLineDashType)&&(d=x,c=s,x=l.x,s=l.y,b(),g.moveTo(l.x,l.y),x=d,s=c,A=l,K=f.nullDataLineDashType,g.setLineDash(T)),g.lineTo(x,ca),g.lineTo(x,s),t&&(r.lineTo(x,ca),r.lineTo(x,s))):(g.beginPath(),g.moveTo(x,s),t&&(r.beginPath(),r.moveTo(x,s)),A={x:x,y:s}),c=d=!1):(g.lineTo(x,ca),t&&r.lineTo(x,
ca),g.lineTo(x,s),t&&r.lineTo(x,s),0==n%250&&b()),l={x:x,y:s},n<m.length-1&&(z!==(m[n].lineColor||w)||K!==(m[n].lineDashType||f.lineDashType))&&(b(),z=m[n].lineColor||w,g.strokeStyle=z,g.setLineDash&&(m[n].lineDashType?(K=m[n].lineDashType,g.setLineDash(H(K,f.lineThickness))):(K=f.lineDashType,g.setLineDash(D)))),y=f.dataPointIds[n],this._eventManager.objectMap[y]={id:y,objectType:"dataPoint",dataSeriesIndex:q,dataPointIndex:n,x1:x,y1:s},0!==m[n].markerSize&&(0<m[n].markerSize||0<f.markerSize)&&(ca=
f.getMarkerProperties(n,x,s,g),h.push(ca),y=Z(y),t&&h.push({x:x,y:s,ctx:r,type:ca.type,size:ca.size,color:y,borderColor:y,borderThickness:ca.borderThickness})),(m[n].indexLabel||f.indexLabel||m[n].indexLabelFormatter||f.indexLabelFormatter)&&this._indexLabels.push({chartType:"stepArea",dataPoint:m[n],dataSeries:f,point:{x:x,y:s},direction:0>m[n].y===a.axisY.reversed?1:-1,color:C}))}b();X.drawMarkers(h)}}t&&(e.drawImage(this._preRenderCanvas,0,0,this.width,this.height),g.globalCompositeOperation="source-atop",
a.axisX.maskCanvas&&g.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&g.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),g.clearRect(k.x1,k.y1,k.width,k.height),this._eventManager.ghostCtx.restore());g.restore();return{source:e,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderStackedArea=
function(a){function d(){if(!(1>k.length)){for(0<C.lineThickness&&b.stroke();0<k.length;){var a=k.pop();b.lineTo(a.x,a.y);t&&x.lineTo(a.x,a.y)}b.closePath();b.globalAlpha=C.fillOpacity;b.fill();b.globalAlpha=1;b.beginPath();t&&(x.closePath(),x.fill(),x.beginPath());k=[]}}var c=a.targetCanvasCtx||this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=null,g=null,r=[],u=this.plotArea,h=[],k=[],l=[],p=[],q=0,f,m,n=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:
0),x=this._eventManager.ghostCtx,s,y,v;t&&x.beginPath();b.save();t&&x.save();b.beginPath();b.rect(u.x1,u.y1,u.width,u.height);b.clip();t&&(x.beginPath(),x.rect(u.x1,u.y1,u.width,u.height),x.clip());for(var e=[],I=0;I<a.dataSeriesIndexes.length;I++){var A=a.dataSeriesIndexes[I],C=this.data[A],w=C.dataPoints;C.dataPointIndexes=[];for(q=0;q<w.length;q++)A=w[q].x.getTime?w[q].x.getTime():w[q].x,C.dataPointIndexes[A]=q,e[A]||(l.push(A),e[A]=!0);l.sort(Ra)}for(I=0;I<a.dataSeriesIndexes.length;I++){A=a.dataSeriesIndexes[I];
C=this.data[A];w=C.dataPoints;y=!0;k=[];q=C.id;this._eventManager.objectMap[q]={objectType:"dataSeries",dataSeriesIndex:A};q=Z(q);x.fillStyle=q;if(0<l.length){var e=C._colorSet[0],z=C.lineColor=C.options.lineColor||e,K=z;b.fillStyle=e;b.strokeStyle=z;b.lineWidth=C.lineThickness;v="solid";if(b.setLineDash){var T=H(C.nullDataLineDashType,C.lineThickness);v=C.lineDashType;var D=H(v,C.lineThickness);b.setLineDash(D)}for(var ca=!0,q=0;q<l.length;q++){var g=l[q],ia=null,ia=0<=C.dataPointIndexes[g]?w[C.dataPointIndexes[g]]:
{x:g,y:null};if(!(g<a.axisX.dataInfo.viewPortMin||g>a.axisX.dataInfo.viewPortMax&&(!C.connectNullData||!ca)))if("number"!==typeof ia.y)C.connectNullData||(ca||y)||d(),ca=!0;else{f=a.axisX.convertValueToPixel(g);var pa=h[g]?h[g]:0;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length){p[g]=ia.y+(p[g]?p[g]:0);if(0>=p[g]&&a.axisY.logarithmic)continue;m=a.axisY.convertValueToPixel(p[g])}else m=a.axisY.convertValueToPixel(ia.y),m-=pa;k.push({x:f,y:n-pa});h[g]=n-m;y||
ca?(!y&&C.connectNullData?(b.setLineDash&&(C.options.nullDataLineDashType||v===C.lineDashType&&C.lineDashType!==C.nullDataLineDashType)&&(y=k.pop(),v=k[k.length-1],d(),b.moveTo(s.x,s.y),k.push(v),k.push(y),v=C.nullDataLineDashType,b.setLineDash(T)),b.lineTo(f,m),t&&x.lineTo(f,m)):(b.beginPath(),b.moveTo(f,m),t&&(x.beginPath(),x.moveTo(f,m))),ca=y=!1):(b.lineTo(f,m),t&&x.lineTo(f,m),0==q%250&&(d(),b.moveTo(f,m),t&&x.moveTo(f,m),k.push({x:f,y:n-pa})));s={x:f,y:m};q<w.length-1&&(K!==(w[q].lineColor||
z)||v!==(w[q].lineDashType||C.lineDashType))&&(d(),b.beginPath(),b.moveTo(f,m),k.push({x:f,y:n-pa}),K=w[q].lineColor||z,b.strokeStyle=K,b.setLineDash&&(w[q].lineDashType?(v=w[q].lineDashType,b.setLineDash(H(v,C.lineThickness))):(v=C.lineDashType,b.setLineDash(D))));if(0<=C.dataPointIndexes[g]){var ma=C.dataPointIds[C.dataPointIndexes[g]];this._eventManager.objectMap[ma]={id:ma,objectType:"dataPoint",dataSeriesIndex:A,dataPointIndex:C.dataPointIndexes[g],x1:f,y1:m}}0<=C.dataPointIndexes[g]&&0!==ia.markerSize&&
(0<ia.markerSize||0<C.markerSize)&&(pa=C.getMarkerProperties(C.dataPointIndexes[g],f,m,b),r.push(pa),g=Z(ma),t&&r.push({x:f,y:m,ctx:x,type:pa.type,size:pa.size,color:g,borderColor:g,borderThickness:pa.borderThickness}));(ia.indexLabel||C.indexLabel||ia.indexLabelFormatter||C.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedArea",dataPoint:ia,dataSeries:C,point:{x:f,y:m},direction:0>w[q].y===a.axisY.reversed?1:-1,color:e})}}d();b.moveTo(f,m);t&&x.moveTo(f,m)}delete C.dataPointIndexes}X.drawMarkers(r);
t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.clearRect(u.x1,u.y1,u.width,u.height),x.restore());b.restore();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,
easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderStackedArea100=function(a){function d(){for(0<C.lineThickness&&b.stroke();0<k.length;){var a=k.pop();b.lineTo(a.x,a.y);t&&v.lineTo(a.x,a.y)}b.closePath();b.globalAlpha=C.fillOpacity;b.fill();b.globalAlpha=1;b.beginPath();t&&(v.closePath(),v.fill(),v.beginPath());k=[]}var c=a.targetCanvasCtx||this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=null,g=null,r=this.plotArea,u=[],m=[],k=[],l=[],p=[],q=
0,f,h,n,x,s,y=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),v=this._eventManager.ghostCtx;b.save();t&&v.save();b.beginPath();b.rect(r.x1,r.y1,r.width,r.height);b.clip();t&&(v.beginPath(),v.rect(r.x1,r.y1,r.width,r.height),v.clip());for(var e=[],I=0;I<a.dataSeriesIndexes.length;I++){var A=a.dataSeriesIndexes[I],C=this.data[A],w=C.dataPoints;C.dataPointIndexes=[];for(q=0;q<w.length;q++)A=w[q].x.getTime?w[q].x.getTime():w[q].x,C.dataPointIndexes[A]=q,e[A]||(l.push(A),e[A]=
!0);l.sort(Ra)}for(I=0;I<a.dataSeriesIndexes.length;I++){A=a.dataSeriesIndexes[I];C=this.data[A];w=C.dataPoints;x=!0;e=C.id;this._eventManager.objectMap[e]={objectType:"dataSeries",dataSeriesIndex:A};e=Z(e);v.fillStyle=e;k=[];if(0<l.length){var e=C._colorSet[q%C._colorSet.length],z=C.lineColor=C.options.lineColor||e,K=z;b.fillStyle=e;b.strokeStyle=z;b.lineWidth=C.lineThickness;s="solid";if(b.setLineDash){var T=H(C.nullDataLineDashType,C.lineThickness);s=C.lineDashType;var D=H(s,C.lineThickness);b.setLineDash(D)}for(var ca=
!0,q=0;q<l.length;q++){var g=l[q],ia=null,ia=0<=C.dataPointIndexes[g]?w[C.dataPointIndexes[g]]:{x:g,y:null};if(!(g<a.axisX.dataInfo.viewPortMin||g>a.axisX.dataInfo.viewPortMax&&(!C.connectNullData||!ca)))if("number"!==typeof ia.y)C.connectNullData||(ca||x)||d(),ca=!0;else{var pa;pa=0!==a.dataPointYSums[g]?100*(ia.y/a.dataPointYSums[g]):0;f=a.axisX.convertValueToPixel(g);var ma=m[g]?m[g]:0;if(a.axisY.logarithmic||a.axisY.scaleBreaks&&0<a.axisY.scaleBreaks._appliedBreaks.length){p[g]=pa+(p[g]?p[g]:
0);if(0>=p[g]&&a.axisY.logarithmic)continue;h=a.axisY.convertValueToPixel(p[g])}else h=a.axisY.convertValueToPixel(pa),h-=ma;k.push({x:f,y:y-ma});m[g]=y-h;x||ca?(!x&&C.connectNullData?(b.setLineDash&&(C.options.nullDataLineDashType||s===C.lineDashType&&C.lineDashType!==C.nullDataLineDashType)&&(x=k.pop(),s=k[k.length-1],d(),b.moveTo(n.x,n.y),k.push(s),k.push(x),s=C.nullDataLineDashType,b.setLineDash(T)),b.lineTo(f,h),t&&v.lineTo(f,h)):(b.beginPath(),b.moveTo(f,h),t&&(v.beginPath(),v.moveTo(f,h))),
ca=x=!1):(b.lineTo(f,h),t&&v.lineTo(f,h),0==q%250&&(d(),b.moveTo(f,h),t&&v.moveTo(f,h),k.push({x:f,y:y-ma})));n={x:f,y:h};q<w.length-1&&(K!==(w[q].lineColor||z)||s!==(w[q].lineDashType||C.lineDashType))&&(d(),b.beginPath(),b.moveTo(f,h),k.push({x:f,y:y-ma}),K=w[q].lineColor||z,b.strokeStyle=K,b.setLineDash&&(w[q].lineDashType?(s=w[q].lineDashType,b.setLineDash(H(s,C.lineThickness))):(s=C.lineDashType,b.setLineDash(D))));if(0<=C.dataPointIndexes[g]){var F=C.dataPointIds[C.dataPointIndexes[g]];this._eventManager.objectMap[F]=
{id:F,objectType:"dataPoint",dataSeriesIndex:A,dataPointIndex:C.dataPointIndexes[g],x1:f,y1:h}}0<=C.dataPointIndexes[g]&&0!==ia.markerSize&&(0<ia.markerSize||0<C.markerSize)&&(ma=C.getMarkerProperties(q,f,h,b),u.push(ma),g=Z(F),t&&u.push({x:f,y:h,ctx:v,type:ma.type,size:ma.size,color:g,borderColor:g,borderThickness:ma.borderThickness}));(ia.indexLabel||C.indexLabel||ia.indexLabelFormatter||C.indexLabelFormatter)&&this._indexLabels.push({chartType:"stackedArea100",dataPoint:ia,dataSeries:C,point:{x:f,
y:h},direction:0>w[q].y===a.axisY.reversed?1:-1,color:e})}}d();b.moveTo(f,h);t&&v.moveTo(f,h)}delete C.dataPointIndexes}X.drawMarkers(u);t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),
b.clearRect(r.x1,r.y1,r.width,r.height),v.restore());b.restore();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderBubble=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=this.plotArea,e=0,g,r;c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(b.x1,b.y1,b.width,b.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),
this._eventManager.ghostCtx.rect(b.x1,b.y1,b.width,b.height),this._eventManager.ghostCtx.clip());for(var u=-Infinity,h=Infinity,k=0;k<a.dataSeriesIndexes.length;k++)for(var l=a.dataSeriesIndexes[k],p=this.data[l],q=p.dataPoints,f=0,e=0;e<q.length;e++)g=q[e].getTime?g=q[e].x.getTime():g=q[e].x,g<a.axisX.dataInfo.viewPortMin||g>a.axisX.dataInfo.viewPortMax||"undefined"===typeof q[e].z||(f=q[e].z,f>u&&(u=f),f<h&&(h=f));for(var m=25*Math.PI,n=Math.max(Math.pow(0.25*Math.min(b.height,b.width)/2,2)*Math.PI,
m),k=0;k<a.dataSeriesIndexes.length;k++)if(l=a.dataSeriesIndexes[k],p=this.data[l],q=p.dataPoints,0<q.length)for(c.strokeStyle="#4572A7 ",e=0;e<q.length;e++)if(g=q[e].getTime?g=q[e].x.getTime():g=q[e].x,!(g<a.axisX.dataInfo.viewPortMin||g>a.axisX.dataInfo.viewPortMax)&&"number"===typeof q[e].y){g=a.axisX.convertValueToPixel(g);r=a.axisY.convertValueToPixel(q[e].y);var f=q[e].z,x=2*Math.max(Math.sqrt((u===h?n/2:m+(n-m)/(u-h)*(f-h))/Math.PI)<<0,1),f=p.getMarkerProperties(e,c);f.size=x;c.globalAlpha=
p.fillOpacity;X.drawMarker(g,r,c,f.type,f.size,f.color,f.borderColor,f.borderThickness);c.globalAlpha=1;var s=p.dataPointIds[e];this._eventManager.objectMap[s]={id:s,objectType:"dataPoint",dataSeriesIndex:l,dataPointIndex:e,x1:g,y1:r,size:x};x=Z(s);t&&X.drawMarker(g,r,this._eventManager.ghostCtx,f.type,f.size,x,x,f.borderThickness);(q[e].indexLabel||p.indexLabel||q[e].indexLabelFormatter||p.indexLabelFormatter)&&this._indexLabels.push({chartType:"bubble",dataPoint:q[e],dataSeries:p,point:{x:g,y:r},
direction:1,bounds:{x1:g-f.size/2,y1:r-f.size/2,x2:g+f.size/2,y2:r+f.size/2},color:null})}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(b.x1,b.y1,b.width,b.height),this._eventManager.ghostCtx.restore());
c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};n.prototype.renderScatter=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=this.plotArea,e=0,g,r;c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(b.x1,b.y1,b.width,b.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(b.x1,b.y1,
b.width,b.height),this._eventManager.ghostCtx.clip());for(var u=0;u<a.dataSeriesIndexes.length;u++){var h=a.dataSeriesIndexes[u],k=this.data[h],l=k.dataPoints;if(0<l.length){c.strokeStyle="#4572A7 ";Math.pow(0.3*Math.min(b.height,b.width)/2,2);for(var p=0,q=0,e=0;e<l.length;e++)if(g=l[e].getTime?g=l[e].x.getTime():g=l[e].x,!(g<a.axisX.dataInfo.viewPortMin||g>a.axisX.dataInfo.viewPortMax)&&"number"===typeof l[e].y){g=a.axisX.convertValueToPixel(g);r=a.axisY.convertValueToPixel(l[e].y);var f=k.getMarkerProperties(e,
g,r,c);c.globalAlpha=k.fillOpacity;X.drawMarker(f.x,f.y,f.ctx,f.type,f.size,f.color,f.borderColor,f.borderThickness);c.globalAlpha=1;Math.sqrt((p-g)*(p-g)+(q-r)*(q-r))<Math.min(f.size,5)&&l.length>Math.min(this.plotArea.width,this.plotArea.height)||(p=k.dataPointIds[e],this._eventManager.objectMap[p]={id:p,objectType:"dataPoint",dataSeriesIndex:h,dataPointIndex:e,x1:g,y1:r},p=Z(p),t&&X.drawMarker(f.x,f.y,this._eventManager.ghostCtx,f.type,f.size,p,p,f.borderThickness),(l[e].indexLabel||k.indexLabel||
l[e].indexLabelFormatter||k.indexLabelFormatter)&&this._indexLabels.push({chartType:"scatter",dataPoint:l[e],dataSeries:k,point:{x:g,y:r},direction:1,bounds:{x1:g-f.size/2,y1:r-f.size/2,x2:g+f.size/2,y2:r+f.size/2},color:null}),p=g,q=r)}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),
this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(b.x1,b.y1,b.width,b.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};n.prototype.renderCandlestick=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d,b=this._eventManager.ghostCtx;if(!(0>=a.dataSeriesIndexes.length)){var e=
null,g=null,r=this.plotArea,u=0,h,k,l,p,q,f,e=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,g=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.015*this.width,n=a.axisX.dataInfo.minDiff;isFinite(n)||(n=0.3*Math.abs(a.axisX.range));n=this.options.dataPointWidth?this.dataPointWidth:0.7*r.width*(a.axisX.logarithmic?Math.log(n)/Math.log(a.axisX.range):Math.abs(n)/Math.abs(a.axisX.range))<<0;
this.dataPointMaxWidth&&e>g&&(e=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,g));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&g<e)&&(g=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,e));n<e&&(n=e);n>g&&(n=g);c.save();t&&b.save();c.beginPath();c.rect(r.x1,r.y1,r.width,r.height);c.clip();t&&(b.beginPath(),b.rect(r.x1,r.y1,r.width,r.height),b.clip());for(var J=0;J<a.dataSeriesIndexes.length;J++){var x=a.dataSeriesIndexes[J],s=this.data[x],y=s.dataPoints;
if(0<y.length)for(var v=5<n&&s.bevelEnabled?!0:!1,u=0;u<y.length;u++)if(y[u].getTime?f=y[u].x.getTime():f=y[u].x,!(f<a.axisX.dataInfo.viewPortMin||f>a.axisX.dataInfo.viewPortMax)&&!m(y[u].y)&&y[u].y.length&&"number"===typeof y[u].y[0]&&"number"===typeof y[u].y[1]&&"number"===typeof y[u].y[2]&&"number"===typeof y[u].y[3]){h=a.axisX.convertValueToPixel(f);k=a.axisY.convertValueToPixel(y[u].y[0]);l=a.axisY.convertValueToPixel(y[u].y[1]);p=a.axisY.convertValueToPixel(y[u].y[2]);q=a.axisY.convertValueToPixel(y[u].y[3]);
var w=h-n/2<<0,A=w+n<<0,g=s.options.fallingColor?s.fallingColor:s._colorSet[0],e=y[u].color?y[u].color:s._colorSet[0],C=Math.round(Math.max(1,0.15*n)),z=0===C%2?0:0.5,D=s.dataPointIds[u];this._eventManager.objectMap[D]={id:D,objectType:"dataPoint",dataSeriesIndex:x,dataPointIndex:u,x1:w,y1:k,x2:A,y2:l,x3:h,y3:p,x4:h,y4:q,borderThickness:C,color:e};c.strokeStyle=e;c.beginPath();c.lineWidth=C;b.lineWidth=Math.max(C,4);"candlestick"===s.type?(c.moveTo(h-z,l),c.lineTo(h-z,Math.min(k,q)),c.stroke(),c.moveTo(h-
z,Math.max(k,q)),c.lineTo(h-z,p),c.stroke(),ba(c,w,Math.min(k,q),A,Math.max(k,q),y[u].y[0]<=y[u].y[3]?s.risingColor:g,C,e,v,v,!1,!1,s.fillOpacity),t&&(e=Z(D),b.strokeStyle=e,b.moveTo(h-z,l),b.lineTo(h-z,Math.min(k,q)),b.stroke(),b.moveTo(h-z,Math.max(k,q)),b.lineTo(h-z,p),b.stroke(),ba(b,w,Math.min(k,q),A,Math.max(k,q),e,0,null,!1,!1,!1,!1))):"ohlc"===s.type&&(c.moveTo(h-z,l),c.lineTo(h-z,p),c.stroke(),c.beginPath(),c.moveTo(h,k),c.lineTo(w,k),c.stroke(),c.beginPath(),c.moveTo(h,q),c.lineTo(A,q),
c.stroke(),t&&(e=Z(D),b.strokeStyle=e,b.moveTo(h-z,l),b.lineTo(h-z,p),b.stroke(),b.beginPath(),b.moveTo(h,k),b.lineTo(w,k),b.stroke(),b.beginPath(),b.moveTo(h,q),b.lineTo(A,q),b.stroke()));(y[u].indexLabel||s.indexLabel||y[u].indexLabelFormatter||s.indexLabelFormatter)&&this._indexLabels.push({chartType:s.type,dataPoint:y[u],dataSeries:s,point:{x:w+(A-w)/2,y:a.axisY.reversed?p:l},direction:1,bounds:{x1:w,y1:Math.min(l,p),x2:A,y2:Math.max(l,p)},color:e})}}t&&(d.drawImage(this._preRenderCanvas,0,0,
this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(r.x1,r.y1,r.width,r.height),b.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,
animationBase:0}}};n.prototype.renderBoxAndWhisker=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d,b=this._eventManager.ghostCtx;if(!(0>=a.dataSeriesIndexes.length)){var e=null,g=this.plotArea,r=0,u,h,k,l,p,q,f,e=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1,r=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.015*this.width,n=a.axisX.dataInfo.minDiff;isFinite(n)||
(n=0.3*Math.abs(a.axisX.range));n=this.options.dataPointWidth?this.dataPointWidth:0.7*g.width*(a.axisX.logarithmic?Math.log(n)/Math.log(a.axisX.range):Math.abs(n)/Math.abs(a.axisX.range))<<0;this.dataPointMaxWidth&&e>r&&(e=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,r));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&r<e)&&(r=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,e));n<e&&(n=e);n>r&&(n=r);c.save();t&&b.save();c.beginPath();c.rect(g.x1,g.y1,g.width,
g.height);c.clip();t&&(b.beginPath(),b.rect(g.x1,g.y1,g.width,g.height),b.clip());for(var J=!1,J=!!a.axisY.reversed,x=0;x<a.dataSeriesIndexes.length;x++){var s=a.dataSeriesIndexes[x],y=this.data[s],v=y.dataPoints;if(0<v.length)for(var w=5<n&&y.bevelEnabled?!0:!1,r=0;r<v.length;r++)if(v[r].getTime?f=v[r].x.getTime():f=v[r].x,!(f<a.axisX.dataInfo.viewPortMin||f>a.axisX.dataInfo.viewPortMax)&&!m(v[r].y)&&v[r].y.length&&"number"===typeof v[r].y[0]&&"number"===typeof v[r].y[1]&&"number"===typeof v[r].y[2]&&
"number"===typeof v[r].y[3]&&"number"===typeof v[r].y[4]&&5===v[r].y.length){u=a.axisX.convertValueToPixel(f);h=a.axisY.convertValueToPixel(v[r].y[0]);k=a.axisY.convertValueToPixel(v[r].y[1]);l=a.axisY.convertValueToPixel(v[r].y[2]);p=a.axisY.convertValueToPixel(v[r].y[3]);q=a.axisY.convertValueToPixel(v[r].y[4]);var A=u-n/2<<0,C=u+n/2<<0,e=v[r].color?v[r].color:y._colorSet[0],z=Math.round(Math.max(1,0.15*n)),D=0===z%2?0:0.5,K=v[r].whiskerColor?v[r].whiskerColor:v[r].color?y.whiskerColor?y.whiskerColor:
v[r].color:y.whiskerColor?y.whiskerColor:e,T="number"===typeof v[r].whiskerThickness?v[r].whiskerThickness:"number"===typeof y.options.whiskerThickness?y.whiskerThickness:z,ha=v[r].whiskerDashType?v[r].whiskerDashType:y.whiskerDashType,ca=m(v[r].whiskerLength)?m(y.options.whiskerLength)?n:y.whiskerLength:v[r].whiskerLength,ca="number"===typeof ca?0>=ca?0:ca>=n?n:ca:"string"===typeof ca?parseInt(ca)*n/100>n?n:parseInt(ca)*n/100:n,ia=1===Math.round(T)%2?0.5:0,pa=v[r].stemColor?v[r].stemColor:v[r].color?
y.stemColor?y.stemColor:v[r].color:y.stemColor?y.stemColor:e,ma="number"===typeof v[r].stemThickness?v[r].stemThickness:"number"===typeof y.options.stemThickness?y.stemThickness:z,F=1===Math.round(ma)%2?0.5:0,E=v[r].stemDashType?v[r].stemDashType:y.stemDashType,L=v[r].lineColor?v[r].lineColor:v[r].color?y.lineColor?y.lineColor:v[r].color:y.lineColor?y.lineColor:e,O="number"===typeof v[r].lineThickness?v[r].lineThickness:"number"===typeof y.options.lineThickness?y.lineThickness:z,S=v[r].lineDashType?
v[r].lineDashType:y.lineDashType,M=1===Math.round(O)%2?0.5:0,P=y.upperBoxColor,Q=y.lowerBoxColor,va=m(y.options.fillOpacity)?1:y.fillOpacity,R=y.dataPointIds[r];this._eventManager.objectMap[R]={id:R,objectType:"dataPoint",dataSeriesIndex:s,dataPointIndex:r,x1:A,y1:h,x2:C,y2:k,x3:u,y3:l,x4:u,y4:p,y5:q,borderThickness:z,color:e,stemThickness:ma,stemColor:pa,whiskerThickness:T,whiskerLength:ca,whiskerColor:K,lineThickness:O,lineColor:L};c.save();0<ma&&(c.beginPath(),c.strokeStyle=pa,c.lineWidth=ma,c.setLineDash&&
c.setLineDash(H(E,ma)),c.moveTo(u-F,k),c.lineTo(u-F,h),c.stroke(),c.moveTo(u-F,p),c.lineTo(u-F,l),c.stroke());c.restore();b.lineWidth=Math.max(z,4);c.beginPath();ba(c,A,Math.min(q,k),C,Math.max(k,q),Q,0,e,J?w:!1,J?!1:w,!1,!1,va);c.beginPath();ba(c,A,Math.min(l,q),C,Math.max(q,l),P,0,e,J?!1:w,J?w:!1,!1,!1,va);c.beginPath();c.lineWidth=z;c.strokeStyle=e;c.rect(A-D,Math.min(k,l)-D,C-A+2*D,Math.max(k,l)-Math.min(k,l)+2*D);c.stroke();c.save();0<O&&(c.beginPath(),c.globalAlpha=1,c.setLineDash&&c.setLineDash(H(S,
O)),c.strokeStyle=L,c.lineWidth=O,c.moveTo(A,q-M),c.lineTo(C,q-M),c.stroke());c.restore();c.save();0<T&&(c.beginPath(),c.setLineDash&&c.setLineDash(H(ha,T)),c.strokeStyle=K,c.lineWidth=T,c.moveTo(u-ca/2<<0,p-ia),c.lineTo(u+ca/2<<0,p-ia),c.stroke(),c.moveTo(u-ca/2<<0,h+ia),c.lineTo(u+ca/2<<0,h+ia),c.stroke());c.restore();t&&(e=Z(R),b.strokeStyle=e,b.lineWidth=ma,0<ma&&(b.moveTo(u-D-F,k),b.lineTo(u-D-F,Math.max(h,p)),b.stroke(),b.moveTo(u-D-F,Math.min(h,p)),b.lineTo(u-D-F,l),b.stroke()),ba(b,A,Math.max(k,
l),C,Math.min(k,l),e,0,null,!1,!1,!1,!1),0<T&&(b.beginPath(),b.lineWidth=T,b.moveTo(u+ca/2,p-ia),b.lineTo(u-ca/2,p-ia),b.stroke(),b.moveTo(u+ca/2,h+ia),b.lineTo(u-ca/2,h+ia),b.stroke()));(v[r].indexLabel||y.indexLabel||v[r].indexLabelFormatter||y.indexLabelFormatter)&&this._indexLabels.push({chartType:y.type,dataPoint:v[r],dataSeries:y,point:{x:A+(C-A)/2,y:a.axisY.reversed?h:p},direction:1,bounds:{x1:A,y1:Math.min(h,p),x2:C,y2:Math.max(h,p)},color:e})}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,
this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(g.x1,g.y1,g.width,g.height),b.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};
n.prototype.renderRangeColumn=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=0,r,h,B,g=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1;r=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:0.03*this.width;var k=a.axisX.dataInfo.minDiff;isFinite(k)||(k=0.3*Math.abs(a.axisX.range));k=this.options.dataPointWidth?
this.dataPointWidth:0.9*(e.width*(a.axisX.logarithmic?Math.log(k)/Math.log(a.axisX.range):Math.abs(k)/Math.abs(a.axisX.range))/a.plotType.totalDataSeries)<<0;this.dataPointMaxWidth&&g>r&&(g=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,r));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&r<g)&&(r=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,g));k<g&&(k=g);k>r&&(k=r);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);
c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(var l=0;l<a.dataSeriesIndexes.length;l++){var p=a.dataSeriesIndexes[l],q=this.data[p],f=q.dataPoints;if(0<f.length)for(var n=5<k&&q.bevelEnabled?!0:!1,g=0;g<f.length;g++)if(f[g].getTime?B=f[g].x.getTime():B=f[g].x,!(B<a.axisX.dataInfo.viewPortMin||B>a.axisX.dataInfo.viewPortMax)&&!m(f[g].y)&&f[g].y.length&&"number"===typeof f[g].y[0]&&"number"===
typeof f[g].y[1]){b=a.axisX.convertValueToPixel(B);r=a.axisY.convertValueToPixel(f[g].y[0]);h=a.axisY.convertValueToPixel(f[g].y[1]);var J=a.axisX.reversed?b+a.plotType.totalDataSeries*k/2-(a.previousDataSeriesCount+l)*k<<0:b-a.plotType.totalDataSeries*k/2+(a.previousDataSeriesCount+l)*k<<0,x=a.axisX.reversed?J-k<<0:J+k<<0,b=f[g].color?f[g].color:q._colorSet[g%q._colorSet.length];if(r>h){var s=r;r=h;h=s}s=q.dataPointIds[g];this._eventManager.objectMap[s]={id:s,objectType:"dataPoint",dataSeriesIndex:p,
dataPointIndex:g,x1:J,y1:r,x2:x,y2:h};ba(c,a.axisX.reversed?x:J,r,a.axisX.reversed?J:x,h,b,0,b,n,n,!1,!1,q.fillOpacity);b=Z(s);t&&ba(this._eventManager.ghostCtx,a.axisX.reversed?x:J,r,a.axisX.reversed?J:x,h,b,0,null,!1,!1,!1,!1);if(f[g].indexLabel||q.indexLabel||f[g].indexLabelFormatter||q.indexLabelFormatter)this._indexLabels.push({chartType:"rangeColumn",dataPoint:f[g],dataSeries:q,indexKeyword:0,point:{x:J+(x-J)/2,y:f[g].y[1]>=f[g].y[0]?h:r},direction:f[g].y[1]>=f[g].y[0]?-1:1,bounds:{x1:J,y1:Math.min(r,
h),x2:x,y2:Math.max(r,h)},color:b}),this._indexLabels.push({chartType:"rangeColumn",dataPoint:f[g],dataSeries:q,indexKeyword:1,point:{x:J+(x-J)/2,y:f[g].y[1]>=f[g].y[0]?r:h},direction:f[g].y[1]>=f[g].y[0]?1:-1,bounds:{x1:J,y1:Math.min(r,h),x2:x,y2:Math.max(r,h)},color:b})}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,
0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};n.prototype.renderError=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d,b=a.axisY._position?"left"===a.axisY._position||
"right"===a.axisY._position?!1:!0:!1;if(!(0>=a.dataSeriesIndexes.length)){var e=null,g=!1,r=this.plotArea,h=0,B,k,l,p,q,f,n,J=a.axisX.dataInfo.minDiff;isFinite(J)||(J=0.3*Math.abs(a.axisX.range));c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(r.x1,r.y1,r.width,r.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(r.x1,r.y1,r.width,r.height),this._eventManager.ghostCtx.clip());for(var x=0,s=0;s<this.data.length;s++)!this.data[s].type.match(/(bar|column)/ig)||
!this.data[s].visible||this.data[s].type.match(/(stacked)/ig)&&x||x++;for(var y=0;y<a.dataSeriesIndexes.length;y++){var v=a.dataSeriesIndexes[y],w=this.data[v],A=w.dataPoints,C=m(w._linkedSeries)?!1:w._linkedSeries.type.match(/(bar|column)/ig)&&w._linkedSeries.visible?!0:!1,D=0;if(C)for(e=w._linkedSeries.id,s=0;s<e;s++)!this.data[s].type.match(/(bar|column)/ig)||!this.data[s].visible||this.data[s].type.match(/(stacked)/ig)&&D||(this.data[s].type.match(/(range)/ig)&&(g=!0),D++);e=this.options.dataPointMinWidth?
this.dataPointMinWidth:this.options.dataPointWidth?this.dataPointWidth:1;h=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:b?Math.min(0.15*this.height,0.9*(this.plotArea.height/(C?x:1)))<<0:0.3*this.width;g&&(h=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:b?Math.min(0.15*this.height,0.9*(this.plotArea.height/(C?x:1)))<<0:0.03*this.width);s=this.options.dataPointWidth?this.dataPointWidth:
0.9*((b?r.height:r.width)*(a.axisX.logarithmic?Math.log(J)/Math.log(a.axisX.range):Math.abs(J)/Math.abs(a.axisX.range))/(C?x:1))<<0;this.dataPointMaxWidth&&e>h&&(e=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,h));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&h<e)&&(h=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,e));s<e&&(s=e);s>h&&(s=h);if(0<A.length)for(var H=w._colorSet,h=0;h<A.length;h++){var e=w.lineColor=w.options.color?w.options.color:H[0],K={color:A[h].whiskerColor?
A[h].whiskerColor:A[h].color?w.whiskerColor?w.whiskerColor:A[h].color:w.whiskerColor?w.whiskerColor:e,thickness:m(A[h].whiskerThickness)?w.whiskerThickness:A[h].whiskerThickness,dashType:A[h].whiskerDashType?A[h].whiskerDashType:w.whiskerDashType,length:m(A[h].whiskerLength)?m(w.options.whiskerLength)?s:w.options.whiskerLength:A[h].whiskerLength,trimLength:m(A[h].whiskerLength)?m(w.options.whiskerLength)?50:0:0};K.length="number"===typeof K.length?0>=K.length?0:K.length>=s?s:K.length:"string"===typeof K.length?
parseInt(K.length)*s/100>s?s:parseInt(K.length)*s/100>s:s;K.thickness="number"===typeof K.thickness?0>K.thickness?0:Math.round(K.thickness):2;var T={color:A[h].stemColor?A[h].stemColor:A[h].color?w.stemColor?w.stemColor:A[h].color:w.stemColor?w.stemColor:e,thickness:A[h].stemThickness?A[h].stemThickness:w.stemThickness,dashType:A[h].stemDashType?A[h].stemDashType:w.stemDashType};T.thickness="number"===typeof T.thickness?0>T.thickness?0:Math.round(T.thickness):2;A[h].getTime?n=A[h].x.getTime():n=A[h].x;
if(!(n<a.axisX.dataInfo.viewPortMin||n>a.axisX.dataInfo.viewPortMax)&&!m(A[h].y)&&A[h].y.length&&"number"===typeof A[h].y[0]&&"number"===typeof A[h].y[1]){var ha=a.axisX.convertValueToPixel(n);b?k=ha:B=ha;ha=a.axisY.convertValueToPixel(A[h].y[0]);b?l=ha:q=ha;ha=a.axisY.convertValueToPixel(A[h].y[1]);b?p=ha:f=ha;b?(q=a.axisX.reversed?k+(C?x:1)*s/2-(C?D-1:0)*s<<0:k-(C?x:1)*s/2+(C?D-1:0)*s<<0,f=a.axisX.reversed?q-s<<0:q+s<<0):(l=a.axisX.reversed?B+(C?x:1)*s/2-(C?D-1:0)*s<<0:B-(C?x:1)*s/2+(C?D-1:0)*s<<
0,p=a.axisX.reversed?l-s<<0:l+s<<0);!b&&q>f&&(ha=q,q=f,f=ha);b&&l>p&&(ha=l,l=p,p=ha);ha=w.dataPointIds[h];this._eventManager.objectMap[ha]={id:ha,objectType:"dataPoint",dataSeriesIndex:v,dataPointIndex:h,x1:Math.min(l,p),y1:Math.min(q,f),x2:Math.max(p,l),y2:Math.max(f,q),isXYSwapped:b,stemProperties:T,whiskerProperties:K};z(c,Math.min(l,p),Math.min(q,f),Math.max(p,l),Math.max(f,q),e,K,T,b);t&&z(this._eventManager.ghostCtx,l,q,p,f,e,K,T,b);if(A[h].indexLabel||w.indexLabel||A[h].indexLabelFormatter||
w.indexLabelFormatter)this._indexLabels.push({chartType:"error",dataPoint:A[h],dataSeries:w,indexKeyword:0,point:{x:b?A[h].y[1]>=A[h].y[0]?l:p:l+(p-l)/2,y:b?q+(f-q)/2:A[h].y[1]>=A[h].y[0]?f:q},direction:A[h].y[1]>=A[h].y[0]?-1:1,bounds:{x1:b?Math.min(l,p):l,y1:b?q:Math.min(q,f),x2:b?Math.max(l,p):p,y2:b?f:Math.max(q,f)},color:e,axisSwapped:b}),this._indexLabels.push({chartType:"error",dataPoint:A[h],dataSeries:w,indexKeyword:1,point:{x:b?A[h].y[1]>=A[h].y[0]?p:l:l+(p-l)/2,y:b?q+(f-q)/2:A[h].y[1]>=
A[h].y[0]?q:f},direction:A[h].y[1]>=A[h].y[0]?1:-1,bounds:{x1:b?Math.min(l,p):l,y1:b?q:Math.min(q,f),x2:b?Math.max(l,p):p,y2:b?f:Math.max(q,f)},color:e,axisSwapped:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,
0,0,this.width,this.height),c.clearRect(r.x1,r.y1,r.width,r.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};n.prototype.renderRangeBar=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=null,e=this.plotArea,g=0,r,h,B,k,g=this.options.dataPointMinWidth?this.dataPointMinWidth:this.options.dataPointWidth?
this.dataPointWidth:1;r=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:Math.min(0.15*this.height,0.9*(this.plotArea.height/a.plotType.totalDataSeries))<<0;var l=a.axisX.dataInfo.minDiff;isFinite(l)||(l=0.3*Math.abs(a.axisX.range));l=this.options.dataPointWidth?this.dataPointWidth:0.9*(e.height*(a.axisX.logarithmic?Math.log(l)/Math.log(a.axisX.range):Math.abs(l)/Math.abs(a.axisX.range))/a.plotType.totalDataSeries)<<0;this.dataPointMaxWidth&&g>
r&&(g=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,r));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&r<g)&&(r=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,g));l<g&&(l=g);l>r&&(l=r);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(e.x1,e.y1,e.width,e.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.clip());for(var p=0;p<a.dataSeriesIndexes.length;p++){var q=
a.dataSeriesIndexes[p],f=this.data[q],n=f.dataPoints;if(0<n.length){var J=5<l&&f.bevelEnabled?!0:!1;c.strokeStyle="#4572A7 ";for(g=0;g<n.length;g++)if(n[g].getTime?k=n[g].x.getTime():k=n[g].x,!(k<a.axisX.dataInfo.viewPortMin||k>a.axisX.dataInfo.viewPortMax)&&!m(n[g].y)&&n[g].y.length&&"number"===typeof n[g].y[0]&&"number"===typeof n[g].y[1]){r=a.axisY.convertValueToPixel(n[g].y[0]);h=a.axisY.convertValueToPixel(n[g].y[1]);B=a.axisX.convertValueToPixel(k);B=a.axisX.reversed?B+a.plotType.totalDataSeries*
l/2-(a.previousDataSeriesCount+p)*l<<0:B-a.plotType.totalDataSeries*l/2+(a.previousDataSeriesCount+p)*l<<0;var x=a.axisX.reversed?B-l<<0:B+l<<0;r>h&&(b=r,r=h,h=b);b=n[g].color?n[g].color:f._colorSet[g%f._colorSet.length];ba(c,r,a.axisX.reversed?x:B,h,a.axisX.reversed?B:x,b,0,null,J,!1,!1,!1,f.fillOpacity);b=f.dataPointIds[g];this._eventManager.objectMap[b]={id:b,objectType:"dataPoint",dataSeriesIndex:q,dataPointIndex:g,x1:r,y1:B,x2:h,y2:x};b=Z(b);t&&ba(this._eventManager.ghostCtx,r,a.axisX.reversed?
x:B,h,a.axisX.reversed?B:x,b,0,null,!1,!1,!1,!1);if(n[g].indexLabel||f.indexLabel||n[g].indexLabelFormatter||f.indexLabelFormatter)this._indexLabels.push({chartType:"rangeBar",dataPoint:n[g],dataSeries:f,indexKeyword:0,point:{x:n[g].y[1]>=n[g].y[0]?r:h,y:B+(x-B)/2},direction:n[g].y[1]>=n[g].y[0]?-1:1,bounds:{x1:Math.min(r,h),y1:B,x2:Math.max(r,h),y2:x},color:b}),this._indexLabels.push({chartType:"rangeBar",dataPoint:n[g],dataSeries:f,indexKeyword:1,point:{x:n[g].y[1]>=n[g].y[0]?h:r,y:B+(x-B)/2},direction:n[g].y[1]>=
n[g].y[0]?1:-1,bounds:{x1:Math.min(r,h),y1:B,x2:Math.max(r,h),y2:x},color:b})}}}t&&(d.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(e.x1,e.y1,e.width,e.height),this._eventManager.ghostCtx.restore());
c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,animationBase:0}}};n.prototype.renderRangeArea=function(a){function d(){if(y){for(var a=null,c=m.length-1;0<=c;c--)a=m[c],b.lineTo(a.x,a.y2),e.lineTo(a.x,a.y2);b.closePath();b.globalAlpha=l.fillOpacity;b.fill();b.globalAlpha=1;e.fill();if(0<l.lineThickness){b.beginPath();b.moveTo(a.x,a.y2);for(c=0;c<m.length;c++)a=m[c],b.lineTo(a.x,a.y2);b.moveTo(m[0].x,m[0].y1);for(c=0;c<m.length;c++)a=
m[c],b.lineTo(a.x,a.y1);b.stroke()}b.beginPath();b.moveTo(n,J);e.beginPath();e.moveTo(n,J);y={x:n,y:J};m=[];m.push({x:n,y1:J,y2:x})}}var c=a.targetCanvasCtx||this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=this._eventManager.ghostCtx,g=[],r=this.plotArea;b.save();t&&e.save();b.beginPath();b.rect(r.x1,r.y1,r.width,r.height);b.clip();t&&(e.beginPath(),e.rect(r.x1,r.y1,r.width,r.height),e.clip());for(var h=0;h<a.dataSeriesIndexes.length;h++){var m=[],k=a.dataSeriesIndexes[h],
l=this.data[k],p=l.dataPoints,g=l.id;this._eventManager.objectMap[g]={objectType:"dataSeries",dataSeriesIndex:k};g=Z(g);e.fillStyle=g;var g=[],q=!0,f=0,n,J,x,s,y=null;if(0<p.length){var v=l._colorSet[f%l._colorSet.length],w=l.lineColor=l.options.lineColor||v,A=w;b.fillStyle=v;b.strokeStyle=w;b.lineWidth=l.lineThickness;var C="solid";if(b.setLineDash){var z=H(l.nullDataLineDashType,l.lineThickness),C=l.lineDashType,D=H(C,l.lineThickness);b.setLineDash(D)}for(var K=!0;f<p.length;f++)if(s=p[f].x.getTime?
p[f].x.getTime():p[f].x,!(s<a.axisX.dataInfo.viewPortMin||s>a.axisX.dataInfo.viewPortMax&&(!l.connectNullData||!K)))if(null!==p[f].y&&p[f].y.length&&"number"===typeof p[f].y[0]&&"number"===typeof p[f].y[1]){n=a.axisX.convertValueToPixel(s);J=a.axisY.convertValueToPixel(p[f].y[0]);x=a.axisY.convertValueToPixel(p[f].y[1]);q||K?(l.connectNullData&&!q?(b.setLineDash&&(l.options.nullDataLineDashType||C===l.lineDashType&&l.lineDashType!==l.nullDataLineDashType)&&(m[m.length-1].newLineDashArray=D,C=l.nullDataLineDashType,
b.setLineDash(z)),b.lineTo(n,J),t&&e.lineTo(n,J),m.push({x:n,y1:J,y2:x})):(b.beginPath(),b.moveTo(n,J),y={x:n,y:J},m=[],m.push({x:n,y1:J,y2:x}),t&&(e.beginPath(),e.moveTo(n,J))),K=q=!1):(b.lineTo(n,J),m.push({x:n,y1:J,y2:x}),t&&e.lineTo(n,J),0==f%250&&d());s=l.dataPointIds[f];this._eventManager.objectMap[s]={id:s,objectType:"dataPoint",dataSeriesIndex:k,dataPointIndex:f,x1:n,y1:J,y2:x};f<p.length-1&&(A!==(p[f].lineColor||w)||C!==(p[f].lineDashType||l.lineDashType))&&(d(),A=p[f].lineColor||w,m[m.length-
1].newStrokeStyle=A,b.strokeStyle=A,b.setLineDash&&(p[f].lineDashType?(C=p[f].lineDashType,m[m.length-1].newLineDashArray=H(C,l.lineThickness),b.setLineDash(m[m.length-1].newLineDashArray)):(C=l.lineDashType,m[m.length-1].newLineDashArray=D,b.setLineDash(D))));if(0!==p[f].markerSize&&(0<p[f].markerSize||0<l.markerSize)){var T=l.getMarkerProperties(f,n,x,b);g.push(T);var ha=Z(s);t&&g.push({x:n,y:x,ctx:e,type:T.type,size:T.size,color:ha,borderColor:ha,borderThickness:T.borderThickness});T=l.getMarkerProperties(f,
n,J,b);g.push(T);ha=Z(s);t&&g.push({x:n,y:J,ctx:e,type:T.type,size:T.size,color:ha,borderColor:ha,borderThickness:T.borderThickness})}if(p[f].indexLabel||l.indexLabel||p[f].indexLabelFormatter||l.indexLabelFormatter)this._indexLabels.push({chartType:"rangeArea",dataPoint:p[f],dataSeries:l,indexKeyword:0,point:{x:n,y:J},direction:p[f].y[0]>p[f].y[1]===a.axisY.reversed?-1:1,color:v}),this._indexLabels.push({chartType:"rangeArea",dataPoint:p[f],dataSeries:l,indexKeyword:1,point:{x:n,y:x},direction:p[f].y[0]>
p[f].y[1]===a.axisY.reversed?1:-1,color:v})}else K||q||d(),K=!0;d();X.drawMarkers(g)}}t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.clearRect(r.x1,r.y1,r.width,r.height),this._eventManager.ghostCtx.restore());
b.restore();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderRangeSplineArea=function(a){function d(a,c){var d=w(J,2);if(0<d.length){if(0<k.lineThickness){b.strokeStyle=c;b.setLineDash&&b.setLineDash(a);b.beginPath();b.moveTo(d[0].x,d[0].y);for(var f=0;f<d.length-3;f+=3){if(d[f].newStrokeStyle||d[f].newLineDashArray)b.stroke(),b.beginPath(),b.moveTo(d[f].x,d[f].y),d[f].newStrokeStyle&&(b.strokeStyle=d[f].newStrokeStyle),
d[f].newLineDashArray&&b.setLineDash(d[f].newLineDashArray);b.bezierCurveTo(d[f+1].x,d[f+1].y,d[f+2].x,d[f+2].y,d[f+3].x,d[f+3].y)}}b.beginPath();b.moveTo(d[0].x,d[0].y);t&&(e.beginPath(),e.moveTo(d[0].x,d[0].y));for(f=0;f<d.length-3;f+=3)b.bezierCurveTo(d[f+1].x,d[f+1].y,d[f+2].x,d[f+2].y,d[f+3].x,d[f+3].y),t&&e.bezierCurveTo(d[f+1].x,d[f+1].y,d[f+2].x,d[f+2].y,d[f+3].x,d[f+3].y);d=w(x,2);b.lineTo(x[x.length-1].x,x[x.length-1].y);for(f=d.length-1;2<f;f-=3)b.bezierCurveTo(d[f-1].x,d[f-1].y,d[f-2].x,
d[f-2].y,d[f-3].x,d[f-3].y),t&&e.bezierCurveTo(d[f-1].x,d[f-1].y,d[f-2].x,d[f-2].y,d[f-3].x,d[f-3].y);b.closePath();b.globalAlpha=k.fillOpacity;b.fill();t&&(e.closePath(),e.fill());b.globalAlpha=1;if(0<k.lineThickness){b.strokeStyle=c;b.setLineDash&&b.setLineDash(a);b.beginPath();b.moveTo(d[0].x,d[0].y);for(var g=f=0;f<d.length-3;f+=3,g++){if(J[g].newStrokeStyle||J[g].newLineDashArray)b.stroke(),b.beginPath(),b.moveTo(d[f].x,d[f].y),J[g].newStrokeStyle&&(b.strokeStyle=J[g].newStrokeStyle),J[g].newLineDashArray&&
b.setLineDash(J[g].newLineDashArray);b.bezierCurveTo(d[f+1].x,d[f+1].y,d[f+2].x,d[f+2].y,d[f+3].x,d[f+3].y)}d=w(J,2);b.moveTo(d[0].x,d[0].y);for(g=f=0;f<d.length-3;f+=3,g++){if(J[g].newStrokeStyle||J[g].newLineDashArray)b.stroke(),b.beginPath(),b.moveTo(d[f].x,d[f].y),J[g].newStrokeStyle&&(b.strokeStyle=J[g].newStrokeStyle),J[g].newLineDashArray&&b.setLineDash(J[g].newLineDashArray);b.bezierCurveTo(d[f+1].x,d[f+1].y,d[f+2].x,d[f+2].y,d[f+3].x,d[f+3].y)}b.stroke()}b.beginPath()}}var c=a.targetCanvasCtx||
this.plotArea.ctx,b=t?this._preRenderCtx:c;if(!(0>=a.dataSeriesIndexes.length)){var e=this._eventManager.ghostCtx,g=[],r=this.plotArea;b.save();t&&e.save();b.beginPath();b.rect(r.x1,r.y1,r.width,r.height);b.clip();t&&(e.beginPath(),e.rect(r.x1,r.y1,r.width,r.height),e.clip());for(var h=0;h<a.dataSeriesIndexes.length;h++){var m=a.dataSeriesIndexes[h],k=this.data[m],l=k.dataPoints,g=k.id;this._eventManager.objectMap[g]={objectType:"dataSeries",dataSeriesIndex:m};g=Z(g);e.fillStyle=g;var g=[],p=0,q,
f,n,J=[],x=[];if(0<l.length){var s=k._colorSet[p%k._colorSet.length],y=k.lineColor=k.options.lineColor||s,v=y;b.fillStyle=s;b.lineWidth=k.lineThickness;var z="solid",A;if(b.setLineDash){var C=H(k.nullDataLineDashType,k.lineThickness),z=k.lineDashType;A=H(z,k.lineThickness)}for(f=!1;p<l.length;p++)if(q=l[p].x.getTime?l[p].x.getTime():l[p].x,!(q<a.axisX.dataInfo.viewPortMin||q>a.axisX.dataInfo.viewPortMax&&(!k.connectNullData||!f)))if(null!==l[p].y&&l[p].y.length&&"number"===typeof l[p].y[0]&&"number"===
typeof l[p].y[1]){q=a.axisX.convertValueToPixel(q);f=a.axisY.convertValueToPixel(l[p].y[0]);n=a.axisY.convertValueToPixel(l[p].y[1]);var D=k.dataPointIds[p];this._eventManager.objectMap[D]={id:D,objectType:"dataPoint",dataSeriesIndex:m,dataPointIndex:p,x1:q,y1:f,y2:n};J[J.length]={x:q,y:f};x[x.length]={x:q,y:n};p<l.length-1&&(v!==(l[p].lineColor||y)||z!==(l[p].lineDashType||k.lineDashType))&&(v=l[p].lineColor||y,J[J.length-1].newStrokeStyle=v,b.setLineDash&&(l[p].lineDashType?(z=l[p].lineDashType,
J[J.length-1].newLineDashArray=H(z,k.lineThickness)):(z=k.lineDashType,J[J.length-1].newLineDashArray=A)));if(0!==l[p].markerSize&&(0<l[p].markerSize||0<k.markerSize)){var E=k.getMarkerProperties(p,q,f,b);g.push(E);var K=Z(D);t&&g.push({x:q,y:f,ctx:e,type:E.type,size:E.size,color:K,borderColor:K,borderThickness:E.borderThickness});E=k.getMarkerProperties(p,q,n,b);g.push(E);K=Z(D);t&&g.push({x:q,y:n,ctx:e,type:E.type,size:E.size,color:K,borderColor:K,borderThickness:E.borderThickness})}if(l[p].indexLabel||
k.indexLabel||l[p].indexLabelFormatter||k.indexLabelFormatter)this._indexLabels.push({chartType:"rangeSplineArea",dataPoint:l[p],dataSeries:k,indexKeyword:0,point:{x:q,y:f},direction:l[p].y[0]<=l[p].y[1]?-1:1,color:s}),this._indexLabels.push({chartType:"rangeSplineArea",dataPoint:l[p],dataSeries:k,indexKeyword:1,point:{x:q,y:n},direction:l[p].y[0]<=l[p].y[1]?1:-1,color:s});f=!1}else 0<p&&!f&&(k.connectNullData?b.setLineDash&&(0<J.length&&(k.options.nullDataLineDashType||!l[p-1].lineDashType))&&(J[J.length-
1].newLineDashArray=C,z=k.nullDataLineDashType):(d(A,y),J=[],x=[])),f=!0;d(A,y);X.drawMarkers(g)}}t&&(c.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&b.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&b.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),b.clearRect(r.x1,r.y1,r.width,r.height),
this._eventManager.ghostCtx.restore());b.restore();return{source:c,dest:this.plotArea.ctx,animationCallback:N.xClipAnimation,easingFunction:N.easing.linear,animationBase:0}}};n.prototype.renderWaterfall=function(a){var d=a.targetCanvasCtx||this.plotArea.ctx,c=t?this._preRenderCtx:d;if(!(0>=a.dataSeriesIndexes.length)){var b=this._eventManager.ghostCtx,e=null,g=this.plotArea,r=0,h,m,k,l,p=a.axisY.convertValueToPixel(a.axisY.logarithmic?a.axisY.viewportMinimum:0),r=this.options.dataPointMinWidth?this.dataPointMinWidth:
this.options.dataPointWidth?this.dataPointWidth:1;m=this.options.dataPointMaxWidth?this.dataPointMaxWidth:this.options.dataPointWidth?this.dataPointWidth:Math.min(0.15*this.width,0.9*(this.plotArea.width/a.plotType.totalDataSeries))<<0;var q=a.axisX.dataInfo.minDiff;isFinite(q)||(q=0.3*Math.abs(a.axisX.range));q=this.options.dataPointWidth?this.dataPointWidth:0.6*(g.width*(a.axisX.logarithmic?Math.log(q)/Math.log(a.axisX.range):Math.abs(q)/Math.abs(a.axisX.range))/a.plotType.totalDataSeries)<<0;this.dataPointMaxWidth&&
r>m&&(r=Math.min(this.options.dataPointWidth?this.dataPointWidth:Infinity,m));!this.dataPointMaxWidth&&(this.dataPointMinWidth&&m<r)&&(m=Math.max(this.options.dataPointWidth?this.dataPointWidth:-Infinity,r));q<r&&(q=r);q>m&&(q=m);c.save();t&&this._eventManager.ghostCtx.save();c.beginPath();c.rect(g.x1,g.y1,g.width,g.height);c.clip();t&&(this._eventManager.ghostCtx.beginPath(),this._eventManager.ghostCtx.rect(g.x1,g.y1,g.width,g.height),this._eventManager.ghostCtx.clip());for(var f=0;f<a.dataSeriesIndexes.length;f++){var n=
a.dataSeriesIndexes[f],w=this.data[n],x=w.dataPoints,e=w._colorSet[0];w.risingColor=w.options.risingColor?w.options.risingColor:e;w.fallingColor=w.options.fallingColor?w.options.fallingColor:"#e40a0a";var s="number"===typeof w.options.lineThickness?Math.round(w.lineThickness):1,y=1===Math.round(s)%2?-0.5:0;if(0<x.length)for(var v=5<q&&w.bevelEnabled?!0:!1,z=!1,A=null,C=null,r=0;r<x.length;r++)if(x[r].getTime?l=x[r].x.getTime():l=x[r].x,"number"!==typeof x[r].y){if(0<r&&!z&&w.connectNullData)var D=
w.options.nullDataLineDashType||!x[r-1].lineDashType?w.nullDataLineDashType:x[r-1].lineDashType;z=!0}else{h=a.axisX.convertValueToPixel(l);m=0===w.dataPointEOs[r].cumulativeSum?p:a.axisY.convertValueToPixel(w.dataPointEOs[r].cumulativeSum);k=0===w.dataPointEOs[r].cumulativeSumYStartValue?p:a.axisY.convertValueToPixel(w.dataPointEOs[r].cumulativeSumYStartValue);h=a.axisX.reversed?h+a.plotType.totalDataSeries*q/2-(a.previousDataSeriesCount+f)*q<<0:h-a.plotType.totalDataSeries*q/2+(a.previousDataSeriesCount+
f)*q<<0;var E=a.axisX.reversed?h-q<<0:h+q<<0;m>k&&(e=m,m=k,k=e);a.axisY.reversed&&(e=m,m=k,k=e);e=w.dataPointIds[r];this._eventManager.objectMap[e]={id:e,objectType:"dataPoint",dataSeriesIndex:n,dataPointIndex:r,x1:h,y1:m,x2:E,y2:k};var K=x[r].color?x[r].color:0<x[r].y?w.risingColor:w.fallingColor;ba(c,a.axisX.reversed?E:h,a.axisY.reversed?k:m,a.axisX.reversed?h:E,a.axisY.reversed?m:k,K,0,K,v,v,!1,!1,w.fillOpacity);e=Z(e);t&&ba(this._eventManager.ghostCtx,a.axisX.reversed?E:h,m,a.axisX.reversed?h:
E,k,e,0,null,!1,!1,!1,!1);var T,K=h;T="undefined"!==typeof x[r].isIntermediateSum&&!0===x[r].isIntermediateSum||"undefined"!==typeof x[r].isCumulativeSum&&!0===x[r].isCumulativeSum?0<x[r].y?m:k:0<x[r].y?k:m;0<r&&A&&(!z||w.connectNullData)&&(z&&c.setLineDash&&c.setLineDash(H(D,s)),c.beginPath(),c.moveTo(A,C-y),c.lineTo(K,T-y),0<s&&c.stroke(),t&&(b.beginPath(),b.moveTo(A,C-y),b.lineTo(K,T-y),0<s&&b.stroke()));z=!1;A=E;C=0<x[r].y?m:k;K=x[r].lineDashType?x[r].lineDashType:w.options.lineDashType?w.options.lineDashType:
"shortDash";c.strokeStyle=x[r].lineColor?x[r].lineColor:w.options.lineColor?w.options.lineColor:"#9e9e9e";c.lineWidth=s;c.setLineDash&&(K=H(K,s),c.setLineDash(K));(x[r].indexLabel||w.indexLabel||x[r].indexLabelFormatter||w.indexLabelFormatter)&&this._indexLabels.push({chartType:"waterfall",dataPoint:x[r],dataSeries:w,point:{x:h+(E-h)/2,y:0<=x[r].y?m:k},direction:0>x[r].y===a.axisY.reversed?1:-1,bounds:{x1:h,y1:Math.min(m,k),x2:E,y2:Math.max(m,k)},color:e})}}t&&(d.drawImage(this._preRenderCanvas,0,
0,this.width,this.height),c.globalCompositeOperation="source-atop",a.axisX.maskCanvas&&c.drawImage(a.axisX.maskCanvas,0,0,this.width,this.height),a.axisY.maskCanvas&&c.drawImage(a.axisY.maskCanvas,0,0,this.width,this.height),this._breaksCanvasCtx&&this._breaksCanvasCtx.drawImage(this._preRenderCanvas,0,0,this.width,this.height),c.clearRect(g.x1,g.y1,g.width,g.height),this._eventManager.ghostCtx.restore());c.restore();return{source:d,dest:this.plotArea.ctx,animationCallback:N.fadeInAnimation,easingFunction:N.easing.easeInQuad,
animationBase:0}}};var ra=function(a,d,c,b,e,g,r,h,m){if(!(0>c)){"undefined"===typeof h&&(h=1);if(!t){var k=Number((r%(2*Math.PI)).toFixed(8));Number((g%(2*Math.PI)).toFixed(8))===k&&(r-=1E-4)}a.save();a.globalAlpha=h;"pie"===e?(a.beginPath(),a.moveTo(d.x,d.y),a.arc(d.x,d.y,c,g,r,!1),a.fillStyle=b,a.strokeStyle="white",a.lineWidth=2,a.closePath(),a.fill()):"doughnut"===e&&(a.beginPath(),a.arc(d.x,d.y,c,g,r,!1),0<=m&&a.arc(d.x,d.y,m*c,r,g,!0),a.closePath(),a.fillStyle=b,a.strokeStyle="white",a.lineWidth=
2,a.fill());a.globalAlpha=1;a.restore()}};n.prototype.renderPie=function(a){function d(){if(k&&l){var a=0,b=0,c=0,d=0;m(k.options.indexLabelMaxWidth)&&(k.indexLabelMaxWidth=0.33*q.width);for(var e=0;e<l.length;e++){var g=l[e],r=k.dataPointIds[e];f[e].id=r;f[e].objectType="dataPoint";f[e].dataPointIndex=e;f[e].dataSeriesIndex=0;var h=f[e],p={percent:null,total:null},u=null,p=n.getPercentAndTotal(k,g);if(k.indexLabelFormatter||g.indexLabelFormatter)u={chart:n.options,dataSeries:k,dataPoint:g,total:p.total,
percent:p.percent};p=g.indexLabelFormatter?g.indexLabelFormatter(u):g.indexLabel?n.replaceKeywordsWithValue(g.indexLabel,g,k,e):k.indexLabelFormatter?k.indexLabelFormatter(u):k.indexLabel?n.replaceKeywordsWithValue(k.indexLabel,g,k,e):g.label?g.label:"";n._eventManager.objectMap[r]=h;h.center={x:v.x,y:v.y};h.y=g.y;h.radius=C;h.percentInnerRadius=E;h.indexLabelText=p;h.indexLabelPlacement=k.indexLabelPlacement;h.indexLabelLineColor=g.indexLabelLineColor?g.indexLabelLineColor:k.options.indexLabelLineColor?
k.options.indexLabelLineColor:g.color?g.color:k._colorSet[e%k._colorSet.length];h.indexLabelLineThickness=m(g.indexLabelLineThickness)?k.indexLabelLineThickness:g.indexLabelLineThickness;h.indexLabelLineDashType=g.indexLabelLineDashType?g.indexLabelLineDashType:k.indexLabelLineDashType;h.indexLabelFontColor=g.indexLabelFontColor?g.indexLabelFontColor:k.indexLabelFontColor;h.indexLabelFontStyle=g.indexLabelFontStyle?g.indexLabelFontStyle:k.indexLabelFontStyle;h.indexLabelFontWeight=g.indexLabelFontWeight?
g.indexLabelFontWeight:k.indexLabelFontWeight;h.indexLabelFontSize=m(g.indexLabelFontSize)?k.indexLabelFontSize:g.indexLabelFontSize;h.indexLabelFontFamily=g.indexLabelFontFamily?g.indexLabelFontFamily:k.indexLabelFontFamily;h.indexLabelBackgroundColor=g.indexLabelBackgroundColor?g.indexLabelBackgroundColor:k.options.indexLabelBackgroundColor?k.options.indexLabelBackgroundColor:k.indexLabelBackgroundColor;h.indexLabelMaxWidth=g.indexLabelMaxWidth?g.indexLabelMaxWidth:k.indexLabelMaxWidth;h.indexLabelWrap=
"undefined"!==typeof g.indexLabelWrap?g.indexLabelWrap:k.indexLabelWrap;h.indexLabelTextAlign=g.indexLabelTextAlign?g.indexLabelTextAlign:k.indexLabelTextAlign?k.indexLabelTextAlign:"left";h.startAngle=0===e?k.startAngle?k.startAngle/180*Math.PI:0:f[e-1].endAngle;h.startAngle=(h.startAngle+2*Math.PI)%(2*Math.PI);h.endAngle=h.startAngle+2*Math.PI/z*Math.abs(g.y);g=(h.endAngle+h.startAngle)/2;g=(g+2*Math.PI)%(2*Math.PI);h.midAngle=g;if(h.midAngle>Math.PI/2-s&&h.midAngle<Math.PI/2+s){if(0===a||f[c].midAngle>
h.midAngle)c=e;a++}else if(h.midAngle>3*Math.PI/2-s&&h.midAngle<3*Math.PI/2+s){if(0===b||f[d].midAngle>h.midAngle)d=e;b++}h.hemisphere=g>Math.PI/2&&g<=3*Math.PI/2?"left":"right";h.indexLabelTextBlock=new la(n.plotArea.ctx,{fontSize:h.indexLabelFontSize,fontFamily:h.indexLabelFontFamily,fontColor:h.indexLabelFontColor,fontStyle:h.indexLabelFontStyle,fontWeight:h.indexLabelFontWeight,textAlign:h.indexLabelTextAlign,backgroundColor:h.indexLabelBackgroundColor,maxWidth:h.indexLabelMaxWidth,maxHeight:h.indexLabelWrap?
5*h.indexLabelFontSize:1.5*h.indexLabelFontSize,text:h.indexLabelText,padding:0,textBaseline:"middle"});h.indexLabelTextBlock.measureText()}r=g=0;p=!1;for(e=0;e<l.length;e++)h=f[(c+e)%l.length],1<a&&(h.midAngle>Math.PI/2-s&&h.midAngle<Math.PI/2+s)&&(g<=a/2&&!p?(h.hemisphere="right",g++):(h.hemisphere="left",p=!0));p=!1;for(e=0;e<l.length;e++)h=f[(d+e)%l.length],1<b&&(h.midAngle>3*Math.PI/2-s&&h.midAngle<3*Math.PI/2+s)&&(r<=b/2&&!p?(h.hemisphere="left",r++):(h.hemisphere="right",p=!0))}}function c(a,
b){var c=n.plotArea.ctx;c.clearRect(q.x1,q.y1,q.width,q.height);c.fillStyle=n.backgroundColor;c.fillRect(q.x1,q.y1,q.width,q.height);for(c=0;c<l.length;c++){var d=f[c].startAngle,e=f[c].endAngle;if(e>d){var g=0.07*C*Math.cos(f[c].midAngle),r=0.07*C*Math.sin(f[c].midAngle),h=!1;if(l[c].exploded){if(1E-9<Math.abs(f[c].center.x-(v.x+g))||1E-9<Math.abs(f[c].center.y-(v.y+r)))f[c].center.x=v.x+g*a,f[c].center.y=v.y+r*a,h=!0}else if(0<Math.abs(f[c].center.x-v.x)||0<Math.abs(f[c].center.y-v.y))f[c].center.x=
v.x+g*(1-a),f[c].center.y=v.y+r*(1-a),h=!0;h&&b&&(g={},g.dataSeries=k,g.dataPoint=k.dataPoints[c],g.index=c,n.toolTip.highlightObjects([g]));ra(n.plotArea.ctx,f[c].center,f[c].radius,l[c].color?l[c].color:k._colorSet[c%k._colorSet.length],k.type,d,e,k.fillOpacity,f[c].percentInnerRadius)}}c=n.plotArea.ctx;c.save();c.fillStyle="black";c.strokeStyle="grey";c.textBaseline="middle";c.lineJoin="round";for(d=d=0;d<l.length;d++)e=f[d],e.indexLabelText&&(e.indexLabelTextBlock.y-=e.indexLabelTextBlock.height/
2-e.indexLabelTextBlock.fontSize/2,g=0,g="left"===e.hemisphere?"inside"!==k.indexLabelPlacement?-(e.indexLabelTextBlock.width+p):-e.indexLabelTextBlock.width/2:"inside"!==k.indexLabelPlacement?p:-e.indexLabelTextBlock.width/2,e.indexLabelTextBlock.x+=g,e.indexLabelTextBlock.render(!0),e.indexLabelTextBlock.x-=g,e.indexLabelTextBlock.y+=e.indexLabelTextBlock.height/2-e.indexLabelTextBlock.fontSize/2,"inside"!==e.indexLabelPlacement&&0<e.indexLabelLineThickness&&(g=e.center.x+C*Math.cos(e.midAngle),
r=e.center.y+C*Math.sin(e.midAngle),c.strokeStyle=e.indexLabelLineColor,c.lineWidth=e.indexLabelLineThickness,c.setLineDash&&c.setLineDash(H(e.indexLabelLineDashType,e.indexLabelLineThickness)),c.beginPath(),c.moveTo(g,r),c.lineTo(e.indexLabelTextBlock.x,e.indexLabelTextBlock.y),c.lineTo(e.indexLabelTextBlock.x+("left"===e.hemisphere?-p:p),e.indexLabelTextBlock.y),c.stroke()),c.lineJoin="miter");c.save()}function b(a,b){var c=0,c=a.indexLabelTextBlock.y-a.indexLabelTextBlock.height/2,e=a.indexLabelTextBlock.y+
a.indexLabelTextBlock.height/2,d=b.indexLabelTextBlock.y-b.indexLabelTextBlock.height/2,f=b.indexLabelTextBlock.y+b.indexLabelTextBlock.height/2;return c=b.indexLabelTextBlock.y>a.indexLabelTextBlock.y?d-e:c-f}function e(a){for(var c=null,e=1;e<l.length;e++)if(c=(a+e+f.length)%f.length,f[c].hemisphere!==f[a].hemisphere){c=null;break}else if(f[c].indexLabelText&&c!==a&&(0>b(f[c],f[a])||("right"===f[a].hemisphere?f[c].indexLabelTextBlock.y>=f[a].indexLabelTextBlock.y:f[c].indexLabelTextBlock.y<=f[a].indexLabelTextBlock.y)))break;
else c=null;return c}function g(a,c,d){d=(d||0)+1;if(1E3<d)return 0;c=c||0;var r=0,k=v.y-1*w,h=v.y+1*w;if(0<=a&&a<l.length){var p=f[a];if(0>c&&p.indexLabelTextBlock.y<k||0<c&&p.indexLabelTextBlock.y>h)return 0;var m=0,q=0,q=m=m=0;0>c?p.indexLabelTextBlock.y-p.indexLabelTextBlock.height/2>k&&p.indexLabelTextBlock.y-p.indexLabelTextBlock.height/2+c<k&&(c=-(k-(p.indexLabelTextBlock.y-p.indexLabelTextBlock.height/2+c))):p.indexLabelTextBlock.y+p.indexLabelTextBlock.height/2<k&&p.indexLabelTextBlock.y+
p.indexLabelTextBlock.height/2+c>h&&(c=p.indexLabelTextBlock.y+p.indexLabelTextBlock.height/2+c-h);c=p.indexLabelTextBlock.y+c;k=0;k="right"===p.hemisphere?v.x+Math.sqrt(Math.pow(w,2)-Math.pow(c-v.y,2)):v.x-Math.sqrt(Math.pow(w,2)-Math.pow(c-v.y,2));q=v.x+C*Math.cos(p.midAngle);m=v.y+C*Math.sin(p.midAngle);m=Math.sqrt(Math.pow(k-q,2)+Math.pow(c-m,2));q=Math.acos(C/w);m=Math.acos((w*w+C*C-m*m)/(2*C*w));c=m<q?c-p.indexLabelTextBlock.y:0;k=null;for(h=1;h<l.length;h++)if(k=(a-h+f.length)%f.length,f[k].hemisphere!==
f[a].hemisphere){k=null;break}else if(f[k].indexLabelText&&f[k].hemisphere===f[a].hemisphere&&k!==a&&(0>b(f[k],f[a])||("right"===f[a].hemisphere?f[k].indexLabelTextBlock.y<=f[a].indexLabelTextBlock.y:f[k].indexLabelTextBlock.y>=f[a].indexLabelTextBlock.y)))break;else k=null;q=k;m=e(a);h=k=0;0>c?(h="right"===p.hemisphere?q:m,r=c,null!==h&&(q=-c,c=p.indexLabelTextBlock.y-p.indexLabelTextBlock.height/2-(f[h].indexLabelTextBlock.y+f[h].indexLabelTextBlock.height/2),c-q<t&&(k=-q,h=g(h,k,d+1),+h.toFixed(y)>
+k.toFixed(y)&&(r=c>t?-(c-t):-(q-(h-k)))))):0<c&&(h="right"===p.hemisphere?m:q,r=c,null!==h&&(q=c,c=f[h].indexLabelTextBlock.y-f[h].indexLabelTextBlock.height/2-(p.indexLabelTextBlock.y+p.indexLabelTextBlock.height/2),c-q<t&&(k=q,h=g(h,k,d+1),+h.toFixed(y)<+k.toFixed(y)&&(r=c>t?c-t:q-(k-h)))));r&&(d=p.indexLabelTextBlock.y+r,c=0,c="right"===p.hemisphere?v.x+Math.sqrt(Math.pow(w,2)-Math.pow(d-v.y,2)):v.x-Math.sqrt(Math.pow(w,2)-Math.pow(d-v.y,2)),p.midAngle>Math.PI/2-s&&p.midAngle<Math.PI/2+s?(k=(a-
1+f.length)%f.length,k=f[k],a=f[(a+1+f.length)%f.length],"left"===p.hemisphere&&"right"===k.hemisphere&&c>k.indexLabelTextBlock.x?c=k.indexLabelTextBlock.x-15:"right"===p.hemisphere&&("left"===a.hemisphere&&c<a.indexLabelTextBlock.x)&&(c=a.indexLabelTextBlock.x+15)):p.midAngle>3*Math.PI/2-s&&p.midAngle<3*Math.PI/2+s&&(k=(a-1+f.length)%f.length,k=f[k],a=f[(a+1+f.length)%f.length],"right"===p.hemisphere&&"left"===k.hemisphere&&c<k.indexLabelTextBlock.x?c=k.indexLabelTextBlock.x+15:"left"===p.hemisphere&&
("right"===a.hemisphere&&c>a.indexLabelTextBlock.x)&&(c=a.indexLabelTextBlock.x-15)),p.indexLabelTextBlock.y=d,p.indexLabelTextBlock.x=c,p.indexLabelAngle=Math.atan2(p.indexLabelTextBlock.y-v.y,p.indexLabelTextBlock.x-v.x))}return r}function r(){var a=n.plotArea.ctx;a.fillStyle="grey";a.strokeStyle="grey";a.font="16px Arial";a.textBaseline="middle";for(var c=a=0,d=0,r=!0,c=0;10>c&&(1>c||0<d);c++){if(k.radius||!k.radius&&"undefined"!==typeof k.innerRadius&&null!==k.innerRadius&&C-d<=D)r=!1;r&&(C-=
d);d=0;if("inside"!==k.indexLabelPlacement){w=C*x;for(a=0;a<l.length;a++){var h=f[a];h.indexLabelTextBlock.x=v.x+w*Math.cos(h.midAngle);h.indexLabelTextBlock.y=v.y+w*Math.sin(h.midAngle);h.indexLabelAngle=h.midAngle;h.radius=C;h.percentInnerRadius=E}for(var m,u,a=0;a<l.length;a++){var h=f[a],s=e(a);if(null!==s){m=f[a];u=f[s];var A=0,A=b(m,u)-t;if(0>A){for(var z=u=0,I=0;I<l.length;I++)I!==a&&f[I].hemisphere===h.hemisphere&&(f[I].indexLabelTextBlock.y<h.indexLabelTextBlock.y?u++:z++);u=A/(u+z||1)*z;
var z=-1*(A-u),H=I=0;"right"===h.hemisphere?(I=g(a,u),z=-1*(A-I),H=g(s,z),+H.toFixed(y)<+z.toFixed(y)&&+I.toFixed(y)<=+u.toFixed(y)&&g(a,-(z-H))):(I=g(s,u),z=-1*(A-I),H=g(a,z),+H.toFixed(y)<+z.toFixed(y)&&+I.toFixed(y)<=+u.toFixed(y)&&g(s,-(z-H)))}}}}else for(a=0;a<l.length;a++)h=f[a],w="pie"===k.type?0.7*C:0.85*C,s=v.x+w*Math.cos(h.midAngle),u=v.y+w*Math.sin(h.midAngle),h.indexLabelTextBlock.x=s,h.indexLabelTextBlock.y=u;for(a=0;a<l.length;a++)if(h=f[a],s=h.indexLabelTextBlock.measureText(),0!==
s.height&&0!==s.width)s=s=0,"right"===h.hemisphere?(s=q.x2-(h.indexLabelTextBlock.x+h.indexLabelTextBlock.width+p),s*=-1):s=q.x1-(h.indexLabelTextBlock.x-h.indexLabelTextBlock.width-p),0<s&&(!r&&h.indexLabelText&&(u="right"===h.hemisphere?q.x2-h.indexLabelTextBlock.x:h.indexLabelTextBlock.x-q.x1,0.3*h.indexLabelTextBlock.maxWidth>u?h.indexLabelText="":h.indexLabelTextBlock.maxWidth=0.85*u,0.3*h.indexLabelTextBlock.maxWidth<u&&(h.indexLabelTextBlock.x-="right"===h.hemisphere?2:-2)),Math.abs(h.indexLabelTextBlock.y-
h.indexLabelTextBlock.height/2-v.y)<C||Math.abs(h.indexLabelTextBlock.y+h.indexLabelTextBlock.height/2-v.y)<C)&&(s/=Math.abs(Math.cos(h.indexLabelAngle)),9<s&&(s*=0.3),s>d&&(d=s)),s=s=0,0<h.indexLabelAngle&&h.indexLabelAngle<Math.PI?(s=q.y2-(h.indexLabelTextBlock.y+h.indexLabelTextBlock.height/2+5),s*=-1):s=q.y1-(h.indexLabelTextBlock.y-h.indexLabelTextBlock.height/2-5),0<s&&(!r&&h.indexLabelText&&(u=0<h.indexLabelAngle&&h.indexLabelAngle<Math.PI?-1:1,0===g(a,s*u)&&g(a,2*u)),Math.abs(h.indexLabelTextBlock.x-
v.x)<C&&(s/=Math.abs(Math.sin(h.indexLabelAngle)),9<s&&(s*=0.3),s>d&&(d=s)));var M=function(a,b,c){for(var e=[],d=0;e.push(f[b]),b!==c;b=(b+1+l.length)%l.length);e.sort(function(a,b){return a.y-b.y});for(b=0;b<e.length;b++)if(c=e[b],d<0.7*a)d+=c.indexLabelTextBlock.height,c.indexLabelTextBlock.text="",c.indexLabelText="",c.indexLabelTextBlock.measureText();else break};(function(){for(var a=-1,c=-1,d=0,g=!1,r=0;r<l.length;r++)if(g=!1,m=f[r],m.indexLabelText){var k=e(r);if(null!==k){var h=f[k];A=0;
A=b(m,h);var q;if(q=0>A){q=m.indexLabelTextBlock.x;var u=m.indexLabelTextBlock.y-m.indexLabelTextBlock.height/2,n=m.indexLabelTextBlock.y+m.indexLabelTextBlock.height/2,s=h.indexLabelTextBlock.y-h.indexLabelTextBlock.height/2,v=h.indexLabelTextBlock.x+h.indexLabelTextBlock.width,B=h.indexLabelTextBlock.y+h.indexLabelTextBlock.height/2;q=m.indexLabelTextBlock.x+m.indexLabelTextBlock.width<h.indexLabelTextBlock.x-p||q>v+p||u>B+p||n<s-p?!1:!0}q?(0>a&&(a=r),k!==a&&(c=k,d+=-A),0===r%Math.max(l.length/
10,3)&&(g=!0)):g=!0;g&&(0<d&&0<=a&&0<=c)&&(M(d,a,c),c=a=-1,d=0)}}0<d&&M(d,a,c)})()}}function h(){n.plotArea.layoutManager.reset();n.title&&(n.title.dockInsidePlotArea||"center"===n.title.horizontalAlign&&"center"===n.title.verticalAlign)&&n.title.render();if(n.subtitles)for(var a=0;a<n.subtitles.length;a++){var b=n.subtitles[a];(b.dockInsidePlotArea||"center"===b.horizontalAlign&&"center"===b.verticalAlign)&&b.render()}n.legend&&(n.legend.dockInsidePlotArea||"center"===n.legend.horizontalAlign&&"center"===
n.legend.verticalAlign)&&(n.legend.setLayout(),n.legend.render())}var n=this;if(!(0>=a.dataSeriesIndexes.length)){var k=this.data[a.dataSeriesIndexes[0]],l=k.dataPoints,p=10,q=this.plotArea,f=k.dataPointEOs,t=2,w,x=1.3,s=20/180*Math.PI,y=6,v={x:(q.x2+q.x1)/2,y:(q.y2+q.y1)/2},z=0;a=!1;for(var A=0;A<l.length;A++)z+=Math.abs(l[A].y),!a&&("undefined"!==typeof l[A].indexLabel&&null!==l[A].indexLabel&&0<l[A].indexLabel.toString().length)&&(a=!0),!a&&("undefined"!==typeof l[A].label&&null!==l[A].label&&
0<l[A].label.toString().length)&&(a=!0);if(0!==z){a=a||"undefined"!==typeof k.indexLabel&&null!==k.indexLabel&&0<k.indexLabel.toString().length;var C="inside"!==k.indexLabelPlacement&&a?0.75*Math.min(q.width,q.height)/2:0.92*Math.min(q.width,q.height)/2;k.radius&&(C=Ta(k.radius,C));var D="undefined"!==typeof k.innerRadius&&null!==k.innerRadius?Ta(k.innerRadius,C):0.7*C;k.radius=C;"doughnut"===k.type&&(k.innerRadius=D);var E=Math.min(D/C,(C-1)/C);this.pieDoughnutClickHandler=function(a){n.isAnimating||
!m(a.dataSeries.explodeOnClick)&&!a.dataSeries.explodeOnClick||(a=a.dataPoint,a.exploded=a.exploded?!1:!0,1<this.dataPoints.length&&n._animator.animate(0,500,function(a){c(a,!0);h();n.dispatchEvent("dataAnimationIterationEnd",{chart:n});n.dispatchEvent("dataAnimationEnd",{chart:n})}))};d();r();r();r();r();this.disableToolTip=!0;this._animator.animate(0,this.animatedRender?this.animationDuration:0,function(a){var b=n.plotArea.ctx;b.clearRect(q.x1,q.y1,q.width,q.height);b.fillStyle=n.backgroundColor;
b.fillRect(q.x1,q.y1,q.width,q.height);for(var b=f[0].startAngle+2*Math.PI*a,c=0;c<l.length;c++){var e=0===c?f[c].startAngle:d,d=e+(f[c].endAngle-f[c].startAngle),g=!1;d>b&&(d=b,g=!0);var r=l[c].color?l[c].color:k._colorSet[c%k._colorSet.length];d>e&&ra(n.plotArea.ctx,f[c].center,f[c].radius,r,k.type,e,d,k.fillOpacity,f[c].percentInnerRadius);if(g)break}h();n.dispatchEvent("dataAnimationIterationEnd",{chart:n});1<=a&&n.dispatchEvent("dataAnimationEnd",{chart:n})},function(){n.disableToolTip=!1;n._animator.animate(0,
n.animatedRender?500:0,function(a){c(a,!1);h();n.dispatchEvent("dataAnimationIterationEnd",{chart:n})});n.dispatchEvent("dataAnimationEnd",{chart:n})})}}};var ta=function(a,d,c,b){"undefined"===typeof c&&(c=1);0>=Math.round(d.y4-d.y1)||(a.save(),a.globalAlpha=c,a.beginPath(),a.moveTo(Math.round(d.x1),Math.round(d.y1)),a.lineTo(Math.round(d.x2),Math.round(d.y2)),a.lineTo(Math.round(d.x3),Math.round(d.y3)),a.lineTo(Math.round(d.x4),Math.round(d.y4)),"undefined"!==d.x5&&(a.lineTo(Math.round(d.x5),Math.round(d.y5)),
a.lineTo(Math.round(d.x6),Math.round(d.y6))),a.closePath(),a.fillStyle=b?b:d.color,a.fill(),a.globalAplha=1,a.restore())};n.prototype.renderFunnel=function(a){function d(){for(var a=0,b=[],c=0;c<y.length;c++){if("undefined"===typeof y[c].y)return-1;y[c].y="number"===typeof y[c].y?y[c].y:0;a+=Math.abs(y[c].y)}if(0===a)return-1;for(c=b[0]=0;c<y.length;c++)b.push(Math.abs(y[c].y)*D/a);return b}function c(){var a=U,b=Y,c=M,e=$,d,f;d=O;f=S-Q;e=Math.abs((f-d)*(b-a+(e-c))/2);c=$-M;d=f-d;f=c*(f-S);f=Math.abs(f);
f=e+f;for(var e=[],g=0,r=0;r<y.length;r++){if("undefined"===typeof y[r].y)return-1;y[r].y="number"===typeof y[r].y?y[r].y:0;g+=Math.abs(y[r].y)}if(0===g)return-1;for(var h=e[0]=0,k=0,l,p,b=b-a,h=!1,r=0;r<y.length;r++)a=Math.abs(y[r].y)*f/g,h?l=0==Number(c.toFixed(3))?0:a/c:(p=da*da*b*b-4*Math.abs(da)*a,0>p?(p=c,h=(b+p)*(d-k)/2,a-=h,l=d-k,k+=d-k,l+=0==p?0:a/p,k+=a/p,h=!0):(l=(Math.abs(da)*b-Math.sqrt(p))/2,p=b-2*l/Math.abs(da),k+=l,k>d&&(k-=l,p=c,h=(b+p)*(d-k)/2,a-=h,l=d-k,k+=d-k,l+=a/p,k+=a/p,h=!0),
b=p)),e.push(l);return e}function b(){if(s&&y){for(var a,b,c,d,e,g,r,k,h,l,p,q,u,n,v,B=[],w=[],z={percent:null,total:null},J=null,A=0;A<y.length;A++)v=R[A],v="undefined"!==typeof v.x5?(v.y2+v.y4)/2:(v.y2+v.y3)/2,v=f(v).x2+1,B[A]=L-v-Z;v=0.5*Z;for(var A=0,C=y.length-1;A<y.length||0<=C;A++,C--){b=s.reversed?y[C]:y[A];a=b.color?b.color:s.reversed?s._colorSet[(y.length-1-A)%s._colorSet.length]:s._colorSet[A%s._colorSet.length];c=b.indexLabelPlacement||s.indexLabelPlacement||"outside";n=b.indexLabelTextAlign||
s.indexLabelTextAlign||"left";d=b.indexLabelBackgroundColor||s.indexLabelBackgroundColor||(t?"transparent":null);e=b.indexLabelFontColor||s.indexLabelFontColor||"#979797";g=m(b.indexLabelFontSize)?s.indexLabelFontSize:b.indexLabelFontSize;r=b.indexLabelFontStyle||s.indexLabelFontStyle||"normal";k=b.indexLabelFontFamily||s.indexLabelFontFamily||"arial";h=b.indexLabelFontWeight||s.indexLabelFontWeight||"normal";a=b.indexLabelLineColor||s.options.indexLabelLineColor||a;l="number"===typeof b.indexLabelLineThickness?
b.indexLabelLineThickness:"number"===typeof s.indexLabelLineThickness?s.indexLabelLineThickness:2;p=b.indexLabelLineDashType||s.indexLabelLineDashType||"solid";q="undefined"!==typeof b.indexLabelWrap?b.indexLabelWrap:"undefined"!==typeof s.indexLabelWrap?s.indexLabelWrap:!0;u=s.dataPointIds[A];x._eventManager.objectMap[u]={id:u,objectType:"dataPoint",dataPointIndex:A,dataSeriesIndex:0,funnelSection:R[s.reversed?y.length-1-A:A]};"inside"===s.indexLabelPlacement&&(B[A]=A!==ea?s.reversed?R[A].x2-R[A].x1:
R[A].x3-R[A].x4:R[A].x3-R[A].x6,20>B[A]&&(B[A]=A!==ea?s.reversed?R[A].x3-R[A].x4:R[A].x2-R[A].x1:R[A].x2-R[A].x1,B[A]/=2));u=b.indexLabelMaxWidth?b.indexLabelMaxWidth:s.options.indexLabelMaxWidth?s.indexLabelMaxWidth:B[A];if(u>B[A]||0>u)u=B[A];w[A]="inside"===s.indexLabelPlacement?q?Math.max(R[A].height,g):1.5*g:!1;z=x.getPercentAndTotal(s,b);if(s.indexLabelFormatter||b.indexLabelFormatter)J={chart:x.options,dataSeries:s,dataPoint:b,total:z.total,percent:z.percent};b=b.indexLabelFormatter?b.indexLabelFormatter(J):
b.indexLabel?x.replaceKeywordsWithValue(b.indexLabel,b,s,A):s.indexLabelFormatter?s.indexLabelFormatter(J):s.indexLabel?x.replaceKeywordsWithValue(s.indexLabel,b,s,A):b.label?b.label:"";0>=l&&(l=0);1E3>u&&1E3-u<v&&(u+=1E3-u);m(s.options.indexLabelMaxWidth)&&(s.indexLabelMaxWidth=m(s.indexLabelMaxWidth)?u:Math.max(u,s.indexLabelMaxWidth));P.roundRect||Aa(P);c=new la(P,{fontSize:g,fontFamily:k,fontColor:e,fontStyle:r,fontWeight:h,horizontalAlign:c,textAlign:n,backgroundColor:d,maxWidth:u,maxHeight:!1===
w[A]?q?4.28571429*g:1.5*g:w[A],text:b,padding:fa,textBaseline:"middle"});c.measureText();c.height=c.height===2*c.padding?0:c.height;c.width=c.width===2*c.padding?0:c.width;G.push({textBlock:c,id:s.reversed?C:A,isDirty:!1,lineColor:a,lineThickness:l,lineDashType:p,height:c.height<c.maxHeight?c.height:c.maxHeight,width:c.width<c.maxWidth?c.width:c.maxWidth})}}}function e(){var a,b,c,d,e,f=[];e=!1;c=0;for(var g,r=L-Y-Z/2,r=s.options.indexLabelMaxWidth?s.indexLabelMaxWidth>r?r:s.indexLabelMaxWidth:r,
k=G.length-1;0<=k;k--){g=y[G[k].id];c=G[k];d=c.textBlock;b=(a=q(k)<R.length?G[q(k)]:null)?a.textBlock:null;c=c.height;a&&d.y+c+fa>b.y&&(e=!0);c=g.indexLabelMaxWidth||r;if(c>r||0>c)c=r;f.push(c)}if(e)for(k=G.length-1;0<=k;k--)a=R[k],G[k].textBlock.maxWidth=f[f.length-(k+1)],G[k].textBlock.measureText(),G[k].textBlock.height=G[k].textBlock.height===2*G[k].textBlock.padding?0:G[k].textBlock.height,G[k].textBlock.width=G[k].textBlock.width===2*G[k].textBlock.padding?0:G[k].textBlock.width,G[k].textBlock.x=
L-r,c=G[k].textBlock.height<G[k].textBlock.maxHeight?G[k].textBlock.height:G[k].textBlock.maxHeight,e=G[k].textBlock.width<G[k].textBlock.maxWidth?G[k].textBlock.width:G[k].textBlock.maxWidth,G[k].height=c,G[k].width=e,c="undefined"!==typeof a.x5?(a.y2+a.y4)/2:(a.y2+a.y3)/2,G[k].textBlock.y=c-G[k].height/2,s.reversed?(G[k].textBlock.y+G[k].height>W+z&&(G[k].textBlock.y=W+z-G[k].height),G[k].textBlock.y<va-z&&(G[k].textBlock.y=va-z)):(G[k].textBlock.y<W-z&&(G[k].textBlock.y=W-z),G[k].textBlock.y+G[k].height>
va+z&&(G[k].textBlock.y=va+z-G[k].height))}function g(){var a,b,c,e;if("inside"!==s.indexLabelPlacement)for(var d=0;d<R.length;d++)0==G[d].textBlock.text.length?G[d].isDirty=!0:(a=R[d],c="undefined"!==typeof a.x5?(a.y2+a.y4)/2:(a.y2+a.y3)/2,b=s.reversed?"undefined"!==typeof a.x5?c>X?f(c).x2+1:(a.x2+a.x3)/2+1:(a.x2+a.x3)/2+1:"undefined"!==typeof a.x5?c<X?f(c).x2+1:(a.x4+a.x3)/2+1:(a.x2+a.x3)/2+1,G[d].textBlock.x=b+Z,G[d].textBlock.y=c-G[d].height/2,s.reversed?(G[d].textBlock.y+G[d].height>W+z&&(G[d].textBlock.y=
W+z-G[d].height),G[d].textBlock.y<va-z&&(G[d].textBlock.y=va-z)):(G[d].textBlock.y<W-z&&(G[d].textBlock.y=W-z),G[d].textBlock.y+G[d].height>va+z&&(G[d].textBlock.y=va+z-G[d].height)));else for(d=0;d<R.length;d++)0==G[d].textBlock.text.length?G[d].isDirty=!0:(a=R[d],b=a.height,c=G[d].height,e=G[d].width,b>=c?(b=d!=ea?(a.x4+a.x3)/2-e/2:(a.x5+a.x4)/2-e/2,c=d!=ea?(a.y1+a.y3)/2-c/2:(a.y1+a.y4)/2-c/2,G[d].textBlock.x=b,G[d].textBlock.y=c):G[d].isDirty=!0)}function r(){function a(b,c){var d;if(0>b||b>=G.length)return 0;
var e,f=G[b].textBlock;if(0>c){c*=-1;e=p(b);d=h(e,b);if(d>=c)return f.y-=c,c;if(0==b)return 0<d&&(f.y-=d),d;d+=a(e,-(c-d));0<d&&(f.y-=d);return d}e=q(b);d=h(b,e);if(d>=c)return f.y+=c,c;if(b==R.length-1)return 0<d&&(f.y+=d),d;d+=a(e,c-d);0<d&&(f.y+=d);return d}function b(){var a,d,e,f,g=0,k;f=(S-O+2*z)/l;k=l;for(var r,h=1;h<k;h++){e=h*f;for(var m=G.length-1;0<=m;m--)!G[m].isDirty&&(G[m].textBlock.y<e&&G[m].textBlock.y+G[m].height>e)&&(r=q(m),!(r>=G.length-1)&&G[m].textBlock.y+G[m].height+fa>G[r].textBlock.y&&
(G[m].textBlock.y=G[m].textBlock.y+G[m].height-e>e-G[m].textBlock.y?e+1:e-G[m].height-1))}for(r=R.length-1;0<r;r--)if(!G[r].isDirty){e=p(r);if(0>e&&(e=0,G[e].isDirty))break;if(G[r].textBlock.y<G[e].textBlock.y+G[e].height){d=d||r;f=r;for(k=0;G[f].textBlock.y<G[e].textBlock.y+G[e].height+fa;){a=a||G[f].textBlock.y+G[f].height;k+=G[f].height;k+=fa;f=e;if(0>=f){f=0;k+=G[f].height;break}e=p(f);if(0>e){f=0;k+=G[f].height;break}}if(f!=r){g=G[f].textBlock.y;a-=g;a=k-a;g=c(a,d,f);break}}}return g}function c(a,
b,d){var e=[],f=0,g=0;for(a=Math.abs(a);d<=b;d++)e.push(R[d]);e.sort(function(a,b){return a.height-b.height});for(d=0;d<e.length;d++)if(b=e[d],f<a)g++,f+=G[b.id].height+fa,G[b.id].textBlock.text="",G[b.id].indexLabelText="",G[b.id].isDirty=!0,G[b.id].textBlock.measureText();else break;return g}for(var d,e,f,g,k,r,l=1,m=0;m<2*l;m++){for(var n=G.length-1;0<=n&&!(0<=p(n)&&p(n),f=G[n],g=f.textBlock,r=(k=q(n)<R.length?G[q(n)]:null)?k.textBlock:null,d=+f.height.toFixed(6),e=+g.y.toFixed(6),!f.isDirty&&
(k&&e+d+fa>+r.y.toFixed(6))&&(d=g.y+d+fa-r.y,e=a(n,-d),e<d&&(0<e&&(d-=e),e=a(q(n),d),e!=d)));n--);b()}}function h(a,b){return(b<R.length?G[b].textBlock.y:s.reversed?W+z:va+z)-(0>a?s.reversed?va-z:W-z:G[a].textBlock.y+G[a].height+fa)}function n(a,b,c){var d,e,f,r=[],h=z,p=[];-1!==b&&(0<=aa.indexOf(b)?(e=aa.indexOf(b),aa.splice(e,1)):(aa.push(b),aa=aa.sort(function(a,b){return a-b})));if(0===aa.length)r=ka;else{e=z*(1!=aa.length||0!=aa[0]&&aa[0]!=R.length-1?2:1)/k();for(var m=0;m<R.length;m++){if(1==
aa.length&&0==aa[0]){if(0===m){r.push(ka[m]);d=h;continue}}else 0===m&&(d=-1*h);r.push(ka[m]+d);if(0<=aa.indexOf(m)||m<R.length&&0<=aa.indexOf(m+1))d+=e}}f=function(){for(var a=[],b=0;b<R.length;b++)a.push(r[b]-R[b].y1);return a}();var q={startTime:(new Date).getTime(),duration:c||500,easingFunction:function(a,b,c,d){return N.easing.easeOutQuart(a,b,c,d)},changeSection:function(a){for(var b,c,d=0;d<R.length;d++)b=f[d],c=R[d],b*=a,"undefined"===typeof p[d]&&(p[d]=0),0>p&&(p*=-1),c.y1+=b-p[d],c.y2+=
b-p[d],c.y3+=b-p[d],c.y4+=b-p[d],c.y5&&(c.y5+=b-p[d],c.y6+=b-p[d]),p[d]=b}};a._animator.animate(0,c,function(c){var d=a.plotArea.ctx||a.ctx;ja=!0;d.clearRect(v.x1,v.y1,v.x2-v.x1,v.y2-v.y1);d.fillStyle=a.backgroundColor;d.fillRect(v.x1,v.y1,v.width,v.height);q.changeSection(c,b);var e={};e.dataSeries=s;e.dataPoint=s.reversed?s.dataPoints[y.length-1-b]:s.dataPoints[b];e.index=s.reversed?y.length-1-b:b;a.toolTip.highlightObjects([e]);for(e=0;e<R.length;e++)ta(d,R[e],s.fillOpacity);J(d);K&&("inside"!==
s.indexLabelPlacement?l(d):g(),w(d));1<=c&&(ja=!1)},null,N.easing.easeOutQuart)}function k(){for(var a=0,b=0;b<R.length-1;b++)(0<=aa.indexOf(b)||0<=aa.indexOf(b+1))&&a++;return a}function l(a){for(var b,c,d,e,g=0;g<R.length;g++)e=1===G[g].lineThickness%2?0.5:0,c=((R[g].y2+R[g].y4)/2<<0)+e,b=f(c).x2-1,d=G[g].textBlock.x,e=(G[g].textBlock.y+G[g].height/2<<0)+e,G[g].isDirty||0==G[g].lineThickness||(a.strokeStyle=G[g].lineColor,a.lineWidth=G[g].lineThickness,a.setLineDash&&a.setLineDash(H(G[g].lineDashType,
G[g].lineThickness)),a.beginPath(),a.moveTo(b,c),a.lineTo(d,e),a.stroke())}function p(a){for(a-=1;-1<=a&&-1!=a&&G[a].isDirty;a--);return a}function q(a){for(a+=1;a<=R.length&&a!=R.length&&G[a].isDirty;a++);return a}function f(a){for(var b,c=0;c<y.length;c++)if(R[c].y1<a&&R[c].y4>a){b=R[c];break}return b?(a=b.y6?a>b.y6?b.x3+(b.x4-b.x3)/(b.y4-b.y3)*(a-b.y3):b.x2+(b.x3-b.x2)/(b.y3-b.y2)*(a-b.y2):b.x2+(b.x3-b.x2)/(b.y3-b.y2)*(a-b.y2),{x1:a,x2:a}):-1}function w(a){for(var b=0;b<R.length;b++)G[b].isDirty||
(a&&(G[b].textBlock.ctx=a),G[b].textBlock.y+=G[b].textBlock._lineHeight/2,G[b].textBlock.render(!0),G[b].textBlock.y-=G[b].textBlock._lineHeight/2)}function J(a){x.plotArea.layoutManager.reset();a.roundRect||Aa(a);x.title&&(x.title.dockInsidePlotArea||"center"===x.title.horizontalAlign&&"center"===x.title.verticalAlign)&&(x.title.ctx=a,x.title.render());if(x.subtitles)for(var b=0;b<x.subtitles.length;b++){var c=x.subtitles[b];if(c.dockInsidePlotArea||"center"===c.horizontalAlign&&"center"===c.verticalAlign)x.subtitles.ctx=
a,c.render()}x.legend&&(x.legend.dockInsidePlotArea||"center"===x.legend.horizontalAlign&&"center"===x.legend.verticalAlign)&&(x.legend.ctx=a,x.legend.setLayout(),x.legend.render());xa.fNg&&xa.fNg(x)}var x=this;if(!(0>=a.dataSeriesIndexes.length)){for(var s=this.data[a.dataSeriesIndexes[0]],y=s.dataPoints,v=this.plotArea,z=0.025*v.width,A=0.01*v.width,C=0,D=v.height-2*z,E=Math.min(v.width-2*A,2.8*v.height),K=!1,T=0;T<y.length;T++)if(!K&&("undefined"!==typeof y[T].indexLabel&&null!==y[T].indexLabel&&
0<y[T].indexLabel.toString().length)&&(K=!0),!K&&("undefined"!==typeof y[T].label&&null!==y[T].label&&0<y[T].label.toString().length)&&(K=!0),!K&&"function"===typeof s.indexLabelFormatter||"function"===typeof y[T].indexLabelFormatter)K=!0;K=K||"undefined"!==typeof s.indexLabel&&null!==s.indexLabel&&0<s.indexLabel.toString().length;"inside"!==s.indexLabelPlacement&&K||(A=(v.width-0.75*E)/2);var T=v.x1+A,L=v.x2-A,O=v.y1+z,S=v.y2-z,P=a.targetCanvasCtx||this.plotArea.ctx||this.ctx;if(0!=s.length&&(s.dataPoints&&
s.visible)&&0!==y.length){var Q,F;a=75*E/100;var Z=30*(L-a)/100;"funnel"===s.type?(Q=m(s.options.neckHeight)?0.35*D:s.neckHeight,F=m(s.options.neckWidth)?0.25*a:s.neckWidth,"string"===typeof Q&&Q.match(/%$/)?(Q=parseInt(Q),Q=Q*D/100):Q=parseInt(Q),"string"===typeof F&&F.match(/%$/)?(F=parseInt(F),F=F*a/100):F=parseInt(F),Q>D?Q=D:0>=Q&&(Q=0),F>a?F=a-0.5:0>=F&&(F=0)):"pyramid"===s.type&&(F=Q=0,s.reversed=s.reversed?!1:!0);var A=T+a/2,U=T,Y=T+a,W=s.reversed?S:O,M=A-F/2,$=A+F/2,X=s.reversed?O+Q:S-Q,va=
s.reversed?O:S;a=[];var A=[],R=[],E=[],ba=O,ea,da=(X-W)/(M-U),ga=-da,T="area"===(s.valueRepresents?s.valueRepresents:"height")?c():d();if(-1!==T){if(s.reversed)for(E.push(ba),F=T.length-1;0<F;F--)ba+=T[F],E.push(ba);else for(F=0;F<T.length;F++)ba+=T[F],E.push(ba);if(s.reversed)for(F=0;F<T.length;F++)E[F]<X?(a.push(M),A.push($),ea=F):(a.push((E[F]-W+da*U)/da),A.push((E[F]-W+ga*Y)/ga));else for(F=0;F<T.length;F++)E[F]<X?(a.push((E[F]-W+da*U)/da),A.push((E[F]-W+ga*Y)/ga),ea=F):(a.push(M),A.push($));
for(F=0;F<T.length-1;F++)ba=s.reversed?y[y.length-1-F].color?y[y.length-1-F].color:s._colorSet[(y.length-1-F)%s._colorSet.length]:y[F].color?y[F].color:s._colorSet[F%s._colorSet.length],F===ea?R.push({x1:a[F],y1:E[F],x2:A[F],y2:E[F],x3:$,y3:X,x4:A[F+1],y4:E[F+1],x5:a[F+1],y5:E[F+1],x6:M,y6:X,id:F,height:E[F+1]-E[F],color:ba}):R.push({x1:a[F],y1:E[F],x2:A[F],y2:E[F],x3:A[F+1],y3:E[F+1],x4:a[F+1],y4:E[F+1],id:F,height:E[F+1]-E[F],color:ba});var fa=2,G=[],ja=!1,aa=[],ka=[],T=!1;a=a=0;Ea(aa);for(F=0;F<
y.length;F++)y[F].exploded&&(T=!0,s.reversed?aa.push(y.length-1-F):aa.push(F));P.clearRect(v.x1,v.y1,v.width,v.height);P.fillStyle=x.backgroundColor;P.fillRect(v.x1,v.y1,v.width,v.height);if(K&&s.visible&&(b(),g(),"inside"!==s.indexLabelPlacement)){e();r();for(F=0;F<y.length;F++)G[F].isDirty||(a=G[F].textBlock.x+G[F].width,a=(L-a)/2,0==F&&(C=a),C>a&&(C=a));for(F=0;F<R.length;F++)R[F].x1+=C,R[F].x2+=C,R[F].x3+=C,R[F].x4+=C,R[F].x5&&(R[F].x5+=C,R[F].x6+=C),G[F].textBlock.x+=C}for(F=0;F<R.length;F++)C=
R[F],ta(P,C,s.fillOpacity),ka.push(C.y1);J(P);K&&s.visible&&("inside"===s.indexLabelPlacement||x.animationEnabled||l(P),x.animationEnabled||w());if(!K)for(F=0;F<y.length;F++)C=s.dataPointIds[F],a={id:C,objectType:"dataPoint",dataPointIndex:F,dataSeriesIndex:0,funnelSection:R[s.reversed?y.length-1-F:F]},x._eventManager.objectMap[C]=a;!x.animationEnabled&&T?n(x,-1,0):x.animationEnabled&&!x.animatedRender&&n(x,-1,0);this.funnelPyramidClickHandler=function(a){var b=-1;if(!ja&&!x.isAnimating&&(m(a.dataSeries.explodeOnClick)||
a.dataSeries.explodeOnClick)&&(b=s.reversed?y.length-1-a.dataPointIndex:a.dataPointIndex,0<=b)){a=b;if("funnel"===s.type||"pyramid"===s.type)s.reversed?y[y.length-1-a].exploded=y[y.length-1-a].exploded?!1:!0:y[a].exploded=y[a].exploded?!1:!0;n(x,b,500)}};return{source:P,dest:this.plotArea.ctx,animationCallback:function(a,b){N.fadeInAnimation(a,b);1<=a&&(n(x,-1,500),J(x.plotArea.ctx||x.ctx))},easingFunction:N.easing.easeInQuad,animationBase:0}}}}};n.prototype.requestAnimFrame=function(){return window.requestAnimationFrame||
window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(a){window.setTimeout(a,1E3/60)}}();n.prototype.cancelRequestAnimFrame=window.cancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||clearTimeout;n.prototype.set=function(a,d,c){c="undefined"===typeof c?!0:c;"options"===a?(this.options=d,
c&&this.render()):n.base.set.call(this,a,d,c)};n.prototype.exportChart=function(a){a="undefined"===typeof a?{}:a;var d=a.format?a.format:"png",c=a.fileName?a.fileName:this.exportFileName;if(a.toDataURL)return this.canvas.toDataURL("image/"+d);var b=this.canvas;if(b&&d&&c){c=c+"."+d;a="image/"+d;var b=b.toDataURL(a),e=!1,g=document.createElement("a");g.download=c;g.href=b;if("undefined"!==typeof Blob&&new Blob){for(var r=b.replace(/^data:[a-z\/]*;base64,/,""),r=atob(r),h=new ArrayBuffer(r.length),
h=new Uint8Array(h),m=0;m<r.length;m++)h[m]=r.charCodeAt(m);d=new Blob([h.buffer],{type:"image/"+d});try{window.navigator.msSaveBlob(d,c),e=!0}catch(k){g.dataset.downloadurl=[a,g.download,g.href].join(":"),g.href=window.URL.createObjectURL(d)}}if(!e)try{event=document.createEvent("MouseEvents"),event.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),g.dispatchEvent?g.dispatchEvent(event):g.fireEvent&&g.fireEvent("onclick")}catch(l){d=window.open(),d.document.write("<img src='"+b+"'></img><div>Please right click on the image and save it to your device</div>"),
d.document.close()}}};n.prototype.print=function(){var a=this.exportChart({toDataURL:!0}),d=document.createElement("iframe");d.setAttribute("class","canvasjs-chart-print-frame");Y(d,{position:"absolute",width:"100%",border:"0px",margin:"0px 0px 0px 0px",padding:"0px 0px 0px 0px"});d.style.height=this.height+"px";this._canvasJSContainer.appendChild(d);var c=this,b=d.contentWindow||d.contentDocument.document||d.contentDocument;b.document.open();b.document.write('<!DOCTYPE HTML>\n<html><body><img src="'+
a+'"/><body/></html>');b.document.body&&b.document.body.style&&(b.document.body.style.margin="0px 0px 0px 0px",b.document.body.style.padding="0px 0px 0px 0px");b.document.close();setTimeout(function(){b.focus();b.print();setTimeout(function(){c._canvasJSContainer.removeChild(d)},1E3)},500)};n.prototype.getPercentAndTotal=function(a,d){var c=null,b=null,e=c=null;if(0<=a.type.indexOf("stacked"))b=0,c=d.x.getTime?d.x.getTime():d.x,c in a.plotUnit.yTotals&&(b=a.plotUnit.yTotals[c],c=a.plotUnit.yAbsTotals[c],
e=isNaN(d.y)?0:0===c?0:100*(d.y/c));else if("pie"===a.type||"doughnut"===a.type||"funnel"===a.type||"pyramid"===a.type){for(c=b=0;c<a.dataPoints.length;c++)isNaN(a.dataPoints[c].y)||(b+=a.dataPoints[c].y);e=isNaN(d.y)?0:100*(d.y/b)}return{percent:e,total:b}};n.prototype.replaceKeywordsWithValue=function(a,d,c,b,e){var g=this;e="undefined"===typeof e?0:e;if((0<=c.type.indexOf("stacked")||"pie"===c.type||"doughnut"===c.type||"funnel"===c.type||"pyramid"===c.type)&&(0<=a.indexOf("#percent")||0<=a.indexOf("#total"))){var r=
"#percent",h="#total",m=this.getPercentAndTotal(c,d),h=isNaN(m.total)?h:m.total,r=isNaN(m.percent)?r:m.percent;do{m="";if(c.percentFormatString)m=c.percentFormatString;else{var m="#,##0.",k=Math.max(Math.ceil(Math.log(1/Math.abs(r))/Math.LN10),2);if(isNaN(k)||!isFinite(k))k=2;for(var l=0;l<k;l++)m+="#";c.percentFormatString=m}a=a.replace("#percent",ga(r,m,g._cultureInfo));a=a.replace("#total",ga(h,c.yValueFormatString?c.yValueFormatString:"#,##0.########",g._cultureInfo))}while(0<=a.indexOf("#percent")||
0<=a.indexOf("#total"))}return a.replace(/\{.*?\}|"[^"]*"|'[^']*'/g,function(a){if('"'===a[0]&&'"'===a[a.length-1]||"'"===a[0]&&"'"===a[a.length-1])return a.slice(1,a.length-1);a=Ha(a.slice(1,a.length-1));a=a.replace("#index",e);var k=null;try{var f=a.match(/(.*?)\s*\[\s*(.*?)\s*\]/);f&&0<f.length&&(k=Ha(f[2]),a=Ha(f[1]))}catch(r){}f=null;if("color"===a)return"waterfall"===c.type?d.color?d.color:0<d.y?c.risingColor:c.fallingColor:"error"===c.type?c.color?c.color:c._colorSet[k%c._colorSet.length]:
d.color?d.color:c.color?c.color:c._colorSet[b%c._colorSet.length];if(d.hasOwnProperty(a))f=d;else if(c.hasOwnProperty(a))f=c;else return"";f=f[a];null!==k&&(f=f[k]);return"x"===a?(c.axisX&&"dateTime"===c.axisX.valueType||"dateTime"===c.xValueType||d.x&&d.x.getTime)&&!c.axisX.logarithmic?Da(f,d.xValueFormatString?d.xValueFormatString:c.xValueFormatString?c.xValueFormatString:c.xValueFormatString=g.axisX&&g.axisX.autoValueFormatString?g.axisX.autoValueFormatString:"DD MMM YY",g._cultureInfo):ga(f,d.xValueFormatString?
d.xValueFormatString:c.xValueFormatString?c.xValueFormatString:c.xValueFormatString="#,##0.########",g._cultureInfo):"y"===a?ga(f,d.yValueFormatString?d.yValueFormatString:c.yValueFormatString?c.yValueFormatString:c.yValueFormatString="#,##0.########",g._cultureInfo):"z"===a?ga(f,d.zValueFormatString?d.zValueFormatString:c.zValueFormatString?c.zValueFormatString:c.zValueFormatString="#,##0.########",g._cultureInfo):f})};qa(E,L);E.prototype.setLayout=function(){var a=this.dockInsidePlotArea?this.chart.plotArea:
this.chart,d=a.layoutManager.getFreeSpace(),c=null,b=0,e=0,g=0,r=0,h=this.markerMargin=this.chart.options.legend&&!m(this.chart.options.legend.markerMargin)?this.chart.options.legend.markerMargin:0.3*this.fontSize;this.height=0;var n=[],k=[];if("top"===this.verticalAlign||"bottom"===this.verticalAlign)this.orientation="horizontal",c=this.verticalAlign,g=this.maxWidth=null!==this.maxWidth?this.maxWidth:d.width,r=this.maxHeight=null!==this.maxHeight?this.maxHeight:0.5*d.height;else if("center"===this.verticalAlign){this.orientation=
"vertical";if("left"===this.horizontalAlign||"center"===this.horizontalAlign||"right"===this.horizontalAlign)c=this.horizontalAlign;g=this.maxWidth=null!==this.maxWidth?this.maxWidth:0.5*d.width;r=this.maxHeight=null!==this.maxHeight?this.maxHeight:d.height}this.errorMarkerColor=[];for(var l=0;l<this.dataSeries.length;l++){var p=this.dataSeries[l];if(p.dataPoints&&p.dataPoints.length)if("pie"!==p.type&&"doughnut"!==p.type&&"funnel"!==p.type&&"pyramid"!==p.type){var q=p.legendMarkerType=p.legendMarkerType?
p.legendMarkerType:"line"!==p.type&&"stepLine"!==p.type&&"spline"!==p.type&&"scatter"!==p.type&&"bubble"!==p.type||!p.markerType?"error"===p.type&&p._linkedSeries?p._linkedSeries.legendMarkerType?p._linkedSeries.legendMarkerType:Q.getDefaultLegendMarker(p._linkedSeries.type):Q.getDefaultLegendMarker(p.type):p.markerType,f=p.legendText?p.legendText:this.itemTextFormatter?this.itemTextFormatter({chart:this.chart,legend:this.options,dataSeries:p,dataPoint:null}):p.name,t=p.legendMarkerColor=p.legendMarkerColor?
p.legendMarkerColor:p.markerColor?p.markerColor:"error"===p.type?m(p.whiskerColor)?p._colorSet[0]:p.whiskerColor:p._colorSet[0],w=p.markerSize||"line"!==p.type&&"stepLine"!==p.type&&"spline"!==p.type?0.75*this.lineHeight:0,x=p.legendMarkerBorderColor?p.legendMarkerBorderColor:p.markerBorderColor,s=p.legendMarkerBorderThickness?p.legendMarkerBorderThickness:p.markerBorderThickness?Math.max(1,Math.round(0.2*w)):0;"error"===p.type&&this.errorMarkerColor.push(t);f=this.chart.replaceKeywordsWithValue(f,
p.dataPoints[0],p,l);q={markerType:q,markerColor:t,text:f,textBlock:null,chartType:p.type,markerSize:w,lineColor:p._colorSet[0],dataSeriesIndex:p.index,dataPointIndex:null,markerBorderColor:x,markerBorderThickness:s};n.push(q)}else for(var y=0;y<p.dataPoints.length;y++){var v=p.dataPoints[y],q=v.legendMarkerType?v.legendMarkerType:p.legendMarkerType?p.legendMarkerType:Q.getDefaultLegendMarker(p.type),f=v.legendText?v.legendText:p.legendText?p.legendText:this.itemTextFormatter?this.itemTextFormatter({chart:this.chart,
legend:this.options,dataSeries:p,dataPoint:v}):v.name?v.name:"DataPoint: "+(y+1),t=v.legendMarkerColor?v.legendMarkerColor:p.legendMarkerColor?p.legendMarkerColor:v.color?v.color:p.color?p.color:p._colorSet[y%p._colorSet.length],w=0.75*this.lineHeight,x=v.legendMarkerBorderColor?v.legendMarkerBorderColor:p.legendMarkerBorderColor?p.legendMarkerBorderColor:v.markerBorderColor?v.markerBorderColor:p.markerBorderColor,s=v.legendMarkerBorderThickness?v.legendMarkerBorderThickness:p.legendMarkerBorderThickness?
p.legendMarkerBorderThickness:v.markerBorderThickness||p.markerBorderThickness?Math.max(1,Math.round(0.2*w)):0,f=this.chart.replaceKeywordsWithValue(f,v,p,y),q={markerType:q,markerColor:t,text:f,textBlock:null,chartType:p.type,markerSize:w,dataSeriesIndex:l,dataPointIndex:y,markerBorderColor:x,markerBorderThickness:s};(v.showInLegend||p.showInLegend&&!1!==v.showInLegend)&&n.push(q)}}!0===this.reversed&&n.reverse();if(0<n.length){p=null;f=v=y=0;v=null!==this.itemWidth?null!==this.itemMaxWidth?Math.min(this.itemWidth,
this.itemMaxWidth,g):this.itemMaxWidth=Math.min(this.itemWidth,g):null!==this.itemMaxWidth?Math.min(this.itemMaxWidth,g):this.itemMaxWidth=g;w=0===w?0.75*this.lineHeight:w;v=(this.itemMaxWidth?this.itemMaxWidth:v)-(w+h);for(l=0;l<n.length;l++){q=n[l];t=v;if("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType)t-=2*0.1*this.lineHeight;if(!(0>=r||"undefined"===typeof r||0>=t||"undefined"===typeof t))if("horizontal"===this.orientation){q.textBlock=new la(this.ctx,{x:0,y:0,maxWidth:t,
maxHeight:this.itemWrap?r:this.lineHeight,angle:0,text:q.text,horizontalAlign:"left",fontSize:this.fontSize,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontColor:this.fontColor,fontStyle:this.fontStyle,textBaseline:"middle"});q.textBlock.measureText();null!==this.itemWidth&&(q.textBlock.width=this.itemWidth-(w+h+("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType?2*0.1*this.lineHeight:0)));if(!p||p.width+Math.round(q.textBlock.width+w+h+(0===p.width?0:this.horizontalSpacing)+
("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType?2*0.1*this.lineHeight:0))>g)p={items:[],width:0},k.push(p),this.height+=f,f=0;f=Math.max(f,q.textBlock.height?q.textBlock.height:this.lineHeight);q.textBlock.x=p.width;q.textBlock.y=0;p.width+=Math.round(q.textBlock.width+w+h+(0===p.width?0:this.horizontalSpacing)+("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType?2*0.1*this.lineHeight:0));p.items.push(q);this.width=Math.max(p.width,this.width)}else q.textBlock=
new la(this.ctx,{x:0,y:0,maxWidth:v,maxHeight:!0===this.itemWrap?r:1.5*this.fontSize,angle:0,text:q.text,horizontalAlign:"left",fontSize:this.fontSize,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontColor:this.fontColor,fontStyle:this.fontStyle,textBaseline:"middle"}),q.textBlock.measureText(),null!==this.itemWidth&&(q.textBlock.width=this.itemWidth-(w+h+("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType?2*0.1*this.lineHeight:0))),this.height<r-this.lineHeight?(p={items:[],
width:0},k.push(p)):(p=k[y],y=(y+1)%k.length),p&&(this.height+=q.textBlock.height?q.textBlock.height:this.lineHeight,q.textBlock.x=p.width,q.textBlock.y=0,p.width+=Math.round(q.textBlock.width+w+h+(0===p.width?0:this.horizontalSpacing)+("line"===q.chartType||"spline"===q.chartType||"stepLine"===q.chartType?2*0.1*this.lineHeight:0)),p.items.push(q),this.width=Math.max(p.width,this.width))}this.height=!1===this.itemWrap?k.length*this.lineHeight:this.height+f;this.height=Math.min(r,this.height);this.width=
Math.min(g,this.width)}"top"===this.verticalAlign?(e="left"===this.horizontalAlign?d.x1:"right"===this.horizontalAlign?d.x2-this.width:d.x1+d.width/2-this.width/2,b=d.y1):"center"===this.verticalAlign?(e="left"===this.horizontalAlign?d.x1:"right"===this.horizontalAlign?d.x2-this.width:d.x1+d.width/2-this.width/2,b=d.y1+d.height/2-this.height/2):"bottom"===this.verticalAlign&&(e="left"===this.horizontalAlign?d.x1:"right"===this.horizontalAlign?d.x2-this.width:d.x1+d.width/2-this.width/2,b=d.y2-this.height);
this.items=n;for(l=0;l<this.items.length;l++)q=n[l],q.id=++this.chart._eventManager.lastObjectId,this.chart._eventManager.objectMap[q.id]={id:q.id,objectType:"legendItem",legendItemIndex:l,dataSeriesIndex:q.dataSeriesIndex,dataPointIndex:q.dataPointIndex};this.markerSize=w;this.rows=k;0<n.length&&a.layoutManager.registerSpace(c,{width:this.width+2+2,height:this.height+5+5});this.bounds={x1:e,y1:b,x2:e+this.width,y2:b+this.height}};E.prototype.render=function(){var a=this.bounds.x1,d=this.bounds.y1,
c=this.markerMargin,b=this.maxWidth,e=this.maxHeight,g=this.markerSize,r=this.rows;(0<this.borderThickness&&this.borderColor||this.backgroundColor)&&this.ctx.roundRect(a,d,this.width,this.height,this.cornerRadius,this.borderThickness,this.backgroundColor,this.borderColor);for(var h=0,m=0;m<r.length;m++){for(var k=r[m],l=0,p=0;p<k.items.length;p++){var q=k.items[p],f=q.textBlock.x+a+(0===p?0.2*g:this.horizontalSpacing),n=d+h,t=f;this.chart.data[q.dataSeriesIndex].visible||(this.ctx.globalAlpha=0.5);
this.ctx.save();this.ctx.beginPath();this.ctx.rect(a,d,b,Math.max(e-e%this.lineHeight,0));this.ctx.clip();if("line"===q.chartType||"stepLine"===q.chartType||"spline"===q.chartType)this.ctx.strokeStyle=q.lineColor,this.ctx.lineWidth=Math.ceil(this.lineHeight/8),this.ctx.beginPath(),this.ctx.moveTo(f-0.1*this.lineHeight,n+this.lineHeight/2),this.ctx.lineTo(f+0.85*this.lineHeight,n+this.lineHeight/2),this.ctx.stroke(),t-=0.1*this.lineHeight;if("error"===q.chartType){this.ctx.strokeStyle=this.errorMarkerColor[0];
this.ctx.lineWidth=g/8;this.ctx.beginPath();var x=f-0.08*this.lineHeight+0.1*this.lineHeight,s=n+0.15*this.lineHeight,y=0.7*this.lineHeight,v=y+0.02*this.lineHeight;this.ctx.moveTo(x,s);this.ctx.lineTo(x+y,s);this.ctx.stroke();this.ctx.beginPath();this.ctx.moveTo(x+y/2,s);this.ctx.lineTo(x+y/2,s+v);this.ctx.stroke();this.ctx.beginPath();this.ctx.moveTo(x,s+v);this.ctx.lineTo(x+y,s+v);this.ctx.stroke();this.errorMarkerColor.shift()}X.drawMarker(f+g/2,n+this.lineHeight/2,this.ctx,q.markerType,"error"===
q.chartType||"line"===q.chartType||"spline"===q.chartType?q.markerSize/2:q.markerSize,q.markerColor,q.markerBorderColor,q.markerBorderThickness);q.textBlock.x=f+c+g;if("line"===q.chartType||"stepLine"===q.chartType||"spline"===q.chartType)q.textBlock.x+=0.1*this.lineHeight;q.textBlock.y=Math.round(n+this.lineHeight/2);q.textBlock.render(!0);this.ctx.restore();l=0<p?Math.max(l,q.textBlock.height?q.textBlock.height:this.lineHeight):q.textBlock.height?q.textBlock.height:this.lineHeight;this.chart.data[q.dataSeriesIndex].visible||
(this.ctx.globalAlpha=1);f=Z(q.id);this.ghostCtx.fillStyle=f;this.ghostCtx.beginPath();this.ghostCtx.fillRect(t,q.textBlock.y-this.lineHeight/2,q.textBlock.x+q.textBlock.width-t,q.textBlock.height?q.textBlock.height:this.lineHeight);q.x1=this.chart._eventManager.objectMap[q.id].x1=t;q.y1=this.chart._eventManager.objectMap[q.id].y1=q.textBlock.y-this.lineHeight/2;q.x2=this.chart._eventManager.objectMap[q.id].x2=q.textBlock.x+q.textBlock.width;q.y2=this.chart._eventManager.objectMap[q.id].y2=q.textBlock.y+
(q.textBlock.height?q.textBlock.height:this.lineHeight)-this.lineHeight/2}h+=l}};qa(Q,L);Q.prototype.getDefaultAxisPlacement=function(){var a=this.type;if("column"===a||"line"===a||"stepLine"===a||"spline"===a||"area"===a||"stepArea"===a||"splineArea"===a||"stackedColumn"===a||"stackedLine"===a||"bubble"===a||"scatter"===a||"stackedArea"===a||"stackedColumn100"===a||"stackedLine100"===a||"stackedArea100"===a||"candlestick"===a||"ohlc"===a||"rangeColumn"===a||"rangeArea"===a||"rangeSplineArea"===a||
"boxAndWhisker"===a||"waterfall"===a)return"normal";if("bar"===a||"stackedBar"===a||"stackedBar100"===a||"rangeBar"===a)return"xySwapped";if("pie"===a||"doughnut"===a||"funnel"===a||"pyramid"===a)return"none";"error"!==a&&window.console.log("Unknown Chart Type: "+a);return null};Q.getDefaultLegendMarker=function(a){if("column"===a||"stackedColumn"===a||"stackedLine"===a||"bar"===a||"stackedBar"===a||"stackedBar100"===a||"bubble"===a||"scatter"===a||"stackedColumn100"===a||"stackedLine100"===a||"stepArea"===
a||"candlestick"===a||"ohlc"===a||"rangeColumn"===a||"rangeBar"===a||"rangeArea"===a||"rangeSplineArea"===a||"boxAndWhisker"===a||"waterfall"===a)return"square";if("line"===a||"stepLine"===a||"spline"===a||"pie"===a||"doughnut"===a)return"circle";if("area"===a||"splineArea"===a||"stackedArea"===a||"stackedArea100"===a||"funnel"===a||"pyramid"===a)return"triangle";if("error"===a)return"none";window.console.log("Unknown Chart Type: "+a);return null};Q.prototype.getDataPointAtX=function(a,d){if(!this.dataPoints||
0===this.dataPoints.length)return null;var c={dataPoint:null,distance:Infinity,index:NaN},b=null,e=0,g=0,r=1,h=Infinity,m=0,k=0,l=0;"none"!==this.chart.plotInfo.axisPlacement&&(this.axisX.logarithmic?(l=Math.log(this.dataPoints[this.dataPoints.length-1].x/this.dataPoints[0].x),l=1<l?Math.min(Math.max((this.dataPoints.length-1)/l*Math.log(a/this.dataPoints[0].x)>>0,0),this.dataPoints.length):0):(l=this.dataPoints[this.dataPoints.length-1].x-this.dataPoints[0].x,l=0<l?Math.min(Math.max((this.dataPoints.length-
1)/l*(a-this.dataPoints[0].x)>>0,0),this.dataPoints.length):0));for(;;){g=0<r?l+e:l-e;if(0<=g&&g<this.dataPoints.length){var b=this.dataPoints[g],p=this.axisX.logarithmic?b.x>a?b.x/a:a/b.x:Math.abs(b.x-a);p<c.distance&&(c.dataPoint=b,c.distance=p,c.index=g);b=p;b<=h?h=b:0<r?m++:k++;if(1E3<m&&1E3<k)break}else if(0>l-e&&l+e>=this.dataPoints.length)break;-1===r?(e++,r=1):r=-1}return d||(c.dataPoint.x.getTime?c.dataPoint.x.getTime():c.dataPoint.x)!==(a.getTime?a.getTime():a)?d&&null!==c.dataPoint?c:null:
c};Q.prototype.getDataPointAtXY=function(a,d,c){if(!this.dataPoints||0===this.dataPoints.length||a<this.chart.plotArea.x1||a>this.chart.plotArea.x2||d<this.chart.plotArea.y1||d>this.chart.plotArea.y2)return null;c=c||!1;var b=[],e=0,g=0,r=1,h=!1,n=Infinity,k=0,l=0,p=0;if("none"!==this.chart.plotInfo.axisPlacement)if(p=(this.chart.axisX[0]?this.chart.axisX[0]:this.chart.axisX2[0]).getXValueAt({x:a,y:d}),this.axisX.logarithmic)var q=Math.log(this.dataPoints[this.dataPoints.length-1].x/this.dataPoints[0].x),
p=1<q?Math.min(Math.max((this.dataPoints.length-1)/q*Math.log(p/this.dataPoints[0].x)>>0,0),this.dataPoints.length):0;else q=this.dataPoints[this.dataPoints.length-1].x-this.dataPoints[0].x,p=0<q?Math.min(Math.max((this.dataPoints.length-1)/q*(p-this.dataPoints[0].x)>>0,0),this.dataPoints.length):0;for(;;){g=0<r?p+e:p-e;if(0<=g&&g<this.dataPoints.length){var q=this.chart._eventManager.objectMap[this.dataPointIds[g]],f=this.dataPoints[g],t=null;if(q){switch(this.type){case "column":case "stackedColumn":case "stackedColumn100":case "bar":case "stackedBar":case "stackedBar100":case "rangeColumn":case "rangeBar":case "waterfall":case "error":a>=
q.x1&&(a<=q.x2&&d>=q.y1&&d<=q.y2)&&(b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:Math.min(Math.abs(q.x1-a),Math.abs(q.x2-a),Math.abs(q.y1-d),Math.abs(q.y2-d))}),h=!0);break;case "line":case "stepLine":case "spline":case "area":case "stepArea":case "stackedArea":case "stackedArea100":case "splineArea":case "scatter":var w=oa("markerSize",f,this)||4,x=c?20:w,t=Math.sqrt(Math.pow(q.x1-a,2)+Math.pow(q.y1-d,2));t<=x&&b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:t});q=
Math.abs(q.x1-a);q<=n?n=q:0<r?k++:l++;t<=w/2&&(h=!0);break;case "rangeArea":case "rangeSplineArea":w=oa("markerSize",f,this)||4;x=c?20:w;t=Math.min(Math.sqrt(Math.pow(q.x1-a,2)+Math.pow(q.y1-d,2)),Math.sqrt(Math.pow(q.x1-a,2)+Math.pow(q.y2-d,2)));t<=x&&b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:t});q=Math.abs(q.x1-a);q<=n?n=q:0<r?k++:l++;t<=w/2&&(h=!0);break;case "bubble":w=q.size;t=Math.sqrt(Math.pow(q.x1-a,2)+Math.pow(q.y1-d,2));t<=w/2&&(b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,
distance:t}),h=!0);break;case "pie":case "doughnut":w=q.center;x="doughnut"===this.type?q.percentInnerRadius*q.radius:0;t=Math.sqrt(Math.pow(w.x-a,2)+Math.pow(w.y-d,2));t<q.radius&&t>x&&(t=Math.atan2(d-w.y,a-w.x),0>t&&(t+=2*Math.PI),t=Number(((180*(t/Math.PI)%360+360)%360).toFixed(12)),w=Number(((180*(q.startAngle/Math.PI)%360+360)%360).toFixed(12)),x=Number(((180*(q.endAngle/Math.PI)%360+360)%360).toFixed(12)),0===x&&1<q.endAngle&&(x=360),w>=x&&(0!==f.y&&!m(f.y))&&(x+=360,t<w&&(t+=360)),t>w&&t<x&&
(b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:0}),h=!0));break;case "funnel":case "pyramid":t=q.funnelSection;d>t.y1&&d<t.y4&&(t.y6?d>t.y6?(g=t.x6+(t.x5-t.x6)/(t.y5-t.y6)*(d-t.y6),t=t.x3+(t.x4-t.x3)/(t.y4-t.y3)*(d-t.y3)):(g=t.x1+(t.x6-t.x1)/(t.y6-t.y1)*(d-t.y1),t=t.x2+(t.x3-t.x2)/(t.y3-t.y2)*(d-t.y2)):(g=t.x1+(t.x4-t.x1)/(t.y4-t.y1)*(d-t.y1),t=t.x2+(t.x3-t.x2)/(t.y3-t.y2)*(d-t.y2)),a>g&&a<t&&(b.push({dataPoint:f,dataPointIndex:q.dataPointIndex,dataSeries:this,distance:0}),h=!0));
break;case "boxAndWhisker":if(a>=q.x1-q.borderThickness/2&&a<=q.x2+q.borderThickness/2&&d>=q.y4-q.borderThickness/2&&d<=q.y1+q.borderThickness/2||Math.abs(q.x2-a+q.x1-a)<q.borderThickness&&d>=q.y1&&d<=q.y4)b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:Math.min(Math.abs(q.x1-a),Math.abs(q.x2-a),Math.abs(q.y2-d),Math.abs(q.y3-d))}),h=!0;break;case "candlestick":if(a>=q.x1-q.borderThickness/2&&a<=q.x2+q.borderThickness/2&&d>=q.y2-q.borderThickness/2&&d<=q.y3+q.borderThickness/2||Math.abs(q.x2-
a+q.x1-a)<q.borderThickness&&d>=q.y1&&d<=q.y4)b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:Math.min(Math.abs(q.x1-a),Math.abs(q.x2-a),Math.abs(q.y2-d),Math.abs(q.y3-d))}),h=!0;break;case "ohlc":if(Math.abs(q.x2-a+q.x1-a)<q.borderThickness&&d>=q.y2&&d<=q.y3||a>=q.x1&&a<=(q.x2+q.x1)/2&&d>=q.y1-q.borderThickness/2&&d<=q.y1+q.borderThickness/2||a>=(q.x1+q.x2)/2&&a<=q.x2&&d>=q.y4-q.borderThickness/2&&d<=q.y4+q.borderThickness/2)b.push({dataPoint:f,dataPointIndex:g,dataSeries:this,distance:Math.min(Math.abs(q.x1-
a),Math.abs(q.x2-a),Math.abs(q.y2-d),Math.abs(q.y3-d))}),h=!0}if(h||1E3<k&&1E3<l)break}}else if(0>p-e&&p+e>=this.dataPoints.length)break;-1===r?(e++,r=1):r=-1}a=null;for(d=0;d<b.length;d++)a?b[d].distance<=a.distance&&(a=b[d]):a=b[d];return a};Q.prototype.getMarkerProperties=function(a,d,c,b){var e=this.dataPoints,g=e[a].markerColor?e[a].markerColor:this.markerColor?this.markerColor:e[a].color?e[a].color:this.color?this.color:this._colorSet[a%this._colorSet.length],r=e[a].markerBorderColor?e[a].markerBorderColor:
this.markerBorderColor?this.markerBorderColor:null,h=m(e[a].markerBorderThickness)?this.markerBorderThickness?this.markerBorderThickness:null:e[a].markerBorderThickness,n=e[a].markerType?e[a].markerType:this.markerType;a=m(e[a].markerSize)?this.markerSize:e[a].markerSize;return{x:d,y:c,ctx:b,type:n,size:a,color:g,borderColor:r,borderThickness:h}};qa(D,L);D.prototype.createExtraLabelsForLog=function(a){a=(a||0)+1;if(!(5<a)){var d=this.logLabelValues[0]||this.intervalStartPosition;if(Math.log(this.range)/
Math.log(d/this.viewportMinimum)<this.noTicks-1){for(var c=D.getNiceNumber((d-this.viewportMinimum)/Math.min(Math.max(2,this.noTicks-this.logLabelValues.length),3),!0),b=Math.ceil(this.viewportMinimum/c)*c;b<d;b+=c)b<this.viewportMinimum||this.logLabelValues.push(b);this.logLabelValues.sort(Ra);this.createExtraLabelsForLog(a)}}};D.prototype.createLabels=function(){var a,d,c=0,b=0,e,g=0,r=0,b=0,b=this.interval,h=0,n,k=0.6*this.chart.height,l;a=!1;var p=this.scaleBreaks?this.scaleBreaks._appliedBreaks:
[],q=p.length?m(this.scaleBreaks.firstBreakIndex)?0:this.scaleBreaks.firstBreakIndex:0;if("axisX"!==this.type||"dateTime"!==this.valueType||this.logarithmic){e=this.viewportMaximum;if(this.labels){a=Math.ceil(b);for(var b=Math.ceil(this.intervalStartPosition),f=!1,c=b;c<this.viewportMaximum;c+=a)if(this.labels[c])f=!0;else{f=!1;break}f&&(this.interval=a,this.intervalStartPosition=b)}if(this.logarithmic&&!this.equidistantInterval)for(this.logLabelValues||(this.logLabelValues=[],this.createExtraLabelsForLog()),
b=0,f=q;b<this.logLabelValues.length;b++)if(c=this.logLabelValues[b],c<this.viewportMinimum)b++;else{for(;f<p.length&&c>p[f].endValue;f++);a=f<p.length&&c>=p[f].startValue&&c<=p[f].endValue;l=c;a||(a=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.options,value:l,label:this.labels[l]?this.labels[l]:null}):"axisX"===this.type&&this.labels[l]?this.labels[l]:ga(l,this.valueFormatString,this.chart._cultureInfo),a=new la(this.ctx,{x:0,y:0,maxWidth:g,maxHeight:r,angle:this.labelAngle,
text:this.prefix+a+this.suffix,backgroundColor:this.labelBackgroundColor,borderColor:this.labelBorderColor,cornerRadius:this.labelCornerRadius,textAlign:this.labelTextAlign,fontSize:this.labelFontSize,fontFamily:this.labelFontFamily,fontWeight:this.labelFontWeight,fontColor:this.labelFontColor,fontStyle:this.labelFontStyle,textBaseline:"middle",borderThickness:0}),this._labels.push({position:l,textBlock:a,effectiveHeight:null}))}f=q;for(c=this.intervalStartPosition;c<=e;c=parseFloat(1E-12>this.interval?
this.logarithmic&&this.equidistantInterval?c*Math.pow(this.logarithmBase,this.interval):c+this.interval:(this.logarithmic&&this.equidistantInterval?c*Math.pow(this.logarithmBase,this.interval):c+this.interval).toFixed(12))){for(;f<p.length&&c>p[f].endValue;f++);a=f<p.length&&c>=p[f].startValue&&c<=p[f].endValue;l=c;a||(a=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.options,value:l,label:this.labels[l]?this.labels[l]:null}):"axisX"===this.type&&this.labels[l]?this.labels[l]:
ga(l,this.valueFormatString,this.chart._cultureInfo),a=new la(this.ctx,{x:0,y:0,maxWidth:g,maxHeight:r,angle:this.labelAngle,text:this.prefix+a+this.suffix,textAlign:this.labelTextAlign,backgroundColor:this.labelBackgroundColor,borderColor:this.labelBorderColor,borderThickness:this.labelBorderThickness,cornerRadius:this.labelCornerRadius,fontSize:this.labelFontSize,fontFamily:this.labelFontFamily,fontWeight:this.labelFontWeight,fontColor:this.labelFontColor,fontStyle:this.labelFontStyle,textBaseline:"middle"}),
this._labels.push({position:l,textBlock:a,effectiveHeight:null}))}}else for(this.intervalStartPosition=this.getLabelStartPoint(new Date(this.viewportMinimum),this.intervalType,this.interval),e=Xa(new Date(this.viewportMaximum),this.interval,this.intervalType),f=q,c=this.intervalStartPosition;c<e;Xa(c,b,this.intervalType)){for(a=c.getTime();f<p.length&&a>p[f].endValue;f++);l=a;a=f<p.length&&a>=p[f].startValue&&a<=p[f].endValue;a||(a=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.options,
value:new Date(l),label:this.labels[l]?this.labels[l]:null}):"axisX"===this.type&&this.labels[l]?this.labels[l]:Da(l,this.valueFormatString,this.chart._cultureInfo),a=new la(this.ctx,{x:0,y:0,maxWidth:g,backgroundColor:this.labelBackgroundColor,borderColor:this.labelBorderColor,borderThickness:this.labelBorderThickness,cornerRadius:this.labelCornerRadius,maxHeight:r,angle:this.labelAngle,text:this.prefix+a+this.suffix,textAlign:this.labelTextAlign,fontSize:this.labelFontSize,fontFamily:this.labelFontFamily,
fontWeight:this.labelFontWeight,fontColor:this.labelFontColor,fontStyle:this.labelFontStyle,textBaseline:"middle"}),this._labels.push({position:l,textBlock:a,effectiveHeight:null,breaksLabelType:void 0}))}if("bottom"===this._position||"top"===this._position)h=this.logarithmic&&!this.equidistantInterval&&2<=this._labels.length?this.lineCoordinates.width*Math.log(Math.min(this._labels[this._labels.length-1].position/this._labels[this._labels.length-2].position,this._labels[1].position/this._labels[0].position))/
Math.log(this.range):this.lineCoordinates.width/(this.logarithmic&&this.equidistantInterval?Math.log(this.range)/Math.log(this.logarithmBase):Math.abs(this.range))*U[this.intervalType+"Duration"]*this.interval,g="undefined"===typeof this.options.labelMaxWidth?0.5*this.chart.width>>0:this.options.labelMaxWidth,this.chart.panEnabled||(r="undefined"===typeof this.options.labelWrap||this.labelWrap?0.8*this.chart.height>>0:1.5*this.labelFontSize);else if("left"===this._position||"right"===this._position)h=
this.logarithmic&&!this.equidistantInterval&&2<=this._labels.length?this.lineCoordinates.height*Math.log(Math.min(this._labels[this._labels.length-1].position/this._labels[this._labels.length-2].position,this._labels[1].position/this._labels[0].position))/Math.log(this.range):this.lineCoordinates.height/(this.logarithmic&&this.equidistantInterval?Math.log(this.range)/Math.log(this.logarithmBase):Math.abs(this.range))*U[this.intervalType+"Duration"]*this.interval,this.chart.panEnabled||(g="undefined"===
typeof this.options.labelMaxWidth?0.3*this.chart.width>>0:this.options.labelMaxWidth),r="undefined"===typeof this.options.labelWrap||this.labelWrap?0.3*this.chart.height>>0:1.5*this.labelFontSize;for(b=0;b<this._labels.length;b++){a=this._labels[b].textBlock;a.maxWidth=g;a.maxHeight=r;var w=a.measureText();n=w.height}e=[];q=p=0;if(this.labelAutoFit||this.options.labelAutoFit)if(m(this.labelAngle)||(this.labelAngle=(this.labelAngle%360+360)%360,90<this.labelAngle&&270>this.labelAngle?this.labelAngle-=
180:270<=this.labelAngle&&360>=this.labelAngle&&(this.labelAngle-=360)),"bottom"===this._position||"top"===this._position)if(g=0.9*h>>0,q=0,!this.chart.panEnabled&&1<=this._labels.length){this.sessionVariables.labelFontSize=this.labelFontSize;this.sessionVariables.labelMaxWidth=g;this.sessionVariables.labelMaxHeight=r;this.sessionVariables.labelAngle=this.labelAngle;this.sessionVariables.labelWrap=this.labelWrap;for(c=0;c<this._labels.length;c++)if(!this._labels[c].breaksLabelType){a=this._labels[c].textBlock;
for(var z,f=a.text.split(" "),b=0;b<f.length;b++)l=f[b],this.ctx.font=a.fontStyle+" "+a.fontWeight+" "+a.fontSize+"px "+a.fontFamily,l=this.ctx.measureText(l),l.width>q&&(z=c,q=l.width)}c=0;for(c=this.intervalStartPosition<this.viewportMinimum?1:0;c<this._labels.length;c++)if(!this._labels[c].breaksLabelType){a=this._labels[c].textBlock;w=a.measureText();for(f=c+1;f<this._labels.length;f++)if(!this._labels[f].breaksLabelType){d=this._labels[f].textBlock;d=d.measureText();break}e.push(a.height);this.sessionVariables.labelMaxHeight=
Math.max.apply(Math,e);Math.cos(Math.PI/180*Math.abs(this.labelAngle));Math.sin(Math.PI/180*Math.abs(this.labelAngle));b=g*Math.sin(Math.PI/180*Math.abs(this.labelAngle))+(r-a.fontSize/2)*Math.cos(Math.PI/180*Math.abs(this.labelAngle));if(m(this.options.labelAngle)&&isNaN(this.options.labelAngle)&&0!==this.options.labelAngle)if(this.sessionVariables.labelMaxHeight=0===this.labelAngle?r:Math.min((b-g*Math.cos(Math.PI/180*Math.abs(this.labelAngle)))/Math.sin(Math.PI/180*Math.abs(this.labelAngle)),b),
l=(k-(n+a.fontSize/2)*Math.cos(Math.PI/180*Math.abs(-25)))/Math.sin(Math.PI/180*Math.abs(-25)),!m(this.options.labelWrap))this.labelWrap?m(this.options.labelMaxWidth)?(this.sessionVariables.labelMaxWidth=Math.min(Math.max(g,q),l),this.sessionVariables.labelWrap=this.labelWrap,d&&w.width+d.width>>0>2*g&&(this.sessionVariables.labelAngle=-25)):(this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth,this.sessionVariables.labelAngle=this.sessionVariables.labelMaxWidth>
g?-25:this.sessionVariables.labelAngle):m(this.options.labelMaxWidth)?(this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelMaxWidth=g,d&&w.width+d.width>>0>2*g&&(this.sessionVariables.labelAngle=-25,this.sessionVariables.labelMaxWidth=l)):(this.sessionVariables.labelAngle=this.sessionVariables.labelMaxWidth>g?-25:this.sessionVariables.labelAngle,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth,this.sessionVariables.labelMaxHeight=
r,this.sessionVariables.labelWrap=this.labelWrap);else{if(m(this.options.labelWrap))if(!m(this.options.labelMaxWidth))this.options.labelMaxWidth<g?(this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth,this.sessionVariables.labelMaxHeight=b):(this.sessionVariables.labelAngle=-25,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth,this.sessionVariables.labelMaxHeight=r);else if(!m(d))if(b=w.width+d.width>>0,f=this.labelFontSize,q<g)b-2*g>p&&(p=b-2*g,b>=2*g&&b<2.2*g?(this.sessionVariables.labelMaxWidth=
g,m(this.options.labelFontSize)&&12<f&&(f=Math.floor(12/13*f),a.measureText()),this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?f:this.options.labelFontSize,this.sessionVariables.labelAngle=this.labelAngle):b>=2.2*g&&b<2.8*g?(this.sessionVariables.labelAngle=-25,this.sessionVariables.labelMaxWidth=l,this.sessionVariables.labelFontSize=f):b>=2.8*g&&b<3.2*g?(this.sessionVariables.labelMaxWidth=Math.max(g,q),this.sessionVariables.labelWrap=!0,m(this.options.labelFontSize)&&12<this.labelFontSize&&
(this.labelFontSize=Math.floor(12/13*this.labelFontSize),a.measureText()),this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?f:this.options.labelFontSize,this.sessionVariables.labelAngle=this.labelAngle):b>=3.2*g&&b<3.6*g?(this.sessionVariables.labelAngle=-25,this.sessionVariables.labelWrap=!0,this.sessionVariables.labelMaxWidth=l,this.sessionVariables.labelFontSize=this.labelFontSize):b>3.6*g&&b<5*g?(m(this.options.labelFontSize)&&12<f&&(f=Math.floor(12/13*f),a.measureText()),this.sessionVariables.labelFontSize=
m(this.options.labelFontSize)?f:this.options.labelFontSize,this.sessionVariables.labelWrap=!0,this.sessionVariables.labelAngle=-25,this.sessionVariables.labelMaxWidth=l):b>5*g&&(this.sessionVariables.labelWrap=!0,this.sessionVariables.labelMaxWidth=g,this.sessionVariables.labelFontSize=f,this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelAngle=this.labelAngle));else if(z===c&&(0===z&&q+this._labels[z+1].textBlock.measureText().width-2*g>p||z===this._labels.length-1&&q+this._labels[z-
1].textBlock.measureText().width-2*g>p||0<z&&z<this._labels.length-1&&q+this._labels[z+1].textBlock.measureText().width-2*g>p&&q+this._labels[z-1].textBlock.measureText().width-2*g>p))p=0===z?q+this._labels[z+1].textBlock.measureText().width-2*g:q+this._labels[z-1].textBlock.measureText().width-2*g,this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?f:this.options.labelFontSize,this.sessionVariables.labelWrap=!0,this.sessionVariables.labelAngle=-25,this.sessionVariables.labelMaxWidth=
l;else if(0===p)for(this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?f:this.options.labelFontSize,this.sessionVariables.labelWrap=!0,b=0;b<this._labels.length;b++)a=this._labels[b].textBlock,a.maxWidth=this.sessionVariables.labelMaxWidth=Math.min(Math.max(g,q),l),w=a.measureText(),b<this._labels.length-1&&(f=b+1,d=this._labels[f].textBlock,d.maxWidth=this.sessionVariables.labelMaxWidth=Math.min(Math.max(g,q),l),d=d.measureText(),w.width+d.width>>0>2*g&&(this.sessionVariables.labelAngle=
-25))}else(this.sessionVariables.labelAngle=this.labelAngle,this.sessionVariables.labelMaxHeight=0===this.labelAngle?r:Math.min((b-g*Math.cos(Math.PI/180*Math.abs(this.labelAngle)))/Math.sin(Math.PI/180*Math.abs(this.labelAngle)),b),l=0!=this.labelAngle?(k-(n+a.fontSize/2)*Math.cos(Math.PI/180*Math.abs(this.labelAngle)))/Math.sin(Math.PI/180*Math.abs(this.labelAngle)):g,this.sessionVariables.labelMaxHeight=this.labelWrap?(k-l*Math.sin(Math.PI/180*Math.abs(this.labelAngle)))/Math.cos(Math.PI/180*Math.abs(this.labelAngle)):
1.5*this.labelFontSize,m(this.options.labelWrap))?m(this.options.labelWrap)&&(this.labelWrap&&!m(this.options.labelMaxWidth)?(this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:l,this.sessionVariables.labelMaxHeight=r):(this.sessionVariables.labelAngle=this.labelAngle,this.sessionVariables.labelMaxWidth=l,this.sessionVariables.labelMaxHeight=b<0.9*h?0.9*h:b,this.sessionVariables.labelWrap=this.labelWrap)):(this.options.labelWrap?
(this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:l):(m(this.options.labelMaxWidth),this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:l,this.sessionVariables.labelWrap=this.labelWrap),this.sessionVariables.labelMaxHeight=r)}for(b=0;b<this._labels.length;b++)a=this._labels[b].textBlock,a.maxWidth=this.labelMaxWidth=this.sessionVariables.labelMaxWidth,a.fontSize=this.sessionVariables.labelFontSize,
a.angle=this.labelAngle=this.sessionVariables.labelAngle,a.wrap=this.labelWrap=this.sessionVariables.labelWrap,a.maxHeight=this.sessionVariables.labelMaxHeight,a.measureText()}else for(c=0;c<this._labels.length;c++)a=this._labels[c].textBlock,a.maxWidth=this.labelMaxWidth=m(this.options.labelMaxWidth)?m(this.sessionVariables.labelMaxWidth)?this.sessionVariables.labelMaxWidth=g:this.sessionVariables.labelMaxWidth:this.options.labelMaxWidth,a.fontSize=this.labelFontSize=m(this.options.labelFontSize)?
m(this.sessionVariables.labelFontSize)?this.sessionVariables.labelFontSize=this.labelFontSize:this.sessionVariables.labelFontSize:this.options.labelFontSize,a.angle=this.labelAngle=m(this.options.labelAngle)?m(this.sessionVariables.labelAngle)?this.sessionVariables.labelAngle=this.labelAngle:this.sessionVariables.labelAngle:this.labelAngle,a.wrap=this.labelWrap=m(this.options.labelWrap)?m(this.sessionVariables.labelWrap)?this.sessionVariables.labelWrap=this.labelWrap:this.sessionVariables.labelWrap:
this.options.labelWrap,a.maxHeight=m(this.sessionVariables.labelMaxHeight)?this.sessionVariables.labelMaxHeight=r:this.sessionVariables.labelMaxHeight,a.measureText();else if("left"===this._position||"right"===this._position)if(g=m(this.options.labelMaxWidth)?0.3*this.chart.width>>0:this.options.labelMaxWidth,r="undefined"===typeof this.options.labelWrap||this.labelWrap?0.3*this.chart.height>>0:1.5*this.labelFontSize,!this.chart.panEnabled&&1<=this._labels.length){this.sessionVariables.labelFontSize=
this.labelFontSize;this.sessionVariables.labelMaxWidth=g;this.sessionVariables.labelMaxHeight=r;this.sessionVariables.labelAngle=m(this.sessionVariables.labelAngle)?0:this.sessionVariables.labelAngle;this.sessionVariables.labelWrap=this.labelWrap;for(c=0;c<this._labels.length;c++)if(!this._labels[c].breaksLabelType){a=this._labels[c].textBlock;w=a.measureText();for(f=c+1;f<this._labels.length;f++)if(!this._labels[f].breaksLabelType){d=this._labels[f].textBlock;d=d.measureText();break}e.push(a.height);
this.sessionVariables.labelMaxHeight=Math.max.apply(Math,e);b=g*Math.sin(Math.PI/180*Math.abs(this.labelAngle))+(r-a.fontSize/2)*Math.cos(Math.PI/180*Math.abs(this.labelAngle));Math.cos(Math.PI/180*Math.abs(this.labelAngle));Math.sin(Math.PI/180*Math.abs(this.labelAngle));m(this.options.labelAngle)&&isNaN(this.options.labelAngle)&&0!==this.options.labelAngle?m(this.options.labelWrap)?m(this.options.labelWrap)&&(m(this.options.labelMaxWidth)?m(d)||(h=w.height+d.height>>0,h-2*r>q&&(q=h-2*r,h>=2*r&&
h<2.4*r?(m(this.options.labelFontSize)&&12<this.labelFontSize&&(this.labelFontSize=Math.floor(12/13*this.labelFontSize),a.measureText()),this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?this.labelFontSize:this.options.labelFontSize):h>=2.4*r&&h<2.8*r?(this.sessionVariables.labelMaxHeight=b,this.sessionVariables.labelFontSize=this.labelFontSize,this.sessionVariables.labelWrap=!0):h>=2.8*r&&h<3.2*r?(this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelWrap=
!0,m(this.options.labelFontSize)&&12<this.labelFontSize&&(this.labelFontSize=Math.floor(12/13*this.labelFontSize),a.measureText()),this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?this.labelFontSize:this.options.labelFontSize,this.sessionVariables.labelAngle=m(this.sessionVariables.labelAngle)?0:this.sessionVariables.labelAngle):h>=3.2*r&&h<3.6*r?(this.sessionVariables.labelMaxHeight=b,this.sessionVariables.labelWrap=!0,this.sessionVariables.labelFontSize=this.labelFontSize):h>3.6*
r&&h<10*r?(m(this.options.labelFontSize)&&12<this.labelFontSize&&(this.labelFontSize=Math.floor(12/13*this.labelFontSize),a.measureText()),this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?this.labelFontSize:this.options.labelFontSize,this.sessionVariables.labelMaxWidth=g,this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelAngle=m(this.sessionVariables.labelAngle)?0:this.sessionVariables.labelAngle):h>10*r&&h<50*r&&(m(this.options.labelFontSize)&&12<this.labelFontSize&&
(this.labelFontSize=Math.floor(12/13*this.labelFontSize),a.measureText()),this.sessionVariables.labelFontSize=m(this.options.labelFontSize)?this.labelFontSize:this.options.labelFontSize,this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelMaxWidth=g,this.sessionVariables.labelAngle=m(this.sessionVariables.labelAngle)?0:this.sessionVariables.labelAngle))):(this.sessionVariables.labelMaxHeight=r,this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:this.sessionVariables.labelMaxWidth)):
(this.sessionVariables.labelMaxWidth=this.labelWrap?this.options.labelMaxWidth?this.options.labelMaxWidth:this.sessionVariables.labelMaxWidth:this.labelMaxWidth?this.options.labelMaxWidth?this.options.labelMaxWidth:this.sessionVariables.labelMaxWidth:g,this.sessionVariables.labelMaxHeight=r):(this.sessionVariables.labelAngle=this.labelAngle,this.sessionVariables.labelMaxWidth=0===this.labelAngle?g:Math.min((b-r*Math.sin(Math.PI/180*Math.abs(this.labelAngle)))/Math.cos(Math.PI/180*Math.abs(this.labelAngle)),
r),m(this.options.labelWrap))?m(this.options.labelWrap)&&(this.labelWrap&&!m(this.options.labelMaxWidth)?(this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:this.sessionVariables.labelMaxWidth,this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxHeight=b):(this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:g,this.sessionVariables.labelMaxHeight=0===this.labelAngle?r:b,m(this.options.labelMaxWidth)&&
(this.sessionVariables.labelAngle=this.labelAngle))):this.options.labelWrap?(this.sessionVariables.labelMaxHeight=0===this.labelAngle?r:b,this.sessionVariables.labelWrap=this.labelWrap,this.sessionVariables.labelMaxWidth=g):(this.sessionVariables.labelMaxHeight=r,m(this.options.labelMaxWidth),this.sessionVariables.labelMaxWidth=this.options.labelMaxWidth?this.options.labelMaxWidth:this.sessionVariables.labelMaxWidth,this.sessionVariables.labelWrap=this.labelWrap)}for(b=0;b<this._labels.length;b++)a=
this._labels[b].textBlock,a.maxWidth=this.labelMaxWidth=this.sessionVariables.labelMaxWidth,a.fontSize=this.labelFontSize=this.sessionVariables.labelFontSize,a.angle=this.labelAngle=this.sessionVariables.labelAngle,a.wrap=this.labelWrap=this.sessionVariables.labelWrap,a.maxHeight=this.sessionVariables.labelMaxHeight,a.measureText()}else for(c=0;c<this._labels.length;c++)a=this._labels[c].textBlock,a.maxWidth=this.labelMaxWidth=m(this.options.labelMaxWidth)?m(this.sessionVariables.labelMaxWidth)?this.sessionVariables.labelMaxWidth=
g:this.sessionVariables.labelMaxWidth:this.options.labelMaxWidth,a.fontSize=this.labelFontSize=m(this.options.labelFontSize)?m(this.sessionVariables.labelFontSize)?this.sessionVariables.labelFontSize=this.labelFontSize:this.sessionVariables.labelFontSize:this.options.labelFontSize,a.angle=this.labelAngle=m(this.options.labelAngle)?m(this.sessionVariables.labelAngle)?this.sessionVariables.labelAngle=this.labelAngle:this.sessionVariables.labelAngle:this.labelAngle,a.wrap=this.labelWrap=m(this.options.labelWrap)?
m(this.sessionVariables.labelWrap)?this.sessionVariables.labelWrap=this.labelWrap:this.sessionVariables.labelWrap:this.options.labelWrap,a.maxHeight=m(this.sessionVariables.labelMaxHeight)?this.sessionVariables.labelMaxHeight=r:this.sessionVariables.labelMaxHeight,a.measureText();for(c=0;c<this.stripLines.length;c++){var g=this.stripLines[c],x;if("outside"===g.labelPlacement){r=this.sessionVariables.labelMaxWidth;if("bottom"===this._position||"top"===this._position)m(g.options.labelWrap)&&!m(this.sessionVariables.stripLineLabelMaxHeight)?
x=this.sessionVariables.stripLineLabelMaxHeight:this.sessionVariables.stripLineLabelMaxHeight=x=g.labelWrap?0.8*this.chart.height>>0:1.5*this.labelFontSize;if("left"===this._position||"right"===this._position)m(g.options.labelWrap)&&!m(this.sessionVariables.stripLineLabelMaxHeight)?x=this.sessionVariables.stripLineLabelMaxHeight:this.sessionVariables.stripLineLabelMaxHeight=x=g.labelWrap?0.8*this.chart.width>>0:1.5*this.labelFontSize;m(g.labelBackgroundColor)&&(g.labelBackgroundColor="#EEEEEE")}else r=
"bottom"===this._position||"top"===this._position?0.9*this.chart.width>>0:0.9*this.chart.height>>0,x=m(g.options.labelWrap)||g.labelWrap?"bottom"===this._position||"top"===this._position?0.8*this.chart.width>>0:0.8*this.chart.height>>0:1.5*this.labelFontSize,m(g.labelBackgroundColor)&&(m(g.startValue)&&0!==g.startValue?g.labelBackgroundColor=t?"transparent":null:g.labelBackgroundColor="#EEEEEE");a=new la(this.ctx,{x:0,y:0,backgroundColor:g.labelBackgroundColor,borderColor:g.labelBorderColor,borderThickness:g.labelBorderThickness,
cornerRadius:g.labelCornerRadius,maxWidth:g.options.labelMaxWidth?g.options.labelMaxWidth:r,maxHeight:x,angle:this.labelAngle,text:g.labelFormatter?g.labelFormatter({chart:this.chart,axis:this,stripLine:g}):g.label,textAlign:this.labelTextAlign,fontSize:"outside"===g.labelPlacement?g.options.labelFontSize?g.labelFontSize:this.labelFontSize:g.labelFontSize,fontFamily:"outside"===g.labelPlacement?g.options.labelFontFamily?g.labelFontFamily:this.labelFontFamily:g.labelFontFamily,fontWeight:"outside"===
g.labelPlacement?g.options.labelFontWeight?g.labelFontWeight:this.labelFontWeight:g.labelFontWeight,fontColor:g.labelFontColor||g.color,fontStyle:"outside"===g.labelPlacement?g.options.labelFontStyle?g.labelFontStyle:this.fontWeight:g.labelFontStyle,textBaseline:"middle"});this._stripLineLabels.push({position:g.value,textBlock:a,effectiveHeight:null,stripLine:g})}};D.prototype.createLabelsAndCalculateWidth=function(){var a=0,d=0;this._labels=[];this._stripLineLabels=[];var c=this.chart.isNavigator?
0:5;if("left"===this._position||"right"===this._position){this.createLabels();if("inside"!=this.labelPlacement||"inside"===this.labelPlacement&&0<this._index)for(d=0;d<this._labels.length;d++){var b=this._labels[d].textBlock,e=b.measureText(),g=0,g=0===this.labelAngle?e.width:e.width*Math.cos(Math.PI/180*Math.abs(this.labelAngle))+(e.height-b.fontSize/2)*Math.sin(Math.PI/180*Math.abs(this.labelAngle));a<g&&(this.labelEffectiveWidth=a=g);this._labels[d].effectiveWidth=g}for(d=0;d<this._stripLineLabels.length;d++)"outside"===
this._stripLineLabels[d].stripLine.labelPlacement&&(this._stripLineLabels[d].stripLine.value>=this.viewportMinimum&&this._stripLineLabels[d].stripLine.value<=this.viewportMaximum)&&(b=this._stripLineLabels[d].textBlock,e=b.measureText(),g=0===this.labelAngle?e.width:e.width*Math.cos(Math.PI/180*Math.abs(this.labelAngle))+(e.height-b.fontSize/2)*Math.sin(Math.PI/180*Math.abs(this.labelAngle)),"inside"===this.tickPlacement&&(g+=this.tickLength),"inside"===this.labelPlacement&&(a+=0<this._index?g:0),
a<g&&(a=g),this.stripLineLabelEffectiveWidth=this._stripLineLabels[d].effectiveWidth=g)}return(this.title?this._titleTextBlock.measureText().height+2:0)+a+("inside"===this.tickPlacement?0<this._index?this.tickLength:0:this.tickLength)+c};D.prototype.createLabelsAndCalculateHeight=function(){var a=0;this._labels=[];this._stripLineLabels=[];var d,c=0,b=this.chart.isNavigator?0:5;if("bottom"===this._position||"top"===this._position){this.createLabels();if("inside"!=this.labelPlacement||"inside"===this.labelPlacement&&
0<this._index)for(c=0;c<this._labels.length;c++){d=this._labels[c].textBlock;var e=d.measureText(),g=0,g=0===this.labelAngle?e.height:e.width*Math.sin(Math.PI/180*Math.abs(this.labelAngle))+(e.height-d.fontSize/2)*Math.cos(Math.PI/180*Math.abs(this.labelAngle));a<g&&(this.labelEffectiveHeight=a=g);this._labels[c].effectiveHeight=g}for(c=0;c<this._stripLineLabels.length;c++)"outside"===this._stripLineLabels[c].stripLine.labelPlacement&&(this._stripLineLabels[c].stripLine.value>=this.viewportMinimum&&
this._stripLineLabels[c].stripLine.value<=this.viewportMaximum)&&(d=this._stripLineLabels[c].textBlock,e=d.measureText(),g=0===this.labelAngle?e.height:e.width*Math.sin(Math.PI/180*Math.abs(this.labelAngle))+(e.height-d.fontSize/2)*Math.cos(Math.PI/180*Math.abs(this.labelAngle)),"inside"===this.tickPlacement&&(g+=this.tickLength),"inside"===this.labelPlacement&&(a+=0<this._index?g:0),a<g&&(a=g),this.stripLineLabelEffectiveHeight=this._stripLineLabels[c].effectiveHeight=g)}return(this.title?this._titleTextBlock.measureText().height+
2:0)+a+("inside"===this.tickPlacement?0<this._index?this.tickLength:0:this.tickLength)+b};D.setLayout=function(a,d,c,b,e,g){var h,n,t,k,l=a[0]?a[0].chart:d[0].chart,p=l.isNavigator?0:10,q=l._axes;if(a&&0<a.length)for(var f=0;f<a.length;f++)a[f]&&a[f].calculateAxisParameters();if(d&&0<d.length)for(f=0;f<d.length;f++)d[f].calculateAxisParameters();if(c&&0<c.length)for(f=0;f<c.length;f++)c[f].calculateAxisParameters();if(b&&0<b.length)for(f=0;f<b.length;f++)b[f].calculateAxisParameters();for(f=0;f<q.length;f++)if(q[f]&&
q[f].scaleBreaks&&q[f].scaleBreaks._appliedBreaks.length)for(var w=q[f].scaleBreaks._appliedBreaks,z=0;z<w.length&&!(w[z].startValue>q[f].viewportMaximum);z++)w[z].endValue<q[f].viewportMinimum||(m(q[f].scaleBreaks.firstBreakIndex)&&(q[f].scaleBreaks.firstBreakIndex=z),w[z].startValue>=q[f].viewPortMinimum&&(q[f].scaleBreaks.lastBreakIndex=z));for(var x=z=0,s=0,y=0,v=0,D=0,A=0,C,E,H=n=0,K,L,N,w=K=L=N=!1,f=0;f<q.length;f++)q[f]&&q[f].title&&(q[f]._titleTextBlock=new la(q[f].ctx,{text:q[f].title,horizontalAlign:"center",
fontSize:q[f].titleFontSize,fontFamily:q[f].titleFontFamily,fontWeight:q[f].titleFontWeight,fontColor:q[f].titleFontColor,fontStyle:q[f].titleFontStyle,borderColor:q[f].titleBorderColor,borderThickness:q[f].titleBorderThickness,backgroundColor:q[f].titleBackgroundColor,cornerRadius:q[f].titleCornerRadius,textBaseline:"middle"}));for(f=0;f<q.length;f++)if(q[f].title)switch(q[f]._position){case "left":q[f]._titleTextBlock.maxWidth=q[f].titleMaxWidth||g.height;q[f]._titleTextBlock.maxHeight=q[f].titleWrap?
0.8*g.width:1.5*q[f].titleFontSize;q[f]._titleTextBlock.angle=-90;break;case "right":q[f]._titleTextBlock.maxWidth=q[f].titleMaxWidth||g.height;q[f]._titleTextBlock.maxHeight=q[f].titleWrap?0.8*g.width:1.5*q[f].titleFontSize;q[f]._titleTextBlock.angle=90;break;default:q[f]._titleTextBlock.maxWidth=q[f].titleMaxWidth||g.width,q[f]._titleTextBlock.maxHeight=q[f].titleWrap?0.8*g.height:1.5*q[f].titleFontSize,q[f]._titleTextBlock.angle=0}if("normal"===e){for(var y=[],v=[],D=[],A=[],O=[],Q=[],P=[],S=[];4>
z;){var F=0,Z=0,U=0,W=0,Y=e=0,M=0,aa=0,X=0,$=0,R=0,ba=0;if(c&&0<c.length)for(D=[],f=R=0;f<c.length;f++)D.push(Math.ceil(c[f]?c[f].createLabelsAndCalculateWidth():0)),R+=D[f],M+=c[f]&&!l.isNavigator?c[f].margin:0;else D.push(Math.ceil(c[0]?c[0].createLabelsAndCalculateWidth():0));P.push(D);if(b&&0<b.length)for(A=[],f=ba=0;f<b.length;f++)A.push(Math.ceil(b[f]?b[f].createLabelsAndCalculateWidth():0)),ba+=A[f],aa+=b[f]?b[f].margin:0;else A.push(Math.ceil(b[0]?b[0].createLabelsAndCalculateWidth():0));
S.push(A);h=Math.round(g.x1+R+M);t=Math.round(g.x2-ba-aa>l.width-p?l.width-p:g.x2-ba-aa);if(a&&0<a.length)for(y=[],f=X=0;f<a.length;f++)a[f]&&(a[f].lineCoordinates={}),a[f].lineCoordinates.width=Math.abs(t-h),a[f].title&&(a[f]._titleTextBlock.maxWidth=0<a[f].titleMaxWidth&&a[f].titleMaxWidth<a[f].lineCoordinates.width?a[f].titleMaxWidth:a[f].lineCoordinates.width),y.push(Math.ceil(a[f]?a[f].createLabelsAndCalculateHeight():0)),X+=y[f],e+=a[f]&&!l.isNavigator?a[f].margin:0;else y.push(Math.ceil(a[0]?
a[0].createLabelsAndCalculateHeight():0));O.push(y);if(d&&0<d.length)for(v=[],f=$=0;f<d.length;f++)d[f]&&(d[f].lineCoordinates={}),d[f].lineCoordinates.width=Math.abs(t-h),d[f].title&&(d[f]._titleTextBlock.maxWidth=0<d[f].titleMaxWidth&&d[f].titleMaxWidth<d[f].lineCoordinates.width?d[f].titleMaxWidth:d[f].lineCoordinates.width),v.push(Math.ceil(d[f]?d[f].createLabelsAndCalculateHeight():0)),$+=v[f],Y+=d[f]&&!l.isNavigator?d[f].margin:0;else v.push(Math.ceil(d[0]?d[0].createLabelsAndCalculateHeight():
0));Q.push(v);if(a&&0<a.length)for(f=0;f<a.length;f++)a[f]&&(a[f].lineCoordinates.x1=h,t=Math.round(g.x2-ba-aa>l.width-p?l.width-p:g.x2-ba-aa),a[f]._labels&&1<a[f]._labels.length&&(n=k=0,k=a[f]._labels[1],n="dateTime"===a[f].valueType?a[f]._labels[a[f]._labels.length-2]:a[f]._labels[a[f]._labels.length-1],x=k.textBlock.width*Math.cos(Math.PI/180*Math.abs(k.textBlock.angle))+(k.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(k.textBlock.angle)),s=n.textBlock.width*Math.cos(Math.PI/
180*Math.abs(n.textBlock.angle))+(n.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(n.textBlock.angle))),!a[f]||(!a[f].labelAutoFit||m(C)||m(E)||l.isNavigator||l.stockChart)||(n=0,0<a[f].labelAngle?E+s>t&&(n+=0<a[f].labelAngle?E+s-t-ba:0):0>a[f].labelAngle?C-x<h&&C-x<a[f].viewportMinimum&&(H=h-(M+a[f].tickLength+D+C-x+a[f].labelFontSize/2)):0===a[f].labelAngle&&(E+s>t&&(n=E+s/2-t-ba),C-x<h&&C-x<a[f].viewportMinimum&&(H=h-M-a[f].tickLength-D-C+x/2)),a[f].viewportMaximum===a[f].maximum&&
a[f].viewportMinimum===a[f].minimum&&0<a[f].labelAngle&&0<n?t-=n:a[f].viewportMaximum===a[f].maximum&&a[f].viewportMinimum===a[f].minimum&&0>a[f].labelAngle&&0<H?h+=H:a[f].viewportMaximum===a[f].maximum&&a[f].viewportMinimum===a[f].minimum&&0===a[f].labelAngle&&(0<H&&(h+=H),0<n&&(t-=n))),l.panEnabled?X=m(l.sessionVariables.axisX.height)?l.sessionVariables.axisX.height=X:l.sessionVariables.axisX.height:l.sessionVariables.axisX.height=X,n=Math.round(g.y2-X-e+F),k=Math.round(g.y2),a[f].lineCoordinates.x2=
t,a[f].lineCoordinates.width=t-h,a[f].lineCoordinates.y1=n,a[f].lineCoordinates.y2=n,"inside"===a[f].labelPlacement&&0<f&&(a[f].lineCoordinates.y1=a[f-1].lineCoordinates.y2+F+(a[f].labelEffectiveHeight||0),a[f].lineCoordinates.y2=a[f].lineCoordinates.y1+a[f].lineThickness/2),"inside"===a[f].tickPlacement&&0<f&&(a[f].lineCoordinates.y1+=a[f].tickLength,a[f].lineCoordinates.y2=a[f].lineCoordinates.y1+a[f].lineThickness/2),a[f].bounds={x1:h,y1:n,x2:t,y2:k-(X+e-y[f]-F),width:t-h},a[f].bounds.height=a[f].bounds.y2-
a[f].bounds.y1),F+=y[f]+a[f].margin;if(d&&0<d.length)for(f=0;f<d.length;f++)d[f].lineCoordinates.x1=Math.round(g.x1+R+M),d[f].lineCoordinates.x2=Math.round(g.x2-ba-aa>l.width-p?l.width-p:g.x2-ba-aa),d[f].lineCoordinates.width=Math.abs(t-h),d[f]._labels&&1<d[f]._labels.length&&(k=d[f]._labels[1],n="dateTime"===d[f].valueType?d[f]._labels[d[f]._labels.length-2]:d[f]._labels[d[f]._labels.length-1],x=k.textBlock.width*Math.cos(Math.PI/180*Math.abs(k.textBlock.angle))+(k.textBlock.height-n.textBlock.fontSize/
2)*Math.sin(Math.PI/180*Math.abs(k.textBlock.angle)),s=n.textBlock.width*Math.cos(Math.PI/180*Math.abs(n.textBlock.angle))+(n.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(n.textBlock.angle))),l.panEnabled?$=m(l.sessionVariables.axisX2.height)?l.sessionVariables.axisX2.height=$:l.sessionVariables.axisX2.height:l.sessionVariables.axisX2.height=$,n=Math.round(g.y1),k=d[f].lineCoordinates.y1=n+$+Y-Z,d[f].lineCoordinates.y2=n,"inside"===d[f].labelPlacement&&0<f&&(d[f].lineCoordinates.y1=
d[f-1].lineCoordinates.y1-Z-(d[f].labelEffectiveHeight||0)),"inside"===d[f].tickPlacement&&0<f&&(d[f].lineCoordinates.y1-=d[f].tickLength),d[f].bounds={x1:h,y1:n+($+Y-v[f]-Z),x2:t,y2:k,width:t-h},d[f].bounds.height=d[f].bounds.y2-d[f].bounds.y1,Z+=v[f]+d[f].margin;if(c&&0<c.length)for(f=0;f<c.length;f++)M=l.isNavigator?0:10,c[f]&&(h=Math.round(a[0]?a[0].lineCoordinates.x1:d[0].lineCoordinates.x1),M=c[f]._labels&&0<c[f]._labels.length?c[f]._labels[c[f]._labels.length-1].textBlock.height/2:p,n=Math.round(g.y1+
$+Y<Math.max(M,p)?Math.max(M,p):g.y1+$+Y),t=Math.round(a[0]?a[0].lineCoordinates.x1:d[0].lineCoordinates.x1),M=0<a.length?0:c[f]._labels&&0<c[f]._labels.length?c[f]._labels[0].textBlock.height/2:p,k=Math.round(g.y2-X-e-M),c[f].lineCoordinates={x1:h-U,y1:n,x2:t-U,y2:k,height:Math.abs(k-n)},"inside"===c[f].labelPlacement&&0<f&&(c[f].lineCoordinates.x1=c[f-1].lineCoordinates.x1-U-(c[f].labelEffectiveWidth||0),c[f].lineCoordinates.x2=c[f].lineCoordinates.x1+c[f].lineThickness/2),"inside"===c[f].tickPlacement&&
0<f&&(c[f].lineCoordinates.x1-=c[f].tickLength,c[f].lineCoordinates.x2=c[f].lineCoordinates.x1+c[f].lineThickness/2),c[f].bounds={x1:h-(D[f]+U),y1:n,x2:t-U,y2:k,height:k-n},c[f].bounds.width=c[f].bounds.x2-c[f].bounds.x1,c[f].title&&(c[f]._titleTextBlock.maxWidth=0<c[f].titleMaxWidth&&c[f].titleMaxWidth<c[f].lineCoordinates.height?c[f].titleMaxWidth:c[f].lineCoordinates.height),U+=D[f]+c[f].margin);if(b&&0<b.length)for(f=0;f<b.length;f++)b[f]&&(h=Math.round(a[0]?a[0].lineCoordinates.x2:d[0].lineCoordinates.x2),
t=Math.round(h),M=b[f]._labels&&0<b[f]._labels.length?b[f]._labels[b[f]._labels.length-1].textBlock.height/2:0,n=Math.round(g.y1+$+Y<Math.max(M,p)?Math.max(M,p):g.y1+$+Y),M=0<a.length?0:b[f]._labels&&0<b[f]._labels.length?b[f]._labels[0].textBlock.height/2:0,k=Math.round(g.y2-(X+e+M)),b[f].lineCoordinates={x1:h+W,y1:n,x2:h+W,y2:k,height:Math.abs(k-n)},"inside"===b[f].labelPlacement&&0<f&&(b[f].lineCoordinates.x1=b[f-1].lineCoordinates.x2+W+(b[f].labelEffectiveWidth||0),b[f].lineCoordinates.x2=b[f].lineCoordinates.x1+
b[f].lineThickness/2),"inside"===b[f].tickPlacement&&0<f&&(b[f].lineCoordinates.x1+=b[f].tickLength,b[f].lineCoordinates.x2=b[f].lineCoordinates.x1+b[f].lineThickness/2),b[f].bounds={x1:h+W,y1:n,x2:t+(A[f]+W),y2:k,height:k-n},b[f].bounds.width=b[f].bounds.x2-b[f].bounds.x1,b[f].title&&(b[f]._titleTextBlock.maxWidth=0<b[f].titleMaxWidth&&b[f].titleMaxWidth<b[f].lineCoordinates.height?b[f].titleMaxWidth:b[f].lineCoordinates.height),W+=A[f]+b[f].margin);if(a&&0<a.length)for(f=0;f<a.length;f++)a[f]&&
(a[f].calculateValueToPixelConversionParameters(),a[f].calculateBreaksSizeInValues(),a[f]._labels&&1<a[f]._labels.length&&(C=(a[f].logarithmic?Math.log(a[f]._labels[1].position/a[f].viewportMinimum)/a[f].conversionParameters.lnLogarithmBase:a[f]._labels[1].position-a[f].viewportMinimum)*Math.abs(a[f].conversionParameters.pixelPerUnit)+a[f].lineCoordinates.x1,h=a[f]._labels[a[f]._labels.length-("dateTime"===a[f].valueType?2:1)].position,h=a[f].getApparentDifference(a[f].viewportMinimum,h),E=a[f].logarithmic?
(1<h?Math.log(h)/a[f].conversionParameters.lnLogarithmBase*Math.abs(a[f].conversionParameters.pixelPerUnit):0)+a[f].lineCoordinates.x1:(0<h?h*Math.abs(a[f].conversionParameters.pixelPerUnit):0)+a[f].lineCoordinates.x1));if(d&&0<d.length)for(f=0;f<d.length;f++)d[f].calculateValueToPixelConversionParameters(),d[f].calculateBreaksSizeInValues(),d[f]._labels&&1<d[f]._labels.length&&(C=(d[f].logarithmic?Math.log(d[f]._labels[1].position/d[f].viewportMinimum)/d[f].conversionParameters.lnLogarithmBase:d[f]._labels[1].position-
d[f].viewportMinimum)*Math.abs(d[f].conversionParameters.pixelPerUnit)+d[f].lineCoordinates.x1,h=d[f]._labels[d[f]._labels.length-("dateTime"===d[f].valueType?2:1)].position,h=d[f].getApparentDifference(d[f].viewportMinimum,h),E=d[f].logarithmic?(1<h?Math.log(h)/d[f].conversionParameters.lnLogarithmBase*Math.abs(d[f].conversionParameters.pixelPerUnit):0)+d[f].lineCoordinates.x1:(0<h?h*Math.abs(d[f].conversionParameters.pixelPerUnit):0)+d[f].lineCoordinates.x1);for(f=0;f<q.length;f++)"axisY"===q[f].type&&
(q[f].calculateValueToPixelConversionParameters(),q[f].calculateBreaksSizeInValues());if(0<z){if(a&&0<a.length)for(f=0;f<a.length;f++)w=O[z-1][f]===O[z][f]?!0:!1;else w=!0;if(d&&0<d.length)for(f=0;f<d.length;f++)K=Q[z-1][f]===Q[z][f]?!0:!1;else K=!0;if(c&&0<c.length)for(f=0;f<c.length;f++)L=P[z-1][f]===P[z][f]?!0:!1;else L=!0;if(b&&0<b.length)for(f=0;f<b.length;f++)N=S[z-1][f]===S[z][f]?!0:!1;else N=!0}if(w&&K&&L&&N)break;z++}if(a&&0<a.length)for(f=0;f<a.length;f++)a[f].calculateStripLinesThicknessInValues(),
a[f].calculateBreaksInPixels();if(d&&0<d.length)for(f=0;f<d.length;f++)d[f].calculateStripLinesThicknessInValues(),d[f].calculateBreaksInPixels();if(c&&0<c.length)for(f=0;f<c.length;f++)c[f].calculateStripLinesThicknessInValues(),c[f].calculateBreaksInPixels();if(b&&0<b.length)for(f=0;f<b.length;f++)b[f].calculateStripLinesThicknessInValues(),b[f].calculateBreaksInPixels()}else{p=[];C=[];H=[];x=[];E=[];s=[];O=[];for(Q=[];4>z;){X=W=U=aa=M=Y=e=S=P=F=$=0;if(a&&0<a.length)for(H=[],f=W=0;f<a.length;f++)H.push(Math.ceil(a[f]?
a[f].createLabelsAndCalculateWidth():0)),W+=H[f],e+=a[f]&&!l.isNavigator?a[f].margin:0;else H.push(Math.ceil(a[0]?a[0].createLabelsAndCalculateWidth():0));O.push(H);if(d&&0<d.length)for(x=[],f=X=0;f<d.length;f++)x.push(Math.ceil(d[f]?d[f].createLabelsAndCalculateWidth():0)),X+=x[f],Y+=d[f]?d[f].margin:0;else x.push(Math.ceil(d[0]?d[0].createLabelsAndCalculateWidth():0));Q.push(x);if(c&&0<c.length)for(f=0;f<c.length;f++)c[f].lineCoordinates={},h=Math.round(g.x1+W+e),t=Math.round(g.x2-X-Y>l.width-10?
l.width-10:g.x2-X-Y),c[f].labelAutoFit&&!m(y)&&(0<!a.length&&(h=0>c[f].labelAngle?Math.max(h,y):0===c[f].labelAngle?Math.max(h,y/2):h),0<!d.length&&(t=0<c[f].labelAngle?t-v/2:0===c[f].labelAngle?t-v/2:t)),c[f].lineCoordinates.x1=h,c[f].lineCoordinates.x2=t,c[f].lineCoordinates.width=Math.abs(t-h),c[f].title&&(c[f]._titleTextBlock.maxWidth=0<c[f].titleMaxWidth&&c[f].titleMaxWidth<c[f].lineCoordinates.width?c[f].titleMaxWidth:c[f].lineCoordinates.width);if(b&&0<b.length)for(f=0;f<b.length;f++)b[f].lineCoordinates=
{},h=Math.round(g.x1+W+e),t=Math.round(g.x2-X-Y>b[f].chart.width-10?b[f].chart.width-10:g.x2-X-Y),b[f]&&b[f].labelAutoFit&&!m(D)&&(0<!a.length&&(h=0<b[f].labelAngle?Math.max(h,D):0===b[f].labelAngle?Math.max(h,D/2):h),0<!d.length&&(t-=A/2)),b[f].lineCoordinates.x1=h,b[f].lineCoordinates.x2=t,b[f].lineCoordinates.width=Math.abs(t-h),b[f].title&&(b[f]._titleTextBlock.maxWidth=0<b[f].titleMaxWidth&&b[f].titleMaxWidth<b[f].lineCoordinates.width?b[f].titleMaxWidth:b[f].lineCoordinates.width);if(c&&0<c.length)for(p=
[],f=U=0;f<c.length;f++)p.push(Math.ceil(c[f]?c[f].createLabelsAndCalculateHeight():0)),U+=p[f]+c[f].margin,M+=c[f].margin;else p.push(Math.ceil(c[0]?c[0].createLabelsAndCalculateHeight():0));E.push(p);if(b&&0<b.length)for(C=[],f=0;f<b.length;f++)C.push(Math.ceil(b[f]?b[f].createLabelsAndCalculateHeight():0)),aa+=b[f].margin;else C.push(Math.ceil(b[0]?b[0].createLabelsAndCalculateHeight():0));s.push(C);if(c&&0<c.length)for(f=0;f<c.length;f++)0<c[f]._labels.length&&(k=c[f]._labels[0],n=c[f]._labels[c[f]._labels.length-
1],y=k.textBlock.width*Math.cos(Math.PI/180*Math.abs(k.textBlock.angle))+(k.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(k.textBlock.angle)),v=n.textBlock.width*Math.cos(Math.PI/180*Math.abs(n.textBlock.angle))+(n.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(n.textBlock.angle)));if(b&&0<b.length)for(f=0;f<b.length;f++)b[f]&&0<b[f]._labels.length&&(k=b[f]._labels[0],n=b[f]._labels[b[f]._labels.length-1],D=k.textBlock.width*Math.cos(Math.PI/180*Math.abs(k.textBlock.angle))+
(k.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(k.textBlock.angle)),A=n.textBlock.width*Math.cos(Math.PI/180*Math.abs(n.textBlock.angle))+(n.textBlock.height-n.textBlock.fontSize/2)*Math.sin(Math.PI/180*Math.abs(n.textBlock.angle)));if(l.panEnabled)for(f=0;f<c.length;f++)p[f]=m(l.sessionVariables.axisY[f].height)?l.sessionVariables.axisY[f].height=p[f]:l.sessionVariables.axisY[f].height;else for(f=0;f<c.length;f++)l.sessionVariables.axisY[f].height=p[f];if(c&&0<c.length)for(f=
c.length-1;0<=f;f--)n=Math.round(g.y2),k=Math.round(g.y2>c[f].chart.height?c[f].chart.height:g.y2),c[f].lineCoordinates.y1=n-(p[f]+c[f].margin+$),c[f].lineCoordinates.y2=n-(p[f]+c[f].margin+$),"inside"===c[f].labelPlacement&&0<f&&(c[f].lineCoordinates.y1=c[f].lineCoordinates.y1+p[f]-(c[f]._titleTextBlock?c[f]._titleTextBlock.height:0)-c[f].tickLength-(c[f].stripLineLabelEffectiveHeight||0)-5,c[f].lineCoordinates.y2=c[f].lineCoordinates.y1+c[f].lineThickness/2),"inside"===c[f].tickPlacement&&0<f&&
(c[f].lineCoordinates.y1+=c[f].tickLength,c[f].lineCoordinates.y2=c[f].lineCoordinates.y1+c[f].lineThickness/2),c[f].bounds={x1:h,y1:n-(p[f]+$+c[f].margin),x2:t,y2:k-($+c[f].margin),width:t-h,height:p[f]},c[f].title&&(c[f]._titleTextBlock.maxWidth=0<c[f].titleMaxWidth&&c[f].titleMaxWidth<c[f].lineCoordinates.width?c[f].titleMaxWidth:c[f].lineCoordinates.width),$+=p[f]+c[f].margin;if(b&&0<b.length)for(f=b.length-1;0<=f;f--)b[f]&&(n=Math.round(g.y1),k=Math.round(g.y1+(C[f]+b[f].margin+F)),b[f].lineCoordinates.y1=
k,b[f].lineCoordinates.y2=k,"inside"===b[f].labelPlacement&&0<f&&(b[f].lineCoordinates.y1=k-C[f]+(b[f]._titleTextBlock?b[f]._titleTextBlock.height:0)+b[f].tickLength+(b[f].stripLineLabelEffectiveHeight||0),b[f].lineCoordinates.y2=b[f].lineCoordinates.y1-b[f].lineThickness/2),"inside"===b[f].tickPlacement&&0<f&&(b[f].lineCoordinates.y1-=b[f].tickLength,b[f].lineCoordinates.y2=b[f].lineCoordinates.y1-b[f].lineThickness/2),b[f].bounds={x1:h,y1:n+(b[f].margin+F),x2:t,y2:k,width:t-h},b[f].bounds.height=
b[f].bounds.y2-b[f].bounds.y1,b[f].title&&(b[f]._titleTextBlock.maxWidth=0<b[f].titleMaxWidth&&b[f].titleMaxWidth<b[f].lineCoordinates.width?b[f].titleMaxWidth:b[f].lineCoordinates.width),F+=C[f]+b[f].margin);if(a&&0<a.length)for(f=0;f<a.length;f++){M=a[f]._labels&&0<a[f]._labels.length?a[f]._labels[0].textBlock.fontSize/2:0;h=Math.round(g.x1+e);n=b&&0<b.length?Math.round(b[0]?b[0].lineCoordinates.y2:g.y1<Math.max(M,10)?Math.max(M,10):g.y1):g.y1<Math.max(M,10)?Math.max(M,10):g.y1;t=Math.round(g.x1+
W+e);k=c&&0<c.length?Math.round(c[0]?c[0].lineCoordinates.y1:g.y2-U>l.height-Math.max(M,10)?l.height-Math.max(M,10):g.y2-U):g.y2>l.height-Math.max(M,10)?l.height-Math.max(M,10):g.y2;if(c&&0<c.length)for(M=0;M<c.length;M++)c[M]&&c[M].labelAutoFit&&(t=c[M].lineCoordinates.x1,h=0>c[M].labelAngle||0===c[M].labelAngle?t-W:h);if(b&&0<b.length)for(M=0;M<b.length;M++)b[M]&&b[M].labelAutoFit&&(t=b[M].lineCoordinates.x1,h=t-W);a[f].lineCoordinates={x1:t-P,y1:n,x2:t-P,y2:k,height:Math.abs(k-n)};"inside"===a[f].labelPlacement&&
0<f&&(a[f].lineCoordinates.x1=a[f].lineCoordinates.x1-(H[f]-(a[f]._titleTextBlock?a[f]._titleTextBlock.height:0))+a[f].tickLength+(a[f].stripLineLabelEffectiveWidth||0),a[f].lineCoordinates.x2=a[f].lineCoordinates.x1+a[f].lineThickness/2);"inside"===a[f].tickPlacement&&0<f&&(a[f].lineCoordinates.x1-=a[f].tickLength,a[f].lineCoordinates.x2=a[f].lineCoordinates.x1+a[f].lineThickness/2);a[f].bounds={x1:t-(H[f]+P),y1:n,x2:t-P,y2:k,height:k-n};a[f].bounds.width=a[f].bounds.x2-a[f].bounds.x1;a[f].title&&
(a[f]._titleTextBlock.maxWidth=0<a[f].titleMaxWidth&&a[f].titleMaxWidth<a[f].lineCoordinates.height?a[f].titleMaxWidth:a[f].lineCoordinates.height);a[f].calculateValueToPixelConversionParameters();a[f].calculateBreaksSizeInValues();P+=H[f]+a[f].margin}if(d&&0<d.length)for(f=0;f<d.length;f++){M=d[f]._labels&&0<d[f]._labels.length?d[f]._labels[0].textBlock.fontSize/2:0;h=Math.round(g.x1-e);n=b&&0<b.length?Math.round(b[0]?b[0].lineCoordinates.y2:g.y1<Math.max(M,10)?Math.max(M,10):g.y1):g.y1<Math.max(M,
10)?Math.max(M,10):g.y1;t=Math.round(g.x2-X-Y);k=c&&0<c.length?Math.round(c[0]?c[0].lineCoordinates.y1:g.y2-U>l.height-Math.max(M,10)?l.height-Math.max(M,10):g.y2-U):g.y2>l.height-Math.max(M,10)?l.height-Math.max(M,10):g.y2;if(c&&0<c.length)for(M=0;M<c.length;M++)c[M]&&c[M].labelAutoFit&&(t=0>c[M].labelAngle?Math.max(t,y):0===c[M].labelAngle?Math.max(t,y/2):t,h=0>c[M].labelAngle||0===c[M].labelAngle?t-X:h);if(b&&0<b.length)for(M=0;M<b.length;M++)b[M]&&b[M].labelAutoFit&&(t=b[M].lineCoordinates.x2,
h=t-X);d[f].lineCoordinates={x1:t+S,y1:n,x2:t+S,y2:k,height:Math.abs(k-n)};"inside"===d[f].labelPlacement&&0<f&&(d[f].lineCoordinates.x1=d[f].lineCoordinates.x1+(x[f]-(d[f]._titleTextBlock?d[f]._titleTextBlock.height:0)-2)-d[f].tickLength-(d[f].stripLineLabelEffectiveWidth||0),d[f].lineCoordinates.x2=d[f].lineCoordinates.x1+d[f].lineThickness/2);"inside"===d[f].tickPlacement&&0<f&&(d[f].lineCoordinates.x1+=d[f].tickLength,d[f].lineCoordinates.x2=d[f].lineCoordinates.x1+d[f].lineThickness/2);d[f].bounds=
{x1:d[f].lineCoordinates.x1,y1:n,x2:t+x[f]+S,y2:k,width:t-h,height:k-n};d[f].bounds.width=d[f].bounds.x2-d[f].bounds.x1;d[f].title&&(d[f]._titleTextBlock.maxWidth=0<d[f].titleMaxWidth&&d[f].titleMaxWidth<d[f].lineCoordinates.height?d[f].titleMaxWidth:d[f].lineCoordinates.height);d[f].calculateValueToPixelConversionParameters();d[f].calculateBreaksSizeInValues();S+=x[f]+d[f].margin}for(f=0;f<q.length;f++)"axisY"===q[f].type&&(q[f].calculateValueToPixelConversionParameters(),q[f].calculateBreaksSizeInValues());
if(0<z){if(a&&0<a.length)for(f=0;f<a.length;f++)w=O[z-1][f]===O[z][f]?!0:!1;else w=!0;if(d&&0<d.length)for(f=0;f<d.length;f++)K=Q[z-1][f]===Q[z][f]?!0:!1;else K=!0;if(c&&0<c.length)for(f=0;f<c.length;f++)L=E[z-1][f]===E[z][f]?!0:!1;else L=!0;if(b&&0<b.length)for(f=0;f<b.length;f++)N=s[z-1][f]===s[z][f]?!0:!1;else N=!0}if(w&&K&&L&&N)break;z++}if(c&&0<c.length)for(f=0;f<c.length;f++)c[f].calculateStripLinesThicknessInValues(),c[f].calculateBreaksInPixels();if(b&&0<b.length)for(f=0;f<b.length;f++)b[f].calculateStripLinesThicknessInValues(),
b[f].calculateBreaksInPixels();if(a&&0<a.length)for(f=0;f<a.length;f++)a[f].calculateStripLinesThicknessInValues(),a[f].calculateBreaksInPixels();if(d&&0<d.length)for(f=0;f<d.length;f++)d[f].calculateStripLinesThicknessInValues(),d[f].calculateBreaksInPixels()}};D.render=function(a,d,c,b,e){var g=a[0]?a[0].chart:d[0].chart;e=g.ctx;g.alignVerticalAxes&&g.alignVerticalAxes();e.save();e.beginPath();a&&a.length&&e.rect(5,a[0].bounds.y1,a[0].chart.width-10,a[a.length-1].bounds.y2);d&&d.length&&e.rect(5,
d[d.length-1].bounds.y1,d[0].chart.width-10,d[0].bounds.y2);e.clip();if(a&&0<a.length)for(var h=0;h<a.length;h++)a[h].renderLabelsTicksAndTitle();if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderLabelsTicksAndTitle();e.restore();if(c&&0<c.length)for(h=0;h<c.length;h++)c[h].renderLabelsTicksAndTitle();if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderLabelsTicksAndTitle();g.preparePlotArea();g=g.plotArea;e.save();e.beginPath();e.rect(g.x1,g.y1,Math.abs(g.x2-g.x1),Math.abs(g.y2-g.y1));e.clip();if(a&&
0<a.length)for(h=0;h<a.length;h++)a[h].renderStripLinesOfThicknessType("value");if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderStripLinesOfThicknessType("value");if(c&&0<c.length)for(h=0;h<c.length;h++)c[h].renderStripLinesOfThicknessType("value");if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderStripLinesOfThicknessType("value");if(a&&0<a.length)for(h=0;h<a.length;h++)a[h].renderInterlacedColors();if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderInterlacedColors();if(c&&0<c.length)for(h=
0;h<c.length;h++)c[h].renderInterlacedColors();if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderInterlacedColors();e.restore();if(a&&0<a.length)for(h=0;h<a.length;h++)a[h].renderGrid(),t&&(a[h].createMask(),a[h].renderBreaksBackground());if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderGrid(),t&&(d[h].createMask(),d[h].renderBreaksBackground());if(c&&0<c.length)for(h=0;h<c.length;h++)c[h].renderGrid(),t&&(c[h].createMask(),c[h].renderBreaksBackground());if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderGrid(),
t&&(b[h].createMask(),b[h].renderBreaksBackground());if(a&&0<a.length)for(h=0;h<a.length;h++)a[h].renderAxisLine();if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderAxisLine();if(c&&0<c.length)for(h=0;h<c.length;h++)c[h].renderAxisLine();if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderAxisLine();if(a&&0<a.length)for(h=0;h<a.length;h++)a[h].renderStripLinesOfThicknessType("pixel");if(d&&0<d.length)for(h=0;h<d.length;h++)d[h].renderStripLinesOfThicknessType("pixel");if(c&&0<c.length)for(h=0;h<c.length;h++)c[h].renderStripLinesOfThicknessType("pixel");
if(b&&0<b.length)for(h=0;h<b.length;h++)b[h].renderStripLinesOfThicknessType("pixel")};D.prototype.calculateStripLinesThicknessInValues=function(){for(var a=0;a<this.stripLines.length;a++)if(null!==this.stripLines[a].startValue&&null!==this.stripLines[a].endValue){var d=Math.min(this.stripLines[a].startValue,this.stripLines[a].endValue),c=Math.max(this.stripLines[a].startValue,this.stripLines[a].endValue),b=this.getApparentDifference(d,c);this.stripLines[a].value=this.convertPixelToValue(Math.abs(this.convertValueToPixel(d)+
this.convertValueToPixel(c))/2);this.stripLines[a].thickness=b;this.stripLines[a]._thicknessType="value"}};D.prototype.calculateBreaksSizeInValues=function(){for(var a="left"===this._position||"right"===this._position?this.lineCoordinates.height||this.chart.height:this.lineCoordinates.width||this.chart.width,d=this.scaleBreaks?this.scaleBreaks._appliedBreaks:[],c=this.conversionParameters.pixelPerUnit||a/(this.logarithmic?this.conversionParameters.maximum/this.conversionParameters.minimum:this.conversionParameters.maximum-
this.conversionParameters.minimum),b=this.scaleBreaks&&!m(this.scaleBreaks.options.spacing),e,g=0;g<d.length;g++)e=b||!m(d[g].options.spacing),d[g].spacing=Ta(d[g].spacing,a,8,e?0.1*a:8,e?0:3)<<0,d[g].size=0>d[g].spacing?0:Math.abs(d[g].spacing/c),this.logarithmic&&(d[g].size=Math.pow(this.logarithmBase,d[g].size))};D.prototype.calculateBreaksInPixels=function(){if(!(this.scaleBreaks&&0>=this.scaleBreaks._appliedBreaks.length)){var a=this.scaleBreaks?this.scaleBreaks._appliedBreaks:[];a.length&&(this.scaleBreaks.firstBreakIndex=
this.scaleBreaks.lastBreakIndex=null);for(var d=0;d<a.length&&!(a[d].startValue>this.conversionParameters.maximum);d++)a[d].endValue<this.conversionParameters.minimum||(m(this.scaleBreaks.firstBreakIndex)&&(this.scaleBreaks.firstBreakIndex=d),a[d].startValue>=this.conversionParameters.minimum&&(a[d].startPixel=this.convertValueToPixel(a[d].startValue),this.scaleBreaks.lastBreakIndex=d),a[d].endValue<=this.conversionParameters.maximum&&(a[d].endPixel=this.convertValueToPixel(a[d].endValue)))}};D.prototype.renderLabelsTicksAndTitle=
function(){var a=this,d=!1,c=0,b=0,e=1,g=0;0!==this.labelAngle&&360!==this.labelAngle&&(e=1.2);if("undefined"===typeof this.options.interval){if("bottom"===this._position||"top"===this._position)if(this.logarithmic&&!this.equidistantInterval&&this.labelAutoFit){for(var c=[],e=0!==this.labelAngle&&360!==this.labelAngle?1:1.2,h,m=this.viewportMaximum,n=this.lineCoordinates.width/Math.log(this.range),k=this._labels.length-1;0<=k;k--){p=this._labels[k];if(p.position<this.viewportMinimum)break;p.position>
this.viewportMaximum||!(k===this._labels.length-1||h<Math.log(m/p.position)*n/e)||(c.push(p),m=p.position,h=p.textBlock.width*Math.abs(Math.cos(Math.PI/180*this.labelAngle))+p.textBlock.height*Math.abs(Math.sin(Math.PI/180*this.labelAngle)))}this._labels=c}else{for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||(h=p.textBlock.width*Math.abs(Math.cos(Math.PI/180*this.labelAngle))+p.textBlock.height*Math.abs(Math.sin(Math.PI/180*this.labelAngle)),c+=h);c>this.lineCoordinates.width*
e&&this.labelAutoFit&&(d=!0)}if("left"===this._position||"right"===this._position)if(this.logarithmic&&!this.equidistantInterval&&this.labelAutoFit){for(var c=[],l,m=this.viewportMaximum,n=this.lineCoordinates.height/Math.log(this.range),k=this._labels.length-1;0<=k;k--){p=this._labels[k];if(p.position<this.viewportMinimum)break;p.position>this.viewportMaximum||!(k===this._labels.length-1||l<Math.log(m/p.position)*n)||(c.push(p),m=p.position,l=p.textBlock.height*Math.abs(Math.cos(Math.PI/180*this.labelAngle))+
p.textBlock.width*Math.abs(Math.sin(Math.PI/180*this.labelAngle)))}this._labels=c}else{for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||(l=p.textBlock.height*Math.abs(Math.cos(Math.PI/180*this.labelAngle))+p.textBlock.width*Math.abs(Math.sin(Math.PI/180*this.labelAngle)),b+=l);b>this.lineCoordinates.height*e&&this.labelAutoFit&&(d=!0)}}this.logarithmic&&(!this.equidistantInterval&&this.labelAutoFit)&&this._labels.sort(function(a,b){return a.position-b.position});
var k=0,p,q;if("bottom"===this._position){for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||p.position>this.viewportMaximum||(q=this.getPixelCoordinatesOnAxis(p.position),this.tickThickness&&"inside"!=this.tickPlacement&&(this.ctx.lineWidth=this.tickThickness,this.ctx.strokeStyle=this.tickColor,b=1===this.ctx.lineWidth%2?(q.x<<0)+0.5:q.x<<0,this.ctx.beginPath(),this.ctx.moveTo(b,q.y<<0),this.ctx.lineTo(b,q.y+this.tickLength<<0),this.ctx.stroke()),d&&0!==g++%2&&this.labelAutoFit||
(0===p.textBlock.angle?(q.x-=p.textBlock.width/2,q.y="inside"===this.labelPlacement?q.y-(("inside"===this.tickPlacement?this.tickLength:0)+p.textBlock.height-p.textBlock.fontSize/2):q.y+("inside"===this.tickPlacement?0:this.tickLength)+p.textBlock.fontSize/2+5):(q.x="inside"===this.labelPlacement?0>this.labelAngle?q.x:q.x-p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):q.x-(0>this.labelAngle?p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):0),q.y="inside"===this.labelPlacement?0>this.labelAngle?
q.y-("inside"===this.tickPlacement?this.tickLength:0)-5:q.y-("inside"===this.tickPlacement?this.tickLength:0)-Math.abs(p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle)+5):q.y+("inside"===this.tickPlacement?0:this.tickLength)+Math.abs(0>this.labelAngle?p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle)-5:5)),p.textBlock.x=q.x,p.textBlock.y=q.y));"inside"===this.tickPlacement&&this.chart.addEventListener("dataAnimationIterationEnd",function(){for(k=0;k<a._labels.length;k++)if(p=a._labels[k],
!(p.position<a.viewportMinimum||p.position>a.viewportMaximum)&&(q=a.getPixelCoordinatesOnAxis(p.position),a.tickThickness)){a.ctx.lineWidth=a.tickThickness;a.ctx.strokeStyle=a.tickColor;var b=1===a.ctx.lineWidth%2?(q.x<<0)+0.5:q.x<<0;a.ctx.save();a.ctx.beginPath();a.ctx.moveTo(b,q.y<<0);a.ctx.lineTo(b,q.y-a.tickLength<<0);a.ctx.stroke();a.ctx.restore()}},this);this.title&&(this._titleTextBlock.measureText(),this._titleTextBlock.x=this.lineCoordinates.x1+this.lineCoordinates.width/2-this._titleTextBlock.width/
2,this._titleTextBlock.y=this.bounds.y2-this._titleTextBlock.height+this._titleTextBlock.fontSize/2-1,this.titleMaxWidth=this._titleTextBlock.maxWidth,this._titleTextBlock.render(!0))}else if("top"===this._position){for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||p.position>this.viewportMaximum||(q=this.getPixelCoordinatesOnAxis(p.position),this.tickThickness&&"inside"!=this.tickPlacement&&(this.ctx.lineWidth=this.tickThickness,this.ctx.strokeStyle=this.tickColor,
b=1===this.ctx.lineWidth%2?(q.x<<0)+0.5:q.x<<0,this.ctx.beginPath(),this.ctx.moveTo(b,q.y<<0),this.ctx.lineTo(b,q.y-this.tickLength<<0),this.ctx.stroke()),d&&0!==g++%2&&this.labelAutoFit||(0===p.textBlock.angle?(q.x-=p.textBlock.width/2,q.y="inside"===this.labelPlacement?q.y+this.labelFontSize/2+("inside"===this.tickPlacement?this.tickLength:0)+5:q.y-(("inside"===this.tickPlacement?0:this.tickLength)+p.textBlock.height-p.textBlock.fontSize/2)):(q.x="inside"===this.labelPlacement?0<this.labelAngle?
q.x:q.x-p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):q.x+(p.textBlock.height-this.labelFontSize)*Math.sin(Math.PI/180*this.labelAngle)-(0<this.labelAngle?p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):0),q.y="inside"===this.labelPlacement?0<this.labelAngle?q.y+("inside"===this.tickPlacement?this.tickLength:0)+5:q.y-p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle)+("inside"===this.tickPlacement?this.tickLength:0)+5:q.y-(("inside"===this.tickPlacement?0:this.tickLength)+((p.textBlock.height-
p.textBlock.fontSize/2)*Math.cos(Math.PI/180*this.labelAngle)+(0<this.labelAngle?p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle):0)))),p.textBlock.x=q.x,p.textBlock.y=q.y));"inside"===this.tickPlacement&&this.chart.addEventListener("dataAnimationIterationEnd",function(){for(k=0;k<a._labels.length;k++)if(p=a._labels[k],!(p.position<a.viewportMinimum||p.position>a.viewportMaximum)&&(q=a.getPixelCoordinatesOnAxis(p.position),a.tickThickness)){a.ctx.lineWidth=a.tickThickness;a.ctx.strokeStyle=
a.tickColor;var b=1===a.ctx.lineWidth%2?(q.x<<0)+0.5:q.x<<0;a.ctx.save();a.ctx.beginPath();a.ctx.moveTo(b,q.y<<0);a.ctx.lineTo(b,q.y+a.tickLength<<0);a.ctx.stroke();a.ctx.restore()}},this);this.title&&(this._titleTextBlock.measureText(),this._titleTextBlock.x=this.lineCoordinates.x1+this.lineCoordinates.width/2-this._titleTextBlock.width/2,this._titleTextBlock.y=this.bounds.y1+this._titleTextBlock.fontSize/2+1,this.titleMaxWidth=this._titleTextBlock.maxWidth,this._titleTextBlock.render(!0))}else if("left"===
this._position){for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||p.position>this.viewportMaximum||(q=this.getPixelCoordinatesOnAxis(p.position),this.tickThickness&&"inside"!=this.tickPlacement&&(this.ctx.lineWidth=this.tickThickness,this.ctx.strokeStyle=this.tickColor,b=1===this.ctx.lineWidth%2?(q.y<<0)+0.5:q.y<<0,this.ctx.beginPath(),this.ctx.moveTo(q.x<<0,b),this.ctx.lineTo(q.x-this.tickLength<<0,b),this.ctx.stroke()),d&&0!==g++%2&&this.labelAutoFit||(0===this.labelAngle?
(p.textBlock.y=q.y,p.textBlock.x="inside"===this.labelPlacement?q.x+("inside"===this.tickPlacement?this.tickLength:0)+5:q.x-p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle)-("inside"===this.tickPlacement?0:this.tickLength)-5):(p.textBlock.y="inside"===this.labelPlacement?q.y:q.y-p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle),p.textBlock.x="inside"===this.labelPlacement?q.x+("inside"===this.tickPlacement?this.tickLength:0)+5:0<this.labelAngle?q.x-p.textBlock.width*Math.cos(Math.PI/
180*this.labelAngle)-("inside"===this.tickPlacement?0:this.tickLength)-5:q.x-p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle)+(p.textBlock.height-p.textBlock.fontSize/2-5)*Math.sin(Math.PI/180*this.labelAngle)-("inside"===this.tickPlacement?0:this.tickLength))));"inside"===this.tickPlacement&&this.chart.addEventListener("dataAnimationIterationEnd",function(){for(k=0;k<a._labels.length;k++)if(p=a._labels[k],!(p.position<a.viewportMinimum||p.position>a.viewportMaximum)&&(q=a.getPixelCoordinatesOnAxis(p.position),
a.tickThickness)){a.ctx.lineWidth=a.tickThickness;a.ctx.strokeStyle=a.tickColor;var b=1===a.ctx.lineWidth%2?(q.y<<0)+0.5:q.y<<0;a.ctx.save();a.ctx.beginPath();a.ctx.moveTo(q.x<<0,b);a.ctx.lineTo(q.x+a.tickLength<<0,b);a.ctx.stroke();a.ctx.restore()}},this);this.title&&(this._titleTextBlock.measureText(),this._titleTextBlock.x=this.bounds.x1+this._titleTextBlock.fontSize/2+1,this._titleTextBlock.y=this.lineCoordinates.height/2+this._titleTextBlock.width/2+this.lineCoordinates.y1,this.titleMaxWidth=
this._titleTextBlock.maxWidth,this._titleTextBlock.render(!0))}else if("right"===this._position){for(k=0;k<this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||p.position>this.viewportMaximum||(q=this.getPixelCoordinatesOnAxis(p.position),this.tickThickness&&"inside"!=this.tickPlacement&&(this.ctx.lineWidth=this.tickThickness,this.ctx.strokeStyle=this.tickColor,b=1===this.ctx.lineWidth%2?(q.y<<0)+0.5:q.y<<0,this.ctx.beginPath(),this.ctx.moveTo(q.x<<0,b),this.ctx.lineTo(q.x+this.tickLength<<
0,b),this.ctx.stroke()),d&&0!==g++%2&&this.labelAutoFit||(0===this.labelAngle?(p.textBlock.y=q.y,p.textBlock.x="inside"===this.labelPlacement?q.x-p.textBlock.width-("inside"===this.tickPlacement?this.tickLength:0)-5:q.x+("inside"===this.tickPlacement?0:this.tickLength)+5):(p.textBlock.y="inside"===this.labelPlacement?q.y-p.textBlock.width*Math.sin(Math.PI/180*this.labelAngle):0>this.labelAngle?q.y:q.y-(p.textBlock.height-p.textBlock.fontSize/2-5)*Math.cos(Math.PI/180*this.labelAngle),p.textBlock.x=
"inside"===this.labelPlacement?q.x-p.textBlock.width*Math.cos(Math.PI/180*this.labelAngle)-("inside"===this.tickPlacement?this.tickLength:0)-5:0<this.labelAngle?q.x+(p.textBlock.height-p.textBlock.fontSize/2-5)*Math.sin(Math.PI/180*this.labelAngle)+("inside"===this.tickPlacement?0:this.tickLength):q.x+("inside"===this.tickPlacement?0:this.tickLength)+5)));"inside"===this.tickPlacement&&this.chart.addEventListener("dataAnimationIterationEnd",function(){for(k=0;k<a._labels.length;k++)if(p=a._labels[k],
!(p.position<a.viewportMinimum||p.position>a.viewportMaximum)&&(q=a.getPixelCoordinatesOnAxis(p.position),a.tickThickness)){a.ctx.lineWidth=a.tickThickness;a.ctx.strokeStyle=a.tickColor;var b=1===a.ctx.lineWidth%2?(q.y<<0)+0.5:q.y<<0;a.ctx.save();a.ctx.beginPath();a.ctx.moveTo(q.x<<0,b);a.ctx.lineTo(q.x-a.tickLength<<0,b);a.ctx.stroke();a.ctx.restore()}},this);this.title&&(this._titleTextBlock.measureText(),this._titleTextBlock.x=this.bounds.x2-this._titleTextBlock.fontSize/2-1,this._titleTextBlock.y=
this.lineCoordinates.height/2-this._titleTextBlock.width/2+this.lineCoordinates.y1,this.titleMaxWidth=this._titleTextBlock.maxWidth,this._titleTextBlock.render(!0))}g=0;if("inside"===this.labelPlacement)this.chart.addEventListener("dataAnimationIterationEnd",function(){for(k=0;k<a._labels.length;k++)p=a._labels[k],p.position<a.viewportMinimum||(p.position>a.viewportMaximum||d&&0!==g++%2&&a.labelAutoFit)||(a.ctx.save(),a.ctx.beginPath(),p.textBlock.render(!0),a.ctx.restore())},this);else for(k=0;k<
this._labels.length;k++)p=this._labels[k],p.position<this.viewportMinimum||(p.position>this.viewportMaximum||d&&0!==g++%2&&this.labelAutoFit)||p.textBlock.render(!0)};D.prototype.renderInterlacedColors=function(){var a=this.chart.plotArea.ctx,d,c,b=this.chart.plotArea,e=0;d=!0;if(("bottom"===this._position||"top"===this._position)&&this.interlacedColor)for(a.fillStyle=this.interlacedColor,e=0;e<this._labels.length;e++)d?(d=this.getPixelCoordinatesOnAxis(this._labels[e].position),c=e+1>this._labels.length-
1?this.getPixelCoordinatesOnAxis(this.viewportMaximum):this.getPixelCoordinatesOnAxis(this._labels[e+1].position),a.fillRect(Math.min(c.x,d.x),b.y1,Math.abs(c.x-d.x),Math.abs(b.y1-b.y2)),d=!1):d=!0;else if(("left"===this._position||"right"===this._position)&&this.interlacedColor)for(a.fillStyle=this.interlacedColor,e=0;e<this._labels.length;e++)d?(c=this.getPixelCoordinatesOnAxis(this._labels[e].position),d=e+1>this._labels.length-1?this.getPixelCoordinatesOnAxis(this.viewportMaximum):this.getPixelCoordinatesOnAxis(this._labels[e+
1].position),a.fillRect(b.x1,Math.min(c.y,d.y),Math.abs(b.x1-b.x2),Math.abs(d.y-c.y)),d=!1):d=!0;a.beginPath()};D.prototype.renderStripLinesOfThicknessType=function(a){if(this.stripLines&&0<this.stripLines.length&&a){var d=this,c,b,e=0,g=0,h=!1;b=!1;for(var n=[],t=[],k=!1,e=0;e<this.stripLines.length;e++){var l=this.stripLines[e];l._thicknessType===a&&("pixel"===a&&(l.value<this.viewportMinimum||l.value>this.viewportMaximum||m(l.value)||isNaN(this.range))||"value"===a&&(l.startValue<=this.viewportMinimum&&
l.endValue<=this.viewportMinimum||l.startValue>=this.viewportMaximum&&l.endValue>=this.viewportMaximum||m(l.startValue)||m(l.endValue)||isNaN(this.range))||n.push(l))}for(e=0;e<this._stripLineLabels.length;e++)if(l=this.stripLines[e],c=this._stripLineLabels[e],!(c.position<this.viewportMinimum||c.position>this.viewportMaximum||isNaN(this.range)))if(b=this.getPixelCoordinatesOnAxis(c.position),"outside"===c.stripLine.labelPlacement){l&&(this.ctx.strokeStyle=l.color,this.ctx.lineWidth="pixel"===l._thicknessType?
l.thickness:this.tickThickness);if("bottom"===this._position){var p=1===this.ctx.lineWidth%2?(b.x<<0)+0.5:b.x<<0;this.ctx.beginPath();this.ctx.moveTo(p,b.y<<0);this.ctx.lineTo(p,b.y+this.tickLength<<0);this.ctx.stroke();0===this.labelAngle?(b.x-=c.textBlock.width/2,b.y+=this.tickLength+c.textBlock.fontSize/2+5):(b.x-=0>this.labelAngle?c.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):0,b.y+=this.tickLength+Math.abs(0>this.labelAngle?c.textBlock.width*Math.sin(Math.PI/180*this.labelAngle)-5:
5))}else"top"===this._position?(p=1===this.ctx.lineWidth%2?(b.x<<0)+0.5:b.x<<0,this.ctx.beginPath(),this.ctx.moveTo(p,b.y<<0),this.ctx.lineTo(p,b.y-this.tickLength<<0),this.ctx.stroke(),0===this.labelAngle?(b.x-=c.textBlock.width/2,b.y-=this.tickLength+c.textBlock.height-c.textBlock.fontSize/2):(b.x+=(c.textBlock.height-this.tickLength-this.labelFontSize/2)*Math.sin(Math.PI/180*this.labelAngle)-(0<this.labelAngle?c.textBlock.width*Math.cos(Math.PI/180*this.labelAngle):0),b.y-=this.tickLength+(c.textBlock.height*
Math.cos(Math.PI/180*this.labelAngle)+(0<this.labelAngle?c.textBlock.width*Math.sin(Math.PI/180*this.labelAngle):0)))):"left"===this._position?(p=1===this.ctx.lineWidth%2?(b.y<<0)+0.5:b.y<<0,this.ctx.beginPath(),this.ctx.moveTo(b.x<<0,p),this.ctx.lineTo(b.x-this.tickLength<<0,p),this.ctx.stroke(),0===this.labelAngle?b.x=b.x-c.textBlock.width*Math.cos(Math.PI/180*this.labelAngle)-this.tickLength-5:(b.y-=c.textBlock.width*Math.sin(Math.PI/180*this.labelAngle),b.x=0<this.labelAngle?b.x-c.textBlock.width*
Math.cos(Math.PI/180*this.labelAngle)-this.tickLength-5:b.x-c.textBlock.width*Math.cos(Math.PI/180*this.labelAngle)+(c.textBlock.height-c.textBlock.fontSize/2-5)*Math.sin(Math.PI/180*this.labelAngle)-this.tickLength)):"right"===this._position&&(p=1===this.ctx.lineWidth%2?(b.y<<0)+0.5:b.y<<0,this.ctx.beginPath(),this.ctx.moveTo(b.x<<0,p),this.ctx.lineTo(b.x+this.tickLength<<0,p),this.ctx.stroke(),0===this.labelAngle?b.x=b.x+this.tickLength+5:(b.y=0>this.labelAngle?b.y:b.y-(c.textBlock.height-c.textBlock.fontSize/
2-5)*Math.cos(Math.PI/180*this.labelAngle),b.x=0<this.labelAngle?b.x+(c.textBlock.height-c.textBlock.fontSize/2-5)*Math.sin(Math.PI/180*this.labelAngle)+this.tickLength:b.x+this.tickLength+5));c.textBlock.x=b.x;c.textBlock.y=b.y;t.push(c)}else l._thicknessType===a&&(c.textBlock.angle=-90,"bottom"===this._position?(c.textBlock.maxWidth=this.options.stripLines[e].labelMaxWidth?this.options.stripLines[e].labelMaxWidth:this.chart.plotArea.height-3,c.textBlock.measureText(),b.x-c.textBlock.height-l.thickness/
2>this.chart.plotArea.x1?m(l.startValue)?b.x-=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.x-=c.textBlock.height/2-c.textBlock.fontSize/2:(c.textBlock.angle=90,m(l.startValue)?b.x+=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.x+=c.textBlock.height/2-c.textBlock.fontSize/2),b.y=-90===c.textBlock.angle?"near"===c.stripLine.labelAlign?this.chart.plotArea.y2-3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.y2+this.chart.plotArea.y1+c.textBlock.width)/2:this.chart.plotArea.y1+
c.textBlock.width+3:"near"===c.stripLine.labelAlign?this.chart.plotArea.y2-c.textBlock.width-3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.y2+this.chart.plotArea.y1-c.textBlock.width)/2:this.chart.plotArea.y1+3):"top"===this._position?(c.textBlock.maxWidth=this.options.stripLines[e].labelMaxWidth?this.options.stripLines[e].labelMaxWidth:this.chart.plotArea.height-3,c.textBlock.measureText(),b.x-c.textBlock.height-l.thickness/2>this.chart.plotArea.x1?m(l.startValue)?b.x-=c.textBlock.height-
c.textBlock.fontSize/2+l.thickness/2:b.x-=c.textBlock.height/2-c.textBlock.fontSize/2:(c.textBlock.angle=90,m(l.startValue)?b.x+=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.x+=c.textBlock.height/2-c.textBlock.fontSize/2),b.y=-90===c.textBlock.angle?"near"===c.stripLine.labelAlign?this.chart.plotArea.y1+c.textBlock.width+3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.y2+this.chart.plotArea.y1+c.textBlock.width)/2:this.chart.plotArea.y2-3:"near"===c.stripLine.labelAlign?this.chart.plotArea.y1+
3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.y2+this.chart.plotArea.y1-c.textBlock.width)/2:this.chart.plotArea.y2-c.textBlock.width-3):"left"===this._position?(c.textBlock.maxWidth=this.options.stripLines[e].labelMaxWidth?this.options.stripLines[e].labelMaxWidth:this.chart.plotArea.width-3,c.textBlock.angle=0,c.textBlock.measureText(),b.y-c.textBlock.height-l.thickness/2>this.chart.plotArea.y1?m(l.startValue)?b.y-=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.y-=c.textBlock.height/
2-c.textBlock.fontSize/2:m(l.startValue)?b.y+=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.y+=c.textBlock.height/2-c.textBlock.fontSize+3,b.x="near"===c.stripLine.labelAlign?this.chart.plotArea.x1+3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.x2+this.chart.plotArea.x1)/2-c.textBlock.width/2:this.chart.plotArea.x2-c.textBlock.width-3):"right"===this._position&&(c.textBlock.maxWidth=this.options.stripLines[e].labelMaxWidth?this.options.stripLines[e].labelMaxWidth:this.chart.plotArea.width-
3,c.textBlock.angle=0,c.textBlock.measureText(),b.y-c.textBlock.height-l.thickness/2>this.chart.plotArea.y1?m(l.startValue)?b.y-=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.y-=c.textBlock.height/2-c.textBlock.fontSize/2:m(l.startValue)?b.y+=c.textBlock.height-c.textBlock.fontSize/2+l.thickness/2:b.y-=c.textBlock.height/2-c.textBlock.fontSize/2+3,b.x="near"===c.stripLine.labelAlign?this.chart.plotArea.x2-c.textBlock.width-3:"center"===c.stripLine.labelAlign?(this.chart.plotArea.x2+this.chart.plotArea.x1)/
2-c.textBlock.width/2:this.chart.plotArea.x1+3),c.textBlock.x=b.x,c.textBlock.y=b.y,t.push(c));if(!k){b=!1;this.ctx.save();this.ctx.beginPath();this.ctx.rect(this.chart.plotArea.x1,this.chart.plotArea.y1,this.chart.plotArea.width,this.chart.plotArea.height);this.ctx.clip();for(e=0;e<n.length;e++)l=n[e],l.showOnTop?h||(h=!0,this.chart.addEventListener("dataAnimationIterationEnd",function(){this.ctx.save();this.ctx.beginPath();this.ctx.rect(this.chart.plotArea.x1,this.chart.plotArea.y1,this.chart.plotArea.width,
this.chart.plotArea.height);this.ctx.clip();for(g=0;g<n.length;g++)l=n[g],l.showOnTop&&l.render();this.ctx.restore()},l)):l.render();for(e=0;e<t.length;e++)c=t[e],c.stripLine.showOnTop?b||(b=!0,this.chart.addEventListener("dataAnimationIterationEnd",function(){for(g=0;g<t.length;g++)c=t[g],"inside"===c.stripLine.labelPlacement&&c.stripLine.showOnTop&&(d.ctx.save(),d.ctx.beginPath(),d.ctx.rect(d.chart.plotArea.x1,d.chart.plotArea.y1,d.chart.plotArea.width,d.chart.plotArea.height),d.ctx.clip(),c.textBlock.render(!0),
d.ctx.restore())},c.textBlock)):"inside"===c.stripLine.labelPlacement&&c.textBlock.render(!0);this.ctx.restore();k=!0}if(k)for(b=!1,e=0;e<t.length;e++)c=t[e],"outside"===c.stripLine.labelPlacement&&c.textBlock.render(!0)}};D.prototype.renderBreaksBackground=function(){this.chart._breaksCanvas&&(this.scaleBreaks&&0<this.scaleBreaks._appliedBreaks.length&&this.maskCanvas)&&(this.chart._breaksCanvasCtx.save(),this.chart._breaksCanvasCtx.beginPath(),this.chart._breaksCanvasCtx.rect(this.chart.plotArea.x1,
this.chart.plotArea.y1,this.chart.plotArea.width,this.chart.plotArea.height),this.chart._breaksCanvasCtx.clip(),this.chart._breaksCanvasCtx.drawImage(this.maskCanvas,0,0,this.chart.width,this.chart.height),this.chart._breaksCanvasCtx.restore())};D.prototype.createMask=function(){if(this.scaleBreaks&&0<this.scaleBreaks._appliedBreaks.length){var a=this.scaleBreaks._appliedBreaks;t?(this.maskCanvas=wa(this.chart.width,this.chart.height),this.maskCtx=this.maskCanvas.getContext("2d")):(this.maskCanvas=
this.chart.plotArea.canvas,this.maskCtx=this.chart.plotArea.ctx);this.maskCtx.save();this.maskCtx.beginPath();this.maskCtx.rect(this.chart.plotArea.x1,this.chart.plotArea.y1,this.chart.plotArea.width,this.chart.plotArea.height);this.maskCtx.clip();for(var d=0;d<a.length;d++)a[d].endValue<this.viewportMinimum||(a[d].startValue>this.viewportMaximum||isNaN(this.range))||a[d].render(this.maskCtx);this.maskCtx.restore()}};D.prototype.renderCrosshair=function(a,d){isFinite(this.minimum)&&isFinite(this.maximum)&&
this.crosshair.render(a,d)};D.prototype.showCrosshair=function(a){m(a)||(a<this.viewportMinimum||a>this.viewportMaximum)||("top"===this._position||"bottom"===this._position?this.crosshair.render(this.convertValueToPixel(a),null,a):this.crosshair.render(null,this.convertValueToPixel(a),a))};D.prototype.renderGrid=function(){if(this.gridThickness&&0<this.gridThickness){var a=this.chart.ctx;a.save();var d,c=this.chart.plotArea;a.lineWidth=this.gridThickness;a.strokeStyle=this.gridColor;a.setLineDash&&
a.setLineDash(H(this.gridDashType,this.gridThickness));if("bottom"===this._position||"top"===this._position)for(b=0;b<this._labels.length;b++)this._labels[b].position<this.viewportMinimum||(this._labels[b].position>this.viewportMaximum||this._labels[b].breaksLabelType)||(a.beginPath(),d=this.getPixelCoordinatesOnAxis(this._labels[b].position),d=1===a.lineWidth%2?(d.x<<0)+0.5:d.x<<0,a.moveTo(d,c.y1<<0),a.lineTo(d,c.y2<<0),a.stroke());else if("left"===this._position||"right"===this._position)for(var b=
0;b<this._labels.length;b++)this._labels[b].position<this.viewportMinimum||(this._labels[b].position>this.viewportMaximum||this._labels[b].breaksLabelType)||(a.beginPath(),d=this.getPixelCoordinatesOnAxis(this._labels[b].position),d=1===a.lineWidth%2?(d.y<<0)+0.5:d.y<<0,a.moveTo(c.x1<<0,d),a.lineTo(c.x2<<0,d),a.stroke());a.restore()}};D.prototype.renderAxisLine=function(){var a=this.chart.ctx,d=t?this.chart._preRenderCtx:a,c=Math.ceil(this.tickThickness/(this.reversed?-2:2)),b=Math.ceil(this.tickThickness/
(this.reversed?2:-2)),e,g;d.save();if("bottom"===this._position||"top"===this._position){if(this.lineThickness){this.reversed?(e=this.lineCoordinates.x2,g=this.lineCoordinates.x1):(e=this.lineCoordinates.x1,g=this.lineCoordinates.x2);d.lineWidth=this.lineThickness;d.strokeStyle=this.lineColor?this.lineColor:"black";d.setLineDash&&d.setLineDash(H(this.lineDashType,this.lineThickness));var h=1===this.lineThickness%2?(this.lineCoordinates.y1<<0)+0.5:this.lineCoordinates.y1<<0;d.beginPath();if(this.scaleBreaks&&
!m(this.scaleBreaks.firstBreakIndex))if(m(this.scaleBreaks.lastBreakIndex))e=this.scaleBreaks._appliedBreaks[this.scaleBreaks.firstBreakIndex].endPixel+b;else for(var n=this.scaleBreaks.firstBreakIndex;n<=this.scaleBreaks.lastBreakIndex;n++)d.moveTo(e,h),d.lineTo(this.scaleBreaks._appliedBreaks[n].startPixel+c,h),e=this.scaleBreaks._appliedBreaks[n].endPixel+b;e&&(d.moveTo(e,h),d.lineTo(g,h));d.stroke()}}else if(("left"===this._position||"right"===this._position)&&this.lineThickness){this.reversed?
(e=this.lineCoordinates.y1,g=this.lineCoordinates.y2):(e=this.lineCoordinates.y2,g=this.lineCoordinates.y1);d.lineWidth=this.lineThickness;d.strokeStyle=this.lineColor;d.setLineDash&&d.setLineDash(H(this.lineDashType,this.lineThickness));h=1===this.lineThickness%2?(this.lineCoordinates.x1<<0)+0.5:this.lineCoordinates.x1<<0;d.beginPath();if(this.scaleBreaks&&!m(this.scaleBreaks.firstBreakIndex))if(m(this.scaleBreaks.lastBreakIndex))e=this.scaleBreaks._appliedBreaks[this.scaleBreaks.firstBreakIndex].endPixel+
c;else for(n=this.scaleBreaks.firstBreakIndex;n<=this.scaleBreaks.lastBreakIndex;n++)d.moveTo(h,e),d.lineTo(h,this.scaleBreaks._appliedBreaks[n].startPixel+b),e=this.scaleBreaks._appliedBreaks[n].endPixel+c;e&&(d.moveTo(h,e),d.lineTo(h,g));d.stroke()}t&&(a.drawImage(this.chart._preRenderCanvas,0,0,this.chart.width,this.chart.height),this.chart._breaksCanvasCtx&&this.chart._breaksCanvasCtx.drawImage(this.chart._preRenderCanvas,0,0,this.chart.width,this.chart.height),d.clearRect(0,0,this.chart.width,
this.chart.height));d.restore()};D.prototype.getPixelCoordinatesOnAxis=function(a){var d={};if("bottom"===this._position||"top"===this._position)d.x=this.convertValueToPixel(a),d.y=this.lineCoordinates.y1;if("left"===this._position||"right"===this._position)d.y=this.convertValueToPixel(a),d.x=this.lineCoordinates.x2;return d};D.prototype.convertPixelToValue=function(a){if("undefined"===typeof a)return null;var d=0,c=0,b,d=!0,e=this.scaleBreaks?this.scaleBreaks._appliedBreaks:[],c="number"===typeof a?
a:"left"===this._position||"right"===this._position?a.y:a.x;if(this.logarithmic){a=b=Math.pow(this.logarithmBase,(c-this.conversionParameters.reference)/this.conversionParameters.pixelPerUnit);if(c<=this.conversionParameters.reference===("left"===this._position||"right"===this._position)!==this.reversed)for(c=0;c<e.length;c++){if(!(e[c].endValue<this.conversionParameters.minimum))if(d)if(e[c].startValue<this.conversionParameters.minimum){if(1<e[c].size&&this.conversionParameters.minimum*Math.pow(e[c].endValue/
e[c].startValue,Math.log(b)/Math.log(e[c].size))<e[c].endValue){a=Math.pow(e[c].endValue/e[c].startValue,Math.log(b)/Math.log(e[c].size));break}else a*=e[c].endValue/this.conversionParameters.minimum/Math.pow(e[c].size,Math.log(e[c].endValue/this.conversionParameters.minimum)/Math.log(e[c].endValue/e[c].startValue)),b/=Math.pow(e[c].size,Math.log(e[c].endValue/this.conversionParameters.minimum)/Math.log(e[c].endValue/e[c].startValue));d=!1}else if(b>e[c].startValue/this.conversionParameters.minimum){b/=
e[c].startValue/this.conversionParameters.minimum;if(b<e[c].size){a*=Math.pow(e[c].endValue/e[c].startValue,1===e[c].size?1:Math.log(b)/Math.log(e[c].size))/b;break}else a*=e[c].endValue/e[c].startValue/e[c].size;b/=e[c].size;d=!1}else break;else if(b>e[c].startValue/e[c-1].endValue){b/=e[c].startValue/e[c-1].endValue;if(b<e[c].size){a*=Math.pow(e[c].endValue/e[c].startValue,1===e[c].size?1:Math.log(b)/Math.log(e[c].size))/b;break}else a*=e[c].endValue/e[c].startValue/e[c].size;b/=e[c].size}else break}else for(c=
e.length-1;0<=c;c--)if(!(e[c].startValue>this.conversionParameters.minimum))if(d)if(e[c].endValue>this.conversionParameters.minimum){if(1<e[c].size&&this.conversionParameters.minimum*Math.pow(e[c].endValue/e[c].startValue,Math.log(b)/Math.log(e[c].size))>e[c].startValue){a=Math.pow(e[c].endValue/e[c].startValue,Math.log(b)/Math.log(e[c].size));break}else a*=e[c].startValue/this.conversionParameters.minimum*Math.pow(e[c].size,Math.log(e[c].startValue/this.conversionParameters.minimum)/Math.log(e[c].endValue/
e[c].startValue))*b,b*=Math.pow(e[c].size,Math.log(this.conversionParameters.minimum/e[c].startValue)/Math.log(e[c].endValue/e[c].startValue));d=!1}else if(b<e[c].endValue/this.conversionParameters.minimum){b/=e[c].endValue/this.conversionParameters.minimum;if(b>1/e[c].size){a*=Math.pow(e[c].endValue/e[c].startValue,1>=e[c].size?1:Math.log(b)/Math.log(e[c].size))*b;break}else a/=e[c].endValue/e[c].startValue/e[c].size;b*=e[c].size;d=!1}else break;else if(b<e[c].endValue/e[c+1].startValue){b/=e[c].endValue/
e[c+1].startValue;if(b>1/e[c].size){a*=Math.pow(e[c].endValue/e[c].startValue,1>=e[c].size?1:Math.log(b)/Math.log(e[c].size))*b;break}else a/=e[c].endValue/e[c].startValue/e[c].size;b*=e[c].size}else break;d=a*this.viewportMinimum}else{a=b=(c-this.conversionParameters.reference)/this.conversionParameters.pixelPerUnit;if(c<=this.conversionParameters.reference===("left"===this._position||"right"===this._position)!==this.reversed)for(c=0;c<e.length;c++){if(!(e[c].endValue<this.conversionParameters.minimum))if(d)if(e[c].startValue<
this.conversionParameters.minimum){if(e[c].size&&this.conversionParameters.minimum+b*(e[c].endValue-e[c].startValue)/e[c].size<e[c].endValue){a=0>=e[c].size?0:b*(e[c].endValue-e[c].startValue)/e[c].size;break}else a+=e[c].endValue-this.conversionParameters.minimum-e[c].size*(e[c].endValue-this.conversionParameters.minimum)/(e[c].endValue-e[c].startValue),b-=e[c].size*(e[c].endValue-this.conversionParameters.minimum)/(e[c].endValue-e[c].startValue);d=!1}else if(b>e[c].startValue-this.conversionParameters.minimum){b-=
e[c].startValue-this.conversionParameters.minimum;if(b<e[c].size){a+=(e[c].endValue-e[c].startValue)*(0===e[c].size?1:b/e[c].size)-b;break}else a+=e[c].endValue-e[c].startValue-e[c].size;b-=e[c].size;d=!1}else break;else if(b>e[c].startValue-e[c-1].endValue){b-=e[c].startValue-e[c-1].endValue;if(b<e[c].size){a+=(e[c].endValue-e[c].startValue)*(0===e[c].size?1:b/e[c].size)-b;break}else a+=e[c].endValue-e[c].startValue-e[c].size;b-=e[c].size}else break}else for(c=e.length-1;0<=c;c--)if(!(e[c].startValue>
this.conversionParameters.minimum))if(d)if(e[c].endValue>this.conversionParameters.minimum)if(e[c].size&&this.conversionParameters.minimum+b*(e[c].endValue-e[c].startValue)/e[c].size>e[c].startValue){a=0>=e[c].size?0:b*(e[c].endValue-e[c].startValue)/e[c].size;break}else a+=e[c].startValue-this.conversionParameters.minimum+e[c].size*(this.conversionParameters.minimum-e[c].startValue)/(e[c].endValue-e[c].startValue),b+=e[c].size*(this.conversionParameters.minimum-e[c].startValue)/(e[c].endValue-e[c].startValue),
d=!1;else if(b<e[c].endValue-this.conversionParameters.minimum){b-=e[c].endValue-this.conversionParameters.minimum;if(b>-1*e[c].size){a+=(e[c].endValue-e[c].startValue)*(0===e[c].size?1:b/e[c].size)+b;break}else a-=e[c].endValue-e[c].startValue-e[c].size;b+=e[c].size;d=!1}else break;else if(b<e[c].endValue-e[c+1].startValue){b-=e[c].endValue-e[c+1].startValue;if(b>-1*e[c].size){a+=(e[c].endValue-e[c].startValue)*(0===e[c].size?1:b/e[c].size)+b;break}else a-=e[c].endValue-e[c].startValue-e[c].size;
b+=e[c].size}else break;d=this.conversionParameters.minimum+a}return d};D.prototype.convertValueToPixel=function(a){a=this.getApparentDifference(this.conversionParameters.minimum,a,a);return this.logarithmic?this.conversionParameters.reference+this.conversionParameters.pixelPerUnit*Math.log(a/this.conversionParameters.minimum)/this.conversionParameters.lnLogarithmBase+0.5<<0:"axisX"===this.type?this.conversionParameters.reference+this.conversionParameters.pixelPerUnit*(a-this.conversionParameters.minimum)+
0.5<<0:this.conversionParameters.reference+this.conversionParameters.pixelPerUnit*(a-this.conversionParameters.minimum)+0.5};D.prototype.getApparentDifference=function(a,d,c,b){var e=this.scaleBreaks?this.scaleBreaks._appliedBreaks:[];if(this.logarithmic){c=m(c)?d/a:c;for(var g=0;g<e.length&&!(d<e[g].startValue);g++)a>e[g].endValue||(a<=e[g].startValue&&d>=e[g].endValue?c=c/e[g].endValue*e[g].startValue*e[g].size:a>=e[g].startValue&&d>=e[g].endValue?c=c/e[g].endValue*a*Math.pow(e[g].size,Math.log(e[g].endValue/
a)/Math.log(e[g].endValue/e[g].startValue)):a<=e[g].startValue&&d<=e[g].endValue?c=c/d*e[g].startValue*Math.pow(e[g].size,Math.log(d/e[g].startValue)/Math.log(e[g].endValue/e[g].startValue)):!b&&(a>e[g].startValue&&d<e[g].endValue)&&(c=a*Math.pow(e[g].size,Math.log(d/a)/Math.log(e[g].endValue/e[g].startValue))))}else for(c=m(c)?Math.abs(d-a):c,g=0;g<e.length&&!(d<e[g].startValue);g++)a>e[g].endValue||(a<=e[g].startValue&&d>=e[g].endValue?c=c-e[g].endValue+e[g].startValue+e[g].size:a>e[g].startValue&&
d>=e[g].endValue?c=c-e[g].endValue+a+e[g].size*(e[g].endValue-a)/(e[g].endValue-e[g].startValue):a<=e[g].startValue&&d<e[g].endValue?c=c-d+e[g].startValue+e[g].size*(d-e[g].startValue)/(e[g].endValue-e[g].startValue):!b&&(a>e[g].startValue&&d<e[g].endValue)&&(c=a+e[g].size*(d-a)/(e[g].endValue-e[g].startValue)));return c};D.prototype.setViewPortRange=function(a,d){this.sessionVariables.newViewportMinimum=this.viewportMinimum=Math.min(a,d);this.sessionVariables.newViewportMaximum=this.viewportMaximum=
Math.max(a,d)};D.prototype.getXValueAt=function(a){if(!a)return null;var d=null;"left"===this._position?d=this.convertPixelToValue(a.y):"bottom"===this._position&&(d=this.convertPixelToValue(a.x));return d};D.prototype.calculateValueToPixelConversionParameters=function(a){a=this.scaleBreaks?this.scaleBreaks._appliedBreaks:[];var d={pixelPerUnit:null,minimum:null,reference:null},c=this.lineCoordinates.width,b=this.lineCoordinates.height,c="bottom"===this._position||"top"===this._position?c:b,b=Math.abs(this.range);
if(this.logarithmic)for(var e=0;e<a.length&&!(this.viewportMaximum<a[e].startValue);e++)this.viewportMinimum>a[e].endValue||(this.viewportMinimum>=a[e].startValue&&this.viewportMaximum<=a[e].endValue?c=0:this.viewportMinimum<=a[e].startValue&&this.viewportMaximum>=a[e].endValue?(b=b/a[e].endValue*a[e].startValue,c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/100):c-Math.min(a[e].spacing,0.1*c)):this.viewportMinimum>a[e].startValue&&this.viewportMaximum>=a[e].endValue?(b=b/
a[e].endValue*this.viewportMinimum,c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/100*Math.log(a[e].endValue/this.viewportMinimum)/Math.log(a[e].endValue/a[e].startValue)):c-Math.min(a[e].spacing,0.1*c)*Math.log(a[e].endValue/this.viewportMinimum)/Math.log(a[e].endValue/a[e].startValue)):this.viewportMinimum<=a[e].startValue&&this.viewportMaximum<a[e].endValue&&(b=b/this.viewportMaximum*a[e].startValue,c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/
100*Math.log(this.viewportMaximum/a[e].startValue)/Math.log(a[e].endValue/a[e].startValue)):c-Math.min(a[e].spacing,0.1*c)*Math.log(this.viewportMaximum/a[e].startValue)/Math.log(a[e].endValue/a[e].startValue)));else for(e=0;e<a.length&&!(this.viewportMaximum<a[e].startValue);e++)this.viewportMinimum>a[e].endValue||(this.viewportMinimum>=a[e].startValue&&this.viewportMaximum<=a[e].endValue?c=0:this.viewportMinimum<=a[e].startValue&&this.viewportMaximum>=a[e].endValue?(b=b-a[e].endValue+a[e].startValue,
c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/100):c-Math.min(a[e].spacing,0.1*c)):this.viewportMinimum>a[e].startValue&&this.viewportMaximum>=a[e].endValue?(b=b-a[e].endValue+this.viewportMinimum,c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/100*(a[e].endValue-this.viewportMinimum)/(a[e].endValue-a[e].startValue)):c-Math.min(a[e].spacing,0.1*c)*(a[e].endValue-this.viewportMinimum)/(a[e].endValue-a[e].startValue)):this.viewportMinimum<=a[e].startValue&&
this.viewportMaximum<a[e].endValue&&(b=b-this.viewportMaximum+a[e].startValue,c=0<a[e].spacing.toString().indexOf("%")?c*(1-parseFloat(a[e].spacing)/100*(this.viewportMaximum-a[e].startValue)/(a[e].endValue-a[e].startValue)):c-Math.min(a[e].spacing,0.1*c)*(this.viewportMaximum-a[e].startValue)/(a[e].endValue-a[e].startValue)));d.minimum=this.viewportMinimum;d.maximum=this.viewportMaximum;d.range=b;if("bottom"===this._position||"top"===this._position)this.logarithmic?(d.lnLogarithmBase=Math.log(this.logarithmBase),
d.pixelPerUnit=(this.reversed?-1:1)*c*d.lnLogarithmBase/Math.log(Math.abs(b))):d.pixelPerUnit=(this.reversed?-1:1)*c/Math.abs(b),d.reference=this.reversed?this.lineCoordinates.x2:this.lineCoordinates.x1;if("left"===this._position||"right"===this._position)this.logarithmic?(d.lnLogarithmBase=Math.log(this.logarithmBase),d.pixelPerUnit=(this.reversed?1:-1)*c*d.lnLogarithmBase/Math.log(Math.abs(b))):d.pixelPerUnit=(this.reversed?1:-1)*c/Math.abs(b),d.reference=this.reversed?this.lineCoordinates.y1:this.lineCoordinates.y2;
this.conversionParameters=d};D.prototype.calculateAxisParameters=function(){if(this.logarithmic)this.calculateLogarithmicAxisParameters();else{var a=this.chart.layoutManager.getFreeSpace(),d=!1,c=!1;"bottom"===this._position||"top"===this._position?(this.maxWidth=a.width,this.maxHeight=a.height):(this.maxWidth=a.height,this.maxHeight=a.width);var a="axisX"===this.type?"xySwapped"===this.chart.plotInfo.axisPlacement?62:70:"xySwapped"===this.chart.plotInfo.axisPlacement?50:40,b=4;"axisX"===this.type&&
(b=600>this.maxWidth?8:6);var a=Math.max(b,Math.floor(this.maxWidth/a)),e,g,h,b=0;!m(this.options.viewportMinimum)&&(!m(this.options.viewportMaximum)&&this.options.viewportMinimum>=this.options.viewportMaximum)&&(this.viewportMinimum=this.viewportMaximum=null);if(m(this.options.viewportMinimum)&&!m(this.sessionVariables.newViewportMinimum)&&!isNaN(this.sessionVariables.newViewportMinimum))this.viewportMinimum=this.sessionVariables.newViewportMinimum;else if(null===this.viewportMinimum||isNaN(this.viewportMinimum))this.viewportMinimum=
this.minimum;if(m(this.options.viewportMaximum)&&!m(this.sessionVariables.newViewportMaximum)&&!isNaN(this.sessionVariables.newViewportMaximum))this.viewportMaximum=this.sessionVariables.newViewportMaximum;else if(null===this.viewportMaximum||isNaN(this.viewportMaximum))this.viewportMaximum=this.maximum;if(this.scaleBreaks)for(b=0;b<this.scaleBreaks._appliedBreaks.length;b++)if((!m(this.sessionVariables.newViewportMinimum)&&this.sessionVariables.newViewportMinimum>=this.scaleBreaks._appliedBreaks[b].startValue||
!m(this.options.minimum)&&this.options.minimum>=this.scaleBreaks._appliedBreaks[b].startValue||!m(this.options.viewportMinimum)&&this.viewportMinimum>=this.scaleBreaks._appliedBreaks[b].startValue)&&(!m(this.sessionVariables.newViewportMaximum)&&this.sessionVariables.newViewportMaximum<=this.scaleBreaks._appliedBreaks[b].endValue||!m(this.options.maximum)&&this.options.maximum<=this.scaleBreaks._appliedBreaks[b].endValue||!m(this.options.viewportMaximum)&&this.viewportMaximum<=this.scaleBreaks._appliedBreaks[b].endValue)){this.scaleBreaks._appliedBreaks.splice(b,
1);break}if("axisX"===this.type){if(this.dataSeries&&0<this.dataSeries.length)for(e=0;e<this.dataSeries.length;e++)"dateTime"===this.dataSeries[e].xValueType&&(c=!0);e=null!==this.viewportMinimum?this.viewportMinimum:this.dataInfo.viewPortMin;g=null!==this.viewportMaximum?this.viewportMaximum:this.dataInfo.viewPortMax;0===g-e&&(b="undefined"===typeof this.options.interval?0.4:this.options.interval,g+=b,e-=b);Infinity!==this.dataInfo.minDiff?h=this.dataInfo.minDiff:1<g-e?h=0.5*Math.abs(g-e):(h=1,c&&
(d=!0))}else"axisY"===this.type&&(e=null!==this.viewportMinimum?this.viewportMinimum:this.dataInfo.viewPortMin,g=null!==this.viewportMaximum?this.viewportMaximum:this.dataInfo.viewPortMax,isFinite(e)||isFinite(g)?isFinite(e)?isFinite(g)||(g=e):e=g:(g="undefined"===typeof this.options.interval?-Infinity:this.options.interval,e="undefined"!==typeof this.options.interval||isFinite(this.dataInfo.minDiff)?0:Infinity),0===e&&0===g?(g+=9,e=0):0===g-e?(b=Math.min(Math.abs(0.01*Math.abs(g)),5),g+=b,e-=b):
e>g?(b=Math.min(0.01*Math.abs(this.getApparentDifference(g,e,null,!0)),5),0<=g?e=g-b:g=isFinite(e)?e+b:0):(b=Math.min(0.01*Math.abs(this.getApparentDifference(e,g,null,!0)),0.05),0!==g&&(g+=b),0!==e&&(e-=b)),h=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:1<g-e?0.5*Math.abs(g-e):1,this.includeZero&&(null===this.viewportMinimum||isNaN(this.viewportMinimum))&&0<e&&(e=0),this.includeZero&&(null===this.viewportMaximum||isNaN(this.viewportMaximum))&&0>g&&(g=0));b=this.getApparentDifference(isNaN(this.viewportMinimum)||
null===this.viewportMinimum?e:this.viewportMinimum,isNaN(this.viewportMaximum)||null===this.viewportMaximum?g:this.viewportMaximum,null,!0);if("axisX"===this.type&&c){this.valueType="dateTime";this.intervalType||(b/1<=a?(this.interval=1,this.intervalType="millisecond"):b/2<=a?(this.interval=2,this.intervalType="millisecond"):b/5<=a?(this.interval=5,this.intervalType="millisecond"):b/10<=a?(this.interval=10,this.intervalType="millisecond"):b/20<=a?(this.interval=20,this.intervalType="millisecond"):
b/50<=a?(this.interval=50,this.intervalType="millisecond"):b/100<=a?(this.interval=100,this.intervalType="millisecond"):b/200<=a?(this.interval=200,this.intervalType="millisecond"):b/250<=a?(this.interval=250,this.intervalType="millisecond"):b/300<=a?(this.interval=300,this.intervalType="millisecond"):b/400<=a?(this.interval=400,this.intervalType="millisecond"):b/500<=a?(this.interval=500,this.intervalType="millisecond"):b/(1*U.secondDuration)<=a?(this.interval=1,this.intervalType="second"):b/(2*
U.secondDuration)<=a?(this.interval=2,this.intervalType="second"):b/(5*U.secondDuration)<=a?(this.interval=5,this.intervalType="second"):b/(10*U.secondDuration)<=a?(this.interval=10,this.intervalType="second"):b/(15*U.secondDuration)<=a?(this.interval=15,this.intervalType="second"):b/(20*U.secondDuration)<=a?(this.interval=20,this.intervalType="second"):b/(30*U.secondDuration)<=a?(this.interval=30,this.intervalType="second"):b/(1*U.minuteDuration)<=a?(this.interval=1,this.intervalType="minute"):b/
(2*U.minuteDuration)<=a?(this.interval=2,this.intervalType="minute"):b/(5*U.minuteDuration)<=a?(this.interval=5,this.intervalType="minute"):b/(10*U.minuteDuration)<=a?(this.interval=10,this.intervalType="minute"):b/(15*U.minuteDuration)<=a?(this.interval=15,this.intervalType="minute"):b/(20*U.minuteDuration)<=a?(this.interval=20,this.intervalType="minute"):b/(30*U.minuteDuration)<=a?(this.interval=30,this.intervalType="minute"):b/(1*U.hourDuration)<=a?(this.interval=1,this.intervalType="hour"):b/
(2*U.hourDuration)<=a?(this.interval=2,this.intervalType="hour"):b/(3*U.hourDuration)<=a?(this.interval=3,this.intervalType="hour"):b/(6*U.hourDuration)<=a?(this.interval=6,this.intervalType="hour"):b/(1*U.dayDuration)<=a?(this.interval=1,this.intervalType="day"):b/(2*U.dayDuration)<=a?(this.interval=2,this.intervalType="day"):b/(4*U.dayDuration)<=a?(this.interval=4,this.intervalType="day"):b/(1*U.weekDuration)<=a?(this.interval=1,this.intervalType="week"):b/(2*U.weekDuration)<=a?(this.interval=2,
this.intervalType="week"):b/(3*U.weekDuration)<=a?(this.interval=3,this.intervalType="week"):b/(1*U.monthDuration)<=a?(this.interval=1,this.intervalType="month"):b/(2*U.monthDuration)<=a?(this.interval=2,this.intervalType="month"):b/(3*U.monthDuration)<=a?(this.interval=3,this.intervalType="month"):b/(6*U.monthDuration)<=a?(this.interval=6,this.intervalType="month"):(this.interval=b/(1*U.yearDuration)<=a?1:b/(2*U.yearDuration)<=a?2:b/(4*U.yearDuration)<=a?4:Math.floor(D.getNiceNumber(b/(a-1),!0)/
U.yearDuration),this.intervalType="year"));if(null===this.viewportMinimum||isNaN(this.viewportMinimum))this.viewportMinimum=e-h/2;if(null===this.viewportMaximum||isNaN(this.viewportMaximum))this.viewportMaximum=g+h/2;d?this.autoValueFormatString="MMM DD YYYY HH:mm":"year"===this.intervalType?this.autoValueFormatString="YYYY":"month"===this.intervalType?this.autoValueFormatString="MMM YYYY":"week"===this.intervalType?this.autoValueFormatString="MMM DD YYYY":"day"===this.intervalType?this.autoValueFormatString=
"MMM DD YYYY":"hour"===this.intervalType?this.autoValueFormatString="hh:mm TT":"minute"===this.intervalType?this.autoValueFormatString="hh:mm TT":"second"===this.intervalType?this.autoValueFormatString="hh:mm:ss TT":"millisecond"===this.intervalType&&(this.autoValueFormatString="fff'ms'");this.valueFormatString||(this.valueFormatString=this.autoValueFormatString)}else{this.intervalType="number";b=D.getNiceNumber(b,!1);this.interval=this.options&&0<this.options.interval?this.options.interval:D.getNiceNumber(b/
(a-1),!0);if(null===this.viewportMinimum||isNaN(this.viewportMinimum))this.viewportMinimum="axisX"===this.type?e-h/2:Math.floor(e/this.interval)*this.interval;if(null===this.viewportMaximum||isNaN(this.viewportMaximum))this.viewportMaximum="axisX"===this.type?g+h/2:Math.ceil(g/this.interval)*this.interval;0===this.viewportMaximum&&0===this.viewportMinimum&&(0===this.options.viewportMinimum?this.viewportMaximum+=10:0===this.options.viewportMaximum&&(this.viewportMinimum-=10),this.options&&"undefined"===
typeof this.options.interval&&(this.interval=D.getNiceNumber((this.viewportMaximum-this.viewportMinimum)/(a-1),!0)))}if(null===this.minimum||null===this.maximum)if("axisX"===this.type?(e=null!==this.minimum?this.minimum:this.dataInfo.min,g=null!==this.maximum?this.maximum:this.dataInfo.max,0===g-e&&(b="undefined"===typeof this.options.interval?0.4:this.options.interval,g+=b,e-=b),h=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:1<g-e?0.5*Math.abs(g-e):1):"axisY"===this.type&&(e=null!==this.minimum?
this.minimum:this.dataInfo.min,g=null!==this.maximum?this.maximum:this.dataInfo.max,isFinite(e)||isFinite(g)?0===e&&0===g?(g+=9,e=0):0===g-e?(b=Math.min(Math.abs(0.01*Math.abs(g)),5),g+=b,e-=b):e>g?(b=Math.min(0.01*Math.abs(this.getApparentDifference(g,e,null,!0)),5),0<=g?e=g-b:g=isFinite(e)?e+b:0):(b=Math.min(0.01*Math.abs(this.getApparentDifference(e,g,null,!0)),0.05),0!==g&&(g+=b),0!==e&&(e-=b)):(g="undefined"===typeof this.options.interval?-Infinity:this.options.interval,e="undefined"!==typeof this.options.interval||
isFinite(this.dataInfo.minDiff)?0:Infinity),h=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:1<g-e?0.5*Math.abs(g-e):1,this.includeZero&&(null===this.minimum||isNaN(this.minimum))&&0<e&&(e=0),this.includeZero&&(null===this.maximum||isNaN(this.maximum))&&0>g&&(g=0)),Math.abs(this.getApparentDifference(e,g,null,!0)),"axisX"===this.type&&c){this.valueType="dateTime";if(null===this.minimum||isNaN(this.minimum))this.minimum=e-h/2,this.minimum=Math.min(this.minimum,null===this.sessionVariables.viewportMinimum||
isNaN(this.sessionVariables.viewportMinimum)?Infinity:this.sessionVariables.viewportMinimum);if(null===this.maximum||isNaN(this.maximum))this.maximum=g+h/2,this.maximum=Math.max(this.maximum,null===this.sessionVariables.viewportMaximum||isNaN(this.sessionVariables.viewportMaximum)?-Infinity:this.sessionVariables.viewportMaximum)}else this.intervalType=this.valueType="number",null===this.minimum&&(this.minimum="axisX"===this.type?e-h/2:Math.floor(e/this.interval)*this.interval,this.minimum=Math.min(this.minimum,
null===this.sessionVariables.viewportMinimum||isNaN(this.sessionVariables.viewportMinimum)?Infinity:this.sessionVariables.viewportMinimum)),null===this.maximum&&(this.maximum="axisX"===this.type?g+h/2:Math.ceil(g/this.interval)*this.interval,this.maximum=Math.max(this.maximum,null===this.sessionVariables.viewportMaximum||isNaN(this.sessionVariables.viewportMaximum)?-Infinity:this.sessionVariables.viewportMaximum)),0===this.maximum&&0===this.minimum&&(0===this.options.minimum?this.maximum+=10:0===
this.options.maximum&&(this.minimum-=10));m(this.sessionVariables.newViewportMinimum)&&(this.viewportMinimum=Math.max(this.viewportMinimum,this.minimum));m(this.sessionVariables.newViewportMaximum)&&(this.viewportMaximum=Math.min(this.viewportMaximum,this.maximum));this.range=this.viewportMaximum-this.viewportMinimum;this.intervalStartPosition="axisX"===this.type&&c?this.getLabelStartPoint(new Date(this.viewportMinimum),this.intervalType,this.interval):Math.floor((this.viewportMinimum+0.2*this.interval)/
this.interval)*this.interval;this.valueFormatString||(this.valueFormatString=D.generateValueFormatString(this.range,2))}};D.prototype.calculateLogarithmicAxisParameters=function(){var a=this.chart.layoutManager.getFreeSpace(),d=Math.log(this.logarithmBase),c;"bottom"===this._position||"top"===this._position?(this.maxWidth=a.width,this.maxHeight=a.height):(this.maxWidth=a.height,this.maxHeight=a.width);var a="axisX"===this.type?500>this.maxWidth?7:Math.max(7,Math.floor(this.maxWidth/100)):Math.max(Math.floor(this.maxWidth/
50),3),b,e,g,h;h=1;if(null===this.viewportMinimum||isNaN(this.viewportMinimum))this.viewportMinimum=this.minimum;if(null===this.viewportMaximum||isNaN(this.viewportMaximum))this.viewportMaximum=this.maximum;if(this.scaleBreaks)for(h=0;h<this.scaleBreaks._appliedBreaks.length;h++)if((!m(this.sessionVariables.newViewportMinimum)&&this.sessionVariables.newViewportMinimum>=this.scaleBreaks._appliedBreaks[h].startValue||!m(this.options.minimum)&&this.options.minimum>=this.scaleBreaks._appliedBreaks[h].startValue||
!m(this.options.viewportMinimum)&&this.viewportMinimum>=this.scaleBreaks._appliedBreaks[h].startValue)&&(!m(this.sessionVariables.newViewportMaximum)&&this.sessionVariables.newViewportMaximum<=this.scaleBreaks._appliedBreaks[h].endValue||!m(this.options.maximum)&&this.options.maximum<=this.scaleBreaks._appliedBreaks[h].endValue||!m(this.options.viewportMaximum)&&this.viewportMaximum<=this.scaleBreaks._appliedBreaks[h].endValue)){this.scaleBreaks._appliedBreaks.splice(h,1);break}"axisX"===this.type?
(b=null!==this.viewportMinimum?this.viewportMinimum:this.dataInfo.viewPortMin,e=null!==this.viewportMaximum?this.viewportMaximum:this.dataInfo.viewPortMax,1===e/b&&(h=Math.pow(this.logarithmBase,"undefined"===typeof this.options.interval?0.4:this.options.interval),e*=h,b/=h),g=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:e/b>this.logarithmBase?e/b*Math.pow(this.logarithmBase,0.5):this.logarithmBase):"axisY"===this.type&&(b=null!==this.viewportMinimum?this.viewportMinimum:this.dataInfo.viewPortMin,
e=null!==this.viewportMaximum?this.viewportMaximum:this.dataInfo.viewPortMax,0>=b&&!isFinite(e)?(e="undefined"===typeof this.options.interval?0:this.options.interval,b=1):0>=b?b=e:isFinite(e)||(e=b),1===b&&1===e?(e*=this.logarithmBase-1/this.logarithmBase,b=1):1===e/b?(h=Math.min(e*Math.pow(this.logarithmBase,0.01),Math.pow(this.logarithmBase,5)),e*=h,b/=h):b>e?(h=Math.min(b/e*Math.pow(this.logarithmBase,0.01),Math.pow(this.logarithmBase,5)),1<=e?b=e/h:e=b*h):(h=Math.min(e/b*Math.pow(this.logarithmBase,
0.01),Math.pow(this.logarithmBase,0.04)),1!==e&&(e*=h),1!==b&&(b/=h)),g=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:e/b>this.logarithmBase?e/b*Math.pow(this.logarithmBase,0.5):this.logarithmBase,this.includeZero&&(null===this.viewportMinimum||isNaN(this.viewportMinimum))&&1<b&&(b=1),this.includeZero&&(null===this.viewportMaximum||isNaN(this.viewportMaximum))&&1>e&&(e=1));h=(isNaN(this.viewportMaximum)||null===this.viewportMaximum?e:this.viewportMaximum)/(isNaN(this.viewportMinimum)||null===
this.viewportMinimum?b:this.viewportMinimum);var n=(isNaN(this.viewportMaximum)||null===this.viewportMaximum?e:this.viewportMaximum)-(isNaN(this.viewportMinimum)||null===this.viewportMinimum?b:this.viewportMinimum);this.intervalType="number";h=Math.pow(this.logarithmBase,D.getNiceNumber(Math.abs(Math.log(h)/d),!1));this.options&&0<this.options.interval?this.interval=this.options.interval:(this.interval=D.getNiceExponent(Math.log(h)/d/(a-1),!0),c=D.getNiceNumber(n/(a-1),!0));if(null===this.viewportMinimum||
isNaN(this.viewportMinimum))this.viewportMinimum="axisX"===this.type?b/Math.sqrt(g):Math.pow(this.logarithmBase,this.interval*Math.floor(Math.log(b)/d/this.interval));if(null===this.viewportMaximum||isNaN(this.viewportMaximum))this.viewportMaximum="axisX"===this.type?e*Math.sqrt(g):Math.pow(this.logarithmBase,this.interval*Math.ceil(Math.log(e)/d/this.interval));1===this.viewportMaximum&&1===this.viewportMinimum&&(1===this.options.viewportMinimum?this.viewportMaximum*=this.logarithmBase-1/this.logarithmBase:
1===this.options.viewportMaximum&&(this.viewportMinimum/=this.logarithmBase-1/this.logarithmBase),this.options&&"undefined"===typeof this.options.interval&&(this.interval=D.getNiceExponent(Math.ceil(Math.log(h)/d)/(a-1)),c=D.getNiceNumber((this.viewportMaximum-this.viewportMinimum)/(a-1),!0)));if(null===this.minimum||null===this.maximum)"axisX"===this.type?(b=null!==this.minimum?this.minimum:this.dataInfo.min,e=null!==this.maximum?this.maximum:this.dataInfo.max,1===e/b&&(h=Math.pow(this.logarithmBase,
"undefined"===typeof this.options.interval?0.4:this.options.interval),e*=h,b/=h),g=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:e/b>this.logarithmBase?e/b*Math.pow(this.logarithmBase,0.5):this.logarithmBase):"axisY"===this.type&&(b=null!==this.minimum?this.minimum:this.dataInfo.min,e=null!==this.maximum?this.maximum:this.dataInfo.max,isFinite(b)||isFinite(e)?1===b&&1===e?(e*=this.logarithmBase,b/=this.logarithmBase):1===e/b?(h=Math.pow(this.logarithmBase,this.interval),e*=h,b/=h):b>e?(h=
Math.min(0.01*(b/e),5),1<=e?b=e/h:e=b*h):(h=Math.min(e/b*Math.pow(this.logarithmBase,0.01),Math.pow(this.logarithmBase,0.04)),1!==e&&(e*=h),1!==b&&(b/=h)):(e="undefined"===typeof this.options.interval?0:this.options.interval,b=1),g=Infinity!==this.dataInfo.minDiff?this.dataInfo.minDiff:e/b>this.logarithmBase?e/b*Math.pow(this.logarithmBase,0.5):this.logarithmBase,this.includeZero&&(null===this.minimum||isNaN(this.minimum))&&1<b&&(b=1),this.includeZero&&(null===this.maximum||isNaN(this.maximum))&&
1>e&&(e=1)),this.intervalType="number",null===this.minimum&&(this.minimum="axisX"===this.type?b/Math.sqrt(g):Math.pow(this.logarithmBase,this.interval*Math.floor(Math.log(b)/d/this.interval)),m(null===this.sessionVariables.viewportMinimum||isNaN(this.sessionVariables.viewportMinimum)?"undefined"===typeof this.sessionVariables.newViewportMinimum?Infinity:this.sessionVariables.newViewportMinimum:this.sessionVariables.viewportMinimum)||(this.minimum=Math.min(this.minimum,null===this.sessionVariables.viewportMinimum||
isNaN(this.sessionVariables.viewportMinimum)?"undefined"===typeof this.sessionVariables.newViewportMinimum?Infinity:this.sessionVariables.newViewportMinimum:this.sessionVariables.viewportMinimum))),null===this.maximum&&(this.maximum="axisX"===this.type?e*Math.sqrt(g):Math.pow(this.logarithmBase,this.interval*Math.ceil(Math.log(e)/d/this.interval)),m(null===this.sessionVariables.viewportMaximum||isNaN(this.sessionVariables.viewportMaximum)?"undefined"===typeof this.sessionVariables.newViewportMaximum?
0:this.sessionVariables.newViewportMaximum:this.sessionVariables.viewportMaximum)||(this.maximum=Math.max(this.maximum,null===this.sessionVariables.viewportMaximum||isNaN(this.sessionVariables.viewportMaximum)?"undefined"===typeof this.sessionVariables.newViewportMaximum?0:this.sessionVariables.newViewportMaximum:this.sessionVariables.viewportMaximum))),1===this.maximum&&1===this.minimum&&(1===this.options.minimum?this.maximum*=this.logarithmBase-1/this.logarithmBase:1===this.options.maximum&&(this.minimum/=
this.logarithmBase-1/this.logarithmBase));this.viewportMinimum=Math.max(this.viewportMinimum,this.minimum);this.viewportMaximum=Math.min(this.viewportMaximum,this.maximum);this.viewportMinimum>this.viewportMaximum&&(!this.options.viewportMinimum&&!this.options.minimum||this.options.viewportMaximum||this.options.maximum?this.options.viewportMinimum||this.options.minimum||!this.options.viewportMaximum&&!this.options.maximum||(this.viewportMinimum=this.minimum=(this.options.viewportMaximum||this.options.maximum)/
Math.pow(this.logarithmBase,2*Math.ceil(this.interval))):this.viewportMaximum=this.maximum=this.options.viewportMinimum||this.options.minimum);b=Math.pow(this.logarithmBase,Math.floor(Math.log(this.viewportMinimum)/(d*this.interval)+0.2)*this.interval);this.range=this.viewportMaximum/this.viewportMinimum;this.noTicks=a;if(!this.options.interval&&this.range<Math.pow(this.logarithmBase,8>this.viewportMaximum||3>a?2:3)){for(d=Math.floor(this.viewportMinimum/c+0.5)*c;d<this.viewportMinimum;)d+=c;this.equidistantInterval=
!1;this.intervalStartPosition=d;this.interval=c}else this.options.interval||(c=Math.ceil(this.interval),this.range>this.interval&&(this.interval=c,b=Math.pow(this.logarithmBase,Math.floor(Math.log(this.viewportMinimum)/(d*this.interval)+0.2)*this.interval))),this.equidistantInterval=!0,this.intervalStartPosition=b;if(!this.valueFormatString&&(this.valueFormatString="#,##0.##",1>this.viewportMinimum)){d=Math.floor(Math.abs(Math.log(this.viewportMinimum)/Math.LN10))+2;if(isNaN(d)||!isFinite(d))d=2;
if(2<d)for(h=0;h<d-2;h++)this.valueFormatString+="#"}};D.generateValueFormatString=function(a,d){var c="#,##0.",b=d;1>a&&(b+=Math.floor(Math.abs(Math.log(a)/Math.LN10)),isNaN(b)||!isFinite(b))&&(b=d);for(var e=0;e<b;e++)c+="#";return c};D.getNiceExponent=function(a,d){var c=Math.floor(Math.log(a)/Math.LN10),b=a/Math.pow(10,c),b=0>c?1>=b?1:5>=b?5:10:Math.max(Math.floor(b),1);return-20>c?Number(b*Math.pow(10,c)):Number((b*Math.pow(10,c)).toFixed(20))};D.getNiceNumber=function(a,d){var c=Math.floor(Math.log(a)/
Math.LN10),b=a/Math.pow(10,c),b=d?1.5>b?1:3>b?2:7>b?5:10:1>=b?1:2>=b?2:5>=b?5:10;return-20>c?Number(b*Math.pow(10,c)):Number((b*Math.pow(10,c)).toFixed(20))};D.prototype.getLabelStartPoint=function(){var a=U[this.intervalType+"Duration"]*this.interval,a=new Date(Math.floor(this.viewportMinimum/a)*a);if("millisecond"!==this.intervalType)if("second"===this.intervalType)0<a.getMilliseconds()&&(a.setSeconds(a.getSeconds()+1),a.setMilliseconds(0));else if("minute"===this.intervalType){if(0<a.getSeconds()||
0<a.getMilliseconds())a.setMinutes(a.getMinutes()+1),a.setSeconds(0),a.setMilliseconds(0)}else if("hour"===this.intervalType){if(0<a.getMinutes()||0<a.getSeconds()||0<a.getMilliseconds())a.setHours(a.getHours()+1),a.setMinutes(0),a.setSeconds(0),a.setMilliseconds(0)}else if("day"===this.intervalType){if(0<a.getHours()||0<a.getMinutes()||0<a.getSeconds()||0<a.getMilliseconds())a.setDate(a.getDate()+1),a.setHours(0),a.setMinutes(0),a.setSeconds(0),a.setMilliseconds(0)}else if("week"===this.intervalType){if(0<
a.getDay()||0<a.getHours()||0<a.getMinutes()||0<a.getSeconds()||0<a.getMilliseconds())a.setDate(a.getDate()+(7-a.getDay())),a.setHours(0),a.setMinutes(0),a.setSeconds(0),a.setMilliseconds(0)}else if("month"===this.intervalType){if(1<a.getDate()||0<a.getHours()||0<a.getMinutes()||0<a.getSeconds()||0<a.getMilliseconds())a.setMonth(a.getMonth()+1),a.setDate(1),a.setHours(0),a.setMinutes(0),a.setSeconds(0),a.setMilliseconds(0)}else"year"===this.intervalType&&(0<a.getMonth()||1<a.getDate()||0<a.getHours()||
0<a.getMinutes()||0<a.getSeconds()||0<a.getMilliseconds())&&(a.setFullYear(a.getFullYear()+1),a.setMonth(0),a.setDate(1),a.setHours(0),a.setMinutes(0),a.setSeconds(0),a.setMilliseconds(0));return a};qa(fa,L);qa(da,L);da.prototype.createUserOptions=function(a){if("undefined"!==typeof a||this.options._isPlaceholder){var d=0;this.parent.options._isPlaceholder&&this.parent.createUserOptions();this.options._isPlaceholder||(Ea(this.parent[this.optionsName]),d=this.parent.options[this.optionsName].indexOf(this.options));
this.options="undefined"===typeof a?{}:a;this.parent.options[this.optionsName][d]=this.options}};da.prototype.render=function(a){if(0!==this.spacing||0!==this.options.lineThickness&&("undefined"!==typeof this.options.lineThickness||0!==this.parent.lineThickness)){var d=this.ctx,c=this.ctx.globalAlpha;this.ctx=a||this.ctx;this.ctx.save();this.ctx.beginPath();this.ctx.rect(this.chart.plotArea.x1,this.chart.plotArea.y1,this.chart.plotArea.width,this.chart.plotArea.height);this.ctx.clip();var b=this.scaleBreaks.parent.getPixelCoordinatesOnAxis(this.startValue),
e=this.scaleBreaks.parent.getPixelCoordinatesOnAxis(this.endValue);this.ctx.strokeStyle=this.lineColor;this.ctx.fillStyle=this.color;this.ctx.beginPath();this.ctx.globalAlpha=1;Z(this.id);var g,h,m,n,k,l;a=Math.max(this.spacing,3);var p=Math.max(0,this.lineThickness);this.ctx.lineWidth=p;this.ctx.setLineDash&&this.ctx.setLineDash(H(this.lineDashType,p));if("bottom"===this.scaleBreaks.parent._position||"top"===this.scaleBreaks.parent._position)if(b=1===p%2?(b.x<<0)+0.5:b.x<<0,h=1===p%2?(e.x<<0)+0.5:
e.x<<0,"top"===this.scaleBreaks.parent._position?(e=this.chart.plotArea.y1,m=this.chart.plotArea.y2+p/2+0.5<<0):(e=this.chart.plotArea.y2,m=this.chart.plotArea.y1-p/2+0.5<<0,a*=-1),this.bounds={x1:b-p/2,y1:e,x2:h+p/2,y2:m},this.ctx.moveTo(b,e),"straight"===this.type||"top"===this.scaleBreaks.parent._position&&0>=a||"bottom"===this.scaleBreaks.parent._position&&0<=a)this.ctx.lineTo(b,m),this.ctx.lineTo(h,m),this.ctx.lineTo(h,e);else if("wavy"===this.type){n=b;k=e;g=0.5;l=(m-k)/a/3;for(var q=0;q<l;q++)this.ctx.bezierCurveTo(n+
g*a,k+a,n+g*a,k+2*a,n,k+3*a),k+=3*a,g*=-1;this.ctx.bezierCurveTo(n+g*a,k+a,n+g*a,k+2*a,n,k+3*a);n=h;g*=-1;this.ctx.lineTo(n,k);for(q=0;q<l;q++)this.ctx.bezierCurveTo(n+g*a,k-a,n+g*a,k-2*a,n,k-3*a),k-=3*a,g*=-1}else{if("zigzag"===this.type){g=-1;k=e+a;n=b+a;l=(m-k)/a/2;for(q=0;q<l;q++)this.ctx.lineTo(n,k),n+=2*g*a,k+=2*a,g*=-1;this.ctx.lineTo(n,k);n+=h-b;for(q=0;q<l+1;q++)this.ctx.lineTo(n,k),n+=2*g*a,k-=2*a,g*=-1;this.ctx.lineTo(n+g*a,k+a)}}else if("left"===this.scaleBreaks.parent._position||"right"===
this.scaleBreaks.parent._position)if(e=1===p%2?(e.y<<0)+0.5:e.y<<0,m=1===p%2?(b.y<<0)+0.5:b.y<<0,"left"===this.scaleBreaks.parent._position?(b=this.chart.plotArea.x1,h=this.chart.plotArea.x2+p/2+0.5<<0):(b=this.chart.plotArea.x2,h=this.chart.plotArea.x1-p/2+0.5<<0,a*=-1),this.bounds={x1:b,y1:e-p/2,x2:h,y2:m+p/2},this.ctx.moveTo(b,e),"straight"===this.type||"left"===this.scaleBreaks.parent._position&&0>=a||"right"===this.scaleBreaks.parent._position&&0<=a)this.ctx.lineTo(h,e),this.ctx.lineTo(h,m),
this.ctx.lineTo(b,m);else if("wavy"===this.type){n=b;k=e;g=0.5;l=(h-n)/a/3;for(q=0;q<l;q++)this.ctx.bezierCurveTo(n+a,k+g*a,n+2*a,k+g*a,n+3*a,k),n+=3*a,g*=-1;this.ctx.bezierCurveTo(n+a,k+g*a,n+2*a,k+g*a,n+3*a,k);k=m;g*=-1;this.ctx.lineTo(n,k);for(q=0;q<l;q++)this.ctx.bezierCurveTo(n-a,k+g*a,n-2*a,k+g*a,n-3*a,k),n-=3*a,g*=-1}else if("zigzag"===this.type){g=1;k=e-a;n=b+a;l=(h-n)/a/2;for(q=0;q<l;q++)this.ctx.lineTo(n,k),k+=2*g*a,n+=2*a,g*=-1;this.ctx.lineTo(n,k);k+=m-e;for(q=0;q<l+1;q++)this.ctx.lineTo(n,
k),k+=2*g*a,n-=2*a,g*=-1;this.ctx.lineTo(n+a,k+g*a)}0<p&&this.ctx.stroke();this.ctx.closePath();this.ctx.globalAlpha=this.fillOpacity;this.ctx.globalCompositeOperation="destination-over";this.ctx.fill();this.ctx.restore();this.ctx.globalAlpha=c;this.ctx=d}};qa(O,L);O.prototype.createUserOptions=function(a){if("undefined"!==typeof a||this.options._isPlaceholder){var d=0;this.parent.options._isPlaceholder&&this.parent.createUserOptions();this.options._isPlaceholder||(Ea(this.parent.stripLines),d=this.parent.options.stripLines.indexOf(this.options));
this.options="undefined"===typeof a?{}:a;this.parent.options.stripLines[d]=this.options}};O.prototype.render=function(){this.ctx.save();var a=this.parent.getPixelCoordinatesOnAxis(this.value),d=Math.abs("pixel"===this._thicknessType?this.thickness:Math.abs(this.parent.convertValueToPixel(this.endValue)-this.parent.convertValueToPixel(this.startValue)));if(0<d){var c=null===this.opacity?1:this.opacity;this.ctx.strokeStyle=this.color;this.ctx.beginPath();var b=this.ctx.globalAlpha;this.ctx.globalAlpha=
c;Z(this.id);var e,g,h,m;this.ctx.lineWidth=d;this.ctx.setLineDash&&this.ctx.setLineDash(H(this.lineDashType,d));if("bottom"===this.parent._position||"top"===this.parent._position)e=g=1===this.ctx.lineWidth%2?(a.x<<0)+0.5:a.x<<0,h=this.chart.plotArea.y1,m=this.chart.plotArea.y2,this.bounds={x1:e-d/2,y1:h,x2:g+d/2,y2:m};else if("left"===this.parent._position||"right"===this.parent._position)h=m=1===this.ctx.lineWidth%2?(a.y<<0)+0.5:a.y<<0,e=this.chart.plotArea.x1,g=this.chart.plotArea.x2,this.bounds=
{x1:e,y1:h-d/2,x2:g,y2:m+d/2};this.ctx.moveTo(e,h);this.ctx.lineTo(g,m);this.ctx.stroke();this.ctx.globalAlpha=b}this.ctx.restore()};qa(ea,L);ea.prototype.showAt=function(a){if(!this.enabled)return!1;var d=this.chart,c=!1;d.resetOverlayedCanvas();d.clearedOverlayedCanvas=this.parent.type;this.chart.renderCrosshairs(this.parent);if("xySwapped"===d.plotInfo.axisPlacement)if("bottom"===this.parent._position)for(var b=0;b<d.axisY.length;b++)this.parent===d.axisY[b]&&(d.axisY[b]._crosshairValue=a>=d.axisY[b].viewportMinimum&&
a<=d.axisY[b].viewportMaximum?a:null);else if("top"===this.parent._position)for(b=0;b<d.axisY2.length;b++)this.parent===d.axisY2[b]&&(d.axisY2[b]._crosshairValue=a>=d.axisY2[b].viewportMinimum&&a<=d.axisY2[b].viewportMaximum?a:null);else if("left"===this.parent._position)for(b=0;b<d.axisX.length;b++)this.parent===d.axisX[b]&&(d.axisX[b]._crosshairValue=a>=d.axisX[b].viewportMinimum&&a<=d.axisX[b].viewportMaximum?a:null);else{if("right"===this.parent._position)for(b=0;b<d.axisX2.length;b++)this.parent===
d.axisX2[b]&&(d.axisX2[b]._crosshairValue=a>=d.axisX2[b].viewportMinimum&&a<=d.axisX2[b].viewportMaximum?a:null)}else if("bottom"===this.parent._position)for(b=0;b<d.axisX.length;b++)this.parent===d.axisX[b]&&(d.axisX[b]._crosshairValue=a>=d.axisX[b].viewportMinimum&&a<=d.axisX[b].viewportMaximum?a:null);else if("top"===this.parent._position)for(b=0;b<d.axisX2.length;b++)this.parent===d.axisX2[b]&&(d.axisX2[b]._crosshairValue=a>=d.axisX2[b].viewportMinimum&&a<=d.axisX2[b].viewportMaximum?a:null);
else if("left"===this.parent._position)for(b=0;b<d.axisY.length;b++)this.parent===d.axisY[b]&&(d.axisY[b]._crosshairValue=a>=d.axisY[b].viewportMinimum&&a<=d.axisY[b].viewportMaximum?a:null);else if("right"===this.parent._position)for(b=0;b<d.axisY2.length;b++)this.parent===d.axisY2[b]&&(d.axisY2[b]._crosshairValue=a>=d.axisY2[b].viewportMinimum&&a<=d.axisY2[b].viewportMaximum?a:null);for(b=0;b<d.axisX.length;b++)a=d.axisX[b]._crosshairValue,d.axisX[b].crosshair&&(d.axisX[b].crosshair.enabled&&!m(a)&&
a>=d.axisX[b].viewportMinimum&&a<=d.axisX[b].viewportMaximum)&&(d.axisX[b].showCrosshair(a),d.axisX[b].crosshair._updatedValue=a,this===d.axisX[b].crosshair&&(c=!0));for(b=0;b<d.axisX2.length;b++)a=d.axisX2[b]._crosshairValue,d.axisX2[b].crosshair&&(d.axisX2[b].crosshair.enabled&&!m(a)&&a>=d.axisX2[b].viewportMinimum&&a<=d.axisX2[b].viewportMaximum)&&(d.axisX2[b].showCrosshair(a),d.axisX2[b].crosshair._updatedValue=a,this===d.axisX2[b].crosshair&&(c=!0));for(b=0;b<d.axisY.length;b++)a=d.axisY[b]._crosshairValue,
d.axisY[b].crosshair&&(d.axisY[b].crosshair.enabled&&!m(a)&&a>=d.axisY[b].viewportMinimum&&a<=d.axisY[b].viewportMaximum)&&(d.axisY[b].showCrosshair(a),d.axisY[b].crosshair._updatedValue=a,this===d.axisY[b].crosshair&&(c=!0));for(b=0;b<d.axisY2.length;b++)a=d.axisY2[b]._crosshairValue,d.axisY2[b].crosshair&&(d.axisY2[b].crosshair.enabled&&!m(a)&&a>=d.axisY2[b].viewportMinimum&&a<=d.axisY2[b].viewportMaximum)&&(d.axisY2[b].showCrosshair(a),d.axisY2[b].crosshair._updatedValue=a,this===d.axisY2[b].crosshair&&
(c=!0));this.chart.toolTip&&this.chart.toolTip._entries&&this.chart.toolTip.highlightObjects(this.chart.toolTip._entries);return c};ea.prototype.hide=function(){this.chart.resetOverlayedCanvas();this.chart.renderCrosshairs(this.parent);this._hidden=!0};ea.prototype.render=function(a,d,c){var b,e,g,h,n=null,t=null,k=null,l="";this.valueFormatString||("dateTime"===this.parent.valueType?this.valueFormatString=this.parent.valueFormatString:(k=0,k="xySwapped"===this.chart.plotInfo.axisPlacement?50<this.parent.range?
0:500<this.chart.width&&25>this.parent.range?2:Math.floor(Math.abs(Math.log(this.parent.range)/Math.LN10))+(5>this.parent.range?2:10>this.parent.range?1:0):50<this.parent.range?0:Math.floor(Math.abs(Math.log(this.parent.range)/Math.LN10))+(5>this.parent.range?2:10>this.parent.range?1:0),this.valueFormatString=D.generateValueFormatString(this.parent.range,k)));var p=null===this.opacity?1:this.opacity,q=Math.abs("pixel"===this._thicknessType?this.thickness:this.parent.conversionParameters.pixelPerUnit*
this.thickness),f=this.chart.overlaidCanvasCtx,w=f.globalAlpha;f.beginPath();f.strokeStyle=this.color;f.lineWidth=q;f.save();this.labelFontSize=Math.abs(m(this.options.labelFontSize)?this.parent.labelFontSize:this.labelFontSize);this.labelMaxWidth=m(this.options.labelMaxWidth)?0.3*this.chart.width:this.labelMaxWidth;this.labelMaxHeight=m(this.options.labelWrap)||this.labelWrap?0.3*this.chart.height:2*this.labelFontSize;0<q&&f.setLineDash&&f.setLineDash(H(this.lineDashType,q));k=new la(f,{x:0,y:0,
padding:{top:2,right:3,bottom:2,left:4},backgroundColor:this.labelBackgroundColor,borderColor:this.labelBorderColor,borderThickness:this.labelBorderThickness,cornerRadius:this.labelCornerRadius,maxWidth:this.labelMaxWidth,maxHeight:this.labelMaxHeight,angle:this.labelAngle,text:l,horizontalAlign:"left",fontSize:this.labelFontSize,fontFamily:this.labelFontFamily,fontWeight:this.labelFontWeight,fontColor:this.labelFontColor,fontStyle:this.labelFontStyle,textBaseline:"middle"});if(this.snapToDataPoint){var z=
0,l=[];if("xySwapped"===this.chart.plotInfo.axisPlacement){var x=null;if("bottom"===this.parent._position||"top"===this.parent._position)z=this.parent.dataSeries[0].axisX.convertPixelToValue({y:d});else if("left"===this.parent._position||"right"===this.parent._position)z=this.parent.convertPixelToValue({y:d});for(var s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(x.dataSeries=this.parent.dataSeries[s],null!==x.dataPoint.y&&x.dataSeries.visible&&
l.push(x));x=null;if(0===l.length)return;l.sort(function(a,b){return a.distance-b.distance});var y=x=0;yPercent=cumulativeY=0;for(var z=Infinity,v,s=0;s<l.length;s++){if("rangeBar"===l[s].dataSeries.type||"error"===l[s].dataSeries.type){if(l[s].dataPoint.y)for(var E=0;E<l[s].dataPoint.y.length;E++)y=Math.abs(a-this.parent.convertValueToPixel(l[s].dataPoint.y[E])),y<=z&&(z=y,x=s)}else"stackedBar"===l[s].dataSeries.type?(cumulativeY=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,y=Math.abs(a-
this.parent.convertValueToPixel(cumulativeY)),y<=z&&(z=y,x=s)):"stackedBar100"===l[s].dataSeries.type?(y=l[0].dataPoint.x.getTime?l[0].dataPoint.x.getTime():l[0].dataPoint.x,m(v)&&(v=Math.abs(a-this.parent.convertValueToPixel(100*(l[0].dataSeries.dataPointEOs[l[0].index].cumulativeY/l[0].dataSeries.plotUnit.dataPointYSums[y])))),cumulativeY=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,y=l[s].dataPoint.x.getTime?l[s].dataPoint.x.getTime():l[s].dataPoint.x,yPercent=100*(cumulativeY/l[s].dataSeries.plotUnit.dataPointYSums[y]),
y=Math.abs(a-this.parent.convertValueToPixel(yPercent)),y<=v&&(v=y,x=s)):(y=Math.abs(a-this.parent.convertValueToPixel(l[s].dataPoint.y)),y<=z&&(z=y,x=s));m(v)||(z=Math.min(z,v))}v=l[x];s=0;if("bottom"===this.parent._position||"top"===this.parent._position){if("rangeBar"===v.dataSeries.type||"error"===v.dataSeries.type){z=Math.abs(a-this.parent.convertValueToPixel(v.dataPoint.y[0]));for(l=0;l<v.dataPoint.y.length;l++)y=Math.abs(a-this.parent.convertValueToPixel(v.dataPoint.y[l])),y<z&&(z=y,s=l);n=
1===f.lineWidth%2?(this.parent.convertValueToPixel(v.dataPoint.y[s])<<0)+0.5:this.parent.convertValueToPixel(v.dataPoint.y[s])<<0;this.value=v.dataPoint.y[s];k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y[s]}):m(this.options.label)?ga(m(c)?v.dataPoint.y[s]:c,this.valueFormatString,this.chart._cultureInfo):this.label}else"stackedBar"===v.dataSeries.type?(z=Math.abs(a-this.parent.convertValueToPixel(l[0].dataPoint.y)),
cumulativeY=v.dataSeries.dataPointEOs[v.index].cumulativeY,n=1===f.lineWidth%2?(this.parent.convertValueToPixel(cumulativeY)<<0)+0.5:this.parent.convertValueToPixel(cumulativeY)<<0,this.value=cumulativeY,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y}):m(this.options.label)?ga(m(c)?v.dataPoint.y:c,this.valueFormatString,this.chart._cultureInfo):this.label):"stackedBar100"===v.dataSeries.type?(z=Math.abs(a-this.parent.convertValueToPixel(l[0].dataPoint.y)),
cumulativeY=v.dataSeries.dataPointEOs[v.index].cumulativeY,y=v.dataPoint.x.getTime?v.dataPoint.x.getTime():v.dataPoint.x,yPercent=100*(cumulativeY/v.dataSeries.plotUnit.dataPointYSums[y]),n=1===f.lineWidth%2?(this.parent.convertValueToPixel(yPercent)<<0)+0.5:this.parent.convertValueToPixel(yPercent)<<0,this.value=yPercent,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:yPercent}):m(this.options.label)?ga(m(c)?yPercent:c,this.valueFormatString,
this.chart._cultureInfo):this.label):(n=1===f.lineWidth%2?(this.parent.convertValueToPixel(v.dataPoint.y)<<0)+0.5:this.parent.convertValueToPixel(v.dataPoint.y)<<0,this.value=v.dataPoint.y,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y}):m(this.options.label)?ga(m(c)?v.dataPoint.y:c,this.valueFormatString,this.chart._cultureInfo):this.label);b=e=n;g=this.chart.plotArea.y1;h=this.chart.plotArea.y2;this.bounds={x1:b-
q/2,y1:g,x2:e+q/2,y2:h};k.x=b-k.measureText().width/2;k.x+k.width>this.chart.bounds.x2?k.x=this.chart.bounds.x2-k.width:k.x<this.chart.bounds.x1&&(k.x=this.chart.bounds.x1);k.y=this.parent.lineCoordinates.y2+("top"===this.parent._position?-k.height+this.parent.tickLength:k.fontSize/2)+2;k.y+k.height>this.chart.bounds.y2?k.y=this.chart.bounds.y2-k.height:k.y<this.chart.bounds.y1&&(k.y=this.chart.bounds.y1)}else if("left"===this.parent._position||"right"===this.parent._position){g=h=t=1===f.lineWidth%
2?(this.parent.convertValueToPixel(v.dataPoint.x)<<0)+0.5:this.parent.convertValueToPixel(v.dataPoint.x)<<0;b=this.chart.plotArea.x1;e=this.chart.plotArea.x2;this.bounds={x1:b,y1:g-q/2,x2:e,y2:h+q/2};s=!1;if(this.parent.labels)for(z=Math.ceil(this.parent.interval),l=0;l<this.parent.viewportMaximum;l+=z)if(this.parent.labels[l])s=!0;else{s=!1;break}if(s){if("axisX"===this.parent.type)for(z=this.parent.convertPixelToValue({y:d}),x=null,s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,
!0))&&0<=x.index&&(k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?x.dataPoint.label:this.label)}else k.text="dateTime"!==this.parent.valueType||this.parent.logarithmic?this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?ga(v.dataPoint.x,this.valueFormatString,this.chart._cultureInfo):this.label:
this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?Da(v.dataPoint.x,this.valueFormatString,this.chart._cultureInfo):this.label;this.value=v.dataPoint.x;k.y=h+k.fontSize/2-k.measureText().height/2+2;k.y-k.fontSize/2<this.chart.bounds.y1?k.y=this.chart.bounds.y1+k.fontSize/2+2:k.y+k.measureText().height-k.fontSize/2>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-k.measureText().height+k.fontSize/2);"left"===
this.parent._position?k.x=this.parent.lineCoordinates.x2-k.measureText().width:"right"===this.parent._position&&(k.x=this.parent.lineCoordinates.x2)}}else if("bottom"===this.parent._position||"top"===this.parent._position){z=this.parent.convertPixelToValue({x:a});for(s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(x.dataSeries=this.parent.dataSeries[s],null!==x.dataPoint.y&&x.dataSeries.visible&&l.push(x));if(0===l.length)return;l.sort(function(a,
b){return a.distance-b.distance});v=l[0];b=e=n=1===f.lineWidth%2?(this.parent.convertValueToPixel(v.dataPoint.x)<<0)+0.5:this.parent.convertValueToPixel(v.dataPoint.x)<<0;g=this.chart.plotArea.y1;h=this.chart.plotArea.y2;this.bounds={x1:b-q/2,y1:g,x2:e+q/2,y2:h};s=!1;if(this.parent.labels)for(z=Math.ceil(this.parent.interval),l=0;l<this.parent.viewportMaximum;l+=z)if(this.parent.labels[l])s=!0;else{s=!1;break}if(s){if("axisX"===this.parent.type)for(z=this.parent.convertPixelToValue({x:a}),x=null,
s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?x.dataPoint.label:this.label)}else k.text="dateTime"!==this.parent.valueType||this.parent.logarithmic?this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?
ga(v.dataPoint.x,this.valueFormatString,this.chart._cultureInfo):this.label:this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.x}):m(this.options.label)?Da(v.dataPoint.x,this.valueFormatString,this.chart._cultureInfo):this.label;this.value=v.dataPoint.x;k.x=b-k.measureText().width/2;k.x+k.width>this.chart.bounds.x2&&(k.x=this.chart.bounds.x2-k.width);k.x<this.chart.bounds.x1&&(k.x=this.chart.bounds.x1);"bottom"===this.parent._position?
k.y=this.parent.lineCoordinates.y2+k.fontSize/2+2:"top"===this.parent._position&&(k.y=this.parent.lineCoordinates.y1-k.height+k.fontSize/2+2)}else if("left"===this.parent._position||"right"===this.parent._position){!m(this.parent.dataSeries)&&0<this.parent.dataSeries.length&&(z=this.parent.dataSeries[0].axisX.convertPixelToValue({x:a}));for(s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(x.dataSeries=this.parent.dataSeries[s],null!==x.dataPoint.y&&
x.dataSeries.visible&&l.push(x));if(0===l.length)return;l.sort(function(a,b){return a.distance-b.distance});y=x=0;z=Infinity;for(s=0;s<l.length;s++){if("rangeColumn"===l[s].dataSeries.type||"rangeArea"===l[s].dataSeries.type||"error"===l[s].dataSeries.type||"rangeSplineArea"===l[s].dataSeries.type||"candlestick"===l[s].dataSeries.type||"ohlc"===l[s].dataSeries.type||"boxAndWhisker"===l[s].dataSeries.type){if(l[s].dataPoint.y)for(E=0;E<l[s].dataPoint.y.length;E++)y=Math.abs(d-this.parent.convertValueToPixel(l[s].dataPoint.y[E])),
y<=z&&(z=y,x=s)}else"stackedColumn"===l[s].dataSeries.type?(b=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,y=Math.abs(d-this.parent.convertValueToPixel(b)),y<=z&&(z=y,x=s)):"stackedArea"===l[s].dataSeries.type?(b=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,y=Math.abs(d-this.parent.convertValueToPixel(b)),y<=z&&(z=y,x=s)):"stackedColumn100"===l[s].dataSeries.type||"stackedArea100"===l[s].dataSeries.type?(y=l[0].dataPoint.x.getTime?l[0].dataPoint.x.getTime():l[0].dataPoint.x,m(v)&&
(v=Math.abs(d-this.parent.convertValueToPixel(100*(l[0].dataSeries.dataPointEOs[l[0].index].cumulativeY/l[0].dataSeries.plotUnit.dataPointYSums[y])))),"stackedColumn100"===l[s].dataSeries.type?(t=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,y=l[s].dataPoint.x.getTime?l[s].dataPoint.x.getTime():l[s].dataPoint.x,b=100*(t/l[s].dataSeries.plotUnit.dataPointYSums[y]),y=Math.abs(d-this.parent.convertValueToPixel(b)),y<=v&&(v=y,x=s)):"stackedArea100"===l[s].dataSeries.type&&(t=l[s].dataSeries.dataPointEOs[l[s].index].cumulativeY,
y=l[s].dataPoint.x.getTime?l[s].dataPoint.x.getTime():l[s].dataPoint.x,b=100*(t/l[s].dataSeries.plotUnit.dataPointYSums[y]),y=Math.abs(d-this.parent.convertValueToPixel(b)),y<=v&&(v=y,x=s))):"waterfall"===l[s].dataSeries.type?(y=Math.abs(d-this.parent.convertValueToPixel(l[s].dataSeries.dataPointEOs[l[s].index].cumulativeSum)),y<=z&&(v=z=y,x=s)):(y=Math.abs(d-this.parent.convertValueToPixel(l[s].dataPoint.y)),y<=z&&(z=y,x=s));m(v)||(z=Math.min(z,v))}v=l[x];s=0;if("rangeColumn"===v.dataSeries.type||
"rangeArea"===v.dataSeries.type||"error"===v.dataSeries.type||"rangeSplineArea"===v.dataSeries.type||"candlestick"===v.dataSeries.type||"ohlc"===v.dataSeries.type||"boxAndWhisker"===v.dataSeries.type){z=Math.abs(d-this.parent.convertValueToPixel(v.dataPoint.y[0]));for(l=0;l<v.dataPoint.y.length;l++)y=Math.abs(d-this.parent.convertValueToPixel(v.dataPoint.y[l])),y<z&&(z=y,s=l);t=1===f.lineWidth%2?(this.parent.convertValueToPixel(v.dataPoint.y[s])<<0)+0.5:this.parent.convertValueToPixel(v.dataPoint.y[s])<<
0;k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y[s]}):m(this.options.label)?ga(m(c)?v.dataPoint.y[s]:c,this.valueFormatString,this.chart._cultureInfo):this.label;this.value=v.dataPoint.y[s]}else"stackedColumn"===v.dataSeries.type?(b=v.dataSeries.dataPointEOs[v.index].cumulativeY,t=1===f.lineWidth%2?(this.parent.convertValueToPixel(b)<<0)+0.5:this.parent.convertValueToPixel(b)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,
axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y}):m(this.options.label)?ga(m(c)?v.dataPoint.y:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=b):"stackedArea"===v.dataSeries.type?(b=v.dataSeries.dataPointEOs[v.index].cumulativeY,t=1===f.lineWidth%2?(this.parent.convertValueToPixel(b)<<0)+0.5:this.parent.convertValueToPixel(b)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:v.dataPoint.y}):
m(this.options.label)?ga(m(c)?v.dataPoint.y:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=b):"stackedColumn100"===v.dataSeries.type?(t=v.dataSeries.dataPointEOs[v.index].cumulativeY,y=v.dataPoint.x.getTime?v.dataPoint.x.getTime():v.dataPoint.x,b=100*(t/v.dataSeries.plotUnit.dataPointYSums[y]),t=1===f.lineWidth%2?(this.parent.convertValueToPixel(b)<<0)+0.5:this.parent.convertValueToPixel(b)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,
crosshair:this.options,value:b}):m(this.options.label)?ga(m(c)?b:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=b):"stackedArea100"===v.dataSeries.type?(t=v.dataSeries.dataPointEOs[v.index].cumulativeY,y=v.dataPoint.x.getTime?v.dataPoint.x.getTime():v.dataPoint.x,b=100*(t/v.dataSeries.plotUnit.dataPointYSums[y]),t=1===f.lineWidth%2?(this.parent.convertValueToPixel(b)<<0)+0.5:this.parent.convertValueToPixel(b)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,
axis:this.parent.options,crosshair:this.options,value:b}):m(this.options.label)?ga(m(c)?b:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=b):"waterfall"===v.dataSeries.type?(t=1===f.lineWidth%2?(this.parent.convertValueToPixel(v.dataSeries.dataPointEOs[v.index].cumulativeSum)<<0)+0.5:this.parent.convertValueToPixel(v.dataSeries.dataPointEOs[v.index].cumulativeSum)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,
value:v.dataSeries.dataPointEOs[v.index].cumulativeSum}):m(this.options.label)?ga(m(c)?v.dataSeries.dataPointEOs[v.index].cumulativeSum:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=v.dataSeries.dataPointEOs[v.index].cumulativeSum):(t=1===f.lineWidth%2?(m(a)?d:this.parent.convertValueToPixel(v.dataPoint.y)<<0)+0.5:m(a)?d:this.parent.convertValueToPixel(v.dataPoint.y)<<0,k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,
value:m(c)?v.dataPoint.y:c}):m(this.options.label)?ga(m(c)?v.dataPoint.y:c,this.valueFormatString,this.chart._cultureInfo):this.label,this.value=v.dataPoint.y);g=h=t;b=this.chart.plotArea.x1;e=this.chart.plotArea.x2;this.bounds={x1:b,y1:g-q/2,x2:e,y2:h+q/2};k.y=h+k.fontSize/2-k.measureText().height/2+2;k.y-k.fontSize/2<this.chart.bounds.y1?k.y=this.chart.bounds.y1+k.fontSize/2+2:k.y+k.measureText().height-k.fontSize/2>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-k.measureText().height+k.fontSize/
2);"left"===this.parent._position?k.x=this.parent.lineCoordinates.x2-k.measureText().width:"right"===this.parent._position&&(k.x=this.parent.lineCoordinates.x2)}l=null;f.globalAlpha=p;if("bottom"===this.parent._position||"top"===this.parent._position)"top"===this.parent._position&&k.y-k.fontSize/2<this.chart.bounds.y1&&(k.y=this.chart.bounds.y1+k.fontSize/2),"bottom"===this.parent._position&&this.parent.lineCoordinates.y2-k.fontSize/2+k.measureText().height>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-
k.height+k.fontSize/2+2),this.value>=Math.min(this.parent.viewportMinimum,this.parent.viewportMaximum)&&this.value<=Math.max(this.parent.viewportMinimum,this.parent.viewportMaximum)&&0<q&&(f.moveTo(b,g),f.lineTo(e,h),f.stroke(),this._hidden=!1);if("left"===this.parent._position||"right"===this.parent._position)"left"===this.parent._position&&k.x<this.chart.bounds.x1&&(k.x=this.chart.bounds.x1),"right"===this.parent._position&&k.x+k.measureText().width>this.chart.bounds.x2&&(k.x=this.chart.bounds.x2-
k.measureText().width),this.value>=Math.min(this.parent.viewportMinimum,this.parent.viewportMaximum)&&this.value<=Math.max(this.parent.viewportMinimum,this.parent.viewportMaximum)&&0<q&&(f.moveTo(b,g),f.lineTo(e,h),f.stroke(),this._hidden=!1)}else{if("bottom"===this.parent._position||"top"===this.parent._position)b=e=n=1===f.lineWidth%2?(a<<0)+0.5:a<<0,g=this.chart.plotArea.y1,h=this.chart.plotArea.y2,this.bounds={x1:b-q/2,y1:g,x2:e+q/2,y2:h};else if("left"===this.parent._position||"right"===this.parent._position)g=
h=t=1===f.lineWidth%2?(d<<0)+0.5:d<<0,b=this.chart.plotArea.x1,e=this.chart.plotArea.x2,this.bounds={x1:b,y1:g-q/2,x2:e,y2:h+q/2};if("xySwapped"===this.chart.plotInfo.axisPlacement)if("left"===this.parent._position||"right"===this.parent._position){s=!1;if(this.parent.labels)for(z=Math.ceil(this.parent.interval),l=0;l<this.parent.viewportMaximum;l+=z)if(this.parent.labels[l])s=!0;else{s=!1;break}if(s){if("axisX"===this.parent.type)for(z=this.parent.convertPixelToValue({y:d}),x=null,s=0;s<this.parent.dataSeries.length;s++)(x=
this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(a):c}):m(this.options.label)?x.dataPoint.label:this.label)}else k.text="dateTime"!==this.parent.valueType||this.parent.logarithmic?this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(d):
c}):m(this.options.label)?ga(m(c)?this.parent.convertPixelToValue(d):c,this.valueFormatString,this.chart._cultureInfo):this.label:this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(d):c}):m(this.options.label)?Da(m(c)?this.parent.convertPixelToValue(d):c,this.valueFormatString,this.chart._cultureInfo):this.label;k.y=d+k.fontSize/2-k.measureText().height/2+2;k.y-k.fontSize/2<this.chart.bounds.y1?k.y=this.chart.bounds.y1+
k.fontSize/2+2:k.y+k.measureText().height-k.fontSize/2>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-k.measureText().height+k.fontSize/2);"left"===this.parent._position?k.x=this.parent.lineCoordinates.x1-k.measureText().width:"right"===this.parent._position&&(k.x=this.parent.lineCoordinates.x2)}else{if("bottom"===this.parent._position||"top"===this.parent._position)k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(a):
c}):m(this.options.label)?ga(m(c)?this.parent.convertPixelToValue(a):c,this.valueFormatString,this.chart._cultureInfo):this.label,k.x=b-k.measureText().width/2,k.x+k.width>this.chart.bounds.x2&&(k.x=this.chart.bounds.x2-k.width),k.x<this.chart.bounds.x1&&(k.x=this.chart.bounds.x1),"bottom"===this.parent._position?k.y=this.parent.lineCoordinates.y2+k.fontSize/2+2:"top"===this.parent._position&&(k.y=this.parent.lineCoordinates.y1-k.height+k.fontSize/2+2)}else if("bottom"===this.parent._position||"top"===
this.parent._position){s=!1;l="";if(this.parent.labels)for(z=Math.ceil(this.parent.interval),l=0;l<this.parent.viewportMaximum;l+=z)if(this.parent.labels[l])s=!0;else{s=!1;break}if(s){if("axisX"===this.parent.type)for(z=this.parent.convertPixelToValue({x:a}),x=null,s=0;s<this.parent.dataSeries.length;s++)(x=this.parent.dataSeries[s].getDataPointAtX(z,!0))&&0<=x.index&&(k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(a):
c}):m(this.options.label)?m(c)?x.dataPoint.label:c:this.label)}else k.text="dateTime"!==this.parent.valueType||this.parent.logarithmic?this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?0<this.parent.dataSeries.length?this.parent.convertPixelToValue(a):"":c}):m(this.options.label)?ga(m(c)?this.parent.convertPixelToValue(a):c,this.valueFormatString,this.chart._cultureInfo):this.label:this.labelFormatter?this.labelFormatter({chart:this.chart,
axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(a):c}):m(this.options.label)?Da(m(c)?this.parent.convertPixelToValue(a):c,this.valueFormatString,this.chart._cultureInfo):this.label;k.x=b-k.measureText().width/2;k.x+k.width>this.chart.bounds.x2&&(k.x=this.chart.bounds.x2-k.width);k.x<this.chart.bounds.x1&&(k.x=this.chart.bounds.x1);"bottom"===this.parent._position?k.y=this.parent.lineCoordinates.y2+k.fontSize/2+2:"top"===this.parent._position&&(k.y=this.parent.lineCoordinates.y1-
k.height+k.fontSize/2+2)}else if("left"===this.parent._position||"right"===this.parent._position)k.text=this.labelFormatter?this.labelFormatter({chart:this.chart,axis:this.parent.options,crosshair:this.options,value:m(c)?this.parent.convertPixelToValue(d):c}):m(this.options.label)?ga(m(c)?this.parent.convertPixelToValue(d):c,this.valueFormatString,this.chart._cultureInfo):this.label,k.y=d+k.fontSize/2-k.measureText().height/2+2,k.y-k.fontSize/2<this.chart.bounds.y1?k.y=this.chart.bounds.y1+k.fontSize/
2+2:k.y+k.measureText().height-k.fontSize/2>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-k.measureText().height+k.fontSize/2),"left"===this.parent._position?k.x=this.parent.lineCoordinates.x2-k.measureText().width:"right"===this.parent._position&&(k.x=this.parent.lineCoordinates.x2);"left"===this.parent._position&&k.x<this.chart.bounds.x1?k.x=this.chart.bounds.x1:"right"===this.parent._position&&k.x+k.measureText().width>this.chart.bounds.x2?k.x=this.chart.bounds.x2-k.measureText().width:"top"===
this.parent._position&&k.y-k.fontSize/2<this.chart.bounds.y1?k.y=this.chart.bounds.y1+k.fontSize/2:"bottom"===this.parent._position&&this.parent.lineCoordinates.y2-k.fontSize/2+k.measureText().height>this.chart.bounds.y2&&(k.y=this.chart.bounds.y2-k.height+k.fontSize/2+2);f.globalAlpha=p;0<q&&(f.moveTo(b,g),f.lineTo(e,h),f.stroke(),this._hidden=!1);this.value="bottom"===this.parent._position||"top"===this.parent._position?this.parent.convertPixelToValue(a):this.parent.convertPixelToValue(d)}if("bottom"===
this.parent._position||"top"===this.parent._position)this._updatedValue=this.parent.convertPixelToValue(n);if("left"===this.parent._position||"right"===this.parent._position)this._updatedValue=this.parent.convertPixelToValue(t);this._textBlock=k;this._label=c;m(c)||this.renderLabel();f.restore();f.globalAlpha=w};ea.prototype.renderLabel=function(){this.value>=Math.min(this.parent.viewportMinimum,this.parent.viewportMaximum)&&this.value<=Math.max(this.parent.viewportMinimum,this.parent.viewportMaximum)&&
(m(this._textBlock)||(m(this._textBlock.text)||!("number"===typeof this._textBlock.text.valueOf()||0<this._textBlock.text.length)||this._hidden)||this._textBlock.render(!0),m(this._label)&&this.dispatchEvent("updated",{chart:this.chart,crosshair:this.options,axis:this.parent,value:this.value},this.parent))};qa(W,L);W.prototype._initialize=function(){this.updateOption("updated");this.updateOption("hidden");if(this.enabled){this.container=document.createElement("div");this.container.setAttribute("class",
"canvasjs-chart-tooltip");this.container.style.position="absolute";this.container.style.height="auto";this.container.style.boxShadow="1px 1px 2px 2px rgba(0,0,0,0.1)";this.container.style.zIndex="1000";this.container.style.pointerEvents="none";this.container.style.display="none";var a=document.createElement("div");a.style.width="auto";a.style.height="auto";a.style.minWidth="50px";a.style.lineHeight="normal";a.style.margin="0px 0px 0px 0px";a.style.padding="5px";a.style.fontFamily="Calibri, Arial, Georgia, serif";
a.style.fontWeight="normal";a.style.fontStyle=t?"italic":"normal";a.style.fontSize="14px";a.style.color="#000000";a.style.textShadow="1px 1px 1px rgba(0, 0, 0, 0.1)";a.style.textAlign="left";a.style.border="2px solid gray";a.style.background=t?"rgba(255,255,255,.9)":"rgb(255,255,255)";a.style.textIndent="0px";a.style.whiteSpace="nowrap";a.style.borderRadius="5px";a.style.MozUserSelect="none";a.style.WebkitUserSelect="none";a.style.msUserSelect="none";a.style.userSelect="none";t||(a.style.filter="alpha(opacity = 90)",
a.style.filter="progid:DXImageTransform.Microsoft.Shadow(Strength=3, Direction=135, Color='#666666')");a.innerText="Sample Tooltip";this.container.appendChild(a);this.contentDiv=this.container.firstChild;this.container.style.borderRadius=this.contentDiv.style.borderRadius;this.chart._canvasJSContainer.appendChild(this.container)}};W.prototype.mouseMoveHandler=function(a,d){this._lastUpdated&&4>(new Date).getTime()-this._lastUpdated||(this._lastUpdated=(new Date).getTime(),this.chart.resetOverlayedCanvas(),
this._updateToolTip(a,d),!this._updatedEventParameters||(isNaN(this._prevX)||isNaN(this._prevY))||this.dispatchEvent("updated",this._updatedEventParameters,this))};W.prototype._updateToolTip=function(a,d,c){c="undefined"===typeof c?!0:c;this.container||this._initialize();this.enabled||this.hide();if(!this.chart.disableToolTip){if("undefined"===typeof a||"undefined"===typeof d){if(isNaN(this._prevX)||isNaN(this._prevY))return;a=this._prevX;d=this._prevY}else this._prevX=a,this._prevY=d;var b=null,
e=null,g=[],h=0;if(this.shared&&this.enabled&&"none"!==this.chart.plotInfo.axisPlacement){var n=[];if(this.chart.axisX)for(var w=0;w<this.chart.axisX.length;w++){for(var h="xySwapped"===this.chart.plotInfo.axisPlacement?this.chart.axisX[w].convertPixelToValue({y:d}):this.chart.axisX[w].convertPixelToValue({x:a}),k=null,b=0;b<this.chart.axisX[w].dataSeries.length;b++)(k=this.chart.axisX[w].dataSeries[b].getDataPointAtX(h,c))&&0<=k.index&&(k.dataSeries=this.chart.axisX[w].dataSeries[b],null!==k.dataPoint.y&&
k.dataSeries.visible&&n.push(k));k=null}if(this.chart.axisX2)for(w=0;w<this.chart.axisX2.length;w++){h="xySwapped"===this.chart.plotInfo.axisPlacement?this.chart.axisX2[w].convertPixelToValue({y:d}):this.chart.axisX2[w].convertPixelToValue({x:a});k=null;for(b=0;b<this.chart.axisX2[w].dataSeries.length;b++)(k=this.chart.axisX2[w].dataSeries[b].getDataPointAtX(h,c))&&0<=k.index&&(k.dataSeries=this.chart.axisX2[w].dataSeries[b],null!==k.dataPoint.y&&k.dataSeries.visible&&n.push(k));k=null}if(0===n.length)return;
n.sort(function(a,b){return a.distance-b.distance});c=n[0];for(b=0;b<n.length;b++)n[b].dataPoint.x.valueOf()===c.dataPoint.x.valueOf()&&g.push(n[b]);n=null}else{if(b=this.chart.getDataPointAtXY(a,d,c))this.currentDataPointIndex=b.dataPointIndex,this.currentSeriesIndex=b.dataSeries.index;else if(t)if(b=$a(a,d,this.chart._eventManager.ghostCtx),0<b&&"undefined"!==typeof this.chart._eventManager.objectMap[b]){b=this.chart._eventManager.objectMap[b];if("legendItem"===b.objectType)return;this.currentSeriesIndex=
b.dataSeriesIndex;this.currentDataPointIndex=0<=b.dataPointIndex?b.dataPointIndex:-1}else this.currentDataPointIndex=-1;else this.currentDataPointIndex=-1;if(0<=this.currentSeriesIndex){e=this.chart.data[this.currentSeriesIndex];k={};if(0<=this.currentDataPointIndex)b=e.dataPoints[this.currentDataPointIndex],k.dataSeries=e,k.dataPoint=b,k.index=this.currentDataPointIndex,k.distance=Math.abs(b.x-h),"waterfall"===e.type&&(k.cumulativeSumYStartValue=e.dataPointEOs[this.currentDataPointIndex].cumulativeSumYStartValue,
k.cumulativeSum=e.dataPointEOs[this.currentDataPointIndex].cumulativeSum);else{if(!this.enabled||"line"!==e.type&&"stepLine"!==e.type&&"spline"!==e.type&&"area"!==e.type&&"stepArea"!==e.type&&"splineArea"!==e.type&&"stackedArea"!==e.type&&"stackedArea100"!==e.type&&"rangeArea"!==e.type&&"rangeSplineArea"!==e.type&&"candlestick"!==e.type&&"ohlc"!==e.type&&"boxAndWhisker"!==e.type)return;h=e.axisX.convertPixelToValue({x:a});k=e.getDataPointAtX(h,c);m(k)||(k.dataSeries=e,this.currentDataPointIndex=k.index,
b=k.dataPoint)}if(!m(k)&&!m(k.dataPoint)&&!m(k.dataPoint.y))if(k.dataSeries.axisY)if(0<k.dataPoint.y.length){for(b=c=0;b<k.dataPoint.y.length;b++)k.dataPoint.y[b]<k.dataSeries.axisY.viewportMinimum?c--:k.dataPoint.y[b]>k.dataSeries.axisY.viewportMaximum&&c++;c<k.dataPoint.y.length&&c>-k.dataPoint.y.length&&g.push(k)}else"column"===e.type||"bar"===e.type?0>k.dataPoint.y?0>k.dataSeries.axisY.viewportMinimum&&k.dataSeries.axisY.viewportMaximum>=k.dataPoint.y&&g.push(k):k.dataSeries.axisY.viewportMinimum<=
k.dataPoint.y&&0<=k.dataSeries.axisY.viewportMaximum&&g.push(k):"bubble"===e.type?(c=this.chart._eventManager.objectMap[e.dataPointIds[k.index]].size/2,k.dataPoint.y>=k.dataSeries.axisY.viewportMinimum-c&&k.dataPoint.y<=k.dataSeries.axisY.viewportMaximum+c&&g.push(k)):"waterfall"===e.type?(c=0,k.cumulativeSumYStartValue<k.dataSeries.axisY.viewportMinimum?c--:k.cumulativeSumYStartValue>k.dataSeries.axisY.viewportMaximum&&c++,k.cumulativeSum<k.dataSeries.axisY.viewportMinimum?c--:k.cumulativeSum>k.dataSeries.axisY.viewportMaximum&&
c++,2>c&&-2<c&&g.push(k)):(0<=k.dataSeries.type.indexOf("100")||"stackedColumn"===e.type||"stackedBar"===e.type||k.dataPoint.y>=k.dataSeries.axisY.viewportMinimum&&k.dataPoint.y<=k.dataSeries.axisY.viewportMaximum)&&g.push(k);else g.push(k)}}if(0<g.length){if(this.highlightObjects(g),this.enabled){c="";c=this.getToolTipInnerHTML({entries:g});if(null!==c){this.contentDiv.innerHTML=c;if(this.isToolTipDefinedInData&&m(this.options.content)&&m(this.options.contentFormatter))for(h=this.contentDiv.getElementsByTagName("span"),
b=0;b<h.length;b++)h[b]&&(h[b].style.color=h[b].getAttribute("data-color"));h=!1;"none"===this.container.style.display&&(h=!0,this.container.style.display="block");try{this.contentDiv.style.background=this.backgroundColor?this.backgroundColor:t?"rgba(255,255,255,.9)":"rgb(255,255,255)",this.borderColor="waterfall"===g[0].dataSeries.type?this.contentDiv.style.borderRightColor=this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=this.options.borderColor?this.options.borderColor:g[0].dataPoint.color?
g[0].dataPoint.color:0<g[0].dataPoint.y?g[0].dataSeries.risingColor:g[0].dataSeries.fallingColor:"error"===g[0].dataSeries.type?this.contentDiv.style.borderRightColor=this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=this.options.borderColor?this.options.borderColor:g[0].dataSeries.color?g[0].dataSeries.color:g[0].dataSeries._colorSet[e.index%g[0].dataSeries._colorSet.length]:this.contentDiv.style.borderRightColor=this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=
this.options.borderColor?this.options.borderColor:g[0].dataPoint.color?g[0].dataPoint.color:g[0].dataSeries.color?g[0].dataSeries.color:g[0].dataSeries._colorSet[g[0].index%g[0].dataSeries._colorSet.length],this.contentDiv.style.borderWidth=this.borderThickness||0===this.borderThickness?this.borderThickness+"px":"2px",this.contentDiv.style.borderRadius=this.cornerRadius||0===this.cornerRadius?this.cornerRadius+"px":"5px",this.container.style.borderRadius=this.contentDiv.style.borderRadius,this.contentDiv.style.fontSize=
this.fontSize||0===this.fontSize?this.fontSize+"px":"14px",this.contentDiv.style.color=this.fontColor?this.fontColor:"#000000",this.contentDiv.style.fontFamily=this.fontFamily?this.fontFamily:"Calibri, Arial, Georgia, serif;",this.contentDiv.style.fontWeight=this.fontWeight?this.fontWeight:"normal",this.contentDiv.style.fontStyle=this.fontStyle?this.fontStyle:t?"italic":"normal"}catch(l){}"pie"===g[0].dataSeries.type||"doughnut"===g[0].dataSeries.type||"funnel"===g[0].dataSeries.type||"pyramid"===
g[0].dataSeries.type||"bar"===g[0].dataSeries.type||"rangeBar"===g[0].dataSeries.type||"stackedBar"===g[0].dataSeries.type||"stackedBar100"===g[0].dataSeries.type?a=a-10-this.container.clientWidth:(a=g[0].dataSeries.axisX.convertValueToPixel(g[0].dataPoint.x)-this.container.clientWidth<<0,a-=10);0>a&&(a+=this.container.clientWidth+20);a+this.container.clientWidth>Math.max(this.chart.container.clientWidth,this.chart.width)&&(a=Math.max(0,Math.max(this.chart.container.clientWidth,this.chart.width)-
this.container.clientWidth));d=1!==g.length||this.shared||"line"!==g[0].dataSeries.type&&"stepLine"!==g[0].dataSeries.type&&"spline"!==g[0].dataSeries.type&&"area"!==g[0].dataSeries.type&&"stepArea"!==g[0].dataSeries.type&&"splineArea"!==g[0].dataSeries.type?"bar"===g[0].dataSeries.type||"rangeBar"===g[0].dataSeries.type||"stackedBar"===g[0].dataSeries.type||"stackedBar100"===g[0].dataSeries.type?g[0].dataSeries.axisX.convertValueToPixel(g[0].dataPoint.x):d:g[0].dataSeries.axisY.convertValueToPixel(g[0].dataPoint.y);
d=-d+10;0<d+this.container.clientHeight+5&&(d-=d+this.container.clientHeight+5-0);this.fixMozTransitionDelay(a,d);!this.animationEnabled||h?this.disableAnimation():(this.enableAnimation(),this.container.style.MozTransition=this.mozContainerTransition);this.positionLeft=a;this.positionBottom=d;this.container.style.left=a+"px";this.container.style.bottom=d+"px"}else this.hide(!1),this.enabled&&this.dispatchEvent("hidden",{chart:this.chart,toolTip:this},this);d=[];for(b=0;b<g.length;b++)d.push({xValue:g[b].dataPoint.x,
dataPoint:g[b].dataPoint,dataSeries:g[b].dataSeries,dataPointIndex:g[b].index,dataSeriesIndex:g[b].dataSeries._index});this._updatedEventParameters={chart:this.chart,toolTip:this.options,content:c,entries:d};this._entries=g}}else this.hide(),this.enabled&&this.dispatchEvent("hidden",{chart:this.chart,toolTip:this},this)}};W.prototype.highlightObjects=function(a){var d=this.chart.overlaidCanvasCtx;m(this.chart.clearedOverlayedCanvas)||"toolTip"===this.chart.clearedOverlayedCanvas?(this.chart.resetOverlayedCanvas(),
d.clearRect(0,0,this.chart.width,this.chart.height),this.chart.clearedOverlayedCanvas="toolTip"):this.chart.clearedOverlayedCanvas=null;d.save();var c=this.chart.plotArea,b=0;d.beginPath();d.rect(c.x1,c.y1,c.x2-c.x1,c.y2-c.y1);d.clip();for(c=0;c<a.length;c++){var e=a[c];if((e=this.chart._eventManager.objectMap[e.dataSeries.dataPointIds[e.index]])&&e.objectType&&"dataPoint"===e.objectType){var b=this.chart.data[e.dataSeriesIndex],g=b.dataPoints[e.dataPointIndex],h=e.dataPointIndex;!1===g.highlightEnabled||
!0!==b.highlightEnabled&&!0!==g.highlightEnabled||("line"===b.type||"stepLine"===b.type||"spline"===b.type||"scatter"===b.type||"area"===b.type||"stepArea"===b.type||"splineArea"===b.type||"stackedArea"===b.type||"stackedArea100"===b.type||"rangeArea"===b.type||"rangeSplineArea"===b.type?(g=b.getMarkerProperties(h,e.x1,e.y1,this.chart.overlaidCanvasCtx),g.size=Math.max(1.5*g.size<<0,10),g.borderColor=g.borderColor||"#FFFFFF",g.borderThickness=g.borderThickness||Math.ceil(0.1*g.size),X.drawMarkers([g]),
"undefined"!==typeof e.y2&&(g=b.getMarkerProperties(h,e.x1,e.y2,this.chart.overlaidCanvasCtx),g.size=Math.max(1.5*g.size<<0,10),g.borderColor=g.borderColor||"#FFFFFF",g.borderThickness=g.borderThickness||Math.ceil(0.1*g.size),X.drawMarkers([g]))):"bubble"===b.type?(g=b.getMarkerProperties(h,e.x1,e.y1,this.chart.overlaidCanvasCtx),g.size=e.size,g.color="white",g.borderColor="white",d.globalAlpha=0.3,X.drawMarkers([g]),d.globalAlpha=1):"column"===b.type||"stackedColumn"===b.type||"stackedColumn100"===
b.type||"bar"===b.type||"rangeBar"===b.type||"stackedBar"===b.type||"stackedBar100"===b.type||"rangeColumn"===b.type||"waterfall"===b.type?ba(d,e.x1,e.y1,e.x2,e.y2,"white",0,null,!1,!1,!1,!1,0.3):"pie"===b.type||"doughnut"===b.type?ra(d,e.center,e.radius,"white",b.type,e.startAngle,e.endAngle,0.3,e.percentInnerRadius):"funnel"===b.type||"pyramid"===b.type?ta(d,e.funnelSection,0.3,"white"):"candlestick"===b.type?(d.globalAlpha=1,d.strokeStyle=e.color,d.lineWidth=2*e.borderThickness,b=0===d.lineWidth%
2?0:0.5,d.beginPath(),d.moveTo(e.x3-b,Math.min(e.y2,e.y3)),d.lineTo(e.x3-b,Math.min(e.y1,e.y4)),d.stroke(),d.beginPath(),d.moveTo(e.x3-b,Math.max(e.y1,e.y4)),d.lineTo(e.x3-b,Math.max(e.y2,e.y3)),d.stroke(),ba(d,e.x1,Math.min(e.y1,e.y4),e.x2,Math.max(e.y1,e.y4),"transparent",2*e.borderThickness,e.color,!1,!1,!1,!1),d.globalAlpha=1):"ohlc"===b.type?(d.globalAlpha=1,d.strokeStyle=e.color,d.lineWidth=2*e.borderThickness,b=0===d.lineWidth%2?0:0.5,d.beginPath(),d.moveTo(e.x3-b,e.y2),d.lineTo(e.x3-b,e.y3),
d.stroke(),d.beginPath(),d.moveTo(e.x3,e.y1),d.lineTo(e.x1,e.y1),d.stroke(),d.beginPath(),d.moveTo(e.x3,e.y4),d.lineTo(e.x2,e.y4),d.stroke(),d.globalAlpha=1):"boxAndWhisker"===b.type?(d.save(),d.globalAlpha=1,d.strokeStyle=e.stemColor,d.lineWidth=2*e.stemThickness,0<e.stemThickness&&(d.beginPath(),d.moveTo(e.x3,e.y2+e.borderThickness/2),d.lineTo(e.x3,e.y1+e.whiskerThickness/2),d.stroke(),d.beginPath(),d.moveTo(e.x3,e.y4-e.whiskerThickness/2),d.lineTo(e.x3,e.y3-e.borderThickness/2),d.stroke()),d.beginPath(),
ba(d,e.x1,Math.max(e.y2,e.y3),e.x2,Math.min(e.y2,e.y3),"transparent",2*e.borderThickness,e.color,!1,!1,!1,!1),d.globalAlpha=1,d.strokeStyle=e.whiskerColor,d.lineWidth=2*e.whiskerThickness,0<e.whiskerThickness&&(d.beginPath(),d.moveTo(Math.floor(e.x3-e.whiskerLength/2),e.y4),d.lineTo(Math.ceil(e.x3+e.whiskerLength/2),e.y4),d.stroke(),d.beginPath(),d.moveTo(Math.floor(e.x3-e.whiskerLength/2),e.y1),d.lineTo(Math.ceil(e.x3+e.whiskerLength/2),e.y1),d.stroke()),d.globalAlpha=1,d.strokeStyle=e.lineColor,
d.lineWidth=2*e.lineThickness,0<e.lineThickness&&(d.beginPath(),d.moveTo(e.x1,e.y5),d.lineTo(e.x2,e.y5),d.stroke()),d.restore(),d.globalAlpha=1):"error"===b.type&&z(d,e.x1,e.y1,e.x2,e.y2,"white",e.whiskerProperties,e.stemProperties,e.isXYSwapped,0.3))}}d.restore();d.globalAlpha=1;d.beginPath()};W.prototype.getToolTipInnerHTML=function(a){a=a.entries;var d=null,c=null,b=null,e=0,g="";this.isToolTipDefinedInData=!0;for(var h=0;h<a.length;h++)if(a[h].dataSeries.toolTipContent||a[h].dataPoint.toolTipContent){this.isToolTipDefinedInData=
!1;break}if(this.isToolTipDefinedInData&&(this.content&&"function"===typeof this.content||this.contentFormatter))a={chart:this.chart,toolTip:this.options,entries:a},d=this.contentFormatter?this.contentFormatter(a):this.content(a);else if(this.shared&&"none"!==this.chart.plotInfo.axisPlacement){for(var m=null,n="",h=0;h<a.length;h++){c=a[h].dataSeries;b=a[h].dataPoint;e=a[h].index;g="";if(0===h&&this.isToolTipDefinedInData&&!this.content){this.chart.axisX&&0<this.chart.axisX.length?n+="undefined"!==
typeof this.chart.axisX[0].labels[b.x]?this.chart.axisX[0].labels[b.x]:"{x}":this.chart.axisX2&&0<this.chart.axisX2.length&&(n+="undefined"!==typeof this.chart.axisX2[0].labels[b.x]?this.chart.axisX2[0].labels[b.x]:"{x}");n+="</br>";if(!c.visible)continue;n=this.chart.replaceKeywordsWithValue(n,b,c,e)}null===b.toolTipContent||"undefined"===typeof b.toolTipContent&&null===c.options.toolTipContent||("line"===c.type||"stepLine"===c.type||"spline"===c.type||"area"===c.type||"stepArea"===c.type||"splineArea"===
c.type||"column"===c.type||"bar"===c.type||"scatter"===c.type||"stackedColumn"===c.type||"stackedColumn100"===c.type||"stackedBar"===c.type||"stackedBar100"===c.type||"stackedArea"===c.type||"stackedArea100"===c.type||"waterfall"===c.type?(this.chart.axisX&&1<this.chart.axisX.length&&(g+=m!=c.axisXIndex?c.axisX.title?c.axisX.title+"<br/>":"X:{axisXIndex}<br/>":""),g+=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+
(this.options.fontColor?"":"'{color}'")+"\"'>{name}:</span>&nbsp;&nbsp;{y}",m=c.axisXIndex):"bubble"===c.type?(this.chart.axisX&&1<this.chart.axisX.length&&(g+=m!=c.axisXIndex?c.axisX.title?c.axisX.title+"<br/>":"X:{axisXIndex}<br/>":""),g+=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>{name}:</span>&nbsp;&nbsp;{y}, &nbsp;&nbsp;{z}"):"rangeColumn"===
c.type||"rangeBar"===c.type||"rangeArea"===c.type||"rangeSplineArea"===c.type||"error"===c.type?(this.chart.axisX&&1<this.chart.axisX.length&&(g+=m!=c.axisXIndex?c.axisX.title?c.axisX.title+"<br/>":"X:{axisXIndex}<br/>":""),g+=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>{name}:</span>&nbsp;&nbsp;{y[0]},&nbsp;{y[1]}"):"candlestick"===c.type||"ohlc"===
c.type?(this.chart.axisX&&1<this.chart.axisX.length&&(g+=m!=c.axisXIndex?c.axisX.title?c.axisX.title+"<br/>":"X:{axisXIndex}<br/>":""),g+=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>{name}:</span><br/>Open: &nbsp;&nbsp;{y[0]}<br/>High: &nbsp;&nbsp;&nbsp;{y[1]}<br/>Low:&nbsp;&nbsp;&nbsp;{y[2]}<br/>Close: &nbsp;&nbsp;{y[3]}"):"boxAndWhisker"===c.type&&
(this.chart.axisX&&1<this.chart.axisX.length&&(g+=m!=c.axisXIndex?c.axisX.title?c.axisX.title+"<br/>":"X:{axisXIndex}<br/>":""),g+=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>{name}:</span><br/>Minimum: &nbsp;{y[0]}<br/>Q1:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[1]}<br/>Q2:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[4]}<br/>Q3:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[2]}<br/>Maximum: &nbsp;{y[3]}"),
null===d&&(d=""),c.visible&&(!0===this.reversed?(d=this.chart.replaceKeywordsWithValue(g,b,c,e)+d,h<a.length-1&&(d="</br>"+d)):(d+=this.chart.replaceKeywordsWithValue(g,b,c,e),h<a.length-1&&(d+="</br>"))))}null!==d&&(d=n+d)}else{c=a[0].dataSeries;b=a[0].dataPoint;e=a[0].index;if(null===b.toolTipContent||"undefined"===typeof b.toolTipContent&&null===c.options.toolTipContent)return null;"line"===c.type||"stepLine"===c.type||"spline"===c.type||"area"===c.type||"stepArea"===c.type||"splineArea"===c.type||
"column"===c.type||"bar"===c.type||"scatter"===c.type||"stackedColumn"===c.type||"stackedColumn100"===c.type||"stackedBar"===c.type||"stackedBar100"===c.type||"stackedArea"===c.type||"stackedArea100"===c.type||"waterfall"===c.type?g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>"+(b.label?"{label}":"{x}")+":</span>&nbsp;&nbsp;{y}":"bubble"===c.type?
g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>"+(b.label?"{label}":"{x}")+":</span>&nbsp;&nbsp;{y}, &nbsp;&nbsp;{z}":"pie"===c.type||"doughnut"===c.type||"funnel"===c.type||"pyramid"===c.type?g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?
"":"'{color}'")+"\"'>"+(b.name?"{name}:</span>&nbsp;&nbsp;":b.label?"{label}:</span>&nbsp;&nbsp;":"</span>")+"{y}":"rangeColumn"===c.type||"rangeBar"===c.type||"rangeArea"===c.type||"rangeSplineArea"===c.type||"error"===c.type?g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>"+(b.label?"{label}":"{x}")+" :</span>&nbsp;&nbsp;{y[0]}, &nbsp;{y[1]}":"candlestick"===
c.type||"ohlc"===c.type?g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>"+(b.label?"{label}":"{x}")+"</span><br/>Open: &nbsp;&nbsp;{y[0]}<br/>High: &nbsp;&nbsp;&nbsp;{y[1]}<br/>Low: &nbsp;&nbsp;&nbsp;&nbsp;{y[2]}<br/>Close: &nbsp;&nbsp;{y[3]}":"boxAndWhisker"===c.type&&(g=b.toolTipContent?b.toolTipContent:c.toolTipContent?c.toolTipContent:this.content&&
"function"!==typeof this.content?this.content:"<span data-color='\""+(this.options.fontColor?"":"'{color}'")+"\"'>"+(b.label?"{label}":"{x}")+"</span><br/>Minimum: &nbsp;{y[0]}<br/>Q1:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[1]}<br/>Q2:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[4]}<br/>Q3:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{y[2]}<br/>Maximum: &nbsp;{y[3]}");null===d&&(d="");d+=this.chart.replaceKeywordsWithValue(g,
b,c,e)}return d};W.prototype.enableAnimation=function(){if(!this.container.style.WebkitTransition){var a=this.getContainerTransition(this.containerTransitionDuration);this.container.style.WebkitTransition=a;this.container.style.MsTransition=a;this.container.style.transition=a;this.container.style.MozTransition=this.mozContainerTransition}};W.prototype.disableAnimation=function(){this.container.style.WebkitTransition&&(this.container.style.WebkitTransition="",this.container.style.MozTransition="",
this.container.style.MsTransition="",this.container.style.transition="")};W.prototype.hide=function(a){this.container&&(this.container.style.display="none",this.currentSeriesIndex=-1,this._prevY=this._prevX=NaN,("undefined"===typeof a||a)&&this.chart.resetOverlayedCanvas())};W.prototype.show=function(a,d,c){this._updateToolTip(a,d,"undefined"===typeof c?!1:c)};W.prototype.showAtIndex=function(a,d){};W.prototype.showAtX=function(a,d){if(!this.enabled)return!1;this.chart.clearedOverlayedCanvas=null;
var c,b,e,g=[];e=!1;d=!m(d)&&0<=d&&d<this.chart.data.length?d:0;if(this.shared)for(var h=0;h<this.chart.data.length;h++)c=this.chart.data[h],(b=c.getDataPointAtX(a,!1))&&(b.dataPoint&&!m(b.dataPoint.y)&&c.visible)&&(b.dataSeries=c,g.push(b));else c=this.chart.data[d],(b=c.getDataPointAtX(a,!1))&&(b.dataPoint&&!m(b.dataPoint.y)&&c.visible)&&(b.dataSeries=c,g.push(b));if(0<g.length){for(h=0;h<g.length;h++)if(b=g[h],(this.shared||0<=b.dataSeries.type.indexOf("100"))&&b.dataPoint.x>=b.dataSeries.axisX.viewportMinimum&&
b.dataPoint.x<=b.dataSeries.axisX.viewportMaximum){e=!1;break}else if(b.dataPoint.x<b.dataSeries.axisX.viewportMinimum||b.dataPoint.x>b.dataSeries.axisX.viewportMaximum||b.dataPoint.y<b.dataSeries.axisY.viewportMinimum||b.dataPoint.y>b.dataSeries.axisY.viewportMaximum)e=!0;else{e=!1;break}if(e)return this.hide(),!1;this.highlightObjects(g);this._entries=g;h="";h=this.getToolTipInnerHTML({entries:g});if(null!==h){this.contentDiv.innerHTML=h;if(this.isToolTipDefinedInData&&m(this.options.content)&&
m(this.options.contentFormatter))for(b=this.contentDiv.getElementsByTagName("span"),h=0;h<b.length;h++)b[h]&&(b[h].style.color=b[h].getAttribute("data-color"));h=!1;"none"===this.container.style.display&&(h=!0,this.container.style.display="block");try{this.contentDiv.style.background=this.backgroundColor?this.backgroundColor:t?"rgba(255,255,255,.9)":"rgb(255,255,255)",this.borderColor="waterfall"===g[0].dataSeries.type?this.contentDiv.style.borderRightColor=this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=
this.options.borderColor?this.options.borderColor:g[0].dataPoint.color?g[0].dataPoint.color:0<g[0].dataPoint.y?g[0].dataSeries.risingColor:g[0].dataSeries.fallingColor:"error"===g[0].dataSeries.type?this.contentDiv.style.borderRightColor=this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=this.options.borderColor?this.options.borderColor:g[0].dataSeries.color?g[0].dataSeries.color:g[0].dataSeries._colorSet[c.index%g[0].dataSeries._colorSet.length]:this.contentDiv.style.borderRightColor=
this.contentDiv.style.borderLeftColor=this.contentDiv.style.borderColor=this.options.borderColor?this.options.borderColor:g[0].dataPoint.color?g[0].dataPoint.color:g[0].dataSeries.color?g[0].dataSeries.color:g[0].dataSeries._colorSet[g[0].index%g[0].dataSeries._colorSet.length],this.contentDiv.style.borderWidth=this.borderThickness||0===this.borderThickness?this.borderThickness+"px":"2px",this.contentDiv.style.borderRadius=this.cornerRadius||0===this.cornerRadius?this.cornerRadius+"px":"5px",this.container.style.borderRadius=
this.contentDiv.style.borderRadius,this.contentDiv.style.fontSize=this.fontSize||0===this.fontSize?this.fontSize+"px":"14px",this.contentDiv.style.color=this.fontColor?this.fontColor:"#000000",this.contentDiv.style.fontFamily=this.fontFamily?this.fontFamily:"Calibri, Arial, Georgia, serif;",this.contentDiv.style.fontWeight=this.fontWeight?this.fontWeight:"normal",this.contentDiv.style.fontStyle=this.fontStyle?this.fontStyle:t?"italic":"normal"}catch(n){}"pie"===g[0].dataSeries.type||"doughnut"===
g[0].dataSeries.type||"funnel"===g[0].dataSeries.type||"pyramid"===g[0].dataSeries.type?c=mouseX-10-this.container.clientWidth:(c="bar"===g[0].dataSeries.type||"rangeBar"===g[0].dataSeries.type||"stackedBar"===g[0].dataSeries.type||"stackedBar100"===g[0].dataSeries.type?g[0].dataSeries.axisY.convertValueToPixel(g[0].dataPoint.y)-this.container.clientWidth<<0:g[0].dataSeries.axisX.convertValueToPixel(g[0].dataPoint.x)-this.container.clientWidth<<0,c-=10);0>c&&(c+=this.container.clientWidth+20);c+this.container.clientWidth>
Math.max(this.chart.container.clientWidth,this.chart.width)&&(c=Math.max(0,Math.max(this.chart.container.clientWidth,this.chart.width)-this.container.clientWidth));g=1!==g.length||this.shared||"line"!==g[0].dataSeries.type&&"stepLine"!==g[0].dataSeries.type&&"spline"!==g[0].dataSeries.type&&"area"!==g[0].dataSeries.type&&"stepArea"!==g[0].dataSeries.type&&"splineArea"!==g[0].dataSeries.type?"bar"===g[0].dataSeries.type||"rangeBar"===g[0].dataSeries.type||"stackedBar"===g[0].dataSeries.type||"stackedBar100"===
g[0].dataSeries.type?g[0].dataSeries.axisX.convertValueToPixel(g[0].dataPoint.x):g[0].dataSeries.axisY.convertValueToPixel(g[0].dataPoint.y):g[0].dataSeries.axisY.convertValueToPixel(g[0].dataPoint.y);g=-g+10;0<g+this.container.clientHeight+5&&(g-=g+this.container.clientHeight+5-0);this.fixMozTransitionDelay(c,g);!this.animationEnabled||h?this.disableAnimation():(this.enableAnimation(),this.container.style.MozTransition=this.mozContainerTransition);this.container.style.left=c+"px";this.container.style.bottom=
g+"px"}else return this.hide(!1),!1}else return this.hide(),!1;return!0};W.prototype.fixMozTransitionDelay=function(a,d){if(20<this.chart._eventManager.lastObjectId)this.mozContainerTransition=this.getContainerTransition(0);else{var c=parseFloat(this.container.style.left),c=isNaN(c)?0:c,b=parseFloat(this.container.style.bottom),b=isNaN(b)?0:b;10<Math.sqrt(Math.pow(c-a,2)+Math.pow(b-d,2))?this.mozContainerTransition=this.getContainerTransition(0.1):this.mozContainerTransition=this.getContainerTransition(0)}};
W.prototype.getContainerTransition=function(a){return"left "+a+"s ease-out 0s, bottom "+a+"s ease-out 0s"};aa.prototype.reset=function(){this.lastObjectId=0;this.objectMap=[];this.rectangularRegionEventSubscriptions=[];this.previousDataPointEventObject=null;this.eventObjects=[];t&&(this.ghostCtx.clearRect(0,0,this.chart.width,this.chart.height),this.ghostCtx.beginPath())};aa.prototype.getNewObjectTrackingId=function(){return++this.lastObjectId};aa.prototype.mouseEventHandler=function(a){if("mousemove"===
a.type||"click"===a.type){var d=[],c=Pa(a),b=null;if((b=this.chart.getObjectAtXY(c.x,c.y,!1))&&"undefined"!==typeof this.objectMap[b])if(b=this.objectMap[b],"dataPoint"===b.objectType){var e=this.chart.data[b.dataSeriesIndex],g=e.dataPoints[b.dataPointIndex],h=b.dataPointIndex;b.eventParameter={x:c.x,y:c.y,dataPoint:g,dataSeries:e.options,dataPointIndex:h,dataSeriesIndex:e.index,chart:this.chart};b.eventContext={context:g,userContext:g,mouseover:"mouseover",mousemove:"mousemove",mouseout:"mouseout",
click:"click"};d.push(b);b=this.objectMap[e.id];b.eventParameter={x:c.x,y:c.y,dataPoint:g,dataSeries:e.options,dataPointIndex:h,dataSeriesIndex:e.index,chart:this.chart};b.eventContext={context:e,userContext:e.options,mouseover:"mouseover",mousemove:"mousemove",mouseout:"mouseout",click:"click"};d.push(this.objectMap[e.id])}else"legendItem"===b.objectType&&(e=this.chart.data[b.dataSeriesIndex],g=null!==b.dataPointIndex?e.dataPoints[b.dataPointIndex]:null,b.eventParameter={x:c.x,y:c.y,dataSeries:e.options,
dataPoint:g,dataPointIndex:b.dataPointIndex,dataSeriesIndex:b.dataSeriesIndex,chart:this.chart},b.eventContext={context:this.chart.legend,userContext:this.chart.legend.options,mouseover:"itemmouseover",mousemove:"itemmousemove",mouseout:"itemmouseout",click:"itemclick"},d.push(b));e=[];for(c=0;c<this.mouseoveredObjectMaps.length;c++){g=!0;for(b=0;b<d.length;b++)if(d[b].id===this.mouseoveredObjectMaps[c].id){g=!1;break}g?this.fireEvent(this.mouseoveredObjectMaps[c],"mouseout",a):e.push(this.mouseoveredObjectMaps[c])}this.mouseoveredObjectMaps=
e;for(c=0;c<d.length;c++){e=!1;for(b=0;b<this.mouseoveredObjectMaps.length;b++)if(d[c].id===this.mouseoveredObjectMaps[b].id){e=!0;break}e||(this.fireEvent(d[c],"mouseover",a),this.mouseoveredObjectMaps.push(d[c]));"click"===a.type?this.fireEvent(d[c],"click",a):"mousemove"===a.type&&this.fireEvent(d[c],"mousemove",a)}}};aa.prototype.fireEvent=function(a,d,c){if(a&&d){var b=a.eventParameter,e=a.eventContext,g=a.eventContext.userContext;g&&(e&&g[e[d]])&&g[e[d]].call(g,b);"mouseout"!==d?g.cursor&&g.cursor!==
c.target.style.cursor&&(c.target.style.cursor=g.cursor):(c.target.style.cursor=this.chart.panEnabled?"itemmouseout"!==e.mouseout||g.dockInsidePlotArea?"move":this.chart._defaultCursor:this.chart._defaultCursor,delete a.eventParameter,delete a.eventContext);"click"===d&&("dataPoint"===a.objectType&&this.chart.pieDoughnutClickHandler)&&this.chart.pieDoughnutClickHandler.call(this.chart.data[a.dataSeriesIndex],b);"click"===d&&("dataPoint"===a.objectType&&this.chart.funnelPyramidClickHandler)&&this.chart.funnelPyramidClickHandler.call(this.chart.data[a.dataSeriesIndex],
b)}};ka.prototype.animate=function(a,d,c,b,e){var g=this;this.chart.isAnimating=!0;e=e||N.easing.linear;c&&this.animations.push({startTime:(new Date).getTime()+(a?a:0),duration:d,animationCallback:c,onComplete:b});for(a=[];0<this.animations.length;)if(d=this.animations.shift(),c=(new Date).getTime(),b=0,d.startTime<=c&&(b=e(Math.min(c-d.startTime,d.duration),0,1,d.duration),b=Math.min(b,1),isNaN(b)||!isFinite(b))&&(b=1),1>b&&a.push(d),d.animationCallback(b),1<=b&&d.onComplete)d.onComplete();this.animations=
a;0<this.animations.length?this.animationRequestId=this.chart.requestAnimFrame.call(window,function(){g.animate.call(g)}):this.chart.isAnimating=!1};ka.prototype.cancelAllAnimations=function(){this.animations=[];this.animationRequestId&&this.chart.cancelRequestAnimFrame.call(window,this.animationRequestId);this.animationRequestId=null;this.chart.isAnimating=!1};var N={yScaleAnimation:function(a,d){if(0!==a){var c=d.dest,b=d.source.canvas,e=d.animationBase;c.drawImage(b,0,0,b.width,b.height,0,e-e*
a,c.canvas.width/na,a*c.canvas.height/na)}},xScaleAnimation:function(a,d){if(0!==a){var c=d.dest,b=d.source.canvas,e=d.animationBase;c.drawImage(b,0,0,b.width,b.height,e-e*a,0,a*c.canvas.width/na,c.canvas.height/na)}},xClipAnimation:function(a,d){if(0!==a){var c=d.dest,b=d.source.canvas;c.save();0<a&&c.drawImage(b,0,0,b.width*a,b.height,0,0,b.width*a/na,b.height/na);c.restore()}},fadeInAnimation:function(a,d){if(0!==a){var c=d.dest,b=d.source.canvas;c.save();c.globalAlpha=a;c.drawImage(b,0,0,b.width,
b.height,0,0,c.canvas.width/na,c.canvas.height/na);c.restore()}},easing:{linear:function(a,d,c,b){return c*a/b+d},easeOutQuad:function(a,d,c,b){return-c*(a/=b)*(a-2)+d},easeOutQuart:function(a,d,c,b){return-c*((a=a/b-1)*a*a*a-1)+d},easeInQuad:function(a,d,c,b){return c*(a/=b)*a+d},easeInQuart:function(a,d,c,b){return c*(a/=b)*a*a*a+d}}},X={drawMarker:function(a,d,c,b,e,g,h,m){if(c){var n=1;c.fillStyle=g?g:"#000000";c.strokeStyle=h?h:"#000000";c.lineWidth=m?m:0;c.setLineDash&&c.setLineDash(H("solid",
m));"circle"===b?(c.moveTo(a,d),c.beginPath(),c.arc(a,d,e/2,0,2*Math.PI,!1),g&&c.fill(),m&&(h?c.stroke():(n=c.globalAlpha,c.globalAlpha=0.15,c.strokeStyle="black",c.stroke(),c.globalAlpha=n))):"square"===b?(c.beginPath(),c.rect(a-e/2,d-e/2,e,e),g&&c.fill(),m&&(h?c.stroke():(n=c.globalAlpha,c.globalAlpha=0.15,c.strokeStyle="black",c.stroke(),c.globalAlpha=n))):"triangle"===b?(c.beginPath(),c.moveTo(a-e/2,d+e/2),c.lineTo(a+e/2,d+e/2),c.lineTo(a,d-e/2),c.closePath(),g&&c.fill(),m&&(h?c.stroke():(n=c.globalAlpha,
c.globalAlpha=0.15,c.strokeStyle="black",c.stroke(),c.globalAlpha=n)),c.beginPath()):"cross"===b&&(c.strokeStyle=g,c.lineWidth=e/4,c.beginPath(),c.moveTo(a-e/2,d-e/2),c.lineTo(a+e/2,d+e/2),c.stroke(),c.moveTo(a+e/2,d-e/2),c.lineTo(a-e/2,d+e/2),c.stroke())}},drawMarkers:function(a){for(var d=0;d<a.length;d++){var c=a[d];X.drawMarker(c.x,c.y,c.ctx,c.type,c.size,c.color,c.borderColor,c.borderThickness)}}};return n}();z.version="v3.7.44 GA";window.CanvasJS&&(z&&!window.CanvasJS.Chart)&&(window.CanvasJS.Chart=
z)})();

/*
  excanvas is used to support IE678 which do not implement HTML5 Canvas Element. You can safely remove the following excanvas code if you don't need to support older browsers.

  Copyright 2006 Google Inc. https://code.google.com/p/explorercanvas/
  Licensed under the Apache License, Version 2.0
*/
document.createElement("canvas").getContext||function(){function V(){return this.context_||(this.context_=new C(this))}function W(a,b,c){var g=M.call(arguments,2);return function(){return a.apply(b,g.concat(M.call(arguments)))}}function N(a){return String(a).replace(/&/g,"&amp;").replace(/"/g,"&quot;")}function O(a){a.namespaces.g_vml_||a.namespaces.add("g_vml_","urn:schemas-microsoft-com:vml","#default#VML");a.namespaces.g_o_||a.namespaces.add("g_o_","urn:schemas-microsoft-com:office:office","#default#VML");
a.styleSheets.ex_canvas_||(a=a.createStyleSheet(),a.owningElement.id="ex_canvas_",a.cssText="canvas{display:inline-block;overflow:hidden;text-align:left;width:300px;height:150px}")}function X(a){var b=a.srcElement;switch(a.propertyName){case "width":b.getContext().clearRect();b.style.width=b.attributes.width.nodeValue+"px";b.firstChild.style.width=b.clientWidth+"px";break;case "height":b.getContext().clearRect(),b.style.height=b.attributes.height.nodeValue+"px",b.firstChild.style.height=b.clientHeight+
"px"}}function Y(a){a=a.srcElement;a.firstChild&&(a.firstChild.style.width=a.clientWidth+"px",a.firstChild.style.height=a.clientHeight+"px")}function D(){return[[1,0,0],[0,1,0],[0,0,1]]}function t(a,b){for(var c=D(),g=0;3>g;g++)for(var e=0;3>e;e++){for(var f=0,d=0;3>d;d++)f+=a[g][d]*b[d][e];c[g][e]=f}return c}function P(a,b){b.fillStyle=a.fillStyle;b.lineCap=a.lineCap;b.lineJoin=a.lineJoin;b.lineWidth=a.lineWidth;b.miterLimit=a.miterLimit;b.shadowBlur=a.shadowBlur;b.shadowColor=a.shadowColor;b.shadowOffsetX=
a.shadowOffsetX;b.shadowOffsetY=a.shadowOffsetY;b.strokeStyle=a.strokeStyle;b.globalAlpha=a.globalAlpha;b.font=a.font;b.textAlign=a.textAlign;b.textBaseline=a.textBaseline;b.arcScaleX_=a.arcScaleX_;b.arcScaleY_=a.arcScaleY_;b.lineScale_=a.lineScale_}function Q(a){var b=a.indexOf("(",3),c=a.indexOf(")",b+1),b=a.substring(b+1,c).split(",");if(4!=b.length||"a"!=a.charAt(3))b[3]=1;return b}function E(a,b,c){return Math.min(c,Math.max(b,a))}function F(a,b,c){0>c&&c++;1<c&&c--;return 1>6*c?a+6*(b-a)*c:
1>2*c?b:2>3*c?a+6*(b-a)*(2/3-c):a}function G(a){if(a in H)return H[a];var b,c=1;a=String(a);if("#"==a.charAt(0))b=a;else if(/^rgb/.test(a)){c=Q(a);b="#";for(var g,e=0;3>e;e++)g=-1!=c[e].indexOf("%")?Math.floor(255*(parseFloat(c[e])/100)):+c[e],b+=v[E(g,0,255)];c=+c[3]}else if(/^hsl/.test(a)){e=c=Q(a);b=parseFloat(e[0])/360%360;0>b&&b++;g=E(parseFloat(e[1])/100,0,1);e=E(parseFloat(e[2])/100,0,1);if(0==g)g=e=b=e;else{var f=0.5>e?e*(1+g):e+g-e*g,d=2*e-f;g=F(d,f,b+1/3);e=F(d,f,b);b=F(d,f,b-1/3)}b="#"+
v[Math.floor(255*g)]+v[Math.floor(255*e)]+v[Math.floor(255*b)];c=c[3]}else b=Z[a]||a;return H[a]={color:b,alpha:c}}function C(a){this.m_=D();this.mStack_=[];this.aStack_=[];this.currentPath_=[];this.fillStyle=this.strokeStyle="#000";this.lineWidth=1;this.lineJoin="miter";this.lineCap="butt";this.miterLimit=1*q;this.globalAlpha=1;this.font="10px sans-serif";this.textAlign="left";this.textBaseline="alphabetic";this.canvas=a;var b="width:"+a.clientWidth+"px;height:"+a.clientHeight+"px;overflow:hidden;position:absolute",
c=a.ownerDocument.createElement("div");c.style.cssText=b;a.appendChild(c);b=c.cloneNode(!1);b.style.backgroundColor="red";b.style.filter="alpha(opacity=0)";a.appendChild(b);this.element_=c;this.lineScale_=this.arcScaleY_=this.arcScaleX_=1}function R(a,b,c,g){a.currentPath_.push({type:"bezierCurveTo",cp1x:b.x,cp1y:b.y,cp2x:c.x,cp2y:c.y,x:g.x,y:g.y});a.currentX_=g.x;a.currentY_=g.y}function S(a,b){var c=G(a.strokeStyle),g=c.color,c=c.alpha*a.globalAlpha,e=a.lineScale_*a.lineWidth;1>e&&(c*=e);b.push("<g_vml_:stroke",
' opacity="',c,'"',' joinstyle="',a.lineJoin,'"',' miterlimit="',a.miterLimit,'"',' endcap="',$[a.lineCap]||"square",'"',' weight="',e,'px"',' color="',g,'" />')}function T(a,b,c,g){var e=a.fillStyle,f=a.arcScaleX_,d=a.arcScaleY_,k=g.x-c.x,n=g.y-c.y;if(e instanceof w){var h=0,l=g=0,u=0,m=1;if("gradient"==e.type_){h=e.x1_/f;c=e.y1_/d;var p=s(a,e.x0_/f,e.y0_/d),h=s(a,h,c),h=180*Math.atan2(h.x-p.x,h.y-p.y)/Math.PI;0>h&&(h+=360);1E-6>h&&(h=0)}else p=s(a,e.x0_,e.y0_),g=(p.x-c.x)/k,l=(p.y-c.y)/n,k/=f*q,
n/=d*q,m=x.max(k,n),u=2*e.r0_/m,m=2*e.r1_/m-u;f=e.colors_;f.sort(function(a,b){return a.offset-b.offset});d=f.length;p=f[0].color;c=f[d-1].color;k=f[0].alpha*a.globalAlpha;a=f[d-1].alpha*a.globalAlpha;for(var n=[],r=0;r<d;r++){var t=f[r];n.push(t.offset*m+u+" "+t.color)}b.push('<g_vml_:fill type="',e.type_,'"',' method="none" focus="100%"',' color="',p,'"',' color2="',c,'"',' colors="',n.join(","),'"',' opacity="',a,'"',' g_o_:opacity2="',k,'"',' angle="',h,'"',' focusposition="',g,",",l,'" />')}else e instanceof
I?k&&n&&b.push("<g_vml_:fill",' position="',-c.x/k*f*f,",",-c.y/n*d*d,'"',' type="tile"',' src="',e.src_,'" />'):(e=G(a.fillStyle),b.push('<g_vml_:fill color="',e.color,'" opacity="',e.alpha*a.globalAlpha,'" />'))}function s(a,b,c){a=a.m_;return{x:q*(b*a[0][0]+c*a[1][0]+a[2][0])-r,y:q*(b*a[0][1]+c*a[1][1]+a[2][1])-r}}function z(a,b,c){isFinite(b[0][0])&&(isFinite(b[0][1])&&isFinite(b[1][0])&&isFinite(b[1][1])&&isFinite(b[2][0])&&isFinite(b[2][1]))&&(a.m_=b,c&&(a.lineScale_=aa(ba(b[0][0]*b[1][1]-b[0][1]*
b[1][0]))))}function w(a){this.type_=a;this.r1_=this.y1_=this.x1_=this.r0_=this.y0_=this.x0_=0;this.colors_=[]}function I(a,b){if(!a||1!=a.nodeType||"IMG"!=a.tagName)throw new A("TYPE_MISMATCH_ERR");if("complete"!=a.readyState)throw new A("INVALID_STATE_ERR");switch(b){case "repeat":case null:case "":this.repetition_="repeat";break;case "repeat-x":case "repeat-y":case "no-repeat":this.repetition_=b;break;default:throw new A("SYNTAX_ERR");}this.src_=a.src;this.width_=a.width;this.height_=a.height}
function A(a){this.code=this[a];this.message=a+": DOM Exception "+this.code}var x=Math,k=x.round,J=x.sin,K=x.cos,ba=x.abs,aa=x.sqrt,q=10,r=q/2;navigator.userAgent.match(/MSIE ([\d.]+)?/);var M=Array.prototype.slice;O(document);var U={init:function(a){a=a||document;a.createElement("canvas");a.attachEvent("onreadystatechange",W(this.init_,this,a))},init_:function(a){a=a.getElementsByTagName("canvas");for(var b=0;b<a.length;b++)this.initElement(a[b])},initElement:function(a){if(!a.getContext){a.getContext=
V;O(a.ownerDocument);a.innerHTML="";a.attachEvent("onpropertychange",X);a.attachEvent("onresize",Y);var b=a.attributes;b.width&&b.width.specified?a.style.width=b.width.nodeValue+"px":a.width=a.clientWidth;b.height&&b.height.specified?a.style.height=b.height.nodeValue+"px":a.height=a.clientHeight}return a}};U.init();for(var v=[],d=0;16>d;d++)for(var B=0;16>B;B++)v[16*d+B]=d.toString(16)+B.toString(16);var Z={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",
bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgreen:"#006400",darkgrey:"#A9A9A9",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",
darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",grey:"#808080",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",
ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgreen:"#90EE90",lightgrey:"#D3D3D3",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",mediumaquamarine:"#66CDAA",
mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",oldlace:"#FDF5E6",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",
peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",whitesmoke:"#F5F5F5",yellowgreen:"#9ACD32"},
H={},L={},$={butt:"flat",round:"round"},d=C.prototype;d.clearRect=function(){this.textMeasureEl_&&(this.textMeasureEl_.removeNode(!0),this.textMeasureEl_=null);this.element_.innerHTML=""};d.beginPath=function(){this.currentPath_=[]};d.moveTo=function(a,b){var c=s(this,a,b);this.currentPath_.push({type:"moveTo",x:c.x,y:c.y});this.currentX_=c.x;this.currentY_=c.y};d.lineTo=function(a,b){var c=s(this,a,b);this.currentPath_.push({type:"lineTo",x:c.x,y:c.y});this.currentX_=c.x;this.currentY_=c.y};d.bezierCurveTo=
function(a,b,c,g,e,f){e=s(this,e,f);a=s(this,a,b);c=s(this,c,g);R(this,a,c,e)};d.quadraticCurveTo=function(a,b,c,g){a=s(this,a,b);c=s(this,c,g);g={x:this.currentX_+2/3*(a.x-this.currentX_),y:this.currentY_+2/3*(a.y-this.currentY_)};R(this,g,{x:g.x+(c.x-this.currentX_)/3,y:g.y+(c.y-this.currentY_)/3},c)};d.arc=function(a,b,c,g,e,f){c*=q;var d=f?"at":"wa",k=a+K(g)*c-r,n=b+J(g)*c-r;g=a+K(e)*c-r;e=b+J(e)*c-r;k!=g||f||(k+=0.125);a=s(this,a,b);k=s(this,k,n);g=s(this,g,e);this.currentPath_.push({type:d,
x:a.x,y:a.y,radius:c,xStart:k.x,yStart:k.y,xEnd:g.x,yEnd:g.y})};d.rect=function(a,b,c,g){this.moveTo(a,b);this.lineTo(a+c,b);this.lineTo(a+c,b+g);this.lineTo(a,b+g);this.closePath()};d.strokeRect=function(a,b,c,g){var e=this.currentPath_;this.beginPath();this.moveTo(a,b);this.lineTo(a+c,b);this.lineTo(a+c,b+g);this.lineTo(a,b+g);this.closePath();this.stroke();this.currentPath_=e};d.fillRect=function(a,b,c,g){var e=this.currentPath_;this.beginPath();this.moveTo(a,b);this.lineTo(a+c,b);this.lineTo(a+
c,b+g);this.lineTo(a,b+g);this.closePath();this.fill();this.currentPath_=e};d.createLinearGradient=function(a,b,c,g){var e=new w("gradient");e.x0_=a;e.y0_=b;e.x1_=c;e.y1_=g;return e};d.createRadialGradient=function(a,b,c,g,e,f){var d=new w("gradientradial");d.x0_=a;d.y0_=b;d.r0_=c;d.x1_=g;d.y1_=e;d.r1_=f;return d};d.drawImage=function(a,b){var c,g,e,d,r,y,n,h;e=a.runtimeStyle.width;d=a.runtimeStyle.height;a.runtimeStyle.width="auto";a.runtimeStyle.height="auto";var l=a.width,u=a.height;a.runtimeStyle.width=
e;a.runtimeStyle.height=d;if(3==arguments.length)c=arguments[1],g=arguments[2],r=y=0,n=e=l,h=d=u;else if(5==arguments.length)c=arguments[1],g=arguments[2],e=arguments[3],d=arguments[4],r=y=0,n=l,h=u;else if(9==arguments.length)r=arguments[1],y=arguments[2],n=arguments[3],h=arguments[4],c=arguments[5],g=arguments[6],e=arguments[7],d=arguments[8];else throw Error("Invalid number of arguments");var m=s(this,c,g),p=[];p.push(" <g_vml_:group",' coordsize="',10*q,",",10*q,'"',' coordorigin="0,0"',' style="width:',
10,"px;height:",10,"px;position:absolute;");if(1!=this.m_[0][0]||this.m_[0][1]||1!=this.m_[1][1]||this.m_[1][0]){var t=[];t.push("M11=",this.m_[0][0],",","M12=",this.m_[1][0],",","M21=",this.m_[0][1],",","M22=",this.m_[1][1],",","Dx=",k(m.x/q),",","Dy=",k(m.y/q),"");var v=s(this,c+e,g),w=s(this,c,g+d);c=s(this,c+e,g+d);m.x=x.max(m.x,v.x,w.x,c.x);m.y=x.max(m.y,v.y,w.y,c.y);p.push("padding:0 ",k(m.x/q),"px ",k(m.y/q),"px 0;filter:progid:DXImageTransform.Microsoft.Matrix(",t.join(""),", sizingmethod='clip');")}else p.push("top:",
k(m.y/q),"px;left:",k(m.x/q),"px;");p.push(' ">','<g_vml_:image src="',a.src,'"',' style="width:',q*e,"px;"," height:",q*d,'px"',' cropleft="',r/l,'"',' croptop="',y/u,'"',' cropright="',(l-r-n)/l,'"',' cropbottom="',(u-y-h)/u,'"'," />","</g_vml_:group>");this.element_.insertAdjacentHTML("BeforeEnd",p.join(""))};d.stroke=function(a){var b=[];b.push("<g_vml_:shape",' filled="',!!a,'"',' style="position:absolute;width:',10,"px;height:",10,'px;"',' coordorigin="0,0"',' coordsize="',10*q,",",10*q,'"',
' stroked="',!a,'"',' path="');for(var c={x:null,y:null},d={x:null,y:null},e=0;e<this.currentPath_.length;e++){var f=this.currentPath_[e];switch(f.type){case "moveTo":b.push(" m ",k(f.x),",",k(f.y));break;case "lineTo":b.push(" l ",k(f.x),",",k(f.y));break;case "close":b.push(" x ");f=null;break;case "bezierCurveTo":b.push(" c ",k(f.cp1x),",",k(f.cp1y),",",k(f.cp2x),",",k(f.cp2y),",",k(f.x),",",k(f.y));break;case "at":case "wa":b.push(" ",f.type," ",k(f.x-this.arcScaleX_*f.radius),",",k(f.y-this.arcScaleY_*
f.radius)," ",k(f.x+this.arcScaleX_*f.radius),",",k(f.y+this.arcScaleY_*f.radius)," ",k(f.xStart),",",k(f.yStart)," ",k(f.xEnd),",",k(f.yEnd))}if(f){if(null==c.x||f.x<c.x)c.x=f.x;if(null==d.x||f.x>d.x)d.x=f.x;if(null==c.y||f.y<c.y)c.y=f.y;if(null==d.y||f.y>d.y)d.y=f.y}}b.push(' ">');a?T(this,b,c,d):S(this,b);b.push("</g_vml_:shape>");this.element_.insertAdjacentHTML("beforeEnd",b.join(""))};d.fill=function(){this.stroke(!0)};d.closePath=function(){this.currentPath_.push({type:"close"})};d.save=function(){var a=
{};P(this,a);this.aStack_.push(a);this.mStack_.push(this.m_);this.m_=t(D(),this.m_)};d.restore=function(){this.aStack_.length&&(P(this.aStack_.pop(),this),this.m_=this.mStack_.pop())};d.translate=function(a,b){z(this,t([[1,0,0],[0,1,0],[a,b,1]],this.m_),!1)};d.rotate=function(a){var b=K(a);a=J(a);z(this,t([[b,a,0],[-a,b,0],[0,0,1]],this.m_),!1)};d.scale=function(a,b){this.arcScaleX_*=a;this.arcScaleY_*=b;z(this,t([[a,0,0],[0,b,0],[0,0,1]],this.m_),!0)};d.transform=function(a,b,c,d,e,f){z(this,t([[a,
b,0],[c,d,0],[e,f,1]],this.m_),!0)};d.setTransform=function(a,b,c,d,e,f){z(this,[[a,b,0],[c,d,0],[e,f,1]],!0)};d.drawText_=function(a,b,c,d,e){var f=this.m_;d=0;var r=1E3,t=0,n=[],h;h=this.font;if(L[h])h=L[h];else{var l=document.createElement("div").style;try{l.font=h}catch(u){}h=L[h]={style:l.fontStyle||"normal",variant:l.fontVariant||"normal",weight:l.fontWeight||"normal",size:l.fontSize||10,family:l.fontFamily||"sans-serif"}}var l=h,m=this.element_;h={};for(var p in l)h[p]=l[p];p=parseFloat(m.currentStyle.fontSize);
m=parseFloat(l.size);"number"==typeof l.size?h.size=l.size:-1!=l.size.indexOf("px")?h.size=m:-1!=l.size.indexOf("em")?h.size=p*m:-1!=l.size.indexOf("%")?h.size=p/100*m:-1!=l.size.indexOf("pt")?h.size=m/0.75:h.size=p;h.size*=0.981;p=h.style+" "+h.variant+" "+h.weight+" "+h.size+"px "+h.family;m=this.element_.currentStyle;l=this.textAlign.toLowerCase();switch(l){case "left":case "center":case "right":break;case "end":l="ltr"==m.direction?"right":"left";break;case "start":l="rtl"==m.direction?"right":
"left";break;default:l="left"}switch(this.textBaseline){case "hanging":case "top":t=h.size/1.75;break;case "middle":break;default:case null:case "alphabetic":case "ideographic":case "bottom":t=-h.size/2.25}switch(l){case "right":d=1E3;r=0.05;break;case "center":d=r=500}b=s(this,b+0,c+t);n.push('<g_vml_:line from="',-d,' 0" to="',r,' 0.05" ',' coordsize="100 100" coordorigin="0 0"',' filled="',!e,'" stroked="',!!e,'" style="position:absolute;width:1px;height:1px;">');e?S(this,n):T(this,n,{x:-d,y:0},
{x:r,y:h.size});e=f[0][0].toFixed(3)+","+f[1][0].toFixed(3)+","+f[0][1].toFixed(3)+","+f[1][1].toFixed(3)+",0,0";b=k(b.x/q)+","+k(b.y/q);n.push('<g_vml_:skew on="t" matrix="',e,'" ',' offset="',b,'" origin="',d,' 0" />','<g_vml_:path textpathok="true" />','<g_vml_:textpath on="true" string="',N(a),'" style="v-text-align:',l,";font:",N(p),'" /></g_vml_:line>');this.element_.insertAdjacentHTML("beforeEnd",n.join(""))};d.fillText=function(a,b,c,d){this.drawText_(a,b,c,d,!1)};d.strokeText=function(a,
b,c,d){this.drawText_(a,b,c,d,!0)};d.measureText=function(a){this.textMeasureEl_||(this.element_.insertAdjacentHTML("beforeEnd",'<span style="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;"></span>'),this.textMeasureEl_=this.element_.lastChild);var b=this.element_.ownerDocument;this.textMeasureEl_.innerHTML="";this.textMeasureEl_.style.font=this.font;this.textMeasureEl_.appendChild(b.createTextNode(a));return{width:this.textMeasureEl_.offsetWidth}};d.clip=function(){};
d.arcTo=function(){};d.createPattern=function(a,b){return new I(a,b)};w.prototype.addColorStop=function(a,b){b=G(b);this.colors_.push({offset:a,color:b.color,alpha:b.alpha})};d=A.prototype=Error();d.INDEX_SIZE_ERR=1;d.DOMSTRING_SIZE_ERR=2;d.HIERARCHY_REQUEST_ERR=3;d.WRONG_DOCUMENT_ERR=4;d.INVALID_CHARACTER_ERR=5;d.NO_DATA_ALLOWED_ERR=6;d.NO_MODIFICATION_ALLOWED_ERR=7;d.NOT_FOUND_ERR=8;d.NOT_SUPPORTED_ERR=9;d.INUSE_ATTRIBUTE_ERR=10;d.INVALID_STATE_ERR=11;d.SYNTAX_ERR=12;d.INVALID_MODIFICATION_ERR=
13;d.NAMESPACE_ERR=14;d.INVALID_ACCESS_ERR=15;d.VALIDATION_ERR=16;d.TYPE_MISMATCH_ERR=17;G_vmlCanvasManager=U;CanvasRenderingContext2D=C;CanvasGradient=w;CanvasPattern=I;DOMException=A}();
/*tslint:enable*/
/*eslint-enable*/
/*jshint ignore:end*/