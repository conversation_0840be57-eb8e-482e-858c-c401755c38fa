<div class="col-md-8 offset-2 sect-jr">
    <div class="receipt" data-tag="<?=$ar_invoice ? $ar_invoice->id : '';?>" style="padding: 10px;">
        <div class="row sect-jr">
            <div class="col-3"></div>

            <div class="col-4 offset-8" style="margin: 0; padding: 0;">
                <div class="row" style="margin-left: 70px; margin-top: 30px;">
                    <p style="font-family: 'Times New Roman', Times, serif; font-size: 14px;"><?=date('F d,')?>
                        <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?=substr(date('Y'), -2)?></span>
                    </p>
                </div>

                <div class="row" style="margin-left: 0; margin-top: 20px;">
                    <p style="font-family: 'Times New Roman', Times, serif; font-size: 14px;"><?=$paid_invoice->customer_name?></p>
                </div>

                <div class="row" style="margin-left: 0; margin-top: 5px; font-family: 'Times New Roman', Times, serif; font-size: 14px;">
                    <p><?=$paid_invoice->customer_tin ? $paid_invoice->customer_tin : '' ?></p>
                </div>

                <div class="row" style="margin-left: 0; margin-top: 0; font-family: 'Times New Roman', Times, serif; font-size: 14px;">
                    <p><?=$paid_invoice->customer_address ? $paid_invoice->customer_address : '' ?></p>
                </div>

                <div class="row" style="margin-top: 20px; margin-left: 5px; font-family: 'Times New Roman', Times, serif; font-size: 14px;">
                    <?php
						$whole = floor($ar_invoice ? $ar_invoice->total_paid : 0);
						$decimal = round((($ar_invoice ? $ar_invoice->total_paid : 0) - $whole) * 100);
						$figures = array_filter([
							$whole > 0 ? spellout($whole) : null,
							$decimal ? $decimal . '/100' : null
						]);

						$ar_invoice_words = implode(' and ', $figures);
					?>
					<span class="text-center" style="font-family: 'Times New Roman', Times, serif; font-size: 14px; margin-left: 0;"><?=ucwords($ar_invoice_words);?></span>
                </div>

                <div class="row" style="margin-top: 5px; margin-left: 140px; font-family: 'Times New Roman', Times, serif; font-size: 14px;">
                    <p><?= number_format($ar_invoice ? $ar_invoice->total_paid : 0, 2) ?></p>
                </div>

                <div class="row" style="margin-top: 5px; margin-left: 100px; font-family: 'Times New Roman', Times, serif; font-size: 14px;">
                    <p><?= $paid_invoice->concatenated_invoice_no ?></p>
                </div>
            </div>

            
        </div>
    </div>

	<div class="clearfix text-center">
		<button type="button" class="btn btn-primary action-print">Print</button>
		<a class="btn btn-light" href="<?=site_url('payments/manager');?>">Close</a>
	</div>
</div>

<style>
	@media print {
		.solid-line {
			display: block !important;
		}
	}

	.empty-border {
		min-height: 20px; /* Adjust height as needed */
	}
</style>
