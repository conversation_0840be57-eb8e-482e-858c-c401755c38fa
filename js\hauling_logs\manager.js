+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-left',
				targets: [0, 1, 2, 3, 4, 8]
			}, {
				className: 'text-center',
				targets: [5, 6, 7]
			}, {
				className: 'text-right',
				targets: [9, 10, 11]
			}, {
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: {
				url: BASE_URI + 'fetch/' + $table.attr('data-package'),
				data: function (data) {
					// Only add date filter if it has been applied via filter button
					if (window.dateFilterApplied) {
						data.date_filter = $('#date_filter').val();
					}
				},
				error: function(xhr, error, thrown) {
				}
			}
		});

		return dataTable;
	}

	function deleteAndReload(haulingLogId, dataTable) {
		$.when(deleteRow(haulingLogId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(haulingLogId) {
		return $.ajax({
			url: BASE_URI + 'hauling_logs/delete/' + haulingLogId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr').find('td');
			var growerFarm = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete the hauling log for ' + growerFarm + '?',
				function () {
					var haulingLogId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(haulingLogId, dataTable);
				}
			).setHeader('<em>Delete Hauling Log</em>');
		});

		
		window.dateFilterApplied = false;

		// Handle automatic date filtering when date changes
		$('#date_filter').on('change', function() {
			var dateValue = $(this).val();
			if (dateValue) {
				window.dateFilterApplied = true;
				dataTable.ajax.reload();

				$('#preview_btn').show();
				var previewUrl = BASE_URI + 'hauling_logs/preview?date_filter=' + encodeURIComponent(dateValue);
				$('#preview_btn').attr('href', previewUrl);
			} else {
				window.dateFilterApplied = false;
				dataTable.ajax.reload();
				$('#preview_btn').hide();
			}
		});

		// Handle form submission (prevent default since we auto-filter on change)
		$('#tableFilter').on('submit', function (event) {
			event.preventDefault();
		});

		$('#calendar-icon').on('click', function() {
			$('#date_filter').focus();
		});

		// Trigger initial load with today's date
		$('#date_filter').trigger('change');
	});
}(jQuery);
