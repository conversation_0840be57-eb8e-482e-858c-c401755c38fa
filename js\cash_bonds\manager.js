+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		
		console.log(BASE_URI + 'fetch/' + $table.attr('data-package'));

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: []
			}, {
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(cashBondId, dataTable) {
		$.when(deleteRow(cashBondId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(cashBondId) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/delete/' + cashBondId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function cancelAndReload(cashBondId, dataTable) {
		$.when(cancelRow(cashBondId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function cancelRow(cashBondId) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/cancel/' + cashBondId,
			type: 'POST',
			dataType: 'json'
		});
	}

  function pendingAndReload(cashBondId, dataTable) {
		$.when(pendingRow(cashBondId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(cashBondId) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/return_to_pending/' + cashBondId,
			type: 'POST',
			dataType: 'json'
		});
  }

  	// Your custom function to handle status change
	  function handleStatusChange(status) {
		console.log("The status has changed to: " + status);

		var $btn = $('#cash-bond-btn');
		var addUrl = $btn.data('add-url'); // Read the 'data-add-url' attribute
		var releaseUrl = $btn.data('release-url'); // Read the 'data-release-url' attribute

		if (status === 'released') {
			// Change the button text to 'Release Cash Bond'
			$btn.text('Release Cash Bond');

			// Modify the URL to the release cash bond URL
			$btn.attr('href', releaseUrl);
		} else {
			// If the status is not released, set the button text back to its original text
			$btn.text('Add New Cash Bond');

			// Reset the URL to the original one for adding a new cash bond
			$btn.attr('href', addUrl);
		}
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-cancel', function (event) {
				var cols = $(event.currentTarget).closest('tr.cash_bond').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to cancel Cash Bond No.' + docNo + '?',
					function () {
						var cashBondId = event.currentTarget.getAttribute('data-id');
						cancelAndReload(cashBondId, dataTable);
					}
				).setHeader('<em>Cancel Cash Bond</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.cash_bond').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete the Billing No. ' + docNo + '?',
					function () {
						var cashBondId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(cashBondId, dataTable);
					}
				).setHeader('<em>Delete Invoice</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/cash_bonds?status=' + event.currentTarget.getAttribute('data-status');
			dataTable.ajax.url(url).load();
		});
		
		$('.tab-link').on('click', function() {
			var newStatus = $(this).data('status'); // Get the new data-status value
			
			// Perform your desired actions whenever the status changes
			handleStatusChange(newStatus); // Call your function here
		});

	});
}(jQuery);
