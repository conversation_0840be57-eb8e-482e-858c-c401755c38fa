!function(n){"use strict";function t(t,e){return n.ajax({url:BASE_URI+"payments/mark_acknowledged/",type:"POST",data:{payment_id:t,ar_no:e},dataType:"json"})}n(function(){n(".action-print").click(function(){var e=n('input[name="payment_id"]').val(),i=n('input[name="ar_no"]').val();alertify.confirm("Are you sure you want to print?",function(n){n?(t(e,i),window.print(),window.location.reload()):alertify.closeLogOnClick(!0)}).setting({title:"Confirm Acknowledgement Receipt!",message:'<div style="text-align: center;">Are you sure you want to print this acknowledgement receipt?</div>'})})})}(jQuery);