<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Sku Controller
 */
class Skus extends MYT_Controller
{
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('sku');
        $this->load->model('customer');
        $this->lang->load('error_messages', 'english');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'SKU';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Sku/manager'
        ];
        $this->build_content('default', 'Skus/manager');
    }

    /**
     * Add SKU
     */
    public function add()
    {
        if ($this->_attempt_add()) {
            redirect(site_url('Skus'));
        } else {
            $this->title = 'Add SKU';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/Sku/add'
            ];

            $form_error = '';
            if (!empty($this->_error)) {
                $form_error = $this->_error_msg();
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $this->customer->select('', ['is_deleted' => 0], null, 'name ASC')
            ];

            $this->build_content('default', 'Skus/add', $data);
        }
    }
    /**
     * Edit SKU
     */
    public function edit($sku_id)
    {
        // Check if ANY edit session is active (regardless of customer or SKU)
        if ($this->_is_edit_session_active() && $this->session->userdata('editing_sku_id') != $sku_id) {
            $editing_sku_id = $this->session->userdata('editing_sku_id');
            $this->session->set_flashdata('sku_error', 'Another SKU (ID: ' . $editing_sku_id . ') is currently being edited. Only one edit session is allowed at a time, even for the same customer.');
            redirect(site_url('Skus'));
            return;
        }

        $where = [
            'id' => $sku_id,
            'is_deleted' => 0
        ];
        $sku = $this->sku->select('', $where, 1) or show_404();

        // Set edit session lock
        $this->_set_edit_session_lock($sku_id);

        if ($this->_attempt_edit($sku)) {
            // Clear edit session lock on successful edit
            $this->_clear_edit_session_lock();
            redirect(site_url('Skus'));
        } else {
            $this->title = 'Edit SKU';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/Sku/add'
            ];

            $form_error = '';
            if (!empty($this->_error)) {
                $form_error = $this->_error_msg();
            }

            // Get related SKUs for this group
            $sku_group = $this->sku->get_sku_group($sku);
            $where = ['is_deleted' => 0];
            $customers = $this->customer->select('', $where);

            $data = [
                'form_error' => $form_error,
                'sku' => $sku,
                'customers' => $customers,
                'carcass_skus' => $sku_group['carcass'],
                'giblets_skus' => $sku_group['giblets']
            ];

            $this->build_content('default', 'Skus/edit', $data);
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        // Check if this is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return false;
        }

        $customer_id = $this->input->post('customer_id');
        $skus = $this->input->post('sku');
        $sizes = $this->input->post('size');
        $ranges = $this->input->post('range');
        $bys = $this->input->post('by');
        $giblets_skus = $this->input->post('giblets_sku');
        $giblets_bys = $this->input->post('giblets_by');

        // Validate input data
        if (!$this->_validate_sku_data($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys)) {
            return false;
        }

        // Check for existing SKUs
        $existing_skus = [];

        // Check Carcass SKUs
        if (!empty($skus) && is_array($skus)) {
            foreach ($skus as $sku) {
                if (!empty(trim($sku)) && $this->sku->sku_exists(trim($sku))) {
                    $existing_skus[] = trim($sku);
                }
            }
        }

        // Check Giblets SKUs
        if (!empty($giblets_skus) && is_array($giblets_skus)) {
            foreach ($giblets_skus as $giblets_sku) {
                if (!empty(trim($giblets_sku)) && $this->sku->sku_exists(trim($giblets_sku))) {
                    $existing_skus[] = trim($giblets_sku);
                }
            }
        }

        // If any SKUs already exist, set error and return false
        if (!empty($existing_skus)) {
            if (count($existing_skus) == 1) {
                $this->_error = "SKU '" . $existing_skus[0] . "' already exists. Each SKU can only be used once.";
            } else {
                $this->_error = "SKUs '" . implode("', '", $existing_skus) . "' already exist. Each SKU can only be used once.";
            }
            return false;
        }

        // Test database connection first
        if (!$this->db->conn_id) {
            $this->_error = 'Database connection failed';
            return false;
        }

        // Start transaction
        $this->db->trans_start();

        try {
            // Use model method to create SKUs
            $sku_id = $this->sku->create_sku_group($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys);

            if (!$sku_id) {
                $this->_error = 'Failed to create SKU group';
                $this->db->trans_rollback();
                return false;
            }

            // Complete transaction
            $this->db->trans_complete();

            // Check transaction status
            if ($this->db->trans_status() === FALSE) {
                $this->_error = 'Transaction failed during commit';
                return false;
            }

            return $sku_id;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            $this->_error = 'Database error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($sku)
    {
        // Check if this is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return false;
        }

        $customer_id = $this->input->post('customer_id');
        $skus = $this->input->post('sku');
        $sizes = $this->input->post('size');
        $ranges = $this->input->post('range');
        $bys = $this->input->post('by');
        $giblets_skus = $this->input->post('giblets_sku');
        $giblets_bys = $this->input->post('giblets_by');

        // Validate input data
        if (!$this->_validate_sku_data($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys)) {
            return false;
        }

        // Start transaction
        $this->db->trans_start();

        try {
            // Use model method to update SKU group
            $result = $this->sku->update_sku_group($sku, $customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys);

            if (!$result) {
                $this->_error = 'server_error';
                $this->db->trans_rollback();
                return false;
            }

            // Complete transaction
            $this->db->trans_complete();

            // Check transaction status
            if ($this->db->trans_status() === FALSE) {
                $this->_error = 'transaction_failed';
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            $this->_error = 'database_error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Delete SKU
     */
    public function delete($sku_id)
    {
        // Check if any delete session is active
        if ($this->_is_delete_session_active()) {
            $deleting_customer_id = $this->session->userdata('deleting_customer_id');
            http_response_code(400);
            $response = [
                'message' => 'Another customer deletion is in progress (Customer ID: ' . $deleting_customer_id . '). Only one delete operation is allowed at a time.'
            ];
            echo json_encode($response);
            return;
        }

        $where = [
            'id' => $sku_id,
            'is_deleted' => 0
        ];
        if (!$sku = $this->sku->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'SKU is not found.'
            ];
        } else {
            // Set delete session lock
            $this->_set_delete_session_lock($sku->customer_id);

            if (!$this->_attempt_delete($sku)) {
                http_response_code(400);
                $response = [
                    'message' => $this->_error_msg()
                ];
            } else {
                $response = [
                    'message' => 'SKU group successfully removed.'
                ];
            }

            // Clear delete session lock
            $this->_clear_delete_session_lock();
        }

        echo json_encode($response);
    }

    /**
     * Check if SKU already exists via AJAX
     */
    public function check_existing_sku()
    {
        $sku_value = $this->input->post('sku');

        if (empty($sku_value)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing SKU value']);
            return;
        }

        $exists = $this->sku->sku_exists($sku_value);

        echo json_encode(['exists' => $exists]);
    }

    /**
     * Cancel edit session (called when user navigates away)
     */
    public function cancel_edit()
    {
        $this->_clear_edit_session_lock();
        redirect(site_url('Skus'));
    }



    /**
     * Attempt Delete
     */
    protected function _attempt_delete($sku)
    {
        // Start transaction
        $this->db->trans_start();

        try {
            // Use model method to delete SKU group
            $result = $this->sku->delete_sku_group($sku);

            if (!$result) {
                $this->_error = 'Failed to delete SKU group';
                $this->db->trans_rollback();
                return false;
            }

            // Complete transaction
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE) {
                $this->_error = 'transaction_failed';
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            $this->_error = 'database_error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Validate SKU Data
     */
    protected function _validate_sku_data($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys)
    {
        if (empty($customer_id)) {
            $this->_error = 'Please select a customer.';
            return false;
        }
        $skus = $skus ?: [];
        $sizes = $sizes ?: [];
        $ranges = $ranges ?: [];
        $bys = $bys ?: [];
        $giblets_skus = $giblets_skus ?: [];
        $giblets_bys = $giblets_bys ?: [];
        if (empty($skus[0]) && empty($sizes[0]) && empty($ranges[0]) && empty($bys[0])) {
            $this->_error = 'At least one Carcass entry is required.';
            return false;
        }
        if (count($skus) !== count($sizes) ||
            count($sizes) !== count($ranges) ||
            count($ranges) !== count($bys)) {
            $this->_error = 'All Carcass fields must have matching entries.';
            return false;
        }
        if (empty(trim($skus[0])) || empty(trim($sizes[0])) ||
            empty(trim($ranges[0])) || empty(trim($bys[0]))) {
            $this->_error = 'The first Carcass entry must have all fields filled.';
            return false;
        }
        if (!empty($giblets_skus) && !empty($giblets_bys)) {
            if (count($giblets_skus) !== count($giblets_bys)) {
                $this->_error = 'Giblets SKU and By fields must have matching entries.';
                return false;
            }
        }

        return true;
    }

    /**
     * Check if an edit session is currently active
     */
    protected function _is_edit_session_active()
    {
        $editing_sku_id = $this->session->userdata('editing_sku_id');
        $edit_timestamp = $this->session->userdata('edit_timestamp');
        if (empty($editing_sku_id) || empty($edit_timestamp)) {
            return false;
        }
        $timeout = 30 * 60; 
        if (time() - $edit_timestamp > $timeout) {
            $this->_clear_edit_session_lock();
            return false;
        }

        return true;
    }

    /**
     * Set edit session lock
     */
    protected function _set_edit_session_lock($sku_id)
    {
        $this->session->set_userdata([
            'editing_sku_id' => $sku_id,
            'edit_timestamp' => time()
        ]);
    }

    /**
     * Clear edit session lock
     */
    protected function _clear_edit_session_lock()
    {
        $this->session->unset_userdata(['editing_sku_id', 'edit_timestamp']);
    }

    /**
     * Check if a delete session is currently active
     */
    protected function _is_delete_session_active()
    {
        $deleting_customer_id = $this->session->userdata('deleting_customer_id');
        $delete_timestamp = $this->session->userdata('delete_timestamp');
        if (empty($deleting_customer_id) || empty($delete_timestamp)) {
            return false;
        }
        $timeout = 5 * 60;
        if (time() - $delete_timestamp > $timeout) {
            $this->_clear_delete_session_lock();
            return false;
        }

        return true;
    }

    /**
     * Set delete session lock
     */
    protected function _set_delete_session_lock($customer_id)
    {
        $this->session->set_userdata([
            'deleting_customer_id' => $customer_id,
            'delete_timestamp' => time()
        ]);
    }

    /**
     * Clear delete session lock
     */
    protected function _clear_delete_session_lock()
    {
        $this->session->unset_userdata(['deleting_customer_id', 'delete_timestamp']);
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'transaction_failed':
                return '<p>Transaction failed. Please try again.</p>';

            default:
                if (strpos($this->_error, 'database_error:') === 0) {
                    return '<p>' . $this->_error . '</p>';
                }
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}
