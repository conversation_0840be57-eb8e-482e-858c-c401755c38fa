<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>Giblets List</h2>
        <a href="<?= site_url('Giblets/add') ?>" class="btn btn-primary">Add Giblets</a>
    </div>
    <?php if ($this->session->flashdata('success')): ?>
        <div class="alert alert-success"><?= $this->session->flashdata('success') ?></div>
    <?php endif; ?>
    <?php if ($this->session->flashdata('error')): ?>
        <div class="alert alert-danger"><?= $this->session->flashdata('error') ?></div>
    <?php endif; ?>
    <table id="gibletsTable" class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>SKU</th>
                <th>By</th>
                <th>NO</th>
                <th>PCS</th>
                <th>KGS</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($giblets_list)): ?>
                <?php foreach ($giblets_list as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row->sku) ?></td>
                        <td><?= htmlspecialchars($row->by) ?></td>
                        <td><?= htmlspecialchars($row->no) ?></td>
                        <td><?= htmlspecialchars($row->pcs) ?></td>
                        <td><?= htmlspecialchars($row->kgs) ?></td>
                        <td>
                            <a href="<?= site_url('Giblets/edit/' . $row->id) ?>" class="btn btn-sm btn-warning">Edit</a>
                            <button class="btn btn-sm btn-danger btn-delete" data-id="<?= $row->id ?>">Delete</button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<script src="<?= base_url('js/Giblets/manager.js') ?>"></script> 