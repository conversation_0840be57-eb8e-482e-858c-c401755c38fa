<?php
class Check extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'check_slip';
        parent::__construct();
    }

    /**
     * Get Print Preview
     */
    public function get_preview($check_id)
    {
        $sql = <<<EOT
SELECT check_slip.*, bank.name AS bank, check_template.file_name AS print_template, supplier.name as supplier
FROM check_slip
LEFT JOIN bank ON bank.id = check_slip.bank_id
LEFT JOIN supplier ON supplier.name = check_slip.payee
LEFT JOIN check_template ON check_template.id = bank.print_template
WHERE check_slip.id = ?
    AND check_slip.is_deleted = 0
EOT;
        $binds = [$check_id];

        $res = $this->advanced_query($sql, $binds);

        return $res ? $res[0] : null;
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
