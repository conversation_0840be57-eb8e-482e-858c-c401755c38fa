<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Live_sell_suppliers extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('live_sell_supplier');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch live_sell_suppliers
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'live_sell_supplier';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_address(),
            $this->_get_phone_no(),
            $this->_get_email(),
            $this->_get_file(),
            // $this->_get_credit_limit(),
            $this->_get_actions()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'live_sell_supplier';
            }
        ];
    }

    /**
     * Get live_sell_supplier name
     */
    protected function _get_name()
    {
        return [
            'db' => 'name',
            'dt' => 0,
            'field' => 'name'
        ];
    }

    /**
     * Get live_sell_supplier address
     */
    protected function _get_address()
    {
        return [
            'db' => 'address',
            'dt' => 1,
            'field' => 'address'
        ];
    }

    /**
     * Get live_sell_supplier phone number
     */
    protected function _get_phone_no()
    {
        return [
            'db' => 'phone_no',
            'dt' => 2,
            'field' => 'phone_no'
        ];
    }

    /**
     * Get live_sell_supplier email
     */
    protected function _get_email()
    {
        return [
            'db' => 'email',
            'dt' => 3,
            'field' => 'email'
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'live_sell_supplier.id',
            'as' => 'file',
            'dt' => 4,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->live_sell_supplier->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/live_sell_suppliers/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get live_sell_supplier credit limit
     */
    protected function _get_credit_limit()
    {
        return [
            'db' => 'IFNULL(credit_limit, 0) AS credit_limit',
            'dt' => 5,
            'field' => 'credit_limit'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 5,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('live_sell_suppliers/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
