<div class="sect">
	<div class="col-md-4 offset-md-3 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class='row'>
					<div class="col-md-12 form-group">
						<label for="total_payable">Total Payable</label>

						<input type="text" name="total_payable" class="form-control-plaintext" value="<?=set_value('total_payable', number_format($payable,2));?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('total_payable');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="amount_paid">Amount Paid</label>

						<input type="number" name="amount_paid" class="form-control" value="<?=set_value('amount_paid');?>" step="any">
						<div class="alert alert-danger invalid-amount d-none" role="alert">
						Paid amount exceeds remaining balance!
						</div>

						<div class="red-text flash-message">
							<?=form_error('amount_paid');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="balance">Remaining Balance</label>

						<input type="text" name="balance" class="form-control-plaintext" value="<?=set_value('balance');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('balance');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="change">Change</label>

						<input type="text" name="change" class="form-control-plaintext" value="<?=set_value('change');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('change');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="payment_date" class="control-label">Payment Date</label>

						<input type="text" name="payment_date" class="form-control datepicker" value="<?=set_value('payment_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('payment_date');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('pos_invoices/'.$pos_invoice->terms)?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
