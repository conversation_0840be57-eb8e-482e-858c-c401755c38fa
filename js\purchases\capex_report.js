+ function ($) {
	'use strict';
    
	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		// console.log(BASE_URI + 'fetch/' + $table.attr('data-package'));
		var dataTable = $table.DataTable({
			dom: 'Brtip',
			lengthChange: true,
			paging: false,
			filter: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
                {
                    className: 'text-right',
                    targets: [4, cols.length - 1]
                },
                {
                    className: 'text-left',
                    targets: [0, 1, 2, 3]
                },
            ],
			buttons: [
                {
                    extend: 'print',
                    className: 'btn btn-secondary',
                    footer: true,
                    exportOptions: {
                        stripHtml: false
                    }
                },
                {
                    extend: 'excel',
                      className: 'btn btn-success ml-2',
                      text: 'Download Excel File',
                    footer: true,
                    exportOptions: {
                         columns: 'th'
                    }
                }
                // {
                //     className: 'btn btn-success ml-2',
                //     text: 'Download Excel',
                //     action: function (e, dt, node, config) {
                //         var date_from = $('input[name = "from"]').val();
                //         var date_to = $('input[name = "to"]').val();

                //         $.when(createExcelReport(date_from, date_to)).then(
                //             function (response) {
                //                 console.log(response);
                //                 var link = document.createElement("a");
                //                 link.setAttribute('download', '');
                //                 link.href = BASE_URI + "assets/" + response.filename;
                //                 document.body.appendChild(link);
                //                 link.click();
                //                 link.remove();
                //             }
                //         )
                //     }
                // }
            ],
			autoWidth: false,
			processing: true,
			serverSide: true,
			deferLoading: 0,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package'),
			footerCallback: function (row, data, start, end, display) {
                var api = this.api();
                var colSum = $table.attr('data-col-sum');

                if (typeof colSum !== 'undefined' && colSum > -1) {
                    // returns the numeric value of data
                    var intVal = function (i) {
                        return typeof i === 'string' ?
                            i.replace(/[\$,]/g, '') * 1 :
                            typeof i === 'number' ?
                            i : 0;
                    };

                    // total of all data in table
                    var grandTotal = api
                        .column(colSum)
                        .data()
                        .reduce(function (a, b) {
                            return intVal(a) + intVal(b);
                        }, 0);


                    $(api.column(colSum).footer()).html($.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2)));
                }
            }
		});

		return dataTable;
	}

    function createExcelReport(date_from, date_to) {
		return $.ajax({
			url: BASE_URI + 'purchase/capex_excel',
			type: 'POST',
			dataType: 'json',
			data: {
				dateFrom: date_from,
				dateTo: date_to
			}
		})
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('#tableFilter').submit(function(event) {
			event.preventDefault();

			// console.log(BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + $(event.currentTarget).serialize());
			var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + $(event.currentTarget).serialize()
			dataTable.ajax.url(url).load();

		}).trigger('submit'); 
	});
}(jQuery);