<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $customer->id === $order_confirmation->customer_id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>
						
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="order_confirmation_date" class="control-label">Confirm Date</label>

						<input type="text" name="order_confirmation_date" class="form-control datepicker" value="<?=set_value('order_confirmation_date', DateTime::createFromFormat('Y-m-d', $order_confirmation->order_confirmation_date)->format('m/d/Y'));?>" >

						<div class="red-text flash-message">
							<?=form_error('order_confirmation_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $order_confirmation->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width:100px">Item</th>

									<th class="text-center" style="width: 20%;">Supplier</th>

									<th class="text-center" style="width: 10%;">Order Qty</th>

									<th class="text-center" style="width: 10%;">Confirmed Qty</th>

									<th class="text-center" style="width: 10%;">Unit</th>

									<th class="text-center" style="width: 10%;">Est. ALW</th>

									<th class="text-center" style="width: 10%;">Est. Due Amount</th>

									<th class="text-center d-none" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($order_confirmation_item_ids)):
                                	$counter = 0;
                                    foreach ($order_confirmation_item_ids as $i => $order_confirmation_item_id):
									$order_confirmation_item = $order_confirmation_items[$counter];
									// $est_alw = $order_confirmation_items[$counter]['est_alw'];
                                    // $order_confirmation_item_id = $order_confirmation_item->id;
                            ?>
										<tr class="order_confirmation_item">
											<td>
												<?php $fn_order_confirmation_item = 'order_confirmation_item_' . $order_confirmation_item_id;?>
												<input type="hidden" name=<?=$fn_order_confirmation_item?> value="<?=set_value($fn_order_confirmation_item, $order_confirmation_item_id);?>">

												<?php $fdn_item = 'item_' . $order_confirmation_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
												<?php if($retain_items[$counter]):?>
														<option value=<?=$retain_items[$counter]['item_id']?>><?=$retain_items[$counter]['item_name']?></option>
													<?php else:?>
														<option></option>
													<?php endif;?>
												</select>
											</td>

											<td>
												<?php $fdn_supplier = 'supplier_id_' . $order_confirmation_item_id;?>

												<select name="<?=$fdn_supplier;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach ($suppliers as $supplier): ?>
														<option value="<?=$supplier->id;?>" <?=set_select('suppliers', $supplier->id, $retain_items[$counter]['supplier_id'] === $supplier->id);?>><?=$supplier->name?></option>
													<?php endforeach; ?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_order_confirmation_item);?>
												</div>
											</td>

											<td>
												<?php $fn_order_qty = 'order_qty_' . $order_confirmation_item_id;?>
												<input type="number" name="<?=$fn_order_qty;?>" class="form-control" value="<?=set_value($fn_order_qty, number_format($order_confirmation_item->qty, 2));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_order_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $order_confirmation_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, $order_confirmation_item->qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $order_confirmation_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $order_confirmation_item->unit);?>" data-suggest="units" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_est_alw = 'est_alw_' . $order_confirmation_item_id;?>
												<input type="number" name="<?=$fn_est_alw;?>" class="form-control" value="<?=set_value($fn_est_alw, $order_confirmation_item->est_alw);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $order_confirmation_item_id;?>
												<input type="number" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount, $order_confirmation_item->est_due_amount);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_due_amount);?>
												</div>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<!-- <button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('order_confirmations')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
