<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <form id="carcassEditForm" method="post" action="<?= site_url('Carcasses/update/' . $carcass->id); ?>" class="crud-box" accept-charset="utf-8">
                        <h1 class="crud-title">Edit Carcass</h1>

                        <?php if (validation_errors()): ?>
                            <div class="alert alert-danger">
                                <?= validation_errors(); ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($this->session->flashdata('error')): ?>
                            <div class="alert alert-danger">
                                <?= $this->session->flashdata('error'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sku">SKU</label>
                                    <input type="text" class="form-control" id="sku" name="sku" value="<?= htmlspecialchars($carcass->sku) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="size">Size</label>
                                    <input type="text" class="form-control" id="size" name="size" value="<?= htmlspecialchars($carcass->size) ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="range">Range</label>
                                    <input type="text" class="form-control" id="range" name="range" value="<?= htmlspecialchars($carcass->range) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no">NO</label>
                                    <input type="text" class="form-control" id="no" name="no" value="<?= htmlspecialchars($carcass->no) ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="pcs">PCS</label>
                                    <input type="number" class="form-control" id="pcs" name="pcs" value="<?= htmlspecialchars($carcass->pcs) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="kgs">KGS</label>
                                    <input type="number" step="0.01" class="form-control" id="kgs" name="kgs" value="<?= htmlspecialchars($carcass->kgs) ?>">
                                </div>
                            </div>
                        </div>

                        <hr>
                        <div class="clearfix text-center">
                            <button type="submit" class="btn btn-primary">Update</button>
                            <a href="<?= site_url('Carcasses'); ?>" class="btn btn-light">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
