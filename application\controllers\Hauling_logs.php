<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Hauling_logs extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('hauling_log');
        $this->load->model('schedule');
        $this->load->model('schedule_list');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Hauling Logs';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/hauling_logs/manager'
        ];
        $this->build_content('default', 'hauling_logs/manager');
    }

    /**
     * Preview hauling logs for a specific date
     */
    public function preview()
    {
        $date_filter = $this->input->get('date_filter', true);

        if (!$date_filter) {
            show_404();
        }

        $filter_date = DateTime::createFromFormat('m/d/Y', $date_filter);
        if (!$filter_date) {
            show_404();
        }

        // Get hauling logs data for the specific date
        $hauling_logs = $this->hauling_log->get_hauling_logs_with_schedule_info($date_filter);

        $this->title = 'Hauling Logs';
        $this->css = [
            'css/style'
        ];
        $this->javascript = [
            'js/hauling_logs/preview'
        ];

        $data = [
            'date_filter' => $date_filter,
            'formatted_date' => $filter_date->format('F d, Y'),
            'hauling_logs' => $hauling_logs
        ];

        $this->build_content('default', 'hauling_logs/preview', $data);
    }

    /**
     * Add hauling log
     */
    public function add()
    {
        $rules = $this->config->item('hauling_logs/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('hauling_logs'));
        } else {
            $this->title = 'Add Hauling Log';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/hauling_logs/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'grower_farms' => $this->_get_grower_farms_from_schedules()
            ];
            $this->build_content('default', 'hauling_logs/add', $data);
        }
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_id = $this->input->post('schedule_id'); // This is actually customer_id now
        $schedule_list_id = $this->input->post('schedule_list_id');

        // Get schedule information to validate
        $schedule_info = $this->hauling_log->get_schedule_info($schedule_list_id, $customer_id);
        if (!$schedule_info) {
            $this->db->trans_rollback();
            $this->_error = 'Invalid schedule selected';
            return false;
        }

        // Calculate weights
        $gross_weight = floatval($this->input->post('gross_weight'));
        $tare_weight = floatval($this->input->post('tare_weight'));
        $net_weight = $gross_weight - $tare_weight;

        // Prepare hauling log data
        $hauling_log_data = [
            'schedule_list_id' => $schedule_list_id,
            'truck_name' => $this->input->post('truck_name'),
            'truck_plate' => $this->input->post('truck_plate'),
            'ata_time' => $this->input->post('ata_time'),
            'weighed_time' => $this->input->post('weighed_time'),
            'remarks' => $this->input->post('remarks'),
            'gross_weight' => $gross_weight,
            'tare_weight' => $tare_weight,
            'net_weight' => $net_weight,
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        // Create hauling log using built-in insert method
        $hauling_log_id = $this->hauling_log->insert($hauling_log_data);

        if (!$hauling_log_id) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $hauling_log_id;
    }

    /**
     * Get grower farm names from schedules for the dropdown
     */
    protected function _get_grower_farms_from_schedules()
    {
        return $this->schedule->get_grower_farms_from_schedules();
    }



    /**
     * Get trips for a specific customer (AJAX endpoint)
     */
    public function get_trips_by_customer($customer_id)
    {
        $trips = $this->schedule->get_trips_by_customer($customer_id);

        echo json_encode([
            'success' => true,
            'trips' => $trips
        ]);
    }

    /**
     * Delete hauling log
     */
    public function delete($hauling_log_id)
    {
        $where = [
            'id' => $hauling_log_id,
            'is_deleted' => 0
        ];
        if (!$hauling_log = $this->hauling_log->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Hauling Log is not found.'
            ];
        } elseif (!$this->_attempt_delete($hauling_log)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Hauling Log is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($hauling_log)
    {
        $this->db->trans_begin();

        $condition = [
            'id' => $hauling_log->id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->hauling_log->update($condition, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $is_deleted;
    }



}