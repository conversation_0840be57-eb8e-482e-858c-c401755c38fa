!function(t){"use strict";t(function(){var e,a,r=(e=t(".tableA"),a=e.find("thead th"),e.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:a.length-1}],buttons:[{extend:"print",footer:!0,exportOptions:{stripHtml:!1}}],autoWidth:!1,processing:!0,serverSide:!0,deferLoading:0,ajax:BASE_URI+"fetch/"+e.attr("data-package"),footerCallback:function(a,r,n,o,i){var u=this.api(),c=e.attr("data-col-sum");if(void 0!==c&&c>-1){var l=function(t){return"string"==typeof t?1*t.replace(/[\$,]/g,""):"number"==typeof t?t:0},f=u.column(c).data().reduce(function(t,e){return l(t)+l(e)},0);u.column(c,{page:"current"}).data().reduce(function(t,e){return l(t)+l(e)},0),t(u.column(c).footer()).html(function(t){for(;/(\d+)(\d{3})/.test(t.toString());)t=t.toString().replace(/(\d+)(\d{3})/,"$1,$2");return t}(parseFloat(f).toFixed(2)))}}}));t("#tableFilter").submit(function(e){e.preventDefault();var a=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+t(e.currentTarget).serialize();console.log(a);r.ajax.url(a).load()})})}(jQuery);