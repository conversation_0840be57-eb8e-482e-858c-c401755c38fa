<div class="hauling-logs-container">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <div class="sect">
                        <div class="col-md-12 form-group">
                            <form id="haulingLogForm" method="post" action="<?php echo site_url('hauling_logs/add'); ?>" class="crud-box" accept-charset="utf-8">
                                <h1 class="crud-title">Add New Hauling Log</h1>
                                <fieldset>
                                    <h5 class="crud-subtitle"><span>Shift Hauling Log Sheet</span></h5>
                                    <br><br>
                                <div class="row">

                                

                                    <div class="col-md-4 form-group">
						            <label for="schedule_id" class="control-label">Grower Farm Name/Place</label>

						                <select id="schedule_id" class="form-control dropsearch" name="schedule_id" data-placeholder="Choose a grower farm...">
							                <option></option>
							                <?php foreach ($grower_farms as $farm): ?>
								            <option value="<?= $farm->id ?>" <?= set_select('schedule_id', $farm->id); ?>>
									        <?= ucwords($farm->grower_farm_name) ?>
								            </option>
							                <?php endforeach; ?>
						                </select>
						                <div class="red-text flash-message">
							                <?= form_error('schedule_id'); ?>
						                    </div>
					                    </div>

                                        <div class="col-md-4 form-group">
                                            <label for="schedule_list_id" class="control-label">Trip No.</label>
                                            <select class="form-control" id="schedule_list_id" name="schedule_list_id" disabled>
                                                <option value="">Select Trip No.</option>
                                            </select>
                                            <div class="red-text flash-message">
                                                <?= form_error('schedule_list_id'); ?>
                                            </div>
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label for="waf_rs_no" class="control-label">WAF No./RS No.</label>
                                            <input type="text" class="form-control" id="waf_rs_no" name="waf_rs_no" readonly>
                                            <div class="red-text flash-message">
                                                <?= form_error('waf_rs_no'); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                        <label for="truck_name" class="control-label">Truck Name</label>
                                            <input type="text" class="form-control" id="truck_name" name="truck_name" value="<?= set_value('truck_name'); ?>">
                                            <div class="red-text flash-message">
                                                <?=form_error('truck_name');?>
                                    </div>
                                        </div>
                                        <div class="col-md-6 form-group">
                                        <label for="truck_plate" class="control-label">Truck Plate#</label>
                                            <input type="text" class="form-control" id="truck_plate" name="truck_plate" value="<?= set_value('truck_plate'); ?>">
                                            <div class="red-text flash-message">
                                                <?=form_error('truck_plate');?>
                                        </div>
                                     </div>
                                </div>

                                    <h5 class="crud-subtitle"><span>Time Tracking</span></h5>
                                    <br>    
                                    <div class="row">
                                        <div class="col-md-3 form-group">
                                            <label for="eta_display" class="control-label">Expected Time of Arrival</label>
                                            <input type="text" class="form-control" id="eta_display" readonly>
                                            <input type="hidden" id="eta" name="eta">
                                            <div class="red-text flash-message">
                                                <?=form_error('eta');?>
                                     </div>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="ata_time" class="control-label">Actual Time of Arrival</label>
                                            <input type="time" class="form-control time-input" id="ata_time" name="ata_time" value="<?= set_value('ata_time'); ?>">
                                            <div class="red-text flash-message">
                                                <?=form_error('ata_time');?>
                                    </div>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="weighed_time" class="control-label">Weighed Time</label>
                                            <input type="time" class="form-control time-input" id="weighed_time" name="weighed_time" value="<?= set_value('weighed_time'); ?>">
                                            <div class="red-text flash-message">
                                                <?=form_error('weighed_time');?>
                                    </div>
                                        </div>

                                        <div class="col-md-3 form-group">
                                            <label for="remarks" class="control-label">Remarks on Arrival</label>
                                            <input type="text" class="form-control" id="remarks" name="remarks" value="<?= set_value('remarks'); ?>">
                                            <div class="red-text flash-message">
                                                <?=form_error('remarks');?>
                                        </div>
                                     </div>
                                </div>

                                    <h5 class="crud-subtitle"><span>Count Information</span></h5>
                                    <br>
                                <div class="row">
                                        <div class="col-md-3 form-group">
                                            <label for="gross_weight_display" class="control-label">Gross WG in Ticket</label>
                                            <input type="number" class="form-control" id="gross_weight_display" name="gross_weight_display" value="0">
                                            <input type="hidden" id="gross_weight" name="gross_weight" >
                                            <div class="red-text flash-message" id="gross_weight_error">
                                                <?=form_error('gross_weight_display');?>
                                                <?=form_error('gross_weight');?>
                                    </div>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="tare_weight_display" class="control-label">Tare WG in Ticket</label>
                                            <input type="number" class="form-control" id="tare_weight_display" name="tare_weight_display" value="0">
                                            <input type="hidden" id="tare_weight" name="tare_weight" >
                                            <div class="red-text flash-message" id="tare_weight_error">
                                              <?=form_error('tare_weight_display');?>
                                              <?=form_error('tare_weight');?>
                                    </div>

                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="net_weight_display" class="control-label">Net weight in Ticket</label>
                                            <input type="text" class="form-control" id="net_weight_display" readonly>
                                            <input type="hidden" id="net_weight" name="net_weight">
                                        </div>
                                    
                                </fieldset>
                                <hr>
                                <div class="clearfix text-center">
                                    <button type="submit" class="btn btn-primary">Save</button>
                                    <a href="<?=site_url('hauling_logs')?>" class="btn btn-light">Close</a>
                                </div>
                            </form>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>


