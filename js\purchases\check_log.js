+(function ($, fns) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			lengthChange: true,
			language: {
				infoFiltered: "",
			},
			pagingType: "full_numbers",
			order: [[0, "asc"]],
			scrollX: true,
			autoWidth: false,
			paging: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + $table.attr("data-package"),
		});

		return dataTable;
	}

	$(function () {
		var dataTable = createDataTable(".tableA");
	});
})(jQuery, dateFns);
