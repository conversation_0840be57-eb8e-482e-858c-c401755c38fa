!function(r){"use strict";function e(e){r.when(a(e)).then(function(){window.print()},function(r){var e=JSON.parse(r.responseText);alertify.error(e.message)})}function t(e,t,a){r.when(n(e,t,a)).then(function(r){window.location.href=BASE_URI+"purchase_orders/manager/"},function(r){var e=JSON.parse(r.responseText);alertify.error(e.message)})}function a(e){return r.ajax({url:BASE_URI+"purchase_orders/mark_printed/"+e,type:"POST",dataType:"json"})}function n(e,t,a){return r.ajax({url:BASE_URI+"purchase_orders/mark_status/",type:"POST",dataType:"json",data:{purchase_order_id:e,type:t,remarks:a}})}r(function(){r("button.action-print").click(function(r){var a=r.currentTarget.getAttribute("data-type");"Audit"===a?alertify.prompt("Audit Purchase Order","Remarks","",function(e,n){var i=r.currentTarget.getAttribute("data-id"),u=n;""===u?alertify.alert('<span class="red-text">Missing Remarks!</span>').setHeader("<em>Required</em>"):t(i,a,u)},function(){alertify.error("Cancel")}):alertify.confirm("Are you sure you want to "+a+" this Purchase Order?",function(){var n=r.currentTarget.getAttribute("data-id");"Print"==a?e(n):t(n,a,null)}).setHeader("<em>"+a+" Purchase Order</em>")})})}(jQuery);