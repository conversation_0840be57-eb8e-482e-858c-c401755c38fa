<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 * Application Number Helpers (Based on CodeIgniter's Helpers)
 *
 * @package		Application
 * @subpackage	Helpers
 * @category	Number
 */

 // ------------------------------------------------------------------------

/**
 * Checks if number does not exceed max value
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('max_value'))
{
	function max_value($value, $max)
	{
        return $value <= $max;
	}
}

 // ------------------------------------------------------------------------

/**
 * Checks if number is not lower than min value
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('min_value'))
{
	function min_value($value, $min)
	{
        return $value >= $min;
	}
}

 // ------------------------------------------------------------------------

/**
 * Checks if number is equal to given value
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('equal_value'))
{
	function equal_value($value, $num)
	{
		return ctype_digit($value) && ctype_digit($num) && $value === $num;
	}
}

 // ------------------------------------------------------------------------

/**
 * Spellout whole number
 *
 * @access    public
 * @param    string
 * @param    string
 * @return    bool
 */

if (!function_exists('spellout')) {
    function spellout($num)
    {
        $num = str_replace([',', ' '], '', trim($num));
        if (!$num || round($num) != $num) {
            return false;
        }
        $num = (int) $num;
        $words = [];
        $list1 = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven',
            'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'
        ];
        $list2 = ['', 'ten', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety', 'hundred'];
        $list3 = ['', 'thousand', 'million', 'billion', 'trillion', 'quadrillion', 'quintillion', 'sextillion', 'septillion',
            'octillion', 'nonillion', 'decillion', 'undecillion', 'duodecillion', 'tredecillion', 'quattuordecillion',
            'quindecillion', 'sexdecillion', 'septendecillion', 'octodecillion', 'novemdecillion', 'vigintillion'
        ];
        $num_length = strlen($num);
        $levels = (int) (($num_length + 2) / 3);
        $max_length = $levels * 3;
        $num = substr('00' . $num, -$max_length);
        $num_levels = str_split($num, 3);
        for ($i = 0; $i < count($num_levels); $i++) {
            $levels--;
            $hundreds = (int) ($num_levels[$i] / 100);
            $hundreds = ($hundreds ? ' ' . $list1[$hundreds] . ' hundred' . ' ' : '');
            $tens = (int) ($num_levels[$i] % 100);
            $singles = '';
            if ($tens < 20) {
                $tens = ($tens ? ' ' . $list1[$tens] . ' ' : '');
            } else {
                $tens = (int) ($tens / 10);
                $tens = ' ' . $list2[$tens] . ' ';
                $singles = (int) ($num_levels[$i] % 10);
                $singles = ' ' . $list1[$singles] . ' ';
            }
            $words[] = $hundreds . $tens . $singles . (($levels && (int) ($num_levels[$i])) ? ' ' . $list3[$levels] . ' ' : '');
        } //end for loop
        $commas = count($words);
        if ($commas > 1) {
            $commas = $commas - 1;
        }

        return implode(' ', $words);
    }
}

/* End of file myt_date_validate_helper.php */
/* Location: ./application/helpers/myt_date_validate_helper.php */
