<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Sales_orders extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('sales_orders/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review sales_order Order for Approval
     */
    public function receive($sales_order_id)
    {
        $sales_order = $this->sales_order->get_by_id($sales_order_id) or show_404();

        $this->title = 'Receive sales_order No. ' . $sales_order->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_orders/receive'
        ];

        $sales_order_items = $this->sales_order_item->get_by_sales_order($sales_order->id);

        $data = [
            'sales_order'          => $sales_order,
            'sales_order_items'    => $sales_order_items
        ];
        $this->build_content('default', 'sales_orders/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add sales_order
     */
    public function add($processing_fee_id = null)
    {
        $processing_fee = $this->processing_fee->get_by_id($processing_fee_id) or show_404();
        $order_slip_id = $processing_fee->order_slip_id;
        $sales_order_item_ids = $this->_get_sales_order_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/sales_orders',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('sales_orders/add'), $this->_get_sales_order_item_rules('', $sales_order_item_ids));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($sales_order_id = $this->_attempt_add($order_slip_id, $processing_fee_id))) {
            $new_path = FCPATH . 'assets/sales_orders/' . $sales_order_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('sales_orders/manager'));
        } else {
            $this->title = 'Add Sales Order';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/sales_orders/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_sales_order_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;
                    }
                }
            }

            $data = [
                'processing_fee'        => $processing_fee,
                'form_error'            => $form_error,
                'sales_order_item_ids'  => $sales_order_item_ids,
                'customers'             => $customers,
                'retain_items'          => $retain_items,
            ];

            $this->build_content('default', 'sales_orders/add', $data);
        }
    }

    /**
     * Edit sales_order
     */
    public function edit($sales_order_id)
    {
        $sales_order = $this->sales_order->get_by_id($sales_order_id) or show_404();
        $order_slip_id = $sales_order->order_slip_id;
        $sales_order_item_ids = $this->_get_sales_order_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/sales_orders',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('sales_orders/edit'), $this->_get_sales_order_item_rules('', $sales_order_item_ids));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($sales_order_id = $this->_attempt_edit($sales_order))) {
            $new_path = FCPATH . 'assets/sales_orders/' . $sales_order_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('sales_orders/manager'));
        } else {
            $this->title = 'Edit Sales Order';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/sales_orders/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_sales_order_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            } else {
                $where = [
                    'sales_order_id' => $sales_order->id,
                    'is_deleted' => 0
                ];
                $sales_order_items = $this->sales_order_item->select('', $where);

                $retain_items = [];
                foreach($sales_order_items AS $index => $sales_order_item) {
                    $sales_order_item_ids[] = $sales_order_item->id;
                    $where = [
                        'id'            => $sales_order_item->item_id,
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);

                    if($item) {
                        $item_name = $item->name;
                    } else {
                        $item_name = "";
                    }

                    $retain_items[] = [
                        'id' => $sales_order_item->id,
                        'item_id' => $sales_order_item->item_id,
                        'item_name' => $item_name,
                        'actual_live_weight' => $sales_order_item->actual_live_weight,
                        'unit' => $sales_order_item->unit,
                        'unit_price' => $sales_order_item->unit_price?:0,
                        'amount' => $sales_order_item->amount,
                        'added_by' => $sales_order_item->added_by,
                        'added_on' => $sales_order_item->added_on
                    ];
                }
            }

            $data = [
                'sales_order'           => $sales_order,
                'form_error'            => $form_error,
                'sales_order_item_ids'  => $sales_order_item_ids,
                'customers'             => $customers,
                'retain_items'          => $retain_items,
            ];

            $this->build_content('default', 'sales_orders/edit', $data);
        }
    }

    /**
     * Approve sales_order
     */
    public function receive_sales_order($sales_order_id)
    {
        $where = [
            'id' => $sales_order_id,
            'is_deleted' => 0
        ];
        if (!$sales_order = $this->sales_order->select('', $where, 1)) {
            $response = [
                'message' => 'sales_order is not found.'
            ];
        } elseif (!$this->_attempt_receive($sales_order)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'sales_order is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($sales_order)
    {
        $where = [
            'id' => $sales_order->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->sales_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete sales_order
     */
    public function delete($sales_order_id)
    {
        $where = [
            'id' => $sales_order_id,
            'is_deleted' => 0
        ];
        if (!$sales_order = $this->sales_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Sales Order is not found.'
            ];
        } elseif (!$this->_attempt_delete($sales_order)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Sales Order is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * sales_orders
     */
    public function manager()
    {
        $this->title = "Sales Orders";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_orders/manager'
        ];

        // $payment_types = ['cash', 'check', 'others'];
        $payment_types = ['cash'];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
            'payment_types'  => $payment_types
        ];

        $this->build_content('default', 'sales_orders/manager', $data);
    }

    /**
     * Add Payment to Sales order
     */
    public function payment($sales_order_id = '')
    {
        $rules = array_merge(
            $this->config->item('sales_order_payments/add')
        );

        if (!$sales_order = $this->sales_order->get_by_id($sales_order_id)) {
            http_response_code(400);
            $response = [
                'message' => 'Sales order is not found.'
            ];
        } elseif (!$this->_validate_form($rules) || !$this->_generate_payment($sales_order)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Sales order has added payment.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Generate Payment
     */
    protected function _generate_payment($sales_order)
    {
        $this->db->trans_begin();

        $payment_type = $this->input->post('payment_type');

        if (!$payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('payment_date'))) {
            $payment_date = new DateTime();
        }

        $values = [
            'customer_id' => $sales_order->customer_id,
            'sales_order_id' => $sales_order->id,
            'payment_date' => $payment_date->format('Y-m-d'),
            'payment_type' => $payment_type,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        switch ($payment_type) {
            case 'cash':
                $amount = floatval(str_replace(',', '', $this->input->post('amount')));
                $values['amount'] = $amount;
                break;

            case 'check':
                $amount = floatval(str_replace(',', '', $this->input->post('amount')));
                $values['amount'] = $amount;
                $values['check_date'] = DateTimeImmutable::createFromFormat('m/d/Y', $this->input->post('check_date'))->format('Y-m-d');
                $values['check_no'] = $this->input->post('check_no');
                $values['bank_name'] = $this->input->post('bank_name');
                break;

            case 'others':
                $amount = floatval(str_replace(',', '', $this->input->post('amount')));
                $values['amount'] = $amount;
                $values['others'] = $this->input->post('payment_description');
                break;
        }

        if (!$payment_id = $this->sales_order_payment->insert($values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
        } else {
            $this->db->trans_commit();
        }

        return $payment_id;
    }

    /**
     * sales_order Print Preview
     */
    public function preview($sales_order_id)
    {
        $sales_order = $this->sales_order->get_by_id($sales_order_id) or show_404();
        $sales_order_items = $this->sales_order_item->get_by_sales_order($sales_order_id);
        $sales_order_payments = $this->sales_order_payment->get_by_sales_order($sales_order_id);

        if (!$sales_order_item_ids = $this->_get_sales_order_item_ids()) {
            foreach($sales_order_items AS $sales_order_item) {
                $item = $this->live_sell_item->select('', ['id' => $sales_order_item->item_id, 'is_deleted' => 0], 1);
                
                $retain_items[] = [
                    'id' => $sales_order_item->id,
                    'item_id' => $sales_order_item->item_id,
                    'item_name' => $item?$item->name:"",
                    'actual_live_weight' => $sales_order_item->actual_live_weight,
                    'unit' => $sales_order_item->unit,
                    'unit_price' => $sales_order_item->unit_price?:0,
                    'amount' => $sales_order_item->amount,
                    'added_by' => $sales_order_item->added_by,
                    'added_on' => $sales_order_item->added_on
                ];

                $sales_order_item_ids[] = $sales_order_item->id;
            }

            $existing_ids = array_map(function ($sales_order_item) {
                return $sales_order_item->id;
            }, $sales_order_items);

            $item_rules = $this->_get_sales_order_item_rules($sales_order->id, $existing_ids);
        } else {
            $item_rules = $this->_get_sales_order_item_rules($sales_order->id, $sales_order_item_ids);
        }


        $this->title = 'Print Preview: Sales Order No. ' . $sales_order->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_orders/preview',
        ];

        $where = [
            'id' => $sales_order->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);

       

        $data = [
            'sales_order' => $sales_order,
            'customer' => $customer,
            'sales_order_items' => $sales_order_items,
            'sales_order_item_ids' => $sales_order_item_ids,
            'sales_order_payments' => $sales_order_payments,
            'retain_items' => $retain_items
        ];
        $this->build_content('default', 'sales_orders/preview', $data);
    }

    /**
     * Get Partially Paid sales_orders
     */
    public function partially_paid()
    {
        $data = [
            'sales_orders' => $this->sales_order->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('processing');
        $this->load->model('processing_item');
        $this->load->model('processing_fee');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('sales_order');
        $this->load->model('sales_order_item');
        $this->load->model('sales_order_payment');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_sales_order_item_ids()
    {
        $sales_order_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'sales_order_item_') === 0) {
                $sales_order_item_ids[] = $value;
            }
        }

        return $sales_order_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_sales_order_item_rules($sales_order_id, $sales_order_item_ids)
    {
        $rules = [];

        foreach ($sales_order_item_ids as $sales_order_item_id) {
            // $rules[] = [
            //     'field' => 'sales_order_item_' . $sales_order_item_id,
            //     'label' => 'sales_order Item',
            //     'rules' => 'trim|callback__is_sales_order_item[' . $sales_order_id . ']',
            //     'errors' => [
            //         '_is_sales_order_item' => 'sales_order Item is not found.'
            //     ]
            // ];

            $rules[] = [
                'field' => 'item_' . $sales_order_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'actual_live_weight_' . $sales_order_item_id,
                'label' => 'actual live weight',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'unit_' . $sales_order_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]'
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id, $processing_fee_id)
    {
        $this->db->trans_begin();

        if (!$sales_order_items = $this->_get_sales_order_item_data()) {
            $this->_error = 'No sales order item added';
            return false;
        }

        if (!$sales_order_id = $this->_insert_sales_older($order_slip_id, $processing_fee_id) or !$this->_insert_sales_order_items($sales_order_id, $sales_order_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $sales_order_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_sales_order_item_data()
    {
        $data = [];
        $sales_order_item_ids = $this->_get_sales_order_item_ids();

        foreach ($sales_order_item_ids as $sales_order_item_id) {
            $data[] = [
                'id' => is_numeric($sales_order_item_id) ? $sales_order_item_id : null,
                'item_id' => $this->input->post('item_' . $sales_order_item_id),
                'actual_live_weight' => $this->input->post('actual_live_weight_' . $sales_order_item_id),
                'unit' => $this->input->post('unit_' . $sales_order_item_id),
                'unit_price' => $this->input->post('unit_price_' . $sales_order_item_id)?:0,
                'amount' => $this->input->post('amount_' . $sales_order_item_id),
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Sales order Info
     */
    public function info($sales_order_id = null)
    {
        if (!$sales_order = $this->sales_order->get_by_id($sales_order_id)) {
            http_response_code(400);
            $response = [
                'message' => 'Sales Order is not found.'
            ];
        } else {
            $response = [
                'message' => 'Sales Order is found.',
                'sales_order' => $sales_order
            ];
        }

        echo json_encode($response);
    }

    /**
     * Insert processing
     */
    protected function _insert_sales_older($order_slip_id, $processing_fee_id)
    {
        $customer_id = $this->input->post('customer');

        $sales_order_date = DateTime::createFromFormat('m/d/Y', $this->input->post('sales_order_date'));
        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $where = [
            'id' => $processing_fee_id,
            'is_deleted' => 0
        ];
        $processing_fee = $this->processing_fee->select('', $where, 1);

        $values = [
            'customer_id' => $customer_id,
            'processing_fee_id' => $processing_fee_id,
            'order_slip_id' => $order_slip_id,
            'sales_order_date' => $sales_order_date->format('Y-m-d'),

            // The triggers will be the one to add the total sales order items in the grand total and balance
            'processing_fee' => $processing_fee->grand_total,
            'grand_total' => $processing_fee->grand_total,
            'balance' => $processing_fee->grand_total,

            'remarks' => $this->input->post('remarks') ?: null,
            'total_live_arrived' => $this->input->post('total_live_arrived') ?: 0,
            'total_live_arrived_heads' => $this->input->post('total_live_arrived_heads') ?: 0,
            'total_live_arrived_kilos' => $this->input->post('total_live_arrived_kilos') ?: 0,
            'total_death_on_arrival' => $this->input->post('total_death_on_arrival') ?: 0,
            'total_death_on_arrival_heads' => $this->input->post('total_death_on_arrival_heads') ?: 0,
            'total_death_on_arrival_kilos' => $this->input->post('total_death_on_arrival_kilos') ?: 0,
            'total_death_after_arrival' => $this->input->post('total_death_after_arrival') ?: 0,
            'total_death_after_arrival_heads' => $this->input->post('total_death_after_arrival_heads') ?: 0,
            'total_death_after_arrival_kilos' => $this->input->post('total_death_after_arrival_kilos') ?: 0,
            'total_trips' => $this->input->post('total_trips') ?: 0,
            'alw' => $this->input->post('alw') ?: 0,
            'coops_loaded' => $this->input->post('coops_loaded') ?: 0,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->sales_order->insert($values);
    }

    /**
     * Generate processing Items
     */
    protected function _insert_sales_order_items($sales_order_id, $sales_order_items)
    {

        foreach ($sales_order_items as $sales_order_item) {
            $values = [
                'sales_order_id' => $sales_order_id,
                'item_id' => $sales_order_item['item_id'],
                'actual_live_weight' => $sales_order_item['actual_live_weight'],
                'unit' => $sales_order_item['unit'],
                'unit_price' => $sales_order_item['unit_price'],
                'amount' => $sales_order_item['amount'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];

            if(!$this->sales_order_item->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Generate sales_order
     */
    // protected function _generate_sales_order()
    // {
    //     $customer_id = $this->input->post('customer');

    //     $sales_order_date = DateTime::createFromFormat('m/d/Y', $this->input->post('sales_order_date'));
    //     if (!empty($this->_uploaded)) {
    //         $attachments = [];
    //         foreach ($this->_uploaded as $uploaded) {
    //             $attachments[] = $uploaded['file_name'];
    //         }
    //         $values['attachment'] = implode('🔥', $attachments);
    //     }
    //     $values = [
    //         'customer_id' => $customer_id,
    //         'sales_order_date' => $sales_order_date->format('Y-m-d'),
    //         'remarks' => $this->input->post('remarks') ?: null,
    //         'added_by' => $_SESSION['user']->id,
    //         'added_on' => date('Y-m-d H:i:s')
    //     ];

    //     if (!empty($this->_uploaded)) {
    //         $attachments = [];
    //         foreach ($this->_uploaded as $uploaded) {
    //             $attachments[] = $uploaded['file_name'];
    //         }
    //         $values['attachment'] = implode('🔥', $attachments);
    //     }

    //     return $this->sales_order->insert($values);
    // }

    /**
     * Generate sales_order Items
     */
    protected function _generate_sales_order_items($sales_order_id, $sales_order_items)
    {

        foreach ($sales_order_items as $sales_order_item) {
            $where = [
                'id' => $sales_order_item['item_id'],
                'is_deleted' => 0
            ];
            $item = $this->live_sell_item->select('', $where, 1);
            // if(($item->current_stock - (int)$sales_order_item['qty']) < 0) {
            //     $this->_error = "Can't sales_order item, it will lead to negative inventory.";
            //     return false;
            // }

            $where = [
                'id' => $sales_order_item['item_id']
            ];
            $values = [
                'updated_by' => $sales_order_item['added_by']
            ];

            if (!$this->live_sell_item->update($where, $values) ||
                !$this->sales_order_item->generate(
                    $sales_order_item['id'],
                    $sales_order_id,
                    $sales_order_item['item_id'],
                    $sales_order_item['qty'],
                    $sales_order_item['pref_alw'],
                    $sales_order_item['est_due_amount'],
                    $sales_order_item['unit'],
                    $sales_order_item['added_by']
                )
            ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($sales_order)
    {
        $this->db->trans_begin();

        $where = [
            'sales_order_id' => $sales_order->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->sales_order_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$sales_order_items = $this->_get_sales_order_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No sales order item added';
            return false;
        } elseif (!$this->_revise_sales_order($sales_order->id, $sales_order_items) || !$this->_attempt_delete_items($sales_order->id) || !$this->_insert_sales_order_items($sales_order->id, $sales_order_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($sales_order_id)
    {
        $where = [
            'sales_order_id' => $sales_order_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$this->sales_order_item->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    /**
     * Revise sales_order
     */
    protected function _revise_sales_order($sales_order_id, $sales_order_items)
    {
        $customer_id = $this->input->post('customer');

        $sales_order_date = DateTime::createFromFormat('m/d/Y', $this->input->post('sales_order_date'));
        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $values = [
            'customer_id' => $customer_id,
            'sales_order_date' => $sales_order_date->format('Y-m-d'),

            // The triggers will be the one to add the total sales order items in the grand total and balance
            // 'processing_fee' => $processing_fee->grand_total,
            // 'grand_total' => $processing_fee->grand_total,
            // 'balance' => $processing_fee->grand_total,

            'remarks' => $this->input->post('remarks') ?: null,
            'total_live_arrived' => $this->input->post('total_live_arrived') ?: 0,
            'total_live_arrived_heads' => $this->input->post('total_live_arrived_heads') ?: 0,
            'total_live_arrived_kilos' => $this->input->post('total_live_arrived_kilos') ?: 0,
            'total_death_on_arrival' => $this->input->post('total_death_on_arrival') ?: 0,
            'total_death_on_arrival_heads' => $this->input->post('total_death_on_arrival_heads') ?: 0,
            'total_death_on_arrival_kilos' => $this->input->post('total_death_on_arrival_kilos') ?: 0,
            'total_death_after_arrival' => $this->input->post('total_death_after_arrival') ?: 0,
            'total_death_after_arrival_heads' => $this->input->post('total_death_after_arrival_heads') ?: 0,
            'total_death_after_arrival_kilos' => $this->input->post('total_death_after_arrival_kilos') ?: 0,
            'total_trips' => $this->input->post('total_trips') ?: 0,
            'alw' => $this->input->post('alw') ?: 0,
            'coops_loaded' => $this->input->post('coops_loaded') ?: 0,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $where = [
            'id' => $sales_order_id
        ];

        return $this->sales_order->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($sales_order)
    {
        $where = [
            'id' => $sales_order->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->sales_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($sales_order)
    {
        $where = [
            'id' => $sales_order->id
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->sales_order->update($where, $values)) {
            $this->_error = 'server_error';
            return $is_deleted;
        }

        $where = [
            'sales_order_id' => $sales_order->id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->sales_order_item->update($where, $values)) {
            $this->_error = 'server_error';
            return $is_deleted;
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if sales_order Item Belongs to sales_order
     */
    public function _is_sales_order_item($sales_order_item_id, $sales_order_id)
    {
        if (!is_numeric($sales_order_item_id)) {
            return true;
        }

        $where = [
            'id' => $sales_order_item_id,
            'sales_order_id' => $sales_order_id
        ];

        return !empty($this->sales_order_item->select('', $where, 1));
    }

    /**
     * Callback: Check if sales_order Term is Either Cash or Check
     */
    public function _is_sales_order_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
