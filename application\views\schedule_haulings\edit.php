<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset style="overflow: hidden;">
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $customer->id === $schedule_hauling->customer_id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Farm</label>

						<select name="farm" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($farms as $farm): ?>
								<!-- <option value="<?=$farm->id;?>" <?=set_select('farm', $farm->id);?>><?=$farm->name?></option> -->
								<option value="<?=$farm->id;?>" <?=set_select('farm', $farm->id, $farm->id === $schedule_hauling->farm_id);?>><?=$farm->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('farm');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="schedule_hauling_date" class="control-label">Date of Hauling</label>

						<input type="text" name="schedule_hauling_date" class="form-control datepicker" value="<?=set_value('schedule_hauling_date', DateTime::createFromFormat('Y-m-d', $schedule_hauling->schedule_hauling_date)->format('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('schedule_hauling_date');?>
						</div>
					</div>

				    <div class="col-md-4 form-group">
						<label for="schedule_hauling_time" class="control-label">Time of Hauling</label>

						<input type="time" name="schedule_hauling_time" id="salesTime" class="form-control" value="<?=set_value('schedule_hauling_time', $schedule_hauling->schedule_hauling_time);?>">

						<div class="red-text flash-message">
							<?=form_error('schedule_hauling_time');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="driver_name" class="control-label">Name of Driver <small class="font-italic"></small></label>

						<input type="text" name="driver_name" class="form-control" value="<?=set_value('driver_name', $schedule_hauling->driver_name);?>">

						<div class="red-text flash-message">
							<?=form_error('driver_name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="helper_name" class="control-label">Name of Helper <small class="font-italic"></small></label>

						<input type="text" name="helper_name" class="form-control" value="<?=set_value('helper_name', $schedule_hauling->helper_name);?>">

						<div class="red-text flash-message">
							<?=form_error('helper_name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $schedule_hauling->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Items for Hauling</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 200px;">Item</th>

									<th class="text-center" style="width: 20%;">No. of Heads</th>

									<th class="text-center" style="width: 10%;">Supplier</th>

									<th class="text-center" style="width: 10%;">Estimated ALW</th>

									<th class="text-center" style="width: 10%;">Estimated Due Amount</th>

									<th class="text-center d-none" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($schedule_hauling_item_ids)):
									$counter = 0;
									foreach ($schedule_hauling_item_ids as $i => $schedule_hauling_item_id):
									$schedule_hauling_item = $schedule_hauling_items[$counter];
                            ?>
										<tr class="schedule_hauling_item">
											<td>
												<?php $fn_schedule_hauling_item = 'schedule_hauling_item_' . $schedule_hauling_item_id;?>
												<input type="hidden" name=<?=$fn_schedule_hauling_item?> value="<?=set_value($fn_schedule_hauling_item, $schedule_hauling_item_id);?>">

												<?php $fdn_item = 'item_' . $schedule_hauling_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$retain_items[$counter]['item_id']?>><?=$retain_items[$counter]['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $schedule_hauling_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, $retain_items[$counter]['qty']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_supplier = 'supplier_' . $schedule_hauling_item_id; ?>

												<select name="<?= $fdn_supplier; ?>" class="form-control dropsearch" data-placeholder="" style="min-width: 200px; padding-right: 0px; margin-right: 0px;">
													<?php foreach ($suppliers as $supplier): ?>
														<option value="<?= $supplier->id; ?>" <?= $supplier->id == $schedule_hauling_item->supplier_id ? $retain_items[$counter]['selected'] : ''; ?>><?= $supplier->name ?></option>
													<?php endforeach; ?>
												</select>

												<div class="red-text flash-message">
													<?= form_error($fdn_supplier); ?>
												</div>
											</td>

											<td>
												<?php $fn_est_alw = 'est_alw_' . $schedule_hauling_item_id;?>
												<input type="number" name="<?=$fn_est_alw;?>" class="form-control" value="<?=set_value($fn_est_alw, $retain_items[$counter]['est_alw']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $schedule_hauling_item_id;?>
												<input type="number" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount, $retain_items[$counter]['est_due_amount']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_due_amount);?>
												</div>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<!-- <button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('schedule_haulings')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
