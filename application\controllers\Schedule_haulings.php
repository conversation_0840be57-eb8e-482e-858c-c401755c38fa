<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Schedule_haulings extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('schedule_haulings/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review schedule_hauling Order for Approval
     */
    public function receive($schedule_hauling_id)
    {
        $schedule_hauling = $this->schedule_hauling->get_by_id($schedule_hauling_id) or show_404();

        $this->title = 'Receive schedule_hauling No. ' . $schedule_hauling->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedule_haulings/receive'
        ];

        $schedule_hauling_items = $this->schedule_hauling_item->get_by_schedule_hauling($schedule_hauling->id);

        $data = [
            'schedule_hauling'          => $schedule_hauling,
            'schedule_hauling_items'    => $schedule_hauling_items
        ];
        $this->build_content('default', 'schedule_haulings/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add schedule_hauling
     */
    public function add($purchase_order_id = null)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();
        $order_slip_id = $purchase_order->order_slip_id;
        $purchase_order_items = $this->purchase_order_item->select('', ['purchase_order_id' => $purchase_order->id, 'is_deleted' => 0]);

        $order_confirmation_items = $this->order_confirmation_item->select('', ['order_confirmation_id' => $purchase_order->order_confirmation_id, 'is_deleted' => 0]);

        foreach($purchase_order_items AS $index => $purchase_order_item) {
            $where = [
                'id' => $purchase_order_items[$index]->item_id,
                'is_deleted' => 0
            ];

            $item = $this->live_sell_item->select('', $where, 1);

            $purchase_order_items[$index]->item_name = $item->name;

            $where = [
                'id' => $order_confirmation_items[$index]->supplier_id,
                'is_deleted' => 0
            ];

            $supplier = $this->live_sell_supplier->select('', $where, 1);


            $purchase_order_items[$index]->supplier_id = $supplier?$supplier->id:"";
            $purchase_order_items[$index]->supplier_name = $supplier?$supplier->name:"";
        }

        $schedule_hauling_item_ids = $this->_get_schedule_hauling_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/schedule_haulings',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('schedule_haulings/add'), $this->_get_schedule_hauling_item_rules('', $schedule_hauling_item_ids));
        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($schedule_hauling_id = $this->_attempt_add($order_slip_id, $purchase_order_id))) {
            $new_path = FCPATH . 'assets/schedule_haulings/' . $schedule_hauling_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('schedule_haulings/manager'));
        } else {
            $this->title = 'Schedule Hauling';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/schedule_haulings/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_schedule_hauling_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->live_sell_supplier->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $farms = $this->farm->select('', $where, '', 'name');

            $data = [
                'purchase_order'                => $purchase_order,
                'purchase_order_items'          => $purchase_order_items,
                'form_error'                    => $form_error,
                'schedule_hauling_item_ids'     => $schedule_hauling_item_ids,
                'customers'                     => $customers,
                'retain_items'                  => $retain_items,
                'farms'                         => $farms,
                'suppliers'                     => $suppliers
            ];

            $this->build_content('default', 'schedule_haulings/add', $data);
        }
    }

    /**
     * Edit schedule_hauling
     */
    public function edit($schedule_hauling_id)
    {
        $schedule_hauling = $this->schedule_hauling->get_by_id($schedule_hauling_id) or show_404();

        $schedule_hauling_items = $this->schedule_hauling_item->get_by_schedule_hauling($schedule_hauling->id);
        $retain_items = $this->_get_schedule_hauling_item_data();
        $rules = $this->config->item('schedule_haulings/edit');

        if (!$schedule_hauling_item_ids = $this->_get_schedule_hauling_item_ids()) {
            foreach($schedule_hauling_items AS $schedule_hauling_item) {
                $item = $this->live_sell_item->select('', ['id' => $schedule_hauling_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $schedule_hauling_item->id,
                    'item_id' => $schedule_hauling_item->item_id,
                    'item_name' => $item?$item->name:"",
                    'qty' => $schedule_hauling_item->qty,
                    'supplier' => $schedule_hauling_item->supplier,
                    'est_alw' => $schedule_hauling_item->est_alw,
                    'est_due_amount' => $schedule_hauling_item->est_due_amount,
                ];

                $schedule_hauling_item_ids[] = $schedule_hauling_item->id;
            }

            $existing_ids = array_map(function ($schedule_hauling_item) {
                return $schedule_hauling_item->id;
            }, $schedule_hauling_items);

            $item_rules = $this->_get_schedule_hauling_item_rules($schedule_hauling->id, $existing_ids);
        } else {
            $item_rules = $this->_get_schedule_hauling_item_rules($schedule_hauling->id, $schedule_hauling_item_ids);
        }

        $rules = array_merge($this->config->item('schedule_haulings/edit'), $item_rules);
        if ($this->_validate_form($rules) && $this->_attempt_edit($schedule_hauling)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('schedule_haulings/preview/' . $schedule_hauling->id));
            } else {
                redirect(site_url('schedule_haulings/manager'));
            }
        } else {
            $this->title = 'Edit Schedule Hauling';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/schedule_haulings/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->live_sell_supplier->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $farms = $this->farm->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'schedule_hauling' => $schedule_hauling,
                'schedule_hauling_items' => $schedule_hauling_items,
                'schedule_hauling_item_ids' => $schedule_hauling_item_ids,
                'retain_items' => $retain_items,
                'farms' => $farms,
            ];

            $this->build_content('default', 'schedule_haulings/edit', $data);
        }
    }

    /**
     * Approve schedule_hauling
     */
    public function receive_schedule_hauling($schedule_hauling_id)
    {
        $where = [
            'id' => $schedule_hauling_id,
            'is_deleted' => 0
        ];
        if (!$schedule_hauling = $this->schedule_hauling->select('', $where, 1)) {
            $response = [
                'message' => 'schedule_hauling is not found.'
            ];
        } elseif (!$this->_attempt_receive($schedule_hauling)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'schedule_hauling is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($schedule_hauling)
    {
        $where = [
            'id' => $schedule_hauling->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->schedule_hauling->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete schedule_hauling
     */
    public function delete($schedule_hauling_id)
    {
        $where = [
            'id' => $schedule_hauling_id,
            'is_deleted' => 0
        ];
        if (!$schedule_hauling = $this->schedule_hauling->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Schedule Hauling is not found.'
            ];
        } elseif (!$this->_attempt_delete($schedule_hauling)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Schedule Hauling is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * schedule_haulings
     */
    public function manager()
    {
        $this->title = "Schedule Hauling";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedule_haulings/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'schedule_haulings/manager', $data);
    }

    /**
     * schedule_hauling Print Preview
     */
    public function preview($schedule_hauling_id)
    {
        $schedule_hauling = $this->schedule_hauling->get_by_id($schedule_hauling_id) or show_404();
        $schedule_hauling_items = $this->schedule_hauling_item->get_by_schedule_hauling($schedule_hauling_id);

        if (!$schedule_hauling_item_ids = $this->_get_schedule_hauling_item_ids()) {
            foreach($schedule_hauling_items AS $schedule_hauling_item) {
                $item = $this->live_sell_item->select('', ['id' => $schedule_hauling_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $schedule_hauling_item->id,
                    'item_id' => $schedule_hauling_item->item_id,
                    'item_name' => $item?$item->name:"",
                    'qty' => $schedule_hauling_item->qty,
                    'supplier_id' => $schedule_hauling_item->supplier_id,
                    'supplier_name' => $schedule_hauling_item->supplier_name,
                    'est_alw' => $schedule_hauling_item->est_alw,
                    'est_due_amount' => $schedule_hauling_item->est_due_amount,
                ];
                $schedule_hauling_item_ids[] = $schedule_hauling_item->id;
            }

            $existing_ids = array_map(function ($schedule_hauling_item) {
                return $schedule_hauling_item->id;
            }, $schedule_hauling_items);

            $item_rules = $this->_get_schedule_hauling_item_rules($schedule_hauling->id, $existing_ids);
        } else {
            $item_rules = $this->_get_schedule_hauling_item_rules($schedule_hauling->id, $schedule_hauling_item_ids);
        }

        $this->title = 'Print Preview: Schedule Hauling No. ' . $schedule_hauling->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedule_haulings/preview',
        ];

        $where = [
            'id' => $schedule_hauling->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);


        $data = [
            'schedule_hauling' => $schedule_hauling,
            'customer' => $customer,
            'schedule_hauling_items' => $schedule_hauling_items,
            'schedule_hauling_item_ids' => $schedule_hauling_item_ids,
            'retain_items' => $retain_items
        ];
        $this->build_content('default', 'schedule_haulings/preview', $data);
    }

    /**
     * Get Info for schedule_hauling
     */
    public function info($schedule_hauling_id = null)
    {
        $data = [
            'schedule_hauling' => $this->schedule_hauling->get_by_id($schedule_hauling_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid schedule_haulings
     */
    public function partially_paid()
    {
        $data = [
            'schedule_haulings' => $this->schedule_hauling->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('farm');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('purchase_order');
        $this->load->model('purchase_order_item');
        $this->load->model('order_confirmation');
        $this->load->model('order_confirmation_item');
        $this->load->model('schedule_hauling');
        $this->load->model('schedule_hauling_item');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');
        $this->load->model('live_sell_supplier');
        
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_schedule_hauling_item_ids()
    {
        $schedule_hauling_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'schedule_hauling_item_') === 0) {
                $schedule_hauling_item_ids[] = $value;
            }
        }

        return $schedule_hauling_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_schedule_hauling_item_rules($schedule_hauling_id, $schedule_hauling_item_ids)
    {
        $rules = [];

        foreach ($schedule_hauling_item_ids as $schedule_hauling_item_id) {
            $rules[] = [
                'field' => 'item_' . $schedule_hauling_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'qty_' . $schedule_hauling_item_id,
                'label' => 'quantity',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'est_alw_' . $schedule_hauling_item_id,
                'label' => 'estimated allowance',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'est_due_amount_' . $schedule_hauling_item_id,
                'label' => 'estimated due amount',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id, $purchase_order_id)
    {
        $this->db->trans_begin();

        if (!$schedule_hauling_items = $this->_get_schedule_hauling_item_data()) {
            $this->_error = 'No Schedule Hauling Item added';
            return false;
        }

        if (!$schedule_hauling_id = $this->_generate_schedule_hauling($order_slip_id, $purchase_order_id) or !$this->_insert_schedule_hauling_items($schedule_hauling_id, $schedule_hauling_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $schedule_hauling_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_schedule_hauling_item_data()
    {
        $data = [];
        $schedule_hauling_item_ids = $this->_get_schedule_hauling_item_ids();

        foreach ($schedule_hauling_item_ids as $schedule_hauling_item_id) {
            $item_id = $this->input->post('item_' . $schedule_hauling_item_id);
            $qty = $this->input->post('qty_' . $schedule_hauling_item_id);
            $supplier_id = $this->input->post('supplier_id_' . $schedule_hauling_item_id);
            $est_alw = $this->input->post('est_alw_' . $schedule_hauling_item_id);
            $est_due_amount = $this->input->post('est_due_amount_' . $schedule_hauling_item_id);
            $added_by = $_SESSION['user']->id;

            $data[] = [
                'id' => is_numeric($schedule_hauling_item_id) ? $schedule_hauling_item_id : null,
                'item_id' => $item_id,
                'qty' => $qty,
                'supplier_id' => $supplier_id,
                'est_alw' => $est_alw,
                'est_due_amount' => $est_due_amount,
                'added_by' => $added_by,
                'added_on' => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Generate schedule_hauling
     */
    protected function _generate_schedule_hauling($order_slip_id, $purchase_order_id)
    {
        $customer_id = $this->input->post('customer');

        $schedule_hauling_date = DateTime::createFromFormat('m/d/Y', $this->input->post('schedule_hauling_date'));
        $schedule_hauling_time = DateTime::createFromFormat('H:i:s', $this->input->post('schedule_hauling_time'));

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $values = [
            'customer_id' => $customer_id,
            'purchase_order_id' => $purchase_order_id,
            'farm_id' => $this->input->post('farm') ?: null,
            'order_slip_id' => $order_slip_id,
            'schedule_hauling_date' => $schedule_hauling_date->format('Y-m-d'),
            'schedule_hauling_time' => $schedule_hauling_time->format('H:i:s'),
            'driver_name' => $this->input->post('driver_name') ?: null,
            'helper_name' => $this->input->post('helper_name') ?: null,
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->schedule_hauling->insert($values);
    }

    /**
     * Generate schedule_hauling Items
     */
    protected function _generate_schedule_hauling_items($schedule_hauling_id, $schedule_hauling_items)
    {

        foreach ($schedule_hauling_items as $schedule_hauling_item) {
            $where = [
                'id' => $schedule_hauling_item['item_id'],
                'is_deleted' => 0
            ];
            $item = $this->live_sell_item->select('', $where, 1);
            // if(($item->current_stock - (int)$schedule_hauling_item['qty']) < 0) {
            //     $this->_error = "Can't schedule_hauling item, it will lead to negative inventory.";
            //     return false;
            // }

            $where = [
                'id' => $schedule_hauling_item['item_id']
            ];
            $values = [
                'updated_by' => $schedule_hauling_item['added_by']
            ];

            if (!$this->live_sell_item->update($where, $values) ||
                !$this->schedule_hauling_item->generate(
                    $schedule_hauling_item['id'],
                    $schedule_hauling_id,
                    $schedule_hauling_item['item_id'],
                    $schedule_hauling_item['qty'],
                    $schedule_hauling_item['supplier_id'],
                    $schedule_hauling_item['est_alw'],
                    $schedule_hauling_item['est_due_amount'],
                    $schedule_hauling_item['added_by']
                )
            ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Insert schedule hauling Items
     */
    protected function _insert_schedule_hauling_items($schedule_hauling_id, $schedule_hauling_items)
    {
        foreach ($schedule_hauling_items as $schedule_hauling_item) {
            $values = [
                'schedule_hauling_id' => $schedule_hauling_id,
                'item_id' => $schedule_hauling_item['item_id'],
                'qty' => $schedule_hauling_item['qty'],
                'supplier_id' => $schedule_hauling_item['supplier_id'],
                'est_alw' => $schedule_hauling_item['est_alw'],
                'est_due_amount' => $schedule_hauling_item['est_due_amount'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];

            if (!$this->schedule_hauling_item->insert($values)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($schedule_hauling)
    {
        $this->db->trans_begin();

        $where = [
            'schedule_hauling_id' => $schedule_hauling->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->schedule_hauling_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$schedule_hauling_items = $this->_get_schedule_hauling_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Schedule Hauling Item added';
            return false;
        } elseif(!$this->_attempt_delete_items($schedule_hauling->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No Schedule Hauling Item added';
            return false;
        } elseif (!$this->_revise_schedule_hauling($schedule_hauling->id, $schedule_hauling_items) || !$this->_insert_schedule_hauling_items($schedule_hauling->id, $schedule_hauling_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise schedule_hauling
     */
    protected function _revise_schedule_hauling($schedule_hauling_id, $schedule_hauling_items)
    {
        $schedule_hauling_date = DateTime::createFromFormat('m/d/Y', $this->input->post('schedule_hauling_date'));
        $schedule_hauling_time = DateTime::createFromFormat('H:i:s', $this->input->post('schedule_hauling_time'));
        // $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($schedule_hauling_items, function ($current_total, $schedule_hauling_item) {
            if (isset($schedule_hauling_item['amount'])) {
                return $current_total + $schedule_hauling_item['amount'];
            }
            return $current_total;
        });
        $discount = $this->input->post('discount') ?: null;

        $where = [
            
            'id' => $schedule_hauling_id
        ];

        $customer_id = $this->input->post('customer');
        
        $values = [
            'customer_id' => $customer_id,
            'schedule_hauling_date' => $schedule_hauling_date->format('Y-m-d'),
            'schedule_hauling_time' => $schedule_hauling_time->format('H:i:s'),
            'driver_name' => $this->input->post('driver_name') ?: null,
            'helper_name' => $this->input->post('helper_name') ?: null,
            'remarks' => $this->input->post('remarks') ?: null,
            'remarks' => $this->input->post('remarks') ?: null,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->schedule_hauling->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($schedule_hauling)
    {
        $where = [
            'id' => $schedule_hauling->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->schedule_hauling->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($schedule_hauling)
    {
        $where = [
            'id' => $schedule_hauling->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->schedule_hauling->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($schedule_hauling_id)
    {
        $where = [
            'schedule_hauling_id' => $schedule_hauling_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->schedule_hauling_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if schedule_hauling Item Belongs to schedule_hauling
     */
    public function _is_schedule_hauling_item($schedule_hauling_item_id, $schedule_hauling_id)
    {
        if (!is_numeric($schedule_hauling_item_id)) {
            return true;
        }

        $where = [
            'id' => $schedule_hauling_item_id,
            'schedule_hauling_id' => $schedule_hauling_id
        ];

        return !empty($this->schedule_hauling_item->select('', $where, 1));
    }

    /**
     * Callback: Check if schedule_hauling Term is Either Cash or Check
     */
    public function _is_schedule_hauling_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
