!function(t){"use strict";function e(e){var a=t(e),n=a.find("thead th"),o=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return o}function a(e,a){return t.ajax({url:BASE_URI+"eod/get_attachments/"+e+"/"+a,type:"GET",dataType:"json"})}function n(e,a){return t.ajax({url:BASE_URI+"eod/delete_attachment/"+e+"/"+a,type:"POST",dataType:"json"})}function o(e,a,o){return new Promise((l,r)=>{function c(){let i="<div>";if(a&&a.length>0){i+='<div class="old-attachments">',i+="<h5>Existing Attachments:</h5>",i+="<ul>";let t=0;for(let n of a)t++,i+=`\n\t\t\t\t\t\t\t<li style="display: flex; align-items: center;">\n\t\t\t\t\t\t\t\t• <a href="${BASE_URI}assets/eod/eod_p/${e}/${n.file_name}" target="_blank" style="flex-grow: 1;">Attachment ${t}</a>\n\t\t\t\t\t\t\t\t<button type="button" class="delete-attachment" data-id="${n.id}" style="background: none; border: none; color: red; cursor: pointer;">\n\t\t\t\t\t\t\t\t\t<i class="fa fa-trash"></i>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</li>`;i+="</ul>",i+="</div>"}i+='<div class="new-attachments">',i+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>',i+="</div>",i+="</div>",alertify.confirm(i,function(){let t=document.getElementById("attachments").files;l({files:t})},function(){r("Upload Canceled")}).setting({reverseButtons:!0,closable:!1,title:"Add Attachment"}).show(),t(document).off("click",".delete-attachment"),t(document).on("click",".delete-attachment",function(t){let e=t.currentTarget.getAttribute("data-id");n(e,"eod_p").done(function(){a=a.filter(t=>t.id!=e),o.ajax.reload(null,!1),c()}).fail(function(t){let e=JSON.parse(t.responseText);alertify.error(e.message)})})}c()})}function l(e,a){var n=new FormData;n.append("table","eod_p"),n.append("id",e);for(var o=0;o<a.length;o++)n.append("attachments[]",a[o]);return t.ajax({url:BASE_URI+"eod/upload_attachment/",type:"POST",data:n,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Files Uploaded."}))}t(function(){var n=e(".tableA"),r=[];t(".tableA").on("click",".action-add",function(t){var e=t.currentTarget.getAttribute("data-id");r=[],a(e,"eod_p").then(function(t){return t.data.forEach(function(t){t.text=t.file_name,r.push(t)}),o(e,r,n)}).then(({files:t})=>l(e,t)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})})}(jQuery);