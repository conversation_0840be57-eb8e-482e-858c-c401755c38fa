<?php
class Paid_invoice extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'paid_invoice';
        parent::__construct();
    }

    /**
     * Get Paid Invoices Between Dates
     */
    public function get_between_dates($from, $to)
    {
        $sql = <<<EOT
SELECT paid_invoice.*,
    payment.payment_date
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
WHERE payment.payment_date BETWEEN ? AND ?
    AND paid_invoice.is_deleted = 0
EOT;
        $binds = [$from, $to];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Paid Invoices by Payment ID
     */
    public function get_invoice_by_payment($payment_id)
    {
        $sql = <<<EOT
SELECT paid_invoice.*,
    payment.payment_date,
    invoice.invoice_date,
    invoice.invoice_no AS invoice_no,
    customer.name AS customer
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
LEFT JOIN customer ON customer.id = invoice.customer_id
WHERE paid_invoice.payment_id = ?
    AND paid_invoice.is_deleted = 0
EOT;
        $binds = [$payment_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Paid Cash Bonds by Payment ID
     */
    public function get_cash_bond_by_payment($payment_id)
    {
        $sql = <<<EOT
SELECT paid_invoice.*,
    payment.payment_date,
    cash_bond.cb_date,
    CONCAT('CB - ', cash_bond.id) AS invoice_no,
    customer.name AS customer
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
LEFT JOIN cash_bond ON cash_bond.id = paid_invoice.invoice_id
LEFT JOIN customer ON customer.id = cash_bond.customer_id
WHERE paid_invoice.payment_id = ?
    AND paid_invoice.is_deleted = 0
EOT;
        $binds = [$payment_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Paid Invoices by Payment ID
     */
    public function get_by_invoice_payment_id($payment_id)
    {
        $sql = <<<EOT
SELECT 
    paid_invoice.*, 
    CASE
        WHEN COUNT(invoice.invoice_no) > 1 THEN GROUP_CONCAT(CONCAT_WS(', ', invoice.invoice_no))
        ELSE MAX(invoice.invoice_no)
    END AS concatenated_invoice_no,
    invoice.customer_name,
    invoice.customer_tin,
    invoice.customer_address
FROM paid_invoice
LEFT JOIN (
    SELECT 
        invoice.id AS id_invoice, 
        invoice.invoice_no AS invoice_no, 
        customer.name AS customer_name,
        COALESCE(customer.tin_number, null) AS customer_tin,
        COALESCE(customer.address, null) AS customer_address
    FROM invoice
    LEFT JOIN customer ON customer.id = invoice.customer_id
    WHERE invoice.is_deleted = 0
) AS invoice ON invoice.id_invoice = paid_invoice.invoice_id
WHERE paid_invoice.payment_id = ?
    AND paid_invoice.is_deleted = 0
GROUP BY paid_invoice.payment_id
EOT;
        $binds = [$payment_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Paid Invoices by Payment ID
     */
    public function get_by_cash_bond_payment_id($payment_id)
    {
        $sql = <<<EOT
SELECT 
    paid_invoice.*, 
    CONCAT('CB - ', invoice.invoice_id) AS concatenated_invoice_no,
    invoice.customer_name,
    invoice.customer_tin,
    invoice.customer_address
FROM paid_invoice
LEFT JOIN (
    SELECT 
        cash_bond.id AS invoice_id,
        customer.name AS customer_name,
        COALESCE(customer.tin_number, null) AS customer_tin,
        COALESCE(customer.address, null) AS customer_address
    FROM cash_bond
    LEFT JOIN customer ON customer.id = cash_bond.customer_id
    WHERE cash_bond.is_deleted = 0
) AS invoice ON invoice.invoice_id = paid_invoice.invoice_id
WHERE paid_invoice.payment_id = ?
    AND paid_invoice.is_deleted = 0
GROUP BY paid_invoice.payment_id
EOT;
        $binds = [$payment_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get AR Paid Invoices by Invoice ID
     */
    public function get_ar_payment($invoice_id)
    {
        $sql = <<<EOT
SELECT 
    SUM(paid_invoice.amount) AS paid_amount
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
WHERE paid_invoice.invoice_id = ?
    AND paid_invoice.is_deleted = 0
    AND payment.is_deleted = 0
    AND ((payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL))
EOT;
        $binds = [$invoice_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res[0]->paid_amount;
        } else {
            return 0;
        }
    }

    /**
     * Get PR Paid Invoices by Invoice ID
     */
    public function get_pr_payment($invoice_id)
    {
        $sql = <<<EOT
SELECT 
    SUM(paid_invoice.amount) AS paid_amount
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
WHERE paid_invoice.invoice_id = ?
    AND paid_invoice.is_deleted = 0
    AND ((payment.ar_no IS NOT NULL AND payment.pr_no IS NOT NULL) OR (payment.type = 'cash' AND payment.pr_no IS NOT NULL))
EOT;
        $binds = [$invoice_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res[0]->paid_amount;
        } else {
            return 0;
        }
    }

    /**
     * Get Payment IDS by Invoice ID
     */
    public function get_payment_ids_by_invoice($invoice_id)
    {
        $sql = <<<EOT
SELECT 
    DISTINCT payment_id
FROM paid_invoice
LEFT JOIN payment ON payment.id = paid_invoice.payment_id
WHERE paid_invoice.invoice_id = ?
    AND paid_invoice.is_deleted = 0
EOT;
        $binds = [$invoice_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res;
        } else {
            return false;
        }
    }

    /**
     * Get Payment IDS by Invoice ID
     */
    public function get_invoice_details_by_payment_id($payment_id)
    {
        $sql = <<<EOT
SELECT *
FROM (
    SELECT 
        paid_invoice.id,
        paid_invoice.payment_id,
        paid_invoice.invoice_id,
        paid_invoice.amount,
        paid_invoice.for_audit,
        invoice.total AS invoice_total,
        invoice.paid_amount,
        paid_invoice.doc_type
    FROM paid_invoice
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    WHERE paid_invoice.payment_id = ?
        AND paid_invoice.doc_type = "invoice"
        AND paid_invoice.is_deleted = 0

    UNION ALL

    SELECT 
        paid_invoice.id,
        paid_invoice.payment_id,
        paid_invoice.invoice_id,
        paid_invoice.amount,
        paid_invoice.for_audit,
        cash_bond.grand_total AS invoice_total,
        cash_bond.paid_amount,
        paid_invoice.doc_type
    FROM paid_invoice
    LEFT JOIN cash_bond ON cash_bond.id = paid_invoice.invoice_id
    WHERE paid_invoice.payment_id = ?
        AND paid_invoice.doc_type = "cash_bond"
        AND paid_invoice.is_deleted = 0
) AS payments
EOT;
        $binds = [$payment_id, $payment_id];

        $res = $this->advanced_query($sql, $binds);

        if(!empty($res)) {
            return $res;
        } else {
            return false;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
