<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Giblet Model
 */
class Giblet extends MYT_Model
{
    protected $table = 'giblets';
    protected $primary_key = 'id';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Create the giblets table if it doesn't exist
     */
    public function create_giblets_table()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `giblets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sku` varchar(255) NOT NULL,
            `size` varchar(255) NOT NULL,
            `range` varchar(255) NOT NULL,
            `no` varchar(50) NULL,
            `pcs` int(11) NULL,
            `kgs` decimal(10,2) DEFAULT NULL,
            `added_on` datetime NOT NULL,
            `added_by` int(11) NOT NULL,
            `is_deleted` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id`),
            <PERSON>EY `sku` (`sku`),
            KEY `is_deleted` (`is_deleted`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        return $this->db->query($sql);
    }

    /**
     * Get all giblets entries
     */
    public function get_all_giblets($where = [], $limit = null, $order_by = null)
    {
        $this->db->select('*')
                 ->from($this->table)
                 ->where('is_deleted', 0);

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($order_by) {
            $this->db->order_by($order_by);
        } else {
            $this->db->order_by('added_on DESC');
        }

        if ($limit) {
            $this->db->limit($limit);
        }

        return $this->db->get()->result();
    }

    /**
     * Get giblets by ID
     */
    public function get_giblets_by_id($id)
    {
        return $this->db->where('id', $id)
                        ->where('is_deleted', 0)
                        ->get($this->table)
                        ->row();
    }

    /**
     * Check if SKU already exists
     */
    public function exists($sku, $exclude_id = null)
    {
        $this->db->where('sku', $sku);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Insert new giblets entry
     */
    public function insert_giblets($data)
    {
        $data['added_on'] = date('Y-m-d H:i:s');
        $data['added_by'] = $_SESSION['user']->id ?? 1;
        $data['is_deleted'] = 0;
        
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update giblets entry
     */
    public function update_giblets($id, $data)
    {
        return $this->db->where('id', $id)
                        ->update($this->table, $data);
    }

    /**
     * Soft delete giblets entry
     */
    public function soft_delete_giblets($id, $deleted_by = null)
    {
        return $this->db->where('id', $id)
                        ->update($this->table, [
                            'is_deleted' => 1,
                            'deleted_on' => date('Y-m-d H:i:s'),
                            'deleted_by' => $deleted_by ?? ($_SESSION['user']->id ?? 1)
                        ]);
    }

    /**
     * Get giblets statistics
     */
    public function get_statistics()
    {
        $stats = [];
        $stats['total_entries'] = $this->db->where('is_deleted', 0)->count_all_results($this->table);
        $stats['recent_entries'] = $this->db->where('is_deleted', 0)
                                           ->where('added_on >=', date('Y-m-d H:i:s', strtotime('-7 days')))
                                           ->count_all_results($this->table);
        return $stats;
    }

    /**
     * Search giblets entries
     */
    public function search_giblets($search_term)
    {
        $this->db->select('*')
                 ->from($this->table)
                 ->where('is_deleted', 0)
                 ->group_start()
                 ->like('sku', $search_term)
                 ->or_like('by', $search_term)
                 ->or_like('no', $search_term)
                 ->group_end()
                 ->order_by('added_on DESC');
        return $this->db->get()->result();
    }

    /**
     * Get giblets entries for export
     */
    public function get_giblets_for_export($where = [])
    {
        $this->db->select('sku, by, no, pcs, kgs, added_on')
                 ->from($this->table)
                 ->where('is_deleted', 0);
        if (!empty($where)) {
            $this->db->where($where);
        }
        $this->db->order_by('added_on DESC');
        return $this->db->get()->result();
    }
} 