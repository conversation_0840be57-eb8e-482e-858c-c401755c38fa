!function(e){"use strict";function r(r){var t=e(r),a=t.find("thead th"),n=t.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-right",targets:a.length-3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+t.attr("data-package")});return n}function t(r,t){e.when(a(r)).then(function(){var t=e('button.action-approve-item[data-id="'+r+'"]');t.closest("td").html(""),location.reload()},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function a(r){return e.ajax({url:BASE_URI+"purchase_orders/approve_item/"+r,type:"POST",dataType:"json"})}function n(r,t){e.when(o(r)).then(function(){var t=e('button.action-approve-item[data-id="'+r+'"]');t.closest("tr.purchase_order_item").remove(),location.reload()},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function o(r){return e.ajax({url:BASE_URI+"purchase_orders/disapprove_item/"+r,type:"POST",dataType:"json"})}function i(r,t){e.when(c(r)).then(function(){location.reload()},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function c(r){return e.ajax({url:BASE_URI+"purchase_orders/pending_item/"+r,type:"POST",dataType:"json"})}function u(r){e.when(s(r)).then(function(){window.location.href=BASE_URI+"purchase_orders/manager/"},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function s(r){return e.ajax({url:BASE_URI+"purchase_orders/mark_approved/"+r,type:"POST",dataType:"json"})}function d(r){e.when(p(r)).then(function(){window.location.href=BASE_URI+"purchase_orders/manager/"},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function p(r){return e.ajax({url:BASE_URI+"purchase_orders/mark_for_approval/"+r,type:"POST",dataType:"json"})}e(function(){var a=r(".tableA");e(".item-table").on("click",".action-approve-item",function(r){var n=e(r.currentTarget).closest("tr.purchase_order_item").find("td"),o=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to approve "+o+"?",function(){var e=r.currentTarget.getAttribute("data-id");t(e,a)}).setHeader("<em>Approve Purchase Order Item</em>")}).on("click",".action-disapprove-item",function(r){var t=e(r.currentTarget).closest("tr.purchase_order_item").find("td"),o=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to disapprove "+o+"?",function(){var e=r.currentTarget.getAttribute("data-id");n(e,a)}).setHeader("<em>Disapprove Purchase Order Item</em>")}).on("click",".action-pending-item",function(r){var t=e(r.currentTarget).closest("tr.purchase_order_item").find("td"),n=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to return to pending "+n+"?",function(){var e=r.currentTarget.getAttribute("data-id");i(e,a)}).setHeader("<em>Pending Purchase Order Item</em>")}),e(".action-approve-purchase_order").click(function(e){var r=e.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to approve to print Purchase Order No. "+r+"?",function(){u(r)}).setHeader("<em>Approve to Print Purchase Order</em>")}),e(".action-forward-verification").click(function(e){var r=e.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to forward for verification Purchase Order No. "+r+"?",function(){d(r)}).setHeader("<em>Forward for Verfication Purchase Order</em>")})})}(jQuery);