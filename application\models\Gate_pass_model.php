<?php
class Gate_pass_model extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'gate_pass';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($gate_pass_id)
    {
        $sql = <<<EOT
SELECT 
    gate_pass.*,
FROM gate_pass
WHERE gate_pass.is_deleted = 0
    AND gate_pass.id = ?
EOT;
        $binds = [$gate_pass_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */