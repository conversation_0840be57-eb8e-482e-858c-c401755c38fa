<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $schedule_hauling->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="processing_date" class="control-label">Production Date</label>

						<input type="text" name="processing_date" class="form-control datepicker" value="<?=set_value('processing_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('processing_date');?>
						</div>
					</div>

	            	<h5 class="crud-subtitle"><span>Efficiency Report</span></h5>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived" class="control-label">Total Live Arrived <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived" class="form-control" value="<?=set_value('total_live_arrived');?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival" class="control-label">Total Death on Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival" class="form-control" value="<?=set_value('total_death_on_arrival');?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival" class="control-label">Total Death after Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival" class="form-control" value="<?=set_value('total_death_after_arrival');?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="la_in_kilos" class="control-label">LA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="la_in_kilos" class="form-control" value="<?=set_value('la_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('la_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="doa_in_kilos" class="control-label">DOA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="doa_in_kilos" class="form-control" value="<?=set_value('doa_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('doa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="daa_in_kilos" class="control-label">DAA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="daa_in_kilos" class="form-control" value="<?=set_value('daa_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('daa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_to_be_processed" class="control-label">Total to be Processed <small class="font-italic"></small></label>

						<input type="number" name="total_to_be_processed" class="form-control" value="<?=set_value('total_to_be_processed');?>">

						<div class="red-text flash-message">
							<?=form_error('total_to_be_processed');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="lp_in_kilos" class="control-label">LP in Kilos <small class="font-italic"></small></label>

						<input type="number" name="lp_in_kilos" class="form-control" value="<?=set_value('lp_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('lp_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_trips" class="control-label">Total Trips <small class="font-italic"></small></label>

						<input type="number" name="total_trips" class="form-control" value="<?=set_value('total_trips');?>">

						<div class="red-text flash-message">
							<?=form_error('total_trips');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="alw" class="control-label">ALW <small class="font-italic"></small></label>

						<input type="number" name="alw" class="form-control" value="<?=set_value('alw');?>">

						<div class="red-text flash-message">
							<?=form_error('alw');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="coops_loaded" class="control-label">Coops Loaded <small class="font-italic"></small></label>

						<input type="number" name="coops_loaded" class="form-control" value="<?=set_value('coops_loaded');?>">

						<div class="red-text flash-message">
							<?=form_error('coops_loaded');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>

									<th class="text-center" style="width: 10%;">Qty</th>

									<th class="text-center" style="width: 10%;">Unit</th>

									<th class="text-center" style="width: 10%;">Unit Price</th>

									<th class="text-center" style="width: 10%;">Assumed Amount</th>

									<th class="text-center" style="width: 10%;">Assumed ALW</th>

									<th class="text-center" style="width: 20%;">Account Name</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcassitems)):
                                	$counter = 0;
                                    foreach ($carcassitems as $i => $carcassitem):
                                    $carcass_id = $carcassitem->id;
                            ?>
										<tr class="purchase_order_item">
											<td>
												<?php $fn_purchase_order_item = 'carcass_' . $carcass_id;?>
												<input type="hidden" name=<?=$fn_purchase_order_item?> value="<?=set_value($fn_purchase_order_item, $carcass_id);?>">

												<?php $fdn_item = 'item_' . $carcass_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$carcassitem->item_id?>><?=$carcassitem->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcass_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_account_name = 'account_name_' . $carcass_id;?>

												<select name="<?=$fdn_account_name;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($accounts AS $account):?>
														<option value=<?=$account->id?>><?=$account->account_name?></option>
													<?php endforeach;?>
												</select>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

<!--             	<h5 class="crud-subtitle"><span>Total Carcass</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center">SKU</th>

									<th class="text-center">Size Range</th>

									<th class="text-center">By</th>

									<th class="text-center"></th>

									<td colspan="6" style="padding: 0px; width: 1500px;">
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 86px; height: 50px;">Item</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 119px; height: 50px;">Crates</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 119px; height: 50px;">Heads</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 119px; height: 50px;">Kilos</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 119px; height: 50px;">Recover</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 69px; height: 50px;">Action</div>
									</td>
								</tr>
							</thead>

							<tbody>
								<tr>
									<td class="text-center font-weight-bold pb-2">OS</td>
									<td>1.5kg above</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">RS2</td>
									<td>1.2kg-1.4kg</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">RS1</td>
									<td>1.1kg-1.2kg</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">LS</td>
									<td>910g-1.05kg</td>
									<td class="text-center pt-2">20</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">US</td>
									<td>600g-800g below</td>
									<td class="text-center pt-2">20</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">FCB</td>
									<td>Open Size</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div> -->

<!--             	<h5 class="crud-subtitle"><span>Total Incidental</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center">SKU</th>

									<th class="text-center">Size Range</th>

									<th class="text-center">By</th>

									<th class="text-center"></th>

									<td colspan="6" style="padding: 0px; width: 1500px;">
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 86px; height: 50px;">Item</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 89px; height: 50px;">Crates</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 89px; height: 50px;">Heads</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 89px; height: 50px;">Kilos</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 89px; height: 50px;">Recover</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 69px; height: 50px;">Action</div>
									</td>
								</tr>
							</thead>

							<tbody>
								<tr>
									<td class="text-center font-weight-bold pb-2">EMANCIATED</td>
									<td>Growing Defect (Condemnation)</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">MANGLED</td>
									<td>Growing Defect (Condemnantion)</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div> -->

<!--             	<h5 class="crud-subtitle"><span>Total Giblets</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center">SKU</th>

									<th class="text-center">Size Range</th>

									<th class="text-center">By</th>

									<th class="text-center"></th>

									<td colspan="6" style="padding: 0px; width: 1500px;">
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 86px; height: 50px;">Item</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Crates</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Heads</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Kilos</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Recover</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 69px; height: 50px;">Action</div>
									</td>
								</tr>
							</thead>

							<tbody>
								<tr>
									<td class="text-center font-weight-bold pb-2">LVR</td>
									<td>1 kg</td>
									<td class="text-center pt-2">20</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">GZD</td>
									<td>1 kg</td>
									<td class="text-center pt-2">20</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div> -->

<!--             	<h5 class="crud-subtitle"><span>Total Byproducts</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center">SKU</th>

									<th class="text-center">Size Range</th>

									<th class="text-center">By</th>

									<th class="text-center"></th>

									<td colspan="6" style="padding: 0px; width: 1500px;">
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 86px; height: 50px;">Item</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Crates</div>
										<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Heads</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Kilos</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 121px; height: 50px;">Recover</div>
											<div class="float-left text-center border border-secondary font-weight-bold pt-3" style="width: 68px; height: 50px;">Action</div>
									</td>
								</tr>
							</thead>

							<tbody>
								<tr>
									<td class="text-center font-weight-bold pb-2">CHD</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">CFT</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">INT</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">PRN</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">BLD</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<td class="text-center font-weight-bold pb-2">FAT</td>
									<td>-</td>
									<td class="text-center pt-2">15</td>
									<td>
										<div class="text-center pt-2"><button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button></div>
									</td>
									<td colspan="6" style="padding: 0px;">
										<table class="sub-item-table" width="100%">
										<tbody class="sub-items">

										</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div> -->

				<div>
<!-- 				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center">Item</th>

									<th class="text-center">No. of Heads</th>

									<th class="text-center">Supplier</th>

									<th class="text-center">Estimated ALW</th>

									<th class="text-center">Estimated Due Amount</th>

									<th class="text-center">Action</th>
								</tr>
							</thead>

							<tbody class="sub-items">
							<?php
                                if (!empty($order_slip_items)):
                                	$counter = 0;
                                    foreach ($order_slip_items as $i => $order_slip_item):
                                    $carcassitem_id = $order_slip_item->id;
                            ?>
										<tr class="carcassitem">
											<td>
												<?php $fn_carcassitem = 'carcassitem_' . $carcassitem_id;?>
												<input type="hidden" name=<?=$fn_carcassitem?> value="<?=set_value($fn_carcassitem, $carcassitem_id);?>">

												<?php $fdn_item = 'item_' . $carcassitem_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$order_slip_item->item_id?>><?=$order_slip_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fdn_supplier = 'supplier_' . $carcassitem_id;?>

												<select name="<?=$fdn_supplier;?>" class="form-control dropsearch" data-placeholder="">
													<?php foreach ($customers as $customer): ?>
														<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $order_slip->customer_id === $customer->id);?>><?=$customer->name?></option>
													<?php endforeach; ?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_carcassitem);?>
												</div>
											</td>

											<td>
												<?php $fn_order_qty = 'order_qty_' . $carcassitem_id;?>
												<input type="number" name="<?=$fn_order_qty;?>" class="form-control" value="<?=set_value($fn_order_qty, number_format($order_slip_item->qty, 2));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_order_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcassitem_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $carcassitem_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $order_slip_item->unit);?>" data-suggest="units" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_pref_alw = 'pref_alw_' . $carcassitem_id;?>
												<input type="number" name="<?=$fn_pref_alw;?>" class="form-control" value="<?=set_value($fn_pref_alw);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_pref_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $carcassitem_id;?>
												<input type="number" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_est_due_amount);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div> -->
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save and Proceed</button>
				<a href="<?=site_url('schedule_haulings')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
