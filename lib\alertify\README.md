[![GitHub version](https://badge.fury.io/gh/<PERSON><PERSON>%2FAlertifyJS.svg)](http://badge.fury.io/gh/<PERSON>Y<PERSON><PERSON>%2FAlertifyJS)
[![NuGet version](https://badge.fury.io/nu/AlertifyJS.svg)](http://badge.fury.io/nu/AlertifyJS)
[![npm version](https://badge.fury.io/js/alertifyjs.svg)](http://badge.fury.io/js/alertifyjs)
[![jsDelivr Hits](https://data.jsdelivr.com/v1/package/npm/alertifyjs/badge?style=rounded)](https://www.jsdelivr.com/package/npm/alertifyjs)

AlertifyJS
==========

[![Join the chat at https://gitter.im/MohammadYounes/AlertifyJS](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/MohammadYounes/AlertifyJS?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)

AlertifyJS is a javascript framework for developing pretty browser dialogs and notifications.

> AlertifyJS is an extreme makeover of <a href="http://www.github.com/fabien-d/alertify.js">alertify.js</a> by <a href="http://www.github.com/fabien-d">@fabien-d</a>



### Install with [NuGet](https://www.nuget.org/packages/AlertifyJS/)

```
Install-Package AlertifyJS
```

### Install with [NPM](https://www.npmjs.com/package/alertifyjs/)

```
npm install alertifyjs --save
```


Documentation
==========
Check out the interactive documentation at http://alertifyjs.com

[![alertifyjs-dialog](https://cloud.githubusercontent.com/assets/4712046/4170670/0d50b04c-3535-11e4-87a7-1ce62dd0d77e.png)](http://alertifyjs.com)


Browser support
==========
Last two versions.



Running documentation locally
==========
* Clone the repo
```
<NAME_EMAIL>:MohammadYounes/AlertifyJS.git
```

* Install dev dependencies
```
npm update; npm install;
```

* Build the project
```
grunt
```

* Start documentation server
```
docpad run
```
* Open your browser to http://localhost:9778/


Dependencies 
==========
None.


Community Contribution
==========

* [alertifyjs-rails](https://github.com/mkhairi/alertifyjs-rails) by **[@mkhairi](https://github.com/mkhairi)**
* [meteor-alertifyjs](https://github.com/ovcharik/meteor-alertifyjs/) by **[@ovcharik](https://github.com/ovcharik)**

Bugs & Questions
==========
Please review the guidelines for [contributing](https://github.com/MohammadYounes/AlertifyJS/blob/master/CONTRIBUTING.md).
>You can fork this [codepen](http://codepen.io/anon/pen/raohK) or this [jsfiddle](http://jsfiddle.net/g2o52zq7/) to get started.


------
Contact: [<EMAIL>](mailto:<EMAIL>)
