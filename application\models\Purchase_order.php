<?php
class Purchase_order extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'purchase_order';
        parent::__construct();
    }

    /**
     * Get purchase_order By ID
     */
    public function get_by_id($purchase_order_id)
    {
        $sql = <<<EOT
SELECT purchase_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, customer.tin_number, supplier.name AS supplier, live_sell_main_usage.name AS usage_name, live_sell_sub_usage.name AS sub_usage_name
FROM purchase_order
LEFT JOIN live_sell_main_usage ON live_sell_main_usage.id = purchase_order.usage_id
LEFT JOIN live_sell_sub_usage ON live_sell_sub_usage.id = purchase_order.sub_usage_id
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN supplier AS supplier ON supplier.id = purchase_order.supplier_id
WHERE purchase_order.id = ?
    AND purchase_order.is_deleted = 0
EOT;
        $binds = [$purchase_order_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM purchase_order
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    public function get_total($purchase_order_id)
    {
        $sql = <<<EOT
SELECT IF(purchase_order.status = "pending", purchase_order.total, IFNULL(SUM(purchase_order_item.est_due_amount), 0)) AS total
FROM purchase_order
LEFT JOIN purchase_order_item ON purchase_order_item.purchase_order_id = purchase_order.id
WHERE purchase_order.id = ?
    AND purchase_order.status = "approved"
    AND purchase_order_item.is_deleted = 0
GROUP BY purchase_order.id
EOT;
        $binds = [$purchase_order_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0]->total;
        } else {
            return 0;
        }
    }

    /**
     * Get Partially Paid purchase_orders
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT purchase_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM purchase_order
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = purchase_order.added_by
WHERE purchase_order.paid_amount < purchase_order.total
    AND purchase_order.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get purchase_orders Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT purchase_order.id, purchase_order.purchase_order_date, purchase_order.customer_id, purchase_order.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM purchase_order
LEFT JOIN (
    SELECT payment.payment_date, paid_purchase_order.purchase_order_id, paid_purchase_order.amount
    FROM payment
    LEFT JOIN paid_purchase_order ON paid_purchase_order.payment_id = payment.id
    LEFT JOIN purchase_order ON purchase_order.id = paid_purchase_order.purchase_order_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND purchase_order.customer_id = ?
) payment ON payment.purchase_order_id = purchase_order.id
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = purchase_order.added_by
WHERE purchase_order.purchase_order_date < ?
    AND customer.id = ?
    AND purchase_order.is_deleted = 0
GROUP BY purchase_order.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get purchase_orders Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT purchase_order.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM purchase_order
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = purchase_order.added_by
WHERE purchase_order.purchase_order_date BETWEEN ? AND ?
    AND purchase_order.customer_id = ?
    AND purchase_order.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Count Urgent
     */
    public function get_count_urgent($supplier_id = null)
    {
        $sql = <<<EOT
SELECT count(is_urgent) as urgent
FROM purchase_order
WHERE is_urgent = 1
AND is_deleted =0
AND status NOT IN('audited','received')
EOT;
        // $binds = [$receive_id];

        if ($res = $this->advanced_query($sql)) {
            return $res[0]->urgent;
        } else {
            return 0;
        }
    }


        /**
     * Get purchase_orders BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT purchase_order.id, purchase_order.purchase_order_no, purchase_order.terms, purchase_order.purchase_order_date, purchase_order.customer_id, purchase_order.total AS charge, purchase_order.total, purchase_order.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM purchase_order
LEFT JOIN (
    SELECT payment.payment_date, paid_purchase_order.purchase_order_id, paid_purchase_order.amount
    FROM payment
    LEFT JOIN paid_purchase_order ON paid_purchase_order.payment_id = payment.id
    LEFT JOIN purchase_order ON purchase_order.id = paid_purchase_order.purchase_order_id
    WHERE payment.payment_date
        AND purchase_order.customer_id = ?
) payment ON payment.purchase_order_id = purchase_order.id
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN user AS issuer ON issuer.id = purchase_order.added_by
WHERE customer.id = ?
    AND purchase_order.is_deleted = 0
GROUP BY purchase_order.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Total Expense
     */
    public function get_total_monthly_expenses()
    {
        $firstDayOfMonth = date('Y-m-01');
        $date_today = date('Y-m-d');

        $sql = <<<EOT
SELECT SUM(purchase_order_item.amount) AS total_monthly_expenses
FROM purchase_order_item
LEFT JOIN purchase_order ON purchase_order.id = purchase_order_item.purchase_order_id
WHERE purchase_order_item.is_deleted = 0
    AND purchase_order.is_deleted = 0
    AND purchase_order.status IN ('approved','printed','for release','released','audited','received')
    AND purchase_order_item.status = "approved"
    AND purchase_order.purchase_order_date BETWEEN '$firstDayOfMonth' AND '$date_today';
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_monthly_expenses;
        }else{
            return 0;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */