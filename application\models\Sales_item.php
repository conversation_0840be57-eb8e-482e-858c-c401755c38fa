<?php
class Sales_item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'sales_item';
        parent::__construct();
    }

    /**
     * Create New Sales Item
     */
    public function generate($name, $unit, $price, $added_by)
    {
        $sql = <<<EOT
INSERT INTO sales_item(name, unit, price, added_by) VALUES(?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    unit = VALUES(unit),
    price = VALUES(price),
    is_deleted = 0
EOT;
        $binds = [$name, $unit, $price, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }


    public function get_by_id($sales_item_id)
    {
        $sql = <<<EOT
SELECT sales_item.*, sales_item.price AS price
FROM sales_item
WHERE sales_item.id = ?
 AND sales_item.is_deleted = 0
EOT;
        $binds = [$sales_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */