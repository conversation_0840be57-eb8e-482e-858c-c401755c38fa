!function(t){"use strict";function e(){var e=t(a());n(e);var i=t(".item-table"),r=i.find(".order_slip_item");0===r.length&&i.find(".no-item").remove(),i.find("tbody").append(e),l()}function n(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:t(this).attr("data-placeholder"),minimumInputLength:3,width:"280px",minimumResultsForSearch:10,ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(t){var e={term:t.term};return e},processResults:function(e){return{results:t.map(e,function(t){return{text:t.name,id:t.id}})}}}})}function a(){var t=i();return'<tr class="order_slip_item"><td><input type="hidden" name="order_slip_item_'+t+'" value="'+t+'"><select style="min-width:200px" width="200px" name="item_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="pref_alw_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="est_due_amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function i(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function r(){return'<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'}function l(){var e=t(".item-table"),n=e.find(".order_slip_item");n.each(function(e,n){t(n).find('input[name^="row"]').val(e+1)})}function o(t,e=null){t.find('input[name^="unit_"]').val(e)}function u(e){return t.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})}function c(e){return t.ajax({url:BASE_URI+"live_sell_customers/get_data/"+e,type:"GET",dataType:"json"})}function s(e){t(e.currentTarget).closest(".order_slip_item").remove();var n=t(e.delegateTarget),a=n.find(".order_slip_item");if(a.length<1){var i=t(r());n.find("tbody").html(i)}l()}t(function(){t(".action-add-item").click(e),t(".item-table").on("click",".action-delete-item",function(t){s(t)}).on("change",'select[name^="item"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item"),a=e.currentTarget.value;t.when(u(a)).then(function(t){o(n,t.unit)},function(){o(n,null)}),console.log(a)}),t('select[name="customer"]').on("change",function(e){t(e.currentTarget).closest(".order_slip_item");var n=e.currentTarget.value;t.when(c(n)).then(function(e){console.log(e),t('input[name="address"]').val(e.address),t('input[name="contact_number"]').val(e.contact_number),t('input[name="email"]').val(e.email),t('input[name="available_credit"]').val(e.available_credit)},function(){t('input[name="address"]').val(""),t('input[name="contact_number"]').val(""),t('input[name="email"]').val(""),t('input[name="available_credit"]').val("")}),console.log(n)}),t(".order_slip_item").each(function(e,a){t(a).find("option").not(":selected").remove(),n(t(a))})})}(jQuery);