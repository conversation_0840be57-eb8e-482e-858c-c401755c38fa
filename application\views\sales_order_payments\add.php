<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_slip" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address <small class="font-italic"></small></label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="contact_number" class="control-label">Contact Number <small class="font-italic"></small></label>

						<input type="text" name="contact_number" class="form-control" value="<?=set_value('contact_number');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('contact_number');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email <small class="font-italic"></small></label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="available_credit" class="control-label">Available Credit <small class="font-italic"></small></label>

						<input type="text" name="available_credit" class="form-control" value="<?=set_value('available_credit');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('available_credit');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="outstanding_balance" class="control-label">Outstanding Balance<small class="font-italic"></small></label>

						<input type="text" name="outstanding_balance" class="form-control" value="<?=set_value('outstanding_balance');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('outstanding_balance');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="order_slip_date" class="control-label">Order Date</label>

						<input type="text" name="order_slip_date" class="form-control datepicker" value="<?=set_value('order_slip_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('order_slip_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('order_slips')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
