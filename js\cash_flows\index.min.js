!function(a){"use strict";function t(t){var e=a(t),o=e.find("thead th"),l=e.DataTable({dom:"lrtip",lengthChange:!0,info:!1,pagingType:"full_numbers",paging:!1,order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:o.length-1}],autoWidth:!1,processing:!0,serverSide:!0,ajax:{url:BASE_URI+"fetch/"+e.attr("data-package"),data:function(t){t.from=a("#from").val(),t.to=a("#to").val()}},footerCallback:function(o,l,s,n,d){var i=this.api(),c=e.attr("data-col-sum");if(void 0!==c&&c>-1){var u=function(a){return"string"==typeof a?1*a.replace(/[\$,]/g,""):"number"==typeof a?a:0},h=i.column(c).data().reduce(function(a,t){return u(a)+u(t)},0);a(i.column(c).footer()).html(a.fn.addNumericCommas(parseFloat(h).toFixed(2))),".tableA"===t?(a("#totalCI").html(a.fn.addNumericCommas(parseFloat(h).toFixed(2))),r+=parseFloat(h)):".tableB"===t&&(a("#totalCO").html(a.fn.addNumericCommas(parseFloat(h).toFixed(2))),r-=parseFloat(h)),a(".totalNetCash").html(a.fn.addNumericCommas(parseFloat(r).toFixed(2)))}}});return l}function e(){o=new Chart(l,{type:"line",data:{labels:[],datasets:[{label:"Cash In",data:[],borderWidth:1,borderColor:"Blue"},{label:"Cash Out",data:[],borderWidth:1,borderColor:"Red"},{label:"Net Cash",data:[],borderWidth:1,borderColor:"Green"}]},options:{scales:{y:{beginAtZero:!0}}}})}var o,r=0,l=document.getElementById("myChart");l.width=600,l.height=200,e(),a(function(){var e=t(".tableA"),s=t(".tableB");a("#tableFilter").submit(function(t){t.preventDefault(),r=0;var n=a("#from").val(),d=a("#to").val();a.ajax({type:"POST",url:BASE_URI+"cash_flows/chart_data",data:{fromDate:n,toDate:d},dataType:"json",success:function(a){o.destroy();let t=[],e=[],r=[],s=[],n=[],d=a.data.cash_in,i=a.data.cash_out;for(let a=0;a<d.length;a++)t.push(d[a].cash_in_date),e.push(parseFloat(d[a].total_cash_in_per_day)),r.push(i[a].cash_out_date),s.push(parseFloat(i[a].total_cash_out_per_day)),n.push(parseFloat(d[a].total_cash_in_per_day-i[a].total_cash_out_per_day));o=new Chart(l,{type:"line",data:{labels:t,datasets:[{label:"Cash In",data:e,borderWidth:1,borderColor:"blue"},{label:"Cash Out",data:s,borderWidth:1,borderColor:"red"},{label:"Net Cash",data:n,borderWidth:1,borderColor:"green"}]},options:{scales:{y:{beginAtZero:!0}}}})},error:function(a){console.error(a)}}),e.ajax.reload(),s.ajax.reload()}).trigger("submit")}),a("#exportToExcel").click(function(){var t=a("#from").val(),e=a("#to").val();a.ajax({type:"POST",url:BASE_URI+"cash_flows/export_excel",data:{fromDate:t,toDate:e},dataType:"json",success:function(a){window.open(BASE_URI+"assets/"+a.filename)},error:function(a){console.error(a)}})})}(jQuery);