<?php
class Eod_d extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_d';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($eodd_id)
    {
        $sql = <<<EOT
SELECT 
    eod_d.*,
    generator.full_name AS generator_name
FROM eod_d
LEFT JOIN user AS generator ON generator.id = eod_d.added_by
WHERE eod_d.is_deleted = 0
    AND eod_d.id = ?
EOT;
        $binds = [$eodd_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */