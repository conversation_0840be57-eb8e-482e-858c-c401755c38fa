<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	theme: "light2", // "light1", "light2", "dark1", "dark2"
	title:{
		text: "Michelson - Morley Experiment"
	},
	subtitles: [{
		text: "Speed = (Given Value + 299,000) km/s",
		fontSize: 15
	}],
	axisY: {
		title: "Readings (in km/s)",
		includeZero: false,
		tickLength: 0,
		gridDashType: "dash",
		stripLines: [{
			value: 792.5,
			label: "True Speed",
			labelFontColor: "#FF0800",
			showOnTop: true,
			labelAlign: "center",
			color: "#FF0800"
		}]
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [{
		type: "boxAndWhisker",
		toolTipContent: "<span style='color:#6D78AD'>{label}:</span> <br><b>Maximum:</b> {y[3]},<br><b>Q3:</b> {y[2]},<br><b>Median:</b> {y[4]}<br><b>Q1:</b> {y[1]}<br><b>Minimum:</b> {y[0]}",
		yValueFormatString: "#####.0 km/s",
		dataPoints: [
			{ x: 0, label: "Experiment 1",  y: [740, 850, 980, 1070, 950] },
			{ x: 1, label: "Experiment 2",  y: [760, 800, 895, 960, 845] },
			{ x: 2, label: "Experiment 3",  y: [840, 840, 880, 910, 860] },
			{ x: 3, label: "Experiment 4",  y: [720, 762.5, 875, 920, 815] },
			{ x: 4, label: "Experiment 5",  y: [740, 802.5, 870, 950, 810] }
		]
	},
	{
		type: "scatter",
		name: "Outlier Values",
		toolTipContent: "<span style='color:#C0504E'>{name}</span>: {y} km/s",
		showInLegend: true,
		dataPoints: [
			{ x: 0, label: "Experiment 1", y: 650 },
			{ x: 2, label: "Experiment 3", y: 620 },
			{ x: 2, label: "Experiment 3", y: 720 },
			{ x: 2, label: "Experiment 3", y: 720 },
			{ x: 2, label: "Experiment 3", y: 970 },
			{ x: 2, label: "Experiment 3", y: 950 }
		]
	}]
});
chart.render();

function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;
	}
	e.chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>