<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_customers extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->live_sell_customer_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_customers/manager'));
    }

    /**
     * Get Customer Data
     */
    public function get_data($customer_id = null)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];
        if (!$customer = $this->live_sell_customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'name' => $customer->name,
                'address' => $customer->address,
                'contact_number' => $customer->phone_no,
                'email' => $customer->email,
                'available_credit' => $customer->available_credit,
                'credit_limit' => $customer->credit_limit,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Callback: Check if customer name exists
     */
    public function _is_customer($customer_name)
    {
        $where = [
            'name' => $customer_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->live_sell_customer_name) === strtolower($customer_name)) {
            return true;
        }

        return empty($this->live_sell_customer->select('', $where, 1));
    }

    /**
     * Add live_sell_Customer
     */
    public function add()
    {

        $config = [
            'upload_path' => FCPATH . 'assets/live_sell_customers',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($this->config->item('live_sell_customers/add')) && $this->_attempt_upload($config, 'attachment', true) && $id = $this->_attempt_add()) {

            $new_path = FCPATH . 'assets/live_sell_customers/' . $id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('live_sell_customers/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Add Live Sell Customer';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'items' => $items,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'live_sell_customers/add', $data);
        }
    }

    /**
     * Edit live_sell_Customer
     */
    public function edit($live_sell_customer_id)
    {
        $where = [
            'id' => $live_sell_customer_id,
            'is_deleted' => 0
        ];
        $live_sell_customer = $this->live_sell_customer->select('', $where, 1) or show_404();

        $this->live_sell_customer_name = $live_sell_customer->name;

        $where = ['is_deleted' => 0];

        $config = [
            'upload_path' => FCPATH . 'assets/live_sell_customers/',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if ($this->_validate_form($this->config->item('live_sell_customers/edit')) and $this->_attempt_upload($config, 'attachments', false, true) and $this->_attempt_edit($live_sell_customer)) {

            $new_path = FCPATH . 'assets/live_sell_customers/' . $live_sell_customer_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('live_sell_customers/manager'));
        } else {
            $this->_delete_uploaded();
            $this->title = 'Edit Live Sell Customer';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'form_error' => $form_error,
                'live_sell_customer' => $live_sell_customer,
                'items' => $items,
            ];
            $this->build_content('default', 'live_sell_customers/edit', $data);
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Delete live_sell_Customer
     */
    public function delete($live_sell_customer_id)
    {
        $where = [
            'id' => $live_sell_customer_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_customer = $this->live_sell_customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Customer is not found.'
            ];
        } elseif (!$this->_attempt_delete($live_sell_customer)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Live Sell Customer is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Check live_sell_Customer remaining Credit Limit
     */
    public function check_credit($live_sell_customer_id)
    {
        $used_credit = $this->live_sell_customer->used_credit($live_sell_customer_id) ?: 0;

        $where = [
            'id' => $live_sell_customer_id,
            'is_deleted' => 0
        ];

        if (!$live_sell_customer = $this->live_sell_customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Customer is found.',
                'credit_limit' => $live_sell_customer->credit_limit ? (float)$live_sell_customer->credit_limit - (float)$used_credit->credit : 0
            ];
        }

        echo json_encode($response);
    }

    /**
     * live_sell_Customers
     */
    public function manager()
    {
        $this->title = 'Live Sell Customers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_customers/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'live_sell_customers/manager', $data);
    }


    /**
     * live_sell_Customers SOA
     */
    public function soa()
    {
        $this->title = 'live_sell_Customers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'js/live_sell_customers/soa'
        ];

        $this->build_content('default', 'live_sell_customers/soa');
    }

    /**
     * live_sell_Customers with negative credit
     */
    public function credit_report()
    {
        $this->title = 'Credit Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_customers/credit_report'
        ];

        $this->build_content('default', 'live_sell_customers/credit_report');
    }

    /**
     * live_sell_Customers
     */
    public function live_sell_customers_sale()
    {
        $this->title = 'Sales Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/select2/dist/js/select2.full',
            'js/live_sell_customers/sales_report'
        ];

        $this->build_content('default', 'live_sell_customers/sales_report');
    }

    /**
     * Get Info of live_sell_Customer
     */
    public function info($live_sell_customer_id = '')
    {
        $where = [
            'id' => $live_sell_customer_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_customer = $this->live_sell_customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Customer is found.',
                'live_sell_customer' => $live_sell_customer
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of live_sell_Customer by name
     */
    public function info_by_name($live_sell_customer_name = '')
    {
        $where = [
            'name' => urldecode($live_sell_customer_name),
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        if (!$live_sell_customer = $this->live_sell_customer->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Customer is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Customer is found.',
                'live_sell_customer' => $live_sell_customer
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('live_sell_customer');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'available_credit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        if (!$live_sell_customer_id = $this->live_sell_customer->insert($values)) {
            $this->_error = 'server_error';
        }

        return $live_sell_customer_id;
    }

    /**
     * Attempt add attachment
     */
    // protected function _add_attachment($id)
    // {
    //     if (!empty($this->_uploaded)) {
    //         $values = [];
    //         foreach ($this->_uploaded as $uploaded) {
    //             $values[] = [
    //                 'live_sell_customer_id' => $id,
    //                 'file_name' => $uploaded['file_name'],
    //                 'added_by' => $_SESSION['user']->id,
    //                 'added_on' => (new DateTime())->format('Y-m-d H:i:s')
    //             ];
    //         }
    //         if (!$attachment_id = $this->live_sell_customer_attachment->insert_batch($values)) {
    //             $this->_error = 'server_error';
    //             return false;
    //         }

    //         return $attachment_id;
    //     } else {
    //         return true;
    //     }

    // }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($live_sell_customer)
    {
        $where = [
            'id' => $live_sell_customer->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->live_sell_customer->update($where, $values)) {
            $this->_error = 'server_error';
        }

        $new_item_prices = $this->input->post('item_price');

        foreach ($new_item_prices as $item_id => $new_item_price) {
            if ($new_item_price == null) {
                continue;
            }

            //Check whether a variable is empty. Also check whether the variable is set/declared:
            if (isset($live_sell_customer_prices_dictionary[$live_sell_customer->id][$item_id])) {
                $where = [
                    'item_id' => $item_id,
                    'live_sell_customer_id' => $live_sell_customer->id,
                    'is_deleted' => 0
                ];

                $values = [
                    'live_sell_customer_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$is_updated = $this->live_sell_customer_prices->update($where, $values)) {
                    $this->_error = 'server_error';
                }
            } else {
                $values = [
                    'item_id' => $item_id,
                    'live_sell_customer_id' => $live_sell_customer->id,
                    'live_sell_customer_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$this->live_sell_customer_prices->insert($values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                }
            }

        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($live_sell_customer)
    {
        $where = [
            'id' => $live_sell_customer->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $live_sell_customer->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->live_sell_customer->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
