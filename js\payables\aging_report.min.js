!function(t){"use strict";function e(e){return t.fn.addNumericCommas(e)}function n(n){var a=t(n),r=(a.find("thead th"),a.DataTable({lengthChange:!0,language:{infoFiltered:""},dom:"Bfrtlip",pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[2,3,4,5,6]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel",footer:!0,exportOptions:{columns:"th"}},{extend:"excel",className:"btn btn-success ml-3",text:"Download A/P Report",footer:!0,exportOptions:{columns:function(t){return 1!==t}}}],autoWidth:!1,processing:!0,serverSide:!0,lengthMenu:[[10,25,50,100,-1],[10,25,50,100,"All"]],ajax:BASE_URI+"fetch/"+a.attr("data-package"),footerCallback:function(n,a,r,o,l){var u=this.api(),c=function(t){return"string"==typeof t&&(t=t.replace(/[\$,]/g,"")),isNaN(t)||""===t||null===t?0:parseFloat(t)},s=function(t){return u.column(t,{page:"current"}).data().reduce(function(t,e){return c(t)+c(e)},0)};[2,3,4,5,6].forEach(function(n){t(u.column(n).footer()).html(e(s(n).toFixed(2)))})}}));return r}t(function(){let e=n(".tableA");t("#tableFilter").submit(function(n){n.preventDefault();var a=t(n.currentTarget).serialize();console.log(a);var r=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+a;e.ajax.url(r).load(),window.history.pushState(null,null,BASE_URI+"payables/aging_report?"+a)}).trigger("submit")})}(jQuery);