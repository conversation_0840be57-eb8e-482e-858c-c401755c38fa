+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		let customer_id = $('input[name="customer_id"]').val();
		let transfer_id = $('input[name="transfer_id"]').val();

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
				{
				  className: 'd-none',
				  targets: [0, 1, 3, 4, 8],
				},
				{
				  className: 'text-left',
				  targets: [5, 7],
				},
				{
				  className: 'text-right',
				  targets: [6],
				},
			],
			searching: false,
			// columnDefs: [{
			// 	className: 'd-none',
			// 	targets: 5
			// ]},
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?customer_id=' + customer_id + '&transfer_id=' + transfer_id
		});

		return dataTable;
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.action-print').click(function () {
			window.print();
		});
	});
}(jQuery);
