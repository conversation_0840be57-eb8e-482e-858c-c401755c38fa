<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Poc_items extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Items
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'item_for_poc';
        $primary_key = 'id'; // Use the primary key of the table

        // Define the columns to be fetched
        $columns = [
            $this->_get_item_name(), // Fetch item description
            $this->_get_unit_price(),  // Fetch unit price
            $this->_get_actions()       // Include actions column if needed
        ];

        $where = "is_deleted = 0"; // Exclude deleted items

        // Generate JSON response using the SSP class
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, null, $where)
        );
    }

    /**
     * Get description
     */
    protected function _get_item_name()
    {
        return [
            'db' => 'item_name',
            'dt' => 0, // Column index for DataTables
            'field' => 'item_name'
        ];
    }

     /**
     * Get unit price
     */
    protected function _get_unit_price()
    {
        return [
            'db' => 'unit_price', // Fetch the raw unit price from the database
            'dt' => 1, // Column index for DataTables
            'field' => 'unit_price',
            'formatter' => function ($d, $row) {
                return number_format($d, 2); // Use PHP's number_format to format with 2 decimal places and commas
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 2, 
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                    $res .= anchor('poc_items/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
