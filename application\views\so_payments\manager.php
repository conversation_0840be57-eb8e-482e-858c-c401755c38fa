<div class="col topbtn">
	<div class="dropdown float-right">
		<a href="<?= site_url('so_payments/add'); ?>" class="btn btn-primary">Add New SO Payment</a>
	</div>

	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">

		<select name="so_payment_type" class="form-control col-1 dropsearch" data-placeholder="Type">
			<option></option>
			<option value="check">Check</option>
			<option value="cash">Cash</option>
		</select>

			<div class="mx-1"></div>

		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<input type="text" value="<?=set_value("from", date('m/01/Y'));?>" name="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" class="form-control datepicker mb-2 mr-sm-2">

<!-- 		<label for="customer" class="mr-sm-2 mld-4">Customer:</label>
		<select name="customer" class="form-control mb-2 mr-sm-2 dropsearch" data-placeholder="Customer">
			<option value=""></option>
			<?php foreach ($customers as $customer): ?>
				<option value="<?=$customer->id;?>"><?=$customer->name;?></option>
			<?php endforeach;?>
		</select> -->

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="so_payments">
		<table class="table table-hover tableA" data-package="so_payments" data-col-sum="2">
			<thead>
				<tr>
                    <th>Payment Date</th>
                    <th>Paying Customer</th>
                    <th>Amount</th>
                    <th>Payment Type</th>
                    <th>Actions</th>
				</tr>
			</thead>

			<tbody></tbody>

			<tfoot>
				<tr>
					<th></th>
					<th class="text-right">Total :</th>
					<th></th>
					<th></th>
					<th></th>
				</tr>
			</tfoot>
		</table>
	</div>
</div>
