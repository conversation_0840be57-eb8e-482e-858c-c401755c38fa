<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="crud-title float-right"><?=$bank->name;?></div>

			<div class="crud-title"><?=$title;?></div>

			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="from">From</label>

						<input type="number" name="from" value="<?=set_value('from');?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('from');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="to">To</label>

						<input type="number" name="to" value="<?=set_value('to');?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('to');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>

				<a href="<?=site_url('banks/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>

<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<div class="crud-box">
			<div class="crud-title">Enrolled Checks</div>

			<table class="table table-bordered datatable">
				<thead>
					<tr>
						<th class="text-center" width="40%">From</th>
						<th class="text-center" width="40%">To</th>
						<th class="text-center" width="20%">Remaining Checks</th>
					</tr>
				</thead>

				<tbody>
					<?php foreach ($batches as $batch): ?>
						<tr>
							<td><?=$batch->first;?></td>
							<td><?=$batch->last;?></td>
							<td class="text-right"><?=$batch->remaining;?></td>
						</tr>
					<?php endforeach; ?>
				</tbody>
			</table>
		</div>
	</div>
</div>
