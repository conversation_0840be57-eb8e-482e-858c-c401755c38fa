<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formsales_order" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title . ' for Order Slip ' . $sales_order->id;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $sales_order->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="sales_order_date" class="control-label">Production Date</label>

						<input type="text" name="sales_order_date" class="form-control datepicker" value="<?=set_value('sales_order_date', DateTime::createFromFormat('Y-m-d', $sales_order->sales_order_date)->format('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('sales_order_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $sales_order->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>

	            	<h5 class="crud-subtitle"><span>Efficiency Report</span></h5>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived" class="control-label">Total Live Arrived <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived" class="form-control" value="<?=set_value('total_live_arrived', $sales_order->total_live_arrived);?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived_heads" class="control-label">Heads <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived_heads" class="form-control" value="<?=set_value('total_live_arrived_heads', $sales_order->total_live_arrived_heads);?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived_heads');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived_kilos" class="control-label">Kilos <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived_kilos" class="form-control" value="<?=set_value('total_live_arrived_kilos', $sales_order->total_live_arrived_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival" class="control-label">Total Death On Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival" class="form-control" value="<?=set_value('total_death_on_arrival', $sales_order->total_death_on_arrival);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival_heads" class="control-label">Heads <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival_heads" class="form-control" value="<?=set_value('total_death_on_arrival_heads', $sales_order->total_death_on_arrival_heads);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival_heads');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival_kilos" class="control-label">Kilos <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival_kilos" class="form-control" value="<?=set_value('total_death_on_arrival_kilos', $sales_order->total_death_on_arrival_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival" class="control-label">Total Death After Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival" class="form-control" value="<?=set_value('total_death_after_arrival', $sales_order->total_death_after_arrival);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival_heads" class="control-label">Heads <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival_heads" class="form-control" value="<?=set_value('total_death_after_arrival_heads', $sales_order->total_death_after_arrival_heads);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival_heads');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival_kilos" class="control-label">Kilos <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival_kilos" class="form-control" value="<?=set_value('total_death_after_arrival_kilos', $sales_order->total_death_after_arrival_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_trips" class="control-label">Total Trips <small class="font-italic"></small></label>

						<input type="number" name="total_trips" class="form-control" value="<?=set_value('total_trips', $sales_order->total_trips);?>">

						<div class="red-text flash-message">
							<?=form_error('total_trips');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="alw" class="control-label">ALW <small class="font-italic"></small></label>

						<input type="number" name="alw" class="form-control" value="<?=set_value('alw', $sales_order->alw);?>">

						<div class="red-text flash-message">
							<?=form_error('alw');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="coops_loaded" class="control-label">Coops Loaded <small class="font-italic"></small></label>

						<input type="number" name="coops_loaded" class="form-control" value="<?=set_value('coops_loaded', $sales_order->coops_loaded);?>">

						<div class="red-text flash-message">
							<?=form_error('coops_loaded');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>Sales Fees</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">Item</th>

									<th class="text-center" style="width: 20%;">Actual Live Weight</th>

									<th class="text-center" style="width: 20%;">Unit</th>

									<th class="text-center" style="width: 20%;">Unit Price</th>

									<th class="text-center" style="width: 20%;">Amount</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($retain_items)):
                                	$counter = 0;
                                    foreach ($retain_items as $i => $retain_item):
                                    $sales_order_item_id = $retain_item['id'];
                            ?>
										<tr class="sales_order_item">
											<td>
												<?php $fn_sales_order_item = 'sales_order_item_' . $sales_order_item_id;?>
												<input type="hidden" name=<?=$fn_sales_order_item?> value="<?=set_value($fn_sales_order_item, $sales_order_item_id);?>">

												<?php $fdn_item = 'item_' . $sales_order_item_id;?>

												<select style="min-width:400px; max-width: 400px;" width="300px" name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<?php if($retain_items[$counter]):?>
														<option value=<?=$retain_items[$counter]['item_id']?>><?=$retain_items[$counter]['item_name']?></option>
													<?php else:?>
														<option></option>
													<?php endif;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_sales_order_item);?>
												</div>
											</td>

											<td>
												<?php $fn_actual_live_weight = 'actual_live_weight_' . $sales_order_item_id;?>
												<input type="number" name="<?=$fn_actual_live_weight;?>" class="form-control" value="<?=set_value($fn_actual_live_weight, $retain_items[$counter]['actual_live_weight']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_actual_live_weight);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $sales_order_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $retain_items[$counter]['unit']);?>" data-suggest="units" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $sales_order_item_id;?>
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price, $retain_items[$counter]['unit_price']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $sales_order_item_id;?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control" value="<?=set_value($fn_amount, $retain_items[$counter]['amount']);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_amount);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('sales_orders')?>" class="btn btn-light">Close</a>
			</div>

<!-- 			<h1 class="crud-title">Past sales_orders</h1>

			<div class="dt-asc-wrapper row" data-display="sales_orders_by_customer">
				<table class="table table-hover tableA" data-package="sales_orders_by_customer">
					<thead>
						<tr>
							<th>Doc No</th>
							<th style="width: 25%;">Customer</th>
							<th>sales_order Date</th>
							<th>Status</th>
							<th>Items</th>
						</tr>
					</thead>

					<tbody></tbody>
				</table>
			</div> -->
		</form>
	</div>
</div>
