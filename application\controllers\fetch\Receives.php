<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Receives extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Receiving Receipts
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'receive';
        $primary_key = 'receive.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_doc_no(),
            $this->_get_receive_date(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
receive
LEFT JOIN purchase ON purchase.id = receive.purchase_id
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
EOT;

        $where = <<<EOT
receive.is_deleted = 0
EOT;

        if ($purchase_id = $this->input->get('purchase', true)) {
            $where .= <<<EOT

    AND receive.purchase_id = {$purchase_id}
EOT;
        } else {
            $columns[] = $this->_get_purchase_no();
            $columns[] = $this->_get_supplier();
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'receive.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'receive.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'receive';
            }
        ];
    }

    /**
     * Get Receiving Receipt No
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'receive.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                $res = '<p data-for="doc_no">%s</p>';
                return sprintf($res, $d);
            }
        ];
    }

    /**
     * Get Purchase Order No
     */
    protected function _get_purchase_no()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'purchase_no',
            'dt' => 1,
            'field' => 'purchase_no',
            'formatter' => function ($d, $row) {
                $res = '<p data-for="purchase_no">%s</p>';
                return sprintf($res, $d);
            }
        ];
    }

    /**
     * Get Supplier Name
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 2,
            'field' => 'supplier'
        ];
    }

    /**
     * Get Receive Date
     */
    protected function _get_receive_date()
    {
        return [
            'db' => 'receive.receive_date',
            'dt' => 3 - (int) (strlen($this->input->get('purchase', true)) > 0) * 2,
            'field' => 'receive_date',
            'formatter' => function ($d, $row) {
                $res = '<p data-for="receive_date">%s</p>';
                return sprintf($res, date('M d, Y', strtotime($d)));
            }
        ];
    }

    /**
     * Get Actions
     */
    protected function _get_actions()
    {
        if (strlen($this->input->get('purchase', true)) > 0) {
            $dt = 2;
        } else {
            $dt = 4;
        }

        return [
            'db' => 'receive.id',
            'as' => 'actions',
            'dt' => $dt,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('receives/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                $res .= anchor('receives/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
