+ function ($) {
	'use strict';

	$(function () {

    $('input[name="amount_paid"]').on('input', function(event){
      var amountPaid = $.fn.removeNumericCommas(event.currentTarget.value);
      var totalPayable = $.fn.removeNumericCommas($('input[name="total_payable"]').val());
      var balance = $('input[name="balance"]');
      var change = $('input[name="change"]');

      var totalChange = amountPaid - totalPayable;
      change.val($.fn.addNumericCommas(totalChange.toFixed(2)<0?0:totalChange.toFixed(2)));

      var totalBalance = totalPayable - amountPaid;
      balance.val($.fn.addNumericCommas(totalBalance.toFixed(2)<0?0:totalBalance.toFixed(2)));

      if(amountPaid > totalPayable){
        $('.invalid-amount').removeClass('d-none');
      } else {
        console.log(amountPaid, " ", totalPayable);
        $('.invalid-amount').addClass('d-none');
      }
    }).trigger('input');


	});
}(jQuery);
