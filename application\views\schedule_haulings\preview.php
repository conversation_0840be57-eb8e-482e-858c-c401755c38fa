<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><b><large>Customer : </large></b> <?=$schedule_hauling->customer;?></p>
					<p class="text-left"><b><large>Farm : </large></b> <?=$schedule_hauling->farm;?></p>
					<p class="text-left"><b><large>Driver : </large></b> <?=$schedule_hauling->driver_name;?></p>
					<p class="text-left"><b><large>Helper : </large></b> <?=$schedule_hauling->helper_name;?></p>
					<p class="text-left"><b><large>Remarks. : </large></b> <?=$schedule_hauling->remarks;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><b><large>Order Slip No : </large></b> <?=anchor(site_url("order_slips/preview/" . $schedule_hauling->order_slip_id), $schedule_hauling->order_slip_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Purchase Order No : </large></b> <?=anchor(site_url("purchase_orders/preview/" . $schedule_hauling->purchase_order_id), $schedule_hauling->purchase_order_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Order Confirmation Date : </large></b> <?=date('m/d/Y', strtotime($schedule_hauling->schedule_hauling_date));?></p>
					<!-- <p class="text-left"><large>Prepared By : </large> <?=$schedule_hauling->issuer;?></p> -->
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>List of Schedule Hauling Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Supplier</th>

									<th class="text-center" style="width: 15%;">Total Qty</th>

									<th class="text-center" style="width: 15%;">Est. Alw</th>

									<th class="text-center" style="width: 15%;">Est. Due Amount</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_quantity = 0;
								$total_est_alw = 0;
								$total_est_due_amount = 0;
								if (!empty($schedule_hauling_item_ids)):
                                	$counter = 0;
                                    foreach ($schedule_hauling_item_ids as $i => $schedule_hauling_item_id):
										$total_quantity += $retain_items[$counter]['qty'];
										$total_est_alw += $retain_items[$counter]['est_alw'];
										$total_est_due_amount += $retain_items[$counter]['est_due_amount'];
							?>
										<tr class="schedule_hauling_item">
											<td><?=$retain_items[$counter]['item_name'];?></td>

											<td class="text-right"><?=$retain_items[$counter]['supplier_name'];?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['qty']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['est_alw']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['est_due_amount']);?></td>
										</tr>
							<?php
									$counter++;
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
									<tr class="schedule_hauling_item">
										<td></td>

										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_quantity);?></td>

										<td class="text-right"><strong><?=floatval($total_est_alw);?></td>

										<td class="text-right"><strong><?=floatval($total_est_due_amount);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($schedule_hauling->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>schedule_hauling Attachments</span></h5>
						<?php $attachments = explode('🔥',$schedule_hauling->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/schedule_haulings/'.$schedule_hauling->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$schedule_hauling->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Order Confirmed By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('schedule_haulings');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="schedule_hauling_id" value="<?=$schedule_hauling->id?>">
		</form>
	</div>
</div>