(function($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			dom: 'Bfrtip',
			language: {
				infoFiltered: ''
			},
			scrollX: false,
			autoWidth: false,
      responsive: true,
			pagingType: 'full_numbers',
			order: [[0, 'asc']],
			columnDefs: [
				{
					className: 'text-right',
					targets: 4
				},
				{
					className: 'dt-action',
					targets: cols.length - 1
				}
			],
			buttons: ['print'],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(checkId, dataTable) {
		$.when(deleteRow(checkId)).then(
			function() {
				dataTable.ajax.reload(null, false);
			},
			function(jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(checkId) {
		return $.ajax({
			url: BASE_URI + 'checks/delete/' + checkId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function approveAndReload(checkId, dataTable) {
		$.when(approveRow(checkId)).then(
			function() {
				dataTable.ajax.reload(null, false);
			},
			function(jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approveRow(checkId) {
		return $.ajax({
			url: BASE_URI + 'checks/approve/' + checkId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function() {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function(event) {
			var cols = $(event.currentTarget)
				.closest('tr.check')
				.find('td');
			var checkNo = typeof cols[0] !== 'undefined' ? cols[2].innerHTML : '';

			alertify
				.confirm('Are you sure you want to delete Check No ' + checkNo + '?', function() {
					var checkId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(checkId, dataTable);
				})
				.setHeader('<em>Delete Check</em>');
		});

		$('.tableA').on('click', '.action-approve', function(event) {
			var cols = $(event.currentTarget)
				.closest('tr.check')
				.find('td');
			var checkNo = typeof cols[0] !== 'undefined' ? cols[2].innerHTML : '';

			alertify
				.confirm('Are you sure you want to approve Check No ' + checkNo + '?', function() {
					var checkId = event.currentTarget.getAttribute('data-id');
					approveAndReload(checkId, dataTable);
				})
				.setHeader('<em>Approve Check</em>');
		});

		$('#tableFilter').submit(function(event) {
			event.preventDefault();

      var queries = $(event.currentTarget).serialize();

			var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + $(event.currentTarget).serialize();
			dataTable.ajax.url(url).load();

      window.history.pushState(null,null,BASE_URI + "checks/manager?"+queries);
		});

	});
})(jQuery);
