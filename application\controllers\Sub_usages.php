<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Sub_usages extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->sub_usage_name = null;
        $this->main_usage_id = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('sub_usages/manager'));
    }

    /**
     * Add Sub-usage
     */
    public function add($usage_id = null)
    {
        $where = [
            'id' => $usage_id,
            'is_deleted' => 0
        ];

        $this->main_usage_id = $usage_id;

        if ($usage = $this->usage->select('', $where, 1) and
            $this->_validate_form($this->config->item('sub_usages/add')) and
            $this->_attempt_add($usage)) {
            redirect(site_url('sub_usages/manager/' . $usage->id));
        } else {
            $this->title = 'Add Sub-usage for ' . $usage->name;

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'usage' => $usage
            ];
            $this->build_content('default', 'sub_usages/add', $data);
        }
    }

    /**
     * Callback: Check if sub-usage name exists
     */
    public function _is_sub_usage($sub_usage_name)
    {
        $where = [
            'name' => $sub_usage_name,
            'is_deleted' => 0,
        ];

        $this->main_usage_id ? $where['main_usage_id'] = $this->main_usage_id : null;

        if(strtolower($this->sub_usage_name) === strtolower($sub_usage_name)) {
            return true;
        }

        return empty($this->sub_usage->select('', $where, 1));
    }

    /**
     * Edit Sub-usage
     */
    public function edit($sub_usage_id = null)
    {
        $where = [
            'id' => $sub_usage_id,
            'is_deleted' => 0
        ];
        $sub_usage = $this->sub_usage->select('', $where, 1) or show_404();

        $this->sub_usage_name = $sub_usage->name;
        $this->main_usage_id = $sub_usage->main_usage_id;

        if ($this->_validate_form($this->config->item('sub_usages/edit')) && $this->_attempt_edit($sub_usage)) {
            redirect(site_url('sub_usages/manager/' . $sub_usage->main_usage_id));
        } else {
            $this->title = 'Edit Sub-usage';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'sub_usage' => $sub_usage
            ];
            $this->build_content('default', 'sub_usages/edit', $data);
        }
    }

    /**
     * Delete Sub-usage
     */
    public function delete($sub_usage_id = null)
    {
        $where = [
            'id' => $sub_usage_id,
            'is_deleted' => 0
        ];
        if (!$sub_usage = $this->sub_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Sub-usage is not found.'
            ];
        } elseif (!$this->_attempt_delete($sub_usage)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Sub-usage is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Sub-usages
     */
    public function manager($usage_id = null)
    {
        $where = [
            'id' => $usage_id,
            'is_deleted' => 0
        ];
        $usage = $this->usage->select('', $where, 1) or show_404();

        $this->title = 'Sub-usages for ' . $usage->name;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sub_usages/manager'
        ];

        $data = [
            'usage' => $usage
        ];
        $this->build_content('default', 'sub_usages/manager', $data);
    }

    /**
     * Get Info of Sub-usage
     */
    public function info($sub_usage_id = null)
    {
        $where = [
            'id' => $sub_usage_id,
            'is_deleted' => 0
        ];
        if (!$sub_usage = $this->sub_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Sub-usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Sub-usage is found.',
                'sub_usage' => $sub_usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of sub-usage by name
     */
    public function info_by_name($sub_usage_name = '')
    {
        $where = [
            'name' => urldecode($sub_usage_name),
            'is_deleted' => 0
        ];

        if (!$sub_usage = $this->sub_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Sub-Usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Sub-Usage is found.',
                'sub_usage' => $sub_usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Sub-usages by Main Usage
     */
    public function subs_of()
    {
        $main_usage = $this->input->get('usage', true);

        $where = [
            'name' => urldecode($main_usage),
            'is_deleted' => 0
        ];
        if (!$usage = $this->usage->select('', $where, 1)) {
            $response = [
                'sub_usages' => null
            ];
        } else {
            $where = [
                'main_usage_id' => $usage->id,
                'is_deleted' => 0
            ];
            $sub_usages = $this->sub_usage->select('', $where);

            $list = array_map(function($sub_usage) {
                return $sub_usage->name;
            }, $sub_usages);

            $list = array_filter($list);
            $list = array_map('trim', $list);
            $list = array_unique($list, SORT_REGULAR);

            $response = [
                'sub_usages' => $list
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('usage');
        $this->load->model('sub_usage');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($usage)
    {
        $values = [
            'main_usage_id' => $usage->id,
            'name' => $this->input->post('name'),
            'added_by' => $_SESSION['user']->id
        ];
        if (!$sub_usage_id = $this->sub_usage->insert($values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $sub_usage_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($sub_usage)
    {
        $where = [
            'id' => $sub_usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->sub_usage->update($where, $values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($sub_usage)
    {
        $where = [
            'id' => $sub_usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $sub_usage->name . uniqid(),
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->sub_usage->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
