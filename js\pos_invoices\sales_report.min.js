!function(t){"use strict";function e(t){for(;/(\d+)(\d{3})/.test(t.toString());)t=t.toString().replace(/(\d+)(\d{3})/,"$1,$2");return t}function a(a){var r=t(a),n=r.find("thead th"),l=r.DataTable({dom:"Bfrtlip",lengthChange:!0,lengthMenu:[[10,25,50,-1],[10,25,50,"All"]],pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2 mr-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!0,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package"),footerCallback:function(a,r,n,l,o){var s=this.api(),i=3,c=s.column(i,{search:"applied"}).data().reduce(function(t,e){var a=parseFloat(e.replace(/[^0-9.-]+/g,""));return t+a},0);t(s.column(i).footer()).html(e(parseFloat(c).toFixed(2)))}});return l}t(function(){let e=a(".tableA");t("#tableFilter").submit(function(a){a.preventDefault();var r=t(a.currentTarget).serialize(),n=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+r;e.ajax.url(n).load(),window.history.pushState(null,null,BASE_URI+"pos_invoices/sales_report?"+r)}).trigger("submit")})}(jQuery);