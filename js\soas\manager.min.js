+function(e){"use strict";function t(t){var a=e(t),n=a.find("thead th"),r=a.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"suppliers/delete/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete",function(e){alertify.confirm("Are you sure you want to delete the selected supplier?",function(){var t=e.currentTarget.getAttribute("data-id");a(t,n)}).setHeader("<em>Delete Supplier</em>")}),e('select[name^="po_id"]').on("change",function(t){var a=t.currentTarget.value;e("a.btn_go").attr("href",BASE_URI+"supplier_invoices/issue/"+a)})})}(jQuery);