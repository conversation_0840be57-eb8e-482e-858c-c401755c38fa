<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Poc extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('customer');
        $this->load->model('poc_model');
        $this->load->model('poc_item');
        $this->load->model('user');
        $this->load->model('item_for_poc');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('poc/manager'));
    }

    /**
     * POC Manager
     */
    public function manager()
    {
        $this->title = "POC";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/poc/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'for_audit'
        ];

        $this->build_content('default', 'poc/manager', $data);
    }

    /**
     * POC Preview
     */
    public function preview($poc_id)
    {
        ($poc = $this->poc_model->get_by_id($poc_id)) or show_404();

        $this->title = 'POC-Purchase Order Form from Customer';
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/poc/preview'
        ];

        $where = [
            'id' => $poc->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $poc_items = $this->poc_item->get_by_poc_id($poc->id);

        $data = [
            'poc' => $poc,
            'customer' => $customer,
            'poc_items' => $poc_items
        ];
        
        $this->build_content('default', 'poc/preview', $data);
    }

     /**
     * Add POC
     */
    public function add()
    {
        $poc_item_ids = $this->_get_poc_item_ids();
        $rules = array_merge($this->config->item('poc/add'), $this->_get_poc_item_rules($poc_item_ids));

        if ($this->_validate_form($rules) AND $id = $this->_attempt_add()) {
            redirect(site_url('poc/manager'));
        } else {
            $this->title = 'POC';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/poc/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $customers = $this->customer->select('', ['is_deleted' => 0], null, 'name ASC');
            $item_for_poc = $this->item_for_poc->select('', ['is_deleted' => 0], null, 'item_name ASC');
            // var_dump($item_for_poc);die();
            // $poc_max = $this->poc_model->select('IFNULL(MAX(id), 0) AS latest_id', ['is_deleted' => 0], 1);

            $data = [
                'form_error'    => $form_error,
                'poc_item_ids'  => $poc_item_ids,
                'customers'     => $customers,
                'item_for_poc'     => $item_for_poc
                // 'latest_id'     => $poc_max->latest_id
            ];


            $this->build_content('default', 'poc/add', $data);
        }
    }

    /**
     * Audit POC
     */
    public function audit()
    {
        $poc_id = $this->input->post('poc_id');

        if (!$poc = $this->poc_model->get_by_id($poc_id)) {
            http_response_code(404);
            $response = [
                'message' => 'POC not found.'
            ];
        } elseif (!$this->_attempt_audit($poc)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'POC audited successfully.'
            ];
        }
        
        echo json_encode($response);
    }
    /**
     * 
     * Change POC Status
     */
    public function change_status($poc_id, $status)
    {
        if (!$poc = $this->poc_model->get_by_id($poc_id)) {
            http_response_code(404);
            $response = [
                'message' => 'POC not found.'
            ];
        } elseif (!$this->_attempt_change_status($poc, $status)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'POC updated successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Attempt Change Status
     */
    protected function _attempt_change_status($poc, $status)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $poc->id
        ];

        switch($status){
            case'approved':
                $values = [
                    'status'        => 'approved',
                    'approved_by'   => $_SESSION['user']->id,
                    'approved_on'   => date('Y-m-d H:i:s')
                ];
                break;
            case'disapproved':
                $values = [
                    'status'           => 'disapproved',
                    'disapproved_by'   => $_SESSION['user']->id,
                    'disapproved_on'   => date('Y-m-d H:i:s')
                ];
                break;
        }

        if(!$this->poc_model->update($where, $values)){
            $this->db->trans_rollback();
            return false;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Audit POC
     */
    protected function _attempt_audit($poc)
    {
        $this->db->trans_begin();

        $credit_line = $this->input->post('credit_line');
        $current_balance = $this->input->post('current_balance');
        $available_credit = $this->input->post('available_credit');

        $where = [
            'id' => $poc->id
        ];

        $values = [
            'status' => 'for_approval',
            'credit_line' => $credit_line,
            'current_balance' => $current_balance,
            'available_credit' => $available_credit,
            'audited_on' => date('Y-m-d H:i:s'),
            'audited_by' => $_SESSION['user']->id,
        ];

        if (!$this->poc_model->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_id = $this->input->post('customer');

        if (!$poc_items = $this->_get_poc_item_data()) {
            $this->_error = 'No poc item added';
            return false;
        }

        if (!$poc_id = $this->_generate_poc($customer_id) or !$this->_generate_poc_items($poc_id, $poc_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $poc_id;
        }
    }

    /**
     * Generate POC
     */
    protected function _generate_poc($customer_id)
    {

        $poc_date           = DateTime::createFromFormat('m/d/Y', $this->input->post('poc_date'));
        $processing         = $this->input->post('processing') === '1' ? 1 : 0;
        $live_sell          = $this->input->post('live_sell') === '1' ? 1 : 0;
        $ice                = $this->input->post('ice') === '1' ? 1 : 0;
        $water              = $this->input->post('water') === '1' ? 1 : 0;
        $other_services     = $this->input->post('other_services') === '1' ? $this->input->post('other_services_field') : null;
        $remarks_1          = $this->input->post('remarks_1') === '1' ? 1 : 0;
        $remarks_2          = $this->input->post('remarks_2') === '1' ? 1 : 0;
        $other_remarks      = $this->input->post('other_remarks') ?: null;

        $poc_max = $this->poc_model->select('IFNULL(MAX(id), 0) + 1 AS next_id', ['is_deleted' => 0], 1);

        $values = [
            'customer_id'       => $customer_id,
            'company_name'      => $this->input->post('company_name'),
            'poc_date'          => $poc_date->format('Y-m-d'),
            'reference'         => 'POC-' . $poc_max->next_id,
            'grand_total'       => $this->input->post('grand_total'),
            'processing'        => $processing,
            'live_sell'         => $live_sell,
            'ice'               => $ice,
            'water'             => $water,
            'others'            => $other_services,
            'remarks_1'         => $remarks_1,
            'remarks_2'         => $remarks_2,
            'other_remarks'     => $other_remarks,
            'added_by'          => $_SESSION['user']->id,
            'added_on'          => date('Y-m-d H:i:s')
        ];

        if(!$id = $this->poc_model->insert($values)) {
            return false;
        } else {
            return $id;
        }
    }

    /**
     * Generate Invoice Items
     */
    protected function _generate_poc_items($poc_id, $poc_items)
    {
        foreach ($poc_items as $poc_item) {
            $values = [
                'poc_id'            => $poc_id,
                'item_for_poc_id'   => $poc_item['item_for_poc_id'],
                'qty'               => $poc_item['qty'],
                'unit_price'        => $poc_item['unit_price'],
                'amount'            => $poc_item['total_amount'],
                'added_by'          => $poc_item['added_by'],
                'added_on'          => $poc_item['added_on']
            ];

            if (!$this->poc_item->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get Item Data
     */
    protected function _get_poc_item_data()
    {
        $data = [];
        $poc_item_ids = $this->_get_poc_item_ids();

        foreach ($poc_item_ids as $poc_item_id) {
            $data[] = [
                'item_for_poc_id'       => $this->input->post('description_'.$poc_item_id),
                'qty'               => $this->input->post('qty_'.$poc_item_id),
                'unit_price'        => $this->input->post('unit_price_'.$poc_item_id),
                'total_amount'      => $this->input->post('total_amount_'.$poc_item_id),
                'added_by'          => $_SESSION['user']->id,
                'added_on'          => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Get Input Suffices
     */
    protected function _get_poc_item_ids()
    {
        $poc_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'poc_item_') === 0) {
                $poc_item_ids[] = $value;
            }
        }

        return $poc_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_poc_item_rules($poc_item_ids)
    {
        $rules = [];

        foreach ($poc_item_ids as $poc_item_id) {
            $rules[] = [
                'field' => 'description_' . $poc_item_id,
                'label' => 'Description',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'qty_' . $poc_item_id,
                'label' => 'Quantity',
                'rules' => 'required|trim|numeric'
            ];

            $rules[] = [
                'field' => 'unit_price_' . $poc_item_id,
                'label' => 'Unit Price',
                'rules' => 'required|trim|numeric'
            ];
        }

        return $rules;
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

      /**
     * Get All item for Poc
     */
    public function all_item_for_poc()
    {
        $data = [
            'item_for_poc' => $this->item_for_poc->get_all() ?: [],
        ];

        echo json_encode($data);
    }




    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}