+function ($) {
    'use strict';

    function createDataTable(selector) {
        var $table = $(selector);

        var dataTable = $table.DataTable({
            destroy: true, // Allow reinitialization
            language: {
                infoFiltered: ''
            },
            lengthChange: false,
            info: false,
            paging: false,
            searching: true,
            ordering: true,
            order: [[0, 'asc']],
            processing: true,
            serverSide: true,
            dom: 'frt',
            ajax: {
                url: BASE_URI + 'fetch/Carcasses',
                type: 'POST',
                dataSrc: 'data',
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX error:', error, thrown);
                    console.log('Response:', xhr.responseText);
                    alertify.error('Error loading data. Please try again.');
                }
            },
            columnDefs: [
                { targets: 0, data: 'sku', title: 'SKU' },
                { targets: 1, data: 'size', title: 'Size', className: 'text-center' },
                { targets: 2, data: 'range', title: 'Range', className: 'text-center' },
                { targets: 3, data: 'no', title: 'NO', className: 'text-center' },
                { targets: 4, data: 'pcs', title: 'PCS', className: 'text-center' },
                { targets: 5, data: 'kgs', title: 'KGS', className: 'text-center' },
                { targets: 6, data: 'actions', title: '', orderable: false, searchable: false, className: 'text-right' }
            ]
        });

        return dataTable;
    }

    function deleteAndReload(carcassId, dataTable) {
        $.when(deleteRow(carcassId)).then(
            function () {
                dataTable.ajax.reload(null, false);
                alertify.success('Carcass record successfully deleted.');
            },
            function (jqXHR) {
                dataTable.ajax.reload(null, false);
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message || 'Delete failed.');
            }
        );
    }

    function deleteRow(carcassId) {
        return $.ajax({
            url: BASE_URI + 'Carcasses/delete/' + carcassId,
            type: 'POST',
            dataType: 'json'
        });
    }

    $(document).ready(function() {
        // Initialize DataTable
        var dataTable = createDataTable('#carcasses-table');

        // Handle delete action
        $(document).on('click', '.action-delete', function() {
            var id = $(this).data('id');
            var $row = $(this).closest('tr');
            var sku = $row.find('td:first').text().trim(); // First column = SKU

            var confirmMessage = sku ?
                'Are you sure you want to delete this carcass record for SKU "' + sku + '"?' :
                'Are you sure you want to delete this carcass record?';

            alertify.confirm(
                confirmMessage,
                function () {
                    deleteAndReload(id, dataTable);
                }
            ).setHeader('<em>Delete Carcass</em>');
        });
    });

}(jQuery);