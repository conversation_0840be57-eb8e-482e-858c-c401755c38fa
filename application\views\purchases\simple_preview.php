<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$purchase->id;?>" size="A4" id="firstReceipt">
		<div class="receipt-header">
			<div class="clearfix">
				<strong class="pull pull-right">Purchase Order</strong>
			</div>

		<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><small>Supplier : </small> <?=strtoupper($supplier->name);?></p>
					<p class="text-left"><small>Usage : </small> <?=$purchase->main_usage . ' - ' . $purchase->sub_usage;?></p>
					<p class="text-left"><small>Plate No. : </small> <?=$purchase->plate_no;?></p>
					<p class="text-left"><small>Date Covered : </small> <?=$purchase->date_covered;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p>
					<?php if ($purchase->is_urgent === '1'): ?>
					<span class="display-4 red-text font-weight-bold">URGENT</span>
					<?php endif;?></p>
					<p class="text-left"><small>Doc No : </small> <?=$purchase->id;?></p>
					<p class="text-left"><small>Date : </small> <?=date('n/j/Y', strtotime($purchase->purchase_date));?></p>
				</div>
			</div>
		</div>

		<div class="clearfix"></div>

		<table class="receipt-body">
			<thead>
				<tr>
					<th width="17%">Qty</th>
					<th width="32%">Description</th>
					<th width="17%" class="text">Account Name</th>
					<th width="17%" class="text-right">Unit Price</th>
					<th width="17%" class="text-right">Price</th>
				</tr>
			</thead>

			<tbody>
				<?php
                    //$total = 0;

                    foreach ($purchase_items as $purchase_item):
                        //if ($purchase_item->status === 'approved'):
                        //    $total += $purchase_item->amount;
                        //endif;
                    ?>
							<tr class="item">
								<td><?=floatval($purchase_item->qty) . ' ' . $purchase_item->unit?></td>
								<td><?=$purchase_item->item;?></td>
								<td class="text-right"><?=$purchase_item->account_type;?></td>
								<td class="text-right"><?=number_format($purchase_item->price, 2);?></td>
								<td class="text-right"><?=$purchase_item->status === 'approved' ? number_format($purchase_item->amount, 2) : '<span class="red-text font-italic font-weight-bold">' . strtoupper($purchase_item->status) . '</span>';?></td>
							</tr>
					<?php
                        endforeach;
                    ?>
			</tbody>

			<tfoot>
				<tr class="item">
					<td colspan="3">
					<?php if (!empty($purchase->remarks)): ?>
						<table width="100%">
							<tr>
								<td><strong>Remarks</strong></td>
								<td><?=$purchase->remarks;?></td>
							</tr>
						</table>
					<?php endif;?>
					</td>

					<td colspan="2">
						<table width="100%">
							<tr>
								<td class="text-right"><strong>Total</strong></td>
								<td class="text-right"><?=number_format($purchase->total, 2);?></td>
							</tr>
						</table>
					</td>
				</tr>
			</tfoot>
		</table>

		<div class="row">
			<div class="col-md-12">
				<p class="text-justify"><strong>Dear Supplier:</strong></p>
				<p class="text-justify">Please honor this request slip as PURCHASE ORDER (Proof of Purchase) for the items listed above. Should you have any verifications, don&apos;t hesitate to call me at (053) 321-1235.</p>
				<p class="text-justify">Thank you and God bless!</p>
				<br>
				<p class="text-justify">Please attach this original PO upon billing. No PO, no pay policy.</p>
				<p class="text-danger">This PO is only valid for 3 days upon the date of issuance.</p>

			</div>
		</div>

		<div class="row">
			<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
				<?=$purchase->requisitioner;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Requisitioned by</p>
			</div>

			<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
			    <?php if (!empty($purchase->author_sig_url)): ?>
				<img src="<?=base_url('assets/asylum/signatures/' . $purchase->author_id . '/' . $purchase->author_sig_url);?>" class="img-responsive" style="height: 100px;">
				<?php endif;?>
				<br>
				<?=$purchase->author;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Prepared by</p>
			</div>

			<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
			    <?php if (!empty($purchase->recommender_sig_url)): ?>
				<img src="<?=base_url('assets/asylum/signatures/' . $purchase->recommender_id . '/' . $purchase->recommender_sig_url);?>" class="img-responsive" style="height: 100px;">
				<?php endif;?>
				<br>
				<?=$purchase->recommender;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Approved by</p>
			</div>

			<div class="col-sm-3 text-center mt-auto" style="margin-top: -47px;">
				<img src="<?=base_url('assets/asylum/signatures/' . $purchase->approver_id . '/' . $purchase->approver_sig_url);?>" class="img-responsive" style="height: 100px;">
				<br>
				<?=$purchase->approved_on?>
				<hr class="signature-line" style="margin: 5px">
				<p>Verified by</p>

			</div>
		</div>

		<div class="col-md-12 text-right">
			<?php
                if ($purchase->printed_by != 0 || $purchase->printed_by != null) {
                    echo "<font size='1'>REPRINTED</font><br/>";
                } else {
                    echo "<font size='1'>ORIGINAL COPY</font>";
                }
            ?>
		</div>
	</div>


	<div class="clearfix text-center">
		<?php if ($_SESSION['user']->role_id !== '5' || $purchase->status !== 'printed'): ?>
			<button type="button" class="btn btn-primary action-approve" data-status="<?=$status?>" data-id="<?=$purchase->id;?>">Approve for <?=$status?></button>
		<?php endif;?>
		<a class="btn btn-light" href="<?=site_url('purchases');?>">Close</a>
	</div>
</div>

<?php if(!empty($purchase->attachment)):?>
<div class="col-md-12 sect-jr">
		<h3 class="text-center">Attachments</h3>
		 <?php $attachments = explode('🔥',$purchase->attachment);?>

		 <table class="table table-bordered">
		 <tbody>

		 <?php foreach($attachments as $attachment):?>
		 <tr>
			<td><a href="<?=base_url('assets/pos/'.$purchase->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a></td>
		 </tr>
		 <?php endforeach;?>

		 </tbody>
		 </table>

</div>
<?php endif;?>