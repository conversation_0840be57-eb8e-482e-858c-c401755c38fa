+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(processingId, dataTable) {
		$.when(receiveRow(processingId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(processingId) {
		return $.ajax({
			url: BASE_URI + 'processings/receive_processing/' + processingId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(processingId, dataTable) {
		$.when(deleteRow(processingId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(processingId) {
		return $.ajax({
			url: BASE_URI + 'processings/delete/' + processingId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.processing').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the processing ' + docNo + '?',
					function () {
						var processingId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(processingId, dataTable);
					}
				).setHeader('<em>receive processing</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.processing').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete processing ' + docNo + '?',
					function () {
						var processingId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(processingId, dataTable);
					}
				).setHeader('<em>Delete processing</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/processings?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'processings/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
