+(function ($) {
    "use strict";

    function printPurchase(purchaseId) {
        $.when(markPrinted(purchaseId)).then(
            function () {
                window.print();
            },
            function (jqXHR) {
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message);
            }
        );
    }

    // function changeStatus(purchaseId, type, remarks, auditStatus) {
    //     $.when(markStatus(purchaseId, type, remarks, auditStatus)).then(
    //         function (response) {
    //             window.location.href = BASE_URI + "purchases/manager/";
    //         },
    //         function (jqXHR) {
    //             var response = JSON.parse(jqXHR.responseText);
    //             alertify.error(response.message);
    //         }
    //     );
    // }

    function changeStatus(purchaseId, type, remarks, auditStatus) {
        var formData = new FormData();
        formData.append('purchase_id', purchaseId);
        formData.append('type', type);
        formData.append('remarks', remarks);
        formData.append('audit_status', auditStatus);

        var attachments = document.getElementById('attachments').files;
        for (var i = 0; i < attachments.length; i++) {
            formData.append('attachments[]', attachments[i]);
        }

        $.ajax({
            url: BASE_URI + "purchases/mark_audit_update/",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            dataType: "json",
            success: function (response) {
                if (response.is_success) {
                    window.location.href = BASE_URI + "purchases/manager/";
                } else {
                    alertify.error(response.message);
                }
            },
            error: function (jqXHR) {
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message);
            }
        });
    }

    function markPrinted(purchaseId) {
        return $.ajax({
            url: BASE_URI + "purchases/mark_printed/" + purchaseId,
            type: "POST",
            dataType: "json",
        });
    }

    function markStatus(purchaseId, type, remarks, auditStatus) {
        return $.ajax({
            url: BASE_URI + "purchases/mark_audit_update/",
            type: "POST",
            dataType: "json",
            data: { purchase_id: purchaseId, type: type, remarks: remarks, audit_status: auditStatus },
        });
    }

    // Function to fetch current audit remarks and status
    function fetchAuditDetails(purchaseId, callback) {
        $.ajax({
            url: BASE_URI + "purchases/fetch_audit_details/" + purchaseId,
            type: "GET",
            dataType: "json",
            success: function(response) {
                callback(response);
            },
            error: function(jqXHR) {
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message);
            }
        });
    }
  
    $(function () {
        $("button.action-print").click(function (event) {
            var type = event.currentTarget.getAttribute("data-type");

            if (type === "Audit") {
                var purchaseId = event.currentTarget.getAttribute("data-id");

                // Fetch current audit details before showing the modal
                fetchAuditDetails(purchaseId, function(response) {
                    alertify.confirm(
                        "Audit Purchase Order",
                        `

						<div>
							<input type="radio" id="complete" name="audit_status" value="complete" ${response.audit_status === 'complete' ? 'checked' : ''}> Complete &nbsp; &nbsp;
							<input type="radio" id="incomplete" name="audit_status" value="incomplete" ${response.audit_status === 'incomplete' ? 'checked' : ''}> Incomplete<br>
							&nbsp;
						</div>
                        <div>
							<textarea id="remarks" name="remarks" placeholder="Enter remarks..." class="w-100">${response.remarks}</textarea>
                        </div>
                        <div>
                            <input type="file" id="attachments" name="attachments[]" accept=".pdf, .jpg, .jpeg, .png" multiple>
                        </div>
                        `,
                        function () {
                            var purchaseId = event.currentTarget.getAttribute("data-id");
                            var remarks = document.getElementById('remarks').value;
                            var auditStatus = document.querySelector('input[name="audit_status"]:checked').value;
    
                            if (remarks === "" || !auditStatus) {
                                alertify.alert('<span class="red-text">Missing Remarks or Audit Status!</span>').setHeader("<em>Required</em>");
                            } else {
                                changeStatus(purchaseId, type, remarks, auditStatus);
                            }
                        },
                        function () {
                            alertify.error("Cancel");
                        }
                    ).setHeader("<em>Audit Purchase Order</em>");
                });
            } else {
                alertify.confirm(
                    "Are you sure you want to " + type + " this Purchase Order?",
                    function () {
                        var purchaseId = event.currentTarget.getAttribute("data-id");
                        if (type == "Print") {
                            printPurchase(purchaseId);
                        } else {
                            changeStatus(purchaseId, type, null);
                        }
                    }
                ).setHeader("<em>" + type + " Purchase Order</em>");
            }
        });
    });
})(jQuery);
