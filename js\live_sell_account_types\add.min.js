!function(i){"use strict";i(function(){var e=i(".img-preview"),n=e.attr("src"),t=i('input[name="image_upload"]');e.click(function(){t.click()}),t.change(function(e){var t,a,r;e=e.currentTarget,t=n,r=i(".img-preview"),e.files&&e.files[0]?((a=new FileReader).onload=function(e){r.attr("src",e.currentTarget.result)},a.readAsDataURL(e.files[0])):r.attr("src",t)}),i("select[name = 'applied_to']").change(function(){i("select[name = 'account_type']").empty(),"Purchase Order"==i(this).val()?i("select[name = 'account_type']").select2({data:[{id:"Cost of sales",text:"Cost of sales"},{id:"Current assets",text:"Current assets"},{id:"Current liabilities",text:"Current liabilities"},{id:"Equity",text:"Equity"},{id:"Fixed asset",text:"Fixed asset"},{id:"Operating expenses",text:"Operating expenses"},{id:"Other current assets",text:"Other current assets"},{id:"Other current liabilities",text:"Other current liabilities"}],placeholder:i(this).attr("data-placeholder"),width:"100%",allowClear:!0}):"Sales Income"==i(this).val()&&i("select[name = 'account_type']").select2({data:[{id:"Revenue",text:"Revenue"}],placeholder:i(this).attr("data-placeholder"),width:"100%",allowClear:!0}),i("select[name = 'account_type']").val(i("select[name = 'account_type']").attr("data-default")).trigger("change")}),i("select[name = 'applied_to']").trigger("change"),i("select[name = 'financial_statement']").change(function(){i("select[name = 'balance_sheet_category']").empty(),"Balance sheet"==i(this).val()?i("select[name = 'balance_sheet_category']").select2({data:[{id:"Cash on hand and in bank",text:"Cash on hand and in bank"},{id:"Cumulative earnings - Appropriated",text:"Cumulative earnings - Appropriated"},{id:"Cumulative earnings - Unappropriated",text:"Cumulative earnings - Unappropriated"},{id:"Income tax payables",text:"Income tax payables"},{id:"Inventories",text:"Inventories"},{id:"Other assets",text:"Other assets"},{id:"Other current assets",text:"Other current assets"},{id:"Property and equipment - Net",text:"Property and equipment - Net"},{id:"Share capital",text:"Share capital"},{id:"Trade and other payables",text:"Trade and other payables"},{id:"Trade and other receivables",text:"Trade and other receivables"}],placeholder:i(this).attr("data-placeholder"),width:"100%",allowClear:!0}):"Income statement"==i(this).val()&&i("select[name = 'balance_sheet_category']").select2({data:[{id:"",text:""}],placeholder:i(this).attr("data-placeholder"),width:"100%",allowClear:!0}),i("select[name = 'balance_sheet_category']").val(i("select[name = 'balance_sheet_category']").attr("data-default")).trigger("change")}),i("select[name = 'financial_statement']").trigger("change")})}(jQuery);