<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Purchase Order Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="supplier" class="control-label" data-status="<?=$purchase->status?>">Supplier</label>
						<?php if ($purchase->status == 'released') { ?>
							<select name="supplier" class="form-control dropsearch" data-placeholder="" readonly>
								<option></option>
								<?php foreach ($suppliers as $supplier): ?>
									<option value="<?=$supplier->id;?>" <?=set_select('supplier', $supplier->id, $supplier->id === $purchase->supplier_id);?>><?=$supplier->name?></option>
								<?php endforeach;?>
							</select>
						<?php } else { ?>
							<select name="supplier" class="form-control dropsearch" data-placeholder="">
								<option></option>
								<?php foreach ($suppliers as $supplier): ?>
									<option value="<?=$supplier->id;?>" <?=set_select('supplier', $supplier->id, $supplier->id === $purchase->supplier_id);?>><?=$supplier->name?></option>
								<?php endforeach;?>
							</select>				
						<?php } ?>
						<div class="red-text flash-message">
							<?=form_error('supplier');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="purchase_date" class="control-label">Purchase Date</label>
						<?php if ($purchase->status == 'released') { ?>
							<input type="text" name="purchase_date" class="form-control" value="<?=set_value('purchase_date', date('m/d/Y', strtotime($purchase->purchase_date)));?>" readonly>
						<?php } else { ?>
							<input type="text" name="purchase_date" class="form-control datepicker" value="<?=set_value('purchase_date', date('m/d/Y', strtotime($purchase->purchase_date)));?>">
						<?php } ?>

						<div class="red-text flash-message">
							<?=form_error('purchase_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="plate_no" class="control-label">Plate No. <small class="font-italic">(Optional)</small></label>
						<?php if ($purchase->status == 'released') { ?>
							<input type="text" name="plate_no" class="form-control" value="<?=set_value('plate_no', $purchase->plate_no);?>" readonly>
						<?php } else { ?>
							<input type="text" name="plate_no" class="form-control" value="<?=set_value('plate_no', $purchase->plate_no);?>">
						<?php } ?>

						<div class="red-text flash-message">
							<?=form_error('plate_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>
						<?php if ($purchase->status == 'released') { ?>
							<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $purchase->remarks);?>" readonly>
						<?php } else { ?>
							<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $purchase->remarks);?>">
						<?php } ?>

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>

                    <div class="col-md-8 usage-mode">
                        <div class="row">
                            <div class="col-md-6 form-group">
                                <label for="main_usage" class="control-label">Usage</label>

                                <select name="main_usage" class="form-control dropsearch" data-placeholder="">
                                    <option></option>
                                    <?php foreach ($main_usages as $main_usage): ?>
                                        <option <?=set_select('main_usage', $main_usage, $main_usage === $purchase->main_usage);?>><?=$main_usage?></option>
                                    <?php endforeach;?>
                                </select>

                                <div class="red-text flash-message">
                                    <?=form_error('main_usage');?>
                                </div>

                                <?php if ($_SESSION['user']->role_id === '1'): ?>
                                    <button type="button" class="btn btn-link btn-sm no-side-padding toggle-usage-mode">Show Usage Textfield</button>
                                <?php endif;?></button>
                            </div>

                            <div class="col-md-6 form-group">
                                <label for="sub_usage" class="control-label">Sub-Usage</label>

                                <select name="sub_usage" class="form-control dropsearch" data-placeholder="" data-default="<?=set_value('sub_usage');?>">
                                    <option value="<?=$purchase->sub_usage?>"><?=$purchase->sub_usage?></option>
                                    <?php foreach ($sub_usages as $sub_usage): ?>
                                        <option <?=set_select('sub_usage', $sub_usage, $sub_usage === $purchase->sub_usage);?>><?=$sub_usage?></option>
                                    <?php endforeach;?>
                                </select>

                                <div class="red-text flash-message">
                                    <?=form_error('sub_usage');?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8 usage-mode d-none">
                        <div class="row">
                            <div class="col-md-6 form-group">
                                <label for="main_usage" class="control-label">Usage</label>

                                <input type="text" name="main_usage" class="form-control go-suggest" value="<?=set_value('main_usage', $purchase->main_usage);?>" data-suggest="main_usages" disabled>

                                <div class="red-text flash-message">
                                    <?=form_error('main_usage');?>
                                </div>

                                <?php if ($_SESSION['user']->role_id === '1'): ?>
                                    <button type="button" class="btn btn-link btn-sm no-side-padding toggle-usage-mode">Show Usage Dropdown</button>
                                <?php endif;?>
                            </div>

                            <div class="col-md-6 form-group">
                                <label for="sub_usage" class="control-label">Sub-Usage</label>

                                <input type="text" name="sub_usage" class="form-control go-suggest" value="<?=set_value('sub_usage', $purchase->sub_usage);?>" data-suggest="sub_usages" disabled>

                                <div class="red-text flash-message">
                                    <?=form_error('sub_usage');?>
                                </div>
                            </div>
                        </div>
                    </div>

					<!-- <div class="col-md-4 form-group">
						<label for="si_cr" class="control-label">Sales Invoice / Collection Receipt </label>

						<input type="text" name="si_cr" class="form-control go-suggest" value="<?=set_value('si_cr', $purchase->si_cr);?>" data-suggest="si_cr">

						<div class="red-text flash-message">
							<?=form_error('si_cr');?>
						</div>
					</div> -->


					<!-- <div class="col-md-4 form-group">
						<label for="dr_no" class="control-label">OR No.</label>

						<input type="text" name="dr_no" class="form-control go-suggest" value="<?=set_value('or_no', $purchase->dr_no);?>" data-suggest="dr_no">

						<div class="red-text flash-message">
							<?=form_error('dr_no');?>
						</div>
					</div> -->


					<div class="col-md-4 form-group">
						<label for="requisitioner" class="control-label">Requistioner</label>
						<?php if ($purchase->status == 'released') { ?>
							<input type="text" name="requisitioner" class="form-control go-suggest" value="<?=set_value('requisitioner', $purchase->requisitioner);?>" data-suggest="requisitioners" readonly>
						<?php } else { ?>
							<input type="text" name="requisitioner" class="form-control go-suggest" value="<?=set_value('requisitioner', $purchase->requisitioner);?>" data-suggest="requisitioners">
						<?php } ?>

						<div class="red-text flash-message">
							<?=form_error('requisitioner');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="date_covered" class="control-label">Date Covered</label>
						<?php if ($purchase->status == 'released') { ?>
							<input type="text" name="date_covered" class="form-control" value="<?=set_value('date_covered', $purchase->date_covered);?>" readonly>
						<?php } else { ?>
							<input type="text" name="date_covered" class="form-control" value="<?=set_value('date_covered', $purchase->date_covered);?>">
						<?php } ?>

						<div class="red-text flash-message">
							<?=form_error('date_covered');?>
						</div>
					</div>
					<?php if ($purchase->status == 'released') { ?>
						<div class="col-md-12 form-group">
						</div>
					<?php } else { ?>
						<div class="col-md-12 form-group">
							<label for="attachment[]" class="control-label">Attachment </label>

							<input type="file" name="attachment[]" class="form-control" accept=".pdf, image/*" multiple>

							<div class="red-text flash-message">
								<?=form_error('attachment[]');?>
							</div>
						</div>
					<?php } ?>

					<div class="col-md-12 form-group">
						<table class="table table-bordered attachment-table" width="100%">
								<thead>
								<tr>
									<th class="text-center" style="width: 80%;">Attachment</th>
									<th class="text-center" style="width: 20%;">Action</th>
								</tr>
								</thead>
								<tbody>
									<?php foreach (array_filter(explode('🔥', $purchase->attachment)) as $attachment): ?>
									<tr class="attachment">
										<td>
										<li>
											<a href="<?=base_url('assets/pos/' . $purchase->id . '/' . $attachment);?>" target="blank"><?=$attachment;?></a>
										</li>
										</td>
										<td class="text-center">
											<?php if ($purchase->status == 'released') { ?>
											<?php } else { ?>
												<button type="button" class="btn btn-sm btn-danger action-delete-attachment" data-id="<?=$purchase->id?>" data-filename="<?=$attachment?>">Delete</button>
											<?php } ?>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
						</table>
					</div>

				</div>

            	<h5 class="crud-subtitle"><span>List of Purchased Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Qty</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Unit Price</th>

									<th class="text-center" style="width: 15%;">Amount</th>

									<th class="text-center" style="width: 15%;">Account Name</th>

									<th class="text-center" style="width: 15%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($purchase_item_ids)):
                                    foreach ($purchase_item_ids as $purchase_item_id):
                                        if (set_value('status_' . $purchase_item_id) === 'pending'):
                                            $input_class = 'form-control';
                                            $readonly = '';
                                            $has_button = true;
                                        else:
                                            $input_class = 'form-control-plaintext';
                                            $readonly = 'readonly';
                                            $has_button = false;
                                        endif;
                                    ?>
					<tr class="purchase_item">
						<td>
							<?php $fn_status = 'purchase_item_' . $purchase_item_id;?>
							<input type="hidden" name="<?=$fn_status;?>" value="<?=set_value($fn_status, $purchase_item_id);?>">

							<?php $fn_status = 'status_' . $purchase_item_id;?>
							<input type="hidden" name="<?=$fn_status;?>" value="<?=set_value($fn_status);?>">

							<?php $fn_item = 'item_' . $purchase_item_id;?>
							<input type="text" name="<?=$fn_item;?>" class="<?=$input_class;?> go-suggest" value="<?=set_value($fn_item)?>" data-suggest="items" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_status);?>
								<?=form_error($fn_item);?>
							</div>
						</td>

						<td>
							<?php $fn_qty = 'qty_' . $purchase_item_id;?>
							<input type="number" name="<?=$fn_qty;?>" class="<?=$input_class;?>" value="<?=set_value($fn_qty);?>" step="any" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_qty);?>
							</div>
						</td>

						<td>
							<?php $fn_unit = 'unit_' . $purchase_item_id;?>
							<input type="text" name="<?=$fn_unit;?>" class="<?=$input_class;?> go-suggest" value="<?=set_value($fn_unit);?>" data-suggest="units" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_unit);?>
							</div>
						</td>

						<td>
							<?php $fn_price = 'price_' . $purchase_item_id;?>
							<input type="number" name="<?=$fn_price;?>" class="<?=$input_class;?>" value="<?=set_value($fn_price);?>" step="any" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_price);?>
							</div>
						</td>

						<td>
							<?php $fn_amount = 'amount_' . $purchase_item_id;?>
							<input type="number" name="<?=$fn_amount;?>" class="form-control-plaintext" value="<?=set_value($fn_amount);?>" step="any" readonly>
						</td>

						<td>
							<?php $fn_account_type = 'account_type_' . $purchase_item_id;?>
	
							<select name="<?=$fn_account_type;?>" class="form-control dropsearch" data-placeholder="">
								<option value="" disabled></option>
								<?php foreach ($account_types as $account_type): ?>
									<option value="<?=$account_type->account_name;?>" <?=set_select($fn_account_type, $account_type->account_name);?>><?=$account_type->account_name?></option>
								<?php endforeach; ?>
							</select>

							<div class="red-text flash-message">
								<?=form_error($fn_account_type);?>
							</div>
						</td>

						<td class="text-center">
							<?php if ($has_button): ?>
								<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
							<?php endif;?>
					</td>
				</tr>
	<?php
        endforeach;
        elseif (!empty($purchase_items)):
            foreach ($purchase_items as $purchase_item):
                if ($purchase_item->status === 'pending'):
                    $input_class = 'form-control';
                    $readonly = '';
                    $has_button = true;
                else:
                    $input_class = 'form-control-plaintext';
                    $readonly = 'readonly';
                    $has_button = false;
                endif;
            ?>
					<tr class="purchase_item">
						<td>
							<?php $fn_status = 'purchase_item_' . $purchase_item->id;?>
							<input type="hidden" name="<?=$fn_status;?>" value="<?=set_value($fn_status, $purchase_item->id);?>">

							<?php $fn_status = 'status_' . $purchase_item->id;?>
							<input type="hidden" name="<?=$fn_status;?>" value="<?=set_value($fn_status, $purchase_item->status);?>">

							<?php $fn_item = 'item_' . $purchase_item->id;?>
							<input type="text" name="<?=$fn_item;?>" class="<?=$input_class;?> go-suggest" value="<?=set_value($fn_item, $purchase_item->item)?>" data-suggest="items" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_status);?>
								<?=form_error($fn_item);?>
							</div>
						</td>

						<td>
							<?php $fn_qty = 'qty_' . $purchase_item->id;?>
							<input type="number" name="<?=$fn_qty;?>" class="<?=$input_class;?>" value="<?=set_value($fn_qty, floatval($purchase_item->qty));?>" step="any" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_qty);?>
							</div>
						</td>

						<td>
							<?php $fn_unit = 'unit_' . $purchase_item->id;?>
							<input type="text" name="<?=$fn_unit;?>" class="<?=$input_class;?> go-suggest" value="<?=set_value($fn_unit, $purchase_item->unit);?>" data-suggest="units" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_unit);?>
							</div>
						</td>

						<td>
							<?php $fn_price = 'price_' . $purchase_item->id;?>
							<input type="number" name="<?=$fn_price;?>" class="<?=$input_class;?>" value="<?=set_value($fn_price, $purchase_item->price);?>" step="any" <?=$readonly;?>>

							<div class="red-text flash-message">
								<?=form_error($fn_price);?>
							</div>
						</td>

						<td>
							<?php $fn_amount = 'amount_' . $purchase_item->id;?>
							<input type="number" name="<?=$fn_amount;?>" class="form-control-plaintext" value="<?=set_value($fn_amount, $purchase_item->amount);?>" step="any" readonly>
						</td>

						<td>
							<?php $fn_account_type = 'account_type_' . $purchase_item->id;?>
	
							<select name="<?=$fn_account_type;?>" class="form-control dropsearch" data-placeholder="">
								<?php foreach ($account_types as $account_type): ?>
									<!-- <option value="<?=$account_type->account_name;?>" <?=set_select($fn_account_type, $account_type->account_name);?>><?=$account_type->account_name?></option> -->
									<option value="<?=$account_type->account_name;?>" <?=set_select('account_type', $account_type->account_name, $account_type->account_name == $purchase_item->account_type);?>><?=$account_type->account_name?></option>
								<?php endforeach; ?>
							</select>

							<div class="red-text flash-message">
								<?=form_error($fn_account_type);?>
							</div>
						</td>

						<td class="text-center">
							<?php if ($has_button): ?>
								<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
							<?php endif;?>
					</td>
				</tr>
	<?php
        endforeach;
        else:
    ?>
					<tr class="no-item">
						<td class="text-center" colspan="6">No items added yet.</td>
					</tr>
			<?php
                endif;
            ?>
			</tbody>
		</table>
	</div>
</div>

				<div class="row">
					<div class="col-md-12 form-group">
						<div class="float-right">
							<strong>Total: </strong><span class="total-amount">0.00</span>
						</div>
						<?php if ($purchase->status == 'released') { ?>
						<?php } else { ?>
							<button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button>
						<?php } ?>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('purchases')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>

<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">Past Purchase Orders</h1>

			<div class="dt-asc-wrapper row" data-display="purchases_by_usage">
				<table class="table table-hover tableA" data-package="purchases_by_usage">
					<thead>
						<tr>
							<th>Doc No</th>
							<th>Supplier</th>
							<th>Purchase Date</th>
							<th>Plate No.</th>
							<th>Total</th>
							<th>Requisitioner</th>
							<th>Items</th>
						</tr>
					</thead>

					<tbody></tbody>
				</table>
			</div>
		</div>
	</div>
</div>
