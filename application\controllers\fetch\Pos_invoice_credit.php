<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_invoice_credit extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Users
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'pos_invoice';
        $primary_key = 'pos_invoice.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_customer(),
            $this->_get_invoice_no(),
            $this->_get_grand_total(),
            $this->_get_paid_amount(),
            $this->_get_balance(),
            $this->_get_remarks(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
pos_invoice
LEFT JOIN customer ON customer.id = pos_invoice.customer_id AND customer.is_deleted = 0
EOT;

        $where = <<<EOT
pos_invoice.is_deleted = 0
AND pos_invoice.terms = 'credit'
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'pos_invoice';
            }
        ];
    }

    /**
     * Get Customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 0,
            'field' => 'customer'
        ];
    }

    /**
     * Get Invoice No.
     */
    protected function _get_invoice_no()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'invoice_no',
            'dt' => 1,
            'field' => 'invoice_no'
        ];
    }

    /**
     * Get Grand Total
     */
    protected function _get_grand_total()
    {
        return [
            'db' => 'pos_invoice.total',
            'as' => 'total',
            'dt' => 2,
            'field' => 'total',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Paid Amount
     */
    protected function _get_paid_amount()
    {
        return [
            'db' => 'pos_invoice.paid_amount',
            'as' => 'paid_amount',
            'dt' => 3,
            'field' => 'paid_amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Balance
     */
    protected function _get_balance()
    {
        return [
            'db' => 'IF (pos_invoice.total - pos_invoice.paid_amount > 0, pos_invoice.total - pos_invoice.paid_amount, 0)',
            'as' => 'balance',
            'dt' => 4,
            'field' => 'balance',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'pos_invoice.remarks',
            'as' => 'remarks',
            'dt' => 5,
            'field' => 'remarks'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('pos_invoices/preview/' . $d, 'View', 'title="View" target="_blank" class="dropdown-item text-left"');
                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN]) && $row['balance'] > 0){
                    $res .= anchor('pos_invoices/add_payment/' . $d, 'Add Payment', 'title="Add Payment" class="dropdown-item text-left"');
                }

                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Void</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
