!function(t){"use strict";function e(e){var a=t(e),r=a.DataTable({lengthChange:!1,dom:"Brtip",info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-left",targets:[0,3]},{className:"text-right",targets:2},{className:"dt-action",targets:4},{searchable:!0,targets:[1]}],processing:!0,serverSide:!0,buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],ajax:BASE_URI+"fetch/"+a.attr("data-package"),footerCallback:function(e,r,n,s,o){var l=this.api(),c=a.attr("data-col-sum");if(void 0!==c&&c>-1){var i=function(t){return"string"==typeof t?1*t.replace(/[\$,]/g,""):"number"==typeof t?t:0},u=l.column(c).data().reduce(function(t,e){return i(t)+i(e)},0);t(l.column(c).footer()).html(t.fn.addNumericCommas(parseFloat(u).toFixed(2)))}}});return r}t(function(){let a=e(".tableA");t("#tableFilter").submit(function(e){e.preventDefault();var r=t(e.currentTarget).serialize(),n=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+r;a.ajax.url(n).load(),window.history.pushState(null,null,BASE_URI+"so_payments/manager?"+r)}).trigger("submit")})}(jQuery);