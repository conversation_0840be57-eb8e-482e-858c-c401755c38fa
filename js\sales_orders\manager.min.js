!function(e){"use strict";function a(a){var t=e(a),n=(t.find("thead th"),t.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+t.attr("data-package")}));return n}function t(a,t){e.when(n(a)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var a=JSON.parse(e.responseText);alertify.error(a.message)})}function n(a){return e.ajax({url:BASE_URI+"sales_orders/receive_sales_order/"+a,type:"POST",dataType:"json"})}function r(a,t){e.when(o(a)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var a=JSON.parse(e.responseText);alertify.error(a.message)})}function o(a){return e.ajax({url:BASE_URI+"sales_orders/delete/"+a,type:"POST",dataType:"json"})}function s(a){return e.ajax({url:BASE_URI+"sales_orders/info/"+a,type:"GET",dataType:"json"})}function c(a,t){return e.ajax({url:a,method:"POST",data:t,processData:!1,contentType:!1,dataType:"json"})}e(function(){var n=a(".tableA");e(".tableA").on("click",".action-receive",function(a){var r=e(a.currentTarget).closest("tr.sales_order").find("td"),o=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the sales_order "+o+"?",function(){var e=a.currentTarget.getAttribute("data-id");t(e,n)}).setHeader("<em>receive sales_order</em>")}).on("click",".action-delete",function(a){var t=e(a.currentTarget).closest("tr.sales_order").find("td"),o=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to delete Sales Order "+o+"?",function(){var e=a.currentTarget.getAttribute("data-id");r(e,n)}).setHeader("<em>Delete Sales Order</em>")}).on("click",".action-payment",function(a){var t=a.currentTarget.getAttribute("data-id");e.when(s(t)).then(function(a){var n=e("#addPayment"),r=BASE_URI+"sales_orders/payment/"+t,o=a.sales_order.grand_total-a.sales_order.paid_amount;e("#others").addClass("d-none"),n.find("form").attr("action",r),n.find('input[name="cash_amount"]').val(o),n.find('input[name="check_amount"]').val(o),n.modal("show")},function(e){var a=e.responseJSON;alertify.error(a.message)})}),e("#addPayment").on("change",'input[name="payment_type"]',function(a){var t=a.currentTarget.value;"check"===t?(e("#check").removeClass("d-none"),e("#others").addClass("d-none")):"others"===t?(e("#others").removeClass("d-none"),e("#check").addClass("d-none")):(e("#others").addClass("d-none"),e("#check").addClass("d-none"))}).on("submit","form",function(a){a.preventDefault();var t=a.currentTarget,r=t.getAttribute("action"),o=new FormData(t);console.log(o);var s=e('input[name="payment_type"]:checked').val();console.log(s),e.when(c(r,o)).then(function(){e(a.delegateTarget).modal("hide"),n.ajax.reload(null,!1)},function(e){e.responseJSON;n.ajax.reload(null,!1)})}),e(".tab-link").click(function(a){e(".tab-link.active").removeClass("active"),a.currentTarget.classList.add("active");var t=BASE_URI+"fetch/sales_orders?status="+a.currentTarget.getAttribute("data-status");n.ajax.url(t).load()}),e(".pending").click()})}(jQuery);