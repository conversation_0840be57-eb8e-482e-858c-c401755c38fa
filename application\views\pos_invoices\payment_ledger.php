<div class="col topbtn">
	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<input type="text" value="<?=set_value("from", date("m/d/Y", strtotime("-1 months")));?>" name="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" class="form-control datepicker mb-2 mr-sm-2">

		<!-- <label for="payment_of" class="mr-sm-2">Payment Of:</label> -->
		<select name="payment_of" class="form-control mb-2 mr-sm-2 dropsearch" data-placeholder="Payment Of">
			<option value=""></option>
			<option value="Invoice">Invoice</option>
			<option value="POS Invoice">POS Invoice</option>
			<option value="Cash Bond">Cash Bond</option>
		</select>

		<div class="mx-1"></div>

		<!-- <label for="customer" class="mr-sm-2 ml-4">Customer:</label> -->
		<select name="customer" class="form-control mb-2 mr-sm-2 dropsearch" data-placeholder="Customer">
			<option value=""></option>
			<?php foreach ($customers as $customer): ?>
				<option value="<?=$customer->id;?>"><?=$customer->name;?></option>
			<?php endforeach;?>
		</select>

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="pos_payment_ledger">
		<table class="table table-hover tableA" data-package="pos_payment_ledger" data-col-sum="2">
			<thead>
				<tr>
					<th class="text-left">Payment Date</th>
					<th class="text-right">Invoice/Billing No.</th>
					<th class="text-right">Paid Amount</th>
					<th class="text-left">Customer</th>
					<th class="text-right">Remarks</th>
					<th class="text-right">Payment Of</th>
				</tr>
			</thead>

			<tbody></tbody>
		<tfoot>
			<tr>
                <th>Total</th>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
			</tr>
		</tfoot>
		</table>
	</div>
</div>  