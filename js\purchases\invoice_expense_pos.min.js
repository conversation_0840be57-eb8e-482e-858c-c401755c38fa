!function(e,t){"use strict";function n(t){var n=e(t),a=n.DataTable({lengthChange:!1,info:!1,paging:!1,searching:!1,order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+encodeURI(n.attr("data-package")),footerCallback:function(){var t=this.api(),n=3,a=function(e){if("number"==typeof e)return e;if("string"==typeof e){e=e.replace(/[$,]/g,"");var t=parseFloat(e);return isNaN(t)?0:t}return 0},r=t.column(n).data().reduce(function(e,t){return a(e)+a(t)},0);console.log(r),e(t.column(n).footer()).html(e.fn.addNumericCommas(parseFloat(r).toFixed(2)))},dom:"Bfrtip",buttons:[{extend:"excelHtml5",text:"Download Excel File",className:"btn btn-success",filename:"invoice_expense_pos_export"}]});return a}e(function(){n(".tableA")})}(jQuery,dateFns);