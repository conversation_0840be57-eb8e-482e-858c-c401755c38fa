<?php
class Job_schedule extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'job_schedule';
        parent::__construct();
    }

    /**
     * Create New Schedule
     */
    public function generate($date, $time_start, $time_end, $qty_birds, $details, $area, $added_by)
    {
        $sql = <<<EOT
INSERT INTO item(date, time_start, time_end, qty_birds, details, area, added_by) VALUES(?, ?, ?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    date = VALUES(date), 
    time_start = VALUES(time_start), 
    time_end = VALUES(time_end), 
    qty_birds = VALUES(qty_birds), 
    details = VALUES(details), 
    area = VALUES(area), 
    is_deleted = 0
EOT;
        $binds = [$date, $time_start, $time_end, $qty_birds, $details, $area, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}