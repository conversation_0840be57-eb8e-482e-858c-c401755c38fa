<?php
class Supplier extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'supplier';
        parent::__construct();
    }

    /**
     * Create New Supplier
     */
    public function generate($name, $tin, $address, $zip_code, $added_by)
    {
        $sql = <<<EOT
INSERT INTO supplier(name, tin, address, zip_code, added_by) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    is_deleted = 0
EOT;
        $binds = [$name, $tin, $address, $zip_code, $added_by];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
