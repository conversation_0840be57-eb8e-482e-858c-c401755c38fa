!function(e){"use strict";function t(t,o){var r=e(n());a(r,o),i(r,t);var c=e(".item-table"),l=c.find(".invoice_item");0===l.length&&c.find(".no-item").remove(),c.find("tbody").append(r),s()}function n(){var e=o();return'<tr class="invoice_item"><td><input type="hidden" name="invoice_item_'+e+'" value="'+e+'"><input type="number" name="row_'+e+'" class="form-control-plaintext" value="" readonly></td><td><input type="number" name="qty_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+e+'" class="form-control go-suggest" value="" data-suggest="units"></td><td><select name="item_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="price_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="amount_'+e+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="text" name="rs_'+e+'" class="form-control" value=""></td><td><input type="number" name="alw_'+e+'" class="form-control" value="" step="any"></td><td><select name="account_type_'+e+'" class="dropsearch form-control" data-placeholder="" style="width:100%;"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function a(t,n){t.find('select[name^="account_type"]').select2({data:n,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%",sorter:function(e){return e.sort(function(e,t){return e.text.localeCompare(t.text)})}})}function o(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function i(t,n){t.find('select[name^="item"]').select2({data:n,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function r(){return'<tr class="no-item"><td class="text-center" colspan="9">No items added yet.</td></tr>'}function s(){var t=e(".item-table"),n=t.find(".invoice_item");n.each(function(t,n){e(n).find('input[name^="row"]').val(t+1)})}function c(e){var t=parseFloat(e.find('input[name^="qty"]').val());isNaN(t)&&(t=0);var n=parseFloat(e.find('input[name^="price"]').val());isNaN(n)&&(n=0);var a=n*t;return a<0&&(a=0),e.find('input[name^="amount"]').val(a>0?a.toFixed(2):""),a}function l(){var t=0;e(".item-table").find(".invoice_item").each(function(n,a){t+=c(e(a))}),e('input[name="subtotal"]').val(t.toFixed(2));var n=e('input[name="discount"]').val(),a=t-n;e('input[name="total"]').val(a.toFixed(2))}function d(t){e(t.currentTarget).closest(".invoice_item").remove();var n=e(t.delegateTarget),a=n.find(".invoice_item");if(a.length<1){var o=e(r());n.find("tbody").html(o)}s(),l()}function m(t){return e.ajax({url:BASE_URI+"customers/info/"+t,type:"GET",dataType:"json"})}function u(t){return e.ajax({url:BASE_URI+"customers/info_by_name/"+encodeURIComponent(t),type:"GET",dataType:"json"})}function p(t,n){return e.ajax({url:BASE_URI+"sales_items/info/"+t+"/"+n,type:"GET",dataType:"json"})}function v(){return e.ajax({url:BASE_URI+"sales_items/all_infos",type:"GET",dataType:"json"})}function f(){return e.ajax({url:BASE_URI+"sales_items/customer_prices",type:"GET",dataType:"json"})}function h(t,n){var a="",o="";null!==n&&(a=n.unit,o=n.price),t.find('input[name^="unit"]').val(a),t.find('input[name^="price"]').val(o),e.when(f()).then(function(a){var o=e('select[name="customer"]').val(),i=a.customer_prices_dictionary;void 0!==i[o][n.id]&&t.find('input[name^="price"]').val(i[o][n.id])})}function y(t){return e.ajax({url:BASE_URI+"invoices/delete_attachment/"+t,type:"POST",dataType:"json"})}function _(){return e.ajax({url:BASE_URI+"account_types/all/sales_income",type:"POST",dataType:"json"})}e(function(){var n=[{id:"",text:""}],a=[{id:"",text:""}];e.when(_()).then(function(e){e.account_types.forEach(function(e){e.text=e.account_name,e.id=e.account_name,a.push(e)})}),e.when(v()).then(function(e){e.items.forEach(function(e){e.text=e.name,n.push(e)})}),""!==e('select[name="customer"]').val()?(e("#btnSubmit").prop("disabled",!1),e(".invoice-history").removeClass("d-none")):(e("#btnSubmit").prop("disabled",!0),e(".invoice-history").addClass("d-none"),e(".item-table").find("tbody").html(e(r()))),console.log(e('select[name="customer"] option:selected').attr("data-walkin")),"0"===e('select[name="customer"] option:selected').attr("data-walkin")&&"16"===e('select[name="customer"]').val()?(e(".credit-terms").addClass("d-none"),e(".walkin-customer").removeClass("d-none")):(e(".credit-terms").removeClass("d-none"),e(".walkin-customer").addClass("d-none")),e('select[name="customer"]').on("change",function(t){var n=e(this).val(),a=e("option:selected",this).attr("data-walkin");e('input[name="address"]').val(""),e('input[name="phone_no"]').val(""),e('input[name="email"]').val(""),e('input[name="available_credit"]').val(""),e(".history").attr("href","#"),"16"!==n&&"0"===a?(e.when(m(n)).then(function(t){var n=t.customer.available_credit;(isNaN(n)||null===n)&&(n=0),e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e('input[name="available_credit"]').val(e.fn.addNumericCommas(n)),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"invoices/history/"+t.customer.id)}),e(".credit-terms").removeClass("d-none"),e(".walkin-customer").addClass("d-none")):(e(".credit-terms").addClass("d-none"),e(".walkin-customer").removeClass("d-none"));var o=e("#btnSubmit");""!==n?(o.prop("disabled",!1),e(".invoice-history").removeClass("d-none"),e(".item-table").find("tbody").html(e(r()))):(o.prop("disabled",!0),e(".invoice-history").addClass("d-none"))}),e('input[name="walkin_customer"]').on("change",function(t){var n=e(this).val();e.when(u(n)).then(function(t){e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"invoices/history/"+t.customer.id)},function(t){e(".history").addClass("d-none")});var a=e("#btnSubmit");""!==n?(a.prop("disabled",!1),e(".item-table").find("tbody").html(e(r()))):a.prop("disabled",!0)}),e('input[name="terms"]').on("change",function(t){var n=e('input[name="terms"]:checked').val();"check"===n?(e(".check-terms").removeClass("d-none"),e(".non-credit-terms").removeClass("d-none")):"credit"===n?(e(".non-credit-terms").addClass("d-none"),e(".check-terms").addClass("d-none")):(e(".check-terms").addClass("d-none"),e(".non-credit-terms").removeClass("d-none"))}).trigger("change"),e(".action-add-item").click(function(){t(n,a)}),e(".item-table").on("change",'select[name^="item"]',function(t){var n=e(t.currentTarget).closest(".invoice_item"),a=e('select[name="customer"').val();e.when(p(t.currentTarget.value,a)).then(function(e){h(n,e.item)})}).on("click",".action-delete-item",function(e){d(e)}).on("change",'input[name^="qty"]',l).on("change",'input[name^="price"]',l),e('input[name="discount"]').change(l),l(),e(".attachment-table").on("click",".action-delete-attachment",function(t){var n=e(t.currentTarget).closest("tr.attachment").find("td"),a=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+a+"?",function(){var e=t.currentTarget.getAttribute("data-id");y(e),location.reload()}).setHeader("<em>Delete Attachment</em>")})})}(jQuery);