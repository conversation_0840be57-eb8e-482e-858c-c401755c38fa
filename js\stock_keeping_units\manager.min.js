!function(e){"use strict";function t(t){var n=e(t),a=n.find("thead th"),r=n.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:a.length-1}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+n.attr("data-package")});return r}function n(t,n){e.when(a(t)).then(function(){n.ajax.reload(null,!1)},function(e){n.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t){return e.ajax({url:BASE_URI+"stock_keeping_units/delete/"+t,type:"POST",dataType:"json"})}e(function(){var a=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.stock_keeping_unit").find("td"),i=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,a)}).setHeader("<em>Delete Stock Keeping Unit</em>")})})}(jQuery);