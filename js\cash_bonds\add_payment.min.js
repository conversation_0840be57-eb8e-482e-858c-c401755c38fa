!function(t,a){"use strict";function n(a){return t.ajax({url:BASE_URI+"cash_bonds/partially_paid/"+a,type:"GET",dataType:"json"})}function e(a){var n=t(o());l(n,a);var e=t(".item-table"),i=e.find(".paid_cash_bond");0===i.length&&e.find(".no-item").remove(),e.find("tbody").append(n)}function o(){var t=i();return'<tr class="paid_cash_bond"><td><input type="hidden" name="paid_cash_bond_'+t+'" value="'+t+'"><select name="cash_bond_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="customer_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="cb_date_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="terms_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="number" name="total_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="paid_amount_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="balance_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-dr">Delete</button></td></tr>'}function i(){for(var t="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=a.charAt(Math.floor(Math.random()*a.length));return t}function l(a,n){a.find('select[name^="cash_bond"]').select2({data:n,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function d(a){return t.ajax({url:BASE_URI+"cash_bonds/info/"+a,type:"GET",dataType:"json"})}function r(t,n){var e="",o="",i="",l=0,d=0,r=0;null!==n&&(e=n.customer,o=a.format(a.parse(n.cb_date),"MMM DD, YYYY"),i=n.terms,l=parseFloat(n.grand_total),isNaN(l)&&(l=0),d=parseFloat(n.paid_amount),isNaN(d)&&(d=0),r=l-d,isNaN(r)&&(r=0)),t.find('input[name^="customer_"]').val(e),t.find('input[name^="cb_date"]').val(o),t.find('input[name^="terms"]').val(i),t.find('input[name^="total"]').val(l>0?l.toFixed(2):0),t.find('input[name^="paid_amount"]').val(d>0?d.toFixed(2):0),t.find('input[name^="balance"]').val(r>0?r.toFixed(2):0),t.find('input[name^="amount"]').val(r>0?r.toFixed(2):0)}function c(a){t(a.currentTarget).closest(".paid_cash_bond").remove();var n=t(a.delegateTarget),e=n.find(".paid_cash_bond");if(e.length<1){var o=t(u());n.find("tbody").html(o)}s()}function u(){return'<tr class="no-item"><td class="text-center" colspan="9">No invoice added yet.</td></tr>'}function s(){var a=0,n=0,e=0,o=0;t(".item-table").find(".paid_cash_bond").each(function(i,l){var d=m(t(l));a+=d.total,n+=d.paidAmount,e+=d.balance,o+=d.amount}),t('input[name="z_total"]').val(a.toFixed(2)),t('input[name="z_paid_amount"]').val(n.toFixed(2)),t('input[name="z_balance"]').val(e.toFixed(2)),t('input[name="z_amount"]').val(o.toFixed(2)),t('input[name="cash_amount"]').val(o.toFixed(2)),t('input[name="check_amount"]').val(o.toFixed(2))}function m(t){var a=parseFloat(t.find('input[name^="total"]').val());isNaN(a)&&(a=0);var n=parseFloat(t.find('input[name^="paid_amount"]').val());isNaN(n)&&(n=0);var e=parseFloat(t.find('input[name^="balance"]').val());isNaN(e)&&(e=0);var o=parseFloat(t.find('input[name^="amount"]').val());return isNaN(o)&&(o=0),{total:a,paidAmount:n,balance:e,amount:o}}t(function(){function a(){t(".item-table").find('select[name^="cash_bond"]').select2({data:o,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}var o=[{id:"",text:""}];a(),t('select[name="customer"]').on("change",function(e){var i=t('select[name="customer"]').val();t(".item-table").find('select[name^="cash_bond"]').empty().trigger("change"),o=[{id:"",text:""}],t.when(n(i)).then(function(n){n.cash_bonds.forEach(function(t){t.text=t.id,o.push(t)}),t(".item-table").find('select[name^="invoice"]').select2("destroy"),a()})}),t(".action-add-dr").click(function(){e(o)}),t(".item-table").on("change",'select[name^="cash_bond"]',function(a){var n=t(a.delegateTarget).find('select[name^="cash_bond"] option[value="'+a.currentTarget.value+'"]:selected');if(n.length>1)return alert("Cash Bond already selected."),void t(a.currentTarget).val("").trigger("change");var e=t(a.currentTarget).closest(".paid_cash_bond");t.when(d(a.currentTarget.value)).then(function(t){r(e,t.cash_bond),s()},function(){r(e,null),s()})}).on("click",".action-delete-dr",function(t){c(t)}).on("change",'input[name^="amount"]',s).trigger("change"),t('input[name="type"]').change(function(a){t(".payment-type").addClass("d-none");var n=a.currentTarget.value;t("#"+n).removeClass("d-none")}),s()})}(jQuery,dateFns);