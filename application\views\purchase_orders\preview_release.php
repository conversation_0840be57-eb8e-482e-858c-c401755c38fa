<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$purchase_order->id;?>" size="A4" id="firstReceipt">
		<div class="receipt-header">
			<div class="clearfix">
				<strong class="pull pull-right">Purchase Order</strong>
			</div>

		<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><strong><large>Supplier : </large></strong> <?=strtoupper($purchase_order->supplier);?></p>
					<p class="text-left"><strong><large>Usage : </large></strong><?=$purchase_order->usage_name;?></p>
					<p class="text-left"><strong><large>Sub-Usage : </large></strong> <?=$purchase_order->sub_usage_name;?></p>
					<p class="text-left"><strong><large>Date Covered : </large></strong> <?=$purchase_order->date_covered;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p>
					<?php if ($purchase_order->is_urgent === '1'): ?>
					<span class="display-4 red-text font-weight-bold">URGENT</span>
					<?php endif;?></p>
					<p class="text-left"><strong><large>Doc No : </large></strong> <?=$purchase_order->id;?></p>
					<p class="text-left"><strong><large>PO Date : </large></strong><?=date('n/j/Y', strtotime($purchase_order->purchase_order_date));?></p>
				</div>
			</div>
		</div>

		<div class="clearfix"></div>

		<table class="receipt-body" width="100%">
			<thead>
				<tr>
					<th class="text-left" style="width: 27%;">Item</th>

					<th class="text-left" style="width: 10%;">Unit</th>

					<th class="text-left" style="width: 10%;">Qty</th>

					<th class="text-left" style="width: 10%;">ALW</th>

					<th class="text-left" style="width: 20%;">Account Name</th>

					<th class="text-left" style="width: 10%;">Unit Price</th>

					<th class="text-right" style="width: 20%;">Price</th>

				</tr>
			</thead>

			<tbody>
			<?php
				$total = 0;

				foreach ($purchase_order_items as $purchase_order_item):
					if ($purchase_order_item->status === 'approved'):
						$total += $purchase_order_item->amount;
					endif;
						?>
						<tr class="purchase_order_item">
							<td class="text-left"><?=$purchase_order_item->item;?></td>

							<td class="text-left"><?=$purchase_order_item->unit;?></td>

							<td class="text-left"><?=number_format($purchase_order_item->qty, 2, '.', ',');?></td>

							<td class="text-left"><?=number_format($purchase_order_item->pref_alw, 2, '.', ',');?></td>

							<td class="text-left"><?=$purchase_order_item->account_type;?></td>

							<td class="text-left"><?=number_format($purchase_order_item->unit_price, 2, '.', ',');?></td>

							<td class="text-right"><?=number_format($purchase_order_item->amount, 2, '.', ',');?></td>
						</tr>
			<?php
					endforeach;
			?>
			</tbody>
			<tfoot>
				<tr class="item">
					<td colspan="3">
						<table width="100%">
							<tr>
								<td class="text-left"><strong>Remarks : </strong></td>
									<?php if (!empty($purchase_order->remarks)): ?>
										<td><?=$purchase_order->remarks;?></td>
									<?php endif;?>
							</tr>
						</table>
					</td>

					<td colspan="4">
						<table width="100%">
							<tr class="purchase_order_item">
								<td></td>

								<td class="text-right"><strong>Total: </td>

								<td class="text-right"><strong><?=number_format($total, 2, '.', ',');?></td>
							</tr>
						</table>
					</td>
				</tr>
			</tfoot>
		</table>

        <br>
        <br>

		<div class="row">
			<div class="col-md-12">
				<p class="text-justify"><strong>Dear Supplier:</strong></p>
				<p class="text-justify">Please honor this request slip as PURCHASE ORDER (Proof of Purchase) for the items listed above. Should you have any verifications, don&apos;t hesitate to call me at (053) 321-1235.</p>
				<p class="text-justify">Thank you and God bless!</p>
				<br>
				<p class="text-justify">Please attach this original PO upon billing. No PO, no pay policy.</p>
				<p class="text-danger">This PO is only valid for 3 days upon the date of issuance.</p>

			</div>
		</div>

        <br>
        <br>

		<div class="row">	
			<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
				<?=$purchase_order->requisitioner;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Requisitioned By</p>
			</div>

			<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
				<?=$issuer->full_name;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Prepared By</p>
			</div>

			<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
				<?=$issuer->full_name;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Approved By</p>
			</div>
		</div>

		<br>
        <br>

<?php if(!empty($purchase_order->attachment)):?>
<div class="col-md-12 sect-jr">
		<h3 class="text-center">Attachments</h3>
		 <?php $attachments = explode('🔥',$purchase_order->attachment);?>

		 <table class="table table-bordered">
		 <tbody>

		 <?php foreach($attachments as $attachment):?>
		 <tr>
			<td><a href="<?=base_url('assets/purchase_orders/'.$purchase_order->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a></td>
		 </tr>
		 <?php endforeach;?>

		 </tbody>
		 </table>

</div>
<?php endif;?>

<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				RELEASE FORM
			</h1>

			<hr>
			<fieldset>
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="or_no">OR NUMBER</label>

						<input type="text" name="or_no" value="<?=set_value('or_no');?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('or_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="or_date">OR DATE</label>

						<input type="text" name="or_date" value="<?=set_value('or_date');?>" class="form-control datepicker">

						<div class="red-text flash-message">
							<?=form_error('or_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="or_amount">OR AMOUNT</label>

						<input type="number" name="or_amount" value="<?=set_value('or_amount', sprintf("%.2f", $total));?>" class="form-control" step="any">

						<div class="red-text flash-message">
							<?=form_error('or_amount');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="attachment[]" class="control-label">Attachment </label>

						<input type="file" name="attachment[]" class="form-control" accept=".pdf, image/*"  multiple>

						<div class="red-text flash-message">
							<?=form_error('attachment[]');?>
						</div>
					</div>

				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Release</button>
				<a href="<?=site_url('purchase_orders');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>

