<div class="tab">
	<button type="button" class="tab-link <?=$status === 'for_ar' ? 'active' : '';?>" data-status="for_ar">For AR</button>
	<button type="button" class="tab-link <?=$status === 'for_pr' ? 'active' : '';?>" data-status="for_pr">For PR</button>
	<!-- <button type="button" class="tab-link <?=$status === 'acknowledgement_receipt' ? 'active' : '';?>" data-status="acknowledgement_receipt">Acknowledgement Reciept</button>
	<button type="button" class="tab-link <?=$status === 'payment_receipt' ? 'active' : '';?>" data-status="payment_receipt">Payment Reciept</button> -->
	<button type="button" class="tab-link <?=$status === 'for_audit' ? 'active' : '';?>" data-status="for_audit">For Audit</button>
	<button type="button" class="tab-link <?=$status === 'incomplete_audited' ? 'active' : '';?>" data-status="incomplete_audited">Incomplete Audited</button>
	<button type="button" class="tab-link <?=$status === 'complete_audited' ? 'active' : '';?>" data-status="complete_audited">Complete Audited</button>
	<button type="button" class="tab-link <?=$status === 'all' ? 'active' : '';?>" data-status="all">All</button>
	<button type="button" class="tab-link <?=$status === 'voided' ? 'active' : '';?>" data-status="voided">Voided</button>
</div>

<div class="col topbtn">
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_CASHIER])): ?>
		<div class="dropdown float-right">
			<button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
				Add New Payment
			</button>
			<div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
				<a class="dropdown-item" href="<?= site_url('payments/add'); ?>">Invoice</a>
				<a class="dropdown-item" href="<?= site_url('cash_bonds/add_payment'); ?>">Cash Bond</a>
			</div>

			<?php if ($_SESSION['user']->id === '12'): ?>
				<!-- <button id="generatePayments" class="btn btn-success btn-process">AR/PR Payments</button> -->
			<?php endif; ?>
		</div>
	<?php endif;?>

	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<select name="customer" class="form-control dropsearch col-2" data-placeholder="Customer">
			<option></option>
			<?php foreach ($customers as $customer): ?>
				<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id);?>><?=$customer->name?></option>
			<?php endforeach; ?>
		</select>

		<?php if ($status === 'all' || $status === 'complete_audited' || $status === 'incomplete_audited'): ?>
			<div class="mx-1"></div>

			<select name="payment_type" class="form-control col-1 dropsearch" data-placeholder="Type">
				<option></option>
				<option value="check">Check</option>
				<option value="cash">Cash</option>
			</select>
		<?php endif; ?>

		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<input type="text" value="<?=set_value("from", date("m/d/Y", strtotime("-1 months")));?>" name="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" class="form-control datepicker mb-2 mr-sm-2">

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="payments?status=<?=$status?>">
		<table class="table table-hover tableA" data-package="payments?status=<?=$status?>">
			<thead>
				<tr>
                    <th>Payment Date</th>
                    <th>Paying Customer</th>
                    <th>Amount</th>
                    <th>Payment Type</th>
					<?php if ($status === 'for_pr' || $status === 'for_audit' || $status === 'complete_audited' || $status === 'incomplete_audited'): ?>
						<th>AR No.</th>
					<?php endif; ?>
					<?php if ($status === 'for_audit' || $status === 'complete_audited' || $status === 'incomplete_audited'): ?>
						<th>PR No.</th>
					<?php endif; ?>
					<?php if ($status === 'all'): ?>
						<th>Status</th>
					<?php endif; ?>
                    <th>Actions</th>
				</tr>
			</thead>

			<tbody></tbody>
		</table>
	</div>
</div>

<style>
	.dropdown-menu
	.dropdown-item:hover {
		background-color: #bfb8b8; /* Custom hover color */
	}
</style>
