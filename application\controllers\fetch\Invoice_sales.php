<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Invoice_sales extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            die(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Users
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db'   => $this->db->database,
            'host' => $this->db->hostname,
        ];
        $table       = 'invoice_item';
        $primary_key = 'invoice_item.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_po_no(),
            $this->_get_customer(),
            $this->_get_po_date(),
            $this->_get_items(),
            $this->_get_po_amount(),
        ];

        $joins = <<<EOT
invoice_item
LEFT JOIN sales_item ON sales_item.id = invoice_item.item_id
LEFT JOIN invoice ON invoice.id = invoice_item.invoice_id
LEFT JOIN customer ON customer.id = invoice.customer_id
EOT;

        $where = <<<EOT
invoice.is_deleted = 0
AND invoice_item.is_deleted = 0
AND invoice.status = 'approved'
EOT;

        $account_name = $this->input->get('account_name', true);
        if (!empty($account_name)) {
            $where .= <<<EOT

AND invoice_item.account_type = '{$account_name}'
EOT;
        }

        $from = $this->input->get('from', true);
        $to   = $this->input->get('to', true);
        if (isset($from) and $from_date = date("Y-m-d", strtotime(urldecode($from))) and
            isset($to) and $to_date = date("Y-m-d", strtotime(urldecode($to)))
        ) {
            $where .= <<<EOT

AND invoice.invoice_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $group_by = <<<EOT
invoice_item.account_type, invoice_item.invoice_id
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'as'    => 'id',
            'db'    => 'invoice_item.invoice_id',
            'dt'    => 'DT_RowId',
            'field' => 'id',
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db'        => 'invoice_item.invoice_id',
            'as'        => 'class',
            'dt'        => 'DT_RowClass',
            'field'     => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice_item';
            },
        ];
    }

    /**
     * Get PO
     */
    protected function _get_po_no()
    {
        return [
            'db'    => 'invoice_item.invoice_id',
            'as'    => 'po_no',
            'dt'    => 0,
            'field' => 'po_no',
        ];
    }

    /**
     * Get Customer
     */
    protected function _get_customer()
    {
        return [
            'db'    => 'customer.name',
            'as'    => 'customer',
            'dt'    => 1,
            'field' => 'customer',
        ];
    }

    /**
     * Get PO Date
     */
    protected function _get_po_date()
    {
        return [
            'db'    => 'invoice.invoice_date',
            'as'    => 'po_date',
            'dt'    => 2,
            'field' => 'po_date',
        ];
    }

    /**
     * Get PO Items
     */
    protected function _get_items()
    {
        return [
            'db'    => 'GROUP_CONCAT(CONCAT("• ",sales_item.name) SEPARATOR "<br>")',
            'as'    => 'items',
            'dt'    => 3,
            'field' => 'items',
        ];
    }

    /**
     * Get PO amount
     */
    protected function _get_po_amount()
    {
        return [
            'db'         => 'SUM(invoice_item.amount)',
            'as'         => 'amount',
            'dt'         => 4,
            'field'      => 'amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            },
        ];
    }

}
