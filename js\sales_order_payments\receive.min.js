!function(a){"use strict";function n(e,t){a.when(a.ajax({url:BASE_URI+"purchases/approve_item/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1),a('button.action-approve-item[data-id="'+e+'"]').closest("td").html("")},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}function i(e,t){a.when(a.ajax({url:BASE_URI+"purchases/disapprove_item/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1),a('button.action-approve-item[data-id="'+e+'"]').closest("tr.purchase_item").remove()},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}function o(e){a.when(a.ajax({url:BASE_URI+"purchases/pending_item/"+e,type:"POST",dataType:"json"})).then(function(){location.reload()},function(e){e=JSON.parse(e.responseText);alertify.error(e.message)})}function s(e){a.when(a.ajax({url:BASE_URI+"purchases/mark_approved/"+e,type:"POST",dataType:"json"})).then(function(){window.location.href=BASE_URI+"purchases/manager/"},function(e){e=JSON.parse(e.responseText);alertify.error(e.message)})}a(function(){t=(e=a(e=".tableA")).find("thead th");var e,t,r=e.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-right",targets:t.length-3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+e.attr("data-package")});a("#tableFilter").submit(function(e){e.preventDefault();var e=a(e.currentTarget).serialize(),t=BASE_URI+"fetch/"+a(".tableA").attr("data-package")+"?"+e;r.ajax.url(t).load(),window.history.pushState(null,null,BASE_URI+"transfers/receives/+?"+e)}).trigger("submit"),a(".item-table").on("click",".action-approve-item",function(e){var t=a(e.currentTarget).closest("tr.purchase_item").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to approve "+t+"?",function(){n(e.currentTarget.getAttribute("data-id"),r)}).setHeader("<em>Approve Purchase Order Item</em>")}).on("click",".action-disapprove-item",function(e){var t=a(e.currentTarget).closest("tr.purchase_item").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to disapprove "+t+"?",function(){i(e.currentTarget.getAttribute("data-id"),r)}).setHeader("<em>Dispprove Purchase Order Item</em>")}).on("click",".action-pending-item",function(e){var t=a(e.currentTarget).closest("tr.purchase_item").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to return to pending "+t+"?",function(){o(e.currentTarget.getAttribute("data-id"))}).setHeader("<em>Pending Purchase Order Item</em>")}),a(".action-approve-purchase").click(function(e){var t=e.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to save to print Purchase Order No. "+t+"?",function(){s(t)}).setHeader("<em>Save to Print Purchase Order</em>")})})}(jQuery);