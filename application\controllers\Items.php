<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Items extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('items/manager'));
    }

    /**
     * Get units of item
     */
    public function unit_of($item_id = null)
    {
        $where = [
            'id' => $item_id,
            'is_deleted' => 0
        ];
        if (!$item = $this->item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'unit' => $item->unit
            ];
        }

        echo json_encode($response);
    }

    public function search_by_item()
    {
        $term = $this->input->get('term', true);

        $where = <<<EOT
CONCAT(name) LIKE '%{$term}%'
AND is_deleted = 0
EOT;

        $this->item->db->distinct();
        $items = $this->item->select('', $where);

        $response =  $items;

        echo json_encode($response);
    }

    /**
     * Add Item
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('items/add')) && $this->_attempt_add()) {
            redirect(site_url('items/manager'));
        } else {
            $this->title = 'Add Item';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
                'js/checks/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $customers = $this->customer->select('', $where);

            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);

            $data = [
                'account_types' => $account_types,
                'form_error' => $form_error,
                'customers' => $customers
            ];

            $this->build_content('default', 'items/add', $data);
        }
    }

    /**
     * Edit Item
     */
    public function edit($item_id)
    {
        $where = [
            'id' => $item_id,
            'is_deleted' => 0
        ];
        $item = $this->item->select('', $where, 1) or show_404();

        $where = ['is_deleted' => 0];
        $customer_prices = $this->customer_prices->select('', $where);

        //creates two dimenstional array for customer price checker
        foreach($customer_prices as $customer_price) {
            $curr_customer_id = $customer_price->customer_id;
            $curr_item_id = $customer_price->item_id;
            $curr_customer_price = $customer_price->customer_price;
            $customer_prices_dictionary[$curr_customer_id][$curr_item_id] = $curr_customer_price;
        }


        if ($this->_validate_form($this->config->item('items/edit')) && $this->_attempt_edit($item, $customer_prices_dictionary)) {
            redirect(site_url('items/manager'));
        } else {
            $this->title = 'Edit Item';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $customers = $this->customer->select('', $where);
            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);;

            $data = [
                'account_types' => $account_types,
                'form_error' => $form_error,
                'item' => $item,
                'customers' => $customers,
                'customer_prices' => $customer_prices,
                'customer_prices_dictionary' =>  $customer_prices_dictionary
            ];

            $this->build_content('default', 'items/edit', $data);
        }
    }

    /**
     * Delete Item
     */
    public function delete($item_id)
    {
        $where = [
            'id' => $item_id,
            'is_deleted' => 0
        ];
        if (!$item = $this->item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Item is not found.'
            ];
        } elseif (!$this->_attempt_delete($item)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Item is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Items
     */
    public function manager()
    {
        $this->title = 'Items';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/items/manager'
        ];
        $this->build_content('default', 'items/manager');
    }

    /**
     * Get Info of Items
     */
    public function info($item_id = '', $customer_id = null)
    {
        $data = [
            'item' => $this->item->get_by_id($item_id, $customer_id)
        ];
        echo json_encode($data);
    }

    /**
     * Get All Info of Items
     */
    public function all_infos()
    {
        $where = [ 'is_deleted' => 0 ];
        $data = [
            'items' => $this->item->select('', $where)
        ];

        echo json_encode($data);
    }

    /**
     * Get Info of Customer Prices in Dictionary form
     */
    public function customer_prices()
    {
        $where = ['is_deleted' => 0];
        $customer_prices = $this->customer_prices->select('', $where);

        //creates two dimenstional array for customer price checker
        foreach($customer_prices as $customer_price) {
            $curr_customer_id = $customer_price->customer_id;
            $curr_item_id = $customer_price->item_id;
            $curr_customer_price =$customer_price->customer_price;
            $customer_prices_dictionary[$curr_customer_id][$curr_item_id] = $curr_customer_price;
        }

        $data = [
            'customer_prices_dictionary' => $customer_prices_dictionary
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('item');
        $this->load->model('customer');
        $this->load->model('customer_prices');
        $this->load->model('account_type');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }


    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return $this->upload->display_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'price' => $this->input->post('price'),
            'account_type' => $this->input->post('account_type'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$item_id = $this->item->insert($values)) {
            $this->_error = 'server_error';
        }

        $customer_prices = $this->input->post('customer_price');

        foreach ($customer_prices as $customer_id => $customer_price) {
            if ($customer_price == NULL) {
                continue;
            }

            $values = [
                'item_id' => $item_id,
                'customer_id' => $customer_id,
                'customer_price' => (float)$customer_price,
                'added_by' => $_SESSION['user']->id,
                'added_on' => (new DateTime())->format('Y-m-d H:i:s')
            ];

            if (!$customer_price_id = $this->customer_prices->insert($values)) {
                $this->db->trans_rollback();
                $this->_error = 'server_error';
            }

        }

        return $item_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($item, $customer_prices_dictionary)
    {
        $where = [
            'id' => $item->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'price' => $this->input->post('price'),
            'account_type' => $this->input->post('account_type'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        $new_customer_prices = $this->input->post('customer_price');

        foreach ($new_customer_prices as $customer_id => $new_customer_price) {
            if ($new_customer_price == NULL) {
                continue;
            }

            //Check whether a variable is empty. Also check whether the variable is set/declared:201
            if (isset($customer_prices_dictionary[$customer_id][$item->id])) {
                $where = [
                    'item_id' => $item->id,
                    'customer_id' => $customer_id,
                    'is_deleted' => 0
                ];

                $values = [
                    'customer_price' => (float)$new_customer_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$is_updated = $this->customer_prices->update($where, $values)) {
                    $this->_error = 'server_error';
                }
            } else {
                $values = [
                    'item_id' => $item->id,
                    'customer_id' => $customer_id,
                    'customer_price' => (float)$new_customer_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$is_updated = $this->customer_prices->insert($values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                }
            }
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($item)
    {
        $where = [
            'id' => $item->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
