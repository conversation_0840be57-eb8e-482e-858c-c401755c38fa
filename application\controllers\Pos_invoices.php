<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_invoices extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = false;
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Add Pos Invoice
     */
    public function add()
    {

        $rules = $this->config->item('pos_invoices/add');

        if ($this->_validate_form($rules) && $id = $this->_attempt_add()) {
            $payment_type = $this->input->post('payment_type');
            redirect('pos_invoices/preview/'.$id);
        } else {
            $this->title = 'POS Invoice';

            $this->css = [
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'lib/alertify/build/alertify',
                'js/pos_invoices/invoice'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];

            $pos_items = $this->pos_item->select('', $where, null, "priority DESC, name ASC");
            $where = [
                'is_deleted' => 0,
                'is_active' => 1
            ];
            $customers = $this->customer->select('', $where, null, "name ASC");

            $data = [
                'form_error' => $form_error,
                'pos_items' => $pos_items,
                'customers' => $customers
            ];

            $this->build_content('default', 'pos_invoices/add', $data);
        }

    }


    /**
     * Add Payment
     */
    public function add_payment($pos_invoice_id)
    {

        $where = [
            'id' => $pos_invoice_id,
            'is_deleted' => 0
        ];

        $pos_invoice = $this->pos_invoice->select('', $where ,1) or show_404();
        $rules = $this->config->item('pos_invoices/add_payment');

        if ($this->_validate_form($rules) && $this->_attempt_add_payment($pos_invoice)) {
            $payment_type = $pos_invoice->terms;
            if ($payment_type === 'credit') {
                redirect('pos_invoices/credit');
            } elseif ($payment_type === 'cash') {
                redirect('pos_invoices/cash');
            }
        } else {
            $this->title = 'POS Invoice No.'.$pos_invoice->id;

            $this->css = [
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'lib/alertify/build/alertify',
                'js/pos_invoices/add_payment'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $payable = $pos_invoice->total - $pos_invoice->paid_amount;

            $data = [
                'form_error' => $form_error,
                'pos_invoice' => $pos_invoice,
                'payable' => $payable
            ];

            $this->build_content('default', 'pos_invoices/add_payment', $data);
        }

    }


    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();
        if (!$pos_invoice_items = $this->_get_pos_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'no_pos_item_added';
            return false;
        }

        $invoice_date = date('Y-m-d');
        $paid_amount = $this->input->post('amount_paid') ?: 0;
        $sub_total = array_reduce($pos_invoice_items, function ($current_total, $pos_invoice_item) {
            return $current_total + $pos_invoice_item['amount'];
        });

        $total = $sub_total;

        $values = [
            'customer_id' => $this->input->post('customer') ? : null,
            'customer_name' => $this->input->post('customer_name') ? : null,
            'invoice_date' => $invoice_date,
            'terms' => $this->input->post('payment_type'),
            'subtotal' => $sub_total,
            'total' => $total,
            'remarks' => $this->input->post('remarks'),
            'paid_amount' => $paid_amount,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];


        if(!$pos_invoice_id = $this->pos_invoice->insert($values) or !$this->_generate_pos_invoice_items($pos_invoice_id, $pos_invoice_items) or !$this->_generate_payment($pos_invoice_id, $pos_invoice_items)){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $pos_invoice_id;
    }


    /**
     * Attempt Add Payment
     */
    protected function _attempt_add_payment($pos_invoice)
    {

        $this->db->trans_begin();
        $where = [
            'pos_invoice_id' => $pos_invoice->id,
            'is_deleted' => 0
        ];

        $pos_invoice_items = $this->pos_invoice_item->select('', $where);

        $paid_amount = $this->input->post('amount_paid');
        $total_paid_amount = $paid_amount + $pos_invoice->paid_amount;

        $where = [
            'id' => $pos_invoice->id
        ];

        $values = [
            'paid_amount' => $total_paid_amount,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];


        if(!$is_updated = $this->pos_invoice->update($where, $values) or !$this->_generate_new_payment($pos_invoice->id, $pos_invoice_items)){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $is_updated;
    }

    /**
     * Payment Ledger for POS
     */

    public function payment_ledger()
    {
        $this->title = 'Payment Ledger';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ]; 
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/pos_invoices/payment_ledger'
        ];


        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $customers = $this->customer->select('', $where, '', 'name');

        $data = [
            'suppliers' => $suppliers,
            'customers' => $customers
        ];
        $this->build_content('default', 'pos_invoices/payment_ledger', $data);
    }

    /**
     * Payment Ledger for POS
     */

    public function voided_invoices()
    {
        $this->title = 'Voided Invoices';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/pos_invoices/voided_invoices'
        ];


        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $data = [
            'suppliers' => $suppliers
        ];
        $this->build_content('default', 'pos_invoices/voided_invoices', $data);
    }

    /**
     * Generate POS Invoice Items
     */
    protected function _generate_pos_invoice_items($pos_invoice_id, $pos_invoice_items)
    {
        foreach ($pos_invoice_items as $pos_invoice_item) {
            if (!$this->pos_invoice_item->generate(
                    $pos_invoice_item['id'],
                    $pos_invoice_id,
                    $pos_invoice_item['pos_item_id'],
                    $pos_invoice_item['qty'],
                    $pos_invoice_item['price'],
                    $pos_invoice_item['amount'],
                    $pos_invoice_item['added_by'],
                    date('Y-m-d H:i:s')
                )
            ) {
                return false;
            }

        }
        return true;

    }

    /**
     * Generate Payment
     */
    protected function _generate_payment($pos_invoice_id,$pos_invoice_items)
    {
        $payment_type = $this->input->post('payment_type');

        if($payment_type === 'credit'){
            return true;
        }

        $paid_amount = $this->input->post('amount_paid');
        $change = floatval(str_replace(',','',$this->input->post('change')));
        $sub_total = array_reduce($pos_invoice_items, function ($current_total, $pos_invoice_item) {
            return $current_total + $pos_invoice_item['amount'];
        });

        $grand_total = $sub_total;

        $values = [
            'pos_invoice_id' => $pos_invoice_id,
            'grand_total' => $grand_total,
            'paid_amount' => $paid_amount,
            'change' => $change,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if(!$pos_payment_id = $this->pos_payment->insert($values)){
            $this->_error = 'server_error';
            return false;
        }

        return $pos_payment_id;

    }


    /**
     * Generate New Payment
     */
    protected function _generate_new_payment($pos_invoice_id,$pos_invoice_items)
    {

        $paid_amount = $this->input->post('amount_paid');
        $change = floatval(str_replace(',','',$this->input->post('change')));
        $sub_total = array_reduce($pos_invoice_items, function ($current_total, $pos_invoice_item) {
            return $current_total + $pos_invoice_item->amount;
        });

        $grand_total = $sub_total;
        $payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('payment_date'));

        $values = [
            'pos_invoice_id' => $pos_invoice_id,
            'grand_total' => $grand_total,
            'paid_amount' => $paid_amount,
            'change' => $change,
            'payment_date' => $payment_date->format("Y-m-d"),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if(!$pos_payment_id = $this->pos_payment->insert($values)){
            $this->_error = 'server_error';
            return false;
        }

        return $pos_payment_id;

    }


    /**
     * Get Item Data
     */
    protected function _get_pos_item_data()
    {
        $data = [];
        $pos_invoice_item_ids = $this->_get_pos_invoice_item_ids();

        foreach ($pos_invoice_item_ids as $pos_invoice_item_id) {
            $pos_item_id = $this->input->post('pos_item_id_' . $pos_invoice_item_id);
            $qty = $this->input->post('qty_' . $pos_invoice_item_id);
            $price = floatval(str_replace(',','',$this->input->post('price_' . $pos_invoice_item_id)));
            $added_by = $_SESSION['user']->id;

            $data[] = [
                'id' => is_numeric($pos_invoice_item_id) ? $pos_invoice_item_id : null,
                'pos_item_id' => $pos_item_id,
                'qty' => $qty,
                'price' => $price,
                'amount' => $qty * $price,
                'added_by' => $added_by
            ];
        }

        return $data;
    }

    /**
     * Get Input Suffices
     */
    protected function _get_pos_invoice_item_ids()
    {
        $pos_invoice_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'order_') === 0) {
                $pos_invoice_item_ids[] = $value;
            }
        }

        return $pos_invoice_item_ids;
    }



    /**
     * Delete POS Invoice main
     */
    public function delete()
    {
        $pos_invoice_id = $this->input->post('pos_invoice_id');

        $where = [
            'id' => $pos_invoice_id,
            'is_deleted' => 0
        ];
        if (!$pos_invoice = $this->pos_invoice->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'POS Invoice is not found.'
            ];
        } elseif (!$this->_attempt_delete($pos_invoice)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'POS Invoice is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Load Essentials
     */
    protected function _load_essentials()
    {
        $this->load->model('pos_invoice');
        $this->load->model('pos_invoice_item');
        $this->load->model('pos_payment');
        $this->load->model('pos_item');
        $this->load->model('customer');
        $this->load->model('supplier');

        $this->lang->load('error_messages', 'english');
        $this->load->library('upload');
        $this->config->load('validation_rules');
    }


    /**
     * POS INVOICE CASH
     */
    public function cash()
    {
        $this->title = 'POS Invoice Cash';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/select2/dist/js/select2.full',
            'lib/alertify/build/alertify',
            'js/pos_invoices/cash'
        ];

        $this->build_content('default', 'pos_invoices/cash');
    }

     /**
     * POS INVOICE Sales Report
     */
    public function sales_report()
    {
        $this->title = 'POS Sales Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/pos_invoices/sales_report'
        ];

        $this->build_content('default', 'pos_invoices/sales_report');
    }


    /**
     * POS INVOICE Credit
     */
    public function credit()
    {
        $this->title = 'POS Invoice Credit';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/select2/dist/js/select2.full',
            'lib/alertify/build/alertify',
            'js/pos_invoices/credit'
        ];

        $this->build_content('default', 'pos_invoices/credit');
    }


    /**
     * POS invoice Print Preview
     */
    public function preview($pos_invoice_id)
    {
        $pos_invoice = $this->pos_invoice->get_by_id($pos_invoice_id) or show_404();

        $this->title = 'Print Preview: POS Invoice No. ' . $pos_invoice->id;
        $this->css = [
            'lib/alertify/build/css/alertify',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2',
            'lib/input-autosize/dist/autosize',
            'js/pos_invoice/preview'
        ];

        $pos_invoice_items = $this->pos_invoice_item->get_by_pos_invoice($pos_invoice_id);

        $data = [
            'pos_invoice' => $pos_invoice,
            'pos_invoice_items' => $pos_invoice_items,
            'customer_balance' => $this->customer->pos_customer_balance($pos_invoice->customer_id)
        ];
        $this->build_content('blank', 'pos_invoices/preview', $data);
    }


        /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return isset($_FILES) ? $this->upload->display_errors() : '';

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }



    /**
     * Attempt delete
     */
    protected function _attempt_delete($pos_invoice)
    {
        $reason = $this->input->post('reason');
        $reason = str_replace('%20', ' ', $reason);

        $where = [
            'id' => $pos_invoice->id
        ];

        $values = [
            'is_deleted' => 1,
            'is_deleted_reason' => $reason,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id
        ];


        if (!$this->pos_invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return true;
    }

}
