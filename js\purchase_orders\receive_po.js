+ function ($) {
	'use strict';

	function setItemList(order_slip_item) {
		purchase_order_item.find('select[name^="item"]').select2({
		  tags: false,
		  allowClear: true,
		  placeholder: $(this).attr('data-placeholder'),
		  width: '250px',
		  ajax: {
		      url: BASE_URI + 'items/search_by_item',
		      dataType: "json",
		      type: "GET",
		      data: function (params) {
		          var queryParameters = {
		              term: params.term
		          }
		          return queryParameters;
		      },
		      processResults: function (data) {
		          return {
		              results: $.map(data, function (item) {
		                  return {
		                      text: item.name,
		                      id: item.id
		                  }
		              })
		          };
		      }
		  } 
		});
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="purchase_order_item">' +
				'<td>' +
				'<input type="hidden" name="purchase_order_item_' +
				rowId +
				'" value="' +
				rowId +
				'">' +
				'<select name="item_' +
				rowId +
				'" class="form-control dropsearch" data-placeholder=""></select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="qty_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="text" name="unit_' + rowId + '" class="form-control go-suggest" value="" data-suggest="units" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="pref_alw_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="current_price_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="est_due_amount_' + rowId + '" class="form-control" value="" min="0" step="any" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="act_qty_' + rowId + '" class="form-control" value="" min="0" step="any" >' +
				'</td>' +
                '<td>' +
					'<input type="number" name="act_alw_' + rowId + '" class="form-control" value="" min="0" step="any" >' +
				'</td>' +
                '<td>' +
					'<input type="number" name="act_due_amount_' + rowId + '" class="form-control" value="" min="0" step="any" >' +
				'</td>' +
			'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function computeRowActDueAmount($row){
		var act_qty = parseFloat($row.find('input[name^="act_qty_"]').val());
		if(isNaN(act_qty)){
			act_qty = 0;
		}
		var act_alw = $row.find('input[name^="act_alw_"]').val();
		if(isNaN(act_alw)){
			act_alw = 0;
		}
		var currentPrice = $row.find('input[name^="current_price_"]').val();
		if(isNaN(currentPrice)){
			currentPrice = 0;
		}
		var actDueAmount = act_qty * act_alw * currentPrice;

		$row.find('input[name^="act_due_amount_"]').val(actDueAmount);
	}

	$(function () {
		$('.item-table')
			.on("input", 'input[name^="act_qty_"]', function(event){
                var $row = $(event.currentTarget).closest('.purchase_order_item');
                console.log($row);
				computeRowActDueAmount($row);
			})
			.on("input",'input[name^="act_alw_"]', function(event){
				var $row = $(event.currentTarget).closest('.purchase_order_item');
				computeRowActDueAmount($row);
			})
	});
}(jQuery);
