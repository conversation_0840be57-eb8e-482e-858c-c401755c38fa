<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Processing_fees extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('processing_fee');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch processing_fees
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'processing_fee';
        $primary_key = 'processing_fee.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_number(),
            $this->_get_processing_number(),
            $this->_get_grand_total(),
            $this->_get_processing_fee_date(),
            $this->_get_processing_fee_number(),
            $this->_get_customer(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
processing_fee
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user ON user.id = processing_fee.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        // var_dump($status);
        // die();
        $where = <<<EOT
processing_fee.is_deleted = 0
EOT;            

        if ($status !== 'all') {
            $where .= <<<EOT

AND processing_fee.status = "$status"
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'processing_fee.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'processing_fee.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'processing_fee';
            }
        ];
    }

    /**
     * Get processing_fee number
     */
    protected function _get_processing_fee_number()
    {
        return [
            'db' => 'processing_fee.id',
            'as' => 'processing_fee_number',
            'dt' => 0,
            'field' => 'processing_fee_number'
        ];
    }

    /**
     * Get order slip number
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'processing_fee.order_slip_id',
            'as' => 'order_slip_number',
            'dt' => 1,
            'field' => 'order_slip_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get processing number
     */
    protected function _get_processing_number()
    {
        return [
            'db' => 'processing_fee.processing_id',
            'as' => 'processing_number',
            'dt' => 2,
            'field' => 'processing_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("processings/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get grand total
     */
    protected function _get_grand_total()
    {
        return [
            'db' => 'processing_fee.grand_total',
            'as' => 'grand_total',
            'dt' => 3,
            'field' => 'grand_total',
            'formatter' => function($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get processing_fee date
     */
    protected function _get_processing_fee_date()
    {
        return [
            'db' => 'DATE_FORMAT(processing_fee.processing_fee_date, "%b %d, %Y")',
            'as' => 'processing_fee_date',
            'dt' => 4,
            'field' => 'processing_fee_date'
        ];
    }

    /**
     * Get processing_fee customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 5,
            'field' => 'customer'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'processing_fee.remarks',
            'dt' => 6,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(processing_fee.is_deleted = 0, processing_fee.status, "cancelled")',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 7,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'processing_fee.id',
            'as' => 'file',
            'dt' => 7,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->processing_fee->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/processing_fees/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'processing_fee.id',
            'as' => 'actions',
            'dt' => 8,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        $res .= anchor('sales_orders/add/' . $d, 'Sales Order', 'title="Confirm Order" class="dropdown-item text-left green-text"');
                        // $res .= anchor('sales_orders/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        // $res .= anchor('processings/add/' . $d, 'Process', 'title="Confirm Order" class="dropdown-item text-left green-text"');
                        $res .= anchor('processing_fees/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('processing_fees/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } else {
                        $res .= anchor('processing_fees/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
