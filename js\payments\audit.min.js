!function(t){"use strict";function e(e){return new Promise((e,a)=>{var n='<input type="radio" name="auditStatus" value="Complete" checked> Complete &nbsp; &nbsp;';n+='<input type="radio" name="auditStatus" value="Incomplete"> Incomplete<br>',n+="&nbsp;",n+='<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100"></textarea>',n+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>',alertify.confirm(n,function(n,s){var i=t("#auditRemarks").val(),r=t('input[name="auditStatus"]:checked').val(),u=t("#attachments")[0].files;""!==i.trim()?e({remarks:i,status:r,files:u}):a("Missing Remarks: Audit remarks cannot be empty.")},function(){a("Audit canceled.")}).setting({reverseButtons:!0,closable:!1,title:"Audit Payment"}).show()})}function a(e,a,n,s){var i=new FormData;i.append("payment_id",e),i.append("audit_remarks",a),i.append("audit_status",n);for(var r=0;r<s.length;r++)i.append("attachments[]",s[r]);return t.ajax({url:BASE_URI+"payments/mark_audited/",type:"POST",data:i,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Payment Audited."}))}function n(e,a){return new Promise((e,n)=>{var s='<input type="radio" name="auditStatus" value="Complete"> Complete &nbsp; &nbsp;';s+='<input type="radio" name="auditStatus" value="Incomplete" checked> Incomplete<br>',s+="&nbsp;",s+='<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100">'+a+"</textarea>",s+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>',alertify.confirm(s,function(a,s){var i=t("#auditRemarks").val(),r=t('input[name="auditStatus"]:checked').val(),u=t("#attachments")[0].files;""!==i.trim()?e({remarks:i,status:r,files:u}):n("Missing Remarks: Audit remarks cannot be empty.")},function(){n("Audit canceled.")}).setting({reverseButtons:!0,closable:!1,title:"Audit Payment"}).show()})}function s(e,a,n,s){var i=new FormData;i.append("payment_id",e),i.append("audit_remarks",a),i.append("audit_status",n);for(var r=0;r<s.length;r++)i.append("attachments[]",s[r]);return t.ajax({url:BASE_URI+"payments/update_mark_audited/",type:"POST",data:i,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Payment Audited."}))}t(function(){t("#auditBtn").click(function(){var n=t('input[name="payment_id"]').val();e(n).then(({remarks:t,status:e,files:s})=>a(n,t,e,s)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})}),t(function(){t("#auditUpdateBtn").click(function(){var e=t('input[name="payment_id"]').val(),a=t('input[name="audit_remarks"]').val();n(e,a).then(({remarks:t,status:a,files:n})=>s(e,t,a,n)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})})}(jQuery);