---
layout: 'default'
hljs: 	'light'
---
<section class="blue">
	<div class="content">
		<div class="grid two">
			<div class="column">
				<h1> Getting Started </h1>
				 A quick guide of how to download and use AlertifyJS.
			</div>		
			<div class="right column">
			  <%- @partial('ad') %>
			</div>
		</div>
	</div>
</section>
<section class="lic">
    <div class="content">
        Looking for a commercial license ?  Keep your source code proprietary and <a href="https://www.uplabs.com/posts/alertifyjs" target="_blank"> Buy a Commercial License Today!</a>
    </div>
</section>
<section class="dark">
<div class="content">

	  <!-- Download -->
	  <h1>
		<i class="fa fa-cloud-download fa-lg"></i> Download (v<%=@getPackage().version%>)
	  </h1>
	  <hr />
	  <div class="grid two">
		<div class="column">
		  <h2> AlertifyJS</h2>
		  <div>Compiled and minified CSS, JavaScript. No docs or original source files are included.</div>
		  
		  <div style="margin:35px 0;">
		   <a class="button" href="/build/alertifyjs.zip">
			<span>Download Library</span>
		   </a>
		  </div>
  	
		  	<h4>Contents</h4>			
<pre>
<code class="lang-bash">alertifyjs/
├── css/
│   ├── alertify.css
│   ├── alertify.min.css
│   ├── alertify.rtl.css
│   ├── alertify.rtl.min.css
│   └── themes/
│   	├── bootstrap.css
│   	├── bootstrap.min.css
│   	├── bootstrap.rtl.css
│   	├── bootstrap.rtl.min.css
│   	├── default.css
│   	├── default.min.css
│   	├── default.rtl.css
│   	├── default.rtl.min.css
│   	├── semantic.css
│   	├── semantic.min.css
│   	├── semantic.rtl.css
│   	└── semantic.min.rtl.css
├── alertify.js
└── alertify.min.js
</code>
</pre>
		</div>		
		<div class="column">
		  <h2>Source Code</h2>
		  Source Javascript, Less and AlertifyJS docs (see steps for running docs locally).
		  <div style="margin:35px 0;">
		    <a class="button" href="https://github.com/MohammadYounes/AlertifyJS/archive/master.zip">
			  <span>Download Source</span>
		    </a>
		  </div>
		  <h4>Contents</h4>			
<pre>
<code class="lang-bash">alertifyjs/
│
├── build/ # <= build output
│   └── css/
│   	└── themes/
│
│
├── docpad/ # <= documentation server
│   ├── data/
│   ├── documents/
│   ├── files/
│   ├── layouts/
│   └── partials/
│
│
│
└── src # <= AlertifyJS source files
   ├── js/
   │  └── dialog/
   └── less/
      └── themes/
</code>
</pre>	
		</div>
	 </div>


<div class="segment">
    <h2 id="cdn">AlertifyJS CDN</h2>
    <p>Latest compiled and minified JavaScript and CSS files are hosted on <a href="http://www.jsdelivr.com/#!alertifyjs" target="_blank">cdn.jsdelivr.net</a> </p>
 
<pre>
<code class="html">
<!-- JavaScript -->
<script src="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/alertify.min.js"></script>

<!-- CSS -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/alertify.min.css"/>
<!-- Default theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/default.min.css"/>
<!-- Semantic UI theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/semantic.min.css"/>
<!-- Bootstrap theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/bootstrap.min.css"/>

<!-- 
    RTL version
-->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/alertify.rtl.min.css"/>
<!-- Default theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/default.rtl.min.css"/>
<!-- Semantic UI theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/semantic.rtl.min.css"/>
<!-- Bootstrap theme -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@<%=@getPackage().version%>/build/css/themes/bootstrap.rtl.min.css"/>

</code>
</pre>		  

</div>

	 
 <div class="grid two">
 	<div class="column double">
	  <h2>Install with NuGet</h2>
<pre>
<code class="lang-bash">
PM> Install-Package AlertifyJS

</code>
</pre>		  
	</div>
</div>
<div class="grid two">
    <div class="column double">
		  <h2>Install with NPM</h2>
<pre>
<code class="lang-bash">
npm install alertifyjs --save

</code>
</pre>		  
	</div>
</div>


	<!-- //Download -->


	<!-- Usage -->
	 <h1>
		<i class="fa fa-flash fa-lg"></i> Usage
	 </h1>
	 <hr />
	
		  <h3> <i class="fa fa-check"></i> Include JS </h3>
<pre>
<code class="lang-html">&lt;!-- include the script --&gt;
&lt;script src="{PATH}/alertify.min.js"&gt;&lt;/script&gt;
</code> 
</pre>
          <h3> <i class="fa fa-check"></i> Include CSS </h3>
<pre>
<code class="lang-html">&lt;!-- include the style --&gt;
&lt;link rel="stylesheet" href="{PATH}/alertify.min.css" /&gt;
&lt;!-- include a theme --&gt;
&lt;link rel="stylesheet" href="{PATH}/themes/default.min.css" /&gt;
</code> 
</pre>



	<div class="segment">
            <h3> <i class="fa fa-check"></i> Start coding!</h3>
<pre>
<code class="lang-javascript">alertify.alert('Ready!');
</code> 
</pre>
          <div class="RIGHT">
            <a class="button example" href="javascript:void(0);">
              <span> Run </span>
            </a>
		  </div>
	</div>
  
  <!-- //Usage -->
  <!-- Defaults -->
  <div class="segment">
	<h1 id="defaults">
		<a href="#defaults"><i class="fa fa-book fa-lg"></i></a>
		Defaults
	</h1>
  <hr />
  <p> The following are the global defaults used by AlertifyJS, override with your own prior to components initialization.</p>

  <pre>
<code class="lang-javascript">
alertify.defaults = {
		// dialogs defaults
        autoReset:true,
        basic:false,
        closable:true,
        closableByDimmer:true,
        frameless:false,
        maintainFocus:true, // <== global default not per instance, applies to all dialogs
        maximizable:true,
        modal:true,
        movable:true,
        moveBounded:false,
        overflow:true,
        padding: true,
        pinnable:true,
        pinned:true,
        preventBodyShift:false, // <== global default not per instance, applies to all dialogs
        resizable:true,
        startMaximized:false,
        transition:'pulse',

		// notifier defaults
        notifier:{
			// auto-dismiss wait time (in seconds)  
            delay:5,
			// default position
            position:'bottom-right',
			// adds a close button to notifier messages
			closeButton: false
        },

		// language resources 
        glossary:{
			// dialogs default title
            title:'AlertifyJS',
			// ok button text
            ok: 'OK',
			// cancel button text
            cancel: 'Cancel'			
        },

		// theme settings
        theme:{
			// class name attached to prompt dialog input textbox.
            input:'ajs-input',
			// class name attached to ok button
            ok:'ajs-ok',
			// class name attached to cancel button 
            cancel:'ajs-cancel'
        }
    };

</code> 
</pre>
	</div>
	<!-- //Defaults -->

</div>
</section>