$(document).ready(function() {
    function gibletsRowTemplate() {
        return '<tr class="giblets-row">' +
            '<td><input type="text" class="form-control" name="sku[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="by[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="no[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="pcs[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="kgs[]" value=""></td>' +
            '<td class="text-center" style="vertical-align: middle;">' +
                '<button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>' +
            '</td>' +
        '</tr>';
    }

    $('#addGibletsRow').click(function() {
        $('#gibletsTableBody').append(gibletsRowTemplate());
    });

    $('#gibletsTableForm').on('click', '.action-delete-row', function() {
        var rowCount = $('#gibletsTableBody .giblets-row').length;
        if (rowCount > 1) {
            $(this).closest('.giblets-row').remove();
        } else {
            alert('At least one row is required.');
        }
    });
}); 