<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	exportEnabled: true,
	title:{
		text: "Gold Medals Won in Olympics"             
	}, 
	axisY:{
		title: "Number of Medals"
	},
	toolTip: {
		shared: true
	},
	legend:{
		cursor:"pointer",
		itemclick: toggleDataSeries
	},
	data: [{        
		type: "spline",  
		name: "US",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 44 },     
			{ label:"Sydney 2000", y: 37 },     
			{ label: "Athens 2004", y: 36 },     
			{ label: "Beijing 2008", y: 36 },     
			{ label: "London 2012", y: 46 },
			{ label: "Rio 2016", y: 46 }
		]
	}, 
	{        
		type: "spline",
		name: "China",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 16 },     
			{ label:"Sydney 2000", y: 28 },     
			{ label: "Athens 2004", y: 32 },     
			{ label: "Beijing 2008", y: 48 },     
			{ label: "London 2012", y: 38 },
			{ label: "Rio 2016", y: 26 }
		]
	},
	{        
		type: "spline",  
		name: "Britain",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 1 },     
			{ label:"Sydney 2000", y: 11 },     
			{ label: "Athens 2004", y: 9 },     
			{ label: "Beijing 2008", y: 19 },     
			{ label: "London 2012", y: 29 },
			{ label: "Rio 2016", y: 27 }
		]
	},
	{        
		type: "spline",  
		name: "Russia",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 26 },     
			{ label:"Sydney 2000", y: 32 },     
			{ label: "Athens 2004", y: 28 },     
			{ label: "Beijing 2008", y: 22 },     
			{ label: "London 2012", y: 20 },
			{ label: "Rio 2016", y: 19 }
		]
	},
	{        
		type: "spline",  
		name: "S Korea",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 7 },     
			{ label:"Sydney 2000", y: 8 },     
			{ label: "Athens 2004", y: 9 },     
			{ label: "Beijing 2008", y: 13 },     
			{ label: "London 2012", y: 13 },
			{ label: "Rio 2016", y: 9 }
		]
	},  
	{        
		type: "spline",  
		name: "Germany",        
		showInLegend: true,
		dataPoints: [
			{ label: "Atlanta 1996" , y: 20 },     
			{ label:"Sydney 2000", y: 13 },     
			{ label: "Athens 2004", y: 13 },     
			{ label: "Beijing 2008", y: 16 },     
			{ label: "London 2012", y: 11 },
			{ label: "Rio 2016", y: 17 }
		]
	}]
});

chart.render();

function toggleDataSeries(e) {
	if(typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	}
	else {
		e.dataSeries.visible = true;            
	}
	chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>