<?php
class Processing_fee extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'processing_fee';
        parent::__construct();
    }

    /**
     * Get processing_fee By ID
     */
    public function get_by_id($processing_fee_id)
    {
        $sql = <<<EOT
SELECT processing_fee.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, issuer.full_name AS issuer
FROM processing_fee
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing_fee.added_by
WHERE processing_fee.id = ?
    AND processing_fee.is_deleted = 0
EOT;
        $binds = [$processing_fee_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM processing_fee
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid processing_fees
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT processing_fee.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM processing_fee
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing_fee.added_by
WHERE processing_fee.paid_amount < processing_fee.total
    AND processing_fee.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get processing_fees Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT processing_fee.id, processing_fee.processing_fee_date, processing_fee.customer_id, processing_fee.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM processing_fee
LEFT JOIN (
    SELECT payment.payment_date, paid_processing_fee.processing_fee_id, paid_processing_fee.amount
    FROM payment
    LEFT JOIN paid_processing_fee ON paid_processing_fee.payment_id = payment.id
    LEFT JOIN processing_fee ON processing_fee.id = paid_processing_fee.processing_fee_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND processing_fee.customer_id = ?
) payment ON payment.processing_fee_id = processing_fee.id
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing_fee.added_by
WHERE processing_fee.processing_fee_date < ?
    AND customer.id = ?
    AND processing_fee.is_deleted = 0
GROUP BY processing_fee.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get processing_fees Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT processing_fee.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM processing_fee
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing_fee.added_by
WHERE processing_fee.processing_fee_date BETWEEN ? AND ?
    AND processing_fee.customer_id = ?
    AND processing_fee.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get processing_fees BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT processing_fee.id, processing_fee.processing_fee_no, processing_fee.terms, processing_fee.processing_fee_date, processing_fee.customer_id, processing_fee.total AS charge, processing_fee.total, processing_fee.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM processing_fee
LEFT JOIN (
    SELECT payment.payment_date, paid_processing_fee.processing_fee_id, paid_processing_fee.amount
    FROM payment
    LEFT JOIN paid_processing_fee ON paid_processing_fee.payment_id = payment.id
    LEFT JOIN processing_fee ON processing_fee.id = paid_processing_fee.processing_fee_id
    WHERE payment.payment_date
        AND processing_fee.customer_id = ?
) payment ON payment.processing_fee_id = processing_fee.id
LEFT JOIN live_sell_customer AS customer ON customer.id = processing_fee.customer_id
LEFT JOIN user AS issuer ON issuer.id = processing_fee.added_by
WHERE customer.id = ?
    AND processing_fee.is_deleted = 0
GROUP BY processing_fee.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */