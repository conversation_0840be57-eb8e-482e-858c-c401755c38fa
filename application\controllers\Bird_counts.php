<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Bird_counts extends MYT_Controller {

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('bird_count');
        $this->load->model('hauling_log');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Bird Counts';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/bird_counts/manager',
        ];
        

        $data = [
            'status' => $this->input->get('status', true) ?: 'active',
           
            
        ];

        $this->build_content('default', 'bird_counts/manager', $data);
    }

    /**
     * Preview bird count
     */
    public function preview($bird_count_id)
    {
        $bird_count = $this->bird_count->get_by_id($bird_count_id) or show_404();

        $this->title = date('Y') . ' Live Sales Transaction Report';
        $this->javascript = [
            'js/bird_counts/preview'
        ];

        // Get bird count data with hauling info
        $bird_count_data = $this->bird_count->get_bird_count_with_details($bird_count_id);

        $data = [
            'bird_count' => $bird_count,
            'bird_count_data' => $bird_count_data
        ];

        $this->build_content('default', 'bird_counts/preview', $data);
    }

        public function add() {
        $rules = $this->config->item('bird_counts/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('bird_counts'));
        } else {
            $this->title = 'Add Bird Count';
            $this->javascript = [
                'js/bird_counts/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'grower_farms' => $this->bird_count->get_grower_farms_from_hauling_logs()
            ];
            $this->build_content('default', 'bird_counts/add', $data);
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $hauling_log_id = $this->input->post('hauling_log_id');

        // Get hauling log info to populate trip_no
        $hauling_log_info = $this->hauling_log->get_hauling_log_info($hauling_log_id);
        if (!$hauling_log_info) {
            $this->db->trans_rollback();
            $this->_error = 'Invalid hauling log selected';
            return false;
        }

        $net_weight = floatval($hauling_log_info->net_weight);
        $shackle_count = floatval($this->input->post('shackle_heads'));

        // Calculate ALW (Average Live Weight)
        $alw = 0;
        if ($net_weight > 0 && $shackle_count > 0) {
            $alw = $net_weight / $shackle_count;
        }

        // Prepare bird count data
        $bird_count_data = [
            'hauling_log_id' => $hauling_log_id,
            'live_heads' => $this->input->post('live_heads'),
            'doa' => $this->input->post('doa_heads'),
            'daa' => $this->input->post('daa_heads'),
            'runts' => $this->input->post('runts_heads'),
            'rejected' => $this->input->post('rejected_heads'),
            'shackle_count' => $shackle_count,
            'alw' => $alw,
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        // Create bird count using built-in insert method
        $bird_count_id = $this->bird_count->insert($bird_count_data);

        if (!$bird_count_id) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $bird_count_id;
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }



    /**
     * Get trips for a specific customer (AJAX endpoint)
     */
    public function get_trips_by_customer($customer_id)
    {
        $trips = $this->hauling_log->get_trips_by_customer_for_bird_count($customer_id);

        echo json_encode([
            'success' => true,
            'trips' => $trips
        ]);
    }

    /**
     * Get hauling log info for net weight auto-fill (AJAX endpoint)
     */
    public function get_hauling_log_info($hauling_log_id)
    {
        $hauling_log_info = $this->hauling_log->get_hauling_log_info($hauling_log_id);

        if ($hauling_log_info) {
            echo json_encode([
                'success' => true,
                'hauling_log' => $hauling_log_info
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Hauling log not found'
            ]);
        }
    }

    /**
     * Delete bird count
     */
    public function delete($bird_count_id)
    {
        $where = [
            'id' => $bird_count_id,
            'is_deleted' => 0
        ];
        if (!$bird_count = $this->bird_count->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Bird Count is not found.'
            ];
        } elseif (!$this->_attempt_delete($bird_count)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Bird Count is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($bird_count)
    {
        $this->db->trans_begin();

        $condition = [
            'id' => $bird_count->id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->bird_count->update($condition, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return $is_deleted;
    }

}