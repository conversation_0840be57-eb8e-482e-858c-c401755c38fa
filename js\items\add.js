+ function ($) {
	'use strict';

	function previewDP(inputFile, defaultSrc) {
		var $preview = $('.img-preview');

		if (inputFile.files && inputFile.files[0]) {
			var reader = new FileReader();

			reader.onload = function (event) {
				$preview.attr('src', event.currentTarget.result);
			}

			reader.readAsDataURL(inputFile.files[0]);
		} else {
			$preview.attr('src', defaultSrc);
		}
	}

	$(function () {
		var $preview = $('.img-preview');
		var defaultSrc = $preview.attr('src');
		var $inputFile = $('input[name="image_upload"]');

		$preview.click(function () {
			$inputFile.click();
		});

		$inputFile.change(function (event) {
			previewDP(event.currentTarget, defaultSrc);
		});
	});
}(jQuery);
