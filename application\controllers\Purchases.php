<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Corp Purchase Orders
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class Purchases extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('purchases/manager'));
    }

    /**
     * Add Purchase Order
     */
    public function add()
    {
        $this->db->trans_begin();

        $purchase_item_ids = $this->_get_purchase_item_ids();
        $rules             = array_merge($this->config->item('purchases/add'), $this->_get_purchase_item_rules('', $purchase_item_ids));

        $config = [
            'upload_path'   => FCPATH . 'assets/asylum',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size'      => 0,
            'overwrite'     => false,
            'encrypt_name'  => false,
        ];

        if ($this->_validate_form($rules) AND $this->_attempt_upload($config, 'attachment', true) AND $purchase_id = $this->_attempt_add()) {
            $new_path = FCPATH . 'assets/pos/' . $purchase_id . '/';
            $this->_move_uploaded($new_path);
            $this->db->trans_commit();

            if ($_SESSION['user']->role_id === '1') {
                redirect(site_url('purchases/review/' . $purchase_id));
            } else {
                redirect(site_url('purchases/manager'));
            }
        } else {
            $this->db->trans_rollback();
            $this->_delete_uploaded();
            $this->title = 'Add Purchase Order';
            $this->css   = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/alertify/build/css/alertify',
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/alertify/build/alertify',
                'js/purchases/add',
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error'        => $form_error,
                'purchase_item_ids' => $purchase_item_ids,
            ];

            $this->css[] = 'lib/select2/dist/css/select2';

            $this->javascript[] = 'lib/select2/dist/js/select2.full';

            $where = [
                'is_deleted' => 0,
            ];
            $usages = $this->usage->select('', $where, '', 'name ASC');

            $data['main_usages'] = array_map(function ($usage) {
                return $usage->name;
            }, $usages);
            $data['main_usages'] = array_filter($data['main_usages']);
            $data['main_usages'] = array_map('trim', $data['main_usages']);
            $data['main_usages'] = array_unique($data['main_usages'], SORT_REGULAR);

            $where = [
                'is_deleted' => 0,
            ];
            $sub_usages = $this->sub_usage->select('', $where, '', 'name ASC');

            $data['sub_usages'] = array_map(function ($sub_usage) {
                return $sub_usage->name;
            }, $sub_usages);
            $data['sub_usages'] = array_filter($data['sub_usages']);
            $data['sub_usages'] = array_map('trim', $data['sub_usages']);
            $data['sub_usages'] = array_unique($data['sub_usages'], SORT_REGULAR);

            $where = [
                'is_deleted' => 0,
                'is_active'  => 1,
            ];
            $suppliers         = $this->supplier->select('', $where, '', 'name');
            $data['suppliers'] = $suppliers;

            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0,
            ];
            $data['account_types'] = $this->account_type->select('', $where);

            $this->build_content('default', 'purchases/add', $data);
        }
    }

    /**
     * Edit Purchase Order
     */
    public function edit($purchase_id)
    {
        $this->db->trans_begin();

        $purchase = $this->purchase->get_by_id($purchase_id) or show_404();

        $purchase_items = $this->purchase_item->get_by_purchase($purchase->id);

        $rules = $this->config->item('purchases/edit');
        if (!$purchase_item_ids = $this->_get_purchase_item_ids()) {
            $existing_ids = array_map(function ($purchase_item) {
                return $purchase_item->id;
            }, $purchase_items);

            $item_rules = $this->_get_purchase_item_rules($purchase->id, $existing_ids);
        } else {
            $item_rules = $this->_get_purchase_item_rules($purchase->id, $purchase_item_ids);
        }

        $rules = array_merge($rules, $item_rules);

        $config = [
            'upload_path'   => FCPATH . 'assets/asylum',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic',
            'max_size'      => 0,
            'overwrite'     => false,
            'encrypt_name'  => false,
        ];

        if ($this->_validate_form($rules) AND $this->_attempt_upload($config, 'attachment') AND $this->_attempt_edit($purchase)) {
            $new_path = FCPATH . 'assets/pos/' . $purchase->id . '/';
            $this->_move_uploaded($new_path);
            $this->db->trans_commit();

            if ($_SESSION['user']->role_id === '1') {
                redirect(site_url('purchases/review/' . $purchase->id));
            } else {
                redirect(site_url('purchases/edit/' . $purchase->id));
            }
        } else {
            $this->db->trans_rollback();
            $this->_delete_uploaded();

            $this->title = 'Edit Purchase Order';
            $this->css   = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/alertify/build/css/alertify',
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/alertify/build/alertify',
                'js/purchases/add',
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';

            }
            $data = [
                'form_error'        => $form_error,
                'purchase_item_ids' => $purchase_item_ids,
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error'        => $form_error,
                'purchase'          => $purchase,
                'purchase_items'    => $purchase_items,
                'purchase_item_ids' => $purchase_item_ids,
            ];

            $this->css[] = 'lib/select2/dist/css/select2';

            $this->javascript[] = 'lib/select2/dist/js/select2.full';

            $where = [
                'is_deleted' => 0,
            ];
            $usages = $this->usage->select('', $where, '', 'name ASC');

            $data['main_usages'] = array_map(function ($usage) {
                return $usage->name;
            }, $usages);
            $data['main_usages'] = array_filter($data['main_usages']);
            $data['main_usages'] = array_map('trim', $data['main_usages']);
            $data['main_usages'] = array_unique($data['main_usages'], SORT_REGULAR);

            $where = [
                'is_deleted' => 0,
            ];
            $sub_usages = $this->sub_usage->select('', $where, '', 'name ASC');

            $data['sub_usages'] = array_map(function ($sub_usage) {
                return $sub_usage->name;
            }, $sub_usages);
            $data['sub_usages'] = array_filter($data['sub_usages']);
            $data['sub_usages'] = array_map('trim', $data['sub_usages']);
            $data['sub_usages'] = array_unique($data['sub_usages'], SORT_REGULAR);

            $where = [
                'is_deleted' => 0,
            ];
            $suppliers         = $this->supplier->select('', $where, '', 'name');
            $data['suppliers'] = $suppliers;

            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0,
            ];
            $data['account_types'] = $this->account_type->select('', $where);

            $this->build_content('default', 'purchases/edit', $data);
        }
    }

    /**
     * Add Purchase Order Attachment
     */
    public function add_attachment($purchase_id)
    {
        $where = [
            'id' => $purchase_id,
        ];
        ($purchase = $this->purchase->select('', $where, 1) and $purchase->status !== 'pending') or show_404();
        $rules = $this->config->item('purchases/add_attachment');

        $config = [
            'upload_path'   => FCPATH . 'assets/asylum',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size'      => 0,
            'overwrite'     => false,
            'encrypt_name'  => false,
        ];

        if ($this->_attempt_upload($config, 'attachment', true) and $this->_validate_form($rules) and $this->_attempt_add_attachment($purchase)) {
            $new_path = FCPATH . 'assets/pos/' . $purchase_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('purchases/manager'));
        } else {
            $this->_delete_uploaded();
            $this->title = 'Add Purchase Order Attachment';
            $this->css   = [
                'lib/alertify/build/css/alertify',
            ];
            $this->javascript = [
                'lib/alertify/build/alertify',
                'lib/input-autosize/dist/autosize',
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'purchase'   => $purchase,
            ];

            $this->build_content('default', 'purchases/add_attachment', $data);
        }
    }

    /**
     * Attempt Add Attachment
     */
    protected function _attempt_add_attachment($purchase)
    {
        $where = [
            'id'         => $purchase->id,
            'is_deleted' => 0,
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];

            if (!empty($purchase->attachment)) {
                $attachments[] = $purchase->attachment;
            }

            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $new_attachment = implode('🔥', $attachments);

            $values = [
                'attachment' => $new_attachment,
            ];
        } else {
            return false;
        }

        if (!$is_updated = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return $is_updated;
    }

    /**
     * Recommend Purchase Order for Approval
     */
    public function recommend($purchase_id)
    {
        $purchase = $this->purchase->get_by_id($purchase_id) or show_404();

        $this->title = 'Recommend Purchase Order No. ' . $purchase->id;
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchases/recommend',
        ];

        $purchase_items = $this->purchase_item->get_by_purchase($purchase->id);

        $data = [
            'purchase'       => $purchase,
            'purchase_items' => $purchase_items,
        ];
        $this->build_content('default', 'purchases/recommend', $data);
    }

    /**
     * Review Purchase Order for Approval
     */
    public function review($purchase_id)
    {
        $purchase = $this->purchase->get_by_id($purchase_id) or show_404();

        $this->title = 'Review Purchase Order No. ' . $purchase->id;
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchases/review',
        ];

        $purchase_items = $this->purchase_item->get_by_purchase($purchase->id);

        $data = [
            'purchase'       => $purchase,
            'purchase_items' => $purchase_items,
        ];
        $this->build_content('default', 'purchases/review', $data);
    }

    /**
     * Approve Purchase Order Item
     */
    public function approve_item($purchase_item_id)
    {
        $where = [
            'id'         => $purchase_item_id,
            'is_deleted' => 0,
        ];
        if (!$purchase_item = $this->purchase_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.',
            ];
        } elseif (!$this->_attempt_approve_item($purchase_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully approved.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Disapprove Purchase Order Item
     */
    public function disapprove_item($purchase_item_id)
    {
        $where = [
            'id'         => $purchase_item_id,
            'is_deleted' => 0,
        ];

        if (!$purchase_item = $this->purchase_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.',
            ];
        } elseif (!$this->_attempt_disapprove_item($purchase_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully disapproved.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Pending Purchase Order Item
     */
    public function pending_item($purchase_item_id)
    {
        $where = [
            'id'         => $purchase_item_id,
            'is_deleted' => 0,
        ];

        if (!$purchase_item = $this->purchase_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.',
            ];
        } elseif (!$this->_attempt_pending_item($purchase_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully pending.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order is For Approval
     */
    public function mark_for_approval($purchase_id)
    {
        $where = [
            'id'         => $purchase_id,
            'status'     => 'pending',
            'is_deleted' => 0,
        ];
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_mark_for_approval($purchase)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as for approval.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order is Approved
     */
    public function mark_approved($purchase_id)
    {
        $where = <<<EOT
id = $purchase_id
AND status IN("pending", "for approval")
AND is_deleted = 0
EOT;
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_mark_approved($purchase)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as approved.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order is Approved
     */
    public function mark_verified($purchase_id)
    {
        $where = <<<EOT
id = $purchase_id
AND is_verified = 0
AND printed_by > 0
AND is_deleted = 0
EOT;
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_mark_verified($purchase)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as verified.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order is Printed
     */
    public function mark_printed($purchase_id)
    {
        $where = [
            'id'         => $purchase_id,
            'status <>'  => 'pending',
            'is_deleted' => 0,
        ];
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_mark_printed($purchase)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as printed.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order Print Preview
     */
    public function preview($purchase_id, $type)
    {
        $purchase = $this->purchase->get_by_id($purchase_id);
        ($purchase and $purchase->status !== 'pending') or show_404();

        $where = [
            'id'         => $purchase->check_slip_id,
            'is_deleted' => 0,
        ];
        $check = $this->check_slip->select('', $where, 1);

        // if ($check !== null) {
        //     $bank = $this->bank->select('',['id' => $check->bank_id, 'is_deleted' => 0], 1);
        // }

        if ($check) {
            $bank            = $this->bank->select('', ['id' => $check->bank_id, 'is_deleted' => 0], 1);
            $check->{'bank'} = $bank !== null ? $bank->name : "";
        }

        if ($type === "Release" and $purchase->status !== 'for release') {
            redirect(site_url('purchases/manager?status=for release'));
        } else if ($type === "Audit" and $purchase->status !== 'released') {
            redirect(site_url('purchases/manager?status=released'));
        }

        $this->title = 'Print Preview: Purchase Order No. ' . $purchase->id;
        $this->css   = [
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/purchases/preview',
        ];

        $where = [
            'id' => $purchase->supplier_id,
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $purchase_items = $this->purchase_item->get_by_purchase($purchase_id);

        $where = [
            'purchase_id' => $purchase->id,
            'is_deleted'  => 0,
        ];
        $purchase_or = $this->purchase_or->select('', $where, 1);

        if (!empty($purchase_or)) {
            $where = [
                'purchase_or_id' => $purchase_or->id,
                'is_deleted'     => 0,
            ];
            $or_attachments = $this->purchase_or_attachment->select('', $where);
        } else {
            $or_attachments = null;
        }

        $data = [
            'purchase'       => $purchase,
            'supplier'       => $supplier,
            'purchase_items' => $purchase_items,
            'type'           => $type,
            'or_attachments' => $or_attachments,
            'check'          => $check,
        ];

        $this->build_content('default', 'purchases/preview', $data);
    }

    /**
     * Purchase Order Print Preview
     */
    public function preview_update($purchase_id, $type)
    {
        $purchase = $this->purchase->get_by_id($purchase_id);
        ($purchase and $purchase->status !== 'pending') or show_404();

        $where = [
            'id'         => $purchase->check_slip_id,
            'is_deleted' => 0,
        ];
        $check = $this->check_slip->select('', $where, 1);

        // if ($check !== null) {
        //     $bank = $this->bank->select('',['id' => $check->bank_id, 'is_deleted' => 0], 1);
        // }

        if ($check) {
            $bank            = $this->bank->select('', ['id' => $check->bank_id, 'is_deleted' => 0], 1);
            $check->{'bank'} = $bank !== null ? $bank->name : "";
        }

        $this->title = 'Print Preview: Purchase Order No. ' . $purchase->id;
        $this->css   = [
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/purchases/preview_update',
        ];

        $where = [
            'id' => $purchase->supplier_id,
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $purchase_items = $this->purchase_item->get_by_purchase($purchase_id);
        $purchase_audit_attachments = $this->purchase_audit_attachment->get_attachment_by_purchase_id($purchase_id);

        $where = [
            'purchase_id' => $purchase->id,
            'is_deleted'  => 0,
        ];
        $purchase_or = $this->purchase_or->select('', $where, 1);

        if (!empty($purchase_or)) {
            $where = [
                'purchase_or_id' => $purchase_or->id,
                'is_deleted'     => 0,
            ];
            $or_attachments = $this->purchase_or_attachment->select('', $where);
        } else {
            $or_attachments = null;
        }

        $data = [
            'purchase'       => $purchase,
            'supplier'       => $supplier,
            'purchase_items' => $purchase_items,
            'type'           => $type,
            'or_attachments' => $or_attachments,
            'check'          => $check,
            'purchase_audit_attachments' => $purchase_audit_attachments,
        ];

        $this->build_content('default', 'purchases/preview_update', $data);
    }

    /**
     * Purchase Order Print Preview
     */
    public function simple_preview($purchase_id, $type)
    {
        $purchase = $this->purchase->get_by_id($purchase_id);
        ($purchase and $purchase->status !== 'pending') or show_404();

        $where = [
            'id' => $purchase_id,
        ];

        $po = $this->purchase->select('', $where, 1);

        if ($type == "Printing" and $po->status != 'check issued') {
            redirect(site_url('purchases/manager?status=check%20issued'));
        } else if ($type == "Releasing" and $po->status != 'printed') {
            redirect(site_url('purchases/manager?status=printed'));
        }

        $this->title = 'Print Preview: Purchase Order No. ' . $purchase->id;

        $this->css = [
            'lib/alertify/build/css/alertify',
        ];

        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/purchases/simple_preview',
        ];

        $where = [
            'id' => $purchase->supplier_id,
        ];

        $supplier = $this->supplier->select('', $where, 1);

        $purchase_items = $this->purchase_item->get_by_purchase($purchase_id);

        $data = [
            'purchase'       => $purchase,
            'supplier'       => $supplier,
            'purchase_items' => $purchase_items,
            'status'         => $type,
        ];

        $this->build_content('default', 'purchases/simple_preview', $data);
    }

    /**
     * Purchase Order Preview Release
     */
    public function preview_release($purchase_id)
    {
        $purchase = $this->purchase->get_by_id($purchase_id);
        ($purchase and $purchase->status === 'for release') or show_404();

        $where = [
            'id'         => $purchase->check_slip_id,
            'is_deleted' => 0,
        ];
        $check           = $this->check_slip->select('', $where, 1);
        $bank            = $this->bank->select('', ['id' => $check->bank_id, 'is_deleted' => 0], 1);
        $check->{'bank'} = $bank->name;

        $rules = $this->config->item('purchases/release');

        $config = [
            'upload_path'   => FCPATH . 'assets/purchase_or',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size'      => 0,
            'overwrite'     => false,
            'encrypt_name'  => false,
        ];

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && $this->_attempt_release($purchase)) {
            $new_path = FCPATH . 'assets/purchase_or/' . $purchase_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('purchases/manager'));
        } else {

            $this->title      = 'FOR RELEASE: Purchase Order No. ' . $purchase->id;
            $this->javascript = [
                'js/purchases/preview_release',
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'id' => $purchase->supplier_id,
            ];
            $supplier = $this->supplier->select('', $where, 1);

            $purchase_items = $this->purchase_item->get_by_purchase($purchase_id);

            $data = [
                'form_error'     => $form_error,
                'purchase'       => $purchase,
                'supplier'       => $supplier,
                'purchase_items' => $purchase_items,
                'check'          => $check,
            ];

            $this->build_content('default', 'purchases/preview_release', $data);
        }
    }

    /**
     * Delete Purchase Order
     */
    public function delete($purchase_id, $delete_reason)
    {
        $where = [
            'id'         => $purchase_id,
            'is_deleted' => 0,
        ];

        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_delete($purchase, $delete_reason)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully removed.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Delete Purchase Order Attachment
     */
    public function delete_attachment($purchase_id, $file_name = null)
    {
        $file_name = $file_name ? $file_name : $this->input->post('file_name');

        $where = [
            'id'         => $purchase_id,
            'is_deleted' => 0,
        ];

        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order Attachment is not found.',
            ];
        } elseif (!$this->_attempt_delete_attachment($purchase, $file_name)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Attachment is successfully removed.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Return to Pending Purchase Order
     */
    public function return_to_pending($purchase_id)
    {
        $where = [
            'id' => $purchase_id,
        ];
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_pending($purchase_id)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully changed to PENDING.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Mark Urgent Purchase Order
     */
    public function mark_urgent($purchase_id)
    {
        $where = [
            'id' => $purchase_id,
        ];
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } elseif (!$this->_attempt_urgent($purchase_id)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg(),
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully changed to URGENT.',
            ];
        }

        echo json_encode($response);
    }

    /**
     * Add Check Number
     */
    public function add_check_no($purchase_id)
    {
        $where = [
            'id'         => $purchase_id,
            'is_deleted' => 0,
        ];
        $purchase = $this->purchase->select('', $where, 1) or show_404();

        $rules = $this->config->item('purchases/add_check_no');

        if ($this->_validate_form($rules) && $this->_attempt_add_check_no($purchase)) {
            redirect(site_url('purchases/manager?status=printed'));
        } else {
            $this->title = 'Add Check No: PO No. ' . $purchase->id;

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'purchase'   => $purchase,
            ];
            $this->build_content('default', 'purchases/add_check_no', $data);
        }
    }

    /**
     * Get All Approved POs
     */
    public function approved($supplier_id = null)
    {
        if (!$purchases = $this->purchase->get_approved()) {
            http_response_code(400);
            $response = [
                'message' => 'No PO is found.',
            ];
        } else {
            $response = [
                'message'   => 'POs are found.',
                'purchases' => $purchases,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get PO info
     */
    public function info($purchase_id)
    {
        if (!$purchase = $this->purchase->get_by_id($purchase_id)) {
            http_response_code(400);
            $response = [
                'message' => 'No PO is found.',
            ];
        } else {
            $purchase->total = $this->purchase->get_total($purchase_id);
            $response        = [
                'message'  => 'PO is found.',
                'purchase' => $purchase,
                'total'    => $this->purchase->get_total($purchase_id),
            ];
        }

        echo json_encode($response);
    }
    /**
     * Get count urgent
     */
    // public function get_count_urgent()
    // {
    //     // if (!$urgent = $this->purchase->get_count_urgent()) {
    //     //     http_response_code(400);
    //     //     $response['urgent'] = [
    //     //         'message' => 'No urgent is found.'
    //     //     ];
    //     // } else {
    //     //     $response['urgent'] = [
    //     //         'message' => 'urgent are found.',
    //     //         'urgent' => $urgent
    //     //     ];
    //     // }
    //     $response = $this->purchase->get_count_urgent();
    //     echo json_encode($response);
    // }

    /**
     * Purchase Orders
     */
    public function manager()
    {
        $this->title = 'Dressing Plant Purchase Orders';
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/purchases/manager',
        ];

        $where = [
            'is_deleted' => 0,
        ];
        // $items = $this->item->select('', $where);

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
            'urgent' => $this->purchase->get_count_urgent(),
        ];

        $this->build_content('default', 'purchases/manager', $data);
    }

    /**
     * Logs
     */
    public function check_log($id)
    {
        if ($_SESSION['user']->role_id != 1) {
            redirect(site_url('purchases/manager'));
        }

        $this->title = 'Log for Purchase Order No.' . $id;
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify',
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/select2/dist/js/select2.full',
            'lib/alertify/build/alertify',
            'js/purchases/check_log',
        ];

        $data = [
            'id' => $id,
        ];

        $this->build_content('default', 'purchases/check_log', $data);
    }

    /**
     * Purchase Order Receiving Receipts
     */
    public function receives($purchase_id)
    {
        if (!$purchase = $this->purchase->get_by_id($purchase_id) or
            $purchase->status === 'pending' or
            stripos($purchase->main_usage, WAD_WAREHOUSE) === false
        ) {
            show_404();
        }

        $this->title = 'Receiving Receipt';
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/receives/manager',
        ];

        $where = [
            'id' => $purchase->supplier_id,
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $data = [
            'purchase' => $purchase,
            'supplier' => $supplier,
            'urgent'   => $this->purchase->get_count_urgent(),
        ];
        $this->build_content('default', 'purchases/receives', $data);
    }

    /**
     * Purchase Order Item Releases
     */
    public function releases($purchase_id)
    {
        if (!$purchase = $this->purchase->get_by_id($purchase_id) or
            $purchase->status === 'pending' or
            stripos($purchase->main_usage, WAD_WAREHOUSE) !== false
        ) {
            show_404();
        }

        $this->title = 'Item Releases';
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/receives/manager',
        ];

        $where = [
            'id' => $purchase->supplier_id,
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $data = [
            'purchase' => $purchase,
            'supplier' => $supplier,
            'urgent'   => $this->purchase->get_count_urgent(),
        ];
        $this->build_content('default', 'purchases/releases', $data);
    }

    /**
     * PO Invoice Expense
     */
    public function invoice_expense_pos()
    {
        $account_name = $this->input->get('account_name');
        $from         = !empty($this->input->get('from')) ? date('Y-m-d', strtotime($this->input->get('from'))) : null;
        $to           = !empty($this->input->get('to')) ? date('Y-m-d', strtotime($this->input->get('to'))) : null;

        if (empty($account_name) || empty($from) || empty($to)) {
            show_404();
        }

        $this->title = $account_name . ' <br>List of POs <br>' . date('F j, Y', strtotime($from)) . ' to ' . date('F j, Y', strtotime($to));
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'js/purchases/invoice_expense_pos',
        ];

        $where = [
            'is_deleted' => 0,
        ];

        $data = [
            'account_name' => $account_name,
            'from'         => $from,
            'to'           => $to,
        ];

        $this->build_content('default', 'purchases/invoice_expense_pos', $data);
    }

    /**
     * PO Invoice Expense by Account Name
     */
    public function invoice_expense_by_account()
    {
        $account_name = $this->input->get('account_name');
        $from         = !empty($this->input->get('from')) ? date('Y-m-d', strtotime($this->input->get('from'))) : null;
        $to           = !empty($this->input->get('to')) ? date('Y-m-d', strtotime($this->input->get('to'))) : null;

        if (empty($account_name) || empty($from) || empty($to)) {
            show_404();
        }

        $this->title = $account_name . '- <br>List of POs <br>' . date('F j, Y', strtotime($from)) . ' to ' . date('F j, Y', strtotime($to));
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'js/purchases/invoice_expense_by_account',
        ];

        $where = [
            'is_deleted' => 0,
        ];

        $data = [
            'account_name' => $account_name,
            'from'         => $from,
            'to'           => $to,
        ];

        $this->build_content('default', 'purchases/invoice_expense_by_account', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('account_type');
        $this->load->model('bank');
        $this->load->model('purchase');
        $this->load->model('purchase_audit_attachment');
        $this->load->model('purchase_item');
        $this->load->model('purchase_or');
        $this->load->model('purchase_or_attachment');
        $this->load->model('purchase');
        $this->load->model('item');
        $this->load->model('supplier');
        $this->load->model('usage');
        $this->load->model('sub_usage');
        $this->load->model('check_slip');
        $this->load->model('check_purchase');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return isset($_FILES) ? $this->upload->display_errors() : '';

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name']     = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type']     = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error']    = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size']     = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'Upload Error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Get Input Suffices
     */
    protected function _get_purchase_item_ids()
    {
        $purchase_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'purchase_item_') === 0) {
                $purchase_item_ids[] = $value;
            }
        }

        return $purchase_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_purchase_item_rules($purchase_id, $purchase_item_ids)
    {
        $rules = [];

        foreach ($purchase_item_ids as $purchase_item_id) {
            $rules[] = [
                'field'  => 'purchase_item_' . $purchase_item_id,
                'label'  => 'Purchase Order Item',
                'rules'  => 'trim|callback__exists_in_purchase[' . $purchase_id . ']',
                'errors' => [
                    '_exists_in_purchase' => 'Purchase Order Item is not found.',
                ],
            ];

            $rules[] = [
                'field' => 'item_' . $purchase_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]',
            ];

            $rules[] = [
                'field' => 'unit_' . $purchase_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]',
            ];

            $rules[] = [
                'field' => 'qty_' . $purchase_item_id,
                'label' => 'Qty',
                'rules' => 'required|trim|is_numeric|greater_than[0]',
            ];

            $rules[] = [
                'field' => 'price_' . $purchase_item_id,
                'label' => 'Price',
                'rules' => 'required|trim|is_numeric',
            ];

            $rules[] = [
                'field' => 'account_type_' . $purchase_item_id,
                'label' => 'Account Name',
                'rules' => 'required',
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        if (!$purchase_items = $this->_get_purchase_item_data()) {
            $this->_error = 'no_purchase_item_added';
            return false;
        }

        if (!$purchase_id = $this->_generate_purchase($purchase_items) or !$this->_generate_purchase_items($purchase_id, $purchase_items)) {
            $this->_error = 'server_error';
            return false;
        } else {
            return $purchase_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_purchase_item_data()
    {
        $data              = [];
        $purchase_item_ids = $this->_get_purchase_item_ids();

        foreach ($purchase_item_ids as $purchase_item_id) {
            $name         = $this->input->post('item_' . $purchase_item_id);
            $unit         = $this->input->post('unit_' . $purchase_item_id);
            $qty          = $this->input->post('qty_' . $purchase_item_id);
            $price        = $this->input->post('price_' . $purchase_item_id);
            $account_type = $this->input->post('account_type_' . $purchase_item_id);
            $added_by     = $_SESSION['user']->id;
            $added_on     = date('Y-m-d H:i:s');
            $data[]       = [
                'id'            => is_numeric($purchase_item_id) ? $purchase_item_id : null,
                'item_id'       => $this->item->generate($name, $unit, $price, $added_by, $added_on),
                'unit'          => $unit,
                'qty'           => $qty,
                'price'         => $price,
                'amount'        => $qty * $price,
                'account_type'  => $account_type,
                'status'        => $this->input->post('status_' . $purchase_item_id) ?? null,
                'added_by'      => $added_by,
                'added_on'      => $added_on,
            ];
        }

        return $data;
    }

    /**
     * Generate Purchase Order
     */
    protected function _generate_purchase($purchase_items)
    {
        $purchase_date = DateTime::createFromFormat('m/d/Y', $this->input->post('purchase_date'));
        $date_covered  = $this->input->post('date_covered');

        $total = array_reduce($purchase_items, function ($current_total, $purchase_item) {
            return $current_total + $purchase_item['amount'];
        });

        $values = [
            'supplier_id'   => $this->input->post('supplier'),
            'purchase_date' => $purchase_date->format('Y-m-d'),
            'plate_no'      => $this->input->post('plate_no') ?: null,
            'total'         => $total,
            'remarks'       => $this->input->post('remarks') ?: null,
            'main_usage'    => $this->input->post('main_usage'),
            'sub_usage'     => $this->input->post('sub_usage'),
            'requisitioner' => $this->input->post('requisitioner') ?: null,
            'date_covered'  => $date_covered,
            'dr_no'         => $this->input->post('dr_no') ?: null,
            'si_cr'         => $this->input->post('si_cr') ?: null,
            'added_by'      => $_SESSION['user']->id,
            'added_on'      => date('Y-m-d H:i:s'),
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->purchase->insert($values);
    }

    /**
     * Generate Purchase Order Items
     */
    protected function _generate_purchase_items($purchase_id, $purchase_items)
    {
        foreach ($purchase_items as $purchase_item) {
            $where = [
                'id' => $purchase_item['item_id'],
            ];
            $values = [
                'price'      => $purchase_item['price'],
                'updated_by' => $purchase_item['added_by'],
                'updated_on' => date('Y-m-d H:i:s'),
            ];

            if (!$this->item->update($where, $values) ||
                !$this->purchase_item->generate(
                    $purchase_item['id'],
                    $purchase_id,
                    $purchase_item['item_id'],
                    $purchase_item['qty'],
                    $purchase_item['unit'],
                    $purchase_item['price'],
                    $purchase_item['amount'],
                    $purchase_item['account_type'],
                    $purchase_item['added_by'],
                    date('Y-m-d H:i:s')
                )
            ) {
                return false;
            }

        }
        return true;

    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($purchase)
    {
        $where = [
            'purchase_id' => $purchase->id,
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];

        if (!$this->purchase_item->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        } elseif (!$purchase_items = $this->_get_purchase_item_data()) {
            $this->_error = 'no_purchase_item_added';
            return false;
        } elseif (!$this->_revise_purchase($purchase->id, $purchase_items) || !$this->_generate_purchase_items($purchase->id, $purchase_items)) {
            $this->_error = 'server_error';
            return false;
        } else {
            return true;
        }
    }

    /**
     * Revise Purchase Order
     */
    protected function _revise_purchase($purchase_id, $purchase_items)
    {
        $purchase_date = DateTime::createFromFormat('m/d/Y', $this->input->post('purchase_date'));
        $date_covered  = $this->input->post('date_covered');

        $total = array_reduce($purchase_items, function ($current_total, $purchase_item) {
            if($purchase_item['status'] !== 'disapproved'){
                return $current_total + $purchase_item['amount'];
            } else {
                return $current_total;
            }
        });

        $purchase_where = [
            'id'         => $purchase_id,
            'is_deleted' => 0,
        ];
        $purchase = $this->purchase->select('', $purchase_where, 1);

        if ($purchase->status == 'released') {
            $status = 'released';
        } elseif($purchase->status == 'audited') {
            $status = 'audited';
        } else {
            $status = 'pending';
        }

        $where = [
            'id' => $purchase_id,
        ];
        $values = [
            'supplier_id'   => $this->input->post('supplier'),
            'purchase_date' => $purchase_date->format('Y-m-d'),
            'plate_no'      => $this->input->post('plate_no') ?: null,
            'total'         => $total,
            'remarks'       => $this->input->post('remarks') ?: null,
            'main_usage'    => $this->input->post('main_usage'),
            'sub_usage'     => $this->input->post('sub_usage'),
            'requisitioner' => $this->input->post('requisitioner') ?: null,
            'status'        => $status,
            'date_covered'  => $date_covered,
            'dr_no'         => $this->input->post('dr_no') ?: null,
            'si_cr'         => $this->input->post('si_cr') ?: null,
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s'),
        ];

        $purchase = $this->purchase->select('', $where, 1);
        if (!empty($purchase->attachment)) {
            $values['attachment'] = $purchase->attachment;
        }

        if (!empty($this->_uploaded)) {
            $attachments = [$purchase->attachment] ?: [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }

            $values['attachment'] = implode('🔥', $attachments);
        }

        if(!$this->purchase->update($where, $values)){
            return false;
        }

        return true;
    }

    /**
     * Attempt Approve Purchase Order Item
     */
    protected function _attempt_approve_item($purchase_item)
    {
        $where = [
            'id' => $purchase_item->id,
        ];
        $values = [
            'status'     => 'approved',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];
        if (!$is_updated = $this->purchase_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Disapprove Purchaser Order Item
     */
    protected function _attempt_disapprove_item($purchase_item)
    {
        $purchase_where = [
            'id'         => $purchase_item->purchase_id,
            'is_deleted' => 0,
        ];
        $purchase       = $this->purchase->select('', $purchase_where, 1);
        $adjusted_total = $purchase->total - $purchase_item->amount;

        $purchase_values = [
            'total'      => $adjusted_total,
            'updated_by' => $_SESSION['user']->id,
        ];
        if (!$is_updated = $this->purchase->update($purchase_where, $purchase_values)) {
            $this->_error = 'server_error';
        }

        $where = [
            'id' => $purchase_item->id,
        ];
        $values = [
            'status'     => 'disapproved',
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_updated = $this->purchase_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Pending Purchaser Order Item
     */
    protected function _attempt_pending_item($purchase_item)
    {

        if ($purchase_item->status === 'disapproved') {
            $purchase_where = [
                'id'         => $purchase_item->purchase_id,
                'is_deleted' => 0,
            ];
            $purchase       = $this->purchase->select('', $purchase_where, 1);
            $adjusted_total = $purchase->total + $purchase_item->amount;

            $purchase_values = [
                'total'      => $adjusted_total,
                'updated_by' => $_SESSION['user']->id,
            ];
            if (!$is_updated = $this->purchase->update($purchase_where, $purchase_values)) {
                $this->_error = 'server_error';
            }
        }

        $where = [
            'id' => $purchase_item->id,
        ];
        $values = [
            'status'     => 'pending',
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_updated = $this->purchase_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($purchase, $delete_reason)
    {
        $where = [
            'id' => $purchase->id,
        ];
        $values = [
            'is_deleted'     => 1,
            'updated_by'     => $_SESSION['user']->id,
            'deleted_reason' => $delete_reason,
        ];
        if (!$is_deleted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Attachment
     */
    protected function _attempt_delete_attachment($purchase, $file_name)
    {
        $where = [
            'id'               => $purchase->id,
            'attachment LIKE ' => "%" . $file_name . "%",
        ];

        $new_attachments = preg_replace('/🔥' . $file_name . '/', '', $purchase->attachment);
        $new_attachments = str_replace($file_name, "", $new_attachments);

        $values = [
            'attachment' => $new_attachments,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];

        if (!$is_deleted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        } else {
            $path = FCPATH . 'assets/pos/' . $purchase->id . '/' . $file_name;
            unlink($path);
        }

        return $is_deleted;
    }

    /**
     * Attempt Mark as For Approval
     */
    protected function _attempt_mark_for_approval($purchase)
    {
        $where = [
            'id' => $purchase->id,
        ];
        $values = [
            'status'         => 'for approval',
            'recommended_by' => $_SESSION['user']->id,
            'updated_by'     => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Mark as Approved
     */
    protected function _attempt_mark_approved($purchase)
    {
        $where = [
            'id' => $purchase->id,
        ];
        $values = [
            'status'      => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by'  => $_SESSION['user']->id,
            'updated_on'  => date('Y-m-d H:i:s'),
        ];

        if (!$is_deleted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Mark as Verified
     */
    protected function _attempt_mark_verified($purchase)
    {
        $where = [
            'id' => $purchase->id,
        ];

        $values = [
            'is_verified' => 1,
            'verified_by' => $_SESSION['user']->id,
            'updated_by'  => $_SESSION['user']->id,
            'updated_on'  => date('Y-m-d H:i:s'),
        ];

        if (!$is_verified = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_verified;
    }

    /**
     * Attempt Mark as Printed
     */
    protected function _attempt_mark_printed($purchase)
    {

        $where = [
            'id' => $purchase->id,
        ];

        if (!empty($purchase->check_slip_id)) { // if check slip id is not null change status to printed
            $values = [
                'status'     => 'printed',
                'printed_by' => $_SESSION['user']->id,
                'updated_by' => $_SESSION['user']->id,
            ];
        } else {
            $values = [
                'printed_by' => $_SESSION['user']->id,
                'updated_by' => $_SESSION['user']->id,
            ];
        }

        if (!$is_deleted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Pending Status
     */
    protected function _attempt_pending($purchase_id)
    {
        //sum all purchase_item of this purchase_item
        $purchase_total = $this->purchase_item->select('SUM(IFNULL(amount,0)) as amount', ['purchase_id' => $purchase_id, 'is_deleted' => 0], 1);

        $where = [
            'id' => $purchase_id,
        ];
        $values = [
            'is_deleted' => 0,
            'total'      => $purchase_total->amount,
            'status'     => 'pending',
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$this->purchase->update($where, $values)) {
            return !$this->_error = 'server_error';
        }

        $where = [
            'purchase_id' => $purchase_id,
        ];
        $purchase_items = $this->purchase_item->select('', $where, '');

        foreach ($purchase_items as $purchase_item) {
            $where = [
                'id' => $purchase_item->id,
            ];
            $values = [
                'is_deleted' => 0,
                'status'     => 'pending',
                'updated_by' => $_SESSION['user']->id,
            ];

            if (!$this->purchase_item->update($where, $values)) {
                return !$this->_error = 'server_error';
            }
        }

        return true;
    }

    /**
     * Attempt Urgent Status
     */
    protected function _attempt_urgent($purchase_id)
    {
        $where = [
            'id' => $purchase_id,
        ];
        $values = [
            'is_deleted' => 0,
            'is_urgent'  => 1,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_updated = $this->purchase->update($where, $values)) {
            return !$this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Add Check No
     */
    protected function _attempt_add_check_no($purchase)
    {
        $where = [
            'id' => $purchase->id,
        ];
        $values = [
            'check_no'   => $this->input->post('check_no'),
            'printed_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_updatted = $this->purchase->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updatted;
    }

    /**
     * Attempt Release
     */
    protected function _attempt_release($purchase)
    {

        $or_date = DateTime::createFromFormat('m/d/Y', $this->input->post('or_date'));

        $values = [
            'purchase_id' => $purchase->id,
            'or_date'     => $or_date->format('Y-m-d'),
            'or_no'       => $this->input->post('or_no') ?: null,
            'or_amount'   => $this->input->post('or_amount'),
            'added_by'    => $_SESSION['user']->id,
            'added_on'    => date('Y-m-d H:i:s'),
        ];

        if (!$purchase_or_id = $this->purchase_or->insert($values) or !$this->_generate_purchase_or_attachment($purchase_or_id, $this->_uploaded)) {
            $this->_error = 'server_error';
            return false;
        }

        if (!empty($purchase_or_id)) {

            $where = [
                'id' => $purchase->id,
            ];
            $values = [
                'status'     => 'released',
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => date('Y-m-d H:i:s'),
            ];

            if (!$this->purchase->update($where, $values)) {

            }
        }

        return $purchase_or_id;
    }

    /**
     * Generate Purchase OR Attachments
     */
    public function _generate_purchase_or_attachment($purchase_or_id, $uploads)
    {

        if (!empty($uploads)) {
            $data = [];
            foreach ($uploads as $uploaded) {
                $data[] = [
                    'purchase_or_id' => $purchase_or_id,
                    'file_name'      => $uploaded['file_name'],
                    'added_by'       => $_SESSION['user']->id,
                    'added_on'       => date('Y-m-d H:i:s'),
                ];
            }

            if (!$this->purchase_or_attachment->insert_batch($data)) {
                $this->_error = 'server_error';
                return false;
            }
        }

        return true;
    }

    /**
     * Callback: Check if Purchase Order Item Belongs to Purchase Order
     */
    public function _exists_in_purchase($purchase_item_id, $purchase_id)
    {
        if (!is_numeric($purchase_item_id)) {
            return true;
        }

        $where = [
            'id'          => $purchase_item_id,
            'purchase_id' => $purchase_id,
        ];

        return !empty($this->purchase_item->select('', $where, 1));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        foreach ($this->_uploaded as $uploaded) {
            unlink($uploaded['full_path']);
        }
    }

    /**
     * Get Purchase Order Ifo
     */
    public function get_info($purchase_order_id)
    {
        $where = [
            'id'         => $purchase_order_id,
            'is_deleted' => 0,
        ];
        if (!$purchase = $this->purchase->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order is not found.',
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully found.',
                'data'    => $purchase,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Approve Status For Release
     */
    public function approve_status()
    {
        $purchase_id = $this->input->post('purchase_id');
        $status      = $this->input->post('status');
        $user        = $_SESSION['user']->id;

        $where = [
            'id' => $purchase_id,
        ];

        $po = $this->purchase->select('', $where, 1);

        $disable = false;
        if ($status == "Printing") {
            if ($po->status != 'check issued') {
                $disable = true;
            }
            $status                         = "for printing";
            $values['approved_printing_by'] = $user;
        } else if ($status == "Releasing") {
            if ($po->status != 'printed') {
                $disable = true;
            }
            $status                        = "for release";
            $values['approved_release_by'] = $user;
        } else {
            $disable = true;
        }

        $values['status'] = $status;

        $response = [
            'is_success' => 1,
            'message'    => "Updated the purchase order's status.",
        ];

        if ($disable || !$this->purchase->update($where, $values)) {
            $response = [
                'is_success' => 0,
                'message'    => "Can't update purchase order's status.",
            ];
        }

        echo json_encode($response);
    }

    // public function mark_status()
    // {
    //     $purchase_id = $this->input->post('purchase_id');
    //     $type        = $this->input->post('type');
    //     $remarks     = $this->input->post('remarks');
    //     $audit_status = $this->input->post('audit_status');
    //     $user        = $_SESSION['user']->id;

    //     $where = [
    //         'id' => $purchase_id,
    //     ];

    //     $po = $this->purchase->select('', $where, 1);

    //     $disable = false;
    //     if ($type === "Release") {
    //         if ($po->status != 'for release') {
    //             $disable = true;
    //         }
    //         $status                = "released";
    //         $values['released_by'] = $user;
    //     } else if ($type === "Audit") {
    //         if ($po->status != 'released') {
    //             $disable = true;
    //         }
    //         $status                  = "audited";
    //         $values['audited_by']    = $user;
    //         $values['audit_remarks'] = $remarks;
    //         $values['audit_status']  = $audit_status; 

    //         // Define upload configuration
    //         $config = [
    //             'upload_path' => FCPATH . 'assets/audit_purchase/', // Specify the upload directory path
    //             'allowed_types' => 'pdf|jpg|jpeg|png', // Specify allowed file types
    //             'max_size' => 10240, // Maximum file size in kilobytes (10MB)
    //             'encrypt_name' => true, // Encrypt the uploaded file's name
    //             'overwrite' => false // Do not overwrite existing files with the same name
    //         ];

    //         // Attempt file upload
    //         if ($this->_attempt_upload($config, 'attachments')) {
    //             // Move uploaded files to the desired directory
    //             $new_path = FCPATH . 'assets/audit_purchase/' . $purchase_id . '/';
    //             if (!is_dir($new_path)) {
    //                 mkdir($new_path, 0755, true);
    //             }
    //             $this->_move_uploaded($new_path);

    //         } else {
    //             $response = [
    //                 'success' => false,
    //                 'message' => $this->_error_msg()
    //             ];
    //         }

    //         // Handle attachments
    //         if (!empty($this->_uploaded)) {
    //             foreach ($this->_uploaded as $uploaded) {
    //                 $attachment_values = [
    //                     'purchase_id' => $purchase_id,
    //                     'file_name' => $uploaded['file_name'],
    //                     'added_by' => $user,
    //                     'added_on' => date('Y-m-d H:i:s')
    //                 ];
    //                 $attachment_id = $this->purchase_audit_attachment->insert($attachment_values);
    //                 if (!$attachment_id) {
    //                     $this->db->trans_rollback();
    //                     $this->_error = 'server_error';
    //                     return false;
    //                 }
    //             }
    //         }

    //     } else {
    //         $disable = true;
    //     }

    //     $values['status'] = $status;

    //     $response = [
    //         'is_success' => 1,
    //         'message'    => "Updated the purchase order's status.",
    //     ];

    //     if ($disable || !$this->purchase->update($where, $values)) {
    //         $response = [
    //             'disable'     => $disable,
    //             'purchase_id' => $purchase_id,
    //             'type'        => $type,
    //             'is_success'  => 0,
    //             'message'     => "Can't update purchase order's status.",
    //         ];
    //     }

    //     echo json_encode($response);
    // }

    public function mark_status()
    {
        $purchase_id = $this->input->post('purchase_id');
        $type = $this->input->post('type');
        $remarks = $this->input->post('remarks');
        $audit_status = $this->input->post('audit_status');
        $user = $_SESSION['user']->id;

        $where = [
            'id' => $purchase_id,
        ];

        $po = $this->purchase->select('', $where, 1);

        $disable = false;
        if ($type === "Release") {
            if ($po->status != 'for release') {
                $disable = true;
            }
            $status = "released";
            $values['released_by'] = $user;
        } else if ($type === "Audit") {
            if ($po->status != 'released') {
                $disable = true;
            }
            $status = "audited";
            $values['audited_by'] = $user;
            $values['audit_remarks'] = $remarks;
            $values['audit_status'] = $audit_status;

            // Define upload configuration
            $config = [
                'upload_path' => FCPATH . 'assets/audit_purchase/', // Specify the upload directory path
                'allowed_types' => 'pdf|jpg|jpeg|png', // Specify allowed file types
                'max_size' => 10240, // Maximum file size in kilobytes (10MB)
                'encrypt_name' => true, // Encrypt the uploaded file's name
                'overwrite' => false // Do not overwrite existing files with the same name
            ];

            // Attempt file upload
            if ($this->_attempt_upload($config, 'attachments')) {
                $new_path = FCPATH . 'assets/audit_purchase/' . $purchase_id . '/';
                if (!is_dir($new_path)) {
                    mkdir($new_path, 0755, true);
                }
                $this->_move_uploaded($new_path);
            
                if (!empty($this->_uploaded)) {
                    foreach ($this->_uploaded as $uploaded) {
                        $attachment_values = [
                            'purchase_id' => $purchase_id,
                            'file_name' => $uploaded['file_name'],
                            'added_by' => $_SESSION['user']->id,
                            'added_on' => date('Y-m-d H:i:s')
                        ];
                        $this->purchase_audit_attachment->insert($attachment_values);
                    }
                }
            } else {
                $response = [
                    'disable' => $disable,
                    'purchase_id' => $purchase_id,
                    'type' => $type,
                    'is_success' => 0,
                    'message' => $this->_error
                ];
                echo json_encode($response);
                return;
            }            
        } else {
            $disable = true;
        }

        $values['status'] = $status;

        $response = [
            'is_success' => 1,
            'message' => "Updated the purchase order's status.",
        ];

        if ($disable || !$this->purchase->update($where, $values)) {
            $response = [
                'disable' => $disable,
                'purchase_id' => $purchase_id,
                'type' => $type,
                'is_success' => 0,
                'message' => "Can't update purchase order's status.",
            ];
        }

        echo json_encode($response);
    }

    public function mark_audit_update()
    {
        $purchase_id = $this->input->post('purchase_id');
        $type        = $this->input->post('type');
        $remarks     = $this->input->post('remarks');
        $audit_status = $this->input->post('audit_status');
        $user        = $_SESSION['user']->id;

        $where = [
            'id' => $purchase_id,
        ];

        $purchase = $this->purchase->select('', $where, 1);

        $values['audited_by']    = $user;
        $values['audit_remarks'] = $remarks;
        $values['audit_status'] = $audit_status;

        // Define upload configuration
        $config = [
            'upload_path' => FCPATH . 'assets/audit_purchase/', // Specify the upload directory path
            'allowed_types' => 'pdf|jpg|jpeg|png', // Specify allowed file types
            'max_size' => 10240, // Maximum file size in kilobytes (10MB)
            'encrypt_name' => true, // Encrypt the uploaded file's name
            'overwrite' => false // Do not overwrite existing files with the same name
        ];

        // Attempt file upload
        if ($this->_attempt_upload($config, 'attachments')) {
            $new_path = FCPATH . 'assets/audit_purchase/' . $purchase_id . '/';
            if (!is_dir($new_path)) {
                mkdir($new_path, 0755, true);
            }
            $this->_move_uploaded($new_path);
        
            if (!empty($this->_uploaded)) {
                foreach ($this->_uploaded as $uploaded) {
                    // $attachment_values = [
                    //     'purchase_id' => $purchase_id,
                    //     'file_name' => $uploaded['file_name'],
                    //     'added_by' => $_SESSION['user']->id,
                    //     'added_on' => date('Y-m-d H:i:s')
                    // ];
                    // $this->purchase_audit_attachment->insert($attachment_values);

                    $existing_audit_attachment = $this->purchase_audit_attachment->get_attachment_by_purchase_id($purchase_id);

                    if ($existing_audit_attachment) {
                        $where_attachment = [
                            'purchase_id' => $purchase_id
                        ];
        
                        $attachment_values = [
                            'file_name' => $uploaded['file_name'],
                            'updated_by' => $_SESSION['user']->id,
                            'updated_on' => date('Y-m-d H:i:s')
                        ];
                        $attachment_id = $this->purchase_audit_attachment->update($where_attachment, $attachment_values);
                        
                        if (!$attachment_id) {
                            $this->db->trans_rollback();
                            $this->_error = 'server_error';
                            return false;
                        }
                    } else {
                        $attachment_values = [
                            'purchase_id' => $purchase_id,
                            'file_name' => $uploaded['file_name'],
                            'added_by' => $_SESSION['user']->id,
                            'added_on' => date('Y-m-d H:i:s')
                        ];
                        $attachment_id = $this->purchase_audit_attachment->insert($attachment_values);
                        
                        if (!$attachment_id) {
                            $this->db->trans_rollback();
                            $this->_error = 'server_error';
                            return false;
                        }
                    }
                }
            }
        }

        $response = [
            'is_success' => 1,
            'message'    => "Updated the purchase order's audit.",
            'remarks' => $purchase->audit_remarks,
            'audit_status' => $purchase->audit_status,
        ];

        if (!$this->purchase->update($where, $values)) {
            $response = [
                'purchase_id' => $purchase_id,
                'is_success'  => 0,
                'message'     => "Can't update purchase order's audit.",
            ];
        }

        echo json_encode($response);
    }

    public function fetch_audit_details($purchase_id) {
        $where = [
            'id' => $purchase_id,
        ];
    
        $purchase = $this->purchase->select('', $where, 1);
    
        $response = [
            'remarks' => $purchase->audit_remarks,
            'audit_status' => $purchase->audit_status,
        ];
    
        echo json_encode($response);
    }    

    /**
     * Export to excel
     */
    public function export_excel()
    {
        // Instantiate the excel 
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $borderMedium = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ]; 
        
        $borderThin = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        
        $borderDouble = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DOUBLE,
                ],
            ],
        ];

        $status = $this->input->get('status', true) ?: 'pending';
        $item = $this->input->get('item', true);
        $date_from = $this->input->get('date_from', true);
        $date_to = $this->input->get('date_to', true);

        $date_from = $date_from ? DateTime::createFromFormat('m/d/Y', $date_from)->format('Y-m-d') : '';
        $date_to = $date_to ? DateTime::createFromFormat('m/d/Y', $date_to)->format('Y-m-d') : '';

        $sheet->setTitle('Dressing Plant Purchase Orders');

        $row = 1;

        $sheet->setCellValue('A' . $row, 'Dressing Plant Purchase Orders | Leyte Agri Ventures Corp.');
        $sheet->getStyle('A' . $row)->getFont()->setSize(12)->setBold(false);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->mergeCells("A$row:K$row");

        $row ++;

        $sheet->setCellValue('A' . $row, "Doc No");
        $sheet->setCellValue('B' . $row, "Item Name");
        $sheet->setCellValue('C' . $row, "Supplier");
        $sheet->setCellValue('D' . $row, "Purchase Date");
        $sheet->setCellValue('E' . $row, "Plate No.");
        $sheet->setCellValue('F' . $row, "Total");
        $sheet->setCellValue('G' . $row, "Requisitioner");
        $sheet->setCellValue('H' . $row, "Prepared By");
        $sheet->setCellValue('I' . $row, "Recommended By");
        $sheet->setCellValue('J' . $row, "Approved By");
        $sheet->setCellValue('K' . $row, "Printed By");

        $headerRange = 'A' . $row . ':K' . $row;

        $sheet->getStyle($headerRange)->getFont()->setSize(12)->setBold(true);
        $sheet->getStyle($headerRange)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle($headerRange)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        $purchases = $this->purchase->export_excel($status, $item, $date_from, $date_to);

        if (!empty($purchases)) {
            foreach ($purchases as $purchase) {
                $row++;

                $sheet->setCellValue('A' . $row, $purchase->doc_no);
                $sheet->getStyle('A' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('A' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('B' . $row, $purchase->item_name);
                $sheet->getStyle('B' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('B' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('C' . $row, $purchase->supplier);
                $sheet->getStyle('C' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('C' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('D' . $row, date('M d, Y', strtotime($purchase->purchase_date)));
                $sheet->getStyle('D' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('D' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('E' . $row, $purchase->plate_no);
                $sheet->getStyle('E' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('E' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('E' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('F' . $row, $purchase->total > 0 ? number_format($purchase->total, 2) : '');
                $sheet->getStyle('F' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('F' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $sheet->getStyle('F' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
    
                $sheet->setCellValue('G' . $row, $purchase->requisitioner);
                $sheet->getStyle('G' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('G' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                $sheet->setCellValue('H' . $row, $purchase->prepared_by);
                $sheet->getStyle('H' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('H' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('H' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                $sheet->setCellValue('I' . $row, $purchase->recommended_by);
                $sheet->getStyle('I' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('I' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('I' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                $sheet->setCellValue('J' . $row, $purchase->approved_by);
                $sheet->getStyle('J' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('J' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('J' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                $sheet->setCellValue('K' . $row, $purchase->printed_by);
                $sheet->getStyle('K' . $row)->getFont()->setSize(10)->setBold(false);
                $sheet->getStyle('K' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle('K' . $row)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            }
        }

        $row ++;

        foreach ($sheet->getColumnIterator() as $column)
            $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);


        // Create a writer and send the Excel file as a download
        $writer = new Xlsx($spreadsheet);


        // Set the file name
        $filename = 'Dressing Plant Purchase Orders ' . date('Y_m_d_H_i_s') . '.xlsx';


        // Send the Excel file to the browser
        $writer->save(FCPATH . 'assets/excel/' . $filename);
        echo json_encode(['status' => 200, 'filename' => $filename]);
    }

    /**
     * CAPEX Report
     */
    public function capex_report()
    {
        $this->title = 'CAPEX Report';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/purchases/capex_report'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name ASC');
        $data = [
            'suppliers' => $suppliers
        ];

        $this->build_content('default', 'purchases/capex_report', $data);
    }

    /**
     * Fund Transfer Report
     */
    public function fund_transfer_report()
    {
        $this->title = 'Fund Transfer Report';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/purchases/fund_transfer_report'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name ASC');
        $data = [
            'suppliers' => $suppliers
        ];

        $this->build_content('default', 'purchases/fund_transfer_report', $data);
    }
}
