+ function ($) {
	'use strict';

	function addItemRow(items) {
		var $newRow = $(itemRow());

		setItemList($newRow, items);

		var $itemTable = $('.item-table');
		var billing_statement_items = $itemTable.find('.billing_statement_item');

		if (billing_statement_items.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);
		setRowNumbers();
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="billing_statement_item">' +
			'<td>' +
				'<input type="hidden" name="billing_statement_item_' + rowId + '" value="' + rowId + '">' +
				'<input type="number" name="row_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
			'</td>' +
			'<td>' +
				'<select name="invoice_no_' + rowId + '" class="form-control dropsearch" data-placeholder=""></select>' +
			'</td>' +
			'<td>' +
				'<input type="number" name="total_' + rowId + '" class="form-control" value="" min="0" step="any" readonly>' +
			'</td>' +
			'<td class="text-center">' +
				'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
		'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function setItemList($itemRow, items) {
		$itemRow.find('select[name^="invoice_no"]').select2({
			data: items,
			placeholder: $(this).attr('data-placeholder'),
			allowClear: true,
			width: '100%'
		});
	}


	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="9">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var billing_statement_items = $itemTable.find('.billing_statement_item');

		billing_statement_items.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function getRowTotal($row) {
		var total = parseFloat($row.find('input[name^="total"]').val());
		if (isNaN(total)) {
			total = 0;
		}

		return total;
	}

	function calculateTotal() {
		var grand_total = 0;
		$('.item-table').find('.billing_statement_item').each(function (_, billing_statement_item) {
			grand_total += getRowTotal($(billing_statement_item));
		});
		$('input[name="grand_total"]').val(grand_total.toFixed(2));
		$('.footer_total').text($.fn.addNumericCommas(grand_total.toFixed(2)));
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.billing_statement_item').remove();

		var $itemTable = $(event.delegateTarget);
		var billing_statement_item = $itemTable.find('.billing_statement_item');
		if (billing_statement_item.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}
		setRowNumbers();
		calculateTotal();
  	}

	function getCustomerInfo(customerId) {
		return $.ajax({
			url: BASE_URI + 'customers/info/' + customerId,
			type: 'GET',
			dataType: 'json',
		});
	}

	function getItems(customerID) {
		return $.ajax({
			url: BASE_URI + 'billing_statements/all_invoices/' + customerID,
			type: 'GET',
			dataType: 'json'
		});
	}

	$(function () {
		var items = [{
			id: '',
			text: ''
		}];

		$('select[name="customer"]').on('change',function(event){
			var customerId = $(this).val();

			$('input[name="address"]').val('');
			$('input[name="phone_no"]').val('');
			$('input[name="email"]').val('');

			$.when(getCustomerInfo(customerId)).then(
				function (response) {
				
					$('input[name="address"]').val(response.customer.address);
					$('input[name="phone_no"]').val(response.customer.phone_no);
					$('input[name="email"]').val(response.customer.email);
				}
			);

			//for enable and disable for add item
			var btnelem = $('#btnSubmit');

			if(customerId !== ""){
				btnelem.prop('disabled',false);
				$('.item-table').find('tbody').html($(emptyRow()));
			}else{
				btnelem.prop('disabled',true);
			}

			$.when(getItems(customerId)).then(
				function (response) {
					response.invoices.forEach(function (item) {
						item.text = item.invoice_no;
						items.push(item);
					});
				}
			);

		});

		$('.action-add-item').click(function () {
			addItemRow(items);
		});

		$('.item-table')
			.on('change', 'select[name^="invoice_no"]', function (event) {
				var existing = $(event.delegateTarget).find('select[name^="invoice_no"] option[value="' + event.currentTarget.value + '"]:selected');
				if (existing.length > 1) {
					alert('Invoice No already selected.');
					$(event.currentTarget).val('').trigger('change');
					stop;
				}
		})
		.on('click', '.action-delete-item', function (event) {
			removeItemRow(event)
		})
		.on("change", 'select[name^="invoice_no"]', function (event) {
			var $row = $(event.currentTarget).closest(".billing_statement_item");
			// console.log($row)
			var selectedId = $(this).val();
			// console.log(selectedId)
			var selectedItem = items.find(item => item.id === selectedId);
			console.log(items)
			var $totalField = $row.find('input[name^="total"]');
			console.log($totalField)
			if (selectedItem) {

				const formattedPrice = parseFloat(selectedItem.total).toFixed(2);
				$totalField.val(formattedPrice);
			} else {
				$totalField.val('');
			}
			calculateTotal();
		});
	});

}(jQuery);
