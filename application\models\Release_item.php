<?php
class Release_item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'release_item';
        parent::__construct();
    }

    /**
     * Get Item By Release
     */
    public function get_by_release($release_id)
    {
        $sql = <<<EOT
SELECT release_item.*,
    item.name, item.unit, purchase_item.price,
    purchase_item.discount, purchase_item.amount, purchase_item.qty AS ordered,
    IFNULL(all_released.qty, 0) AS released
FROM release_item
LEFT JOIN purchase_item ON purchase_item.id = release_item.purchase_item_id
LEFT JOIN (
    SELECT release_item.purchase_item_id, IFNULL(SUM(release_item.qty), 0) AS qty
    FROM release_item
    LEFT JOIN release ON release.id = release_item.release_id
    WHERE release.is_deleted = 0
        AND release_item.is_deleted = 0
    GROUP BY release_item.purchase_item_id
) all_released ON all_released.purchase_item_id = purchase_item.id
LEFT JOIN item ON item.id = purchase_item.item_id
WHERE release_item.release_id = ?
    AND release_item.is_deleted = 0
HAVING release_item.qty > 0
EOT;
        $binds = array($release_id);

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Generate Release Item
     */
    public function generate($release_id, $purchase_item_id, $item_id, $qty, $added_by)
    {
        $sql =<<<EOT
INSERT INTO release_item(release_id, purchase_item_id, item_id, qty, added_by) (
    SELECT release.id AS release_id, purchase_item.id AS purchase_item_id, ? AS item_id, ? AS qty, ? AS added_by
    FROM release
    CROSS JOIN purchase_item
    WHERE release.id = ?
        AND release.is_deleted = 0
        AND purchase_item.id = ?
        AND purchase_item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    release_item.qty = VALUES(release_item.qty),
    release_item.updated_by = VALUES(release_item.added_by),
    release_item.is_deleted = 0
EOT;
        $binds = array($item_id, $qty, $added_by, $release_id, $purchase_item_id);

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */