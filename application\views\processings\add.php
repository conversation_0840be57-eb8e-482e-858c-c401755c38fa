<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $schedule_hauling->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="processing_date" class="control-label">Production Date</label>

						<input type="text" name="processing_date" class="form-control datepicker" value="<?=set_value('processing_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('processing_date');?>
						</div>
					</div>

	            	<h5 class="crud-subtitle"><span>Efficiency Report</span></h5>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived" class="control-label">Total Live Arrived <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived" class="form-control" value="<?=set_value('total_live_arrived');?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival" class="control-label">Total Death on Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival" class="form-control" value="<?=set_value('total_death_on_arrival');?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival" class="control-label">Total Death after Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival" class="form-control" value="<?=set_value('total_death_after_arrival');?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="la_in_kilos" class="control-label">LA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="la_in_kilos" class="form-control" value="<?=set_value('la_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('la_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="doa_in_kilos" class="control-label">DOA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="doa_in_kilos" class="form-control" value="<?=set_value('doa_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('doa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="daa_in_kilos" class="control-label">DAA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="daa_in_kilos" class="form-control" value="<?=set_value('daa_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('daa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_to_be_processed" class="control-label">Total to be Processed <small class="font-italic"></small></label>

						<input type="number" name="total_to_be_processed" class="form-control" value="<?=set_value('total_to_be_processed');?>">

						<div class="red-text flash-message">
							<?=form_error('total_to_be_processed');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="lp_in_kilos" class="control-label">LP in Kilos <small class="font-italic"></small></label>

						<input type="number" name="lp_in_kilos" class="form-control" value="<?=set_value('lp_in_kilos');?>">

						<div class="red-text flash-message">
							<?=form_error('lp_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_trips" class="control-label">Total Trips <small class="font-italic"></small></label>

						<input type="number" name="total_trips" class="form-control" value="<?=set_value('total_trips');?>">

						<div class="red-text flash-message">
							<?=form_error('total_trips');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="alw" class="control-label">ALW <small class="font-italic"></small></label>

						<input type="number" name="alw" class="form-control" value="<?=set_value('alw');?>">

						<div class="red-text flash-message">
							<?=form_error('alw');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="coops_loaded" class="control-label">Coops Loaded <small class="font-italic"></small></label>

						<input type="number" name="coops_loaded" class="form-control" value="<?=set_value('coops_loaded');?>">

						<div class="red-text flash-message">
							<?=form_error('coops_loaded');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Carcass</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table carcass" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcass_items)):
                                	$counter = 0;
                                    foreach ($carcass_items as $i => $carcass_item):
                                    $carcass_id = $carcass_item->id;
                            ?>
										<tr class="processing_item">
											<td>
												<?php $fn_processing_item = 'carcass_' . $carcass_id;?>
												<input type="hidden" name=<?=$fn_processing_item?> value="<?=set_value($fn_processing_item, $carcass_id);?>">

												<?php $fdn_item = 'item_' . $carcass_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$carcass_item->item_id?>><?=$carcass_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcass_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_account_name = 'account_name_' . $carcass_id;?>

												<select name="<?=$fdn_account_name;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($accounts AS $account):?>
														<option value=<?=$account->id?>><?=$account->account_name?></option>
													<?php endforeach;?>
												</select>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="carcass" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Incidental</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table incidental" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcass_items)):
                                	$counter = 0;
                                    foreach ($carcass_items as $i => $carcass_item):
                                    $carcass_id = $carcass_item->id;
                            ?>
										<tr class="processing_item">
											<td>
												<?php $fn_processing_item = 'carcass_' . $carcass_id;?>
												<input type="hidden" name=<?=$fn_processing_item?> value="<?=set_value($fn_processing_item, $carcass_id);?>">

												<?php $fdn_item = 'item_' . $carcass_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$carcass_item->item_id?>><?=$carcass_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcass_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_account_name = 'account_name_' . $carcass_id;?>

												<select name="<?=$fdn_account_name;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($accounts AS $account):?>
														<option value=<?=$account->id?>><?=$account->account_name?></option>
													<?php endforeach;?>
												</select>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="incidental" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Giblets</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table giblets" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcass_items)):
                                	$counter = 0;
                                    foreach ($carcass_items as $i => $carcass_item):
                                    $carcass_id = $carcass_item->id;
                            ?>
										<tr class="processing_item">
											<td>
												<?php $fn_processing_item = 'carcass_' . $carcass_id;?>
												<input type="hidden" name=<?=$fn_processing_item?> value="<?=set_value($fn_processing_item, $carcass_id);?>">

												<?php $fdn_item = 'item_' . $carcass_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$carcass_item->item_id?>><?=$carcass_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcass_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_account_name = 'account_name_' . $carcass_id;?>

												<select name="<?=$fdn_account_name;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($accounts AS $account):?>
														<option value=<?=$account->id?>><?=$account->account_name?></option>
													<?php endforeach;?>
												</select>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="giblets" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Byproducts</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table byproducts" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcass_items)):
                                	$counter = 0;
                                    foreach ($carcass_items as $i => $carcass_item):
                                    $carcass_id = $carcass_item->id;
                            ?>
										<tr class="processing_item">
											<td>
												<?php $fn_processing_item = 'carcass_' . $carcass_id;?>
												<input type="hidden" name=<?=$fn_processing_item?> value="<?=set_value($fn_processing_item, $carcass_id);?>">

												<?php $fdn_item = 'item_' . $carcass_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<option value=<?=$carcass_item->item_id?>><?=$carcass_item->item_name?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $carcass_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fdn_account_name = 'account_name_' . $carcass_id;?>

												<select name="<?=$fdn_account_name;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($accounts AS $account):?>
														<option value=<?=$account->id?>><?=$account->account_name?></option>
													<?php endforeach;?>
												</select>
											</td>

											<td class="text-center d-none">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="byproducts" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save and Proceed</button>
				<a href="<?=site_url('schedule_haulings')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
