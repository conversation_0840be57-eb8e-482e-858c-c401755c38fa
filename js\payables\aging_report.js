+ function ($) {
    'use strict';

	function addNumberCommas(val) {
        return $.fn.addNumericCommas(val);
    }

    function createDataTable(selector) {
        var $table = $(selector);
        var cols = $table.find('thead th');

        var dataTable = $table.DataTable({
            lengthChange: true,
            language: {
				infoFiltered: '',
			},
            dom: 'Bfrtlip',
            pagingType: 'full_numbers',
            order: [
                [0, 'asc']
            ],
            columnDefs: [{
                className: 'text-right',
                targets: [2, 3, 4, 5, 6]
            }],
            buttons: [{
                extend: 'print',
                className: 'btn btn-secondary',
                footer: true,
                exportOptions: {
                    stripHtml: false
                }
            },
            {
                extend: 'excel',
                className: 'btn btn-success ml-2',
                text: 'Download Excel',
                footer: true,
                exportOptions: {
                    columns: 'th'
                }
            },
            {
                extend: 'excel',
                className: 'btn btn-success ml-3',
                text: 'Download A/P Report',
                footer: true,
                exportOptions: {
                    columns: function (idx) {
                        return idx !== 1;
                    }
                }
            }
        
        ],
            autoWidth: false,
            processing: true,
            serverSide: true,
            lengthMenu: [
				[10, 25, 50, 100, -1],
				[10, 25, 50, 100, 'All'],
			],
            ajax: BASE_URI + 'fetch/' + $table.attr('data-package'),
            footerCallback: function (row, data, start, end, display) {
                var api = this.api();

                // Function to remove commas and parse as float
                var parseNumber = function (i) {
                    if (typeof i === 'string') {
                        i = i.replace(/[\$,]/g, '');
                    }
                    if (isNaN(i) || i === '' || i === null) {
                        return 0;
                    }
                    return parseFloat(i);
                };

                // Function to sum column data
                var sumColumnData = function (columnIdx) {
                    return api
                        .column(columnIdx, { page: 'current' })
                        .data()
                        .reduce(function (a, b) {
                            return parseNumber(a) + parseNumber(b);
                        }, 0);
                };

                // Set the footer values
                [2, 3, 4, 5, 6].forEach(function (columnIdx) {
                    $(api.column(columnIdx).footer()).html(
                        addNumberCommas(sumColumnData(columnIdx).toFixed(2))
                    );
                });
            }
        });

        return dataTable;
    }

    $(function () {
        let dataTable = createDataTable('.tableA');

        $('#tableFilter')
            .submit(function (event) {
                event.preventDefault();

                var queries = $(event.currentTarget).serialize();
                console.log(queries); 

                var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + queries;
                dataTable.ajax.url(url).load();

                window.history.pushState(null, null, BASE_URI + 'payables/aging_report?' + queries);
            }).trigger('submit');
    });
}(jQuery);
