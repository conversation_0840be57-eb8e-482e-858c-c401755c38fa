+(function ($, fns) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			paging: false,
			searching: false,
			order: [[0, "asc"]],
			columnDefs: [
				{
					className: "text-right",
					targets: 3,
				},
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + encodeURI($table.attr("data-package")),
      footerCallback: function () {
        var api = this.api();
        var col = 3;

        var floatVal = function (i) { 
          if (typeof i === "number") {
              return i;
          }
          if (typeof i === "string") {
              i = i.replace(/[$,]/g, ""); // Remove $ and , characters
              var result = parseFloat(i);
              return isNaN(result) ? 0 : result;
          }
          return 0; // Fallback for unexpected types
        };

        // Total of all data in the column
        var grandTotal = api
        .column(col)
        .data()
        .reduce(function (a, b) {
            return floatVal(a) + floatVal(b);
        }, 0);

    console.log(grandTotal);

    $(api.column(col).footer()).html(
        $.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
    );
      }
		});

		return dataTable;
	}

	$(function () {
		createDataTable(".tableA");
	});
})(jQuery, dateFns);
