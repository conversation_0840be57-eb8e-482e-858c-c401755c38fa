!function(e){"use strict";function t(t){var n=e(a());l(n,t);var c=e(".purchase-table"),r=c.find(".check-purchase");0===r.length&&c.find(".no-purchase").remove(),c.find("tbody").append(n)}function a(){var e=n();return'<tr class="check_purchase"><td><input type="hidden" name="check_purchase_'+e+'" value="'+e+'"><select name="purchase_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="amount_'+e+'" class="form-control" min="0" step="any"></td><td class="text-right"><button type="button" class="btn btn-sm btn-danger action-delete-purchase">Delete</button></td></tr>'}function n(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function c(){return'<tr class="no-purchase"><td class="text-center" colspan="3">No purchase orders added yet.</td></tr>'}function r(t){return e.ajax({url:BASE_URI+"purchases/get_info/"+t,type:"GET",dataType:"json"})}function o(t,a){var n=parseFloat(a.total);isNaN(n)&&(n=0),t.find('input[name^="amount"]').val(e.fn.addNumericCommas(n.toFixed(2)))}function u(){var t=0;e(".purchase-table").find(".check_purchase").each(function(a,n){var c=parseFloat(e.fn.removeNumericCommas(e(n).find('input[name^="amount"]').val()));isNaN(c)&&(c=0),t+=c}),e('input[name="total"]').val(e.fn.addNumericCommas(t.toFixed(2)))}function i(t){e(t.currentTarget).closest(".check_purchase").remove();var a=e(t.delegateTarget),n=a.find(".check_purchase");if(n.length<1){var r=e(c());a.find("tbody").html(r)}u()}function s(t){return e.ajax({url:BASE_URI+"receives/get_w_balance/"+t,type:"GET",dataType:"json"})}function l(t,a){t.find('select[name^="purchase"]').select2({data:a,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function d(){let t=""!==e("select[name='supplier']").children("option:selected").val();e(".action-add-payment").prop("disabled",!t)}function p(t){return e.ajax({url:BASE_URI+"checks/approve/"+t,type:"POST",dataType:"json"})}d(),e(function(){function a(){let t=e("select[name='supplier']").children("option:selected").val();t&&e.when(s(t)).then(function(t){n=[{id:"",text:""}],t.purchases.forEach(function(t){var a=parseFloat(t.total);isNaN(a)&&(a=0),t.text="Purchase Order #"+t.id+" - Php"+e.fn.addNumericCommas(a.toFixed(2)),n.push(t)})})}var n=[{id:"",text:""}];a(),e("select[name='supplier']").change(function(t){a(),e(".purchase-table").find("tbody").html(e(c())),d()}),e(".action-add-payment").click(function(){t(n)});var l=null;e(".purchase-table").on("change",'select[name^="purchase"]',function(t){l=e(t.currentTarget).closest(".check_purchase"),e.when(r(t.currentTarget.value)).then(function(e){o(l,e.data),u()})}).on("click",".action-delete-purchase",function(e){i(e)}).on("change",'input[name^="amount"]',u),e(".purchase-table").on("change",'input[name^="amount"]',u).trigger("change"),e(".action-approve").on("click",function(e){var t=e.currentTarget.getAttribute("data-checkno");alertify.confirm("Are you sure you want to approve Check No "+t+"?",function(){var t=e.currentTarget.getAttribute("data-id");p(t),window.location.href=BASE_URI+"checks/manager"}).setHeader("<em>Approve Check</em>")}),u()})}(jQuery);