<?php
class Customer extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'customer';
        parent::__construct();
    }

    /**
     * Get credit limit
     */
    public function used_credit($customer_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS credit
FROM pos_invoice
WHERE customer_id = ?
AND terms = "credit"
AND is_deleted = 0
EOT;
        $binds = [$customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get POS customer balance
     */
    public function pos_customer_balance($customer_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS balance
FROM pos_invoice
WHERE customer_id = ?
AND is_deleted = 0
EOT;
        $binds = [$customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }    

    /**
     * Create New Customer
     */
    public function generate($name, $address, $phone_no, $email, $added_by)
    {
        $sql = <<<EOT
INSERT INTO customer(name, address, phone_no, email, added_by) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    phone_no = VALUES(phone_no),
    email = VALUES(email),
    is_deleted = 0
EOT;
        $binds = [$name, $address, $phone_no, $email, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }

    /**
     * Get Customer with Invoices
     */
    public function get_customer_with_invoices()
    {
        $sql = <<<EOT
SELECT customer.*
FROM customer
LEFT JOIN invoice ON invoice.customer_id = customer.id
WHERE customer.is_deleted = 0
    AND invoice.is_deleted = 0
    AND customer.id <> 16
    AND invoice.id NOT IN (
        SELECT DISTINCT invoice_id
        FROM cash_bond_item
        WHERE is_deleted = 0
    )
GROUP BY customer.id
ORDER BY customer.name ASC
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return [];
        }
    }

    /**
     * Get Customer Details
     */
    public function get_details($customer_id)
    {
        $sql = <<<EOT
SELECT 
    IFNULL(customer.credit_limit, 0) AS credit_limit,
    IFNULL(receivable.total_receivable, 0) AS total_receivable
FROM customer
LEFT JOIN (
    SELECT 
        invoice.customer_id,
        SUM(invoice.total - invoice.paid_amount) AS total_receivable
    FROM invoice
    WHERE invoice.is_deleted = 0
        AND invoice.status = "approved"
        AND invoice.total > invoice.paid_amount
    GROUP BY invoice.customer_id
) AS receivable ON receivable.customer_id = customer.id 
WHERE customer.id = ?
EOT;
        $binds = [$customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return false;
        }
    }

    /**
     * Get Customers with Bad Debts
     */
    public function get_with_bad_debts()
    {
        $sql = <<<EOT
SELECT 
    customer.id,
    customer.name
FROM invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
WHERE invoice.is_deleted = 0
    AND invoice.status = "bad_debt"
    AND customer.is_deleted = 0
GROUP BY invoice.customer_id
ORDER BY customer.name ASC
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return [];
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
