<?php
class Invoice_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'invoice_attachment';
        parent::__construct();
    }

    /**
     * Get Attachment by Invoice Id
     */
    public function get_by_id($id){

    $sql = <<<EOT
SELECT invoice_attachment.*
FROM invoice_attachment
WHERE invoice_attachment.is_deleted = 0
AND invoice_attachment.invoice_id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */