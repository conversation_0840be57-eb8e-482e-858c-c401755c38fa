!function(e,t){"use strict";function n(t){var n=e(o());i(n,t);var a=e(".item-table"),r=a.find(".cash_bond_item");0===r.length&&a.find(".no-item").remove(),a.find("tbody").append(n),s()}function a(n){var a=e(".item-table"),r=!1;if(a.find('select[name^="invoice_no_"]').each(function(){if(e(this).val()===n.id)return r=!0,!1}),r)return;var i=e(o());const d=t.format(t.parse(n.cb_date),"MMM DD, YYYY");console.log(i),i.find('select[name^="invoice_no_"]').append(e("<option>",{value:n.id,text:n.id}).prop("selected",!0)).on("mousedown",function(e){e.preventDefault()}),i.find('input[name^="cb_date_"]').val(d).prop("readonly",!0),i.find('input[name^="total_cb_"]').val(parseFloat(n.grand_total||0).toFixed(2)).prop("readonly",!0),i.find('input[name^="remarks_"]').val(n.remarks).prop("readonly",!0);var c=a.find(".cash_bond_item");0===c.length&&a.find(".no-item").remove(),a.find("tbody").append(i),s()}function o(){var e=r();return'<tr class="cash_bond_item"><td><input type="hidden" name="cash_bond_item_'+e+'" value="'+e+'"><input type="number" name="row_'+e+'" class="form-control-plaintext" value="" readonly></td><td><select name="invoice_no_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="cb_date_'+e+'" class="form-control" value="" readonly></td><td><input type="number" name="total_cb_'+e+'" class="form-control" value="" min="0" step="any" readonly></td><td><input type="text" name="remarks_'+e+'" class="form-control" value="" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function r(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function i(t,n){t.find('select[name^="invoice_no"]').select2({data:n,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function d(){return'<tr class="no-item"><td class="text-center" colspan="9">No items added yet.</td></tr>'}function s(){var t=e(".item-table"),n=t.find(".cash_bond_item");n.each(function(t,n){e(n).find('input[name^="row"]').val(t+1)})}function c(e){var t=parseFloat(e.find('input[name^="total_cb"]').val());return isNaN(t)&&(t=0),t}function l(){var t=0;e(".item-table").find(".cash_bond_item").each(function(n,a){t+=c(e(a))}),e('input[name="grand_total"]').val(t.toFixed(2)),e(".footer_total").text(e.fn.addNumericCommas(t.toFixed(2)))}function m(t){e(t.currentTarget).closest(".cash_bond_item").remove();var n=e(t.delegateTarget),a=n.find(".cash_bond_item");if(a.length<1){var o=e(d());n.find("tbody").html(o)}s(),l()}function u(t){return e.ajax({url:BASE_URI+"customers/info/"+t,type:"GET",dataType:"json"})}function p(t){return e.ajax({url:BASE_URI+"customers/info_by_name/"+encodeURIComponent(t),type:"GET",dataType:"json"})}function h(t){return e.ajax({url:BASE_URI+"cash_bonds/customer_cash_bonds/"+t,type:"GET",dataType:"json"})}function f(t){return e.ajax({url:BASE_URI+"cash_bonds/delete_attachment/"+t,type:"POST",dataType:"json"})}e(function(){var o=[{id:"",text:""}];""!==e('select[name="customer"]').val()?(e("#btnSubmit").prop("disabled",!1),e(".cash_bond-history").removeClass("d-none")):(e("#btnSubmit").prop("disabled",!0),e(".cash_bond-history").addClass("d-none"),e(".item-table").find("tbody").html(e(d()))),"0"===e('select[name="customer"] option:selected').attr("data-walkin")&&"16"===e('select[name="customer"]').val()?(e(".credit-terms").addClass("d-none"),e(".walkin-customer").removeClass("d-none")):(e(".credit-terms").removeClass("d-none"),e(".walkin-customer").addClass("d-none"));var r=e('input[name="customer"]').val();"undefined"!=typeof cashBondData&&a(cashBondData),e.when(h(r)).then(function(e){e.cash_bonds.forEach(function(e){e.text=e.id,o.push(e)})}),e('select[name="customer"]').on("change",function(t){var n=e(this).val();o=[{id:"",text:""}],e('input[name="address"]').val(""),e('input[name="phone_no"]').val(""),e('input[name="email"]').val(""),e('input[name="available_credit"]').val(""),e(".history").attr("href","#"),e.when(u(n)).then(function(t){var n=t.customer.available_credit;(isNaN(n)||null===n)&&(n=0),e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e('input[name="available_credit"]').val(e.fn.addNumericCommas(n)),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"cash_bonds/history/"+t.customer.id)}),e.when(h(n)).then(function(e){e.cash_bonds.forEach(function(e){e.text=e.id,o.push(e)})});var a=e("#btnSubmit");""!==n?(a.prop("disabled",!1),e(".cash_bond-history").removeClass("d-none"),e(".item-table").find("tbody").html(e(d()))):(a.prop("disabled",!0),e(".cash_bond-history").addClass("d-none"))}),e('input[name="walkin_customer"]').on("change",function(t){var n=e(this).val();e.when(p(n)).then(function(t){e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"cash_bonds/history/"+t.customer.id)},function(t){e(".history").addClass("d-none")});var a=e("#btnSubmit");""!==n?(a.prop("disabled",!1),e(".item-table").find("tbody").html(e(d()))):a.prop("disabled",!0)}),e('input[name="terms"]').on("change",function(t){var n=e('input[name="terms"]:checked').val();"check"===n?(e(".check-terms").removeClass("d-none"),e(".non-credit-terms").removeClass("d-none")):"credit"===n?(e(".non-credit-terms").addClass("d-none"),e(".check-terms").addClass("d-none")):(e(".check-terms").addClass("d-none"),e(".non-credit-terms").removeClass("d-none"))}).trigger("change"),e(".action-add-item").click(function(){n(o)}),e(".item-table").on("change",'select[name^="invoice_no"]',function(n){var a=e(n.currentTarget),r=e(n.delegateTarget).find('select[name^="invoice_no"] option[value="'+a.val()+'"]:selected');if(r.length>1)return alert("Cash Bond already selected."),void a.val("").trigger("change");var i=a.closest(".cash_bond_item"),d=a.val();if(!d)return i.find('input[name^="total_cb"]').val(""),i.find('input[name^="cb_date"]').val(""),void i.find('input[name^="remarks"]').val("");var s=o.find(e=>e.id===d),c=i.find('input[name^="total_cb"]'),m=i.find('input[name^="cb_date"]'),u=i.find('input[name^="remarks"]');if(s){const e=parseFloat(s.grand_total).toFixed(2),n=t.format(t.parse(s.cb_date),"MMM DD, YYYY");c.val(e),m.val(n),u.val(s.remarks)}else c.val(""),m.val(""),u.val("");l()}).on("click",".action-delete-item",function(e){m(e)}),e(".attachment-table").on("click",".action-delete-attachment",function(t){var n=e(t.currentTarget).closest("tr.attachment").find("td"),a=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+a+"?",function(){var e=t.currentTarget.getAttribute("data-id");f(e),location.reload()}).setHeader("<em>Delete Attachment</em>")})})}(jQuery,dateFns);