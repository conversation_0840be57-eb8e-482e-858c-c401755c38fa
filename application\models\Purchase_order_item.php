<?php
class Purchase_order_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'purchase_order_item';
        parent::__construct();
    }

    /**
     * Generate purchase_order Item
     */
    public function generate($id, $purchase_order_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO purchase_order_item(id, purchase_order_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, purchase_order.id AS purchase_order_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM purchase_order
    CROSS JOIN item
    WHERE purchase_order.id = ?
        AND purchase_order.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    purchase_order_item.id = LAST_INSERT_ID(purchase_order_item.id),
    purchase_order_item.qty = VALUES(purchase_order_item.qty),
    purchase_order_item.unit = VALUES(purchase_order_item.unit),
    purchase_order_item.updated_by = VALUES(purchase_order_item.added_by),
    purchase_order_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $purchase_order_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get purchase_order Item By ID
     */
    public function get_by_id($purchase_order_item_id)
    {
        $sql = <<<EOT
SELECT purchase_order_item.*, item.name AS item
FROM purchase_order_item
LEFT JOIN item ON item.id = purchase_order_item.item_id
WHERE purchase_order_item.id = ?
    AND purchase_order_item.is_deleted = 0
EOT;
        $binds = [$purchase_order_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get purchase_order Items by purchase_order
     */
    public function get_by_purchase_order($purchase_order_id)
    {
        $sql = <<<EOT
SELECT purchase_order_item.*, item.name AS item
FROM purchase_order_item
LEFT JOIN item ON item.id = purchase_order_item.item_id
WHERE purchase_order_item.purchase_order_id = ?
    AND purchase_order_item.is_deleted = 0
EOT;
        $binds = [$purchase_order_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get purchase_order Items by purchase_order
     */
    public function get_approved_by_purchase_order($purchase_order_id)
    {
        $sql = <<<EOT
SELECT purchase_order_item.*, item.name AS item
FROM purchase_order_item
LEFT JOIN item ON item.id = purchase_order_item.item_id
WHERE purchase_order_item.purchase_order_id = ?
    AND purchase_order_item.is_deleted = 0
    AND purchase_order_item.status = 'approved'
EOT;
        $binds = [$purchase_order_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
