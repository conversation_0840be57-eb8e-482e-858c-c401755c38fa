<?php
class Poc_item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'poc_item';
        parent::__construct();
    }

    /**
     * Get Items by POC ID 
     */
    public function get_by_poc_id($poc_id)
    {
        $sql = <<<EOT
SELECT 
    poc_item.id,
    poc_item.poc_id,
    CASE
        WHEN poc_item.item_for_poc_id IS NULL THEN poc_item.description
        ELSE item_for_poc.item_name
    END as description,
    poc_item.qty,
    poc_item.unit_price,
    poc_item.amount
FROM poc_item
LEFT JOIN item_for_poc ON item_for_poc.id = poc_item.item_for_poc_id
WHERE poc_item.is_deleted = 0
    AND poc_item.poc_id = ?
EOT;
        $binds = [$poc_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */