<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><b><large>Customer : </large></b> <?=$processing_fee->customer;?></p>
					<p class="text-left"><b><large>Processing No. : </large></b> <?=anchor(site_url("processings/preview/" . $processing_fee->processing_id), $processing_fee->processing_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Remarks : </large></b> <?=$processing_fee->remarks;?></p>
				</div>

				<div class="col-sm-6">
					<p class="text-right"><b><large>Processing Fee Date : </large></b> <?=date('m/d/Y', strtotime($processing_fee->processing_fee_date));?></p>
					<p class="text-right"><b><large>Order Slip No. : </large></b> <?=anchor(site_url("order_slips/preview/" . $processing_fee->order_slip_id), $processing_fee->order_slip_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>List of Purchase Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 25%;">Unit</th>

									<th class="text-center" style="width: 15%;">Classification</th>

									<th class="text-center" style="width: 15%;">Qty</th>

									<th class="text-center" style="width: 15%;">Unit Price</th>

									<th class="text-center" style="width: 15%;">Amount</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_qty = 0;
								$total_unit_price = 0;
								$total_amount = 0;
								if (!empty($processing_fee_item_ids)):
                                	$counter = 0;
                                    foreach ($processing_fee_item_ids as $i => $processing_fee_item_id):
										$total_qty += $retain_items[$counter]['qty'];
										$total_unit_price += $retain_items[$counter]['unit_price'];
										$total_amount += $retain_items[$counter]['amount'];
							?>
										<tr class="processing_fee_item">
											<td><?=$retain_items[$counter]['item_name'];?></td>

											<td><?=$retain_items[$counter]['unit'];?></td>

											<td><?=$retain_items[$counter]['classification'];?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['qty']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['unit_price']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['amount']);?></td>
										</tr>
							<?php
									$counter++;
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
									<tr class="processing_fee_item">
										<td></td>

										<td></td>

										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_qty);?></td>

										<td class="text-right"><strong><?=floatval($total_unit_price);?></td>

										<td class="text-right"><strong><?=floatval($total_amount);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($processing_fee->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>Purchase Order Attachments</span></h5>
						<?php $attachments = explode('🔥',$processing_fee->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/processing_fees/'.$processing_fee->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$processing_fee->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Purchase Order Created By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('processing_fees');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="processing_fee_id" value="<?=$processing_fee->id?>">
		</form>
	</div>
</div>