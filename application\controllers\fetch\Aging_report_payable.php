<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Aging_report_payable extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch checks
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase';
        $primary_key = 'purchase.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_supplier(),
            $this->_get_purchases(),
            $this->_get_total(),
            $this->_get_one_to_thirty(),
            $this->_get_thirtyone_to_sixty(),
            $this->_get_sixtyone_to_ninety(),
            $this->_get_over_ninety(),
        ];

        $joins = <<<EOT
purchase
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
EOT;

        $where = <<<EOT
purchase.is_deleted = 0
AND purchase.status IN ("approved","check issued")
AND purchase.total > purchase.paid_amount
EOT;

        $supplier = $this->input->get('supplier', true) ?: null;

        if(!empty($supplier)){
            $where .= <<<EOT

AND purchase.supplier_id = '{$supplier}'
EOT;
        }

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->get('date_from'));
        $date_from = $date_from ? $date_from->format('Y-m-d') : '';
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->get('date_to'));
        $date_to = $date_to ? $date_to->format('Y-m-d') : '';

        if ($date_from && $date_to) {
            $where .= <<<EOT

AND purchase.purchase_date BETWEEN "{$date_from}" AND "{$date_to}"
EOT;
        } elseif ($date_from && !$date_to) {
            $where .= <<<EOT

AND purchase.purchase_date >= "{$date_from}"
EOT;
        } elseif (!$date_from && $date_to) {
            $where .= <<<EOT

AND purchase.purchase_date <= "{$date_to}"
EOT;
        }

        $group_by = "supplier.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'check';
            }
        ];
    }

    /**
     * Get Customer
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier_name',
            'dt' => 0,
            'field' => 'supplier_name'
        ];
    }

    /**
     * Get purchases
     */
    protected function _get_purchases()
    {
        return [
            'db' => 'GROUP_CONCAT(CONCAT("• ", purchase.id, " : ", (purchase.total - purchase.paid_amount)) SEPARATOR "<br>")',
            'as' => 'purchase_no',
            'dt' => 1,
            'field' => 'purchase_no',
            'formatter' => function ($d, $row) {
                $details = explode("<br>", $d);
                foreach ($details as &$detail) {
                    preg_match('/:\s*([\d.-]+)/', $detail, $matches);
                    if (isset($matches[1])) {
                        $amount = floatval($matches[1]);
                        $formatted_amount = number_format($amount, 2);
                        $detail = preg_replace('/:\s*[\d.-]+/', ': ' . $formatted_amount, $detail);
                    }
                }
                return implode("<br>", $details);
            }
            
        ];
    }
    
    /**
     * Get Total
     */
    protected function _get_total()
    {
        return [
            'db' => 'IF(SUM(purchase.total - purchase.paid_amount) > 0, SUM(purchase.total - purchase.paid_amount), 0)',
            'as' => 'total',
            'dt' => 2,
            'field' => 'total',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get One to Thirty
     */
    protected function _get_one_to_thirty()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) <= 30 THEN (purchase.total - purchase.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) <= 30 THEN (purchase.total - purchase.paid_amount) END), 0)",
            'as' => 'one_to_thirty',
            'dt' => 3,
            'field' => 'one_to_thirty',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Thirtyone to Sixty 
     */
    protected function _get_thirtyone_to_sixty()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) BETWEEN 31 AND 60 THEN (purchase.total - purchase.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) BETWEEN 31 AND 60 THEN (purchase.total - purchase.paid_amount) END), 0)",
            'as' => 'thirtyone_to_sixty',
            'dt' => 4,
            'field' => 'thirtyone_to_sixty',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Sixtyone to Ninety 
     */
    protected function _get_sixtyone_to_ninety()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) BETWEEN 61 AND 90 THEN (purchase.total - purchase.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) BETWEEN 61 AND 90 THEN (purchase.total - purchase.paid_amount) END), 0)",
            'as' => 'sixtyone_to_ninety',
            'dt' => 5,
            'field' => 'sixtyone_to_ninety',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Over Ninety
     */
    protected function _get_over_ninety()
    {
        $today = date('Y-m-d');
        
        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) > 90 THEN (purchase.total - purchase.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', purchase.purchase_date) > 90 THEN (purchase.total - purchase.paid_amount) END), 0)",
            'as' => 'over_ninety',
            'dt' => 6,
            'field' => 'over_ninety',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }


    /**
     * Format purchase details
     */
    protected function format_purchase_details($d, $row)
    {
        $details = explode("<br>", $d);
        foreach ($details as &$detail) {
            preg_match('/:\s*([\d.-]+)/', $detail, $matches);
            if (isset($matches[1])) {
                $amount = floatval($matches[1]);
                $formatted_amount = number_format($amount, 2);
                $detail = preg_replace('/:\s*[\d.-]+/', ': ' . $formatted_amount, $detail);
            }
        }
        return implode("<br>", $details);
    }
}
