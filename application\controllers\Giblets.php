<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Giblets Controller
 */
class Giblets extends MYT_Controller
{
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('giblet');
        $this->lang->load('error_messages', 'english');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Giblets';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/Giblets/manager'
        ];
        $this->build_content('default', 'Giblets/manager');
    }

    /**
     * Add giblets records (multi-row)
     */
    public function add()
    {
        if ($this->input->post()) {
            $skus = $this->input->post('sku') ?: [];
            $sizes = $this->input->post('size') ?: [];
            $ranges = $this->input->post('range') ?: [];
            $nos = $this->input->post('no') ?: [];
            $pcs = $this->input->post('pcs') ?: [];
            $kgs = $this->input->post('kgs') ?: [];

            // Ensure all inputs are arrays
            if (!is_array($skus)) $skus = [$skus];
            if (!is_array($sizes)) $sizes = [$sizes];
            if (!is_array($ranges)) $ranges = [$ranges];
            if (!is_array($nos)) $nos = [$nos];
            if (!is_array($pcs)) $pcs = [$pcs];
            if (!is_array($kgs)) $kgs = [$kgs];

            $success_count = 0;
            $max_count = max(count($skus), count($sizes), count($ranges), count($nos), count($pcs), count($kgs));

            for ($i = 0; $i < $max_count; $i++) {
                $sku = isset($skus[$i]) ? trim($skus[$i]) : '';
                $size = isset($sizes[$i]) ? trim($sizes[$i]) : '';
                $range = isset($ranges[$i]) ? trim($ranges[$i]) : '';
                $no = isset($nos[$i]) ? trim($nos[$i]) : '';
                $pcs_value = isset($pcs[$i]) ? trim($pcs[$i]) : '';
                $kgs_value = isset($kgs[$i]) ? trim($kgs[$i]) : '';

                // Skip empty rows
                if (empty($sku) && empty($size) && empty($range) && empty($no) && empty($pcs_value) && empty($kgs_value)) {
                    continue;
                }

                $data = [
                    'sku' => $sku,
                    'size' => $size,
                    'range' => $range,
                    'no' => !empty($no) ? (int)$no : null,
                    'pcs' => !empty($pcs_value) ? (int)$pcs_value : null,
                    'kgs' => !empty($kgs_value) ? (float)$kgs_value : null
                ];
                $this->giblet->insert_giblets($data);
                $success_count++;
            }
            if ($success_count > 0) {
                $this->session->set_flashdata('success', 'Giblets records added successfully.');
                redirect('Giblets');
            } else {
                $this->session->set_flashdata('error', 'No valid data to save.');
                redirect('Giblets/add');
            }
        }

        $this->title = 'Add Giblets';
        $this->css = [
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2.full',
            'js/Giblets/add'
        ];

        $data = [];

        $this->build_content('default', 'Giblets/add', $data);
    }

    /**
     * Edit a single giblets record
     */
    public function edit($id)
    {
        $giblets = $this->giblet->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
            redirect('Giblets');
        }
        $this->title = 'Edit Giblets';
        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Update a single giblets record
     */
    public function update($id)
    {
        $giblets = $this->giblet->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
            redirect('Giblets');
        }
        if ($this->input->post()) {
            $data = [
                'sku' => $this->input->post('sku'),
                'size' => $this->input->post('size'),
                'range' => $this->input->post('range'),
                'no' => $this->input->post('no'),
                'pcs' => $this->input->post('pcs'),
                'kgs' => $this->input->post('kgs')
            ];
            $this->giblet->update_giblets($id, $data);
            redirect('Giblets');
        }
        $this->title = 'Edit Giblets';
        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Delete giblets record (AJAX)
     */
    public function delete($id)
    {
        $giblets = $this->giblet->get_giblets_by_id($id);
        if (!$giblets) {
            http_response_code(400);
            $response = [
                'message' => 'Giblets record not found.'
            ];
        } else {
            if ($this->giblet->soft_delete_giblets($id, $_SESSION['user']->id ?? 1)) {
                $response = [
                    'message' => 'Giblets record successfully deleted.'
                ];
            } else {
                http_response_code(400);
                $response = [
                    'message' => 'Failed to delete giblets record.'
                ];
            }
        }

        echo json_encode($response);
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'transaction_failed':
                return '<p>Transaction failed. Please try again.</p>';

            case 'server_error':
                return;

            default:
                // Handle database_error and other custom errors
                if (strpos($this->_error, 'database_error:') === 0) {
                    return '<p>' . $this->_error . '</p>';
                }

                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}