	<div class="col-md-12 sect-jr">
		<!-- <div class="receipt" data-tag="<?=$soa->id;?>"> -->
		<div class="receipt">
			<div class="receipt-header">
				<div class="clearfix">
					<strong class="pull pull-right">Statement of Account</strong>
				</div>

				<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
				<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

				<div class="row sect-jr">
					<div class="col-sm-6">
						<p class="text-left">Name: <?=strtoupper($customer->name);?><p>
						<p class="text-left">Address: <?=$customer->address;?></p>
						<p class="text-left">TIN: <?=$customer->tin_number;?></p>
						<p class="text-left">Business Style: <?=$customer->business_style;?></p>
					</div>

					<div class="col-sm-4 offset-sm-2">
						<!-- <p class="text-left"><small>Doc No: </small> <?=$soa->soa_no;?></p> -->
						<p class="text-left">Date From:  <?=$date_from;?></p>
						<p class="text-left">Date To:  <?=$date_to;?></p>
						<!-- <p class="text-left"><small>Billing Period: </small> <?=date('n/j/Y', strtotime($soa->from_date)) . ' - ' . date('n/j/Y', strtotime($soa->to_date));?></p> -->
					</div>
				</div>
			</div>

			<div class="clearfix"></div>

			<table class="receipt-body table-bordered">
				<thead class="sticky-table-header">
					<tr>
						<th class="text-center" style="width: 5%;">ITEM</th>
						<th class="text-center" style="width: 10%;">DATE</th>
						<th class="text-center" style="width: 5%;">BILLING NO.</th>
						<th class="text-center" style="width: 5%;">MOP</th>
						<th class="text-center" style="width: 10%;">Charges</th>
						<th class="text-center" style="width: 50%;" colspan="4">Credits</th>
						<th class="text-center" style="width: 15%;">ACCOUNT BALANCE</th>
					</tr>
					<tr>
						<th class="text-center" colspan="5"></th>
						<th class="text-center">AR No.</th>
						<th class="text-center">Amount</th>
						<th class="text-center">PR No.</th>
						<th class="text-center">Amount</th>
						<th class="text-center"></th>
					</tr>
				</thead>

				<?php if(!empty($entries)):?>
				<tbody class="table-bordered">
					<?php
					$total = 0;
					$payment_in_transit = 0;
					foreach ($entries as $i => $entry):
						$total += ($entry->total - $entry->pr_amount);
						$payment_in_transit += $entry->ar_amount;
					?>
						<tr class="item">
							<td class="text-center"><?=++$i;?></td>
							<td><?=date('M d, Y', strtotime($entry->transaction_date));?></td>
							<td>
								<?php if ($entry->paid_amount > 0): ?>
									<a href="../../soas/soa_invoice/<?=$entry->id;?>" target="_blank" class="btn btn-link"><?=$entry->reference;?></a>
								<?php else: ?>
									<?=$entry->reference;?>
								<?php endif; ?>
							</td>
							<td><?=ucwords($entry->mop);?></td>
							<td class="text-right"><?=$entry->total > 0 ? number_format($entry->total, 2) : '';?></td>
							<td class="text-left"><?= $entry->ar_amount > 0 ? $entry->ar_no : '' ;?></td>
							<td class="text-right"><?= $entry->ar_amount > 0 ? number_format($entry->ar_amount, 2) : '' ;?></td>
							<td class="text-left"><?= $entry->pr_amount > 0 ? $entry->pr_no : '' ;?></td>
							<td class="text-right"><?= $entry->pr_amount > 0 ? number_format($entry->pr_amount, 2) : '' ;?></td>
							<td class="text-right"><?=number_format($total, 2);?></td>
						</tr>
					<?php
						// $balance += $total;
					endforeach;
					?>
				</tbody>

				<tfoot>
					<tr class="item">
						<th colspan="9" class="text-right">Outstanding Balance</th>
						<th class="text-right"><?='Php ' . number_format($total, 2);?></th>
					</tr>
					<tr class="item">
						<th colspan="9" class="text-right">Payment in Transit</th>
						<th class="text-right"><?='Php ' . number_format($payment_in_transit, 2);?></th>
					</tr>
					<tr class="item">
						<th colspan="9" class="text-right">Remaining Balance</th>
						<th class="text-right"><?='Php ' . number_format($total - $payment_in_transit, 2);?></th>
					</tr>
				</tfoot>
				<?php else:?>
				<tbody>
					<tr>
						<td colspan="10" class="text-center">No Matching Records Found.</td>
					</tr>
				</tbody>
				<?php endif;?>
			</table>

			<div class="row">
				<div class="col form-group">
					<p>Thank you for your business!</p>
				</div>
			</div>

			<div class="row">
				<div class="col-sm-4 offset-sm-8 text-center" style="margin-top: 30px">
					<hr class="signature-line" style="margin-bottom: 5px">

					<p>Signature above printed name</p>
					Approved by
				</div>
			</div>
		</div>

		<div class="clearfix text-center">
			<button type="button" class="btn btn-primary action-print" onclick="window.print()"><i class="fa fa-print"></i> Print</button>

			<a class="btn btn-light" href="<?=site_url('soas/add');?>">Close</a>
		</div>
	</div>

	<style>
		@media print {
			.sticky-table-header th {
				position: sticky;
				top: 0;
				background-color: #fff;
				border: none;
				outline: 1px solid #ccc;
				width: 10%; /* Set equal width for each column */
			}

			.sticky-table-header tr:nth-child(2) th {
				top: 21px;
				border: none;
				outline: 1px solid #ccc;
				width: 10%; /* Set equal width for each column */
			}

			/* .receipt-body td {
				width: 10%; 
			} */

			/* .receipt-body th {
				font-size: 1rem;
			} */

			/* Additional styling */
			.receipt {
				background-color: #fff !important;
				-webkit-print-color-adjust: exact;
				color-adjust: exact;
			}

			tfoot {
				display: table-row-group;
			}

			@page {
				margin-bottom: 2cm;
			}

			tbody tr:last-child {
				page-break-inside: avoid;
			}

			tbody:empty + tfoot {
				display: table-footer-group;
			}

			.receipt-body tbody:empty:after {
				content: "";
				display: table-row;
				height: 100px;
			}
		}
		
		.sticky-table-header th {
			position: sticky;
			top: 0;
			background-color: #fff;
			border: none;
			outline: 1px solid #ccc;
		}

		.sticky-table-header tr:nth-child(2) th {
			top: 21px;
			border: none;
			outline: 1px solid #ccc;
		}
	</style>