<!DOCTYPE HTML>
<html>
<head>
<script type="text/javascript" src="https://canvasjs.com/assets/script/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../../canvasjs.stock.min.js"></script>
<script type="text/javascript">
window.onload = function () {
  var dataPoints = [];
  var stockChart = new CanvasJS.StockChart("stockChartContainer",{
    theme: "light2",
    colorSet: "colorSet1",
    exportEnabled: true, //false
    title:{
      text:"StockChart with Area Chart"
    },
    subtitles: [{
      text: "Retail Sales Of ACME Corp."
    }],
    charts: [{
      axisX: {
        crosshair: {
          enabled: true
        }
      },
      axisY: {
        prefix: "$",
        suffix: "M",
        title: "Sales Revenue in USD",
        titleFontSize: 14
      },
      data: [{
        type: "area",
        xValueFormatString: "MMM YYYY",
        yValueFormatString: "$#,###.##M",
        dataPoints : dataPoints
      }]
    }],
    navigator: {
      slider: {
        minimum: new Date(2010, 00, 01),
        maximum: new Date(2014, 00, 01)
      }
    }
  });
  $.getJSON("https://canvasjs.com/data/gallery/stock-chart/grocery-sales.json", function(data) {
    for(var i = 0; i < data.length; i++){
      dataPoints.push({x: new Date(data[i].date), y: Number(data[i].sale)});
    }
    stockChart.render();
  });
}
</script>
</head>
<body>
<div id="stockChartContainer" style="height: 400px; max-width: 920px; margin: 0px auto;"></div>
</body>
</html>