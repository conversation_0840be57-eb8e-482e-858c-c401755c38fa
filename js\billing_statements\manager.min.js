!function(t){"use strict";function e(e){var a=t(e),n=a.find("thead th");console.log(BASE_URI+"fetch/"+a.attr("data-package"));var r=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(e,a){t.when(n(e)).then(function(){a.ajax.reload(null,!1)},function(t){a.ajax.reload(null,!1);var e=JSON.parse(t.responseText);alertify.error(e.message)})}function n(e){return t.ajax({url:BASE_URI+"billing_statements/cancel/"+e,type:"POST",dataType:"json"})}t(function(){var n=e(".tableA");t(".tableA").on("click",".action-cancel",function(e){var r=t(e.currentTarget).closest("tr"),c=r.find("td"),i=void 0!==c[0]?c[0].innerHTML:"";alertify.confirm("Are you sure you want to cancel Billing Statement No."+i+"?",function(){var t=e.currentTarget.getAttribute("data-id");a(t,n)}).setHeader("<em>Cancel Billing Statement</em>")}),t(".tab-link").click(function(e){t(".tab-link.active").removeClass("active"),e.currentTarget.classList.add("active");var a=BASE_URI+"fetch/billing_statements?status="+e.currentTarget.getAttribute("data-status");n.ajax.url(a).load()})})}(jQuery);