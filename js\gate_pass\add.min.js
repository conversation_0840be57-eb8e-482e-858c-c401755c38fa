!function(t){"use strict";function e(){var e=t(n()),a=t(".item-table"),i=a.find(".poc_item");0===i.length&&a.find(".no-item").remove(),a.find("tbody").append(e)}function n(){var t=a();return'<tr class="poc_item"><td><input type="hidden" name="poc_item_'+t+'" value="'+t+'"><input type="text" name="description_'+t+'" class="form-control" value=""></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="unit_price_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="total_amount_'+t+'" class="form-control" value="" min="0" step="any" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function a(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(){return'<tr class="no-item"><td class="text-center" colspan="5">No items added yet.</td></tr>'}function o(t){var e=parseFloat(t.find('input[name^="qty"]').val());isNaN(e)&&(e=0);var n=parseFloat(t.find('input[name^="unit_price"]').val());isNaN(n)&&(n=0);var a=n*e;return a<0&&(a=0),t.find('input[name^="total_amount"]').val(a>0?a.toFixed(2):""),a}function r(){var e=0;t(".item-table").find(".poc_item").each(function(n,a){e+=o(t(a))}),t('input[name="grand_total"]').val(e.toFixed(4)),t(".footer_total").text(t.fn.addNumericCommas(e.toFixed(2)))}function l(e){t(e.currentTarget).closest(".poc_item").remove();var n=t(e.delegateTarget),a=n.find(".poc_item");if(a.length<1){var o=t(i());n.find("tbody").html(o)}r()}function u(e){return t.ajax({url:BASE_URI+"customers/info/"+e,type:"GET",dataType:"json"})}function c(){var e=t('input[name="latest_id"]').val();e=parseInt(e,10),e+=1;let n=`POC-${e}`;var a=t('input[name="reference"]');a.val(n)}t(function(){t('select[name="customer"]').on("change",function(e){var n=t(this).val();t('input[name="address"]').val(""),t('input[name="phone_no"]').val(""),t('input[name="email"]').val(""),t('input[name="available_credit"]').val(""),t.when(u(n)).then(function(e){var n=e.customer.available_credit;(isNaN(n)||null===n)&&(n=0),t('input[name="address"]').val(e.customer.address),t('input[name="phone_no"]').val(e.customer.phone_no),t('input[name="email"]').val(e.customer.email),t('input[name="available_credit"]').val(t.fn.addNumericCommas(n))});var a=t("#btnSubmit");""!==n?(a.prop("disabled",!1),t(".item-table").find("tbody").html(t(i()))):a.prop("disabled",!0)}),t(".action-add-item").click(function(){e()}),t('input[name="other_services"]').on("click",function(e){var n=t('input[name="other_services_field"]'),a=t(e.currentTarget).prop("checked");a?(n.prop("readonly",!1),n.val("")):(n.prop("readonly",!0),n.val(""))}),t(".item-table").on("click",".action-delete-item",function(t){l(t)}).on("input",'input[name^="qty_"]',r).on("input",'input[name^="unit_price_"]',r),r(),c()})}(jQuery);