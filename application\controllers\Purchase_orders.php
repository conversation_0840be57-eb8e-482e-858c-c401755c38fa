<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Purchase_orders extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('purchase_orders/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review purchase_order Order for Approval
     */
    public function receive($purchase_order_id)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();

        $this->title = 'Receive purchase_order No. ' . $purchase_order->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchase_orders/receive'
        ];

        $purchase_order_items = $this->purchase_order_item->get_by_purchase_order($purchase_order->id);

        $data = [
            'purchase_order'          => $purchase_order,
            'purchase_order_items'    => $purchase_order_items
        ];
        $this->build_content('default', 'purchase_orders/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false, $is_edit = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name']) ? count($_FILES[$field_name]['name']) : 0;

        if($total_attempt == 1 AND $_FILES[$field_name]['name'][0] == "") {
            $this->_error = 'You did not select a file to upload.';
            return $required?false:true;
        }

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
             $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'File Upload Error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);
        $is_uploaded = $total_uploaded === $total_attempt;
        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Delete Purchase Order Attachment
     */
    public function delete_attachment($purchase_order_id, $file_name = null)
    {
        $file_name = $file_name ? $file_name : $this->input->post('file_name');

        $where = [
            'id' => $purchase_order_id,
            'is_deleted' => 0
        ];

        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order Attachment is not found.'
            ];
        } elseif (!$this->_attempt_delete_attachment($purchase_order, $file_name)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Attachment is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete Attachment
     */
    protected function _attempt_delete_attachment($purchase_order, $file_name)
    {
        $where = [
            'id' => $purchase_order->id,
            'attachment LIKE ' => "%".$file_name."%"
        ];

        $new_attachments = preg_replace('/🔥'.$file_name.'/', '', $purchase_order->attachment);
        $new_attachments = str_replace($file_name, "", $new_attachments);

        $values = [
            'attachment' => $new_attachments,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        } else {
            $path = FCPATH . 'assets/purchase_orders/' . $purchase_order->id . '/' . $file_name;
            unlink($path);
        }

        return $is_deleted;
    }

    /**
     * Add purchase_order
     */
    public function add($order_slip_id = null)
    {
        $order_slip = $this->order_slip->get_by_id($order_slip_id) or show_404();
        $order_slip_id = $order_slip->id;
        $order_slip_items = $this->order_slip_item->select('', ['order_slip_id' => $order_slip->id, 'is_deleted' => 0]);

        foreach($order_slip_items AS $index => $order_slip_item) {
            $where = [
                'id' => $order_slip_items[$index]->item_id,
                'is_deleted' => 0
            ];
            $item = $this->item->select('', $where, 1);

            $order_slip_items[$index]->item_name = $item->name;
        }

        $purchase_order_item_ids = $this->_get_purchase_order_item_ids();


        $config = [
            'upload_path' => FCPATH . 'assets/purchase_orders',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('purchase_orders/add'), $this->_get_purchase_order_item_rules('', $purchase_order_item_ids));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', false) && ($purchase_order_id = $this->_attempt_add($order_slip_id, $order_slip_id))) {
            $new_path = FCPATH . 'assets/purchase_orders/' . $purchase_order_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('purchase_orders/manager'));
        } else {
            $this->_delete_uploaded();
            $this->title = 'Add Purchase Order';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/purchase_orders/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->supplier->select('', $where, '', 'name');

            $retain_items = $this->_get_purchase_order_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $where = [
                'is_deleted' => 0
            ];
            $usages = $this->live_sell_usage->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $sub_usages = $this->live_sell_sub_usage->select('', $where, '', 'name');

            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);

            $data = [
                'order_slip'                    => $order_slip,
                'order_slip_items'              => $order_slip_items,
                'form_error'                    => $form_error,
                'purchase_order_item_ids'       => $purchase_order_item_ids,
                'customers'                     => $customers,
                'suppliers'                     => $suppliers,
                'retain_items'                  => $retain_items,
                'usages'                        => $usages,
                'sub_usages'                    => $sub_usages,
                'account_types'                 => $account_types,
            ];

            $this->build_content('default', 'purchase_orders/add', $data);
        }
    }

    /**
     * Edit purchase_order
     */
    public function edit($purchase_order_id)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();

        $purchase_order_items = $this->purchase_order_item->get_by_purchase_order($purchase_order->id);

        $retain_items = $this->_get_purchase_order_item_data();
        $rules = $this->config->item('purchase_orders/edit');

        if (!$purchase_order_item_ids = $this->_get_purchase_order_item_ids()) {
            foreach($purchase_order_items AS $purchase_order_item) {
                $item = $this->item->select('', ['id' => $purchase_order_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $purchase_order_item->id,
                    'item_id' => $purchase_order_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $purchase_order_item->qty,
                    'pref_alw' => $purchase_order_item->pref_alw,
                    'amount' => $purchase_order_item->amount,
                    'unit_price' => $purchase_order_item->unit_price,
                    'account_type' => $purchase_order_item->account_type,
                ];
                $purchase_order_item_ids[] = $purchase_order_item->id;
            }

            $existing_ids = array_map(function ($purchase_order_item) {
                return $purchase_order_item->id;
            }, $purchase_order_items);

            $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $existing_ids);
        } else {
            $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $purchase_order_item_ids);
        }

        $rules = array_merge($this->config->item('purchase_orders/edit'), $item_rules);

        $config = [
            'upload_path' => FCPATH . 'assets/purchase_orders',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];
        

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', false, true) && $this->_attempt_edit($purchase_order)) {
            $new_path = FCPATH . 'assets/purchase_orders/' . $purchase_order_id . '/';
            $this->_move_uploaded($new_path);
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('purchase_orders/preview/' . $purchase_order->id));
            } else {
                redirect(site_url('purchase_orders/manager'));
            }
        } else {
            $this->_delete_uploaded();
            $this->title = 'Edit Purchase Order';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/alertify/build/css/alertify',
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/alertify/build/alertify',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/purchase_orders/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->supplier->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $usages = $this->live_sell_usage->select('', $where, '', 'name');

            $where = [
                'main_usage_id' => $purchase_order->usage_id,
                'is_deleted' => 0
            ];
            $sub_usages = $this->live_sell_sub_usage->select('', $where, '', 'name');

            $where = [
                'applied_to' => 'Purchase Order',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'purchase_order' => $purchase_order,
                'purchase_order_items' => $purchase_order_items,
                'purchase_order_item_ids' => $purchase_order_item_ids,
                'retain_items' => $retain_items,
                'usages' => $usages,
                'suppliers' => $suppliers,
                'sub_usages' => $sub_usages,
                'account_types' => $account_types
            ];

            $this->build_content('default', 'purchase_orders/edit', $data);
        }
    }

    /**
     * Approve purchase_order
     */
    public function receive_purchase_order($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id,
            'is_deleted' => 0
        ];
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            $response = [
                'message' => 'purchase_order is not found.'
            ];
        } elseif (!$this->_attempt_receive($purchase_order)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'purchase_order is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($purchase_order)
    {
        $where = [
            'id' => $purchase_order->id
        ];
        $values = [
            'status' => 'received',
            'received_by' => $_SESSION['user']->id,
            'received_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];

        if (!$is_updated = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete purchase_order
     */
    public function delete($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id,
            'is_deleted' => 0
        ];
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.'
            ];
        } elseif (!$this->_attempt_delete($purchase_order)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * purchase_orders
     */
    public function manager()
    {
        $this->title = "Live Sell Purchase Orders";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchase_orders/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
            'urgent' => $this->purchase_order->get_count_urgent()
        ];

        $this->build_content('default', 'purchase_orders/manager', $data);
    }

     /**
     * Purchase Order Receive Manager
     */
    public function receive_manager()
    {
        $this->title = "Live Sell Received Purchase Orders";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchase_orders/receive_manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
            'urgent' => $this->purchase_order->get_count_urgent()
        ];

        $this->build_content('default', 'purchase_orders/receive_manager', $data);
    }

    public function receive_po($purchase_order_id)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();
        $purchase_order_items = $this->purchase_order_item->get_approved_by_purchase_order($purchase_order_id);

        $received_on = DateTime::createFromFormat('m/d/Y', $this->input->post('received_on'));

        $rules = $this->config->item('purchase_orders/receive_po');
        // var_dump($rules);die();
        // if ($this->_validate_form($rules) AND $this->_attempt_receive_po($purchase_order)) {
        if ($this->_validate_form($rules) AND $this->_attempt_receive_po($purchase_order)) {
            redirect(site_url('purchase_orders/receive_manager'));
        } else {
            if (!$purchase_order_item_ids = $this->_get_purchase_order_item_ids()) {
                foreach($purchase_order_items AS $index => $purchase_order_item) {
                    $item = $this->item->select('', ['id' => $purchase_order_item->item_id, 'is_deleted' => 0], 1);
                    $purchase_order_items[$index]->item_name = $item->name;
                    $retain_items[] = [
                        'id' => $purchase_order_item->id,
                        'item_id' => $purchase_order_item->item_id,
                        'item_name' => $item->name,
                        'unit' => $item->unit,
                        'qty' => $purchase_order_item->qty,
                        'pref_alw' => $purchase_order_item->pref_alw,
                        'amount' => $purchase_order_item->amount,
                        'unit_price' => $purchase_order_item->unit_price,
                    ];
                    $purchase_order_item_ids[] = $purchase_order_item->id;
                }

                $existing_ids = array_map(function ($purchase_order_item) {
                    return $purchase_order_item->id;
                }, $purchase_order_items);

                $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $existing_ids);
            } else {
                foreach($purchase_order_items AS $index => $purchase_order_item) {
                    $item = $this->item->select('', ['id' => $purchase_order_item->item_id, 'is_deleted' => 0], 1);
                    $purchase_order_items[$index]->item_name = $item->name;
                    $retain_items[] = [
                        'id' => $purchase_order_item->id,
                        'item_id' => $purchase_order_item->item_id,
                        'item_name' => $item->name,
                        'unit' => $item->unit,
                        'qty' => $purchase_order_item->qty,
                        'pref_alw' => $purchase_order_item->pref_alw,
                        'amount' => $purchase_order_item->amount,
                        'unit_price' => $purchase_order_item->unit_price,
                    ];
                    $purchase_order_item_ids[] = $purchase_order_item->id;
                }

                $existing_ids = array_map(function ($purchase_order_item) {
                    return $purchase_order_item->id;
                }, $purchase_order_items);
                $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $purchase_order_item_ids);
            }

            $where = [
                'purchase_order_id' => $purchase_order->id,
                'is_deleted' => 0
            ];
            $purchase_order_or = $this->purchase_order_or->select('',$where, 1);

            if(!empty($purchase_order_or)){
                $where = [
                    'purchase_order_or_id' => $purchase_order_or->id,
                    'is_deleted' => 0
                ];
                $or_attachments = $this->purchase_order_or_attachment->select('',$where);
            }else{
                $or_attachments = null;
            }

            $where = [
                'id' => $purchase_order->added_by
            ];

            $issuer = $this->user->select('', $where, 1);

            $this->title = 'Receive: Purchase Order No. ' . $purchase_order->id;

            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/alertify/build/css/alertify'
            ];

            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/alertify/build/alertify',
                'js/purchase_orders/receive_po',
            ];

            $where = [
                'id' => $purchase_order->customer_id
            ];
            $customer = $this->customer->select('', $where, 1);

            $where = [
                'id' => $purchase_order->supplier_id
            ];
            $supplier = $this->supplier->select('', $where, 1);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'purchase_order' => $purchase_order,
                'purchase_order_item' => $purchase_order_item,
                'purchase_order_item_ids' => $purchase_order_item_ids,
                'retain_items' => $retain_items,
                'received_on' => $received_on,
                'customer' => $customer,
                'supplier' => $supplier,
                'purchase_order_items' => $purchase_order_items,
                'or_attachments' => $or_attachments,
                'issuer' => $issuer
            ];
            $this->build_content('default', 'purchase_orders/receive_po', $data);
        }
    }

    /**
     * Purchase Order Print Preview
     */
    public function preview($purchase_order_id, $type)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();
        $purchase_order_items = $this->purchase_order_item->get_approved_by_purchase_order($purchase_order_id);
        
        if (!$purchase_order_item_ids = $this->_get_purchase_order_item_ids()) {
            foreach($purchase_order_items AS $purchase_order_item) {
                $item = $this->item->select('', ['id' => $purchase_order_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $purchase_order_item->id,
                    'item_id' => $purchase_order_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $purchase_order_item->qty,
                    'pref_alw' => $purchase_order_item->pref_alw,
                    'amount' => $purchase_order_item->amount,
                    'unit_price' => $purchase_order_item->unit_price,
                ];
                $purchase_order_item_ids[] = $purchase_order_item->id;
            }

            $existing_ids = array_map(function ($purchase_order_item) {
                return $purchase_order_item->id;
            }, $purchase_order_items);

            $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $existing_ids);
        } else {
            $item_rules = $this->_get_purchase_order_item_rules($purchase_order->id, $purchase_order_item_ids);
        }

        if ($type === "Release" and $purchase_order->status !== 'for release') {
            redirect(site_url('purchase_orders/manager?status=for release'));
        } else if ($type === "Audit" and $purchase_order->status !== 'released') {
            redirect(site_url('purchase_orders/manager?status=released'));
        }

        $where = [
            'purchase_order_id' => $purchase_order->id,
            'is_deleted' => 0
        ];
        $purchase_order_or = $this->purchase_order_or->select('',$where, 1);

        if(!empty($purchase_order_or)){
            $where = [
                'purchase_order_or_id' => $purchase_order_or->id,
                'is_deleted' => 0
            ];
            $or_attachments = $this->purchase_order_or_attachment->select('',$where);
        }else{
            $or_attachments = null;
         }

        $where = [
            'id' => $purchase_order->added_by
        ];

        $issuer = $this->user->select('', $where, 1);

        $this->title = 'Purchase Order';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchase_orders/preview',
        ];

        $where = [
            'id' => $purchase_order->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $where = [
            'id' => $purchase_order->supplier_id
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $data = [
            'purchase_order' => $purchase_order,
            'purchase_order_item' => $purchase_order_item,
            'purchase_order_item_ids' => $purchase_order_item_ids,
            'retain_items' => $retain_items,
            'customer' => $customer,
            'supplier' => $supplier,
            'purchase_order_items' => $purchase_order_items,
            'type' => $type,
            'or_attachments' => $or_attachments,
            'issuer' => $issuer
        ];
        $this->build_content('default', 'purchase_orders/preview', $data);
    }

    /**
     * Purchase Order Print Preview
     */
    public function simple_preview($purchase_order_id, $type)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id);
        ($purchase_order and $purchase_order->status !== 'pending') or show_404();

        $where = [
            'id' => $purchase_order_id
        ];

        $po = $this->purchase_order->select('', $where, 1);

        if ($type == "Printing" and $po->status != 'check issued') {
            redirect(site_url('purchase_orders/manager?status=check%20issued'));
        } else if ($type == "Releasing" and $po->status != 'printed') {
            redirect(site_url('purchase_orders/manager?status=printed'));
        }

        $this->title = 'Print Preview: Purchase_order Order No. ' . $purchase_order->id;

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/purchase_orders/simple_preview'
        ];


        $where = [
            'id' => $purchase_order->supplier_id
        ];

        $supplier = $this->supplier->select('', $where, 1);

        $purchase_order_items = $this->purchase_order_item->get_by_purchase_order($purchase_order_id);

        $where = [
            'id' => $purchase_order->added_by
        ];
        $issuer = $this->user->select('', $where, 1);

        $data = [
            'purchase_order' => $purchase_order,
            'supplier' => $supplier,
            'purchase_order_items' => $purchase_order_items,
            'status' => $type,
            'issuer' => $issuer
        ];

        $this->build_content('default', 'purchase_orders/simple_preview', $data);
    }

    /**
     * Review Purchase Order for Approval
     */
    public function review($purchase_order_id)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id) or show_404();

        $this->title = 'Review Purchase Order No. ' . $purchase_order->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/purchase_orders/review'
        ];

        $purchase_order_items = $this->purchase_order_item->get_by_purchase_order($purchase_order->id);

        $data = [
            'purchase_order' => $purchase_order,
            'purchase_order_items' => $purchase_order_items,
        ];
        $this->build_content('default', 'purchase_orders/review', $data);
    }

    /**
     * Pending Purchase Order Item
     */
    public function pending_item($purchase_order_item_id)
    {
        $where = [
            'id' => $purchase_order_item_id,
            'is_deleted' => 0
        ];

        if (!$purchase_order_item = $this->purchase_order_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.'
            ];
        } elseif (!$this->_attempt_pending_item($purchase_order_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully pending.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Pending Purchaser Order Item
     */
    protected function _attempt_pending_item($purchase_order_item)
    {

        if($purchase_order_item->status === 'disapproved'){
            $purchase_order_where = [
                'id' => $purchase_order_item->purchase_order_id,
                'is_deleted' => 0
            ];
            $purchase_order = $this->purchase_order->select('', $purchase_order_where, 1);
            $adjusted_total = $purchase_order->total + $purchase_order_item->amount;

            $purchase_order_values = [
                'total' => $adjusted_total,
                'updated_by' => $_SESSION['user']->id
            ];
            if (!$is_updated = $this->purchase_order->update($purchase_order_where, $purchase_order_values)) {
                $this->_error = 'server_error';
            }
        }

        $where = [
            'id' => $purchase_order_item->id
        ];
        $values = [
            'status' => 'pending',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->purchase_order_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Approve Purchase Order Item
     */
    public function approve_item($purchase_order_item_id)
    {
        $where = [
            'id' => $purchase_order_item_id,
            'is_deleted' => 0
        ];
        if (!$purchase_order_item = $this->purchase_order_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.'
            ];
        } elseif (!$this->_attempt_approve_item($purchase_order_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully approved.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve Purchase Order Item
     */
    protected function _attempt_approve_item($purchase_order_item)
    {
        $where = [
            'id' => $purchase_order_item->id
        ];
        $values = [
            'status' => 'approved',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];
        if (!$is_updated = $this->purchase_order_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Disapprove Purchase Order Item
     */
    public function disapprove_item($purchase_order_item_id)
    {
        $where = [
            'id' => $purchase_order_item_id,
            'is_deleted' => 0
        ];

        if (!$purchase_order_item = $this->purchase_order_item->select('', $where, 1)) {
            http_response_code(404);
            $response = [
                'message' => 'Purchase Order Item is not found.'
            ];
        } elseif (!$this->_attempt_disapprove_item($purchase_order_item)) {
            http_response_code(404);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order Item is successfully disapproved.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Disapprove Purchaser Order Item
     */
    protected function _attempt_disapprove_item($purchase_order_item)
    {
        $purchase_order_where = [
            'id' => $purchase_order_item->purchase_order_id,
            'is_deleted' => 0
        ];
        $purchase_order = $this->purchase_order->select('', $purchase_order_where, 1);
        $adjusted_total = $purchase_order->total - $purchase_order_item->est_due_amount;

        $purchase_order_values = [
            'total' => $adjusted_total,
            'updated_by' => $_SESSION['user']->id
        ];
        if (!$is_updated = $this->purchase_order->update($purchase_order_where, $purchase_order_values)) {
            $this->_error = 'server_error';
        }

        $where = [
            'id' => $purchase_order_item->id
        ];
        $values = [
            'status' => 'disapproved',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->purchase_order_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Purchase Order is Approved
     */
    public function mark_approved($purchase_order_id)
    {
        $where = <<<EOT
id = $purchase_order_id
AND status IN("pending", "for approval")
AND is_deleted = 0
EOT;
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.'
            ];
        } elseif (!$this->_attempt_mark_approved($purchase_order)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as approved.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Mark as Approved
     */
    protected function _attempt_mark_approved($purchase_order)
    {
        $where = [
            'id' => $purchase_order->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Mark Urgent Purchase Order
     */
    public function mark_urgent($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id
        ];
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.'
            ];
        } elseif (!$this->_attempt_urgent($purchase_order_id)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully changed to URGENT.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Urgent Status
     */
    protected function _attempt_urgent($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id
        ];
        $values = [
            'is_deleted' => 0,
            'is_urgent' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->purchase_order->update($where, $values)) {
            return !$this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Return to Pending Purchase Order
     */
    public function return_to_pending($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id
        ];
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.'
            ];
        } elseif (!$this->_attempt_pending($purchase_order_id)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully changed to PENDING.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Pending Status
     */
    protected function _attempt_pending($purchase_order_id)
    {

        $where = [
            'id' => $purchase_order_id
        ];
        $values = [
            'is_deleted' => 0,
            'status' => 'pending',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->purchase_order->update($where, $values)) {
            return !$this->_error = 'server_error';
        }

        $where = [
            'purchase_order_id' => $purchase_order_id
        ];
        $purchase_order_items = $this->purchase_order_item->select('', $where, '');

        foreach ($purchase_order_items as $purchase_order_item) {
            $where = [
                'id' => $purchase_order_item->id
            ];
            $values = [
                'is_deleted' => 0,
                'status' => 'pending',
                'updated_by' => $_SESSION['user']->id
            ];

            if (!$this->purchase_order_item->update($where, $values)) {
                return !$this->_error = 'server_error';
            }
        }

        return true;
    }

    /**
     * Add Purchase Order Attachment
     */
    public function add_attachment($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id
        ];
        ($purchase_order = $this->purchase_order->select('', $where, 1) and $purchase_order->status !== 'pending') or show_404();;
        $rules = $this->config->item('purchase_orders/add_attachment');

        $config = [
            'upload_path' => FCPATH . 'assets/purchase_orders',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if ($this->_attempt_upload($config, 'attachment', true) and $this->_validate_form($rules) and $this->_attempt_add_attachment($purchase_order)) {
            $new_path = FCPATH . 'assets/purchase_orders/' . $purchase_order_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('purchase_orders/manager'));
        } else {
            $this->_delete_uploaded();
            $this->title = 'Add Purchase Order Attachment';
            $this->css = [
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/alertify/build/alertify',
                'lib/input-autosize/dist/autosize'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'purchase_order' => $purchase_order
            ];

            $this->build_content('default', 'purchase_orders/add_attachment', $data);
        }

    }

    /**
     * Attempt Add Attachment
     */
    protected function _attempt_add_attachment($purchase_order)
    {
        $where = [
            'id' => $purchase_order->id,
            'is_deleted' => 0
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];

            if(!empty($purchase_order->attachment)){
                $attachments[] = $purchase_order->attachment;
            }

            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $new_attachment = implode('🔥', $attachments);

            $values = [
                'attachment' => $new_attachment
            ];
        }else{
            return false;
        }

        if (!$is_updated = $this->purchase_order->update($where,$values)) {
            $this->_error = 'server_error';
            return false;
        }

        return $is_updated;
    }

    /**
     * Approve Status For Release
     */
    public function approve_status()
    {
        $purchase_order_id = $this->input->post('purchase_order_id');
        $status = $this->input->post('status');
        $user = $_SESSION['user']->id;

        $where = [
            'id' => $purchase_order_id
        ];

        $po = $this->purchase_order->select('', $where, 1);

        $disable = false;
        if ($status == "printed") {
            if ($po->status != 'approved') {
                $disable = true;
            }
            $status = "printed";
            // $values['approved_printing_by'] = $user;
        } else if ($status == "Releasing") {
            if ($po->status != 'printed') {
                $disable = true;
            }
            $status = "for release";
            // $values['approved_release_by'] = $user;
        } else {
            $disable = true;
        }

        $values['status'] = $status;

        $response = [
            'is_success' => 1,
            'message' => "Updated the purchase order's status."
        ];

        if ($disable || !$this->purchase_order->update($where, $values))
        {
            $response = [
                'is_success' => 0,
                'message' => "Can't update purchase order's status."
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order Preview Release
     */
    public function preview_release($purchase_order_id)
    {
        $purchase_order = $this->purchase_order->get_by_id($purchase_order_id);
        ($purchase_order and $purchase_order->status === 'for release') or show_404();

        // $where = [
        //     'id' => $purchase_order->check_slip_id,
        //     'is_deleted' => 0
        // ];
        // $check = $this->check_slip->select('', $where ,1);
        // $bank = $this->bank->select('',['id' => $check->bank_id, 'is_deleted' => 0], 1);
        // $check->{'bank'} = $bank->name;


        $rules = $this->config->item('purchase_orders/release');

        $config = [
            'upload_path' => FCPATH . 'assets/purchase_order_or',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && $this->_attempt_release($purchase_order)) {
            $new_path = FCPATH . 'assets/purchase_order_or/' . $purchase_order_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('purchase_orders/manager'));
        } else {

        $this->title = 'FOR RELEASE: Purchase Order No. ' . $purchase_order->id;
        // $this->javascript = [
        //     'js/purchase_orders/preview_release'
        // ];

        if (($form_error = $this->_error_msg()) === validation_errors()) {
            $form_error = '';
        }

        $where = [
            'id' => $purchase_order->supplier_id
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $purchase_order_items = $this->purchase_order_item->get_by_purchase_order($purchase_order_id);

        $where = [
            'id' => $purchase_order->added_by
        ];
        $issuer = $this->user->select('', $where, 1);

        $data = [
            'form_error' => $form_error,
            'purchase_order' => $purchase_order,
            'supplier' => $supplier,
            'purchase_order_items' => $purchase_order_items,
            'issuer' => $issuer
            // 'check' => $check
        ];

        $this->build_content('default', 'purchase_orders/preview_release', $data);
        }
    }

    /**
     * Attempt Release
     */
    protected function _attempt_release($purchase_order)
    {

        $or_date = DateTime::createFromFormat('m/d/Y', $this->input->post('or_date'));

        $values = [
            'purchase_order_id' => $purchase_order->id,
            'or_date' => $or_date->format('Y-m-d'),
            'or_no' => $this->input->post('or_no') ?: null,
            'or_amount' => $this->input->post('or_amount'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];


        if (!$purchase_order_or_id = $this->purchase_order_or->insert($values) or !$this->_generate_purchase_order_or_attachment($purchase_order_or_id, $this->_uploaded)) {
            $this->_error = 'server_error';
            return false;
        }

        if(!empty($purchase_order_or_id)){

            $where = [
                'id' => $purchase_order->id
            ];
            $values = [
                'status' => 'released',
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => date('Y-m-d H:i:s')
            ];

            if(!$this->purchase_order->update($where, $values)){

            }
        }

        return $purchase_order_or_id;
    }

    /**
     * Generate Purchase OR Attachments
     */
    public function _generate_purchase_order_or_attachment($purchase_order_or_id, $uploads)
    {
        if (!empty($uploads)) {
            $data = [];
            foreach ($uploads as $uploaded) {
                $data[] = [
                    'purchase_order_or_id' => $purchase_order_or_id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];
            }

            if(!$this->purchase_order_or_attachment->insert_batch($data)){
                $this->_error = 'server_error';
                return false;
            }
        }
        return true;
    }

    public function mark_status() {
        $purchase_order_id = $this->input->post('purchase_order_id');
        $type = $this->input->post('type');
        $remarks = $this->input->post('remarks');
        $user = $_SESSION['user']->id;

        $where = [
            'id' => $purchase_order_id
        ];

        $po = $this->purchase_order->select('', $where, 1);

        $disable = false;
        if ($type === "Release") {
            if ($po->status != 'for release') {
                $disable = true;
            }
            $status = "released";
            $values['released_by'] = $user;
        } else if ($type === "Audit") {
            if ($po->status != 'released') {
                $disable = true;
            }
            $status = "audited";
            $values['audited_by'] = $user;
            $values['audit_remarks'] = $remarks;
        } else {
            $status = "printed";
            $disable = true;
        }

        $values['status'] = $status;

        $response = [
            'is_success' => 1,
            'message' => "Updated the purchase order's status."
        ];

        if ($disable || !$this->purchase_order->update($where, $values))
        {
            $response = [
                'disable' => $disable,
                'purchase_order_id' => $purchase_order_id,
                'type' => $type,
                'is_success' => 0,
                'message' => "Can't update purchase order's status."
            ];
        }

        echo json_encode($response);
    }

    /**
     * Purchase Order is Printed
     */
    public function mark_printed($purchase_order_id)
    {
        $where = [
            'id' => $purchase_order_id,
            'status <>' => 'pending',
            'is_deleted' => 0
        ];
        if (!$purchase_order = $this->purchase_order->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Order is not found.'
            ];
        } elseif (!$this->_attempt_mark_printed($purchase_order)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Purchase Order is successfully marked as printed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Mark as Printed
     */
    public function _attempt_mark_printed($purchase_order)
    {

        $where = [
            'id' => $purchase_order->id
        ];

        $values = [
            'status' => 'printed',
            'printed_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];
        
        if (!$is_deleted = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Get Info for Purchase Order
     */
    public function info($purchase_order_id)
    {
        if (!$purchase_order = $this->purchase_order->get_by_id($purchase_order_id)) {
            http_response_code(400);
            $response = [
                'message' => 'No PO is found.'
            ];
        } else {
            $purchase_order->total = $this->purchase_order->get_total($purchase_order_id);
            $response = [
                'message' => 'PO is found.',
                'purchase_order' => $purchase_order,
                'total' => $this->purchase_order->get_total($purchase_order_id)
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Partially Paid Purchase Orders
     */
    public function partially_paid()
    {
        $data = [
            'purchase_orders' => $this->purchase_order->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('user');
        $this->load->model('purchase_order');
        $this->load->model('purchase_order_item');
        $this->load->model('purchase_order_or');
        $this->load->model('purchase_order_or_attachment');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('item');
        $this->load->model('account_type');
        $this->load->model('customer');
        $this->load->model('supplier');
        $this->load->model('live_sell_usage');
        $this->load->model('live_sell_sub_usage');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return isset($_FILES) ? $this->upload->display_errors() : '';

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_purchase_order_item_ids()
    {
        $purchase_order_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'purchase_order_item_') === 0) {
                $purchase_order_item_ids[] = $value;
            }
        }

        return $purchase_order_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_purchase_order_item_rules($purchase_order_id, $purchase_order_item_ids)
    {
        $rules = [];

        foreach ($purchase_order_item_ids as $purchase_order_item_id) {
            $rules[] = [
                'field' => 'item_' . $purchase_order_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'qty_' . $purchase_order_item_id,
                'label' => 'Qty',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'account_type_' . $purchase_order_item_id,
                'label' => 'Account Type',
                'rules' => 'required'
            ];

            $rules[] = [
                'field' => 'unit_price_' . $purchase_order_item_id,
                'label' => 'Unit Price',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'unit_' . $purchase_order_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]'
            ];

            $rules[] = [
                'field' => 'pref_alw_' . $purchase_order_item_id,
                'label' => 'Pref ALW',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id)
    {
        $this->db->trans_begin();

        if (!$purchase_order_items = $this->_get_purchase_order_item_data()) {
            $this->_error = 'No Purchase Order Item added';
            return false;
        }

        if (!$purchase_order_id = $this->_generate_purchase_order($order_slip_id) or !$this->_insert_purchase_order_items($purchase_order_id, $purchase_order_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $purchase_order_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_purchase_order_item_data()
    {
        $data = [];
        $purchase_order_item_ids = $this->_get_purchase_order_item_ids();

        foreach ($purchase_order_item_ids as $purchase_order_item_id) {
            $item_id = $this->input->post('item_' . $purchase_order_item_id);
            $qty = $this->input->post('qty_' . $purchase_order_item_id);
            $unit = $this->input->post('unit_' . $purchase_order_item_id);
            $est_due_amount = $this->input->post('est_due_amount_' . $purchase_order_item_id);
            $unit_price = $this->input->post('unit_price_' . $purchase_order_item_id);
            $pref_alw = $this->input->post('pref_alw_' . $purchase_order_item_id);
            $unit_price = $this->input->post('unit_price_' . $purchase_order_item_id);
            if(!$unit_price) {
                $unit_price = $this->input->post('current_price_' . $purchase_order_item_id);
            }
            $amount = $this->input->post('amount_' . $purchase_order_item_id);   
            if(!$amount) {
                $unit_price = $this->input->post('est_due_amount_' . $purchase_order_item_id);
            }
            $account_type = $this->input->post('account_type_' . $purchase_order_item_id);
            $act_qty = $this->input->post('act_qty_' . $purchase_order_item_id);
            $act_alw = $this->input->post('act_alw_' . $purchase_order_item_id);
            $act_due_amount = $this->input->post('act_due_amount_' . $purchase_order_item_id);

            $added_by = $_SESSION['user']->id;

            $where = [
                'id' => $item_id,
                'is_deleted' => 0
            ];
            // $item = $this->item->select('', ['id' => $item_id, 'is_deleted' => 0], 1);
            $item = $this->item->select('', $where, 1);

            $data[] = [
                'id' => is_numeric($purchase_order_item_id) ? $purchase_order_item_id : null,
                'order_slip_item_id' => $purchase_order_item_id,
                'item_id' => $item_id,
                'item_name' => $item->name,
                'unit' => $unit,
                'pref_alw' => $pref_alw,
                'qty' => $qty,
                'est_due_amount' => $est_due_amount,
                'unit_price' => $unit_price,
                'unit_price' => $unit_price,
                'added_by' => $added_by,
                'amount' => $amount,
                'account_type' => $account_type,
                'added_on' => date('Y-m-d H:i:s'),
                'act_qty' => $act_qty,
                'act_alw' => $act_alw,
                'act_due_amount' => $act_due_amount,
            ];
        }

        return $data;
    }

    /**
     * Generate purchase_order
     */
    protected function _generate_purchase_order($order_slip_id)
    {
        $customer_id = $this->input->post('customer');
        $supplier_id = $this->input->post('supplier');
        
        $where = [
            'id' => $order_slip_id
        ];
        $order_slip_items = $this->order_slip_item->select('', $where);

        $purchase_order_date = DateTime::createFromFormat('m/d/Y', $this->input->post('purchase_order_date'));
        $date_covered = DateTime::createFromFormat('m/d/Y', $this->input->post('date_covered'));

        // $total = array_reduce($order_slip_items, function ($current_total, $purchase_order_item) {
        //     if (isset($purchase_order_item->amount)) {
        //         return $current_total + $purchase_order_item->amount;
        //     }
        //     return $current_total;
        // });

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }
        $values = [
            'order_slip_id' => $order_slip_id,
            'customer_id' => $customer_id,
            'supplier_id' => $supplier_id,
            'purchase_order_date' => $purchase_order_date->format('Y-m-d') ?:null,
            'date_covered' => $date_covered->format('Y-m-d') ?:null,
            'usage_id' => $this->input->post('usage')?:null,
            // 'total' => $total,
            'sub_usage_id' => $this->input->post('sub_usage')?:null,
            'remarks' => $this->input->post('remarks') ?: null,
            'requisitioner' => $this->input->post('requisitioner') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->purchase_order->insert($values);
    }

    /**
     * Insert purchase_order Items
     */
    protected function _insert_purchase_order_items($purchase_order_id, $purchase_order_items)
    {

        foreach ($purchase_order_items as $purchase_order_item) {
            $values = [
                'purchase_order_id' => $purchase_order_id,
                'order_slip_item_id' => $purchase_order_item['order_slip_item_id'],
                'item_id' => $purchase_order_item['item_id'],
                'qty' => $purchase_order_item['qty'],
                // 'est_due_amount' => $purchase_order_item['est_due_amount'],
                'unit_price' => $purchase_order_item['unit_price'],
                'unit' => $purchase_order_item['unit'],
                'amount' => $purchase_order_item['amount'],
                'pref_alw' => $purchase_order_item['pref_alw'],
                'account_type' => $purchase_order_item['account_type'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];

            if (!$this->purchase_order_item->insert($values)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Insert purchase_order Items
     */
    protected function _receive_purchase_order_items($purchase_order_id, $purchase_order_items)
    {

        foreach ($purchase_order_items as $purchase_order_item) {
            $values = [
                // 'purchase_order_id' => $purchase_order_id,
                // 'order_slip_item_id' => $purchase_order_item['order_slip_item_id'],
                // 'item_id' => $purchase_order_item['item_id'],
                // 'qty' => $purchase_order_item['qty'],
                // // 'est_due_amount' => $purchase_order_item['est_due_amount'],
                // 'unit_price' => $purchase_order_item['unit_price'],
                // 'unit' => $purchase_order_item['unit'],
                // 'amount' => $purchase_order_item['amount'],
                // 'pref_alw' => $purchase_order_item['pref_alw'],
                // 'account_type' => $purchase_order_item['account_type'],
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => date('Y-m-d H:i:s'),
                'act_qty' => $purchase_order_item['act_qty'],
                'act_alw' => $purchase_order_item['act_alw'],
                'act_due_amount' => $purchase_order_item['act_due_amount'],
                // 'status' => 'approved'
            ];

            $where = [
                'id' => $purchase_order_item['id']
            ];
            if (!$this->purchase_order_item->update($where, $values)) {
                return false;
            }
        }
        return true;
    }

    
    protected function _attempt_receive_po($purchase_order)
    {
        $this->db->trans_begin();

        $purchase_order_items = $this->_get_purchase_order_item_data();
        if (!$purchase_order_items = $this->_get_purchase_order_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Purchase Order Item added';
            return false;
        } elseif (!$this->_attempt_receive($purchase_order) || !$this->_receive_purchase_order_items($purchase_order->id, $purchase_order_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($purchase_order)
    {
        $this->db->trans_begin();

        $purchase_order_items = $this->_get_purchase_order_item_data();
        if (!$purchase_order_items = $this->_get_purchase_order_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Purchase Order Item added';
            return false;
        } elseif(!$this->_attempt_delete_items($purchase_order->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No Purchase Order Item added';
            return false;
        } elseif (!$this->_revise_purchase_order($purchase_order->id, $purchase_order_items) || !$this->_insert_purchase_order_items($purchase_order->id, $purchase_order_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise purchase_order
     */
    protected function _revise_purchase_order($purchase_order_id, $purchase_order_items)
    {
        $purchase_order_date = DateTime::createFromFormat('m/d/Y', $this->input->post('purchase_order_date'));
        $date_covered = DateTime::createFromFormat('m/d/Y', $this->input->post('date_covered'));

        $total = array_reduce($purchase_order_items, function ($current_total, $purchase_order_item) {
            if (isset($purchase_order_item['est_due_amount'])) {
                return $current_total + $purchase_order_item['est_due_amount'];
            }
            return $current_total;
        });

        $customer_id = $this->input->post('customer');
        $supplier_id = $this->input->post('supplier');

        $where = [
            'id' => $purchase_order_id
        ];
        $purchase_order = $this->purchase_order->select('', $where, 1);

        $values = [
            'customer_id' => $customer_id,
            'supplier_id' => $supplier_id,
            'purchase_order_date' => $purchase_order_date->format('Y-m-d'),
            'date_covered' => $date_covered->format('Y-m-d'),
            'usage_id' => $this->input->post('usage')?:null,
            'sub_usage_id' => $this->input->post('sub_usage')?:null,
            'total' => $total,
            'remarks' => $this->input->post('remarks') ?: null,
            'requisitioner' => $this->input->post('requisitioner') ?: null,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s'),
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
            if($purchase_order->attachment) {
                $values['attachment'] = $values['attachment'] . '🔥' . $purchase_order->attachment;
            }
        }

        return $this->purchase_order->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($purchase_order)
    {
        $where = [
            'id' => $purchase_order->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'approved_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($purchase_order)
    {
        $where = [
            'id' => $purchase_order->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->purchase_order->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($purchase_order_id)
    {
        $where = [
            'purchase_order_id' => $purchase_order_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->purchase_order_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if purchase_order Item Belongs to purchase_order
     */
    public function _is_purchase_order_item($purchase_order_item_id, $purchase_order_id)
    {
        if (!is_numeric($purchase_order_item_id)) {
            return true;
        }

        $where = [
            'id' => $purchase_order_item_id,
            'purchase_order_id' => $purchase_order_id
        ];

        return !empty($this->purchase_order_item->select('', $where, 1));
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        foreach ($this->_uploaded as $uploaded) {
            unlink($uploaded['full_path']);
        }
    }

    /**
     * Callback: Check if purchase_order Term is Either Cash or Check
     */
    public function _is_purchase_order_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}