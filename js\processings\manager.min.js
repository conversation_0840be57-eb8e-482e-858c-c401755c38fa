!function(r){"use strict";function n(e,t){r.when(r.ajax({url:BASE_URI+"processings/receive_processing/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}function s(e,t){r.when(r.ajax({url:BASE_URI+"processings/delete/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}r(function(){(e=r(e=".tableA")).find("thead th"),t=r(".tab-link.active").attr("data-status");var e,t,a=e.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+e.attr("data-package")+"?status="+t});r(".tableA").on("click",".action-receive",function(e){var t=r(e.currentTarget).closest("tr.processing").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the processing "+t+"?",function(){n(e.currentTarget.getAttribute("data-id"),a)}).setHeader("<em>receive processing</em>")}).on("click",".action-delete",function(e){var t=r(e.currentTarget).closest("tr.processing").find("td"),t=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to delete processing "+t+"?",function(){s(e.currentTarget.getAttribute("data-id"),a)}).setHeader("<em>Delete processing</em>")}),r(".tab-link").click(function(e){r(".tab-link.active").removeClass("active"),e.currentTarget.classList.add("active");var t=BASE_URI+"fetch/processings?status="+e.currentTarget.getAttribute("data-status"),e=BASE_URI+"processings/manager?status="+e.currentTarget.getAttribute("data-status");console.log(t),window.history.pushState(null,null,e),a.ajax.url(t).load()})})}(jQuery);