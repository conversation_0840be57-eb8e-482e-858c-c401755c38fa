+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(live_sell_supplierId, dataTable) {
		$.when(deleteRow(live_sell_supplierId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(live_sell_supplierId) {
		return $.ajax({
			url: BASE_URI + 'live_sell_suppliers/delete/' + live_sell_supplierId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.live_sell_supplier').find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete ' + name + '?',
				function () {
					var live_sell_supplierId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(live_sell_supplierId, dataTable);
				}
			).setHeader('<em>Delete Live Sell Supplier</em>');
		});
	});
}(jQuery);
