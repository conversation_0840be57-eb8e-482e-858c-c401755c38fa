<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_invoice_sales_report extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Users
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'pos_invoice';
        $primary_key = 'pos_invoice.id';
    
        $columns = [
            $this->_get_id(),
            $this->_get_customer(),
            $this->_get_payment_type(),
            $this->_get_balance(),
            $this->_get_added_by(),
        ];
    
        $joins = <<<EOT
    pos_invoice
    LEFT JOIN customer ON customer.id = pos_invoice.customer_id AND customer.is_deleted = 0
    LEFT JOIN user ON user.id = pos_invoice.added_by AND user.is_deleted = 0
    EOT;
    
        $where = <<<EOT
    pos_invoice.is_deleted = 0
    EOT;
    
        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);
        if (isset($from) AND $from_date = date("Y-m-d 00:00:00", strtotime(urldecode($from))) and
            isset($to) AND $to_date = date("Y-m-d 23:59:00", strtotime(urldecode($to))) 
        ) {
            $where .= <<<EOT
    
    AND IFNULL(pos_invoice.invoice_date, pos_invoice.added_on) BETWEEN "{$from_date}" AND "{$to_date}"
    EOT;
        }

    if($type = $this->input->get('payment_type')) {
            $where .= <<<EOT
    
    AND pos_invoice.terms = "$type"
    EOT;
        }
    
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }
    

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'id',
            'dt' => 0,
            'field' => 'id'
        ];
    }

    /**
     * Get Customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'COALESCE(pos_invoice.customer_name, customer.name)',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'

        ];
    }

    /**
     * Get Customer
     */
    protected function _get_payment_type()
    {
        return [
            'db' => 'pos_invoice.terms',
            'as' => 'payment_type',
            'dt' => 2,
            'field' => 'payment_type'

        ];
    }

    /**
     * Get Balance
     */
    protected function _get_balance()
    {
        return [
            'db' => 'pos_invoice.total',
            'as' => 'balance',
            'dt' => 3,
            'field' => 'balance',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Added By
     */
    protected function _get_added_by()
    {
        return [
            // 'db' => 'user.full_name',
            'db' => 'CONCAT(user.full_name, "<br>", DATE_FORMAT(pos_invoice.added_on, "%b %d, %Y %h:%i %p"))',
            'as' => 'added_by',
            'dt' => 4,
            'field' => 'added_by',
        ];
    }
}
