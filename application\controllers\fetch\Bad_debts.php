<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Bad_debts extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch invoices
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice';
        $primary_key = 'invoice.id';

        $columns = [
            $this->_get_customer_id(),
            $this->_get_invoice_ids(),
            $this->_get_class(),
            $this->_get_customer(),
            $this->_get_invoices(),
            $this->_get_total_per_invoice(),
            $this->_get_total(),
        ];

        $joins = <<<EOT
invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
EOT;

        $where = <<<EOT
invoice.is_deleted = 0
AND invoice.status = "bad_debt"
EOT;

        $customer = $this->input->get('customer', true) ?: null;

        if(!empty($customer)){
            $where .= <<<EOT

AND invoice.customer_id = '{$customer}'
EOT;
        }

        $group_by = "customer.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_customer_id()
    {
        return [
            'db' => 'invoice.customer_id',
            'dt' => 'DT_RowCustomerId',
            'field' => 'customer_id'
        ];
    }

    /**
     * Get Invoice ID
     */
    protected function _get_invoice_ids()
    {
        return [
            'db' => 'GROUP_CONCAT(invoice.id SEPARATOR "<br>")',
            'as' => 'invoice_ids',
            'dt' => 'DT_RowInvoiceId',
            'field' => 'invoice_ids'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice.customer_id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice';
            }
        ];
    }

    /**
     * Get Customer Name
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 0,
            'field' => 'customer'
        ];
    }

    /**
     * Get All Invoices
     */
    protected function _get_invoices()
    {
        return [
            'db' => 'GROUP_CONCAT(CONCAT("• ", invoice.invoice_no) SEPARATOR "<br>")',
            'as' => 'invoice_no',
            'dt' => 1,
            'field' => 'invoice_no',
            'formatter' => function ($d, $row) {
                $invoiceNumbers = explode('<br>', $d);
                $ids = explode('<br>', $row['invoice_ids']);

                $res = '';

                foreach ($invoiceNumbers as $i => $invoice) {
                    $res .= '<a href="../invoices/preview/' . $ids[$i] . '" target="_blank" class="btn btn-link" style="margin: 0; padding: 0;">' . $invoice . '</a><br style="margin: 0; padding: 0;">';
                }

                return $res;
            }
        ];
    }

    /**
     * Get Total Per Invoice
     */
    protected function _get_total_per_invoice()
    {
        return [
            'db' => 'GROUP_CONCAT(FORMAT(invoice.total, 2) SEPARATOR "<br>")',
            'as' => 'total_per_invoice',
            'dt' => 2,
            'field' => 'total_per_invoice'
        ];
    }

    /**
     * Get Total
     */
    protected function _get_total()
    {
        return [
            'db' => 'FORMAT(SUM(invoice.total), 2)',
            'as' => 'total',
            'dt' => 3,
            'field' => 'total'
        ];
    }
}
