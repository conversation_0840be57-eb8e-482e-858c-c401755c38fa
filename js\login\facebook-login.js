'use strict';

function statusChangeCallback(response) {
	if (response.status === 'connected') {
		attemptLogin(response);
	} else {
		document.getElementById('error').innerHTML = 'Please log ' +
			'into this app.';
	}
}

function checkLoginState() {
	FB.getLoginStatus(function (response) {
		statusChangeCallback(response);
	});
}

function attemptLogin(response) {
	var accessToken = response.authResponse.accessToken;
	window.location.href = BASE_URI + 'login/fb_login?token=' + accessToken;
}

window.fbAsyncInit = function () {
	FB.init({
		appId: '256961608204790',
		cookie: true,
		xfbml: true,
		version: 'v2.8'
	});

	FB.AppEvents.logPageView();

	FB.getLoginStatus(function (response) {
		statusChangeCallback(response);
	});
};

(function (d, s, id) {
	'use strict';

	var js, fjs = d.getElementsByTagName(s)[0];
	if (d.getElementById(id)) {
		return;
	}
	js = d.createElement(s);
	js.id = id;
	js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId=256961608204790&autoLogAppEvents=1';
	fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));
