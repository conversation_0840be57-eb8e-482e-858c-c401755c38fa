<?php
class Customer_attachment extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'customer_attachment';
        parent::__construct();
    }



    /**
     * Get Attachment by Customer Id
     */
    public function get_by_id($id){

        $sql = <<<EOT
SELECT customer_attachment.*
FROM customer_attachment
WHERE customer_attachment.is_deleted = 0
AND customer_attachment.customer_id = ?
EOT;
            $binds = [$id];

            return $this->advanced_query($sql, $binds);
        }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
