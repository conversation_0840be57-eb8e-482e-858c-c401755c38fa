<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_voided_invoices extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Invoice Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'pos_payment';
        $primary_key = 'pos_payment.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_voided_date(),
            $this->_get_pos_invoice_no(),
            $this->_get_voided_by(),
            $this->_get_type(),
            $this->_get_grand_total(),
            $this->_get_paid_amount(),
            $this->_get_customer(),
            $this->_get_void_reason()
        ];

        $joins = <<<EOT
pos_invoice
LEFT JOIN pos_payment ON pos_payment.pos_invoice_id = pos_invoice.id
LEFT JOIN customer ON customer.id = pos_invoice.customer_id
LEFT JOIN user ON user.id = pos_invoice.updated_by
EOT;

        $where = <<<EOT

pos_invoice.is_deleted = 1
EOT;
        
        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);
        if (isset($from) AND $from_date = date("Y-m-d 00:00:00", strtotime(urldecode($from))) and
            isset($to) AND $to_date = date("Y-m-d 23:59:00", strtotime(urldecode($to))) 
        ) {
            $where .= <<<EOT

AND pos_invoice.updated_on BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $group_by = "id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
            // SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'pos_invoice.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'pos_invoice';
            }
        ];
    }

    /**
     * Get Account Type
     */
    protected function _get_voided_date()
    {
        return [
            'db' => 'pos_invoice.updated_on',
            'as' => 'voided_date',
            'dt' => 0,
            'field' => 'voided_date',
            'formatter' => function($d, $row) {
                // return $d;
                return DateTime::createFromFormat('Y-m-d H:i:s', $d)->format("F d, Y");
            }
        ];
    }

    /**
     * Get Pos Invoice Number
     */
    protected function _get_pos_invoice_no()
    {
        return [
            'db' => 'pos_invoice.id',
            'as' => 'pos_invoice_id',
            'dt' => 1,
            'field' => 'pos_invoice_id'
        ];
    }

    /**
     * Get Voided BY
     */
    protected function _get_voided_by()
    {
        return [
            'db' => 'user.full_name',
            'as' => 'voided_by',
            'dt' => 2,
            'field' => 'voided_by'
        ];
    }

    /**
     * Get Voided BY
     */
    protected function _get_type()
    {
        return [
            'db' => 'pos_invoice.terms',
            'as' => 'type',
            'dt' => 3,
            'field' => 'type'
        ];
    }

    /**
     * Get Grand Total
     */
    protected function _get_grand_total()
    {
        return [
            'db' => 'FORMAT(IFNULL(pos_invoice.total, 0), 2)',
            'as' => 'grand_total',
            'dt' => 4,
            'field' => 'grand_total'
        ];
    }

    /**
     * Get Paid Amount
     */
    protected function _get_paid_amount()
    {
        return [
            'db' => 'FORMAT(IFNULL(pos_payment.paid_amount, 0), 2)',
            'as' => 'paid_amount',
            'dt' => 5,
            'field' => 'paid_amount'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 6,
            'field' => 'customer'
        ];
    }

    /**
     * Get Remarks
     */
    protected function _get_void_reason()
    {
        return [
            'db' => 'pos_invoice.is_deleted_reason',
            'as' => 'void_reason',
            'dt' => 7,
            'field' => 'void_reason'
        ];
    }   
}