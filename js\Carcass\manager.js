+function ($) {
    'use strict';

    function createDataTable(selector) {
        var $table = $(selector);

        var dataTable = $table.DataTable({
            language: {
                infoFiltered: ''
            },
            lengthChange: false,
            info: false,
            paging: false, // Disable pagination
            searching: true, // Keep search functionality
            ordering: true, // Keep sorting functionality
            order: [[0, 'asc']], // Order by SKU ascending
            columns: [
                { data: 'sku', title: 'SKU', defaultContent: '', className: 'text-left' },
                { data: 'size', title: 'Size', defaultContent: '', className: 'text-center' },
                { data: 'range', title: 'Range', defaultContent: '', className: 'text-center' },
                { data: 'no', title: 'NO', defaultContent: '', className: 'text-center' },
                { data: 'pcs', title: 'PCS', defaultContent: '', className: 'text-center' },
                { data: 'kgs', title: 'KGS', defaultContent: '', className: 'text-center' },
                { data: 'actions', title: '', orderable: false, searchable: false, className: 'text-right' }
            ],
            autoWidth: false,
            processing: true,
            serverSide: true,
            dom: 'frt', // Only show filter (search), table, and processing
            ajax: {
                url: BASE_URI + 'fetch/Carcasses',
                type: 'POST',
                dataSrc: 'data', // Explicitly specify data source
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX error:', error, thrown);
                    console.log('Response:', xhr.responseText);
                    alertify.error('Error loading data. Please try again.');
                }
            }
        });

        return dataTable;
    }

    $(document).ready(function() {
        // Initialize DataTable
        var dataTable = createDataTable('#carcasses-table');

        // Handle delete action
        $(document).on('click', '.action-delete', function() {
            var id = $(this).data('id');
            if (confirm('Are you sure you want to delete this carcass record?')) {
                window.location.href = BASE_URI + 'Carcasses/delete/' + id;
            }
        });
    });

}(jQuery);