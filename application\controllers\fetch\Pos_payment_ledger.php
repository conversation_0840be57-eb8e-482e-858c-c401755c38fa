<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_payment_ledger extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Invoice Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'pos_payment';
        $primary_key = 'pos_payment.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_payment_date(),
            $this->_get_pos_invoice_no(),
            $this->_get_paid_amount(),
            $this->_get_customer(),
            $this->_get_remarks(),
            $this->_get_payment_of()
        ];

        $joins = <<<EOT
(
        SELECT pos_payment.id AS id, IFNULL(pos_payment.payment_date, pos_payment.added_on) AS payment_date, pos_payment.pos_invoice_id AS pos_invoice_id, IFNULL(pos_payment.paid_amount, 0) AS paid_amount, customer.name AS customer_name, pos_payment.remarks AS remarks, pos_payment.added_on AS added_on, pos_payment.is_deleted AS is_deleted, "POS Invoice" AS payment_of, customer.id AS customer_id
        FROM pos_payment
        LEFT JOIN pos_invoice ON pos_invoice.id = pos_payment.pos_invoice_id
        LEFT JOIN customer ON customer.id = pos_invoice.customer_id

        UNION

        SELECT payment.id AS id, IFNULL(payment.payment_date, payment.added_on) AS payment_date, invoice.id AS pos_invoice_id, IFNULL(payment.total_paid, 0) AS paid_amount, customer.name AS customer_name, "" AS remarks, payment.added_on AS added_on, payment.is_deleted AS is_deleted, "Invoice" AS payment_of, customer.id AS customer_id
        FROM payment
        LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
        LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
        LEFT JOIN customer ON customer.id = invoice.customer_id
        WHERE paid_invoice.doc_type = "invoice"

        UNION

         SELECT 
        payment.id AS id, 
        IFNULL(payment.payment_date, date(payment.added_on)) AS payment_date, 
        cash_bond.id AS pos_invoice_id, 
        IFNULL(payment.total_paid, 0) AS paid_amount, 
        customer.name AS customer_name, 
        "" AS remarks, payment.added_on AS added_on, payment.is_deleted AS is_deleted, "Cash Bond" AS payment_of, customer.id AS customer_id
        FROM payment
        LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
        LEFT JOIN cash_bond ON cash_bond.id = paid_invoice.invoice_id
        LEFT JOIN customer ON customer.id = cash_bond.customer_id
        WHERE paid_invoice.doc_type = 'cash_bond'

) AS pos_payment
EOT;

        $where = <<<EOT

pos_payment.is_deleted = 0
EOT;
        
        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);
        if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
            isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
        ) {
            $where .= <<<EOT

AND IFNULL(pos_payment.payment_date, pos_payment.added_on) BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $payment_of = $this->input->get('payment_of', true);
if($payment_of) {
            $where .= <<<EOT

AND pos_payment.payment_of = "$payment_of"
EOT;
}

        $customer = $this->input->get('customer', true);
if($customer) {
            $where .= <<<EOT

AND pos_payment.customer_id = $customer
EOT;
}

        // $group_by = "payment_date";

        echo json_encode(
            // SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'pos_payment.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'pos_payment.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'pos_payment';
            }
        ];
    }

    /**
     * Get Account Type
     */
    protected function _get_payment_date()
    {
        return [
            'db' => 'IFNULL(pos_payment.payment_date, pos_payment.added_on)',
            'as' => 'payment_date',
            'dt' => 0,
            'field' => 'payment_date',
            'formatter' => function($d, $row) {
                // return $d;
                return DateTime::createFromFormat('Y-m-d H:i:s', $d)->format("F d, Y");
            }
        ];
    }

    /**
     * Get Pos Invoice Number
     */
    protected function _get_pos_invoice_no()
    {
        return [
            'db' => 'pos_payment.pos_invoice_id',
            'as' => 'pos_invoice_id',
            'dt' => 1,
            'field' => 'pos_invoice_id',
            'formatter' => function ($d, $row) {
                if($row["payment_of"] == "Invoice") {
                    $res = '<a href="../invoices/preview/' . $d . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                } 
                else if($row["payment_of"] == "Cash Bond") {
                    $res = '<a href="../cash_bonds/preview/' . $d . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                }
                else {
                    $res = '<a href="../pos_invoices/preview/' . $d . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                }

                return $res;
            }
        ];
    }

    /**
     * Get Paid Amount
     */
    protected function _get_paid_amount()
    {
        return [
            'db' => 'FORMAT(IFNULL(pos_payment.paid_amount, 0), 2)',
            'as' => 'paid_amount',
            'dt' => 2,
            'field' => 'paid_amount'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_customer()
    {
        return [
            'db' => 'pos_payment.customer_name',
            'as' => 'customer',
            'dt' => 3,
            'field' => 'customer'
        ];
    }

    /**
     * Get Remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'pos_payment.remarks',
            'as' => 'remarks',
            'dt' => 4,
            'field' => 'remarks'
        ];
    }

    /**
     * Get Payment From
     */
    protected function _get_payment_of()
    {
        return [
            'db' => 'pos_payment.payment_of',
            'as' => 'payment_of',
            'dt' => 5,
            'field' => 'payment_of'
        ];
    } 
}