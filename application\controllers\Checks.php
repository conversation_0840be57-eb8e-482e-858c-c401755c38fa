<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Serendipite Check Maker
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Checks extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('checks/manager'));
    }

    /**
     * Add Check Main
     */
    public function add()
    {
        $check_purchase_ids = $this->_get_check_purchase_ids();
        $rules = array_merge($this->config->item('checks/add'), $this->_get_check_purchase_rules('', $check_purchase_ids));

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('checks/manager'));
        } else {
            $this->title = 'Issue a Check';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
                'js/checks/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0,
            ];
            $banks = $this->bank->select('', $where);
            $where = [
                'is_deleted' => 0,
                'is_active'  => 1,
            ];
            $suppliers = $this->supplier->select('', $where);

            $supplier_id = 0 ;
            if (!empty($this->input->post('supplier'))) {
                $supplier_id = $this->input->post('supplier');
            }

            $where = <<<EOT
purchase.supplier_id = {$supplier_id}
AND purchase.printed_by > 0
AND is_deleted = 0
EOT;
            $purchases = $this->purchase->select('', $where);

            $data = [
                'form_error' => $form_error,
                'banks' => $banks,
                'suppliers' => $suppliers,
                'check_no' => $this->input->post('check_no'),
                'check_purchase_ids' => $check_purchase_ids,
                'purchases' => $purchases
            ];
            $this->build_content('default', 'checks/add', $data);
        }
    }

    /**
     * Edit Check Main
     */
    public function edit($check_id = null)
    {
        $where = [
            'id' => $check_id,
            'is_deleted' => 0
        ];
        ($check = $this->check_slip->select('', $where, 1) and $check->status === 'pending') or show_404();

        $check_purchase_ids = $this->_get_check_purchase_ids();

        $rules = $this->config->item('checks/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit($check)) {
            redirect(site_url('checks/manager'));
        } else {
            $this->title = 'Edit an Issued Check';
            $this->css = [
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify',
            ];
            $this->javascript = [
                'lib/input-autosize/dist/autosize',
                'lib/select2/dist/js/select2.full',
                'lib/alertify/build/alertify',
                'js/checks/edit'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $banks = $this->bank->select('', $where);
            $suppliers = $this->supplier->select('', $where);

            $where = <<<EOT
is_deleted = 0
EOT;
            $purchases = $this->purchase->select('', $where);

            $where = [
                'check_id' => $check->id,
                'is_deleted' => 0
            ];
            $check_purchases = $this->check_purchase->select('', $where);

            $data = [
                'check' => $check,
                'form_error' => $form_error,
                'suppliers' => $suppliers,
                'banks' => $banks,
                'purchases' => $purchases,
                'check_no' => $this->input->post('check_no') ?: $check->check_no,
                'check_purchases' => $check_purchases,
                'check_purchase_ids' => $check_purchase_ids
            ];
            $this->build_content('default', 'checks/edit', $data);
        }
    }

    /**
     * Print Check Main
     */
    public function preview($check_id = null)
    {
        $check = $this->check_slip->get_preview($check_id) or show_404();

        $where = [
            'check_id' => $check_id
        ];
        $check_purchases = $this->check_purchase->select('', $where);

        $data = [
            'check' => $check,
            'check_purchases' => $check_purchases
        ];
        $this->load->view('check_templates/' . $check->print_template, $data);
    }


    /**
     * Print Check Voucher
     */
    public function print_checkvoucher($check_id = null)
    {
        $check = $this->check_slip->get_preview($check_id) or show_404();

        $where = [
            'check_id' => $check_id
        ];
        $check_purchases = $this->check_purchase->select('', $where);

        $data = [
            'check' => $check,
            'check_purchases' => $check_purchases
        ];
        $this->load->view('check_templates/check_voucher', $data);
    }

    /**
     * Delete Check Main
     */
    public function delete($check_id = null)
    {
        $where = [
            'id' => $check_id,
            'is_deleted' => 0
        ];

        if (!$check = $this->check_slip->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Check is not found.'
            ];
        } elseif (!$this->_attempt_delete($check)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Check is successfully removed.'
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Approve Check Main
     */
    public function approve($check_id = null)
    {
        $where = [
            'id' => $check_id,
            'status' => 'pending',
            'is_deleted' => 0
        ];

        if (!$check = $this->check_slip->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Check is not found.'
            ];
        } elseif (!$this->_attempt_approve($check)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Check is successfully approved.'
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Printed Check Main
     */
    public function printed($check_id = null)
    {
        $where = [
            'id' => $check_id,
            'status <>' => 'pending',
            'is_deleted' => 0
        ];

        if (!$check = $this->check_slip->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Check is not found.'
            ];
        } elseif (!$this->_attempt_printed($check)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Check is successfully printed.'
            ];
        }

        exit(json_encode($response));
    }
    /**
     * Checks check details
     */
    public function details($check_id = null)
    {

        $check = $this->check_slip->get_preview($check_id);
        $check_pos = $this->check_purchase->get_by_check($check_id);
        $response = [
            'check' => $check,
            'check_pos' => $check_pos
        ];

        exit(json_encode($response));
    }

    /**
     * Checks manager main
     */
    public function manager()
    {
        $this->title = 'List of Issued Checks';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/checks/manager'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $banks = $this->bank->select('', $where);
        $data = [
            'banks' => $banks
        ];
        $this->build_content('default', 'checks/manager', $data);
    }

    /**
     * Check enrollment for bank
     */
    public function enroll($bank_id = null)
    {
        $where = [
            'id' => $bank_id,
            'is_deleted' => 0
        ];
        $bank = $this->bank->select('', $where, 1) or show_404();

        $rules = $this->config->item('checks/enroll');

        if ($this->_validate_form($rules) && $this->_attempt_enroll($bank)) {
            redirect(site_url('checks/enroll/' . $bank->id));
        } else {
            $this->title = 'Check Enrollment';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $batches = $this->enrolled_check->get_batch_info($bank->id);

            $data = [
                'form_error' => $form_error,
                'bank' => $bank,
                'batches' => $batches
            ];
            $this->build_content('default', 'checks/enroll', $data);
        }
    }

    /**
     * Check enrollment for bank via AJAX main
     */
    public function enroll_remote($bank_id = null)
    {
        $where = [
            'id' => $bank_id,
            'is_deleted' => 0
        ];

        if (!$bank = $this->bank->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Bank is not found.'
            ];
            exit(json_encode($response));
        }

        $rules = $this->config->item('checks/enroll');

        if (!$this->_validate_form($rules) || !$this->_attempt_enroll($bank)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Checks are successfully enrolled.'
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Get Check No
     */
    public function no($bank_id = null)
    {
        $where = [
            'bank_id' => $bank_id,
            'status' => 'available'
        ];
        if (!$enrolled_checks = $this->enrolled_check->select('', $where)) {
            http_response_code(400);
            $response = [
                'message' => 'No available check number for bank.'
            ];
        } else {
            $response = [
                'message' => 'An available check number for bank is found.',
                'enrolled_checks' => $enrolled_checks
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Load pre-requisite models, config files, and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('check_slip');
        $this->load->model('check_purchase');
        $this->load->model('purchase');
        $this->load->model('purchase');
        $this->load->model('enrolled_check');
        $this->load->model('check_template');
        $this->load->model('bank');
        $this->load->model('supplier');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return $this->upload->display_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Upload file to server
     */
    protected function _attempt_upload()
    {
        $config = [
            'upload_path' => FCPATH . 'assets/asylum',
            'allowed_types' => 'gif|png|jpg',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => true
        ];
        $this->upload->initialize($config);

        if (!$upload_ok = $this->upload->do_upload('image_upload')) {
            $this->_error = 'upload_error';
        } else {
            $this->_uploaded = $this->upload->data();
        }

        return $upload_ok || empty($_FILES['image_upload']['name']);
    }

    /**
     * Move uploaded file
     */
    protected function _move_uploaded()
    {
        if ($this->_uploaded) {
            $re_path = FCPATH . 'img/thumbnails/';
            is_dir($re_path) or mkdir($re_path, 0755, true);
            rename($this->_uploaded['full_path'], $re_path . $this->_uploaded['file_name']);
        }
    }

    /**
     * Delete uploaded file
     */
    protected function _delete_uploaded()
    {
        if ($this->_uploaded) {
            unlink($this->uploaded['full_path']);
        }
    }

    /**
     * Get checked purchase IDs
     */
    protected function _get_check_purchase_ids()
    {
        $check_purchase_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'check_purchase_') === 0) {
                $check_purchase_ids[] = $value;
            }
        }

        return $check_purchase_ids;
    }
    /**
     * Get checked suppliers IDs
     */
    protected function _get_check_suppliers_ids()
    {
        $check_suppliers_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'check_purchase_') === 0) {
                $check_purchase_ids[] = $value;
            }
        }

        return $check_suppliers_ids;
    }

    /**
     * Get checked purchase rules
     */
    protected function _get_check_purchase_rules($check_id, $check_purchase_ids)
    {
        $rules = [];

        foreach ($check_purchase_ids as $check_purchase_id) {
            $rules[] = [
                'field' => 'check_purchase_' . $check_purchase_id,
                'label' => 'Checked Purchase',
                'rules' => 'trim|callback__exists_in_check[' . $check_id . ']',
                'errors' => [
                    '_exists_in_check' => 'Checked Purchase is not found.'
                ]
            ];

            $rules[] = [
                'field' => 'purchase_' . $check_purchase_id,
                'label' => 'Purchase Invoice',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'amount_' . $check_purchase_id,
                'label' => 'Amount',
                'rules' => 'required|trim'
            ];
        }

        return $rules;
    }

    /**
     * Get checked purchase data
     */
    protected function _get_check_purchase_data()
    {
        $data = [];

        $check_purchase_ids = $this->_get_check_purchase_ids();

        foreach ($check_purchase_ids as $check_purchase_id) {
            $purchase_id = $this->input->post('purchase_' . $check_purchase_id);
            $amount = $this->input->post('amount_' . $check_purchase_id) ?: 0;
            $amount = str_replace(',', '', $amount);

            $data[] = [
                'id' => is_numeric($check_purchase_id) ? $check_purchase_id : null,
                'purchase_id' => $purchase_id,
                'amount' => $amount,
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Generate Checked Purchases
     */
    protected function _generate_check_purchases($check_id, $check_purchases)
    {

        $where = [
            'check_id' => $check_id,
            'is_deleted' => 0
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->check_purchase->update($where, $values)) {
            return false;
        }

        foreach ($check_purchases as $check_purchase) {
            if (is_numeric($check_purchase['id'])) {
                $where = [
                    'id' => $check_purchase['id']
                ];
                $values = [
                    'purchase_id' => $check_purchase['purchase_id'],
                    'amount' => $check_purchase['amount'],
                    'updated_by' => $check_purchase['added_by'],
                    'updated_on' => $check_purchase['added_on']
                ];
                if (!$this->check_purchase->update($where, $values)) {
                    return false;
                }
            } else {
                $values = [
                    'check_id' => $check_id,
                    'purchase_id' => $check_purchase['purchase_id'],
                    'amount' => $check_purchase['amount'],
                    'added_by' => $check_purchase['added_by'],
                    'added_on' => $check_purchase['added_on']
                ];
                if (!$this->check_purchase->insert($values)) {
                    return false;
                }
            }
        }


        return true;
    }

    /**
     * Attempt add
     */
    protected function _attempt_add()
    {
        if (!$check_purchases = $this->_get_check_purchase_data()) {
            $this->_error = 'no_check_purchase';
            return false;
        }

        $amount = array_reduce($check_purchases, function ($current_total, $purchase_item) {
            return $current_total + $purchase_item['amount'];
        });

        $check_date = DateTime::createFromFormat('m/d/Y', $this->input->post('check_date'));

        $values = [
            'bank_id' => $this->input->post('bank'),
            'check_no' => $this->input->post('check_no'),
            'check_date' => $check_date->format('Y-m-d'),
            'amount' => floatval(str_replace(',', '', $this->input->post('total'))),
            'payee' => $this->input->post('supplier'),
            'particulars' => $this->input->post('particulars') ?: null,
            'sig_1' => $this->input->post('sig_1') ?: null,
            'sig_2' => $this->input->post('sig_2') ?: null,
            'supplier_id' => $this->input->post('supplier'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        $this->db->trans_begin();

        if ($check_id = $this->check_slip->insert($values) and $this->_generate_check_purchases($check_id, $check_purchases)
            and $this->_update_purchase_paid_amount($check_purchases, $check_id)) {

            $this->db->trans_commit();
            return $check_id;
        } else {
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
    }
    /**update paid amount in purchases */

    protected function _update_purchase_paid_amount($check_purchases, $check_id)
    {

        foreach ($check_purchases as $check_purchase) {

            $where = [
                'id' => $check_purchase['purchase_id'],
                'is_deleted' => 0
            ];

            $purchase = $this->purchase->select('', $where, 1);

            $purchased_paid = $purchase->paid_amount + $check_purchase['amount'];

            $values = [
                'paid_amount' => $purchased_paid,
                'check_slip_id' => $check_id,
                'status' => 'check issued',
                'updated_on' => date('Y-m-d H:i:s'),
                'updated_by' => $_SESSION['user']->id
            ];

            if (!$this->purchase->update($where, $values)) {

                $this->_error = 'server_error';
                return false;
            }

        }

        return true;
    }
    /**
     * Attempt edit
     */
    protected function _attempt_edit($check)
    {

        if (!$check_purchases = $this->_get_check_purchase_data()) {
            $this->_error = 'no_check_purchase';
            return false;
        }

        $amount = array_reduce($check_purchases, function ($current_total, $purchase_item) {
            return $current_total + $purchase_item['amount'];
        });

        $check_date = DateTime::createFromFormat('m/d/Y', $this->input->post('check_date'));

        $where = [
            'id' => $check->id
        ];
        $values = [
            'bank_id' => $this->input->post('bank'),
            'check_date' => $check_date->format('Y-m-d'),
            'check_no' => $this->input->post('check_no'),
            'amount' => $amount,
            'payee' => $this->input->post('supplier'),
            'particulars' => $this->input->post('particulars') ?: null,
            'sig_1' => $this->input->post('sig_1') ?: null,
            'sig_2' => $this->input->post('sig_2') ?: null,
            'supplier_id' => $this->input->post('supplier'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        $this->db->trans_begin();

        if ($this->check_slip->update($where, $values) && $this->_generate_check_purchases($check->id, $check_purchases) and $this->_update_purchase_paid_amount($check_purchases, $check->id)) {
            $this->db->trans_commit();
            return true;
        } else {
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
    }

    /**
     * Attempt approve
     */
    protected function _attempt_approve($check)
    {

        $where = [
            'check_slip_id' => $check->id,
            'is_deleted' => 0
        ];

        $values = [
            'status' => 'for printing',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if(!$this->purchase->update($where, $values)){
            $this->_error = 'server_error';
            return false;
        }

        $where = [
            'id' => $check->id
        ];

        $values = [
            'status' => 'approved',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->check_slip->update($where, $values) or !$this->_error = 'server_error';
    }

    /**
     * Attempt printed
     */
    protected function _attempt_printed($check)
    {

        if(in_array($_SESSION['user']->role_id,[ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CASHIER, ROLE_CENTRAL_ACCOUNTING])){
            $where = [
                'check_slip_id' => $check->id,
                'is_deleted' => 0
            ];

            $values = [
                'status' => 'printed',
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => date('Y-m-d H:i:s')
            ];

            if(!$this->purchase->update($where, $values)){
                $this->_error = 'server_error';
                return false;
            }
        }

        $where = [
            'id' => $check->id
        ];
        $values = [
            'status' => 'printed',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->check_slip->update($where, $values) or !$this->_error = 'server_error';
    }

    /**
     * Attempt delete
     */
    protected function _attempt_delete($check)
    {
        $where = [
            'check_slip_id' => $check->id,
            'is_deleted' => 0
        ];

        $values = [
            'check_slip_id' => NULL,
            'status' => 'approved',
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if(!$this->purchase->update($where, $values)){
            $this->_error = 'server_error';
            return false;
        }

        $where = [
            'id' => $check->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->check_slip->update($where, $values) or !$this->_error = 'server_error';
    }

    /**
     * Attempt enroll checks
     */
    protected function _attempt_enroll($bank)
    {
        $values = [];

        $batch_id = random_string('alnum', 32);
        $from = $this->input->post('from');
        $to = $this->input->post('to');

        while ($from <= $to) {
            $values[] = [
                'bank_id' => $bank->id,
                'batch_id' => $batch_id,
                'doc_no' => $from,
                'added_by' => $_SESSION['user']->id
            ];

            $from++;
        }

        return $this->enrolled_check_slip->insert_batch($values) or !$this->_error = 'server_error';
    }

    /**
     * Callback: Does ID belongs to a bank
     */
    public function _is_bank($bank_id)
    {
        if (trim($bank_id) === '') {
            return true;
        }

        $where = [
            'id' => $bank_id,
            'is_deleted' => 0
        ];

        return !empty($this->bank->select('', $where, 1)) or !$this->_error = 'server_error';
    }

    /**
     * Callback: Is Check Number Available
     */
    public function _is_check_no($check_no)
    {
        if (trim($check_no) === '') {
            return true;
        }

        $where = [
            'doc_no' => $check_no,
            'bank_id' => $this->input->post('bank'),
            'status' => 'available',
            'is_deleted' => 0
        ];

        return !empty($this->enrolled_check->select('', $where, 1)) or !$this->_error = 'server_error';
    }

    /**
     * Callback: Check if Purchase Belongs to Check
     */
    public function _exists_in_check($check_purchase_id, $check_id)
    {
        if (trim($check_purchase_id) === '' || !is_numeric($check_purchase_id)) {
            return true;
        }

        $where = [
            'id' => $check_purchase_id,
            'check_id' => $check_id
        ];
        $check_purchase = $this->check_purchase->select('', $where, 1);

        return !empty($check_purchase);
    }

    /**
     * Callback: Check if Purchase is Approved
     */
    public function _is_approved_purchase($purchase_id)
    {
        if (trim($purchase_id) === '') {
            return true;
        }

        $where = <<<EOT
id = $purchase_id
AND status IN("approved", "printed")
AND is_deleted = 0
EOT;
        $purchase = $this->purchase->select('', $where, 1);

        return !empty($purchase);
    }
    
    
        /**
     * Callback: Check if Check No is available
     */
    public function _check_no_taken()
    {
        
        $where = [
            'bank_id' => $this->input->post('bank'),
            'check_no' => $this->input->post('check_no'),
            'is_deleted' => 0
        ];
        $check_slip = $this->check_slip->select('', $where, 1);
        
        if(!empty($check_slip) && $check_slip->id === $this->input->post('check_id')){
            return true;
        }

        return empty($check_slip);
    }

    public function issued_checks()
    {
        $this->title = 'List of Issued Checks';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/checks/manager'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $banks = $this->bank->select('', $where);
        $data = [
            'banks' => $banks
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $data = [
            'suppliers' => $suppliers
        ];
        $this->build_content('default', 'checks/issued_checks', $data);
    }

}
