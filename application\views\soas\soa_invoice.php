<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$invoice->id;?>">
		<div class="receipt-header">
			<div class="clearfix">
				<strong class="pull pull-right">Invoice</strong>
			</div>

			<h4><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="30%"></h4>

			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><small>Name : </small> <?=strtoupper($customer->name);?></p>
					<p class="text-left"><small>Address : </small> <?=$customer->address;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><small>Doc No : </small> <?=$invoice->invoice_no;?></p>
					<p class="text-left"><small>Date : </small> <?=date('n/j/Y', strtotime($invoice->invoice_date));?></p>
					<p class="text-left"><small>Due Date : </small> <?=$invoice->due_date ? date('n/j/Y', strtotime($invoice->due_date)): 'Due upon receipt';?></p>
				</div>
			</div>
		</div>

		<div class="clearfix"></div>
		<!-- invoice->paid_invoice->payment -->

		<table class="receipt-body">
			<thead>
				<tr>
					<th width="17%">Qty</th>
					<th width="17%">Description</th>
					<th width="17%">Account Name</th>
					<th width="7%">RS/SO</th>
					<th width="7%">ALW</th>
					<th width="17%" class="text-right">Unit Price</th>
					<th width="17%" class="text-right">Price</th>
				</tr>
			</thead>

			<tbody>
			<?php foreach ($invoice_items as $invoice_item): ?>
				<tr class="item">
					<td><?=floatval($invoice_item->qty) . ' ' . $invoice_item->unit?></td>
					<td><?=$invoice_item->item;?></td>
					<td><?=$invoice_item->account_type;?></td>
					<td><?=$invoice_item->rs;?></td>
					<td><?=$invoice_item->alw;?></td>
					<td class="text-right"><?=number_format($invoice_item->price, 2);?></td>
					<td class="text-right"><?=number_format($invoice_item->qty * $invoice_item->price, 2);?></td>
				</tr>
			<?php endforeach;?>
			</tbody>

			<tfoot>
				<tr class="item">
					<td colspan="3">
					<?php if (trim($invoice->remarks) !== ''): ?>
						<div class="row">
							<div class="col-md-12">
								<p class="text-justify"><strong>Remarks: </strong><?=nl2br($invoice->remarks);?></p>
							</div>
						</div>
					<?php endif;?>
					</td>

					<td colspan="3">
						<table width="100%">
							<tr>
								<td width="50%" class="text-right"><strong>Sub Total</strong></td>
								<td width="50%" class="text-right"><?=number_format($invoice->subtotal, 2);?></td>
							</tr>

						<?php if ($invoice->discount > 0): ?>
							<tr>
								<td class="text-right"><strong>(Discount)</strong></td>
								<td class="text-right">-<?=number_format($invoice->discount, 2);?></td>
							</tr>
						<?php endif;?>
							<tr>
								<td class="text-right"><strong>Grand Total</strong></td>
								<td class="text-right"><?=number_format($invoice->total, 2);?></td>
							</tr>
						</table>
					</td>
				</tr>
			</tfoot>
		</table>

		<div class="row">
			<div class="col-sm-3 text-center" style="margin-top: 78px;">
				<?=$invoice->issuer;?>
				<hr class="signature-line" style="margin: 5px">
				<p>Issued by</p>
			</div>

			<div class="col-sm-3 offset-sm-6 text-center" style="margin-top: 78px">
				<?=$invoice->approver;?>
				<hr class="signature-line" style="margin: 5px">
				<!-- <p>Signature above printed name</p> -->
				Approved by
			</div>
		</div>
	</div>

	<!-- <div class="clearfix text-center">
		<a class="btn btn-success" href="<?=site_url('invoices/edit/' . $invoice->id);?>">
			<i class="fa fa-pencil"></i> Edit
		</a>
		
		<?php if ($invoice->is_reviewed == 0): ?>
			<button class="btn btn-warning action-review" data-invoiceid="<?=$invoice->id;?>">
				<i class="fa fa-pencil"></i> Confirm Review
			</button>
		<?php endif;?>

		<?php if($invoice->status === 'approved'):?>
		<a class="btn btn-primary" href="<?=site_url('invoices/make_pdf/' . $invoice->id);?>">
			<i class="fa fa-save"></i> Export as PDF
		</a>

		<button type="button" class="btn btn-primary action-print">
			<i class="fa fa-print"></i> Print
		</button>
		<?php else:?>
		<button type="button" class="btn btn-primary action-approve" data-id="<?=$invoice->id?>">
			<i class="fa fa-"></i> Approve to Print
		</button>
		<?php endif;?>

		<a class="btn btn-light" href="<?=site_url('invoices');?>">Close</a>
	</div> -->
</div>

<div class="col-md-12">
	<!-- <div class="payment_receipt" data-tag="<?=$payment->id;?>"> -->
	<div class="payment_receipt">
		<div class="receipt-header">
			<div class="clearfix">
				<strong class="pull pull-right">Payment</strong>
			</div>
		</div>

		<table class="receipt-body">
			<thead>
				<tr>
					<th width="20%">Payment Date</th>
					<th width="20%">Payment type</th>
					<th width="15%">Amount</th>
					<th width="15%">Added by</th>
					<th width="15%">AR by</th>
					<th width="15%">PR by</th>
				</tr>
			</thead>

			<tbody>
				<?php foreach ($payments as $payment): ?>
					<tr class="item">
						<td><?=date('F j, Y', strtotime($payment['payment_date']));?></td>
						<td><?=strtoupper($payment['type']);?></td>
						<td><?=number_format($payment['total_paid'], 2);?></td>
						<td><?= $payment['receiver'] . ' <br> ' . date('M d, Y', strtotime($payment['added_on'])) . ' <br> ' . date('h:i A', strtotime($payment['added_on']));?> </td>
						<td><?= $payment['ard_name'] ? $payment['ard_name'] . ' <br> ' . date('M d, Y', strtotime($payment['ar_on'])) . ' <br> ' . date('h:i A', strtotime($payment['ar_on'])) : '';?> </td>
						<td><?= $payment['prd_name'] ? $payment['prd_name'] . ' <br> ' . date('M d, Y', strtotime($payment['pr_on'])) . ' <br> ' . date('h:i A', strtotime($payment['pr_on'])) : '';?> </td>
					</tr>
				<?php endforeach;?>
			</tbody>
		</table>
	</div>
</div>

<style>
	.payment_receipt {
		background: #fff;
		border: 1px solid #d8e2e7;
		-webkit-border-radius: 5px;
		-moz-border-radius: 5px;
		-ms-border-radius: 5px;
		border-radius: 5px;
		padding: 20px;
		position: relative;
		min-height: 150px;
		margin: 0 auto 20px 0;
	}
</style>