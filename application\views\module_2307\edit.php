
<div class="sect">
    <div class="col-md-12 form-group">
        <form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
            <h1 class="crud-title">
                <?= $title; ?>
            </h1>

            <fieldset>
                <h5 class="crud-subtitle"><span>Form 2307 Info</span></h5>

                <div class="row">
                    <div class="col-md-4 form-group">
                        <label for="supplier" class="control-label">Supplier</label>
                        <select class="form-control dropsearch" name="supplier" data-placeholder="">
                            <option></option>
                            <?php foreach ($suppliers as $supplier): ?>
                                <option value="<?= $supplier->id; ?>" <?= set_select('supplier', $supplier->id, $supplier->id === $module_2307->suppliers_id); ?>>
                                    <?= ucfirst($supplier->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="red-text flash-message">
                            <?= form_error('supplier'); ?>
                        </div>
                    </div>

                    <div class="col-md-4 form-group">
                        <label for="period_from" class="control-label">For the Period From (MM/DD/YYYY)</label>
                        <input type="text" name="period_from" class="form-control datepicker" value="<?= set_value('period_from', date('m/d/Y', strtotime($module_2307->period_from))); ?>">
                        <div class="red-text flash-message">
                            <?= form_error('period_from'); ?>
                        </div>
                    </div>

                    <div class="col-md-4 form-group">
                        <label for="period_to" class="control-label">For the Period To (MM/DD/YYYY)</label>
                        <input type="text" name="period_to" class="form-control datepicker" value="<?= set_value('period_to', date('m/d/Y', strtotime($module_2307->period_to))); ?>">
                        <div class="red-text flash-message">
                            <?= form_error('period_to'); ?>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 form-group">
                        <label for="tin" class="control-label">Supplier's Tin</label>
                        <input type="text" name="tin" class="form-control" value="<?= set_value('tin', $module_2307->supplier_tin); ?>" readonly>
                        <div class="red-text flash-message">
                            <?= form_error('tin'); ?>
                        </div>
                    </div>

                    <div class="col-md-4 form-group">
                        <label for="address" class="control-label">Supplier's Registered Address</label>
                        <input type="text" name="address" class="form-control" value="<?= set_value('address', $module_2307->supplier_address); ?>" readonly>
                        <div class="red-text flash-message">
                            <?= form_error('address'); ?>
                        </div>
                    </div>

                    <div class="col-md-4 form-group">
                        <label for="zip_code" class="control-label">Zip Code</label>
                        <input type="text" name="zip_code" class="form-control" value="<?= set_value('zip_code', $module_2307->supplier_zip_code); ?>" readonly>
                        <div class="red-text flash-message">
                            <?= form_error('zip_code'); ?>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 form-group">
                        <label for="attachments" class="control-label">Upload Attachments</label>
                        <input type="file" name="attachments[]" class="form-control-file" style="padding: 10px;" accept=".pdf, image/*" multiple>
                        <div class="red-text flash-message">
                            <?= form_error('attachments'); ?>
                        </div>
                    </div>
                </div>

                    <div class="col-md-12 form-group">
                        <table class="table table-bordered attachment-table" width="100%">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 80%;">Attachment</th>
                                    <th class="text-center" style="width: 20%;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($module_2307_attachments as $attachment): ?>
                                    <tr class="attachment">
                                        <td>
                                            <li>
                                                <a href="<?= base_url('assets/module_2307/' . $module_2307->id . '/' . $attachment->file_name); ?>" target="_blank">
                                                    <?= $attachment->file_name; ?>
                                                </a>
                                            </li>
                                        </td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-danger action-delete-attachment" data-id="<?= $attachment->id; ?>" data-filename="<?= $attachment->file_name; ?>">Delete</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                <h5 class="crud-subtitle"><span>Details of Monthly Income Payments and Tax Withheld for the Quarter</span></h5>

                <div class="row">
                    <div class="col-md-12 form-group">
                        <table class="table table-bordered item-table" width="100%">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 25%;">Income Payments Subject to Expanded Withholding Tax</th>
                                    <th class="text-center" style="width: 10%;">ATC</th>
                                    <th class="text-center" style="width: 20%;">Total Amount of Income Payments</th>
                                    <th class="text-center" style="width: 15%;">Tax Withheld For the Quarter</th>
                                    <th class="text-center" style="width: 10%;">Action</th>
                                </tr>
                            </thead>

                            <tbody>

                            <?php if (!empty($_2307_detail_ids)): ?>
                                <?php foreach ($_2307_detail_ids as $i => $_2307_detail_id): ?>
                                    <tr class="_2307_detail">
                                        <td>
                                            <?php $fn_2307_detail = '_2307_detail_' . $_2307_detail_id; ?>
                                            <input type="hidden" name="<?= $fn_2307_detail; ?>" value="<?= set_value($fn_2307_detail, isset($_2307_detail_id) ? $_2307_detail_id : ''); ?>">

                                            <?php $fn_income_payment_subject = 'income_payment_subject_' . $_2307_detail_id; ?>
                                            <select name="<?= $fn_income_payment_subject; ?>" class="form-control" data-placeholder="">
                                                <option value=""></option>
                                                <option value="Rentals" <?= set_select($fn_income_payment_subject, 'Rentals', isset($module_2307_details[$i]) && isset($module_2307_details[$i]->income_payment_subject) && $module_2307_details[$i]->income_payment_subject === 'Rentals'); ?>>Rentals</option>
                                                <option value="Goods" <?= set_select($fn_income_payment_subject, 'Goods', isset($module_2307_details[$i]) && isset($module_2307_details[$i]->income_payment_subject) && $module_2307_details[$i]->income_payment_subject === 'Goods'); ?>>Goods</option>
                                                <option value="Services" <?= set_select($fn_income_payment_subject, 'Services', isset($module_2307_details[$i]) && isset($module_2307_details[$i]->income_payment_subject) && $module_2307_details[$i]->income_payment_subject === 'Services'); ?>>Services</option>
                                            </select>

                                            <div class="red-text flash-message">
                                                <?= form_error($fn_income_payment_subject); ?>
                                            </div>
                                        </td>

                                        <td>
                                            <?php $fn_atc = 'atc_' . $_2307_detail_id; ?>
                                            <input type="text" name="<?= $fn_atc; ?>" class="form-control" 
                                                value="<?= set_value($fn_atc, isset($module_2307_details[$i]) && isset($module_2307_details[$i]->atc) ? $module_2307_details[$i]->atc : ''); ?>" readonly>

                                            <div class="red-text flash-message">
                                                <?= form_error($fn_atc); ?>
                                            </div>
                                        </td>

                                        <td>
                                            <?php $fn_total_amount_of_income = 'total_amount_of_income_' . $_2307_detail_id; ?>
                                            <input type="number" name="<?= $fn_total_amount_of_income; ?>" class="form-control" value="<?= set_value($fn_total_amount_of_income, isset($module_2307_details[$i]) && isset($module_2307_details[$i]->total_income_payment) ? sprintf("%.2f", $module_2307_details[$i]->total_income_payment) : ''); ?>" min="0" step="0.01">

                                            <div class="red-text flash-message">
                                                <?= form_error($fn_total_amount_of_income); ?>
                                            </div>
                                        </td>

                                        <td>
                                            <?php $fn_tax_withheld = 'tax_withheld_' . $_2307_detail_id; ?>
                                            <input type="number" name="<?= $fn_tax_withheld; ?>" class="form-control" value="<?= set_value($fn_tax_withheld, isset($module_2307_details[$i]) && isset($module_2307_details[$i]->tax_withheld) ? sprintf("%.2f", $module_2307_details[$i]->tax_withheld) : ''); ?>" readonly>
                                            
                                            <div class="red-text flash-message">
                                                <?= form_error($fn_tax_withheld); ?>    
                                            </div>
                                        </td>

                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr class="no-item">
                                    <td class="text-center" colspan="5">No items added yet.</td>
                                </tr>
                            <?php endif; ?>


                            </tbody>

                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-right border-0"><strong>Overall Total Tax Withheld:</strong></td>
                                    <td class="text-right border-0">
                                        <strong id="overall-total">0.00</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 form-group">
                        <button type="button" id="btnAddItem" class="btn btn-sm btn-dark action-add-item">Add Item</button>
                    </div>
                </div>
            </fieldset>

            <div class="error">
                <?= $form_error; ?>
            </div>

            <hr>

            <div class="clearfix text-center">
                <button type="submit" name="submit" class="btn btn-primary">Save</button>
                <a href="<?= site_url('module_2307/manager') ?>" class="btn btn-light">Close</a>
            </div>
        </form>
    </div>
</div>
