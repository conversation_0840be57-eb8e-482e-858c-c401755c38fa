<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Carcasses extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Carcasses
     */
    public function index()
    {
        // Get request parameters
        $draw = intval($this->input->get_post('draw'));
        $start = intval($this->input->get_post('start'));
        $length = intval($this->input->get_post('length'));
        $search_value = $this->input->get_post('search')['value'];

        // Get all carcass records
        $this->db->select('
            carcass.id as carcass_id,
            carcass.sku,
            carcass.size,
            carcass.range,
            carcass.no,
            carcass.pcs,
            carcass.kgs,
            carcass.added_on,
            carcass.added_by
        ');
        $this->db->from('carcass');
        $this->db->where('carcass.is_deleted', 0);
        $this->db->order_by('carcass.added_on DESC');

        // Apply search filter
        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('carcass.sku', $search_value);
            $this->db->or_like('carcass.size', $search_value);
            $this->db->or_like('carcass.range', $search_value);
            $this->db->group_end();
        }

        // Get total count before pagination
        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results();

        // Apply pagination - if length is -1, return all records
        if ($length > 0) {
            $this->db->limit($length, $start);
        }

        // Execute query
        $query = $this->db->get();
        $data = [];

        if ($query->num_rows() > 0) {
            $grouped_data = [];

            // Group by SKU
            foreach ($query->result() as $row) {
                $sku_key = $row->sku;

                if (!isset($grouped_data[$sku_key])) {
                    $grouped_data[$sku_key] = [];
                }

                $grouped_data[$sku_key][] = $row;
            }

            // Process each SKU group
            foreach ($grouped_data as $sku_key => $records) {
                $first_record = $records[0];

                // First row: Show SKU and first record's data
                $data[] = [
                    'DT_RowId' => 'carcass_' . $first_record->carcass_id,
                    'DT_RowClass' => 'carcass',
                    'sku' => $first_record->sku,
                    'size' => $first_record->size,
                    'range' => $first_record->range,
                    'no' => $first_record->no,
                    'pcs' => $first_record->pcs,
                    'kgs' => $first_record->kgs,
                    'actions' => $this->_generate_actions($first_record->carcass_id)
                ];

                // Additional rows: Show only Size, Range, NO, PCS, KGS (empty SKU)
                for ($i = 1; $i < count($records); $i++) {
                    $record = $records[$i];
                    $data[] = [
                        'DT_RowId' => 'carcass_' . $record->carcass_id,
                        'DT_RowClass' => 'carcass',
                        'sku' => '', // Empty SKU for additional rows
                        'size' => $record->size,
                        'range' => $record->range,
                        'no' => $record->no,
                        'pcs' => $record->pcs,
                        'kgs' => $record->kgs,
                        'actions' => $this->_generate_actions($record->carcass_id)
                    ];
                }
            }
        }

        // Return DataTables response
        $response = [
            'draw' => $draw,
            'recordsTotal' => $this->_get_total_carcasses(),
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        exit(json_encode($response));
    }

    /**
     * Generate actions for a carcass record
     */
    protected function _generate_actions($carcass_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])) {
            $res .= anchor('Carcasses/edit/' . $carcass_id, 'Edit', 'title="Edit" class="dropdown-item text-left"');
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $carcass_id . '">Delete</button>';
        }

        $res .= '</div></div>';
        return $res;
    }

    /**
     * Get total carcass records
     */
    protected function _get_total_carcasses()
    {
        $this->db->from('carcass');
        $this->db->where('carcass.is_deleted', 0);
        return $this->db->count_all_results();
    }
}
