<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Order_confirmations extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('order_confirmations/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review order_confirmation Order for Approval
     */
    public function receive($order_confirmation_id)
    {
        $order_confirmation = $this->order_confirmation->get_by_id($order_confirmation_id) or show_404();

        $this->title = 'Receive order_confirmation No. ' . $order_confirmation->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_confirmations/receive'
        ];

        $order_confirmation_items = $this->order_confirmation_item->get_by_order_confirmation($order_confirmation->id);

        $data = [
            'order_confirmation'          => $order_confirmation,
            'order_confirmation_items'    => $order_confirmation_items
        ];
        $this->build_content('default', 'order_confirmations/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add order_confirmation
     */
    public function add($order_slip_id = null)
    {
        $order_slip = $this->order_slip->get_by_id($order_slip_id) or show_404();
        $order_slip_id = $order_slip->id;
        $order_slip_items = $this->order_slip_item->select('', ['order_slip_id' => $order_slip->id, 'is_deleted' => 0]);
        foreach($order_slip_items AS $index => $order_slip_item) {
            $where = [
                'id' => $order_slip_items[$index]->item_id,
                'is_deleted' => 0
            ];
            $item = $this->live_sell_item->select('', $where, 1);

            $order_slip_items[$index]->item_name = $item->name;
        }

        $order_confirmation_item_ids = $this->_get_order_confirmation_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/order_confirmations',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('order_confirmations/add'), $this->_get_order_confirmation_item_rules('', $order_confirmation_item_ids));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($order_confirmation_id = $this->_attempt_add($order_slip_id))) {
            $new_path = FCPATH . 'assets/order_confirmations/' . $order_confirmation_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('order_confirmations/manager'));
        } else {
            $this->title = 'Add Order Confirmation';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/order_confirmations/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_order_confirmation_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->live_sell_supplier->select('', $where, '', 'name');

            $data = [
                'order_slip'                    => $order_slip,
                'order_slip_items'              => $order_slip_items,
                'form_error'                    => $form_error,
                'order_confirmation_item_ids'   => $order_confirmation_item_ids,
                'customers'                     => $customers,
                'suppliers'                     => $suppliers,
                'retain_items'                  => $retain_items
            ];

            $this->build_content('default', 'order_confirmations/add', $data);
        }
    }

    /**
     * Edit order_confirmation
     */
    public function edit($order_confirmation_id)
    {
        $order_confirmation = $this->order_confirmation->get_by_id($order_confirmation_id) or show_404();

        $order_confirmation_items = $this->order_confirmation_item->get_by_order_confirmation($order_confirmation->id);

        $retain_items = $this->_get_order_confirmation_item_data();
        $rules = $this->config->item('order_confirmations/edit');

        if (!$order_confirmation_item_ids = $this->_get_order_confirmation_item_ids()) {
            foreach($order_confirmation_items AS $order_confirmation_item) {
                $item = $this->live_sell_item->select('', ['id' => $order_confirmation_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $order_confirmation_item->id,
                    'item_id' => $order_confirmation_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $order_confirmation_item->qty,
                    'supplier_id' => $order_confirmation_item->supplier_id,
                    'est_alw' => $order_confirmation_item->est_alw,
                    'est_due_amount' => $order_confirmation_item->est_due_amount,
                ];
                $order_confirmation_item_ids[] = $order_confirmation_item->id;
            }

            $existing_ids = array_map(function ($order_confirmation_item) {
                return $order_confirmation_item->id;
            }, $order_confirmation_items);

            $item_rules = $this->_get_order_confirmation_item_rules($order_confirmation->id, $existing_ids);
        } else {
            $item_rules = $this->_get_order_confirmation_item_rules($order_confirmation->id, $order_confirmation_item_ids);
        }

        $rules = array_merge($this->config->item('order_confirmations/edit'), $item_rules);

        if ($this->_validate_form($rules) && $this->_attempt_edit($order_confirmation)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('order_confirmations/preview/' . $order_confirmation->id));
            } else {
                redirect(site_url('order_confirmations/manager'));
            }
        } else {
            $this->title = 'Edit Order Confirmation';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/order_confirmations/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $where = [
                'is_deleted' => 0
            ];
            $suppliers = $this->live_sell_supplier->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'order_confirmation' => $order_confirmation,
                'order_confirmation_items' => $order_confirmation_items,
                'order_confirmation_item_ids' => $order_confirmation_item_ids,
                'retain_items' => $retain_items,
                'suppliers' => $suppliers
            ];

            $this->build_content('default', 'order_confirmations/edit', $data);
        }
    }

    /**
     * Approve order_confirmation
     */
    public function receive_order_confirmation($order_confirmation_id)
    {
        $where = [
            'id' => $order_confirmation_id,
            'is_deleted' => 0
        ];
        if (!$order_confirmation = $this->order_confirmation->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Order Confirmation is not found.'
            ];
        } elseif (!$this->_attempt_receive($order_confirmation)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Order Confirmation is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($order_confirmation)
    {
        $where = [
            'id' => $order_confirmation->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->order_confirmation->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete order_confirmation
     */
    public function delete($order_confirmation_id)
    {
        $where = [
            'id' => $order_confirmation_id,
            'is_deleted' => 0
        ];
        if (!$order_confirmation = $this->order_confirmation->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Order Confirmation is not found.'
            ];
        } elseif (!$this->_attempt_delete($order_confirmation)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Order Confirmation is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * order_confirmations
     */
    public function manager()
    {
        $this->title = "Confirmed Orders";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_confirmations/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'order_confirmations/manager', $data);
    }

    /**
     * order_confirmation Print Preview
     */
    public function preview($order_confirmation_id)
    {
        $order_confirmation = $this->order_confirmation->get_by_id($order_confirmation_id) or show_404();
        $order_confirmation_items = $this->order_confirmation_item->get_by_order_confirmation($order_confirmation->id);

        if (!$order_confirmation_item_ids = $this->_get_order_confirmation_item_ids()) {
            foreach($order_confirmation_items AS $order_confirmation_item) {
                $item = $this->live_sell_item->select('', ['id' => $order_confirmation_item->item_id, 'is_deleted' => 0], 1);
                $supplier = $this->live_sell_supplier->select('', ['id' => $order_confirmation_item->supplier_id, 'is_deleted' => 0], 1);

                $retain_items[] = [
                    'id' => $order_confirmation_item->id,
                    'item_id' => $order_confirmation_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $order_confirmation_item->qty,
                    'supplier_id' => $order_confirmation_item->supplier_id,
                    'supplier_name' => $supplier->name,
                    'est_alw' => $order_confirmation_item->est_alw,
                    'est_due_amount' => $order_confirmation_item->est_due_amount,
                ];
                $order_confirmation_item_ids[] = $order_confirmation_item->id;
            }

            $existing_ids = array_map(function ($order_confirmation_item) {
                return $order_confirmation_item->id;
            }, $order_confirmation_items);

            $item_rules = $this->_get_order_confirmation_item_rules($order_confirmation->id, $existing_ids);
        } else {
            $item_rules = $this->_get_order_confirmation_item_rules($order_confirmation->id, $order_confirmation_item_ids);
        }

        $where = [
            'id' => $order_confirmation->added_by
        ];

        $issuer = $this->user->select('', $where, 1);

        $this->title = 'Print Preview: Order Confirmation No. ' . $order_confirmation->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_confirmations/preview',
        ];

        $where = [
            'id' => $order_confirmation->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);

        

        // var_dump($order_confirmation_items);
        // die();

        $data = [
            'order_confirmation' => $order_confirmation,
            'customer' => $customer,
            'order_confirmation_items' => $order_confirmation_items,
            'order_confirmation_item_ids' => $order_confirmation_item_ids,
            'issuer' => $issuer,
            'retain_items' => $retain_items,
        ];
        $this->build_content('default', 'order_confirmations/preview', $data);
    }

    /**
     * Get Info for order_confirmation
     */
    public function info($order_confirmation_id = null)
    {
        $data = [
            'order_confirmation' => $this->order_confirmation->get_by_id($order_confirmation_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid order_confirmations
     */
    public function partially_paid()
    {
        $data = [
            'order_confirmations' => $this->order_confirmation->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('user');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('order_confirmation');
        $this->load->model('order_confirmation_item');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');
        $this->load->model('live_sell_supplier');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_order_confirmation_item_ids()
    {
        $order_confirmation_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'order_confirmation_item_') === 0) {
                $order_confirmation_item_ids[] = $value;
            }
        }

        return $order_confirmation_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_order_confirmation_item_rules($order_confirmation_id, $order_confirmation_item_ids)
    {
        $rules = [];

        foreach ($order_confirmation_item_ids as $order_confirmation_item_id) {
            $rules[] = [
                'field' => 'item_' . $order_confirmation_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'qty_' . $order_confirmation_item_id,
                'label' => 'confirmed quantity',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'supplier_id_' . $order_confirmation_item_id,
                'label' => 'supplier',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'est_alw_' . $order_confirmation_item_id,
                'label' => 'est_alw',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'est_due_amount_' . $order_confirmation_item_id,
                'label' => 'Est_due_amount',
                'rules' => 'required|trim|numeric|greater_than[-1]'
            ];

            $rules[] = [
                'field' => 'unit_' . $order_confirmation_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]'
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id)
    {
        $this->db->trans_begin();

        if (!$order_confirmation_items = $this->_get_order_confirmation_item_data()) {
            $this->_error = 'No Order Confirmation item added';
            return false;
        }

        if (!$order_confirmation_id = $this->_generate_order_confirmation($order_slip_id) or !$this->_insert_order_confirmation_items($order_confirmation_id, $order_confirmation_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $order_confirmation_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_order_confirmation_item_data()
    {
        $data = [];
        $order_confirmation_item_ids = $this->_get_order_confirmation_item_ids();

        foreach ($order_confirmation_item_ids as $order_confirmation_item_id) {
            $item_id = $this->input->post('item_' . $order_confirmation_item_id);
            $unit = $this->input->post('unit_' . $order_confirmation_item_id);
            $qty = $this->input->post('qty_' . $order_confirmation_item_id);
            $est_alw = $this->input->post('est_alw_' . $order_confirmation_item_id);
            $supplier_id = $this->input->post('supplier_id_' . $order_confirmation_item_id);
            $est_due_amount = $this->input->post('est_due_amount_' . $order_confirmation_item_id);

            $added_by = $_SESSION['user']->id;

            $data[] = [
                'id' => is_numeric($order_confirmation_item_id) ? $order_confirmation_item_id : null,
                'order_slip_item_id' => $order_confirmation_item_id,
                'item_id' => $item_id,
                'unit' => $unit,
                'qty' => $qty,
                'supplier_id' => $supplier_id,
                'est_alw' => $est_alw,
                'est_due_amount' => $est_due_amount,
                'added_by' => $added_by,
                'added_on' => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Generate order_confirmation
     */
    protected function _generate_order_confirmation($order_slip_id)
    {
        $customer_id = $this->input->post('customer');

        $order_confirmation_date = DateTime::createFromFormat('m/d/Y', $this->input->post('order_confirmation_date'));
        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }
        $values = [
            'order_slip_id' => $order_slip_id,
            'customer_id' => $customer_id,
            'order_confirmation_date' => $order_confirmation_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->order_confirmation->insert($values);
    }

    /**
     * Generate order_confirmation Items
     */
    // protected function _generate_order_confirmation_items($order_confirmation_id, $order_confirmation_items)
    // {

    //     foreach ($order_confirmation_items as $order_confirmation_item) {
    //         $where = [
    //             'id' => $order_confirmation_item['item_id'],
    //             'is_deleted' => 0
    //         ];
    //         $item = $this->live_sell_item->select('', $where, 1);
    //         // if(($item->current_stock - (int)$order_confirmation_item['qty']) < 0) {
    //         //     $this->_error = "Can't order_confirmation item, it will lead to negative inventory.";
    //         //     return false;
    //         // }

    //         $where = [
    //             'id' => $order_confirmation_item['item_id']
    //         ];
    //         $values = [
    //             'updated_by' => $order_confirmation_item['added_by']
    //         ];

    //         if (!$this->live_sell_item->update($where, $values) ||
    //             !$this->order_confirmation_item->generate(
    //                 $order_confirmation_item['id'],
    //                 $order_confirmation_id,
    //                 $order_confirmation_item['item_id'],
    //                 $order_confirmation_item['order_slip_item_id'],
    //                 $order_confirmation_item['qty'],
    //                 $order_confirmation_item['supplier_id'],
    //                 $order_confirmation_item['est_alw'],
    //                 $order_confirmation_item['est_due_amount'],
    //                 $order_confirmation_item['unit'],
    //                 $order_confirmation_item['added_by']
    //             )
    //         ) {
    //             // if($this->order_confirmation_item)
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    /**
     * Insert order_confirmation Items
     */
    protected function _insert_order_confirmation_items($order_confirmation_id, $order_confirmation_items)
    {

        foreach ($order_confirmation_items as $order_confirmation_item) {
            $values = [
                'order_confirmation_id' => $order_confirmation_id,
                'order_slip_item_id' => $order_confirmation_item['order_slip_item_id'],
                'item_id' => $order_confirmation_item['item_id'],
                'qty' => $order_confirmation_item['qty'],
                'supplier_id' => $order_confirmation_item['supplier_id'],
                'est_alw' => $order_confirmation_item['est_alw'],
                'est_due_amount' => $order_confirmation_item['est_due_amount'],
                'unit' => $order_confirmation_item['unit'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];

            if (!$this->order_confirmation_item->insert($values)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($order_confirmation)
    {
        $this->db->trans_begin();

        $where = [
            'order_confirmation_id' => $order_confirmation->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->order_confirmation_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$order_confirmation_items = $this->_get_order_confirmation_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Order Confirmation Item added';
            return false;
        } elseif(!$this->_attempt_delete_items($order_confirmation->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No Order Confirmation Item added';
            return false;
        } elseif (!$this->_revise_order_confirmation($order_confirmation->id, $order_confirmation_items) || !$this->_insert_order_confirmation_items($order_confirmation->id, $order_confirmation_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise order_confirmation
     */
    protected function _revise_order_confirmation($order_confirmation_id, $order_confirmation_items)
    {
        $order_confirmation_date = DateTime::createFromFormat('m/d/Y', $this->input->post('order_confirmation_date'));
        $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($order_confirmation_items, function ($current_total, $order_confirmation_item) {
            return $current_total + $order_confirmation_item['est_due_amount'];
        });
        $discount = $this->input->post('discount') ?: null;

        $where = [
            'id' => $order_confirmation_id
        ];

        $customer_id = $this->input->post('customer');

        $values = [
            'customer_id' => $customer_id,
            'order_confirmation_date' => $order_confirmation_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->order_confirmation->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($order_confirmation)
    {
        $where = [
            'id' => $order_confirmation->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->order_confirmation->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($order_confirmation)
    {
        $where = [
            'id' => $order_confirmation->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->order_confirmation->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($order_confirmation_id)
    {
        $where = [
            'order_confirmation_id' => $order_confirmation_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->order_confirmation_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if order_confirmation Item Belongs to order_confirmation
     */
    public function _is_order_confirmation_item($order_confirmation_item_id, $order_confirmation_id)
    {
        if (!is_numeric($order_confirmation_item_id)) {
            return true;
        }

        $where = [
            'id' => $order_confirmation_item_id,
            'order_confirmation_id' => $order_confirmation_id
        ];

        return !empty($this->order_confirmation_item->select('', $where, 1));
    }

    /**
     * Callback: Check if order_confirmation Term is Either Cash or Check
     */
    public function _is_order_confirmation_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
