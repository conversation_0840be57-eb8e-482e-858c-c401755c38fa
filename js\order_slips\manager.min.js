!function(e){"use strict";function t(t){var r=e(t),a=(r.find("thead th"),e(".tab-link.active").attr("data-status")),n=r.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{searchable:!0,targets:[2,3]},{className:"text-right",targets:[4,5]},{className:"text-left",targets:[0,1,2,3,6]},{className:"exclude-print",targets:[7]}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package")+"?status="+a,footerCallback:function(t,a,n,i,o){var s=this.api(),l=r.attr("data-col-sum");if(void 0!==l&&l>-1){var u=function(e){return"string"==typeof e?1*e.replace(/[\$,]/g,""):"number"==typeof e?e:0},c=s.column(l).data().reduce(function(e,t){return u(e)+u(t)},0);e(s.column(l).footer()).html(e.fn.addNumericCommas(parseFloat(c).toFixed(2)))}}});return n}function r(t,r){e.when(a(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t){return e.ajax({url:BASE_URI+"order_slips/receive_order_slip/"+t,type:"POST",dataType:"json"})}function n(t,r){e.when(i(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function i(t){return e.ajax({url:BASE_URI+"order_slips/delete/"+t,type:"POST",dataType:"json"})}e(function(){var a=t(".tableA");e(".tableA").on("click",".action-receive",function(t){var n=e(t.currentTarget).closest("tr.order_slip").find("td"),i=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the order slip "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,a)}).setHeader("<em>receive order_slip</em>")}).on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.order_slip").find("td"),i=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete order slip"+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,a)}).setHeader("<em>Delete Order Slip</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var r=BASE_URI+"fetch/order_slips?status="+t.currentTarget.getAttribute("data-status"),n=BASE_URI+"order_slips/manager?status="+t.currentTarget.getAttribute("data-status");console.log(r),window.history.pushState(null,null,n),a.ajax.url(r).load()})})}(jQuery);