+(function ($) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);

		var dataTable = $table.DataTable({
			dom: "Brtip",
			lengthChange: true,
			paging: false,
			filter: true,
			info: false,
			pagingType: "full_numbers",
			order: [[0, "asc"]],
			// columnDefs: [
			// 	{
			// 		className: "text-right",
			// 		targets: cols.length - 1,
			// 	},
			// ],
			buttons: [
				{
					extend: "print",
					footer: true,
					exportOptions: {
						stripHtml: false,
					},
				},
			],
			scrollX: true,
			autoWidth: false,
			paging: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + $table.attr("data-package"),
			footerCallback: function (row, data, start, end, display) {
				var api = this.api();
				var colSum = $table.attr("data-col-sum");

				if (typeof colSum !== "undefined" && colSum > -1) {
					// returns the numeric value of data
					var intVal = function (i) {
						return typeof i === "string"
							? i.replace(/[\$,]/g, "") * 1
							: typeof i === "number"
							? i
							: 0;
					};

					// total of all data in table
					var grandTotal = api
						.column(colSum)
						.data()
						.reduce(function (a, b) {
							return intVal(a) + intVal(b);
						}, 0);

					$(api.column(colSum).footer()).html(
						$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
					);
				}
			},
		});

		return dataTable;
	}

	$(function () {
		setTimeout(() => {
			$(".form-control").attr("autocomplete", "off");
		}, 500);
		var dataTable = createDataTable(".tableA");

		$("#tableFilter").submit(function (event) {
			event.preventDefault();

			var url =
				BASE_URI +
				"fetch/" +
				$(".tableA").attr("data-package") +
				"?" +
				$(event.currentTarget).serialize();

			dataTable.ajax.url(url).load();
		});
	});
})(jQuery);
