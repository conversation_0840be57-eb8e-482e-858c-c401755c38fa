<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cash_bonds extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch cash bonds
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'cash_bond';
        $primary_key = 'cash_bond.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_status(),
            $this->_get_doc_no(),
            $this->_get_customer(),
            $this->_get_cash_bond_date(),
            $this->_get_terms(),
            $this->_get_due_date(),
            // $this->_get_total(),
            $this->_get_issued_by(),
            // $this->_get_approved_by(),
            $this->_get_file(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'active';

        switch($status){
            case 'active':
                $where = <<<EOT
cash_bond.status = "active"
AND cash_bond.is_deleted = 0
EOT;
                break;

            case 'audited':
                $where = <<<EOT
cash_bond.status = "audited"
AND cash_bond.is_deleted = 0
EOT;
                break;

            case 'incomplete':
                $where = <<<EOT
cash_bond.status = "incomplete"
AND cash_bond.is_deleted = 0
EOT;
                break;

            case 'complete':
                $where = <<<EOT
cash_bond.status = "complete"
AND cash_bond.is_deleted = 0
EOT;
                break;

            case 'cancelled':
                $where = <<<EOT
cash_bond.status = "cancelled"
AND cash_bond.is_deleted = 0
EOT;
                break;

            case 'released':
                $where = <<<EOT
cash_bond.status = "released"
AND cash_bond.is_deleted = 0
EOT;
                break;
            default:
                break;
        }


        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'cash_bond.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'cash_bond.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'cash_bond';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'cash_bond.status',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get cash_bond document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'cash_bond.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                $res = '<a href="../cash_bonds/preview/' . $d . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                return $res;
            }
        ];
    }

    /**
     * Get cash_bond customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'
        ];
    }

    /**
     * Get cash_bond date
     */
    protected function _get_cash_bond_date()
    {
        return [
            'db' => 'DATE_FORMAT(cash_bond.cb_date, "%b %d, %Y")',
            'as' => 'cash_bond_date',
            'dt' => 2,
            'field' => 'cash_bond_date'
        ];
    }

    /**
     * Get terms
     */
    protected function _get_terms()
    {
        return [
            'db' => 'cash_bond.terms',
            'as' => 'terms',
            'dt' => 3,
            'field' => 'terms',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get due date
     */
    protected function _get_due_date()
    {
        return [
            'db' => 'DATE_FORMAT(cash_bond.due_date, "%b %d, %Y")',
            'as' => 'due_date',
            'dt' => 4,
            'field' => 'due_date',
            'formatter' => function ($d, $row) {
                if(!empty($d)){
                    return $d;
                } else {
                    return '';
                }
            }
        ];
    }

    // /**
    //  * Get total
    //  */
    // protected function _get_total()
    // {
    //     return [
    //         'db' => 'FORMAT(cash_bond.total, 2)',
    //         'as' => 'total',
    //         'dt' => 5,
    //         'field' => 'total'
    //     ];
    // }

    /**
     * Get issuing person
     */
    protected function _get_issued_by()
    {
        return [
            'db' => 'issuer.full_name',
            'as' => 'issued_by',
            'dt' => 5,
            'field' => 'issued_by'
        ];
    }

    // /**
    //  * Get issuing person
    //  */
    // protected function _get_approved_by()
    // {
    //     return [
    //         'db' => 'approver.full_name',
    //         'as' => 'approved_by',
    //         'dt' => 7,
    //         'field' => 'approved_by'
    //     ];
    // }

    /**
     * Get File
     */
    protected function _get_file()
    {
        $CI = &get_instance();
        return [
            'db' => 'cash_bond.id',
            'as' => 'file',
            'dt' => 6,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('cash_bond_attachment');
                $files = $CI->cash_bond_attachment->get_by_id($d);
                $res = '';

                foreach($files as $i => $file){
                    $res .= '<a href="'.base_url('assets/asylum/cash_bonds/'.$d.'/'.$file->file_name).'" target="_blank">Attachment '.++$i.'</a><br>';
                }

                return $res;

            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'cash_bond.id',
            'as' => 'actions',
            'dt' => 7,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('cash_bonds/preview/' . $d, 'Preview', 'title="Preview" class="dropdown-item text-left"');

                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                    switch($row['status']){
                        case 'active' :
                            // $res .= anchor('cash_bonds/audit/' . $d, 'Audit', 'title="Audit" class="dropdown-item text-left green-text"');
                            $res .= anchor('cash_bonds/release/' . $d, 'Release', 'title="Release Cash bond" class="dropdown-item text-left green-text"');
                            $res .= '<button class="action-cancel dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                            break;
                        case 'incomplete' :
                            // $res .= anchor('cash_bonds/audit/' . $d, 'View Audit', 'title="View Audit" class="dropdown-item text-left green-text"');
                            break;
                        default:
                            break;
                    }
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
