<?php
class Module_2307_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'form_2307_attachment';
        parent::__construct();
    }

    /**
     * Get Attachment by 2307 Id
     */
    public function get_by_2307_attachment_id($id){

        $sql = <<<EOT
SELECT form_2307_attachment.*
FROM form_2307_attachment
WHERE form_2307_attachment.is_deleted = 0
    AND form_2307_attachment.2307_id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */