<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Account_types_purchase extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // var_dump($_SESSION['user']); die();
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Purchase Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase_item';
        $primary_key = 'purchase_item.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_account_type(),
            $this->_get_amount()
        ];

        $joins = <<<EOT
purchase_item
LEFT JOIN `purchase` ON `purchase`.id = `purchase_item`.purchase_id

EOT;

        $where = <<<EOT
purchase_item.is_deleted = 0
AND purchase_item.status = 'approved'
AND purchase.audit_status = 'complete'
EOT;

        $from = $this->input->get('from', true);
        $to = $this->input->get('to', true);
        if (
            isset($from) and $from_date = date("Y-m-d", strtotime(urldecode($from))) and
            isset($to) and $to_date = date("Y-m-d", strtotime(urldecode($to)))
        ) {
            $where .= <<<EOT

AND purchase.purchase_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

        $group_by = "account_type";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        ); 
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase_item.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase_item.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase_item';
            }
        ];
    }

    /**
     * Get Account Type
     */
    protected function _get_account_type()
    {
        return [
            'db' => 'purchase_item.account_type',
            'as' => 'account_type',
            'dt' => 0,
            'field' => 'account_type',
            'formatter' => function ($d, $row) {
                if (empty($d)) {
                    return "No Account Name";
                } else {
                    $from = $this->input->get('from', true);
                    $to   = $this->input->get('to', true);
                    return anchor('purchases/invoice_expense_by_account?account_name=' . $d.'&from='.$from.'&to='.$to, $d, ' class="btn btn-link link-unstyled" style="display:block;" target="_blank"');
                }
            },
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'IFNULL(SUM(purchase_item.amount), 0)',
            'as' => 'total',
            'dt' => 1,
            'field' => 'total',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

}
