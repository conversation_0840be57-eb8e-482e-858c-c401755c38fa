+(function ($) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find("thead th");

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: "full_numbers",
			order: [[0, "asc"]],
			columnDefs: [
				{
					className: "dt-action",
					targets: cols.length - 1,
				},
			],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + $table.attr("data-package"),
		});

		return dataTable;
	}

	function deleteAndReload(module_2307_model_id, dataTable) {
		$.when(deleteRow(module_2307_model_id)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(module_2307_model_id) {
		return $.ajax({
			url: BASE_URI + "module_2307/delete/" + module_2307_model_id,
			type: "POST",
			dataType: "json",
		});
	}

	$(function () {
		var dataTable = createDataTable(".tableA");

		$(".tableA").on("click", ".action-delete", function (event) {
			var cols = $(event.currentTarget).closest("tr.module_2307").find("td");
			var name = typeof cols[2] !== "undefined" ? cols[0].innerHTML : "";

			alertify
				.confirm("Are you sure you want to delete " + name + "?", function () {
					var module_2307_model_id =
						event.currentTarget.getAttribute("data-id");
					deleteAndReload(module_2307_model_id, dataTable);
				})
				.setHeader("<em>Delete Form 2307</em>");
		});
	});
})(jQuery);
