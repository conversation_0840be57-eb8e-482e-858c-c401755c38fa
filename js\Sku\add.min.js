$(document).ready(function(){"use strict";function t(){var t=$('#skusTableBody .sku-row:first input[name="sku[]"]').val()||"",i=$(e(t));$("#skusTableBody").append(i)}function i(){var t=$('#gibletsTableBody .giblets-row:first input[name="giblets_sku[]"]').val()||"",i=$(n(t));$("#gibletsTableBody").append(i)}function e(t){return t=t||"",'<tr class="sku-row"><td class="text-center" style="vertical-align: middle;"><input type="text" class="form-control" name="sku[]" value="'+t+'"></td><td><input type="text" class="form-control" name="size[]" value=""></td><td><input type="text" class="form-control" name="range[]" value=""></td><td><input type="text" class="form-control" name="by[]" value=""></td><td class="text-center" style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm action-delete-item">Delete</button></td></tr>'}function n(t){return t=t||"",'<tr class="giblets-row"><td class="text-center" style="vertical-align: middle;"><input type="text" class="form-control" name="giblets_sku[]" value="'+t+'"></td><td><input type="text" class="form-control" name="giblets_by[]" value=""></td><td class="text-center" style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm action-delete-giblets">Delete</button></td></tr>'}$("#addSkuRow").on("click",function(){t()}),$("#addGibletsRow").on("click",function(){i()}),$(document).on("click",".action-delete-item",function(){var t=$("#skusTableBody .sku-row");t.length>1?$(this).closest(".sku-row").remove():alert("At least one SKU row is required.")}),$(document).on("click",".action-delete-giblets",function(){$(this).closest(".giblets-row").remove()}),$(document).on("input",'input[name="sku[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder");var t=$(this).closest(".sku-row"),i=$("#skusTableBody .sku-row:first");if(t.is(i)){var e=$(this).val();$('#skusTableBody .sku-row:not(:first) input[name="sku[]"]').val(e)}}),$(document).on("input",'input[name="size[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder")}),$(document).on("input",'input[name="range[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder")}),$(document).on("input",'input[name="by[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder")}),$(document).on("input",'input[name="giblets_sku[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder");var t=$(this).closest(".giblets-row"),i=$("#gibletsTableBody .giblets-row:first");if(t.is(i)){var e=$(this).val();$('#gibletsTableBody .giblets-row:not(:first) input[name="giblets_sku[]"]').val(e)}}),$(document).on("input",'input[name="giblets_by[]"]',function(){$(this).removeClass("is-invalid").removeAttr("placeholder")}),$(".customer-select").select2({placeholder:"Choose a customer...",allowClear:!0,width:"100%"}),$("#skuForm").on("submit",function(t){$(".form-control").removeClass("is-invalid"),$(".validation-error").remove(),$("#customer_id").removeClass("is-invalid");var i=!1,e=$("#customer_id").val();if(e||($("#customer_id").addClass("is-invalid"),i=!0),$(".sku-row").each(function(t){var e=$(this).find('input[name="sku[]"]').val().trim(),n=$(this).find('input[name="size[]"]').val().trim(),s=$(this).find('input[name="range[]"]').val().trim(),a=$(this).find('input[name="by[]"]').val().trim();0===t?(e||($(this).find('input[name="sku[]"]').addClass("is-invalid"),i=!0),n||($(this).find('input[name="size[]"]').addClass("is-invalid"),i=!0),s||($(this).find('input[name="range[]"]').addClass("is-invalid"),i=!0),a||($(this).find('input[name="by[]"]').addClass("is-invalid"),i=!0)):(e||n||s||a)&&(e||($(this).find('input[name="sku[]"]').addClass("is-invalid"),i=!0),n||($(this).find('input[name="size[]"]').addClass("is-invalid"),i=!0),s||($(this).find('input[name="range[]"]').addClass("is-invalid"),i=!0),a||($(this).find('input[name="by[]"]').addClass("is-invalid"),i=!0))}),$(".giblets-row").each(function(){var t=$(this).find('input[name="giblets_sku[]"]').val().trim(),e=$(this).find('input[name="giblets_by[]"]').val().trim();(t||e)&&(t||($(this).find('input[name="giblets_sku[]"]').addClass("is-invalid"),i=!0),e||($(this).find('input[name="giblets_by[]"]').addClass("is-invalid"),i=!0))}),i)return t.preventDefault(),!1;var n=[];if($(".sku-row").each(function(){var t=$(this).find('input[name="sku[]"]').val().trim();t&&n.push(t)}),$(".giblets-row").each(function(){var t=$(this).find('input[name="giblets_sku[]"]').val().trim();t&&n.push(t)}),n.length>0){t.preventDefault();var s=[],a=[];n.forEach(function(t){var i=$.ajax({url:BASE_URI+"Skus/check_existing_sku",type:"POST",data:{sku:t},dataType:"json"}).done(function(i){i.exists&&a.push(t)});s.push(i)}),$.when.apply($,s).done(function(){a.length>0?($(".sku-row").each(function(){var t=$(this).find('input[name="sku[]"]').val().trim();-1!==a.indexOf(t)&&$(this).find('input[name="sku[]"]').addClass("is-invalid").val("").attr("placeholder","Type another SKU")}),$(".giblets-row").each(function(){var t=$(this).find('input[name="giblets_sku[]"]').val().trim();-1!==a.indexOf(t)&&$(this).find('input[name="giblets_sku[]"]').addClass("is-invalid").val("").attr("placeholder","Type another SKU")})):$("#skuForm")[0].submit()}).fail(function(){$("#skuForm")[0].submit()})}else $("#skuForm")[0].submit()}),window.location.pathname.includes("/edit/")&&$(window).on("beforeunload",function(){navigator.sendBeacon(BASE_URI+"Skus/cancel_edit")})});