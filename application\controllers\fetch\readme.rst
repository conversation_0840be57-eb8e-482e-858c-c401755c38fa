######################################
Datables Server-side Fetch Controllers
######################################

These class objects extends the MYT controller class.
Also, these classes fetches data for jQuery DataTables.


#####################################################
Steps to Setup DataTable with Server-side Processing:
#####################################################

1.  Set "processing" and "serverSide" DataTable properties to TRUE
2.  Set "ajax" property to the URL where the DataTable data will be fetched


##########################################################
Formatting the URL to be set in DataTable "ajax" property:
##########################################################

1. BASE_URI + "fetch/" + $DataTable.attr('data-package')
2. BASE_URI is site_url()
3. "data-package" is the same with the Fetch controller
    (i.e. if data-package is "patient, then Fetch controller is "Patient")


####################################################
IMPORTANT! Formatting Database Column Values as HTML
####################################################

Yes, column data and HTML tags can be concatenated and formatted in SQL but it is recommended
to format them in PHP (i.e. using the formatter key of an SSP column) because
the HTML tags can mess up the results of DataTable filter(search) method