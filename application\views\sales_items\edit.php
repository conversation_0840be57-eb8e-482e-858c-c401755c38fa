<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" class="form-control" value="<?=set_value('name', $item->name);?>">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="unit">Unit</label>

						<input type="text" name="unit" class="form-control" value="<?=set_value('unit', $item->unit);?>">

						<div class="red-text flash-message">
							<?=form_error('unit');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="price">SRP</label>

						<input type="number" name="price" class="form-control" value="<?=set_value('price', $item->price);?>" step="any">

						<div class="red-text flash-message">
							<?=form_error('price');?>
						</div>
					</div>
				</div>
				
				<h5 class="crud-subtitle"><span>Customers</span></h5>

				<div class='row'>
					<?php foreach($customers as $customer):
						//Checks whether a variable is empty. Also check whether the variable is set/declared:
						if (isset($customer_prices_dictionary[$customer->id][$item->id])) {
							$customer_special_price = $customer_prices_dictionary[$customer->id][$item->id];
						} else {
							$customer_special_price = NULL;
						}
					?>
						<div class='col-md-4 form-group'>
							<strong><?=$customer->name?></strong><br>
							<input type='number' name='customer_price[<?=$customer->id?>]' class='form-control' value='<?=$customer_special_price?>'><br>
						</div>
					<?php endforeach;?>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
			</div>
		</form>
	</div>
</div>
					