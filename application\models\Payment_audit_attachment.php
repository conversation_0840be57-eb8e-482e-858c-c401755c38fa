<?php
class Payment_audit_attachment extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'payment_audit_attachment';
        parent::__construct();
    }

    /**
     * Get attachment by ID
     */
    public function get_attachment_by_payment_id($payment_id)
    {
        $sql = <<<EOT
SELECT *
FROM payment_audit_attachment
WHERE is_deleted = 0
    AND payment_id = ?
EOT;

        $binds = [$payment_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }

    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
