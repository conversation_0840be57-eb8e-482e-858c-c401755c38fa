+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: cols.length - 3
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function approveAndReload(purchaseItemId, dataTable) {
		$.when(approveRow(purchaseItemId)).then(
			function () {
				dataTable.ajax.reload(null, false);

				var $button = $('button.action-approve-item[data-id="' + purchaseItemId + '"]');
				$button.closest('td').html('');
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approveRow(purchaseItemId) {
		return $.ajax({
			url: BASE_URI + 'purchases/approve_item/' + purchaseItemId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function disapproveAndReload(purchaseItemId, dataTable) {
		$.when(disapproveRow(purchaseItemId)).then(
			function () {
				dataTable.ajax.reload(null, false);

				var $button = $('button.action-approve-item[data-id="' + purchaseItemId + '"]');
				$button.closest('tr.purchase_item').remove();
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function disapproveRow(purchaseItemId) {
		return $.ajax({
			url: BASE_URI + 'purchases/disapprove_item/' + purchaseItemId,
			type: 'POST',
			dataType: 'json'
		});
	}

  function pendingAndReload(purchaseItemId, dataTable) {
		$.when(pendingRow(purchaseItemId)).then(
			function () {
				location.reload();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(purchaseItemId) {
		return $.ajax({
			url: BASE_URI + "purchases/pending_item/" + purchaseItemId,
			type: "POST",
			dataType: "json",
		});
	}

	function saveToPrint(purchaseId) {
		$.when(approvePurchase(purchaseId)).then(
      function () {
				window.location.href = BASE_URI + "purchases/manager/";
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approvePurchase(purchaseId) {
		return $.ajax({
			url: BASE_URI + 'purchases/mark_approved/' + purchaseId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

	    $('#tableFilter')
	      .submit(function (event) {
	        event.preventDefault();

	        var queries = $(event.currentTarget).serialize();

	        var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + queries;
	        dataTable.ajax.url(url).load();

	        window.history.pushState(null, null, BASE_URI + 'transfers/receives/+?' + queries);
	      }).trigger('submit');

		$('.item-table')
			.on('click', '.action-approve-item', function (event) {
				var cols = $(event.currentTarget).closest('tr.purchase_item').find('td');
				var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to approve ' + name + '?',
					function () {
						var purchaseItemId = event.currentTarget.getAttribute('data-id');
						approveAndReload(purchaseItemId, dataTable);
					}
				).setHeader('<em>Approve Purchase Order Item</em>');
			})
			.on('click', '.action-disapprove-item', function (event) {
				var cols = $(event.currentTarget).closest('tr.purchase_item').find('td');
				var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to disapprove ' + name + '?',
					function () {
						var purchaseItemId = event.currentTarget.getAttribute('data-id');
						disapproveAndReload(purchaseItemId, dataTable);
					}
				).setHeader('<em>Dispprove Purchase Order Item</em>');
			})
			.on("click", ".action-pending-item", function (event) {
				var cols = $(event.currentTarget)
					.closest("tr.purchase_item")
					.find("td");
				var name = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to return to pending " + name + "?",
						function () {
							var purchaseItemId = event.currentTarget.getAttribute("data-id");
							pendingAndReload(purchaseItemId, dataTable);
						}
					)
					.setHeader("<em>Pending Purchase Order Item</em>");
			});


		$('.action-approve-purchase').click(function (event) {
			var purchaseId = event.currentTarget.getAttribute('data-id');

			alertify.confirm(
				'Are you sure you want to save to print Purchase Order No. ' + purchaseId + '?',
				function () {
					saveToPrint(purchaseId);
				}
			).setHeader('<em>Save to Print Purchase Order</em>');
		});
	});
}(jQuery);
