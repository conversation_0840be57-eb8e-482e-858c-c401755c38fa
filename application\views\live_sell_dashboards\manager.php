<style>
    .card {
        background-color: #fff;
        color: #000;
        border-radius: 15px;
        border-width: 1px;
        border-color: #555;
        text-align: center;
        transition: ease 0.3s;
    }

    @keyframes spinner {
        0% {
          transform: translate3d(-50%, -50%, 0) rotate(0deg);
        }
        100% {
          transform: translate3d(-50%, -50%, 0) rotate(360deg);
        }
    }

    .spin {
      animation: 1.5s linear infinite spinner;
      animation-play-state: inherit;
      border: solid 5px #cfd0d1;
      border-bottom-color: #1c87c9;
      border-radius: 50%;
      content: "";
      height: 40px;
      position: relative;
      top: 25px;
      left: 20px;
      width: 40px;
      display: inline-block;
      will-change: transform;
    }

    .card:hover {
        -webkit-box-shadow: 0 10px 6px -6px #777;
        -moz-box-shadow: 0 10px 6px -6px #777;
        box-shadow: 0 10px 6px -6px #777;
        cursor: pointer;
        background-color: #555;
        color: #fff;
    }

    .card-invisible:hover {
        -webkit-box-shadow: 0 !important;
        -moz-box-shadow: 0 !important;
        box-shadow: 0 !important;
        cursor: arrow;
    }
    .center{
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .card-sm { 
        cursor: pointer;
        height: 20vh;
    }

    .redirect {
        cursor: pointer;
        padding: 4px;
        transition: background-color .7s;
    }
    
    .redirect:hover {
        border-radius: 10px;
    }

    .card-m {
        height: 30vh;
        /* width: 70%; */
    }

    .row {
        margin: 2vh 0;
    }

    .emphasize-sm {
        font-size: 2.5em;
    }

    .title {
        padding-left: 5%;
    }

    .app-content,
    .app-footer {
        margin-left: 15%;
    }

    @media screen and (max-width: 768px) {
        .col {
            width: 100%; /* Full width for columns on small screens */
        }

        .row.center {
            flex-direction: column; /* Arrange rows in a column for small screens */
            text-align: center;
        }

        .card-m {
            width: 80%; /* Adjust the width to occupy more space */
            max-width: 400px; /* Set a maximum width to avoid becoming too wide */
            margin: 10px; /* Center the card horizontally */
        }

        .emphasize-sm {
            font-size: 1.2em;
        }

        .text-left.h3.text-center {
            text-align: left; /* Left-align text on small screens */
            font-size: 1.4em; /* Adjust font size for small screens */
            padding-top: 2%;
        }

        .title {
            padding: 5%;
            padding-bottom: 0; 
        }

        hr {
            padding: 0%;
            padding-bottom: 0; 
            width: 100%;
        }

        .app-content,
        .app-footer {
            margin: 5%;
            margin-bottom: 0%;
        }

    }
    
</style>

<div class="col topbtn">

	<div class="title">Live Sell Dashboard</div>
    <hr style="width:90%; border-color: black;">
    
    <br>

    <div class="container">
        <div class="row center">
            <div class="col-1" ></div>
            <div onclick="window.open('<?=base_url('purchase_orders/manager?status=pending'); ?>','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4">
                <p class="text-left h3 text-center">Pending PO</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?= number_format($pending_po)?>" class="spin" id="spin_1"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="window.open('<?=base_url('purchase_orders/manager?status=urgent');?>','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4">
                <p class="text-left h3 text-center">Urgent PO</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?= number_format($urgent_po)?>" class="spin" id="spin_2"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="window.open('<?=base_url('order_slips/manager?status=all');?>','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4">
                <p class="text-left h3 text-center">Receivables</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱'. number_format($total_receivables, 2)?>" class="spin" id="spin_3"></div></h1>
                    </div>
                </div>
            </div>
            
        </div>

        <div class="row center">
            <div class="col-1" ></div>

            <div onclick="window.open('<?=base_url('so_payments/manager'); ?>','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4">
                <p class="text-left h3 text-center">Monthly Sales</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱'. number_format($total_monthly_sales, 2)?>" class="spin" id="spin_4"></div></h1>
                    </div>
                </div>
            </div>

            <!-- <div onclick="window.open('<?=base_url('purchase_orders/receive_manager');?>','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4"> -->
            <div onclick="window.open('','_self')" class="card card-m d-flex flex-column justify-content-center col-3 ml-4">
                <p class="text-left h3 text-center">Monthly Expenses</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱'. number_format($total_monthly_expenses, 2)?>" class="spin" id="spin_5"></div></h1>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
