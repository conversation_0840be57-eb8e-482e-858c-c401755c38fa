!function(e){"use strict";function t(t){var r=e(t),a=r.find("thead th"),n=r.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:a.length-1},{targets:4,render:function(e){return 1==e?'<span style="color: green;">Active</span>':'<span style="color: red;">Inactive</span>'}}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package")});return n}function r(t,r){e.when(n(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t,r){e.when(i(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"suppliers/delete/"+t,type:"POST",dataType:"json"})}function i(t){return e.ajax({url:BASE_URI+"suppliers/inactive/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete, .action-inactive",function(t){var i=e(t.currentTarget).closest("tr.supplier").find("td"),o=void 0!==i[0]?i[0].innerHTML:"",s=e(t.currentTarget).hasClass("action-delete")?"delete":"inactive";if("delete"===s)alertify.confirm("Are you sure you want to delete "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,n)}).setHeader("<em>Delete Supplier</em>");else if("inactive"===s){var u=e(t.currentTarget).text().trim(),l="Set Active"===u?"ACTIVE":"INACTIVE",c="Set Active"===u?"green":"red";alertify.confirm("Are you sure you want to set status of supplier "+o+' to <span style="color:'+c+';">'+l+"</span>?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Set Status Supplier</em>")}}),e('select[name^="po_id"]').on("change",function(t){var r=t.currentTarget.value;e("a.btn_go").attr("href",BASE_URI+"supplier_invoices/issue/"+r)})})}(jQuery);