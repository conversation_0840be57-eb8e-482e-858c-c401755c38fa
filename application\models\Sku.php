<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sku extends MYT_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table_name = 'sku'; 
    }

    /**
     * Check if SKU already exists in database
     */
    public function sku_exists($sku_value)
    {
        $sql = "SELECT COUNT(*) as count FROM sku WHERE sku = ? AND is_deleted = 0";
        $query = $this->db->query($sql, [$sku_value]);
        $result = $query->row();
        return $result && $result->count > 0;
    }

    /**
     * Create SKU group with carcass and giblets data
     */
    public function create_sku_group($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys)
    {
        // Get user ID from session
        $user_id = 1; // Default user ID
        if (isset($_SESSION['user']) && isset($_SESSION['user']->id)) {
            $user_id = $_SESSION['user']->id;
        }

        $current_time = date('Y-m-d H:i:s');
        $first_id = null;

        // Insert Carcass SKUs
        if (!empty($skus) && is_array($skus)) {
            for ($i = 0; $i < count($skus); $i++) {
                if (!empty(trim($skus[$i]))) {
                    $data = [
                        'customer_id' => (int)$customer_id,
                        'sku' => trim($skus[$i]),
                        'size' => isset($sizes[$i]) ? trim($sizes[$i]) : '',
                        'size_range' => isset($ranges[$i]) ? trim($ranges[$i]) : '',
                        'by' => isset($bys[$i]) ? trim($bys[$i]) : '',
                        'category' => 'Carcass',
                        'added_on' => $current_time,
                        'added_by' => $user_id,
                        'is_deleted' => 0
                    ];

                    $result = $this->db->insert('sku', $data);
                    if (!$result) {
                        return false;
                    }
                    if ($first_id === null) {
                        $first_id = $this->db->insert_id();
                    }
                }
            }
        }

        // Insert Giblets SKUs
        if (!empty($giblets_skus) && is_array($giblets_skus)) {
            for ($i = 0; $i < count($giblets_skus); $i++) {
                if (!empty(trim($giblets_skus[$i]))) {
                    $data = [
                        'customer_id' => (int)$customer_id,
                        'sku' => trim($giblets_skus[$i]),
                        'size' => '', // Giblets don't have size
                        'size_range' => '', // Giblets don't have size_range
                        'by' => isset($giblets_bys[$i]) ? trim($giblets_bys[$i]) : '',
                        'category' => 'Giblets',
                        'added_on' => $current_time,
                        'added_by' => $user_id,
                        'is_deleted' => 0
                    ];

                    $result = $this->db->insert('sku', $data);
                    if (!$result) {
                        return false;
                    }
                    if ($first_id === null) {
                        $first_id = $this->db->insert_id();
                    }
                }
            }
        }
        return $first_id !== null ? $first_id : false;
    }

    /**
     * Get SKU group (carcass and giblets) for editing
     */
    public function get_sku_group($sku)
    {
        $customer_id = $sku->customer_id;
        $carcass_skus = $this->db->where([
            'customer_id' => $customer_id,
            'category' => 'Carcass',
            'is_deleted' => 0
        ])->get('sku')->result();
        $giblets_skus = $this->db->where([
            'customer_id' => $customer_id,
            'category' => 'Giblets',
            'is_deleted' => 0
        ])->get('sku')->result();

        return [
            'carcass' => $carcass_skus,
            'giblets' => $giblets_skus
        ];
    }
    /**
     * Update SKU group
     */
    public function update_sku_group($sku, $customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys)
    {
        $user_id = 1;
        if (isset($_SESSION['user']) && isset($_SESSION['user']->id)) {
            $user_id = $_SESSION['user']->id;
        }

        $current_time = date('Y-m-d H:i:s');
        $this->db->where([
            'customer_id' => $sku->customer_id,
            'added_on' => $sku->added_on,
            'added_by' => $sku->added_by,
            'is_deleted' => 0
        ])->update('sku', [
            'is_deleted' => 1,
            'updated_by' => $user_id,
            'updated_on' => $current_time
        ]);
        return $this->create_sku_group($customer_id, $skus, $sizes, $ranges, $bys, $giblets_skus, $giblets_bys);
    }
    /**
     * Delete SKU group (soft delete) - only delete specific submission/order
     */
    public function delete_sku_group($sku)
    {
        $user_id = 1; 
        if (isset($_SESSION['user']) && isset($_SESSION['user']->id)) {
            $user_id = $_SESSION['user']->id;
        }

        $current_time = date('Y-m-d H:i:s');
        return $this->db->where([
            'customer_id' => $sku->customer_id,
            'added_on' => $sku->added_on,
            'added_by' => $sku->added_by,
            'is_deleted' => 0
        ])->update('sku', [
            'is_deleted' => 1,
            'updated_by' => $user_id,
            'updated_on' => $current_time
        ]);
    }
}