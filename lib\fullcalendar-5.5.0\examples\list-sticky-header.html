<!DOCTYPE html>
<html>
<head>
<meta charset='utf-8' />
<link href='../lib/main.css' rel='stylesheet' />
<script src='../lib/main.js'></script>
<script src='../lib/main.js'></script>
<script>

  document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');

    var calendar = new FullCalendar.Calendar(calendarEl, {
      height: 'auto',
      // stickyHeaderDates: false, // for disabling

      headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'listMonth,listYear'
      },

      // customize the button names,
      // otherwise they'd all just say "list"
      views: {
        listMonth: { buttonText: 'list month' },
        listYear: { buttonText: 'list year' }
      },

      initialView: 'listYear',
      initialDate: '2020-09-12',
      navLinks: true, // can click day/week names to navigate views
      editable: true,
      events: [
        {
          title: 'repeating event 1',
          daysOfWeek: [ 1, 2, 3 ],
          duration: '00:30'
        },
        {
          title: 'repeating event 2',
          daysOfWeek: [ 1, 2, 3 ],
          duration: '00:30'
        },
        {
          title: 'repeating event 3',
          daysOfWeek: [ 1, 2, 3 ],
          duration: '00:30'
        }
      ]
    });

    calendar.render();
  });

</script>
<style>

  body {
    margin: 40px 10px;
    padding: 0;
    font-family: Arial, Helvetica Neue, Helvetica, sans-serif;
    font-size: 14px;
  }

  #calendar {
    max-width: 1100px;
    margin: 0 auto;
  }

</style>
</head>
<body>

  <div id='calendar'></div>

</body>
</html>
