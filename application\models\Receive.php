<?php
class Receive extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'receive';
        parent::__construct();
    }

    /**
     * Get Receive By ID
     */
    public function get_by_id($receive_id)
    {
        $sql = <<<EOT
SELECT receive.*,
    purchase.purchase_date, purchase.total
FROM receive
LEFT JOIN receive_item ON receive_item.receive_id = receive.id
LEFT JOIN purchase ON purchase.id = receive.purchase_id
WHERE receive.id = ?
    AND receive.is_deleted = 0
    AND purchase.is_deleted = 0
EOT;
        $binds = [$receive_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_balance_by_supplier($supplier_id)
    {
        $sql = <<<EOT
SELECT purchase.*, (purchase.total - purchase.paid_amount) AS balance
FROM purchase
WHERE purchase.supplier_id = ?
    AND purchase.paid_amount < purchase.total
    AND status = 'approved'
    AND is_deleted = 0
EOT;
        $binds = [$supplier_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
