<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><b><large>Customer : </large></b> <?=$order_confirmation->customer;?></p>
					<p class="text-left"><b><large>Remarks : </large></b> <?=$order_confirmation->remarks;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><b><large>Order Slip No. : </large></b> <?=anchor(site_url("order_slips/preview/" . $order_confirmation->order_slip_id), $order_confirmation->order_slip_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Order Confirmation Date : </large></b> <?=date('m/d/Y', strtotime($order_confirmation->order_confirmation_date));?></p>
					<!-- <p class="text-left"><large>Prepared By : </large> <?=$order_confirmation->issuer;?></p> -->
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>List of Confirmed Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Supplier</th>

									<th class="text-center" style="width: 15%;">Total Qty</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_quantity = 0;
                                if (!empty($order_confirmation_item_ids)):
                                	$counter = 0;
                                    foreach ($order_confirmation_item_ids as $i => $order_confirmation_item_id):
										$total_quantity += $retain_items[$counter]['qty'];
                            ?>
										<tr class="order_confirmation_item">
											<td><?=$retain_items[$counter]['item_name'];?></td>

											<td><?=$retain_items[$counter]['unit'];?></td>

											<td><?=$retain_items[$counter]['supplier_name'];?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['qty']);?></td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
									<tr class="order_confirmation_item">
										<td></td>

										<td></td>

										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_quantity);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($order_confirmation->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>order_confirmation Attachments</span></h5>
						<?php $attachments = explode('🔥',$order_confirmation->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/order_confirmations/'.$order_confirmation->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$issuer->full_name;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Order Confirmed By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('order_confirmations');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="order_confirmation_id" value="<?=$order_confirmation->id?>">
		</form>
	</div>
</div>