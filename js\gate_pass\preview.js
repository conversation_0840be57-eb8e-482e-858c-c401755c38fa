+ function ($) {
    'use strict';

    function changeStatusAndReload(pocId, status) {
		return $.ajax({
			url: BASE_URI + 'poc/change_status/' + pocId + '/' + status,
			type: 'POST',
			dataType: 'json'
		});
	}

    $(function () {
        $('.action-print').click(function () {
                window.print();
        });

        $('.action-approve').click(function (event) {
            let pocId = event.currentTarget.getAttribute('data-id');

            alertify.confirm(
                'Are you sure you want to approve POC No. ' + pocId + '?',
                function () {
                    changeStatusAndReload(pocId, 'approved').done(function () {
                        window.location.reload();
                    });
                }
            ).setHeader('<em>Approve POC</em>');
        });

        $('.action-disapprove').click(function (event) {
            let pocId = event.currentTarget.getAttribute('data-id');

            alertify.confirm(
                'Are you sure you want to disapprove POC No. ' + pocId + '?',
                function () {
                    changeStatusAndReload(pocId, 'disapproved').done(function () {
                        window.location.reload();
                    });
                }
            ).setHeader('<em>Disapprove POC</em>');
        });
    });
}(jQuery);
    