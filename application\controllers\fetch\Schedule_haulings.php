<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedule_haulings extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('schedule_hauling');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch schedule_haulings
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'schedule_hauling';
        $primary_key = 'schedule_hauling.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_number(),
            $this->_get_purchase_order_number(),
            $this->_get_schedule_hauling_date(),
            $this->_get_schedule_hauling_number(),
            $this->_get_customer(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
schedule_hauling
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user ON user.id = schedule_hauling.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        // var_dump($status);
        // die();
        $where = <<<EOT
schedule_hauling.is_deleted = 0
EOT;            

        if ($status !== 'all') {
            $where .= <<<EOT

AND schedule_hauling.status = "$status"
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'schedule_hauling.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'schedule_hauling.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'schedule_hauling';
            }
        ];
    }

    /**
     * Get schedule_hauling number
     */
    protected function _get_schedule_hauling_number()
    {
        return [
            'db' => 'schedule_hauling.id',
            'as' => 'schedule_hauling_number',
            'dt' => 0,
            'field' => 'schedule_hauling_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("schedule_haulings/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get order slip number
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'schedule_hauling.order_slip_id',
            'as' => 'order_slip_number',
            'dt' => 1,
            'field' => 'order_slip_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get purchase order number
     */
    protected function _get_purchase_order_number()
    {
        return [
            'db' => 'schedule_hauling.purchase_order_id',
            'as' => 'purchase_order_number',
            'dt' => 2,
            'field' => 'purchase_order_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("purchase_orders/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get schedule_hauling date
     */
    protected function _get_schedule_hauling_date()
    {
        return [
            'db' => 'DATE_FORMAT(schedule_hauling.schedule_hauling_date, "%b %d, %Y")',
            'as' => 'schedule_hauling_date',
            'dt' => 3,
            'field' => 'schedule_hauling_date'
        ];
    }

    /**
     * Get schedule_hauling customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 4,
            'field' => 'customer'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'schedule_hauling.remarks',
            'dt' => 5,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(schedule_hauling.is_deleted = 0, schedule_hauling.status, "cancelled")',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 6,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'schedule_hauling.id',
            'as' => 'file',
            'dt' => 7,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->schedule_hauling->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/schedule_haulings/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'schedule_hauling.id',
            'as' => 'actions',
            'dt' => 7,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        // $res .= anchor('sales_orders/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        $res .= anchor('processings/add/' . $d, 'Process', 'title="Confirm Order" class="dropdown-item text-left green-text"');
                        $res .= anchor('schedule_haulings/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('schedule_haulings/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } else {
                        $res .= anchor('schedule_haulings/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
