+(function ($) {
	"use strict";

	function submitForm(action, formData) {
		return $.ajax({
			url: action,
			method: "POST",
			data: formData,
			processData: false,
			contentType: false,
			dataType: "json",
		});
	}

	function getCheckNo(bankId) {
		return $.ajax({
			url: BASE_URI + "checks/no/" + bankId,
			method: "GET",
			dataType: "json",
		});
	}

	function showError($modal, error) {
		$modal.find(".error").html(error);
	}

	function getPurchases() {
		return $.ajax({
			url: BASE_URI + "purchases/approved",
			type: "GET",
			dataType: "json",
		});
	}

	function addPurchaseRow(receives) {
		var $newRow = $(purchaseRow());

		setPurchaseInvoice($newRow, receives);

		var $purchaseTable = $(".purchase-table");
		var checkPurchases = $purchaseTable.find(".check-purchase");

		if (checkPurchases.length === 0) {
			$purchaseTable.find(".no-purchase").remove();
		}
		$purchaseTable.find("tbody").append($newRow);
	}

	function purchaseRow() {
		var rowId = makeRandomId();

		return (
			'<tr class="check_purchase">' +
			"<td>" +
			'<input type="hidden" name="check_purchase_' +
			rowId +
			'" value="' +
			rowId +
			'">' +
			'<select name="receive_' +
			rowId +
			'" class="form-control dropsearch" data-placeholder=""></select>' +
			"</td>" +
			"<td>" +
			'<input type="number" name="amount_' +
			rowId +
			'" class="form-control" min="0" step="any">' +
			"</td>" +
			'<td class="text-right">' +
			'<button type="button" class="btn btn-sm btn-danger action-delete-purchase">Delete</button>' +
			"</td>" +
			"</tr>"
		);
	}

	function makeRandomId() {
		var text = "";
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function setPurchaseList($row, purchases) {
		$row.find('select[name^="receive"]').select2({
			data: purchases,
			placeholder: $(this).attr("data-placeholder"),
			allowClear: true,
			width: "100%",
		});
	}

	function emptyRow() {
		return (
			'<tr class="no-purchase">' +
			'<td class="text-center" colspan="3">No purchase orders added yet.</td>' +
			"</tr>"
		);
	}

	function getInfo(receiveId) {
		return $.ajax({
			url: BASE_URI + "receives/info/" + receiveId,
			type: "GET",
			dataType: "json",
		});
	}

	function setPurchase($row, purchase) {
		var total = parseFloat(purchase.grand_total);
		if (isNaN(total)) {
			total = 0;
		}

		$row.find('input[name^="amount"]').val(total.toFixed(2));
	}

	function calculateTotal() {
		var total = 0;
		$(".purchase-table")
			.find(".check_purchase")
			.each(function (_, purchase) {
				var subtotal = parseFloat(
					$(purchase).find('input[name^="amount"]').val()
				);
				if (isNaN(subtotal)) {
					subtotal = 0;
				}
				total += subtotal;
			});
		$('input[name="total"]').val(total.toFixed(2));
	}

	function removePurchaseRow(event) {
		$(event.currentTarget).closest(".check_purchase").remove();

		var $purchaseTable = $(event.delegateTarget);
		var purchaseItems = $purchaseTable.find(".check_purchase");
		if (purchaseItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$purchaseTable.find("tbody").html($emptyRow);
		}

		calculateTotal();
	}

	function getInvoice(supplierId) {
		return $.ajax({
			url: BASE_URI + "receives/get_w_balance/" + supplierId,
			type: "GET",
			dataType: "json",
		});
	}

	function setPurchaseInvoice($row, receives) {
		$row.find('select[name^="receive"]').select2({
			data: receives,
			placeholder: $(this).attr("data-placeholder"),
			allowClear: true,
			width: "100%",
		});
	}

	$(function () {
		var $btnAddBank = $(".action-new-bank");
		var $modalBank = $("#addBank");

		$btnAddBank.click(function () {
			$modalBank.modal("show");
		});

		$modalBank.on("submit", "form", function (event) {
			event.preventDefault();

			var form = event.currentTarget;
			var action = form.getAttribute("action");
			var formData = new FormData(form);

			$.when(submitForm(action, formData)).then(
				function (response) {
					var id = response.bank_id;
					var name = formData.get("name");
					var option = new Option(name, id, false, true);

					$('select[name^="bank"]').append(option).trigger("change");
					$modalBank.modal("hide");
				},
				function (jqXHR) {
					var response = JSON.parse(jqXHR.responseText);
					showError($modalBank, response.message);
				}
			);
		});

		var $inputBank = $('select[name="bank"]');
		var $inputCheckNo = $('select[name="check_no"]');
		var $btnEnrollCheck = $(".action-enroll-check");
		var $modalEnrollCheck = $("#enrollCheck");

		$inputBank
			.change(function (event) {
				var bankId = event.currentTarget.value;
				if (bankId === "") return;

				$.when(getCheckNo(bankId)).then(
					function (response) {
						var checkNos = response.enrolled_checks;
						checkNos.map(function (checkNo) {
							checkNo.id = checkNo.doc_no;
							checkNo.text = checkNo.doc_no;
						});

						$inputCheckNo.empty().select2("destroy").select2({
							data: checkNos,
							placeholder: "",
							allowClear: true,
							width: "100%",
						});

						$inputCheckNo
							.val($inputCheckNo.attr("data-default"))
							.attr("data-default", "")
							.trigger("change");

						$btnEnrollCheck.addClass("d-none");
					},
					function () {
						$inputCheckNo.val("").empty();
						$btnEnrollCheck.removeClass("d-none");
					}
				);
			})
			.trigger("change");

		$btnEnrollCheck.click(function () {
			var url = BASE_URI + "checks/enroll_remote/" + $inputBank.val();
			var $form = $modalEnrollCheck.find("form");

			$form.attr("action", url);
			$form
				.find('input[name="bank"]')
				.val($inputBank.find("option:selected").text());

			$modalEnrollCheck.modal("show");
		});

		$modalEnrollCheck.on("submit", "form", function (event) {
			event.preventDefault();

			var form = event.currentTarget;
			var action = form.getAttribute("action");
			var formData = new FormData(form);

			$.when(submitForm(action, formData)).then(
				function () {
					$inputBank.trigger("change");
					$modalEnrollCheck.modal("hide");
					$btnEnrollCheck.addClass("d-none");
				},
				function (jqXHR) {
					var response = JSON.parse(jqXHR.responseText);
					showError($modalEnrollCheck, response.message);
				}
			);
		});

		var recevies = [
			{
				id: "",
				text: "",
			},
		];

		$("select[name='supplier']").change(function (event) {
			var supplierId = event.currentTarget.value;

			$.when(getInvoice(supplierId)).then(function (response) {
				response.receives.forEach(function (receive) {
					var total = parseFloat(receive.grand_total);
					if (isNaN(total)) {
						total = 0;
					}

					receive.text =
						"Purchase Invoice #" + receive.id + " - " + total.toFixed(2);
					recevies.push(receive);
				});
			});
		});

		$(".action-add-payment").click(function () {
			addPurchaseRow(recevies);
		});

		var checkPurchase = null;
		$(".purchase-table")
			.on("change", 'select[name^="receive"]', function (event) {
				checkPurchase = $(event.currentTarget).closest(".check_purchase");

				$.when(getInfo(event.currentTarget.value)).then(
					function (response) {
						setPurchase(checkPurchase, response.receive);
						calculateTotal();
					},
					function () {
						setPurchase(checkPurchase, null);
						calculateTotal();
					}
				);
			})
			.on("click", ".action-delete-purchase", function (event) {
				removePurchaseRow(event);
			})
			.on("change", 'input[name^="amount"]', calculateTotal);

		calculateTotal();
	});
})(jQuery);
