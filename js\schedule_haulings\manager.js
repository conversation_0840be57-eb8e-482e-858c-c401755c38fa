+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(schedule_haulingId, dataTable) {
		$.when(receiveRow(schedule_haulingId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(schedule_haulingId) {
		return $.ajax({
			url: BASE_URI + 'schedule_haulings/receive_schedule_hauling/' + schedule_haulingId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(schedule_haulingId, dataTable) {
		$.when(deleteRow(schedule_haulingId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(schedule_haulingId) {
		return $.ajax({
			url: BASE_URI + 'schedule_haulings/delete/' + schedule_haulingId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.schedule_hauling').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the schedule_hauling ' + docNo + '?',
					function () {
						var schedule_haulingId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(schedule_haulingId, dataTable);
					}
				).setHeader('<em>receive schedule_hauling</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.schedule_hauling').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete Schedule Hauling ' + docNo + '?',
					function () {
						var schedule_haulingId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(schedule_haulingId, dataTable);
					}
				).setHeader('<em>Delete Schedule Hauling</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/schedule_haulings?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'schedule_haulings/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
