<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	exportEnabled: true,
	theme: "light1",
	title:{
		text: "Software Sales Conversion"
	},
	data: [{
		type: "pyramid",
		yValueFormatString: "#'%'",
		indexLabelFontColor: "black",
		indexLabelFontSize: 16,
		indexLabel: "{label} - {y}",
		//reversed: true, // Reverses the pyramid
		dataPoints: [
			{ y: 100, label: "Website Visit" },
			{ y: 65, label: "Download Page Visit" },
			{ y: 45, label: "Downloaded" },
			{ y: 32, label: "Interested To Buy" },
			{ y: 5, label: "Purchased" }
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>