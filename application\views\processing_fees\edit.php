<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formprocessing" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" >
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $processing_fee->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="processing_fee_date" class="control-label">Processing Fee Date</label>

						<input type="text" name="processing_fee_date" class="form-control datepicker" value="<?=set_value('processing_fee_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('processing_fee_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $processing_fee->remarks);?>">
						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

			<div>
            	<h5 class="crud-subtitle"><span>Processing Fees</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table processing-fees" data-type="processing_fees" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($processing_fees_items)):
                                	$counter = 0;
                                    foreach ($processing_fees_items as $i => $processing_fees_item):
                                    $processing_fees_item = $processing_fees_items[$counter];
                                    if($processing_fees_item['classification'] !== "processing-fees") {
                                    	continue;
                                    }
                            ?>
										<tr class="processing_fee_item">
											<td>
												<input type="hidden" name=<?="classification_" . $processing_fees_item['id']?> value="processing-fees">
												<?php $fn_processing_fees_item = 'processing_fee_item_' . $processing_fees_item['id'];?>
												<input type="hidden" name="<?=$fn_processing_fees_item?>" value="<?=set_value($fn_processing_fees_item, $processing_fees_item['id']);?>">

												<?php $fdn_item = 'item_' . $processing_fees_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$processing_fees_item['item_id']?>"><?=$processing_fees_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $processing_fees_item['id'];?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, $processing_fees_items[$counter]['qty']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $processing_fees_item['id'];?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control" value="<?=set_value($fn_unit, $processing_fees_items[$counter]['unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $processing_fees_item['id'];?>
												<!-- <input type="number" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range);?>" step="any" readonly> -->
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price, $processing_fees_items[$counter]['unit_price']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $processing_fees_item['id'];?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control" value="<?=set_value($fn_amount, $processing_fees_items[$counter]['amount']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_amount);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="processing-fees" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>

        	<h5 class="crud-subtitle"><span>Add-ons</span></h5>

			<div>
				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table add-ons" data-type="add_ons" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($add_ons_items)):
                                	$counter = 0;
                                    foreach ($add_ons_items as $i => $add_ons_item):
                                    $add_ons_item = $add_ons_items[$counter];
                                    if($add_ons_item['classification'] !== "add-ons") {
                                    	continue;
                                    }
                            ?>
										<tr class="processing_fee_item">
											<td>
												<input type="hidden" name=<?="classification_" . $add_ons_item['id']?> value="add-ons">
												<?php $fn_add_ons_item = 'processing_fee_item_' . $add_ons_item['id'];?>
												<input type="hidden" name="<?=$fn_add_ons_item?>" value="<?=set_value($fn_add_ons_item, $add_ons_item['id']);?>">

												<?php $fdn_item = 'item_' . $add_ons_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$add_ons_item['item_id']?>"><?=$add_ons_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $add_ons_item['id'];?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, $add_ons_items[$counter]['qty']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $add_ons_item['id'];?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control unit" value="<?=set_value($fn_unit, $add_ons_items[$counter]['unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $add_ons_item['id'];?>
												<!-- <input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price);?>" step="any" readonly> -->
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price, $add_ons_items[$counter]['unit_price']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $add_ons_item['id'];?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control" value="<?=set_value($fn_amount, $add_ons_items[$counter]['amount']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_amount);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="add-ons" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>

        	<h5 class="crud-subtitle"><span>Other Charges</span></h5>

			<div>
				<div class="row">
					<div class="col-md-12 form-group other-charges">
						<table class="item-table" data-type="other_charges" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($other_charges_items)):
                                	$counter = 0;
                                    foreach ($other_charges_items as $i => $other_charges_item):
                                    $other_charges_item = $other_charges_items[$counter];
                                    if($other_charges_item['classification'] !== "other-charges") {
                                    	continue;
                                    }
                            ?>
										<tr class="processing_fee_item">
											<td>
												<input type="hidden" name=<?="classification_" . $other_charges_item['id']?> value="other-charges">
												<?php $fn_other_charges_item = 'processing_fee_item_' . $other_charges_item['id'];?>
												<input type="hidden" name="<?=$fn_other_charges_item?>" value="<?=set_value($fn_other_charges_item, $other_charges_item['id']);?>">

												<?php $fdn_item = 'item_' . $other_charges_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$other_charges_item['item_id']?>"><?=$other_charges_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $other_charges_item['id'];?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, $other_charges_items[$counter]['qty']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $other_charges_item['id'];?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control" value="<?=set_value($fn_unit, $other_charges_items[$counter]['unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $other_charges_item['id'];?>
												<!-- <input type="number" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range);?>" step="any" readonly> -->
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price, $other_charges_items[$counter]['unit_price']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $other_charges_item['id'];?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control" value="<?=set_value($fn_amount, $other_charges_items[$counter]['amount']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_amount);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="other-charges" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>
			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save 	</button>
				<a href="<?=site_url('processing_fees')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
v 