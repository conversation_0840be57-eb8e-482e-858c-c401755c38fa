<!DOCTYPE html>
<html>

<head>
    <title>Asia United Bank Print Check (Voucher)</title>

    <style>
        @media screen {
            html, body {
                font-size: 9pt!important;
            }
        }

        @media print {
            @page {
                size: 8in 8.5in;
                size: portrait;
                margin: 0;
            }

            html, body {
                font-size: 9pt!important;
                width: 8in;
                height: 8.5in;
            }
        }

        table.voucher,
        table.voucher th,
        table.voucher td {
            vertical-align: top;
            padding: 7px;
            border: 1px solid black;
            border-collapse: collapse;
            font-size: 9pt!important;
        }
	</style>
</head>

<body>
    <div style="position: absolute; margin-top: 10mm; margin-left: 3mm; width: 190mm;">
        <table class="voucher" style="width: 100%;">
            <tbody>
                <tr>
                <td colspan="2" style="width: 65%; font-weight: bold;">LEYTE AGRI VENTURES CORPORATION</td>
                    <td style="width: 35%;">CHECK NO: <?=$check->check_no;?></td>
                </tr>

                <tr>
                    <td style="width: 10%;">DATE:</td>
                    <td style="width: 55%;">
                        <?php
                            $check_date = new DateTime($check->check_date);
                            echo $check_date->format('F j, Y');
                        ?>
                    </td>
                    <td style="width: 35%;">AMOUNT: <?='Php ' . number_format($check->amount, 2);?></td>
                </tr>

                <tr>
                    <td style="width: 10%;">PAYEE:</td>
                    <td colspan="2" style="width: 90%;"><?=$check->supplier;?></td>
                </tr>

                <tr style="height: 50mm;">
                    <td style="width: 20%;">PARTICULARS</td>
                    <td colspan="2" style="width: 80%;">
                    <?php if(!empty($check_purchases)):?>
                            <?php foreach($check_purchases as $check_purchase):?>
                            PO #<?=$check_purchase->purchase_id?> <br>
                            <?php endforeach;?>
                    <?php endif;?>
                    </td>
                </tr>

                <tr>
                    <td style="width: 20%;">REFERENCE NO:</td>
                    <td style="width: 40%;"></td>
                    <td style="width: 40%;">RECEIVED DATE:</td>
                </tr>

                <tr>
                    <td style="width: 20%;">PREPARED BY:</td>
                    <td style="width: 40%;"><?=$_SESSION['user']->username;?></td>
                    <td style="width: 40%;"></td>
                </tr>

                <tr>
                    <td style="width: 20%;">APPROVED BY:</td>
                    <td style="width: 40%;"></td>
                    <td style="width: 40%;"></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="position: absolute; margin-top: 152mm; margin-right: 14mm; width: 45mm; text-align: left; padding-left: 10mm; right: 0;">
        <?php
            $check_date = new DateTime($check->check_date);
            // Get the formatted date
            $formatted_date = $check_date->format('m/d/Y');
            
            // Get the year and add spaces between each character
            $year = $check_date->format('Y');
            $day = $check_date->format('d');
            $month = $check_date->format('m');
            $spaced_year = implode(' ', str_split($year));
            $spaced_day = implode(' ', str_split($day));
            $spaced_month = implode(' ', str_split($month));
            
            // Output the formatted date with CSS classes
            echo '<span class="date-part">' . $month . '</span>';
            echo '<span class="date-part dash-white">-</span>'; // Adding a separator with spaces
            echo '<span class="date-part">' . $day . '</span>';
            echo '<span class="date-part dash-white">-</span>'; // Adding a separator with spaces
            echo '<span class="date-part">' . $year . '</span>';
        ?>
    </div>
    <style>
        .date-part {
            letter-spacing: 0.8em; /* Adjust the value as needed */
        }
        .dash-white {
            color: white; /* Set the color to white */
        }
    </style>

    <div style="position: absolute; margin-top: 160mm; margin-left: 30mm; width: 115mm;">
        <?php
            echo '*** ' . $check->supplier . ' ***';
        ?>
    </div>

    <div style="position: absolute; margin-top: 160mm; margin-right: 10mm; width: 45mm; text-align: left; padding-left: 10mm; right: 0;">
        <?php
            echo number_format($check->amount, 2);
        ?>
    </div>

    <div style="position: absolute; margin-top: 167mm; margin-left: 30mm; width: 180mm;">
        <?php
            $whole = floor($check->amount);
            $decimal = round(($check->amount - $whole) * 100);
            $figures = array_filter([
                $whole > 0 ? spellout($whole) : null,
                $decimal ? $decimal . '/100' : null
            ]);

            echo '*** ' . implode(' and ', $figures) . ' pesos only' . ' ***';
        ?>
    </div>

    <div style="position: absolute; margin-top: 187mm; margin-right: 50mm; width: 55mm; text-align: left; padding-left: 10mm; right: 0;">
        <?=strtoupper($check->sig_1);?>
    </div>

    <div style="position: absolute; margin-top: 187mm; width: 55mm; text-align: left; padding-left: 10mm; right: 0;">
        <?=strtoupper($check->sig_2);?>
    </div>

	<script src="<?=base_url('lib/jquery/jquery-3.3.1.min.js');?>"></script>

    <script>
        function markPrinted() {
            $.ajax({
                url: '<?=site_url('checks/printed/' . $check->id);?>',
                type: 'POST'
            })
            .done(function () {
                window.print();
            })
            .fail(function () {
                alert('Something went wrong. Please refresh page.');
            });
        }

        markPrinted();
    </script>
</body>

</html>
