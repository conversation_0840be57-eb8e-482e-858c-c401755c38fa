!function(t){"use strict";function e(e){var a=t(e),n=(a.find("thead th"),a.DataTable({lengthChange:!0,language:{infoFiltered:""},dom:"Bfrtlip",pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[2,3]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,lengthMenu:[[10,25,50,100,-1],[10,25,50,100,"All"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")}));return n}t(function(){var a=e(".tableA");t("#tableFilter").submit(function(e){e.preventDefault();var n=t(e.currentTarget).serialize(),r=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+n;a.ajax.url(r).load(),window.history.pushState(null,null,BASE_URI+"invoices/bad_debts?"+n)}).trigger("submit")})}(jQuery);