+ function ($) {
    'use strict';

    function generateEod() {
		return $.ajax({
			url: BASE_URI + 'eod/generate_eodbc',
			type: 'POST',
			dataType: 'json'
		});
	}

    $(function () {
        $('.action-print').click(function (event) {
            let pocId = event.currentTarget.getAttribute('data-id');
            pocId = parseInt(pocId) + 1;

            alertify.confirm(
                'Are you sure you want to generate EOD-BC-I No. ' + pocId + '?',
                function () {
                    window.print();
                    generateEod().done(function () {
                        window.location.reload();
                    });
                }
            ).setHeader('<em>Generate EOD-BC-I</em>');
        });
    });
}(jQuery);
    