!function(e){"use strict";function t(t){var a=e(t),o=a.find("thead th"),n=a.DataTable({dom:"lrtip",lengthChange:!0,info:!1,pagingType:"full_numbers",paging:!1,order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:o.length-1}],autoWidth:!1,processing:!0,serverSide:!0,ajax:{url:BASE_URI+"fetch/"+a.attr("data-package"),data:function(t){t.from=e("#from").val(),t.to=e("#to").val()}},footerCallback:function(o,n,l,s,d){var i=this.api(),c=a.attr("data-col-sum");if(void 0!==c&&c>-1){var u=function(e){return"string"==typeof e?1*e.replace(/<.*?>/g,"").replace(/[\$,]/g,""):"number"==typeof e?e:0},p=i.column(c).data().reduce(function(e,t){return u(e)+u(t)},0);e(i.column(c).footer()).html(e.fn.addNumericCommas(parseFloat(p).toFixed(2))),".tableA"===t?(e("#totalRevenue").html(e.fn.addNumericCommas(parseFloat(p).toFixed(2))),r+=parseFloat(p)):".tableB"===t&&(e("#totalExpense").html(e.fn.addNumericCommas(parseFloat(p).toFixed(2))),r-=parseFloat(p)),e(".totalIncome").html(e.fn.addNumericCommas(parseFloat(r).toFixed(2)))}}});return n}function a(){o=new Chart(n,{type:"line",data:{labels:[],datasets:[{label:"Revenue",data:[],borderWidth:1,borderColor:"Blue"},{label:"Expense",data:[],borderWidth:1,borderColor:"Red"},{label:"Income",data:[],borderWidth:1,borderColor:"Green"}]},options:{scales:{y:{beginAtZero:!0}}}})}var o,r=0,n=document.getElementById("myChart");n.width=600,n.height=200,a(),e(function(){var a=t(".tableA"),l=t(".tableB");e("#tableFilter").submit(function(t){t.preventDefault(),r=0;var s=e("#from").val(),d=e("#to").val();e.ajax({type:"POST",url:BASE_URI+"incomes/chart_data",data:{fromDate:s,toDate:d},dataType:"json",success:function(e){o.destroy();let t=[],a=[],r=[],l=[],s=[],d=e.data.revenue,i=e.data.expense;for(let e=0;e<d.length;e++)t.push(d[e].revenue_date),a.push(parseFloat(d[e].total_revenue_per_day)),r.push(i[e].expense_date),l.push(parseFloat(i[e].total_expense_per_day)),s.push(parseFloat(d[e].total_revenue_per_day-i[e].total_expense_per_day));o=new Chart(n,{type:"line",data:{labels:t,datasets:[{label:"Revenue",data:a,borderWidth:1,borderColor:"blue"},{label:"Expense",data:l,borderWidth:1,borderColor:"red"},{label:"Income",data:s,borderWidth:1,borderColor:"green"}]},options:{scales:{y:{beginAtZero:!0}}}})},error:function(e){console.error(e)}}),a.ajax.reload(),l.ajax.reload()}).trigger("submit")}),e("#exportToExcel").click(function(){var t=e("#from").val(),a=e("#to").val();e.ajax({type:"POST",url:BASE_URI+"incomes/export_excel",data:{fromDate:t,toDate:a},dataType:"json",success:function(t){window.open(BASE_URI+"assets/"+t.filename);var a="Download Excel By Filter";e(".btn-process").text(a),e(".btn-process").css("pointer-events","auto")},error:function(e){console.error(e)}})}),e("#exportByYear").click(function(){var t=e("#from").val();e.ajax({type:"POST",url:BASE_URI+"incomes/export_excel_by_year",data:{fromDate:t},dataType:"json",success:function(t){window.open(BASE_URI+"assets/"+t.filename);var a="Download Excel By Year";e(".btn-process").text(a),e(".btn-process").css("pointer-events","auto")},error:function(e){console.error(e)}})})}(jQuery);