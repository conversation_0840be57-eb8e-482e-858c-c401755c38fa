<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_slip" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $customer->id === $order_slip->customer_id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address <small class="font-italic"></small></label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address', $order_slip->address);?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="contact_number" class="control-label">Contact Number <small class="font-italic"></small></label>

						<input type="text" name="contact_number" class="form-control" value="<?=set_value('contact_number', $order_slip->phone_no);?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('contact_number');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email <small class="font-italic"></small></label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email', $order_slip->email);?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="available_credit" class="control-label">Available Credit <small class="font-italic"></small></label>

						<input type="text" name="available_credit" class="form-control" value="<?=set_value('available_credit', $order_slip->available_credit);?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('available_credit');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="tin_number" class="control-label">Tin Number<small class="font-italic"></small></label>

						<input type="text" name="tin_number" class="form-control" value="<?=set_value('tin_number', $order_slip->tin_number);?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('tin_number');?>
						</div>
					</div>

					<!-- <div class="col-md-4 form-group">
						<label for="outstanding_balance" class="control-label">Outstanding Balance<small class="font-italic"></small></label>

						<input type="text" name="outstanding_balance" class="form-control" value="<?=set_value('outstanding_balance');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('outstanding_balance');?>
						</div>
					</div> -->

					<div class="col-md-4 form-group">
						<label for="order_slip_date" class="control-label">Order Date</label>

						<input type="text" name="order_slip_date" class="form-control datepicker" value="<?=set_value('order_slip_date', DateTime::createFromFormat('Y-m-d', $order_slip->order_slip_date)->format('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('order_slip_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $order_slip->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">Item</th>

									<th class="text-center" style="width: 10%;">Pref. Qty</th>

									<th class="text-center" style="width: 10%;">Unit</th>

									<th class="text-center" style="width: 15%;">Pref. ALW</th>

									<th class="text-center" style="width: 15%;">Current Price</th>

									<th class="text-center" style="width: 20%;">Est. Due Amount</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($order_slip_item_ids)):
                                	$counter = 0;
                                    foreach ($order_slip_item_ids as $i => $order_slip_item_id):
                            ?>
										<tr class="order_slip_item">
											<td>
												<?php $fn_order_slip_item = 'order_slip_item_' . $order_slip_item_id;?>
												<input type="hidden" name=<?=$fn_order_slip_item?> value="<?=set_value($fn_order_slip_item, $order_slip_item_id);?>">

												<?php $fdn_item = 'item_' . $order_slip_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<?php if($retain_items[$counter]):?>
														<option value=<?=$retain_items[$counter]['item_id']?>><?=$retain_items[$counter]['item_name']?></option>
													<?php else:?>
														<option></option>
													<?php endif;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fdn_item);?>
												</div>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $order_slip_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, sprintf("%.2f", $retain_items[$counter]['qty']));?>" step="any">												

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $order_slip_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $retain_items[$counter]['unit']);?>" data-suggest="units" readonly>
											</td>

											<td>
												<?php $fn_pref_alw = 'pref_alw_' . $order_slip_item_id;?>
												<input type="number" name="<?=$fn_pref_alw;?>" class="form-control" value="<?=set_value($fn_pref_alw, sprintf("%.2f", $retain_items[$counter]['pref_alw']));?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_pref_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_current_price = 'current_price_' . $order_slip_item_id;?>
												<input type="number" name="<?=$fn_current_price;?>" class="form-control" value="<?=set_value($fn_current_price, sprintf("%.2f", $retain_items[$counter]['current_price']));?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_current_price);?>
												</div>
											</td>

											<td>
												<?php $fn_est_due_amount = 'est_due_amount_' . $order_slip_item_id;?>
												<div style="display: flex; align-items: center;">
													<input type="text" name="<?=$fn_est_due_amount;?>" class="form-control" value="<?=set_value($fn_est_due_amount, sprintf("%.2f", $retain_items[$counter]['est_due_amount']));?>" step="any" readonly style="text-align: right;">
													<div class="red-text flash-message" style="margin-left: 10px;">
														<?=form_error($fn_est_due_amount);?>
													</div>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('order_slips')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
