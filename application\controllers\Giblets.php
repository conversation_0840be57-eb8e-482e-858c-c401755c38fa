<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Giblets Controller (Simplified)
 */
class Giblets extends MYT_Controller
{
    protected $_error = '';

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->model('giblets');
        $this->load->library('form_validation');
        $this->load->library('session');
        $this->load->helper('url');
        $this->load->helper('form');
    }

    /**
     * List all giblets records
     */
    public function index()
    {
        $this->title = 'Giblets List';
        $data['giblets_list'] = $this->giblets->get_all_giblets();
        $this->build_content('default', 'Giblets/manager', $data);
    }

    /**
     * Add giblets records (multi-row)
     */
    public function add()
    {
        if ($this->input->post()) {
            $skus = $this->input->post('sku');
            $bys = $this->input->post('by');
            $nos = $this->input->post('no');
            $pcs = $this->input->post('pcs');
            $kgs = $this->input->post('kgs');

            $success_count = 0;
            for ($i = 0; $i < count($skus); $i++) {
                if (empty($skus[$i]) && empty($bys[$i])) {
                    continue;
                }
                $data = [
                    'sku' => $skus[$i],
                    'by' => $bys[$i],
                    'no' => $nos[$i],
                    'pcs' => $pcs[$i],
                    'kgs' => $kgs[$i]
                ];
                $this->giblets->insert_giblets($data);
                $success_count++;
            }
            if ($success_count > 0) {
                redirect('Giblets');
            }
        }
        $this->title = 'Add Giblets';
        $this->build_content('default', 'Giblets/add', []);
    }

    /**
     * Edit a single giblets record
     */
    public function edit($id)
    {
        $giblets = $this->giblets->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
            redirect('Giblets');
        }
        $this->title = 'Edit Giblets';
        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Update a single giblets record
     */
    public function update($id)
    {
        $giblets = $this->giblets->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
            redirect('Giblets');
        }
        if ($this->input->post()) {
            $data = [
                'sku' => $this->input->post('sku'),
                'by' => $this->input->post('by'),
                'no' => $this->input->post('no'),
                'pcs' => $this->input->post('pcs'),
                'kgs' => $this->input->post('kgs')
            ];
            $this->giblets->update_giblets($id, $data);
            redirect('Giblets');
        }
        $this->title = 'Edit Giblets';
        $data['giblets'] = $giblets;
        $this->build_content('default', 'Giblets/edit', $data);
    }

    /**
     * Soft delete a giblets record
     */
    public function delete($id)
    {
        $giblets = $this->giblets->get_giblets_by_id($id);
        if (!$giblets) {
            $this->session->set_flashdata('error', 'Giblets not found.');
        } else {
            $this->giblets->soft_delete_giblets($id, $_SESSION['user']->id ?? 1);
            $this->session->set_flashdata('success', 'Giblets deleted.');
        }
        redirect('Giblets');
    }
} 