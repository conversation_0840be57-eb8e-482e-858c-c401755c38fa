<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Revenues extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Payables
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice_item';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_account_name(),
            $this->_get_amount()
        ];

        $joins = <<<EOT
(
    SELECT
        invoice_item.id,
        invoice.invoice_date,
        invoice_item.account_type,
        invoice_item.amount
    FROM invoice_item
    LEFT JOIN invoice ON invoice.id = invoice_item.invoice_id
    WHERE invoice.is_deleted = 0
        AND invoice_item.is_deleted = 0
        AND invoice.status = 'approved'

    UNION ALL

    SELECT
        pos_invoice.id,
        pos_invoice.invoice_date,
        'Revenue - Ice Plant POS' AS account_type,
        pos_invoice.total AS amount
    FROM pos_invoice
    WHERE pos_invoice.is_deleted = 0
) AS invoice_item
EOT;

        $where = <<<EOT
1
EOT;

$from = $this->input->get('from', true);
$to = $this->input->get('to', true);
if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
    isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
) {
    $where .= <<<EOT

AND invoice_item.invoice_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
}

        $group_by = <<<EOT

invoice_item.account_type
EOT;
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'invoice_item.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice_item.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice_item';
            }
        ];
    }

    /**
     * Get Account Name
     */
    protected function _get_account_name()
    {
        return [
            'db' => 'invoice_item.account_type',
            'as' => 'account_name',
            'dt' => 0,
            'field' => 'account_name',
            'formatter' => function ($d, $row) {
                if(empty($d)) {
                   return "No Account Name"; 
                } else {
                    $from = $this->input->get('from', true);
                    $to   = $this->input->get('to', true);
                    return anchor('invoices/invoice_sales?account_name=' . $d.'&from='.$from.'&to='.$to, $d, ' class="btn btn-link link-unstyled" style="display:block;" target="_blank"');
                    // return $d;
                }
            }
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'SUM(invoice_item.amount)',
            'as' => 'amount',
            'dt' => 1,
            'field' => 'amount',
            'formatter' => function ($d, $row) {
                $from = $this->input->get('from', true);
                $to   = $this->input->get('to', true);
                return anchor('invoices/invoice_sales?account_name=' . $row['account_name'].'&from='.$from.'&to='.$to, number_format($d,2), ' class="btn btn-link link-unstyled" style="display:block;text-align:right;" target="_blank"');
                // return number_format($d, 2);
            }
        ];
    }
}
