<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Eod_bc extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch cash bonds
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'eod_bc';
        $primary_key = 'eod_bc.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_doc_no(),
            $this->_get_generated_by(),
            $this->_get_eodbc_date(),
            $this->_get_eodbc_attachments(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
eod_bc
LEFT JOIN user AS generator ON generator.id = eod_bc.added_by
EOT;

        $where = <<<EOT
eod_bc.is_deleted = 0
EOT;

        if(in_array($_SESSION['user']->role_id, [ROLE_BILLING, ROLE_CASHIER, ROLE_OFFICE])){
            $where .= <<<EOT

AND eod_bc.added_by = '{$_SESSION['user']->id}'            
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'eod_bc.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'eod_bc.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'eod_bc';
            }
        ];
    }

    /**
     * Get eod_bc document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'eod_bc.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                // $res = '<a href="../eod/reprint_eodp/' . $row['actions'] . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                return $d;
            }
        ];
    }

    /**
     * Get issuing person
     */
    protected function _get_generated_by()
    {
        return [
            'db' => 'generator.full_name',
            'as' => 'generated_by',
            'dt' => 1,
            'field' => 'generated_by'
        ];
    }

    /**
     * Get eod_bc date
     */
    protected function _get_eodbc_date()
    {
        return [
            'db' => 'DATE_FORMAT(eod_bc.added_on, "%b %d, %Y %H:%i:%s")',
            'as' => 'eod_bc_date',
            'dt' => 2,
            'field' => 'eod_bc_date'
        ];
    }

    /**
     * Get EOD-BC Attachments
     */
    protected function _get_eodbc_attachments()
    {
        $CI = &get_instance();
        return [
            'db' => 'eod_bc.id',
            'as' => 'file',
            'dt' => 3,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('eod_bc_attachment');
                $files = $CI->eod_bc_attachment->get_by_parent_id($d);
                $res = '';

                if(!$files){
                    $res .= '';
                } else {
                    foreach($files as $i => $file){
                        $res .= '• <a href="'.base_url('assets/eod/eod_bc/'.$d.'/'.$file->file_name).'" target="_blank">'.$file->file_name.'</a><br>';
                    }
                }

                return $res;

            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'eod_bc.id',
            'as' => 'actions',
            'dt' => 4,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_BILLING, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])) {
                    $res .= anchor('eod/reprint_eodbc/' . $d, 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                }

                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_BILLING, ROLE_SUPERVISOR, ROLE_OFFICE])) {
                    $res .= '<button class="action-add dropdown-item text-left blue-text" role="button" data-id="' . $d . '">Add Attachment</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
