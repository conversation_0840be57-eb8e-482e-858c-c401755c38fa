<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	title:{
		text: "QOS - Survey Result"
	},
	axisY:{
		title: "Response values",
		interval: 10
	},
	toolTip: {
		shared: true
	},
	data: [{
			type: "bar",
			name: "Avg. Score",
			toolTipContent: "<b>{label}</b> <br> <span style='color:#4F81BC'>{name}</span>: {y}",
			dataPoints: [
				{ y: 94, label: "Order Accuracy" },
				{ y: 74, label: "Packaging" },
				{ y: 80, label: "Quantity" },
				{ y: 88, label: "Quality" },
				{ y: 76, label: "Delivery" }
			]
		},
		{
			type: "error",
			name: "Variability Range",
			toolTipContent: "<span style='color:#C0504E'>{name}</span>: {y[0]} - {y[1]}",
			dataPoints: [
				{ y: [92, 98], label: "Order Accuracy" },
				{ y: [70, 78], label: "Packaging" },
				{ y: [78, 85], label: "Quantity" },
				{ y: [85, 92], label: "Quality" },
				{ y: [72, 78], label: "Delivery" }
			]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>