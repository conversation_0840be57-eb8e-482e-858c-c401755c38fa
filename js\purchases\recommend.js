+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: cols.length - 3
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function recommendForApproval(purchaseId) {
		$.when(recommendPurchase(purchaseId)).then(
			function () {
				window.location.href = BASE_URI + 'purchases/manager?status=for approval'
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function recommendPurchase(purchaseId) {
		return $.ajax({
			url: BASE_URI + 'purchases/mark_for_approval/' + purchaseId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		createDataTable('.tableA');

		$('.action-for-approval-purchase').click(function (event) {
			var purchaseId = event.currentTarget.getAttribute('data-id');

			alertify.confirm(
				'Are you sure you want to recommend Purchase Order No. ' + purchaseId + ' for approval?',
				function () {
					recommendForApproval(purchaseId);
				}
			).setHeader('<em>Recommend Purchase Order for Approval</em>');
		});
	});
}(jQuery);
