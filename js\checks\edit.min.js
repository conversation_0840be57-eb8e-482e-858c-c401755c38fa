!function(e){"use strict";function t(t){var n=e(a());l(n,t);var r=e(".purchase-table"),c=r.find(".check-purchase");0===c.length&&r.find(".no-purchase").remove(),r.find("tbody").append(n)}function a(){var e=n();return'<tr class="check_purchase"><td><input type="hidden" name="check_purchase_'+e+'" value="'+e+'"><select name="purchase_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="amount_'+e+'" class="form-control text-right" min="0" step="any"></td><td class=\'text-center\'><a href="#" target="_blank" name="details_'+e+'">View</a></td><td class="text-right"><button type="button" class="btn btn-sm btn-danger action-delete-purchase">Delete</button></td></tr>'}function n(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function r(){return'<tr class="no-purchase"><td class="text-center" colspan="3">No purchase orders added yet.</td></tr>'}function c(t){return e.ajax({url:BASE_URI+"purchases/get_info/"+t,type:"GET",dataType:"json"})}function o(t,a){var n=parseFloat(a.total);isNaN(n)&&(n=0),t.find('input[name^="amount"]').val(e.fn.addNumericCommas(n.toFixed(2))),t.find('a[name^="details"]').attr("href",BASE_URI+"purchases/preview/"+a.id+"/Print")}function i(){var t=0;e(".purchase-table").find(".check_purchase").each(function(a,n){var r=parseFloat(e.fn.removeNumericCommas(e(n).find('input[name^="amount"]').val()));isNaN(r)&&(r=0),t+=r}),e('input[name="total"]').val(e.fn.addNumericCommas(t.toFixed(2)))}function u(t){e(t.currentTarget).closest(".check_purchase").remove();var a=e(t.delegateTarget),n=a.find(".check_purchase");if(n.length<1){var c=e(r());a.find("tbody").html(c)}i()}function s(t){return e.ajax({url:BASE_URI+"receives/get_w_balance/"+t,type:"GET",dataType:"json"})}function l(t,a){t.find('select[name^="purchase"]').select2({data:a,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function d(){let t=""!==e("select[name='supplier']").children("option:selected").val();e(".action-add-payment").prop("disabled",!t)}function p(t){return e.ajax({url:BASE_URI+"checks/approve/"+t,type:"POST",dataType:"json"})}d(),e(function(){function a(){let t=e("select[name='supplier']").children("option:selected").val();t&&e.when(s(t)).then(function(t){n=[{id:"",text:""}],t.purchases.forEach(function(t){var a=parseFloat(t.total);isNaN(a)&&(a=0),t.text="Purchase Order #"+t.id+" - Php"+e.fn.addNumericCommas(a.toFixed(2)),n.push(t)})})}var n=[{id:"",text:""}];a(),e("select[name='supplier']").change(function(t){a(),e(".purchase-table").find("tbody").html(e(r())),d()}),e(".action-add-payment").click(function(){t(n)});var l=null;e(".purchase-table").on("change",'select[name^="purchase"]',function(t){l=e(t.currentTarget).closest(".check_purchase"),e.when(c(t.currentTarget.value)).then(function(e){o(l,e.data),i()})}).on("click",".action-delete-purchase",function(e){u(e)}).on("change",'input[name^="amount"]',i),e(".purchase-table").on("change",'input[name^="amount"]',i).trigger("change"),e(".action-approve").on("click",function(e){var t=e.currentTarget.getAttribute("data-checkno");alertify.confirm("Are you sure you want to approve Check No "+t+"?",function(){var t=e.currentTarget.getAttribute("data-id");p(t),window.location.href=BASE_URI+"checks/manager"}).setHeader("<em>Approve Check</em>")}),i()})}(jQuery);