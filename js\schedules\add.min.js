!function(t){"use strict";function e(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function a(){var t=e();return`\n\t\t\t<tr class="trip_item">\n\t\t\t\t<td>\n\t\t\t\t\t<input type="hidden" name="trip_item_${t}" value="${t}">\n\t\t\t\t\t<input type="number" name="trip_no_${t}" class="form-control-plaintext" value="" readonly>\n\t\t\t\t</td>\n\t\t\t\t<td>\n\t\t\t\t\t<input type="time" name="eta_time_${t}" class="form-control" value="">\n\t\t\t\t</td>\n\t\t\t\t<td class="text-center">\n\t\t\t\t\t<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>\n\t\t\t\t</td>\n\t\t\t</tr>`}function i(){return'<tr class="no-item">\n\t\t\t<td class="text-center" colspan="3">No trips added yet.</td>\n\t\t</tr>'}function n(){var e=t(a()),i=t(".item-table"),n=i.find(".trip_item");0===n.length&&i.find(".no-item").remove(),i.find("tbody").append(e),r()}function r(){var e=t('select[name="customer"]').val(),a=t('input[name="eta_date"]').val();if(e&&a)t.ajax({url:BASE_URI+"schedules/get_next_trip_number",type:"POST",data:{customer_id:e,eta_date:a},dataType:"json",success:function(e){if(e.next_trip_no){var a=t(".item-table"),i=a.find(".trip_item"),n=e.next_trip_no;i.each(function(e,a){t(a).find('input[name^="trip_no_"]').val(n+e)})}},error:function(){var e=t(".item-table"),a=e.find(".trip_item");a.each(function(e,a){t(a).find('input[name^="trip_no_"]').val(e+1)})}});else{var i=t(".item-table"),n=i.find(".trip_item");n.each(function(e,a){t(a).find('input[name^="trip_no_"]').val(e+1)})}}function o(e){t(e.currentTarget).closest(".trip_item").remove();var a=t(e.delegateTarget),n=a.find(".trip_item");n.length<1&&a.find("tbody").html(t(i())),r()}function s(){var e=[],a=!1;t('.item-table input[name^="eta_time_"]').each(function(){t(this).val()&&e.push(t(this).val())});var i=[...new Set(e)];if(i.length!==e.length&&(a=!0),t(".duplicate-time-error").remove(),t('.item-table input[name^="eta_time_"]').removeClass("is-invalid"),a){var n=t('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">\n\t\t\t\tDuplicate ETA times are not allowed. Each trip must have a unique time.\n\t\t\t</div>');t(".action-add-item").closest(".form-group").append(n);var r={};e.forEach(t=>r[t]=(r[t]||0)+1),t('.item-table input[name^="eta_time_"]').each(function(){r[t(this).val()]>1&&t(this).addClass("is-invalid")})}}function l(){var e=t('select[name="customer"]').val(),a=t('input[name="eta_date"]').val();e&&a?t.ajax({url:BASE_URI+"schedules/check_duplicate_schedule",type:"POST",data:{customer_id:e,eta_date:a},dataType:"json",success:function(e){if(t(".duplicate-schedule-error").remove(),e.is_duplicate){var a=t('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');t('input[name="eta_date"]').closest(".form-group").append(a),t('input[name="eta_date"]').addClass("is-invalid")}else t('input[name="eta_date"]').removeClass("is-invalid")}}):(t(".duplicate-schedule-error").remove(),t('input[name="eta_date"]').removeClass("is-invalid"))}t(function(){n();var e=(new Date).toISOString().split("T")[0],a=t('input[name="eta_date"]');a.attr("min",e),a.on("input change blur",function(){t(this).val()&&t(this).val()<e&&t(this).val(e)}),a.on("focus click",function(){t(this).attr("min",e)}),t(".action-add-item").click(n),t(".item-table").on("click",".action-delete-item",o),t('select[name="customer"], input[name="eta_date"]').on("change",function(){r(),l()}),t(".item-table").on("change input",'input[name^="eta_time_"]',s),t("form.crud-box").on("submit",function(e){var a=[],i=!1,n=!1;if(t('.item-table input[name^="eta_time_"]').each(function(){if(!t(this).val())return i=!0,!1;a.push(t(this).val())}),i||new Set(a).size===a.length||(n=!0),t(".eta-time-error, .duplicate-time-error").remove(),t('.item-table input[name^="eta_time_"]').removeClass("is-invalid"),i){var r=t('<div class="eta-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">ETA Time is required for all trips.</div>');t(".action-add-item").closest(".form-group").append(r),e.preventDefault()}if(n){r=t('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');t(".action-add-item").closest(".form-group").append(r);let i={};a.forEach(t=>i[t]=(i[t]||0)+1),t('.item-table input[name^="eta_time_"]').each(function(){i[t(this).val()]>1&&t(this).addClass("is-invalid")}),e.preventDefault()}var o=t('select[name="customer"]').val(),s=t('input[name="eta_date"]').val();if(o&&s){var l=!1;t.ajax({url:BASE_URI+"schedules/check_duplicate_schedule",type:"POST",data:{customer_id:o,eta_date:s},dataType:"json",async:!1,success:function(e){if(e.is_duplicate){l=!0,t(".duplicate-schedule-error").remove();var a=t('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');t('input[name="eta_date"]').closest(".form-group").append(a)}}}),l&&e.preventDefault()}})})}(jQuery);