	<div class="col-md-12 sect-jr">
		<div class="receipt" data-tag="<?=$invoice->id;?>">
			<div class="receipt-header">
				<div class="clearfix">
					<strong class="pull pull-right">Invoice</strong>
				</div>

				<h4><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></h4>

				<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

				<div class="row sect-jr">
					<div class="col-sm-6">
						<p class="text-left">Name : <?=strtoupper($customer->name);?></p>
						<p class="text-left">Address :  <?=$customer->address;?></p>
						<p class="text-left">TIN : <?=$customer->tin_number;?> </p>
						<p class="text-left">Business Style : <?=$customer->business_style;?> </p>
					</div>

					<div class="col-sm-2 offset-sm-4">
						<p class="text-left">Doc No :  <?=$invoice->invoice_no;?></p>
						<p class="text-left">Date :  <?=date('n/j/Y', strtotime($invoice->invoice_date));?></p>
						<p class="text-left">Due Date :  <?=$invoice->due_date ? date('n/j/Y', strtotime($invoice->due_date)): 'Due upon receipt';?></p>
					</div>
				</div>
			</div>

			<div class="clearfix"></div>

			<table class="receipt-body">
				<thead>
					<tr>
						<th width="17%">Qty</th>
						<th width="17%">Description</th>
						<th width="17%">Account Name</th>
						<th width="7%">RS/SO</th>
						<th width="7%">ALW</th>
						<th width="17%" class="text-right">Unit Price</th>
						<th width="17%" class="text-right">Price</th>
					</tr>
				</thead>

				<tbody>
				<?php foreach ($invoice_items as $invoice_item): ?>
					<tr class="item">
						<td><?=floatval($invoice_item->qty) . ' ' . $invoice_item->unit?></td>
						<td><?=$invoice_item->item;?></td>
						<td><?=$invoice_item->account_type;?></td>
						<td><?=$invoice_item->rs;?></td>
						<td><?=$invoice_item->alw;?></td>
						<td class="text-right"><?=number_format($invoice_item->price, 2);?></td>
						<td class="text-right"><?=number_format($invoice_item->qty * $invoice_item->price, 2);?></td>
					</tr>
				<?php endforeach;?>
				</tbody>

				<tfoot>
					<tr class="item">
						<td colspan="3">
						<?php if (trim($invoice->remarks) !== ''): ?>
							<div class="row">
								<div class="col-md-12">
									<p class="text-justify"><strong>Remarks: </strong><?=nl2br($invoice->remarks);?></p>
								</div>
							</div>
						<?php endif;?>
						</td>

						<td colspan="3">
							<table width="100%">
								<tr>
									<td width="50%" class="text-right"><strong>Sub Total</strong></td>
									<td width="50%" class="text-right"><?=number_format($invoice->subtotal, 2);?></td>
								</tr>

							<?php if ($invoice->discount > 0): ?>
								<tr>
									<td class="text-right"><strong>(Discount)</strong></td>
									<td class="text-right">-<?=number_format($invoice->discount, 2);?></td>
								</tr>
							<?php endif;?>
								<tr>
									<td class="text-right"><strong>Grand Total</strong></td>
									<td class="text-right"><?=number_format($invoice->total, 2);?></td>
								</tr>
							</table>
						</td>
					</tr>
				</tfoot>
			</table>

			<div class="row">
				<div class="col-sm-3 text-center" style="margin-top: 78px;">
					<?=$invoice->issuer;?>
					<hr class="signature-line" style="margin: 5px">
					<p>Issued by</p>
				</div>

				<div class="col-sm-3 offset-sm-6 text-center" style="margin-top: 78px">
					<?=$invoice->approver;?>
					<hr class="signature-line" style="margin: 5px">
					<!-- <p>Signature above printed name</p> -->
					Approved by
				</div>
			</div>
		</div>

		<div class="clearfix text-center">
			<!-- <a class="btn btn-success" href="<?=site_url('invoices/edit/' . $invoice->id);?>">
				<i class="fa fa-pencil"></i> Edit
			</a> -->
			
			<!-- <?php if ($invoice->is_reviewed == 0): ?>
				<button class="btn btn-warning action-review" data-invoiceid="<?=$invoice->id;?>">
					<i class="fa fa-pencil"></i> Confirm Review
				</button>
			<?php endif;?> -->

			<?php if($invoice->status === 'approved' && $invoice->is_deleted === '0'):?>
				<!-- <a class="btn btn-primary" href="<?=site_url('invoices/make_pdf/' . $invoice->id);?>">
					<i class="fa fa-save"></i> Export as PDF
				</a> -->

				<button type="button" class="btn btn-primary action-print">
					<i class="fa fa-print"></i> Print
				</button>
			<?php elseif($invoice->is_deleted === '0' && $invoice->status === 'approved'):?>
				<button type="button" class="btn btn-primary action-approve" data-id="<?=$invoice->id?>">
					<i class="fa fa-"></i> Approve to Print
				</button>
			<?php endif;?>

			<a class="btn btn-light" href="<?=site_url('invoices/manager');?>">Close</a>
		</div>
	</div>