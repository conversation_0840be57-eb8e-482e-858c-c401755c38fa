!function(n){"use strict";function r(e,t){n.when(n.ajax({url:BASE_URI+"live_sell_account_types/delete/"+e,type:"POST",dataType:"json"})).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);e=JSON.parse(e.responseText);alertify.error(e.message)})}n(function(){(e=n(e=".tableA")).find("thead th");var e,a=e.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-left",targets:[0,1,2,3,4,5]},{className:"dt-action",targets:5}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+e.attr("data-package")});n(".tableA").on("click",".action-delete",function(e){var t=n(e.currentTarget).closest("tr.live_sell_account_type").find("td"),t=void 0!==t[1]?t[1].innerHTML:"";alertify.confirm("Are you sure you want to delete "+t+"?",function(){r(e.currentTarget.getAttribute("data-id"),a)}).setHeader("<em>Delete Account Type</em>")})})}(jQuery);