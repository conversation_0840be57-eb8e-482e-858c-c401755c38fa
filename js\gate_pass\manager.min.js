!function(e){"use strict";function a(a){var t=e(a),r=t.find("thead th"),n=t.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:r.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+t.attr("data-package")});return n}function t(a,t){e.when(r(a)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var a=JSON.parse(e.responseText);alertify.error(a.message)})}function r(a){return e.ajax({url:BASE_URI+"gate_pass/delete/"+a,type:"POST",dataType:"json"})}function n(){var a=e(".approvedCreditLine").val(),t=e(".currentArBalance").val();let r=a-t;e('input[name="avCreditLine"]').val(r.toFixed(2))}function i(a,t,r){return new Promise((i,l)=>{var s='<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Approved Credit Line : </div>';s+='<div class="col-md-6 form-group" style="margin: 0;"><input type="number" class="approvedCreditLine" style="margin: 0;" name="approvedCreditLine" value="'+t+'" readonly></div></div> <br><br>',s+='<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Current AR Balance : </div>',s+='<div class="col-md-6 form-group" style="margin: 0;"><input type="number" class="currentArBalance" style="margin: 0;" name="currentArBalance" value="'+r+'" readonly></div></div> <br><br>',s+='<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>Available Credit Line : </div>',s+='<div class="col-md-6 form-group" style="margin: 0;"><input type="number" style="margin: 0;" name="avCreditLine" value="" readonly></div></div> <br><br>',s+='<div class="row" style="margin: 0;"><div class="col-md-6 form-group" style="margin: 0;"><label>CEC Officer : </div>',s+='<div class="col-md-6 form-group" style="margin: 0;"><input type="text" style="margin: 0;" class="form-control" name="officer" value="'+a+'" readonly></div></div>',alertify.confirm(s,function(a,t){var r=e('input[name="approvedCreditLine"]').val(),n=e('input[name="currentArBalance"]').val(),l=e('input[name="avCreditLine"]').val();i({credit_line:r,current_balance:n,available_credit:l})},function(){l("Canceled")}).setting({reverseButtons:!0,closable:!1,title:"Audit POC"}).show(),e(document).on("input",".approvedCreditLine, .currentArBalance",n)})}function l(a,t,r,n){var i=new FormData;return i.append("gatePass_id",a),i.append("credit_line",t),i.append("current_balance",r),i.append("available_credit",n),e.ajax({url:BASE_URI+"gate_pass/audit/",type:"POST",data:i,processData:!1,contentType:!1,dataType:"json"}).then(e=>({success:!0,message:"POC Audited."}))}function s(a){return e.ajax({url:BASE_URI+"customers/get_details/"+a,type:"GET",dataType:"json"})}e(function(){var r=a(".tableA"),c=0,o=0;e(".tableA").on("click",".action-audit",function(a){var t=a.currentTarget.getAttribute("data-id"),r=a.currentTarget.getAttribute("user-name"),d=a.currentTarget.getAttribute("customer-id");e.when(s(d)).then(function(e){c=parseFloat(e.customer.credit_limit),o=parseFloat(e.customer.total_receivable),isNaN(c)&&(c=0),isNaN(o)&&(o=0),i(r,c,o).then(({credit_line:e,current_balance:a,available_credit:r})=>l(t,e,a,r)).then(e=>{alertify.success(e.message),window.location.reload()}).catch(e=>{alertify.error(e)}),n()})}).on("click",".action-delete",function(a){var n=e(a.currentTarget).closest("tr.gatePass").find("td"),i=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to delete POC No. "+i+"?",function(){var e=a.currentTarget.getAttribute("data-id");t(e,r)}).setHeader("<em>Delete POC</em>")}),e(".tab-link").click(function(a){e(".tab-link.active").removeClass("active"),a.currentTarget.classList.add("active");var t=BASE_URI+"fetch/gatePass?status="+a.currentTarget.getAttribute("data-status");r.ajax.url(t).load()})})}(jQuery);