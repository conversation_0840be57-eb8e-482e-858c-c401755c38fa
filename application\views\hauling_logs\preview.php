<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">
				<div class="row">
					<div class="col-md-12">
						<p><strong>Date:</strong> <?= $formatted_date; ?></p>
					</div>
				</div>
			</div>

		</div>

		<div class="receipt-body">
			<div class="row">
				<div class="col-12">
					<table class="table table-bordered table-sm preview-table">
						<thead>
							<tr>
								<th>Grower Farm Name/Place</th>
								<th>Trip No.</th>
								<th>WAF No./RS No.</th>
								<th>Truck Name</th>
								<th>Truck Plates#</th>
								<th>ETA</th>
								<th>ATA Time</th>
								<th>Weighed Time</th>
								<th>Remarks on Arrival</th>
								<th>Gross WG in Ticket</th>
								<th>Tare WG in Ticket</th>
								<th>Net weight in Ticket</th>
							</tr>
						</thead>
						<tbody>
							<?php if (!empty($hauling_logs)): ?>
								<?php foreach ($hauling_logs as $log): ?>
									<tr>
										<td><?= htmlspecialchars($log->grower_farm ?: '') ?></td>
										<td><?= htmlspecialchars($log->trip_no ?: '') ?></td>
										<td><?= htmlspecialchars($log->waf_rs_no ?: '') ?></td>
										<td><?= htmlspecialchars($log->truck_name) ?></td>
										<td><?= htmlspecialchars($log->truck_plate) ?></td>
										<td>
											<?php
											if (!empty($log->eta)) {
												echo date('F j, Y g:i A', strtotime($log->eta));
											}
											?>
										</td>
										<td>
											<?php
											if (!empty($log->ata_time)) {
												echo date('F j, Y g:i A', strtotime($log->ata_time));
											}
											?>
										</td>
										<td>
											<?php
											if (!empty($log->weighed_time)) {
												echo date('F j, Y g:i A', strtotime($log->weighed_time));
											}
											?>
										</td>
										<td><?= htmlspecialchars($log->remarks ?: '') ?></td>
										<td class="text-right"><?= number_format($log->gross_weight, 2) ?></td>
										<td class="text-right"><?= number_format($log->tare_weight, 2) ?></td>
										<td class="text-right"><?= number_format($log->net_weight, 2) ?></td>
									</tr>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="12" class="text-center">No hauling logs found for this date.</td>
								</tr>
							<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Signature and Additional Remarks Section -->
			<div class="row mt-5">
				<div class="col-6 text-center" style="margin-top: 30px;">
					<p><strong>Prepared by:</strong></p>
					<br><br>
					<hr class="signature-line" style="margin: 5px auto; width: 70%;">
					<p style="font-size: 11px;">GLYDS</p>
					<p style="font-size: 11px;">LAVC Truckscale Officer</p>
				</div>
				<div class="col-6 text-right" style="margin-top: 30px;">
					<p><strong>Additional Remarks:</strong>
					<span style="display: inline-block; width: 200px; border-bottom: 1px solid #333; margin-left: 10px;"></span></p>
				</div>
			</div>
		</div>

		<!-- Print button below the table -->
		<div class="text-center mt-3 no-print">
			<button type="button" class="btn btn-primary action-print"><i class="fa fa-print"></i> Print</button>
			<a class="btn btn-light" href="<?=site_url('hauling_logs');?>">Close</a>
		</div>


	</div>
</div>
