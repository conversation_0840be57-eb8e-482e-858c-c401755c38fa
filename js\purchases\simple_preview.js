+(function ($) {
	"use strict";

	function printPurchase(purchaseId) {
		$.when(markPrinted(purchaseId)).then(
			function () {
				window.print();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approve(purchaseId, status) {
		$.when(approvePurchase(purchaseId, status)).then(function (response) {
			window.location.href = BASE_URI + "purchases/manager?status=for release";
		});
	}

	function approvePurchase(purchaseId, status) {
		return $.ajax({
			url: BASE_URI + "purchases/approve_status/",
			type: "POST",
			dataType: "json",
			data: { purchase_id: purchaseId, status: status },
		});
	}

	$(function () {
		$("button.action-approve").click(function (event) {
			var status = event.currentTarget.getAttribute("data-status");

			alertify
				.confirm(
					"Are you sure you want to approve this Purchase Order for " +
						status +
						"?",
					function () {
						var purchaseId = event.currentTarget.getAttribute("data-id");
						approve(purchaseId, status);
					}
				)
				.setHeader("<em>Approve Purchase Order for " + status + "</em>");
		});
	});
})(jQuery);
