+(function ($) {
	"use strict";

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find("thead th");

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: "full_numbers",
			order: [[0, "desc"]],
			columnDefs: [
				{
					className: "text-right",
					targets: cols.length - 3,
				},
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + "fetch/" + $table.attr("data-package"),
		});

		return dataTable;
	}

	function approveAndReload(purchaseOrderItemID, dataTable) {
		$.when(approveRow(purchaseOrderItemID)).then(
			function () {
				var $button = $(
					'button.action-approve-item[data-id="' + purchaseOrderItemID + '"]'
				);
				$button.closest("td").html("");
				location.reload();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approveRow(purchaseOrderItemID) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/approve_item/" + purchaseOrderItemID,
			type: "POST",
			dataType: "json",
		});
	}

	function disapproveAndReload(purchaseOrderItemID, dataTable) {
		$.when(disapproveRow(purchaseOrderItemID)).then(
			function () {
				var $button = $(
					'button.action-approve-item[data-id="' + purchaseOrderItemID + '"]'
				);
				$button.closest("tr.purchase_order_item").remove();
				location.reload();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function disapproveRow(purchaseOrderItemID) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/disapprove_item/" + purchaseOrderItemID,
			type: "POST",
			dataType: "json",
		});
	}

    function pendingAndReload(purchaseOrderItemID, dataTable) {
		$.when(pendingRow(purchaseOrderItemID)).then(
			function () {
				location.reload();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(purchaseOrderItemID) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/pending_item/" + purchaseOrderItemID,
			type: "POST",
			dataType: "json",
		});
	}

	function saveToPrint(purchaseOrderId) {
		$.when(approvePurchase(purchaseOrderId)).then(
			function () {
				window.location.href = BASE_URI + "purchase_orders/manager/";
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approvePurchase(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/mark_approved/" + purchaseOrderId,
			type: "POST",
			dataType: "json",
		});
	}

	function forVerification(purchaseOrderId) {
		$.when(verifyPurchase(purchaseOrderId)).then(
			function () {
				window.location.href = BASE_URI + "purchase_orders/manager/";
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function verifyPurchase(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/mark_for_approval/" + purchaseOrderId,
			type: "POST",
			dataType: "json",
		});
	}

	$(function () {
		var dataTable = createDataTable(".tableA");

		$(".item-table")
			.on("click", ".action-approve-item", function (event) {
				var cols = $(event.currentTarget)
					.closest("tr.purchase_order_item")
					.find("td");
				var name = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to approve " + name + "?",
						function () {
							var purchaseOrderItemID = event.currentTarget.getAttribute("data-id");
							approveAndReload(purchaseOrderItemID, dataTable);
						}
					)
					.setHeader("<em>Approve Purchase Order Item</em>");
			})
			.on("click", ".action-disapprove-item", function (event) {
				var cols = $(event.currentTarget)
					.closest("tr.purchase_order_item")
					.find("td");
				var name = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to disapprove " + name + "?",
						function () {
							var purchaseOrderItemID = event.currentTarget.getAttribute("data-id");
							disapproveAndReload(purchaseOrderItemID, dataTable);
						}
					)
					.setHeader("<em>Disapprove Purchase Order Item</em>");
			})
			.on("click", ".action-pending-item", function (event) {
				var cols = $(event.currentTarget)
					.closest("tr.purchase_order_item")
					.find("td");
				var name = typeof cols[0] !== "undefined" ? cols[0].innerHTML : "";

				alertify
					.confirm(
						"Are you sure you want to return to pending " + name + "?",
						function () {
							var purchaseOrderItemID = event.currentTarget.getAttribute("data-id");
							pendingAndReload(purchaseOrderItemID, dataTable);
						}
					)
					.setHeader("<em>Pending Purchase Order Item</em>");
			});

		$(".action-approve-purchase_order").click(function (event) {
			var purchaseOrderId = event.currentTarget.getAttribute("data-id");

			alertify
				.confirm(
					"Are you sure you want to approve to print Purchase Order No. " +
						purchaseOrderId +
						"?",
					function () {
						saveToPrint(purchaseOrderId);
					}
				)
				.setHeader("<em>Approve to Print Purchase Order</em>");
		});

		$(".action-forward-verification").click(function (event) {
			var purchaseOrderId = event.currentTarget.getAttribute("data-id");

			alertify
				.confirm(
					"Are you sure you want to forward for verification Purchase Order No. " +
						purchaseOrderId +
						"?",
					function () {
						forVerification(purchaseOrderId);
					}
				)
				.setHeader("<em>Forward for Verfication Purchase Order</em>");
		});
	});
})(jQuery);
