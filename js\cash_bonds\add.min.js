!function(e){"use strict";function t(t){var a=e(n());o(a,t);var i=e(".item-table"),s=i.find(".cash_bond_item");0===s.length&&i.find(".no-item").remove(),i.find("tbody").append(a),r()}function n(){var e=a();return'<tr class="cash_bond_item"><td><input type="hidden" name="cash_bond_item_'+e+'" value="'+e+'"><input type="number" name="row_'+e+'" class="form-control-plaintext" value="" readonly></td><td><select name="invoice_no_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="total_heads_no_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="unit_price_'+e+'" class="form-control" value="2.00" min="0" step="any"></td><td><input type="number" name="total_cb_'+e+'" class="form-control" value="" min="0" step="any" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function a(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function o(t,n){t.find('select[name^="invoice_no"]').select2({data:n,placeholder:e(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function i(){return'<tr class="no-item"><td class="text-center" colspan="9">No items added yet.</td></tr>'}function r(){var t=e(".item-table"),n=t.find(".cash_bond_item");n.each(function(t,n){e(n).find('input[name^="row"]').val(t+1)})}function s(e){var t=parseFloat(e.find('input[name^="total_heads_no"]').val());isNaN(t)&&(t=0);var n=parseFloat(e.find('input[name^="unit_price"]').val());isNaN(n)&&(n=0);var a=n*t;return a<0&&(a=0),e.find('input[name^="total_cb"]').val(a>0?a.toFixed(2):""),a}function d(){var t=0;e(".item-table").find(".cash_bond_item").each(function(n,a){t+=s(e(a))}),e('input[name="grand_total"]').val(t.toFixed(4))}function c(t){e(t.currentTarget).closest(".cash_bond_item").remove();var n=e(t.delegateTarget),a=n.find(".cash_bond_item");if(a.length<1){var o=e(i());n.find("tbody").html(o)}r(),d()}function l(t){return e.ajax({url:BASE_URI+"customers/info/"+t,type:"GET",dataType:"json"})}function m(t){return e.ajax({url:BASE_URI+"customers/info_by_name/"+encodeURIComponent(t),type:"GET",dataType:"json"})}function u(t){return e.ajax({url:BASE_URI+"invoices/all_infos/"+t,type:"GET",dataType:"json"})}function h(t){return e.ajax({url:BASE_URI+"cash_bonds/delete_attachment/"+t,type:"POST",dataType:"json"})}e(function(){var n=[{id:"",text:""}];""!==e('select[name="customer"]').val()?(e("#btnSubmit").prop("disabled",!1),e(".cash_bond-history").removeClass("d-none")):(e("#btnSubmit").prop("disabled",!0),e(".cash_bond-history").addClass("d-none"),e(".item-table").find("tbody").html(e(i()))),"0"===e('select[name="customer"] option:selected').attr("data-walkin")&&"16"===e('select[name="customer"]').val()?(e(".credit-terms").addClass("d-none"),e(".walkin-customer").removeClass("d-none")):(e(".credit-terms").removeClass("d-none"),e(".walkin-customer").addClass("d-none")),e('select[name="customer"]').on("change",function(t){var a=e(this).val();n=[{id:"",text:""}],e('input[name="address"]').val(""),e('input[name="phone_no"]').val(""),e('input[name="email"]').val(""),e('input[name="available_credit"]').val(""),e(".history").attr("href","#"),e.when(l(a)).then(function(t){var n=t.customer.available_credit;(isNaN(n)||null===n)&&(n=0),e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e('input[name="available_credit"]').val(e.fn.addNumericCommas(n)),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"cash_bonds/history/"+t.customer.id)});var o=e("#btnSubmit");""!==a?(o.prop("disabled",!1),e(".cash_bond-history").removeClass("d-none"),e(".item-table").find("tbody").html(e(i()))):(o.prop("disabled",!0),e(".cash_bond-history").addClass("d-none")),e.when(u(a)).then(function(e){e.invoices.forEach(function(e){e.text=e.invoice_no,n.push(e)})})}),e('input[name="walkin_customer"]').on("change",function(t){var n=e(this).val();e.when(m(n)).then(function(t){e('input[name="address"]').val(t.customer.address),e('input[name="phone_no"]').val(t.customer.phone_no),e('input[name="email"]').val(t.customer.email),e(".history").removeClass("d-none"),e(".history").attr("href",BASE_URI+"cash_bonds/history/"+t.customer.id)},function(t){e(".history").addClass("d-none")});var a=e("#btnSubmit");""!==n?(a.prop("disabled",!1),e(".item-table").find("tbody").html(e(i()))):a.prop("disabled",!0)}),e('input[name="terms"]').on("change",function(t){var n=e('input[name="terms"]:checked').val();"check"===n?(e(".check-terms").removeClass("d-none"),e(".non-credit-terms").removeClass("d-none")):"credit"===n?(e(".non-credit-terms").addClass("d-none"),e(".check-terms").addClass("d-none")):(e(".check-terms").addClass("d-none"),e(".non-credit-terms").removeClass("d-none"))}).trigger("change"),e(".action-add-item").click(function(){t(n)}),e(".item-table").on("change",'select[name^="invoice_no"]',function(t){var n=e(t.delegateTarget).find('select[name^="invoice_no"] option[value="'+t.currentTarget.value+'"]:selected');n.length>1&&(alert("Invoice No already selected."),e(t.currentTarget).val("").trigger("change"),stop)}).on("click",".action-delete-item",function(e){c(e)}).on("input",'input[name^="total_heads_no"]',d).on("input",'input[name^="unit_price_"]',d),e('input[name="discount"]').change(d),d(),e(".attachment-table").on("click",".action-delete-attachment",function(t){var n=e(t.currentTarget).closest("tr.attachment").find("td"),a=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+a+"?",function(){var e=t.currentTarget.getAttribute("data-id");h(e),location.reload()}).setHeader("<em>Delete Attachment</em>")})})}(jQuery);