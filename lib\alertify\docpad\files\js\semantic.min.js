﻿!function(n,t,i,r){n.fn.checkbox=function(t){var i,e=n(this),o=e.selector||"",f=(new Date).getTime(),u=[],s=arguments[0],h="string"==typeof s,c=[].slice.call(arguments,1);return e.each(function(){var e,l=n.extend(!0,{},n.fn.checkbox.settings,t),g=l.className,d=l.namespace,y=(l.error,"."+d),p="module-"+d,v=n(this),nt=n(this).next(l.selector.label).first(),a=n(this).find(l.selector.input),b=v.selector||"",w=v.data(p),k=this;e={initialize:function(){e.verbose("Initializing checkbox",l);l.context&&""!==b?(e.verbose("Adding delegated events"),n(k,l.context).on(b,"click"+y,e.toggle).on(b+" + "+l.selector.label,"click"+y,e.toggle)):(v.on("click"+y,e.toggle).data(p,e),nt.on("click"+y,e.toggle));e.instantiate()},instantiate:function(){e.verbose("Storing instance of module",e);w=e;v.data(p,e)},destroy:function(){e.verbose("Destroying previous module");v.off(y).removeData(p)},is:{radio:function(){return v.hasClass(g.radio)},enabled:function(){return a.prop("checked")!==r&&a.prop("checked")},disabled:function(){return!e.is.enabled()}},can:{disable:function(){return"boolean"==typeof l.required?l.required:!e.is.radio()}},enable:function(){e.debug("Enabling checkbox",a);a.prop("checked",!0).trigger("change");n.proxy(l.onChange,a.get())();n.proxy(l.onEnable,a.get())()},disable:function(){e.debug("Disabling checkbox");a.prop("checked",!1).trigger("change");n.proxy(l.onChange,a.get())();n.proxy(l.onDisable,a.get())()},toggle:function(){e.verbose("Determining new checkbox state");a.prop("disabled")||(e.is.disabled()?e.enable():e.is.enabled()&&e.can.disable()&&e.disable())},setting:function(t,i){if(n.isPlainObject(t))n.extend(!0,l,t);else{if(i===r)return l[t];l[t]=i}},internal:function(t,i){if(n.isPlainObject(t))n.extend(!0,e,t);else{if(i===r)return e[t];e[t]=i}},debug:function(){l.debug&&(l.performance?e.performance.log(arguments):(e.debug=Function.prototype.bind.call(console.info,console,l.name+":"),e.debug.apply(console,arguments)))},verbose:function(){l.verbose&&l.debug&&(l.performance?e.performance.log(arguments):(e.verbose=Function.prototype.bind.call(console.info,console,l.name+":"),e.verbose.apply(console,arguments)))},error:function(){e.error=Function.prototype.bind.call(console.error,console,l.name+":");e.error.apply(console,arguments)},performance:{log:function(n){var t,i,r;l.performance&&(t=(new Date).getTime(),r=f||t,i=t-r,f=t,u.push({Element:k,Name:n[0],Arguments:[].slice.call(n,1)||"","Execution Time":i}));clearTimeout(e.performance.timer);e.performance.timer=setTimeout(e.performance.display,100)},display:function(){var t=l.name+":",i=0;f=!1;clearTimeout(e.performance.timer);n.each(u,function(n,t){i+=t["Execution Time"]});t+=" "+i+"ms";o&&(t+=" '"+o+"'");(console.group!==r||console.table!==r)&&u.length>0&&(console.groupCollapsed(t),console.table?console.table(u):n.each(u,function(n,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd());u=[]}},invoke:function(t,u,f){var h,o,s,e=w;return u=u||c,f=k||f,"string"==typeof t&&e!==r&&(t=t.split(/[\. ]/),h=t.length-1,n.each(t,function(i,u){var f=i!=h?u+t[i+1].charAt(0).toUpperCase()+t[i+1].slice(1):t;if(n.isPlainObject(e[f])&&i!=h)e=e[f];else{if(e[f]!==r)return o=e[f],!1;if(!n.isPlainObject(e[u])||i==h)return e[u]!==r?(o=e[u],!1):!1;e=e[u]}})),n.isFunction(o)?s=o.apply(f,u):o!==r&&(s=o),n.isArray(i)?i.push(s):i!==r?i=[i,s]:s!==r&&(i=s),o}};h?(w===r&&e.initialize(),e.invoke(s)):(w!==r&&e.destroy(),e.initialize())}),i!==r?i:this};n.fn.checkbox.settings={name:"Checkbox",namespace:"checkbox",debug:!1,verbose:!0,performance:!0,context:!1,required:"auto",onChange:function(){},onEnable:function(){},onDisable:function(){},error:{method:"The method you called is not defined."},selector:{input:"input[type=checkbox], input[type=radio]",label:"label"},className:{radio:"radio"}}}(jQuery,window,document);!function(n,t,i,r){n.fn.dropdown=function(t){var u,s=n(this),e=n(i),c=s.selector||"",o="ontouchstart"in i.documentElement,h=(new Date).getTime(),f=[],l=arguments[0],a="string"==typeof l,v=[].slice.call(arguments,1);return s.each(function(){var i,y=n.isPlainObject(t)?n.extend(!0,{},n.fn.dropdown.settings,t):n.extend({},n.fn.dropdown.settings),k=y.className,b=y.metadata,ot=y.namespace,g=y.selector,ut=y.error,w="."+ot,et="module-"+ot,p=n(this),d=p.find(g.item),tt=p.find(g.text),it=p.find(g.input),nt=p.children(g.menu),rt=this,ft=p.data(et);i={initialize:function(){i.debug("Initializing dropdown",y);i.save.defaults();i.set.selected();o&&i.bind.touchEvents();i.bind.mouseEvents();i.bind.keyboardEvents();i.instantiate()},instantiate:function(){i.verbose("Storing instance of dropdown",i);ft=i;p.data(et,i)},destroy:function(){i.verbose("Destroying previous dropdown for",p);d.off(w);p.off(w).removeData(et)},bind:{keyboardEvents:function(){i.debug("Binding keyboard events");p.on("keydown"+w,i.handleKeyboard);p.on("focus"+w,i.show)},touchEvents:function(){i.debug("Touch device detected binding touch events");p.on("touchstart"+w,i.event.test.toggle);d.on("touchstart"+w,i.event.item.mouseenter).on("touchstart"+w,i.event.item.click)},mouseEvents:function(){i.verbose("Mouse detected binding mouse events");"click"==y.on?p.on("click"+w,i.event.test.toggle):"hover"==y.on?p.on("mouseenter"+w,i.delay.show).on("mouseleave"+w,i.delay.hide):p.on(y.on+w,i.toggle);d.on("mouseenter"+w,i.event.item.mouseenter).on("mouseleave"+w,i.event.item.mouseleave).on("click"+w,i.event.item.click)},intent:function(){i.verbose("Binding hide intent event to document");o&&e.on("touchstart"+w,i.event.test.touch).on("touchmove"+w,i.event.test.touch);e.on("click"+w,i.event.test.hide)}},unbind:{intent:function(){i.verbose("Removing hide intent event from document");o&&e.off("touchstart"+w).off("touchmove"+w);e.off("click"+w)}},handleKeyboard:function(t){var o,s=d.filter("."+k.selected),u=t.which,f={enter:13,escape:27,upArrow:38,downArrow:40},e=k.selected,r=d.index(s),h=s.size()>0,c=d.size();if(u==f.escape&&(i.verbose("Escape key pressed, closing dropdown"),i.hide()),i.is.visible()){if(u==f.enter&&h)return i.verbose("Enter key pressed, choosing selected item"),n.proxy(i.event.item.click,d.filter("."+e))(t),t.preventDefault(),!1;u==f.upArrow?(i.verbose("Up key pressed, changing active item"),o=0>r-1?r:r-1,d.removeClass(e).eq(o).addClass(e),t.preventDefault()):u==f.downArrow&&(i.verbose("Down key pressed, changing active item"),o=r+1>=c?r:r+1,d.removeClass(e).eq(o).addClass(e),t.preventDefault())}else u==f.enter&&i.show()},event:{test:{toggle:function(n){i.determine.intent(n,i.toggle)&&n.preventDefault()},touch:function(n){i.determine.intent(n,function(){"touchstart"==n.type?i.timer=setTimeout(i.hide,y.delay.touch):"touchmove"==n.type&&clearTimeout(i.timer)});n.stopPropagation()},hide:function(n){i.determine.intent(n,i.hide)}},item:{mouseenter:function(t){var r=n(this).find(g.submenu),u=n(this).siblings(g.item).children(g.menu);(r.length>0||u.length>0)&&(clearTimeout(i.itemTimer),i.itemTimer=setTimeout(function(){u.length>0&&i.animate.hide(!1,u.filter(":visible"));r.length>0&&(i.verbose("Showing sub-menu",r),i.animate.show(!1,r))},2*y.delay.show),t.preventDefault(),t.stopPropagation())},mouseleave:function(){var t=n(this).find(g.menu);t.size()>0&&(clearTimeout(i.itemTimer),i.itemTimer=setTimeout(function(){i.verbose("Hiding sub-menu",t);i.animate.hide(!1,t)},y.delay.hide))},click:function(t){var u=n(this),f=u.data(b.text)!==r?u.data(b.text):u.text(),e=u.data(b.value)!==r?u.data(b.value):"string"==typeof f?f.toLowerCase():f,o=function(){i.determine.selectAction(f,e);n.proxy(y.onChange,rt)(e,f)};0===u.find(g.menu).size()&&("touchstart"==t.type?u.one("click",o):o())}},resetStyle:function(){n(this).removeAttr("style")}},determine:{selectAction:function(t,r){i.verbose("Determining action",y.action);n.isFunction(i.action[y.action])?(i.verbose("Triggering preset action",y.action,t,r),i.action[y.action](t,r)):n.isFunction(y.action)?(i.verbose("Triggering user action",y.action,t,r),y.action(t,r)):i.error(ut.action,y.action)},intent:function(t,r){return i.debug("Determining whether event occurred in dropdown",t.target),r=r||function(){},0===n(t.target).closest(nt).size()?(i.verbose("Triggering event",r),r(),!0):(i.verbose("Event occurred in dropdown, canceling callback"),!1)}},action:{nothing:function(){},hide:function(){i.hide()},activate:function(n,t){t=t!==r?t:n;i.set.selected(t);i.set.value(t);i.hide()},auto:function(n,t){t=t!==r?t:n;i.set.selected(t);i.set.value(t);i.hide()},changeText:function(n,t){t=t!==r?t:n;i.set.selected(t);i.hide()},updateForm:function(n,t){t=t!==r?t:n;i.set.selected(t);i.set.value(t);i.hide()}},get:{text:function(){return tt.text()},value:function(){return it.size()>0?it.val():p.data(b.value)},item:function(t,u){var f=!1;return t=t!==r?t:i.get.value()!==r?i.get.value():i.get.text(),u===r&&""===t?(i.debug("Ambiguous dropdown value using strict type check",t),u=!0):u=u||!1,t!==r?d.each(function(){var i=n(this),e=i.data(b.text)!==r?i.data(b.text):i.text(),o=i.data(b.value)!==r?i.data(b.value):"string"==typeof e?e.toLowerCase():e;u?o===t?f=n(this):f||e!==t||(f=n(this)):o==t?f=n(this):f||e!=t||(f=n(this))}):t=i.get.text(),f||!1}},restore:{defaults:function(){i.restore.defaultText();i.restore.defaultValue()},defaultText:function(){var n=p.data(b.defaultText);i.debug("Restoring default text",n);i.set.text(n)},defaultValue:function(){var n=p.data(b.defaultValue);n!==r&&(i.debug("Restoring default value",n),i.set.selected(n),i.set.value(n))}},save:{defaults:function(){i.save.defaultText();i.save.defaultValue()},defaultValue:function(){p.data(b.defaultValue,i.get.value())},defaultText:function(){p.data(b.defaultText,tt.text())}},set:{text:function(n){i.debug("Changing text",n,tt);tt.removeClass(k.placeholder);tt.text(n)},value:function(n){i.debug("Adding selected value to hidden input",n,it);it.size()>0?it.val(n).trigger("change"):p.data(b.value,n)},active:function(){p.addClass(k.active)},visible:function(){p.addClass(k.visible)},selected:function(n){var u,t=i.get.item(n);t&&(i.debug("Setting selected menu item to",t),u=t.data(b.text)!==r?t.data(b.text):t.text(),d.removeClass(k.active),t.addClass(k.active),i.set.text(u))}},remove:{active:function(){p.removeClass(k.active)},visible:function(){p.removeClass(k.visible)}},is:{selection:function(){return p.hasClass(k.selection)},animated:function(n){return n?n.is(":animated")||n.transition&&n.transition("is animating"):nt.is(":animated")||nt.transition&&nt.transition("is animating")},visible:function(n){return n?n.is(":visible"):nt.is(":visible")},hidden:function(n){return n?n.is(":not(:visible)"):nt.is(":not(:visible)")}},can:{click:function(){return o||"click"==y.on},show:function(){return!p.hasClass(k.disabled)}},animate:{show:function(t,u){var f=u||nt;t=t||function(){};i.is.hidden(f)&&(i.verbose("Doing menu show animation",f),"none"==y.transition?t():n.fn.transition!==r&&p.transition("is supported")?f.transition({animation:y.transition+" in",duration:y.duration,complete:t,queue:!1}):"slide down"==y.transition?f.hide().clearQueue().children().clearQueue().css("opacity",0).delay(50).animate({opacity:1},y.duration,"easeOutQuad",i.event.resetStyle).end().slideDown(100,"easeOutQuad",function(){n.proxy(i.event.resetStyle,this)();t()}):"fade"==y.transition?f.hide().clearQueue().fadeIn(y.duration,function(){n.proxy(i.event.resetStyle,this)();t()}):i.error(ut.transition,y.transition))},hide:function(t,u){var f=u||nt;t=t||function(){};i.is.visible(f)&&(i.verbose("Doing menu hide animation",f),n.fn.transition!==r&&p.transition("is supported")?f.transition({animation:y.transition+" out",duration:y.duration,complete:t,queue:!1}):"none"==y.transition?t():"slide down"==y.transition?f.show().clearQueue().children().clearQueue().css("opacity",1).animate({opacity:0},100,"easeOutQuad",i.event.resetStyle).end().delay(50).slideUp(100,"easeOutQuad",function(){n.proxy(i.event.resetStyle,this)();t()}):"fade"==y.transition?f.show().clearQueue().fadeOut(150,function(){n.proxy(i.event.resetStyle,this)();t()}):i.error(ut.transition))}},show:function(){i.debug("Checking if dropdown can show");i.is.hidden()&&(i.hideOthers(),i.set.active(),i.animate.show(function(){i.can.click()&&i.bind.intent();i.set.visible()}),n.proxy(y.onShow,rt)())},hide:function(){!i.is.animated()&&i.is.visible()&&(i.debug("Hiding dropdown"),i.can.click()&&i.unbind.intent(),i.remove.active(),i.animate.hide(i.remove.visible),n.proxy(y.onHide,rt)())},delay:{show:function(){i.verbose("Delaying show event to ensure user intent");clearTimeout(i.timer);i.timer=setTimeout(i.show,y.delay.show)},hide:function(){i.verbose("Delaying hide event to ensure user intent");clearTimeout(i.timer);i.timer=setTimeout(i.hide,y.delay.hide)}},hideOthers:function(){i.verbose("Finding other dropdowns to hide");s.not(p).has(g.menu+":visible").dropdown("hide")},toggle:function(){i.verbose("Toggling menu visibility");i.is.hidden()?i.show():i.hide()},setting:function(t,i){if(n.isPlainObject(t))n.extend(!0,y,t);else{if(i===r)return y[t];y[t]=i}},internal:function(t,u){if(n.isPlainObject(t))n.extend(!0,i,t);else{if(u===r)return i[t];i[t]=u}},debug:function(){y.debug&&(y.performance?i.performance.log(arguments):(i.debug=Function.prototype.bind.call(console.info,console,y.name+":"),i.debug.apply(console,arguments)))},verbose:function(){y.verbose&&y.debug&&(y.performance?i.performance.log(arguments):(i.verbose=Function.prototype.bind.call(console.info,console,y.name+":"),i.verbose.apply(console,arguments)))},error:function(){i.error=Function.prototype.bind.call(console.error,console,y.name+":");i.error.apply(console,arguments)},performance:{log:function(n){var t,r,u;y.performance&&(t=(new Date).getTime(),u=h||t,r=t-u,h=t,f.push({Element:rt,Name:n[0],Arguments:[].slice.call(n,1)||"","Execution Time":r}));clearTimeout(i.performance.timer);i.performance.timer=setTimeout(i.performance.display,100)},display:function(){var t=y.name+":",u=0;h=!1;clearTimeout(i.performance.timer);n.each(f,function(n,t){u+=t["Execution Time"]});t+=" "+u+"ms";c&&(t+=" '"+c+"'");(console.group!==r||console.table!==r)&&f.length>0&&(console.groupCollapsed(t),console.table?console.table(f):n.each(f,function(n,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd());f=[]}},invoke:function(t,f,e){var c,s,h,o=ft;return f=f||v,e=rt||e,"string"==typeof t&&o!==r&&(t=t.split(/[\. ]/),c=t.length-1,n.each(t,function(u,f){var e=u!=c?f+t[u+1].charAt(0).toUpperCase()+t[u+1].slice(1):t;if(n.isPlainObject(o[e])&&u!=c)o=o[e];else{if(o[e]!==r)return s=o[e],!1;if(!n.isPlainObject(o[f])||u==c)return o[f]!==r?(s=o[f],!1):(i.error(ut.method,t),!1);o=o[f]}})),n.isFunction(s)?h=s.apply(e,f):s!==r&&(h=s),n.isArray(u)?u.push(h):u!==r?u=[u,h]:h!==r&&(u=h),s}};a?(ft===r&&i.initialize(),i.invoke(l)):(ft!==r&&i.destroy(),i.initialize())}),u!==r?u:this};n.fn.dropdown.settings={name:"Dropdown",namespace:"dropdown",debug:!1,verbose:!0,performance:!0,on:"click",action:"activate",delay:{show:200,hide:300,touch:50},transition:"slide down",duration:250,onChange:function(){},onShow:function(){},onHide:function(){},error:{action:"You called a dropdown action that was not defined",method:"The method you called is not defined.",transition:"The requested transition was not found"},metadata:{defaultText:"defaultText",defaultValue:"defaultValue",text:"text",value:"value"},selector:{menu:".menu",submenu:"> .menu",item:".menu > .item",text:"> .text",input:'> input[type="hidden"]'},className:{active:"active",placeholder:"default",disabled:"disabled",visible:"visible",selected:"selected",selection:"selection"}};n.extend(n.easing,{easeOutQuad:function(n,t,i,r,u){return-r*(t/=u)*(t-2)+i}})}(jQuery,window,document);!function(n,t,i,r){n.fn.transition=function(){var u,e=n(this),h=e.selector||"",o=(new Date).getTime(),f=[],c=arguments,l=c[0],a=[].slice.call(arguments,1),s="string"==typeof l;return t.requestAnimationFrame||t.mozRequestAnimationFrame||t.webkitRequestAnimationFrame||t.msRequestAnimationFrame||function(n){setTimeout(n,0)},e.each(function(){var y,w,d,p,it,b,g,tt,nt,t,v=n(this),k=this;t={initialize:function(){y=t.get.settings.apply(k,c);t.verbose("Converted arguments into settings object",y);d=y.error;p=y.className;tt=y.namespace;it=y.metadata;nt="module-"+tt;b=t.get.animationEvent();g=t.get.animationName();w=v.data(nt)||t;s&&(s=t.invoke(l));s===!1&&t.animate();t.instantiate()},instantiate:function(){t.verbose("Storing instance of module",t);v.data(nt,w)},destroy:function(){t.verbose("Destroying previous module for",k);v.removeData(nt)},refresh:function(){t.verbose("Refreshing display type on next animation");delete w.displayType},forceRepaint:function(){t.verbose("Forcing element repaint");var i=v.parent(),n=v.next();0===n.size()?v.detach().appendTo(i):v.detach().insertBefore(n)},repaint:function(){t.verbose("Repainting element");k.offsetWidth},animate:function(n){return y=n||y,t.is.supported()?(t.debug("Preparing animation",y.animation),t.is.animating()&&y.queue?(!y.allowRepeats&&t.has.direction()&&t.is.occuring()&&w.queuing!==!0?t.error(d.repeated):t.queue(y.animation),!1):void(t.can.animate?t.set.animating(y.animation):t.error(d.noAnimation,y.animation))):(t.error(d.support),!1)},reset:function(){t.debug("Resetting animation to beginning conditions");v.off(b);t.restore.conditions();t.hide();t.remove.animating()},queue:function(n){t.debug("Queueing animation of",n);w.queuing=!0;v.one(b,function(){w.queuing=!1;t.repaint();t.animate.apply(this,y)})},complete:function(){t.verbose("CSS animation complete",y.animation);t.is.looping()||(t.is.outward()?(t.verbose("Animation is outward, hiding element"),t.restore.conditions(),t.remove.display(),t.hide(),n.proxy(y.onHide,this)()):t.is.inward()?(t.verbose("Animation is outward, showing element"),t.restore.conditions(),t.show(),n.proxy(y.onShow,this)()):t.restore.conditions(),t.remove.duration(),t.remove.animating());n.proxy(y.complete,this)()},has:{direction:function(n){return n=n||y.animation,-1!==n.search(p.inward)||-1!==n.search(p.outward)?(t.debug("Direction already set in animation"),!0):!1}},set:{animating:function(n){n=n||y.animation;t.save.conditions();t.can.transition()&&!t.has.direction()&&t.set.direction();t.remove.hidden();t.set.display();v.addClass(p.animating).addClass(p.transition).addClass(n).one(b,t.complete);t.set.duration(y.duration);t.debug("Starting tween",y.animation,v.attr("class"))},display:function(){var n=t.get.displayType();"block"!==n&&"none"!==n&&(t.verbose("Setting final visibility to",n),v.css({display:n}))},direction:function(){v.is(":visible")?(t.debug("Automatically determining the direction of animation","Outward"),v.removeClass(p.inward).addClass(p.outward)):(t.debug("Automatically determining the direction of animation","Inward"),v.removeClass(p.outward).addClass(p.inward))},looping:function(){t.debug("Transition set to loop");v.addClass(p.looping)},duration:function(n){n=n||y.duration;n="number"==typeof n?n+"ms":n;t.verbose("Setting animation duration",n);v.css({"-webkit-animation-duration":n,"-moz-animation-duration":n,"-ms-animation-duration":n,"-o-animation-duration":n,"animation-duration":n})},hidden:function(){v.addClass(p.transition).addClass(p.hidden)},visible:function(){v.addClass(p.transition).addClass(p.visible)}},save:{displayType:function(n){w.displayType=n},transitionExists:function(i,r){n.fn.transition.exists[i]=r;t.verbose("Saving existence of transition",i,r)},conditions:function(){w.cache={className:v.attr("class"),style:v.attr("style")};t.verbose("Saving original attributes",w.cache)}},restore:{conditions:function(){return w.cache===r?!1:(w.cache.className?v.attr("class",w.cache.className):v.removeAttr("class"),w.cache.style?v.attr("style",w.cache.style):"block"===t.get.displayType()&&v.removeAttr("style"),t.is.looping()&&t.remove.looping(),void t.verbose("Restoring original attributes",w.cache))}},remove:{animating:function(){v.removeClass(p.animating)},display:function(){w.displayType!==r&&v.css("display","")},duration:function(){v.css({"-webkit-animation-duration":"","-moz-animation-duration":"","-ms-animation-duration":"","-o-animation-duration":"","animation-duration":""})},hidden:function(){v.removeClass(p.hidden)},visible:function(){v.removeClass(p.visible)},looping:function(){t.debug("Transitions are no longer looping");v.removeClass(p.looping);t.forceRepaint()}},get:{settings:function(t,i,r){return"object"==typeof t?n.extend(!0,{},n.fn.transition.settings,t):"function"==typeof r?n.extend({},n.fn.transition.settings,{animation:t,complete:r,duration:i}):"string"==typeof i||"number"==typeof i?n.extend({},n.fn.transition.settings,{animation:t,duration:i}):"object"==typeof i?n.extend({},n.fn.transition.settings,i,{animation:t}):"function"==typeof i?n.extend({},n.fn.transition.settings,{animation:t,complete:i}):n.extend({},n.fn.transition.settings,{animation:t})},displayType:function(){return w.displayType===r&&t.can.transition(),w.displayType},transitionExists:function(t){return n.fn.transition.exists[t]},animationName:function(){var n,f=i.createElement("div"),u={animation:"animationName",OAnimation:"oAnimationName",MozAnimation:"mozAnimationName",WebkitAnimation:"webkitAnimationName"};for(n in u)if(f.style[n]!==r)return t.verbose("Determined animation vendor name property",u[n]),u[n];return!1},animationEvent:function(){var n,f=i.createElement("div"),u={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(n in u)if(f.style[n]!==r)return t.verbose("Determined animation vendor end event",u[n]),u[n];return!1}},can:{animate:function(){return"none"!==v.css(y.animation)?(t.debug("CSS definition found",v.css(y.animation)),!0):(t.debug("Unable to find css definition",v.attr("class")),!1)},transition:function(){var i,o,s,f,h=v.attr("class"),e=y.animation,u=t.get.transitionExists(y.animation);return(u===r||w.displayType===r)&&(t.verbose("Determining whether animation exists"),i=n("<div>").addClass(h).appendTo(n("body")),o=i.removeClass(p.inward).removeClass(p.outward).addClass(p.animating).addClass(p.transition).addClass(e).css(g),s=i.addClass(p.inward).css(g),f=i.attr("class",h).show().css("display"),t.verbose("Determining final display state",f),o!=s?(t.debug("Transition exists for animation",e),u=!0):(t.debug("Static animation found",e,f),u=!1),i.remove(),t.save.displayType(f),t.save.transitionExists(e,u)),u}},is:{animating:function(){return v.hasClass(p.animating)},inward:function(){return v.hasClass(p.inward)},outward:function(){return v.hasClass(p.outward)},looping:function(){return v.hasClass(p.looping)},occuring:function(n){return n=n||y.animation,v.hasClass(n)},visible:function(){return v.is(":visible")},supported:function(){return g!==!1&&b!==!1}},hide:function(){t.verbose("Hiding element");t.remove.visible();t.set.hidden();t.repaint()},show:function(n){t.verbose("Showing element",n);t.remove.hidden();t.set.visible();t.repaint()},start:function(){t.verbose("Starting animation");v.removeClass(p.disabled)},stop:function(){t.debug("Stopping animation");v.addClass(p.disabled)},toggle:function(){t.debug("Toggling play status");v.toggleClass(p.disabled)},setting:function(t,i){if(n.isPlainObject(t))n.extend(!0,y,t);else{if(i===r)return y[t];y[t]=i}},internal:function(i,u){if(n.isPlainObject(i))n.extend(!0,t,i);else{if(u===r)return t[i];t[i]=u}},debug:function(){y.debug&&(y.performance?t.performance.log(arguments):(t.debug=Function.prototype.bind.call(console.info,console,y.name+":"),t.debug.apply(console,arguments)))},verbose:function(){y.verbose&&y.debug&&(y.performance?t.performance.log(arguments):(t.verbose=Function.prototype.bind.call(console.info,console,y.name+":"),t.verbose.apply(console,arguments)))},error:function(){t.error=Function.prototype.bind.call(console.error,console,y.name+":");t.error.apply(console,arguments)},performance:{log:function(n){var i,r,u;y.performance&&(i=(new Date).getTime(),u=o||i,r=i-u,o=i,f.push({Element:k,Name:n[0],Arguments:[].slice.call(n,1)||"","Execution Time":r}));clearTimeout(t.performance.timer);t.performance.timer=setTimeout(t.performance.display,100)},display:function(){var i=y.name+":",u=0;o=!1;clearTimeout(t.performance.timer);n.each(f,function(n,t){u+=t["Execution Time"]});i+=" "+u+"ms";h&&(i+=" '"+h+"'");e.size()>1&&(i+=" ("+e.size()+")");(console.group!==r||console.table!==r)&&f.length>0&&(console.groupCollapsed(i),console.table?console.table(f):n.each(f,function(n,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd());f=[]}},invoke:function(t,i,f){var h,o,s,e=w;return i=i||a,f=k||f,"string"==typeof t&&e!==r&&(t=t.split(/[\. ]/),h=t.length-1,n.each(t,function(i,u){var f=i!=h?u+t[i+1].charAt(0).toUpperCase()+t[i+1].slice(1):t;if(n.isPlainObject(e[f])&&i!=h)e=e[f];else{if(e[f]!==r)return o=e[f],!1;if(!n.isPlainObject(e[u])||i==h)return e[u]!==r?(o=e[u],!1):!1;e=e[u]}})),n.isFunction(o)?s=o.apply(f,i):o!==r&&(s=o),n.isArray(u)?u.push(s):u!==r?u=[u,s]:s!==r&&(u=s),o||!1}};t.initialize()}),u!==r?u:this};n.fn.transition.exists={};n.fn.transition.settings={name:"Transition",debug:!1,verbose:!0,performance:!0,namespace:"transition",complete:function(){},onShow:function(){},onHide:function(){},allowRepeats:!1,animation:"fade",duration:"700ms",queue:!0,className:{animating:"animating",disabled:"disabled",hidden:"hidden",inward:"in",loading:"loading",looping:"looping",outward:"out",transition:"ui transition",visible:"visible"},error:{noAnimation:"There is no css animation matching the one you specified.",repeated:"That animation is already occurring, cancelling repeated animation",method:"The method you called is not defined",support:"This browser does not support CSS animations"}}}(jQuery,window,document);
//# sourceMappingURL=semantic.min.js.map
