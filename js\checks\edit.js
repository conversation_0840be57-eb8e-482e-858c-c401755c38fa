+(function ($) {
	"use strict";

	checkAddButton();

	function submitForm(action, formData) {
		return $.ajax({
			url: action,
			method: "POST",
			data: formData,
			processData: false,
			contentType: false,
			dataType: "json",
		});
	}

	function getCheckNo(bankId) {
		return $.ajax({
			url: BASE_URI + "checks/no/" + bankId,
			method: "GET",
			dataType: "json",
		});
	}

	function showError($modal, error) {
		$modal.find(".error").html(error);
	}

	function getPurchases() {
		return $.ajax({
			url: BASE_URI + "purchases/approved",
			type: "GET",
			dataType: "json",
		});
	}

	function addPurchaseRow(purchases) {
		var $newRow = $(paymentRow());

		setPurchaseInvoice($newRow, purchases);

		var $purchaseTable = $(".purchase-table");
		var checkPurchases = $purchaseTable.find(".check-purchase");

		if (checkPurchases.length === 0) {
			$purchaseTable.find(".no-purchase").remove();
		}
		$purchaseTable.find("tbody").append($newRow);
	}

	function paymentRow() {
		var rowId = makeRandomId();

		return (
			'<tr class="check_purchase">' +
			"<td>" +
			'<input type="hidden" name="check_purchase_' +
			rowId +
			'" value="' +
			rowId +
			'">' +
			'<select name="purchase_' +
			rowId +
			'" class="form-control dropsearch" data-placeholder=""></select>' +
			"</td>" +
			"<td>" +
			'<input type="text" name="amount_' +
			rowId +
			'" class="form-control text-right" min="0" step="any">' +
			"</td>" +
      "<td class='text-center'>" +
			'<a href="#" target="_blank" name="details_' +
      rowId + '">View</a>' +
			"</td>" +
			'<td class="text-right">' +
			'<button type="button" class="btn btn-sm btn-danger action-delete-purchase">Delete</button>' +
			"</td>" +
			"</tr>"
		);
	}

	function makeRandomId() {
		var text = "";
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function setPurchaseList($row, purchases) {
		$row.find('select[name^="purchase"]').select2({
			data: purchases,
			placeholder: $(this).attr("data-placeholder"),
			allowClear: true,
			width: "100%",
		});
	}

	function emptyRow() {
		return (
			'<tr class="no-purchase">' +
			'<td class="text-center" colspan="3">No purchase orders added yet.</td>' +
			"</tr>"
		);
	}

	function getInfo(purchaseId) {
		return $.ajax({
			url: BASE_URI + "purchases/get_info/" + purchaseId,
			type: "GET",
			dataType: "json",
		});
	}

	function setPurchase($row, purchase) {
		var total = parseFloat(purchase.total);
		if (isNaN(total)) {
			total = 0;
		}


		$row
			.find('input[name^="amount"]')
			.val($.fn.addNumericCommas(total.toFixed(2)));


    $row.find('a[name^="details"]').attr('href',BASE_URI + 'purchases/preview/' + purchase.id + '/Print');
	}

	function calculateTotal() {
		var total = 0;
		$(".purchase-table")
			.find(".check_purchase")
			.each(function (_, purchase) {
				var subtotal = parseFloat(
					$.fn.removeNumericCommas(
						$(purchase).find('input[name^="amount"]').val()
					)
				);
				if (isNaN(subtotal)) {
					subtotal = 0;
				}
				total += subtotal;
			});
		$('input[name="total"]').val($.fn.addNumericCommas(total.toFixed(2)));
	}

	function removePurchaseRow(event) {
		$(event.currentTarget).closest(".check_purchase").remove();

		var $purchaseTable = $(event.delegateTarget);
		var purchaseItems = $purchaseTable.find(".check_purchase");
		if (purchaseItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$purchaseTable.find("tbody").html($emptyRow);
		}

		calculateTotal();
	}

	function getPOs(supplierId) {
		return $.ajax({
			url: BASE_URI + "receives/get_w_balance/" + supplierId,
			type: "GET",
			dataType: "json",
		});
	}

	function setPurchaseInvoice($row, purchases) {
		$row.find('select[name^="purchase"]').select2({
			data: purchases,
			placeholder: $(this).attr("data-placeholder"),
			allowClear: true,
			width: "100%",
		});
	}

	function checkAddButton() {
		let hasValue =
			$("select[name='supplier']").children("option:selected").val() !== ""
				? true
				: false;
		$(".action-add-payment").prop("disabled", !hasValue);
	}

  function approveCheck(checkId) {
		return $.ajax({
			url: BASE_URI + 'checks/approve/' + checkId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var recevies = [
			{
				id: "",
				text: "",
			},
		];

		function updatedPaymentDropdown() {
			let supplierId = $("select[name='supplier']")
				.children("option:selected")
				.val();
			if (supplierId) {
				$.when(getPOs(supplierId)).then(function (response) {
					recevies = [
						{
							id: "",
							text: "",
						},
					];
					response.purchases.forEach(function (purchase) {
						var total = parseFloat(purchase.total);
						if (isNaN(total)) {
							total = 0;
						}

						purchase.text =
							"Purchase Order #" +
							purchase.id +
							" - Php" +
							$.fn.addNumericCommas(total.toFixed(2));
						recevies.push(purchase);
					});
				});
			}
		}

		updatedPaymentDropdown();
		$("select[name='supplier']").change(function (event) {
			updatedPaymentDropdown();
			$(".purchase-table").find("tbody").html($(emptyRow()));
			checkAddButton();
		});

		$(".action-add-payment").click(function () {
			addPurchaseRow(recevies);
		});

		var checkPurchase = null;

		$(".purchase-table")
			.on("change", 'select[name^="purchase"]', function (event) {
				checkPurchase = $(event.currentTarget).closest(".check_purchase");
				$.when(getInfo(event.currentTarget.value)).then(function (response) {
					setPurchase(checkPurchase, response.data);
					calculateTotal();
				});

			})
			.on("click", ".action-delete-purchase", function (event) {
				removePurchaseRow(event);
			})
			.on("change", 'input[name^="amount"]', calculateTotal);

		$(".purchase-table")
			.on("change", 'input[name^="amount"]', calculateTotal)
			.trigger('change');





    $('.action-approve').on('click', function(event) {
      var checkNo = event.currentTarget.getAttribute('data-checkno');

      alertify
        .confirm('Are you sure you want to approve Check No ' + checkNo + '?', function() {
          var checkId = event.currentTarget.getAttribute('data-id');
          approveCheck(checkId);
          window.location.href = BASE_URI + 'checks/manager';
        })
        .setHeader('<em>Approve Check</em>');
    });


		calculateTotal();
	});
})(jQuery);
