<?php
class Order_slip extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'order_slip';
        parent::__construct();
    }

    /**
     * Get Partially Paid Invoices
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT order_slip.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM order_slip
LEFT JOIN customer ON customer.id = order_slip.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_slip.added_by
WHERE order_slip.paid_amount < order_slip.grand_total
    AND order_slip.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get order_slip By ID
     */
    public function get_by_id($order_slip_id)
    {
        $sql = <<<EOT
SELECT order_slip.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, customer.available_credit, customer.tin_number, issuer.full_name AS issuer
FROM order_slip
LEFT JOIN customer AS customer ON customer.id = order_slip.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_slip.added_by
WHERE order_slip.id = ?
    AND order_slip.is_deleted = 0
EOT;
        $binds = [$order_slip_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get order_slip file ID
     */
    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM order_slip
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get order_slips Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT order_slip.id, order_slip.order_slip_date, order_slip.customer_id, order_slip.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM order_slip
LEFT JOIN (
    SELECT payment.payment_date, paid_order_slip.order_slip_id, paid_order_slip.amount
    FROM payment
    LEFT JOIN paid_order_slip ON paid_order_slip.payment_id = payment.id
    LEFT JOIN order_slip ON order_slip.id = paid_order_slip.order_slip_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND order_slip.customer_id = ?
) payment ON payment.order_slip_id = order_slip.id
LEFT JOIN customer AS customer ON customer.id = order_slip.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_slip.added_by
WHERE order_slip.order_slip_date < ?
    AND customer.id = ?
    AND order_slip.is_deleted = 0
GROUP BY order_slip.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get order_slips Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT order_slip.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM order_slip
LEFT JOIN customer AS customer ON customer.id = order_slip.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_slip.added_by
WHERE order_slip.order_slip_date BETWEEN ? AND ?
    AND order_slip.customer_id = ?
    AND order_slip.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


    /**
     * Get Invoices Total Receivables
     */
    public function get_total_receivables()
    {

        $sql = <<<EOT
SELECT SUM(order_slip.grand_total - order_slip.paid_amount) AS total_receivables
FROM order_slip
WHERE is_deleted = 0
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_receivables;
        }else{
            return 0;
        }
    }


        /**
     * Get order_slips BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT order_slip.id, order_slip.order_slip_no, order_slip.terms, order_slip.order_slip_date, order_slip.customer_id, order_slip.total AS charge, order_slip.total, order_slip.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM order_slip
LEFT JOIN (
    SELECT payment.payment_date, paid_order_slip.order_slip_id, paid_order_slip.amount
    FROM payment
    LEFT JOIN paid_order_slip ON paid_order_slip.payment_id = payment.id
    LEFT JOIN order_slip ON order_slip.id = paid_order_slip.order_slip_id
    WHERE payment.payment_date
        AND order_slip.customer_id = ?
) payment ON payment.order_slip_id = order_slip.id
LEFT JOIN customer AS customer ON customer.id = order_slip.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_slip.added_by
WHERE customer.id = ?
    AND order_slip.is_deleted = 0
GROUP BY order_slip.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */