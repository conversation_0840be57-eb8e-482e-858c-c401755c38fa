<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$poc->id;?>">
		<div class="receipt-header">
			<div class="clearfix">
				<i class="pull pull-right">POC-Purchase Order Form from Customer</i>
			</div>

			<h1>POC</h1>
			<h5>PURCHASE ORDER FORM</h5>

			<div class="row">
                <div class="col-10">
                    <p class="text-right" style="font-size: 14px;">POC No. : </p>
                </div>
                <div class="col-2" style="padding: 0; font-size: 14px;">
                    <p class="text-left"><?=$poc->reference;?></p>
                </div>
			</div>
			<div class="row">
                <div class="col-10">
                    <p class="text-right" style="font-size: 14px;">Date : </p>
                </div>
                <div class="col-2" style="padding: 0; font-size: 14px;">
                    <p class="text-left"><?=date('M d, Y', strtotime($poc->poc_date));?></p>
                </div>
			</div>
		</div>

		<div class="clearfix"></div>

        <div class="row">
            <div class="col-6 form-group">
                <div class="row">
                    <div class="col-4 no-margin">
                        <p class="text-left">Customer's Name :</p>
                    </div>
                    <div class="col-8 bordered-bottom">
                        <p class="text-center"><strong><?= $customer->name;?></strong></p>
                    </div>
                </div>
            </div>
            <div class="col-6 form-group">
                <div class="row">
                    <div class="col-4 no-margin">
                        <p class="text-left">Company Name :</p>
                    </div>
                    <div class="col-8 bordered-bottom">
                        <p class="text-center"><strong><?= $poc->company_name;?></strong></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 form-group">
                <div class="row">
                    <div class="col-2 no-margin">
                        <p class="text-left">Complete Address :</p>
                    </div>
                    <div class="col-10 bordered-bottom">
                        <p class="text-center"><strong><?= $customer->address;?></strong></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-6 form-group">
                <div class="row">
                    <div class="col-4 no-margin">
                        <p class="text-left">Contact Number :</p>
                    </div>
                    <div class="col-8 bordered-bottom">
                        <p class="text-center"><strong><?= $customer->phone_no;?></strong></p>
                    </div>
                </div>
            </div>
            <div class="col-6 form-group">
                <div class="row">
                    <div class="col-4 no-margin">
                        <p class="text-left">Email Address :</p>
                    </div>
                    <div class="col-8 bordered-bottom">
                        <p class="text-center"><strong><?= $customer->email;?></strong></p>
                    </div>
                </div>
            </div>
        </div>

        <br>

        <div class="row" style="border-bottom: 2px solid black;"></div>

        <br>

        <div class="row">
            <div class="col-3 form-group no-padding-right">
                <p>Product/Services Availed</p>
            </div>
            <div class="col form-group">
                <div class="row">
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="processing" name="processing" value="1" <?= $poc->processing == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                        <label style="font-size: 14px; margin-right: 10px;">Processing</label>
                    </div>
                </div>
            </div>
            <div class="col form-group">
                <div class="row">
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="live_sell" name="live_sell" value="1" <?= $poc->live_sell == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                        <label style="font-size: 14px; margin-right: 10px;">Live Sell</label>
                    </div>
                </div>
            </div>
            <div class="col form-group">
                <div class="row">
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="ice" name="ice" value="1" <?= $poc->ice == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                        <label style="font-size: 14px; margin-right: 10px;">Ice</label>
                    </div>
                </div>
            </div>
            <div class="col form-group">
                <div class="row">
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="water" name="water" value="1" <?= $poc->water == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                        <label style="font-size: 14px; margin-right: 10px;">Water</label>
                    </div>
                </div>
            </div>
            <div class="col-3 form-group">
                <div class="row">
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="others" name="other_services" value="1" <?= !empty($poc->others) ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                        <label for="others" style="font-size: 14px; margin-right: 10px;">Others:</label>
                        <input type="text" value="<?= $poc->others; ?>" readonly style="border: none; border-bottom: 1px solid #000; font-size: 14px; width: 100%;">
                    </div>
                </div>
            </div>
        </div>

        <br>

		<table class="item-table">
			<thead>
                <tr>
                    <th class="text-center" colspan="6" style="background-color: lightgray">TRANSACTION RECORD</th>
                </tr>
                <tr>
                    <th class="text-center" style="width: 25%;">Description</th>

                    <th class="text-center" style="width: 25%;">Quantity</th>

                    <th class="text-center" style="width: 25%;">Unit Price</th>

                    <th class="text-center" style="width: 25%;">Total Amount</th>
                </tr>
			</thead>

			<tbody>
				<?php 
                    $total = 0;
					foreach ($poc_items as $poc_item): 
                        $total += $poc_item->amount;
				?>
					<tr class="item">
						<td class="text-left"><?= $poc_item->description ?></td>
						<td class="text-right"><?= number_format($poc_item->qty, 2) ?></td>
						<td class="text-right"><?= number_format($poc_item->unit_price, 2) ?></td>
						<td class="text-right"><?= number_format($poc_item->amount, 2) ?></td>
					</tr>
				<?php endforeach;?>
			</tbody>
            <tfoot>
                <tr>
                    <th class="text-right" colspan="3">TOTAL AMOUNT DUE</th>
                    <th class="text-right"><?= number_format($total, 2) ?></th>
                </tr>
            </tfoot>
		</table>

        <br>

        <div class="row">
            <div class="col-1 form-group">
                <label for="remarks">Remarks:</label>
            </div>
            <div class="col form-group">
                <div class="row">
                    <div class="col-12 form-group">
                        <div class="row">
                            <div class="col-12 form-group">
                                <input type="checkbox" id="remarks_1" name="remarks_1" value="1" <?= $poc->remarks_1 == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                                <span>Order was made through call/text, confirmed by aforementioned customer and agreed to signed this form prior to the release of goods as proof of purchase.</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 form-group">
                                <input type="checkbox" id="remarks_2" name="remarks_2" value="1" <?= $poc->remarks_2 == 1 ? 'checked' : ''; ?> disabled style="transform: scale(1.5); margin-right: 10px;">
                                <span>Final due amount may vary depending on actual delivery.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-2 form-group">
                <label for="other_remarks" class="custom-label">Other Remarks:</label>
            </div>
            <div class="col-sm-10 form-group text-left">
                <input type="text" value="<?= $poc->other_remarks; ?>" readonly style="border: none; border-bottom: 1px solid #000; font-size: 14px; width: 100%;">
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 form-group">
                <div style="text-align: justify;">
                    I, hereby certify that the above information are true and correct based on my personal knowledge, and I undertake to abide and comply with the terms and conditions stipulated hereunder as part of this purchase contract.
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-8 form-group">
                <div style="text-align: justify;">&nbsp;</div>
            </div>
            <div class="col-sm-4 text-center" style="margin-top: 10px;">
                &nbsp;
				<hr class="signature-line" style="margin: 5px">
				<p>Printed Name & Signature</p>
				<p style="font-size: 10px;">Customer/Authorized Representative</p>
			</div>
        </div>
        
        <br>

        <div class="row">
            <div class="col-12 form-group no-margin">
                <div style="text-align: justify;">
                    Terms and Conditions:
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 form-group">
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        1.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        The customer shall settle the total amount payable as shown above prior to discharge and/or the delivery of the finished products;
                    </div>
                </div>
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        2.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        For every batch of processing, the customer shall post a cash bond in the amount of ₱2.00 per head, as guarantee for any unsettled balance. The cash bond will only be released to the customer without interest once the customer is free of any liabilities to the company within the time allowed. Processing of withdrawal of cash bond will take 30-45 days from the day of application;
                    </div>
                </div>
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        3.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        Payments made through online banking will be validated for 1-3 banking days;
                    </div>
                </div>
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        4.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        Delay in payments shall be charged with interests as follows:
                    </div>
                </div>
                <div class="row">
                    <div class="col-2 form-group text-right no-margin" style="font-size: 12px;">
                        4.1
                    </div>
                    <div class="col-10 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        0.5% of the total amount payable for a delay of 1-7 days from due date;
                    </div>
                </div>
                <div class="row">
                    <div class="col-2 form-group text-right no-margin" style="font-size: 12px;">
                        4.2
                    </div>
                    <div class="col-10 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        1% of the total amount payable for a delay of 8-15 days from due date;
                    </div>
                </div>
                <div class="row">
                    <div class="col-2 form-group text-right no-margin" style="font-size: 12px;">
                        4.3
                    </div>
                    <div class="col-10 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        2% of the total amount payable for a delay of 15-21 days from due date;
                    </div>
                </div>
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        5.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        Delay in payment for a period of more than 21 days shall be cause for forfeit of the cash bond. If the cash bond is insufficient to cover the amount due, the customer shall pay the remaining balance;
                    </div>
                </div>
                <div class="row">
                    <div class="col-1 form-group text-right no-margin" style="font-size: 12px;">
                        6.
                    </div>
                    <div class="col-11 form-group no-margin" style="font-size: 12px; text-align: justify;">
                        The credit limit for purchases on credit shall be listed below by the Accounting/Audit Officer on-duty.
                    </div>
                </div>
            </div>
        </div>

        <br>

        <div class="row" style="border-bottom: 2px solid black;"></div>

        <br>

        <div class="row d-flex align-items-stretch" style="margin-bottom: 0;">
            <div class="col-3 form-group table-bordered d-flex flex-column">
                <div class="row" style="margin-bottom: 15px;">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                        POC Prepared By:
                    </div>
                </div>
                <div class="row">
                    <?php if (!empty($poc->issuer_signature)): ?>
                        <div class="col-12 form-group no-margin">
                            <img src="<?= base_url('assets/asylum/signatures/' . $poc->added_by . '/' . $poc->issuer_signature); ?>" class="img-fluid" style="max-height: 100px; width: auto;" alt="Signature">
                        </div>
                    <?php endif; ?>
                </div>
                <div class="row">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                        <strong><?= $poc->issuer_name ?></strong>
                    </div>
                </div>
                <div class="row" style="margin-bottom: 15px;">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 13px;">
                        Date: <?= date('M d, Y h:i:s A', strtotime($poc->added_on)) ?>
                    </div>
                </div>
            </div>

            <div class="col-4 form-group table-bordered mx-5 d-flex flex-column">
                <div class="row">
                    <div class="col-12 form-group text-center no-margin only-border-bottom" style="font-size: 14px;">
                        <strong>CREDIT EVALUATION CHECK</strong>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 form-group text-left bordered-bottom-right no-margin" style="font-size: 14px; padding-left: 5px;">
                        Approved Credit Line
                    </div>
                    <div class="col-6 form-group text-center only-border-bottom no-margin">
                        <?= !empty($poc->credit_line) ? number_format($poc->credit_line, 2) : '' ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 form-group text-left bordered-bottom-right no-margin" style="font-size: 14px; padding-left: 5px;">
                        Current AR Balance
                    </div>
                    <div class="col-6 form-group text-center only-border-bottom no-margin">
                        <?= !empty($poc->current_balance) ? number_format($poc->current_balance, 2) : '' ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 form-group text-left bordered-bottom-right no-margin" style="font-size: 14px; padding-left: 5px;">
                        Available Credit Line
                    </div>
                    <div class="col-6 form-group text-center only-border-bottom no-margin">
                        <?= !empty($poc->available_credit) ? number_format($poc->available_credit, 2) : '' ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 form-group text-right no-margin d-flex align-items-center justify-content-end" style="font-size: 14px; padding-left: 5px; padding-bottom: 2px; border-right: 2px solid #000;">
                        <strong>CEC Officer</strong>
                    </div>
                    <div class="col-6 form-group text-center no-margin">
                        <div class="row">
                            <?php if (!empty($poc->audited_by) && !empty($poc->auditor_signature)): ?>
                                <div class="col-12 form-group text-center no-margin">
                                    <img src="<?= base_url('assets/asylum/signatures/' . $poc->audited_by . '/' . $poc->auditor_signature); ?>" class="img-fluid" style="max-height: 50px; width: auto;" alt="Signature">
                                </div>
                            <?php endif; ?>
                            <div class="col-12 form-group text-center no-margin">
                                <?= !empty($poc->auditor_name) ? $poc->auditor_name : '' ?>
                            </div>
                            <div class="col-12 form-group text-center no-margin" style="font-size: 10px;">
                                <?= !empty($poc->auditor_name) ? date('M d, Y h:i:s A', strtotime($poc->audited_on)) : '' ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-4 form-group table-bordered d-flex flex-column">
                <div class="row" style="margin-bottom: 15px;">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                        <?php if ($poc->status === 'disapproved'): ?>
                            POC Disapproved By:
                        <?php else: ?>
                            POC Approved to Proceed By:
                        <?php endif; ?>
                    </div>
                </div>
                <div class="row">
                    <?php if ($poc->status === 'approved' && !empty($poc->approver_signature)): ?>
                        <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                            <img src="<?= base_url('assets/asylum/signatures/' . $poc->approved_by . '/' . $poc->approver_signature); ?>" class="img-fluid" style="max-height: 100px; width: auto;" alt="Signature">
                        </div>
                    <?php elseif ($poc->status === 'disapproved' && !empty($poc->approver_signature)): ?>
                        <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                            <img src="<?= base_url('assets/asylum/signatures/' . $poc->disapproved_by . '/' . $poc->disapprover_signature); ?>" class="img-fluid" style="max-height: 100px; width: auto;" alt="Signature">
                        </div>
                    <?php else: ?>
                        &nbsp;
                    <?php endif; ?>
                </div>
                <div class="row">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                        <?php if ($poc->status === 'approved'): ?>
                            <strong><?= $poc->approver_name ?></strong>
                        <?php elseif ($poc->status === 'disapproved'): ?>
                            <strong><?= $poc->disapprover_name ?></strong>
                        <?php else: ?>
                            &nbsp;
                        <?php endif; ?>
                    </div>
                </div>
                <div class="row" style="margin-bottom: 15px;">
                    <div class="col-12 form-group text-left no-margin" style="font-size: 14px;">
                        <?php if ($poc->status === 'approved'): ?>
                            Date: <?= date('M d, Y h:i:s A', strtotime($poc->approved_on)) ?>
                        <?php elseif ($poc->status === 'disapproved'): ?>
                            Date: <?= date('M d, Y h:i:s A', strtotime($poc->disapproved_on)) ?>
                        <?php else: ?>
                            Date:
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" style="margin-top: 0;">
            <div class="col-12 form-group">
                <div class="row">
                    <div class="col-12 form-group text-center" style="font-size: 10px; padding-right: 9%;">
                        <i>[To be accomplished by ACCOUNTING/AUDIT Head Only]</i>
                    </div>
                </div>
            </div>
        </div>

	</div>

	<div class="clearfix text-center">
        <?php if ($poc->status === 'approved' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE])): ?>
			<button type="button" class="btn btn-primary action-print"><i class="fa fa-print"></i> Print</button>
		<?php endif; ?>
        <?php if ($poc->status === 'for_approval' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR])): ?>
			<button type="button" class="btn btn-success action-approve" data-id="<?=$poc->id;?>"><i class="fa fa-check"></i> Approve</button>
			<button type="button" class="btn btn-danger action-disapprove" data-id="<?=$poc->id;?>"><i class="fa fa-times"></i> Disapprove</button>
		<?php endif; ?>
		<a class="btn btn-light" href="<?=site_url('poc/manager');?>">Close</a>
	</div>
</div>

<style>
    p {
        font-size: 14px;
    }

    h1 {
        font-size: 54px;
        font-weight: bold;
        color: #000;
    }

    h5 {
        font-size: 18px;
        font-weight: bold;
        color: #000;
    }

    .receipt {
        padding: 10%;
    }

    .bordered-bottom {
        border-bottom: 1px solid #000;
        padding-bottom: 2px;
    }

    .bordered-bottom-right {
        border-bottom: 2px solid #000;
        border-right: 2px solid #000;
        height: 100%; /* Ensures this class takes full height */
    }

    .only-border-bottom {
        border-bottom: 2px solid #000;
    }

    .no-margin {
        margin: 0;
    }

    .no-padding-right {
        padding-right: 0;
    }

    .no-padding {
        padding: 0;
    }

    .small-text {
        font-size: 12px;
    }

    .with-margin {
        margin-left: 4%;
        margin-right: 4%;
    }

    .table-bordered {
        max-height: 100%; /* Prevents columns from exceeding a certain height */
        overflow: hidden; /* Hides any overflow content */
        border: 2px solid black; /* Optional: Add border for visibility */
    }

    .d-flex {
        display: flex;
    }

    .align-items-stretch {
        align-items: stretch; /* Ensures children stretch to the height of the parent */
    }

    .flex-column {
        flex-direction: column;
        justify-content: space-between; /* Ensures content is evenly spaced */
        flex-grow: 1; /* Ensures the columns grow to fill available space */
    }

    .row.d-flex {
        display: flex;
        align-items: stretch; /* Stretch child columns to equal height */
    }

    .col-3, .col-4 {
        flex: 1; /* Ensures equal width and allows columns to grow equally */
        display: flex; /* Make each column a flex container */
        flex-direction: column; /* Stack content vertically */
        justify-content: space-between; /* Space out content evenly */
        min-height: 100%; /* Ensure they take full height */
    }

    .mx-5 {
        margin-left: 5px;
        margin-right: 5px;
    }

    @media print {
        @page {
            margin: 0;
            padding: 0;
            orientation: portrait;
        }

        .receipt {
            padding: 0%;
        }
    }
</style>