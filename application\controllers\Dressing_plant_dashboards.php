<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Dressing_plant_dashboards extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('dressing_plant_dashboards/manager'));
    }

    /**
     * Dressing Plant Dashboard Manager
     */
    public function manager()
    {
        $this->title = 'Dressing Plant Dashboard';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/dressing_plant_dashboard/manager'
        ];

        $month_filter = $this->input->get('month');
        $year_filter = $this->input->get('year');

        $where = [
            'status'                => 'pending',
            'is_deleted'            => 0
        ];
        if(!empty($month_filter)){
            $where['MONTH(purchase_date)'] = $month_filter;
        } else {
            $where['MONTH(purchase_date)'] = date('m');
        }

        if(!empty($year_filter)){
            $where['YEAR(purchase_date)'] = $year_filter;
        } else {
            $where['YEAR(purchase_date)'] = date('Y');
        }
        $pending_po = $this->purchase->select('', $where);
        $pending_po = $pending_po?count($pending_po):0;

        $where = [
            'status <>'             => 'audited',
            'is_urgent'             => 1,
            'is_deleted'            => 0
        ];
        if(!empty($month_filter)){
            $where['MONTH(purchase_date)'] = $month_filter;
        } else {
            $where['MONTH(purchase_date)'] = date('m');
        }

        if(!empty($year_filter)){
            $where['YEAR(purchase_date)'] = $year_filter;
        } else {
            $where['YEAR(purchase_date)'] = date('Y');
        }
        $urgent_po = $this->purchase->select('', $where);
        $urgent_po = $urgent_po?count($urgent_po):0;

        $total_monthly_sales = $this->invoice->get_total_monthly_sales($month_filter, $year_filter);
        $total_receivables = $this->invoice->get_total_receivables();
        $total_monthly_expenses = $this->purchase->get_total_monthly_expenses($month_filter, $year_filter);
        $total_monthly_capex = $this->purchase->get_total_monthly_capex($month_filter, $year_filter);

        $first_so_year = $this->invoice->get_first_year();
        
        $data = [
            'pending_po' => $pending_po,
            'urgent_po' => $urgent_po,
            'first_so_year' => $first_so_year,
            'total_monthly_sales' => $total_monthly_sales,
            'total_receivables' => $total_receivables,
            'total_monthly_expenses' => $total_monthly_expenses,
            'total_monthly_capex' => $total_monthly_capex,
            'selected_month' => $month_filter,
            'selected_year' => $year_filter
        ];

        $this->build_content('default', 'dressing_plant_dashboards/manager', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('purchase');
        $this->load->model('invoice');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}
