<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Eod extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('eod_p');
        $this->load->model('eod_d');
        $this->load->model('eod_s');
        $this->load->model('eod_bc');
        $this->load->model('eod_p_attachment');
        $this->load->model('eod_d_attachment');
        $this->load->model('eod_s_attachment');
        $this->load->model('eod_bc_attachment');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * EOD-P Manager
     */
    public function eodp_manager()
    {
        $this->title = "EOD-P";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/eod/p_manager'
        ];

        $this->build_content('default', 'eod/p_manager');
    }

    /**
     * EOD-D Manager
     */
    public function eodd_manager()
    {
        $this->title = "EOD-D";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/eod/d_manager'
        ];

        $this->build_content('default', 'eod/d_manager');
    }

    /**
     * EOD-S Manager
     */
    public function eods_manager()
    {
        $this->title = "EOD-S-I";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/eod/s_manager'
        ];

        $this->build_content('default', 'eod/s_manager');
    }

    /**
     * EOD-BC Manager
     */
    public function eodbc_manager()
    {
        $this->title = "EOD-BC-I";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/eod/bc_manager'
        ];

        $this->build_content('default', 'eod/bc_manager');
    }

    /**
     * EOD-P Add
     */
    public function p_add()
    {
        $this->title = 'EOD-P';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/alertify/build/alertify',
            'lib/input-autosize/dist/autosize',
            'js/eod/p_add'
        ];

        $eodp_max = $this->eod_p->select('IFNULL(MAX(id), 0) AS latest_id', ['is_deleted' => 0], 1);

        $data = [
            'latest_id'     => $eodp_max->latest_id
        ];

        $this->build_content('default', 'eod/p_add', $data);
    }

    /**
     * EOD-D Add
     */
    public function d_add()
    {
        $this->title = 'EOD-D';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/alertify/build/alertify',
            'lib/input-autosize/dist/autosize',
            'js/eod/d_add'
        ];

        $eodd_max = $this->eod_d->select('IFNULL(MAX(id), 0) AS latest_id', ['is_deleted' => 0], 1);

        $data = [
            'latest_id'     => $eodd_max->latest_id
        ];

        $this->build_content('default', 'eod/d_add', $data);
    }

    /**
     * EOD-S-I Add
     */
    public function s_add()
    {
        $this->title = 'EOD-S-I';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/alertify/build/alertify',
            'lib/input-autosize/dist/autosize',
            'js/eod/s_add'
        ];

        $eods_max = $this->eod_s->select('IFNULL(MAX(id), 0) AS latest_id', ['is_deleted' => 0], 1);

        $data = [
            'latest_id'     => $eods_max->latest_id
        ];

        $this->build_content('default', 'eod/s_add', $data);
    }

    /**
     * EOD-BC-I Add
     */
    public function bc_add()
    {
        $this->title = 'EOD-S-I';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/alertify/build/alertify',
            'lib/input-autosize/dist/autosize',
            'js/eod/bc_add'
        ];

        $eodbc_max = $this->eod_bc->select('IFNULL(MAX(id), 0) AS latest_id', ['is_deleted' => 0], 1);

        $data = [
            'latest_id'     => $eodbc_max->latest_id
        ];

        $this->build_content('default', 'eod/bc_add', $data);
    }

    /**
     * EOD-P Reprint
     */
    public function reprint_eodp($eodp_id)
    {
        ($eodp = $this->eod_p->get_by_id($eodp_id)) or show_404();

        $this->title = 'EOD-P';
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/eod/p_reprint'
        ];

        $data = [
            'eodp'  => $eodp
        ];
        
        $this->build_content('default', 'eod/p_reprint', $data);
    }

    /**
     * EOD-D Reprint
     */
    public function reprint_eodd($eodd_id)
    {
        ($eodd = $this->eod_d->get_by_id($eodd_id)) or show_404();

        $this->title = 'EOD-P';
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/eod/d_reprint'
        ];

        $data = [
            'eodd'  => $eodd
        ];
        
        $this->build_content('default', 'eod/d_reprint', $data);
    }

    /**
     * EOD-S Reprint
     */
    public function reprint_eods($eods_id)
    {
        ($eods = $this->eod_s->get_by_id($eods_id)) or show_404();

        $this->title = 'EOD-S-I';
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/eod/d_reprint'
        ];

        $data = [
            'eods'  => $eods
        ];
        
        $this->build_content('default', 'eod/s_reprint', $data);
    }

    /**
     * EOD-BC Reprint
     */
    public function reprint_eodbc($eodbc_id)
    {
        ($eodbc = $this->eod_bc->get_by_id($eodbc_id)) or show_404();

        $this->title = 'EOD-BC-I';
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/eod/bc_reprint'
        ];

        $data = [
            'eodbc'  => $eodbc
        ];
        
        $this->build_content('default', 'eod/bc_reprint', $data);
    }

    /**
     * Generate EOD-P
     */
    public function generate_eodp()
    {
        if (!$this->_attempt_generate_eodp()) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'EOD-P generated successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Generate EOD-D
     */
    public function generate_eodd()
    {
        if (!$this->_attempt_generate_eodd()) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'EOD-D generated successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Generate EOD-S
     */
    public function generate_eods()
    {
        if (!$this->_attempt_generate_eods()) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'EOD-S-I generated successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Generate EOD-BC
     */
    public function generate_eodbc()
    {
        if (!$this->_attempt_generate_eodbc()) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'EOD-BC-I generated successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Attempt Generate EOD-P
     */
    public function _attempt_generate_eodp()
    {
        $this->db->trans_begin();

        $values = [
            'added_by'  => $_SESSION['user']->id,
            'added_on'  => date('Y-m-d H:i:s')
        ];

        if(!$this->eod_p->insert($values)){
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Generate EOD-D
     */
    public function _attempt_generate_eodd()
    {
        $this->db->trans_begin();

        $values = [
            'added_by'  => $_SESSION['user']->id,
            'added_on'  => date('Y-m-d H:i:s')
        ];

        if(!$this->eod_d->insert($values)){
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Generate EOD-S
     */
    public function _attempt_generate_eods()
    {
        $this->db->trans_begin();

        $values = [
            'added_by'  => $_SESSION['user']->id,
            'added_on'  => date('Y-m-d H:i:s')
        ];

        if(!$this->eod_s->insert($values)){
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Generate EOD-BC
     */
    public function _attempt_generate_eodbc()
    {
        $this->db->trans_begin();

        $values = [
            'added_by'  => $_SESSION['user']->id,
            'added_on'  => date('Y-m-d H:i:s')
        ];

        if(!$this->eod_bc->insert($values)){
            $this->_error = 'server_error';
            $this->db->trans_rollback();
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Upload Attachments
     */
    public function upload_attachment()
    {
        $table = $this->input->post('table');
        $id = $this->input->post('id');

        $config = [
            'upload_path' => FCPATH . 'assets/eod/' . $table . '/',
            'allowed_types' => 'pdf|jpg|jpeg|png',
            'encrypt_name' => false,
            'overwrite' => false
        ];

        $new_path = FCPATH . 'assets/eod/' . $table . '/' . $id . '/';
        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        if ($this->_attempt_upload($config, 'attachments')) {
            $this->_move_uploaded($new_path);

            if ($this->_attempt_upload_attachment($id, $table)) {
                $response = [
                    'success' => true,
                    'message' => 'Uploaded Successfully'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => $this->_error_msg()
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        }

        echo json_encode($response);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Attempt Mark as Audited
     */
    protected function _attempt_upload_attachment($id, $table)
    {
        $this->db->trans_begin();

        $new_table = $table . '_attachment';

        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                $attachment_values = [
                    $table . '_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];

                if (!$attachment_id = $this->$new_table->insert($attachment_values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                    return false;
                }
            }
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {

        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }

    /**
     * Get Attachments by Parent ID
     */
    public function get_attachments($id, $table)
    {
        $new_table = $table . '_attachment';

        if (!$data = $this->$new_table->get_by_parent_id($id)) {
            $data = [];

            $response = [
                'message' => 'No data found.',
                'data'  => $data
            ];
        } else {
            $response = [
                'message' => 'EOD-S-I generated successfully.',
                'data' => $data
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Delete Attachment
     */
    public function delete_attachment($id, $table)
    {
        if (!$this->_attempt_delete_attachment($id, $table)) {
            http_response_code(400);
            $response = [
                'message' => 'Failed to delete attachment.'
            ];
        } else {
            $response = [
                'message' => 'Attachment deleted successfully.'
            ];
        }
        
        echo json_encode($response);
    }

    /**
     * Attempt Delete Attachment
     */
    protected function _attempt_delete_attachment($id, $table)
    {
        $this->db->trans_begin();

        $new_table = $table . '_attachment';

        $where = [
            'id'    => $id
        ];

        $values = [
            'is_deleted'    => 1,
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if(!$this->$new_table->update($where, $values)) {
            $this->db->trans_rollback();
            return false;
        }

        $this->db->trans_commit();
        return true;
    }
}