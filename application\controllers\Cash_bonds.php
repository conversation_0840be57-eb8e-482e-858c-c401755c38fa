<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Cash_bonds extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('account_type');
        $this->load->model('cash_bond');
        $this->load->model('cash_bond_attachment');
        $this->load->model('cash_bond_audit_attachment');
        $this->load->model('cash_bond_item');
        $this->load->model('invoice');
        $this->load->model('customer');
        $this->load->model('payment');
        $this->load->model('payment_detail');
        $this->load->model('paid_invoice');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('cash_bonds/manager'));
    }

    /**
     * Cash Bonds Manager
     */
    public function manager()
    {
        /**
         * Trigger for statuses can be seen in payment_audit table
         */

        $this->title = "Cash Bonds";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/cash_bonds/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'active'
        ];

        $this->build_content('default', 'cash_bonds/manager', $data);
    }

    /**
     * Add Cash Bond
     */
    public function add()
    {

        $cash_bond_item_ids = $this->_get_cash_bond_item_ids();
        $rules = array_merge($this->config->item('cash_bonds/add'), $this->_get_cash_bond_item_rules('', $cash_bond_item_ids));

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'jpg|jpeg|png|pdf|PDF',
            // 'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($rules) AND $this->_attempt_upload($config, 'attachments', true) AND $id = $this->_attempt_add() AND $this->_add_attachment($id)) {

            $new_path = FCPATH. 'assets/asylum/cash_bonds/'.$id.'/';
            $this->_move_uploaded($new_path);

            redirect(site_url('cash_bonds/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Cash Bond';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/cash_bonds/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $customers = $this->customer->get_customer_with_invoices();

            $where = [
                'is_deleted' => 0
            ];
            $cash_bonds = $this->cash_bond->select('', $where);

            $data = [
                'form_error' => $form_error,
                'cash_bond_item_ids' => $cash_bond_item_ids,
                'customers' => $customers,
                'items' => $cash_bonds,
                'status' => 'active'
            ];

            $this->build_content('default', 'cash_bonds/add', $data);
        }
    }

    /**
     * Cash Bond Print Preview
     */
    public function preview($cash_bond_id)
    {
        ($cash_bond = $this->cash_bond->get_by_id($cash_bond_id)) or show_404();

        $this->title = 'Print Preview: Cash Bond No. ' . $cash_bond->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/cash_bonds/preview'
        ];

        $where = [
            'id' => $cash_bond->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $cash_bond_items = $this->cash_bond_item->get_by_invoice($cash_bond_id);

        $data = [
            'cash_bond' => $cash_bond,
            'customer' => $customer,
            'cash_bond_items' => $cash_bond_items
        ];
        
        $this->build_content('default', 'cash_bonds/preview', $data);
    }

    /**
     * Audit
     */
    public function audit($cash_bond_id)
    {
        ($cash_bond = $this->cash_bond->get_by_id($cash_bond_id)) or show_404();

        $cash_bond_items = $this->cash_bond_item->get_by_invoice($cash_bond_id);
        $cash_bond_audit_attachment = $this->cash_bond_audit_attachment->select('', ['is_deleted' => 0, 'cash_bond_id' => $cash_bond_id], 1);

        $audit_remarks = $this->generate_audited();

        $cash_bond->total_paid = array_reduce($cash_bond_items, function ($current_total, $item) {
            return $current_total + $item->total_cb;
        });

        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/cash_bonds/audit'
        ];

        $data = [
            'cash_bond' => $cash_bond,
            'cash_bond_items' => $cash_bond_items,
            'cash_bond_audit_attachment' => $cash_bond_audit_attachment ? $cash_bond_audit_attachment->file_name : '',
            'audit_remarks' => $audit_remarks
        ];
        
        $this->build_content('default', 'cash_bonds/audit', $data);
    }

    /**
     * Mark Payment as Audited
     */
    public function mark_audited()
    { 
        $cash_bond_id = $this->input->post('cash_bond_id');
        $audit_remarks = $this->input->post('audit_remarks');
        $audit_status = $this->input->post('audit_status');
        
        // Define upload configuration
        $config = [
            'upload_path' => FCPATH . 'assets/cash_bonds/audit/',
            'allowed_types' => 'pdf|jpg|jpeg|png',
            'encrypt_name' => false,
            'overwrite' => false
        ];

        // var_dump($this->_attempt_upload($config, 'attachments'));die();

        // Attempt file upload
        if ($this->_attempt_upload($config, 'attachments')) {
            $new_path = FCPATH . 'assets/cash_bonds/audit/' . $cash_bond_id . '/';
            if (!is_dir($new_path)) {
                mkdir($new_path, 0755, true);
            }
            $this->_move_uploaded($new_path);

            if ($this->_attempt_mark_audited($cash_bond_id, $audit_remarks, $audit_status)) {
                $response = [
                    'success' => true,
                    'message' => 'Cash bond successfully marked as audited.'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => $this->_error_msg()
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        }

        // Return the response as JSON
        echo json_encode($response);
    }

    /**
     * Update Audited
     */
    public function update_mark_audited()
    {
        $cash_bond_id = $this->input->post('cash_bond_id');
        $audit_remarks = $this->input->post('audit_remarks');
        $audit_status = $this->input->post('audit_status');
        
        // Define upload configuration
        $config = [
            'upload_path' => FCPATH . 'assets/cash_bonds/audit/',
            'allowed_types' => 'pdf|jpg|jpeg|png',
            'encrypt_name' => false,
            'overwrite' => false
        ];

        // Attempt file upload
        if ($this->_attempt_upload($config, 'attachments')) {
            // Move uploaded files to the desired directory
            $new_path = FCPATH . 'assets/cash_bonds/audit/' . $cash_bond_id . '/';
            if (!is_dir($new_path)) {
                mkdir($new_path, 0755, true);
            }
            $this->_move_uploaded($new_path);

            // Update the database with the audit details
            if ($this->_attempt_update_mark_audited($cash_bond_id, $audit_remarks, $audit_status)) {
                $response = [
                    'success' => true,
                    'message' => 'Cash Bond successfully marked as audited.'
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => $this->_error_msg()
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        }

        // Return the response as JSON
        echo json_encode($response);
    }

    /**
     * Cash Bonds History by Customer
     */
    public function history($customer_id)
    {
        $this->title = 'Cash Bonds History';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/cash_bonds/history'
        ];

        $data = [
            'customer_id' => $customer_id
        ];

        $this->build_content('default', 'cash_bonds/history', $data);
    }

    /**
     * Add Cash Bond Payment
     */
    public function add_payment()
    {
        $cash_bond_ids = $this->_get_payment_inputs();
        $rules = array_merge(
            $this->config->item('cash_bonds/add_payment'),
            $this->_get_invoice_rules($cash_bond_ids),
            $this->_get_type_rules($this->input->post('type'))
        );

        $customer_id = $this->input->post('customer');

        if ($this->_validate_form($rules) && $payment_id = $this->_attempt_add_payment()) {
            redirect(site_url('payments/payment_proof/'.$payment_id));
        } else {
            $this->title = 'Add Payment';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/cash_bonds/add_payment'
            ];

            $customers = $this->cash_bond->get_customers() ?: [];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'cash_bond_ids' => $cash_bond_ids,
                'cash_bonds' => $this->cash_bond->get_partially_paid($customer_id)
            ];

            $this->build_content('default', 'cash_bonds/add_payment', $data);
        }
    }

    /**
     * Attempt Add Payment
     */
    protected function _attempt_add_payment()
    {
        $this->db->trans_begin();

        $paid_cash_bonds = $this->_get_cash_bond_data();

        if (empty($paid_cash_bonds)) {
            $this->_error = 'No Cash Bond Added';
            return false;
        }

        if (!$payment_id = $this->_add_payment($paid_cash_bonds)){
            $this->db->trans_rollback();
            $this->_error = 'Failed to add payment';
            return false;
        } elseif (!$this->_add_payment_detail($payment_id) ) {
            $this->db->trans_rollback();
            $this->_error = 'Failed to add payment details';
            return false;
        } elseif (!$this->_add_paid_invoices($payment_id, $paid_cash_bonds)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $payment_id;
        }

    }

    /**
     * Add Payment
     */
    protected function _add_payment($paid_invoices)
    {
        $type = $this->input->post('type');
        $amount = ($type === 'check') ? floatval(str_replace(',','',$this->input->post('check_amount'))) : floatval(str_replace(',','',$this->input->post('cash_amount')));
        $item_amount = array_reduce($paid_invoices, function ($current_total, $paid_invoice) {
            return $current_total + $paid_invoice['amount'];
        });


        if (number_format($amount,2) !== number_format($item_amount,2)) {
            $this->_error = ucfirst($type) . ' amount and total amount in table does not match.';
            return false;
        }

        $payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('payment_date'));

        $values = [
            'payment_date' => $payment_date->format('Y-m-d'),
            'total_paid' => $amount,
            'type' => $type,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];


        if (!$payment_id = $this->payment->insert($values)) {
            $this->_error = 'server_error';
            return false;
        }

        return $payment_id;
    }

    /**
     * Add Payment Detail
     */
    protected function _add_payment_detail($payment_id)
    {
        if ($this->input->post('type') === 'check') {
            $check_date = DateTime::createFromFormat('m/d/Y', $this->input->post('check_date'));
            $values = [
                'payment_id' => $payment_id,
                'check_no' => $this->input->post('check_no'),
                'check_date' => $check_date->format('Y-m-d'),
                'bank_name' => $this->input->post('bank_name'),
                'amount' => $this->input->post('check_amount'),
                'added_by' => $_SESSION['user']->id
            ];
            if (!$this->payment_detail->insert($values)) {
                $this->_error = 'server_error';
                return false;
            }
        }

        return true;
    }

    /**
     * Add Paid Invoices
     */
    protected function _add_paid_invoices($payment_id, $paid_invoices)
    {
        foreach ($paid_invoices as &$paid_invoice) {
            $paid_invoice['payment_id'] = $payment_id;
            $paid_invoice['doc_type'] = 'cash_bond';
            $paid_invoice['added_by'] = $_SESSION['user']->id;
            $paid_invoice['added_on'] = date('Y-m-d H:i:s');
        }
        unset($paid_invoice);

        if (!$is_inserted = $this->paid_invoice->insert_batch($paid_invoices)) {
            $this->_error = 'server_error';
            return false;
        }


        return $is_inserted;
    }

    /**
     * Get Invoice Data
     */
    protected function _get_cash_bond_data()
    {
        $data = [];

        $paid_invoice_ids = $this->_get_payment_inputs();
        foreach ($paid_invoice_ids as $paid_invoice_id) {
            $data[] = [
                'id' => is_numeric($paid_invoice_id) ? $paid_invoice_id : null,
                'invoice_id' => $this->input->post('cash_bond_' . $paid_invoice_id),
                'amount' => $this->input->post('amount_' . $paid_invoice_id),
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Get Input Suffices
     */
    protected function _get_payment_inputs()
    {
        $cash_bond_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'paid_cash_bond_') === 0) {
                $cash_bond_ids[] = $value;
            }
        }

        return $cash_bond_ids;
    }

    /**
     * Get Cash Bonds By Customer
     */
    public function partially_paid($customer_id)
    {
        $data = [
            'cash_bonds' => $this->cash_bond->get_partially_paid($customer_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Invoice Rules
     */
    protected function _get_invoice_rules($cash_bond_ids)
    {
        $rules = [];

        foreach ($cash_bond_ids as $cash_bond_id) {
            $rules[] = [
                'field' => 'paid_cash_bond_' . $cash_bond_id,
                'label' => 'Paid Cash Bond',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'cash_bond_' . $cash_bond_id,
                'label' => 'Cash Bond No',
                'rules' => 'required|trim|callback__is_invoice|callback__no_invoice_duplicate',
                'errors' => [
                    '_is_invoice' => 'Cash Bond is not found.',
                    '_no_invoice_duplicate' => 'Duplicate Cash Bond.'
                ]
            ];

            $where = [
                'id' => $this->input->post('cash_bond_' . $cash_bond_id)
            ];
            if (!$cash_bond = $this->cash_bond->select('', $where, 1) OR $cash_bond->grand_total < $cash_bond->paid_amount) {
                $balance = 0;
            } else {
                $balance = $cash_bond->grand_total - $cash_bond->paid_amount;
            }

            $rules[] = [
                'field' => 'amount_' . $cash_bond_id,
                'label' => 'Amount',
                'rules' => 'required|trim|numeric|less_than_equal_to[' . $balance . ']|greater_than[0]'
            ];
        }

        return $rules;
    }

    /**
     * Callback: Check if Cash Bond Exists
     */
    public function _is_invoice($invoice_id)
    {
        $invoice = $this->cash_bond->get_by_id($invoice_id);
        return !empty($invoice);
    }

    /**
     * Callback: Check if Invoice No Has No Duplicate
     */
    public function _no_invoice_duplicate($invoice_id)
    {
        $copies = 0;

        foreach ($_POST as $field => $value) {
            if (strpos($field, 'cash_bond_') === 0 && $value === $invoice_id) {
                $copies++;
            }
        }

        return $copies <= 1;
    }

    /**
     * Get Type Rules
     */
    protected function _get_type_rules($payment_type)
    {
        switch ($payment_type) {
            case 'cash':
                return [
                    [
                        'field' => 'cash_amount',
                        'label' => 'Cash Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            case 'check':
                return [
                    [
                        'field' => 'check_date',
                        'label' => 'Check Date',
                        'rules' => 'required|trim|is_date[m/d/Y]'
                    ],
                    [
                        'field' => 'check_no',
                        'label' => 'Check No',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'bank_name',
                        'label' => 'Bank Name',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'check_amount',
                        'label' => 'Check Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            default:
                return [];
        }
    }

    /**
     * Get Info for Cash Bond
     */
    public function info($cash_bond_id = null)
    {
        $data = [
            'cash_bond' => $this->cash_bond->get_for_payment($cash_bond_id)
        ];

        echo json_encode($data);
    }

    /**
     * Attempt Mark as Audited
     */
    protected function _attempt_mark_audited($cash_bond_id, $audit_remarks, $audit_status)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $cash_bond_id
        ];

        $values = [
            'audit_remarks' => $audit_remarks,
            'status' => $audit_status,
            'audited_by' => $_SESSION['user']->id,
            'audited_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->cash_bond->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }
        
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                $attachment_values = [
                    'cash_bond_id'  => $cash_bond_id,
                    'file_name'     => $uploaded['file_name'],
                    'added_by'      => $_SESSION['user']->id,
                    'added_on'      => date('Y-m-d H:i:s')
                ];

                if (!$this->cash_bond_audit_attachment->insert($attachment_values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                    return false;
                }
            }
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Attempt Update Audited
     */
    protected function _attempt_update_mark_audited($cash_bond_id, $audit_remarks, $audit_status)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $cash_bond_id
        ];

        $values = [
            'audit_remarks' => $audit_remarks,
            'status' => $audit_status,
            'audited_by' => $_SESSION['user']->id,
            'audited_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->cash_bond->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Handle attachments
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {

                $existing_audit_attachment = $this->cash_bond_audit_attachment->select('', ['cash_bond_id' => $cash_bond_id], 1);

                if ($existing_audit_attachment) {
                    $where = [
                        'cash_bond_id' => $cash_bond_id
                    ];
    
                    $attachment_values = [
                        'file_name' => $uploaded['file_name'],
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => date('Y-m-d H:i:s')
                    ];

                    $attachment_id = $this->cash_bond_audit_attachment->update($where, $attachment_values);
                    
                    if (!$attachment_id) {
                        $this->db->trans_rollback();
                        $this->_error = 'server_error';
                        return false;
                    }
                } else {
                    $attachment_values = [
                        'cash_bond_id' => $cash_bond_id,
                        'file_name' => $uploaded['file_name'],
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => date('Y-m-d H:i:s')
                    ];
                    
                    if (!$this->cash_bond_audit_attachment->insert($attachment_values)) {
                        $this->db->trans_rollback();
                        $this->_error = 'server_error';
                        return false;
                    }
                }
            }
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Cancel Cash Bond
     */

    public function cancel($cash_bond_id)
    {
        if (!$this->_attempt_cancel($cash_bond_id)) {
            $response = [
                'success' => false,
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'success' => true,
                'message' => 'Cash Bond cancelled successfully.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Cancel
     */
    protected function _attempt_cancel($cash_bond_id)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $cash_bond_id
        ];

        $values = [
            'status'        => 'cancelled',
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if(!$this->cash_bond->update($where, $values)){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Generate Audit No.
     */
    protected function generate_audited()
    {
        $month_year = date("m-Y");
        $audit_remarks_prefix = $month_year;
        
        $last_cash_bond = $this->cash_bond->select('MAX(counter) AS last_counter', ['is_deleted' => 0], 1);

        $next_audit_remarks_digits = str_pad((int)$last_cash_bond->last_counter + 1, 7, '0', STR_PAD_LEFT);
        $next_audit_remarks = $audit_remarks_prefix ." - ". $next_audit_remarks_digits;

        return $next_audit_remarks;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_id = $this->input->post('customer');

        if (!$cash_bond_items = $this->_get_cash_bond_item_data()) {
            $this->_error = 'No cash bond item added';
            return false;
        }

        if (!$cash_bond_id = $this->_generate_cash_bond($cash_bond_items, $customer_id) or !$this->_generate_cash_bond_items($cash_bond_id, $cash_bond_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $cash_bond_id;
        }
    }



    /**
     * Get Input Suffices
     */
    protected function _get_cash_bond_item_ids()
    {
        $cash_bond_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'cash_bond_item_') === 0) {
                $cash_bond_item_ids[] = $value;
            }
        }

        return $cash_bond_item_ids;
    }



    /**
     * Get Item Rules
     */
    protected function _get_cash_bond_item_rules($cash_bond_id, $cash_bond_item_ids)
    {
        $rules = [];

        foreach ($cash_bond_item_ids as $cash_bond_item_id) {
            $rules[] = [
                'field' => 'invoice_no_' . $cash_bond_item_id,
                'label' => 'Invoice No.',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'total_heads_no_' . $cash_bond_item_id,
                'label' => 'Total No. of Heads',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'unit_price_' . $cash_bond_item_id,
                'label' => 'Unit Price',
                'rules' => 'required|trim|numeric'
            ];

            $rules[] = [
                'field' => 'total_cb_' . $cash_bond_item_id,
                'label' => 'Total CB',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];
        }

        return $rules;
    }



    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);
            
            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Attempt add attachment
     */
    protected function _add_attachment($id)
    {
        if (!empty($this->_uploaded)) {
            $values = [];
            foreach ($this->_uploaded as $uploaded) {
                $values[] = [
                    'cash_bond_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];
            }
            if (!$attachment_id = $this->cash_bond_attachment->insert_batch($values)) {
                $this->_error = 'server_error';
                return false;
            }

            return $attachment_id;
        } else {
            return true;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_cash_bond_item_data()
    {
        $data = [];
        $cash_bond_item_ids = $this->_get_cash_bond_item_ids();

        foreach ($cash_bond_item_ids as $cash_bond_item_id) {
            $data[] = [
                'invoice_id'        => $this->input->post('invoice_no_'.$cash_bond_item_id),
                'total_heads_no'    => $this->input->post('total_heads_no_'.$cash_bond_item_id),
                'unit_price'        => $this->input->post('unit_price_'.$cash_bond_item_id),
                'total_cb'        => $this->input->post('total_cb_'.$cash_bond_item_id),
                'added_by'          => $_SESSION['user']->id,
                'added_on'          => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Generate Invoice
     */
    protected function _generate_cash_bond($cash_bond_items, $customer_id)
    {

        $cash_bond_date = DateTime::createFromFormat('m/d/Y', $this->input->post('cash_bond_date'));
        $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));

        $values = [
            'customer_id' => $customer_id,
            'cb_date' => $cash_bond_date->format('Y-m-d'),
            'due_date' => !empty($due_date) ? $due_date->format('Y-m-d') : null,
            'terms' => $this->input->post('terms'),
            'remarks' => $this->input->post('remarks') ?: null,
            'status' => 'active',
            'grand_total' => $this->input->post('grand_total'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        return $this->cash_bond->insert($values);
    }

    /**
     * Generate Invoice Items
     */
    protected function _generate_cash_bond_items($cash_bond_id, $cash_bond_items)
    {
        foreach ($cash_bond_items as $cash_bond_item) {
            $values = [
                'cash_bond_id'      => $cash_bond_id,
                'invoice_id'        => $cash_bond_item['invoice_id'],
                'total_heads_no'    => $cash_bond_item['total_heads_no'],
                'unit_price'        => $cash_bond_item['unit_price'],
                'total_cb'          => $cash_bond_item['total_cb'],
                'added_by'          => $cash_bond_item['added_by'],
                'added_on'          => $cash_bond_item['added_on']
            ];

            if (!$this->cash_bond_item->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {

        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }

    /**
     * Cash Bond report
     */
    public function report()
    {
        $this->title = 'Cash Bond Receivable Aging';
        
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify',
            'lib/select2/dist/css/select2',
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/cash_bonds/report'
        ];

        $data = [
            'customers' => $this->customer->select('', ['is_deleted' => 0])
        ];

        $this->build_content('default', 'cash_bonds/report', $data);
    }


    /**
     * Release Cash Bond
     */
    public function release($cash_bond_id = null)
    {
        if($cash_bond_id === null){
            $customer_id = null;
        }else{
            ($cash_bond = $this->cash_bond->get_by_id($cash_bond_id)) or show_404();
            $customer_id = $cash_bond->customer_id;
            
        }

        $cash_bond_ids = $this->_get_cash_bond_ids();
        $cash_bond_item_ids = $this->_get_cash_bond_item_ids();
        // echo "<script>console.log('cash_bond_ids: " . json_encode($cash_bond_ids) . "');</script>";


        $rules = array_merge($this->config->item('cash_bonds/release'), $this->_get_cash_bond_rules('', $cash_bond_item_ids));
        // echo "<script>console.log('_validate_form: " . json_encode($rules) . "');</script>";
        // echo "<script>console.log('_validate_form: " . json_encode($this->_validate_form($rules)) . "');</script>";
        // echo "<script>console.log('_attempt_release: " . json_encode($this->_attempt_release($customer_id, $cash_bond_ids)) . "');</script>";
        if ($this->_validate_form($rules) AND $this->_attempt_release($customer_id,$cash_bond_ids)) {      

            redirect(site_url('cash_bonds/manager'));

        } else {

            $this->title = 'Release : Cash Bond';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/cash_bonds/release'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            if($customer_id ===null){
                $customers = $this->customer->get_customer_with_invoices();
                $where = [
                    'is_deleted' => 0
                ];
                $cash_bonds = $this->cash_bond->select('', $where);
                $customer = null;
                $cash_bond = null;
            }else{
                $where = [
                    'id' => $customer_id
                ];
                $customer = $this->customer->select('', $where, 1);
                $cash_bonds = $this->cash_bond->get_cash_bonds_by_customer($customer_id);
                $customers = null;
                // echo "<script>console.log(" . json_encode($cash_bond) . ");</script>";
            }
           
            $data = [
                'cash_bond' => $cash_bond,
                'form_error' => $form_error,
                'cash_bond_item_ids' => $cash_bond_item_ids,
                'customer' => $customer,
                'customers'=> $customers,
                'items' => $cash_bonds
            ];

            $this->build_content('default', 'cash_bonds/release', $data);
        }
    }

    /**
     * Attempt Release
     */
    protected function _attempt_release($customer_id, $cash_bond_ids)
    {
        $this->db->trans_begin();

        if($customer_id === null){
            $customer_id = $this->input->post('customer');
        }else{
            $this->db->where('customer_id', $customer_id);
        }
        $this->db->where_in('id', $cash_bond_ids);

        $values = [
            'status'        => 'released',
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if (!$this->db->update('cash_bond', $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Get Input Cash bonds
     */
    protected function _get_cash_bond_ids()
    {
        $cash_bond_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'invoice_no_') === 0) {
                $cash_bond_ids[] = $value;
            }
        }

        return $cash_bond_ids;
    }

    /**
     * Get Cash bond Rules
     */
    protected function _get_cash_bond_rules($cash_bond_id, $cash_bond_item_ids)
    {
        $rules = [];

        foreach ($cash_bond_item_ids as $cash_bond_item_id) {
            $rules[] = [
                'field' => 'invoice_no_' . $cash_bond_item_id,
                'label' => 'Cash Bond No.',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'cb_date_' . $cash_bond_item_id,
                'label' => 'CB Date',
                'rules' => 'required'
            ];

            $rules[] = [
                'field' => 'total_cb_' . $cash_bond_item_id,
                'label' => 'Amount',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];
        }

        return $rules;
    }

    public function customer_cash_bonds($customer_id)
    {
        $data = [
            'cash_bonds' => $this->cash_bond->get_cash_bonds_by_customer($customer_id)
        ];

        echo json_encode($data);
    }
}