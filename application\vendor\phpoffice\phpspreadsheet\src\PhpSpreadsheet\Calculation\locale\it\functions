############################################################
##
## PhpSpreadsheet - function name translations
##
## Italiano (Italian)
##
############################################################


##
## Funzioni cubo (Cube Functions)
##
CUBEKPIMEMBER = MEMBRO.KPI.CUBO
CUBEMEMBER = MEMBRO.CUBO
CUBEMEMBERPROPERTY = PROPRIETÀ.MEMBRO.CUBO
CUBERANKEDMEMBER = MEMBRO.CUBO.CON.RANGO
CUBESET = SET.CUBO
CUBESETCOUNT = CONTA.SET.CUBO
CUBEVALUE = VALORE.CUBO

##
## Funzioni di database (Database Functions)
##
DAVERAGE = DB.MEDIA
DCOUNT = DB.CONTA.NUMERI
DCOUNTA = DB.CONTA.VALORI
DGET = DB.VALORI
DMAX = DB.MAX
DMIN = DB.MIN
DPRODUCT = DB.PRODOTTO
DSTDEV = DB.DEV.ST
DSTDEVP = DB.DEV.ST.POP
DSUM = DB.SOMMA
DVAR = DB.VAR
DVARP = DB.VAR.POP

##
## Funzioni data e ora (Date & Time Functions)
##
DATE = DATA
DATEDIF = DATA.DIFF
DATESTRING = DATA.STRINGA
DATEVALUE = DATA.VALORE
DAY = GIORNO
DAYS = GIORNI
DAYS360 = GIORNO360
EDATE = DATA.MESE
EOMONTH = FINE.MESE
HOUR = ORA
ISOWEEKNUM = NUM.SETTIMANA.ISO
MINUTE = MINUTO
MONTH = MESE
NETWORKDAYS = GIORNI.LAVORATIVI.TOT
NETWORKDAYS.INTL = GIORNI.LAVORATIVI.TOT.INTL
NOW = ADESSO
SECOND = SECONDO
THAIDAYOFWEEK = THAIGIORNODELLASETTIMANA
THAIMONTHOFYEAR = THAIMESEDELLANNO
THAIYEAR = THAIANNO
TIME = ORARIO
TIMEVALUE = ORARIO.VALORE
TODAY = OGGI
WEEKDAY = GIORNO.SETTIMANA
WEEKNUM = NUM.SETTIMANA
WORKDAY = GIORNO.LAVORATIVO
WORKDAY.INTL = GIORNO.LAVORATIVO.INTL
YEAR = ANNO
YEARFRAC = FRAZIONE.ANNO

##
## Funzioni ingegneristiche (Engineering Functions)
##
BESSELI = BESSEL.I
BESSELJ = BESSEL.J
BESSELK = BESSEL.K
BESSELY = BESSEL.Y
BIN2DEC = BINARIO.DECIMALE
BIN2HEX = BINARIO.HEX
BIN2OCT = BINARIO.OCT
BITAND = BITAND
BITLSHIFT = BIT.SPOSTA.SX
BITOR = BITOR
BITRSHIFT = BIT.SPOSTA.DX
BITXOR = BITXOR
COMPLEX = COMPLESSO
CONVERT = CONVERTI
DEC2BIN = DECIMALE.BINARIO
DEC2HEX = DECIMALE.HEX
DEC2OCT = DECIMALE.OCT
DELTA = DELTA
ERF = FUNZ.ERRORE
ERF.PRECISE = FUNZ.ERRORE.PRECISA
ERFC = FUNZ.ERRORE.COMP
ERFC.PRECISE = FUNZ.ERRORE.COMP.PRECISA
GESTEP = SOGLIA
HEX2BIN = HEX.BINARIO
HEX2DEC = HEX.DECIMALE
HEX2OCT = HEX.OCT
IMABS = COMP.MODULO
IMAGINARY = COMP.IMMAGINARIO
IMARGUMENT = COMP.ARGOMENTO
IMCONJUGATE = COMP.CONIUGATO
IMCOS = COMP.COS
IMCOSH = COMP.COSH
IMCOT = COMP.COT
IMCSC = COMP.CSC
IMCSCH = COMP.CSCH
IMDIV = COMP.DIV
IMEXP = COMP.EXP
IMLN = COMP.LN
IMLOG10 = COMP.LOG10
IMLOG2 = COMP.LOG2
IMPOWER = COMP.POTENZA
IMPRODUCT = COMP.PRODOTTO
IMREAL = COMP.PARTE.REALE
IMSEC = COMP.SEC
IMSECH = COMP.SECH
IMSIN = COMP.SEN
IMSINH = COMP.SENH
IMSQRT = COMP.RADQ
IMSUB = COMP.DIFF
IMSUM = COMP.SOMMA
IMTAN = COMP.TAN
OCT2BIN = OCT.BINARIO
OCT2DEC = OCT.DECIMALE
OCT2HEX = OCT.HEX

##
## Funzioni finanziarie (Financial Functions)
##
ACCRINT = INT.MATURATO.PER
ACCRINTM = INT.MATURATO.SCAD
AMORDEGRC = AMMORT.DEGR
AMORLINC = AMMORT.PER
COUPDAYBS = GIORNI.CED.INIZ.LIQ
COUPDAYS = GIORNI.CED
COUPDAYSNC = GIORNI.CED.NUOVA
COUPNCD = DATA.CED.SUCC
COUPNUM = NUM.CED
COUPPCD = DATA.CED.PREC
CUMIPMT = INT.CUMUL
CUMPRINC = CAP.CUM
DB = AMMORT.FISSO
DDB = AMMORT
DISC = TASSO.SCONTO
DOLLARDE = VALUTA.DEC
DOLLARFR = VALUTA.FRAZ
DURATION = DURATA
EFFECT = EFFETTIVO
FV = VAL.FUT
FVSCHEDULE = VAL.FUT.CAPITALE
INTRATE = TASSO.INT
IPMT = INTERESSI
IRR = TIR.COST
ISPMT = INTERESSE.RATA
MDURATION = DURATA.M
MIRR = TIR.VAR
NOMINAL = NOMINALE
NPER = NUM.RATE
NPV = VAN
ODDFPRICE = PREZZO.PRIMO.IRR
ODDFYIELD = REND.PRIMO.IRR
ODDLPRICE = PREZZO.ULTIMO.IRR
ODDLYIELD = REND.ULTIMO.IRR
PDURATION = DURATA.P
PMT = RATA
PPMT = P.RATA
PRICE = PREZZO
PRICEDISC = PREZZO.SCONT
PRICEMAT = PREZZO.SCAD
PV = VA
RATE = TASSO
RECEIVED = RICEV.SCAD
RRI = RIT.INVEST.EFFETT
SLN = AMMORT.COST
SYD = AMMORT.ANNUO
TBILLEQ = BOT.EQUIV
TBILLPRICE = BOT.PREZZO
TBILLYIELD = BOT.REND
VDB = AMMORT.VAR
XIRR = TIR.X
XNPV = VAN.X
YIELD = REND
YIELDDISC = REND.TITOLI.SCONT
YIELDMAT = REND.SCAD

##
## Funzioni relative alle informazioni (Information Functions)
##
CELL = CELLA
ERROR.TYPE = ERRORE.TIPO
INFO = AMBIENTE.INFO
ISBLANK = VAL.VUOTO
ISERR = VAL.ERR
ISERROR = VAL.ERRORE
ISEVEN = VAL.PARI
ISFORMULA = VAL.FORMULA
ISLOGICAL = VAL.LOGICO
ISNA = VAL.NON.DISP
ISNONTEXT = VAL.NON.TESTO
ISNUMBER = VAL.NUMERO
ISODD = VAL.DISPARI
ISREF = VAL.RIF
ISTEXT = VAL.TESTO
N = NUM
NA = NON.DISP
SHEET = FOGLIO
SHEETS = FOGLI
TYPE = TIPO

##
## Funzioni logiche (Logical Functions)
##
AND = E
FALSE = FALSO
IF = SE
IFERROR = SE.ERRORE
IFNA = SE.NON.DISP.
IFS = PIÙ.SE
NOT = NON
OR = O
SWITCH = SWITCH
TRUE = VERO
XOR = XOR

##
## Funzioni di ricerca e di riferimento (Lookup & Reference Functions)
##
ADDRESS = INDIRIZZO
AREAS = AREE
CHOOSE = SCEGLI
COLUMN = RIF.COLONNA
COLUMNS = COLONNE
FORMULATEXT = TESTO.FORMULA
GETPIVOTDATA = INFO.DATI.TAB.PIVOT
HLOOKUP = CERCA.ORIZZ
HYPERLINK = COLLEG.IPERTESTUALE
INDEX = INDICE
INDIRECT = INDIRETTO
LOOKUP = CERCA
MATCH = CONFRONTA
OFFSET = SCARTO
ROW = RIF.RIGA
ROWS = RIGHE
RTD = DATITEMPOREALE
TRANSPOSE = MATR.TRASPOSTA
VLOOKUP = CERCA.VERT

##
## Funzioni matematiche e trigonometriche (Math & Trig Functions)
##
ABS = ASS
ACOS = ARCCOS
ACOSH = ARCCOSH
ACOT = ARCCOT
ACOTH = ARCCOTH
AGGREGATE = AGGREGA
ARABIC = ARABO
ASIN = ARCSEN
ASINH = ARCSENH
ATAN = ARCTAN
ATAN2 = ARCTAN.2
ATANH = ARCTANH
BASE = BASE
CEILING.MATH = ARROTONDA.ECCESSO.MAT
CEILING.PRECISE = ARROTONDA.ECCESSO.PRECISA
COMBIN = COMBINAZIONE
COMBINA = COMBINAZIONE.VALORI
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMALE
DEGREES = GRADI
ECMA.CEILING = ECMA.ARROTONDA.ECCESSO
EVEN = PARI
EXP = EXP
FACT = FATTORIALE
FACTDOUBLE = FATT.DOPPIO
FLOOR.MATH = ARROTONDA.DIFETTO.MAT
FLOOR.PRECISE = ARROTONDA.DIFETTO.PRECISA
GCD = MCD
INT = INT
ISO.CEILING = ISO.ARROTONDA.ECCESSO
LCM = MCM
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MATR.DETERM
MINVERSE = MATR.INVERSA
MMULT = MATR.PRODOTTO
MOD = RESTO
MROUND = ARROTONDA.MULTIPLO
MULTINOMIAL = MULTINOMIALE
MUNIT = MATR.UNIT
ODD = DISPARI
PI = PI.GRECO
POWER = POTENZA
PRODUCT = PRODOTTO
QUOTIENT = QUOZIENTE
RADIANS = RADIANTI
RAND = CASUALE
RANDBETWEEN = CASUALE.TRA
ROMAN = ROMANO
ROUND = ARROTONDA
ROUNDBAHTDOWN = ARROTBAHTGIU
ROUNDBAHTUP = ARROTBAHTSU
ROUNDDOWN = ARROTONDA.PER.DIF
ROUNDUP = ARROTONDA.PER.ECC
SEC = SEC
SECH = SECH
SERIESSUM = SOMMA.SERIE
SIGN = SEGNO
SIN = SEN
SINH = SENH
SQRT = RADQ
SQRTPI = RADQ.PI.GRECO
SUBTOTAL = SUBTOTALE
SUM = SOMMA
SUMIF = SOMMA.SE
SUMIFS = SOMMA.PIÙ.SE
SUMPRODUCT = MATR.SOMMA.PRODOTTO
SUMSQ = SOMMA.Q
SUMX2MY2 = SOMMA.DIFF.Q
SUMX2PY2 = SOMMA.SOMMA.Q
SUMXMY2 = SOMMA.Q.DIFF
TAN = TAN
TANH = TANH
TRUNC = TRONCA

##
## Funzioni statistiche (Statistical Functions)
##
AVEDEV = MEDIA.DEV
AVERAGE = MEDIA
AVERAGEA = MEDIA.VALORI
AVERAGEIF = MEDIA.SE
AVERAGEIFS = MEDIA.PIÙ.SE
BETA.DIST = DISTRIB.BETA.N
BETA.INV = INV.BETA.N
BINOM.DIST = DISTRIB.BINOM.N
BINOM.DIST.RANGE = INTERVALLO.DISTRIB.BINOM.N.
BINOM.INV = INV.BINOM
CHISQ.DIST = DISTRIB.CHI.QUAD
CHISQ.DIST.RT = DISTRIB.CHI.QUAD.DS
CHISQ.INV = INV.CHI.QUAD
CHISQ.INV.RT = INV.CHI.QUAD.DS
CHISQ.TEST = TEST.CHI.QUAD
CONFIDENCE.NORM = CONFIDENZA.NORM
CONFIDENCE.T = CONFIDENZA.T
CORREL = CORRELAZIONE
COUNT = CONTA.NUMERI
COUNTA = CONTA.VALORI
COUNTBLANK = CONTA.VUOTE
COUNTIF = CONTA.SE
COUNTIFS = CONTA.PIÙ.SE
COVARIANCE.P = COVARIANZA.P
COVARIANCE.S = COVARIANZA.C
DEVSQ = DEV.Q
EXPON.DIST = DISTRIB.EXP.N
F.DIST = DISTRIBF
F.DIST.RT = DISTRIB.F.DS
F.INV = INVF
F.INV.RT = INV.F.DS
F.TEST = TESTF
FISHER = FISHER
FISHERINV = INV.FISHER
FORECAST.ETS = PREVISIONE.ETS
FORECAST.ETS.CONFINT = PREVISIONE.ETS.INTCONF
FORECAST.ETS.SEASONALITY = PREVISIONE.ETS.STAGIONALITÀ
FORECAST.ETS.STAT = PREVISIONE.ETS.STAT
FORECAST.LINEAR = PREVISIONE.LINEARE
FREQUENCY = FREQUENZA
GAMMA = GAMMA
GAMMA.DIST = DISTRIB.GAMMA.N
GAMMA.INV = INV.GAMMA.N
GAMMALN = LN.GAMMA
GAMMALN.PRECISE = LN.GAMMA.PRECISA
GAUSS = GAUSS
GEOMEAN = MEDIA.GEOMETRICA
GROWTH = CRESCITA
HARMEAN = MEDIA.ARMONICA
HYPGEOM.DIST = DISTRIB.IPERGEOM.N
INTERCEPT = INTERCETTA
KURT = CURTOSI
LARGE = GRANDE
LINEST = REGR.LIN
LOGEST = REGR.LOG
LOGNORM.DIST = DISTRIB.LOGNORM.N
LOGNORM.INV = INV.LOGNORM.N
MAX = MAX
MAXA = MAX.VALORI
MAXIFS = MAX.PIÙ.SE
MEDIAN = MEDIANA
MIN = MIN
MINA = MIN.VALORI
MINIFS = MIN.PIÙ.SE
MODE.MULT = MODA.MULT
MODE.SNGL = MODA.SNGL
NEGBINOM.DIST = DISTRIB.BINOM.NEG.N
NORM.DIST = DISTRIB.NORM.N
NORM.INV = INV.NORM.N
NORM.S.DIST = DISTRIB.NORM.ST.N
NORM.S.INV = INV.NORM.S
PEARSON = PEARSON
PERCENTILE.EXC = ESC.PERCENTILE
PERCENTILE.INC = INC.PERCENTILE
PERCENTRANK.EXC = ESC.PERCENT.RANGO
PERCENTRANK.INC = INC.PERCENT.RANGO
PERMUT = PERMUTAZIONE
PERMUTATIONA = PERMUTAZIONE.VALORI
PHI = PHI
POISSON.DIST = DISTRIB.POISSON
PROB = PROBABILITÀ
QUARTILE.EXC = ESC.QUARTILE
QUARTILE.INC = INC.QUARTILE
RANK.AVG = RANGO.MEDIA
RANK.EQ = RANGO.UG
RSQ = RQ
SKEW = ASIMMETRIA
SKEW.P = ASIMMETRIA.P
SLOPE = PENDENZA
SMALL = PICCOLO
STANDARDIZE = NORMALIZZA
STDEV.P = DEV.ST.P
STDEV.S = DEV.ST.C
STDEVA = DEV.ST.VALORI
STDEVPA = DEV.ST.POP.VALORI
STEYX = ERR.STD.YX
T.DIST = DISTRIB.T.N
T.DIST.2T = DISTRIB.T.2T
T.DIST.RT = DISTRIB.T.DS
T.INV = INVT
T.INV.2T = INV.T.2T
T.TEST = TESTT
TREND = TENDENZA
TRIMMEAN = MEDIA.TRONCATA
VAR.P = VAR.P
VAR.S = VAR.C
VARA = VAR.VALORI
VARPA = VAR.POP.VALORI
WEIBULL.DIST = DISTRIB.WEIBULL
Z.TEST = TESTZ

##
## Funzioni di testo (Text Functions)
##
BAHTTEXT = BAHTTESTO
CHAR = CODICE.CARATT
CLEAN = LIBERA
CODE = CODICE
CONCAT = CONCAT
DOLLAR = VALUTA
EXACT = IDENTICO
FIND = TROVA
FIXED = FISSO
ISTHAIDIGIT = ÈTHAICIFRA
LEFT = SINISTRA
LEN = LUNGHEZZA
LOWER = MINUSC
MID = STRINGA.ESTRAI
NUMBERSTRING = NUMERO.STRINGA
NUMBERVALUE = NUMERO.VALORE
PHONETIC = FURIGANA
PROPER = MAIUSC.INIZ
REPLACE = RIMPIAZZA
REPT = RIPETI
RIGHT = DESTRA
SEARCH = RICERCA
SUBSTITUTE = SOSTITUISCI
T = T
TEXT = TESTO
TEXTJOIN = TESTO.UNISCI
THAIDIGIT = THAICIFRA
THAINUMSOUND = THAINUMSUONO
THAINUMSTRING = THAISZÁMKAR
THAISTRINGLENGTH = THAILUNGSTRINGA
TRIM = ANNULLA.SPAZI
UNICHAR = CARATT.UNI
UNICODE = UNICODE
UPPER = MAIUSC
VALUE = VALORE

##
## Funzioni Web (Web Functions)
##
ENCODEURL = CODIFICA.URL
FILTERXML = FILTRO.XML
WEBSERVICE = SERVIZIO.WEB

##
## Funzioni di compatibilità (Compatibility Functions)
##
BETADIST = DISTRIB.BETA
BETAINV = INV.BETA
BINOMDIST = DISTRIB.BINOM
CEILING = ARROTONDA.ECCESSO
CHIDIST = DISTRIB.CHI
CHIINV = INV.CHI
CHITEST = TEST.CHI
CONCATENATE = CONCATENA
CONFIDENCE = CONFIDENZA
COVAR = COVARIANZA
CRITBINOM = CRIT.BINOM
EXPONDIST = DISTRIB.EXP
FDIST = DISTRIB.F
FINV = INV.F
FLOOR = ARROTONDA.DIFETTO
FORECAST = PREVISIONE
FTEST = TEST.F
GAMMADIST = DISTRIB.GAMMA
GAMMAINV = INV.GAMMA
HYPGEOMDIST = DISTRIB.IPERGEOM
LOGINV = INV.LOGNORM
LOGNORMDIST = DISTRIB.LOGNORM
MODE = MODA
NEGBINOMDIST = DISTRIB.BINOM.NEG
NORMDIST = DISTRIB.NORM
NORMINV = INV.NORM
NORMSDIST = DISTRIB.NORM.ST
NORMSINV = INV.NORM.ST
PERCENTILE = PERCENTILE
PERCENTRANK = PERCENT.RANGO
POISSON = POISSON
QUARTILE = QUARTILE
RANK = RANGO
STDEV = DEV.ST
STDEVP = DEV.ST.POP
TDIST = DISTRIB.T
TINV = INV.T
TTEST = TEST.T
VAR = VAR
VARP = VAR.POP
WEIBULL = WEIBULL
ZTEST = TEST.Z
