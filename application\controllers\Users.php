<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Users extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('users/manager'));
    }

    /**
     * Add user main
     */
    public function add()
    {
        $rules = $this->config->item('users/add');

        if ($this->_validate_form($rules) and $this->_attempt_upload() and $user_id = $this->_attempt_add()) {

            $new_path = FCPATH . 'assets/asylum/signatures/' . $user_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('users/manager'));
        } else {

            $this->_delete_uploaded();
            $this->title = 'Add User';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'roles' => $this->role->select()
            ];
            $this->build_content('default', 'users/add', $data);
        }
    }

    /**
     * Edit user main
     */
    public function edit($user_id = null)
    {
        $where = [
            'id' => $user_id,
            'is_deleted' => 0
        ];
        $user = $this->user->select('', $where, 1) or show_404();
        $rules = $this->config->item('users/edit');

        if ($this->_validate_form($rules) and $this->_attempt_upload() and $this->_attempt_edit($user)) {

            $new_path = FCPATH . 'assets/asylum/signatures/' . $user_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('users/manager'));
        } else {
            $this->_delete_uploaded();

            $this->title = 'Edit Profile';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/users/crud'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'user' => $user,
                'roles' => $this->role->select()
            ];
            $this->build_content('default', 'users/edit', $data);
        }
    }

    /**
     * Delete user main
     */
    public function delete($user_id = null)
    {
        $where = [
            'id' => $user_id,
            'is_deleted' => 0
        ];
        if (!$user = $this->user->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'User is not found.'
            ];
        } elseif (!$this->_attempt_delete($user)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'User is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * User reset password main
     */
    public function reset_password($user_id = null)
    {
        $where = [
            'id' => $user_id,
            'is_deleted' => 0
        ];
        $user = $this->user->select('', $where, 1) or show_404();
        $rules = $this->config->item('users/reset_password');

        if ($this->_validate_form($rules) && $this->_attempt_reset_password($user)) {
            redirect(site_url('users/manager'));
        } else {
            $this->title = 'Reset Password';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'user' => $user
            ];
            $this->build_content('default', 'users/reset_password', $data);
        }
    }

    /**
     * Users manager main
     */
    public function manager()
    {
        $this->title = 'Users';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/users/manager'
        ];
        $this->build_content('default', 'users/manager');
    }

    /**
     * Load pre-requisite models, config files, and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('user');
        $this->load->model('role');
        $this->lang->load('error_messages', 'english');
        $this->load->library('upload');
        $this->config->load('validation_rules');
    }

    /**
     * Validate form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return $this->upload->display_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

       /**
     * Upload File to Server
     */
    protected function _attempt_upload()
    {
        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'png|PNG',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'],0755,true);
        }

        $this->upload->initialize($config);

        if (!$upload_ok = $this->upload->do_upload('signature')) {
            $this->_error = 'upload_error';
        } else {
            $this->_uploaded = $this->upload->data();
        }

        return $upload_ok || empty($_FILES);
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            unlink($this->_uploaded['full_path']);
        }
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        rename($this->_uploaded['full_path'], $new_path . $this->_uploaded['file_name']);
        $this->index_html($new_path);
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }

    /**
     * Callback: Check if role is valid
     */
    public function _is_role($role_id)
    {
        $where = [
            'id' => $role_id
        ];
        $role = $this->role->select('', $where, 1);

        return !empty($role);
    }

    /**
     * Attempt add
     */
    protected function _attempt_add()
    {
        $values = [
            'username' => $this->input->post('username'),
            'password' => password_hash($this->input->post('password'), PASSWORD_BCRYPT),
            'full_name' => $this->input->post('full_name'),
            'role_id' => $this->input->post('role'),
            'added_by' => $_SESSION['user']->id
        ];

        if(!empty($this->_uploaded)){
            $values['sig_url'] = $this->_uploaded['file_name'];
        }

        if (!$user_id = $this->user->insert($values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_username';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $user_id;
    }

    /**
     * Attempt edit
     */
    protected function _attempt_edit($user)
    {
        $where = [
            'id' => $user->id
        ];
        $values = [
            'username' => $this->input->post('username'),
            'full_name' => $this->input->post('full_name'),
            'role_id' => $this->input->post('role'),
            'updated_by' => $_SESSION['user']->id
        ];

        if(!empty($this->_uploaded)){
            $values['sig_url'] = $this->_uploaded['file_name'];
        }

        if (!$is_updated = $this->user->update($where, $values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_username';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $is_updated;
    }

    /**
     * Attempt delete
     */
    protected function _attempt_delete($user)
    {
        $where = [
            'id' => $user->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->user->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Reset Password
     */
    protected function _attempt_reset_password($user)
    {
        $where = [
            'id' => $user->id
        ];
        $values = [
            'password' => password_hash($this->input->post('new_password'), PASSWORD_BCRYPT),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_reset = $this->user->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_reset;
    }
}
