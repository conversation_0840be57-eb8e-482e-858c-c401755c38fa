<!DOCTYPE HTML>
<html>
<head>
<script type="text/javascript" src="https://canvasjs.com/assets/script/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../../canvasjs.stock.min.js"></script>
<script type="text/javascript">
window.onload = function () {
  var dataPoints1 = [], dataPoints2 = [];
  var stockChart = new CanvasJS.StockChart("chartContainer",{
    theme: "light2",
    exportEnabled: true,
    title:{
      text:"Ethereum Price"
    },
    charts: [{
      axisY: {
        prefix: "$"
      },
      data: [{
        type: "candlestick",
        yValueFormatString: "$#,###.00",
        dataPoints : dataPoints1
      }]
    }],
    navigator: {
      data: [{
        color: "#6D78AD",
        dataPoints: dataPoints2
      }],
      slider: {
        minimum: new Date(2018, 04, 01),
        maximum: new Date(2018, 06, 01)
      }
    }
  });
  $.getJSON("https://canvasjs.com/data/docs/ethusd2018.json", function(data) {
    for(var i = 0; i < data.length; i++){
      dataPoints1.push({x: new Date(data[i].date), y: [Number(data[i].open), Number(data[i].high), Number(data[i].low), Number(data[i].close)]});
      dataPoints2.push({x: new Date(data[i].date), y: Number(data[i].close)});
    }
    stockChart.render();
  });
}
</script>
</head>
<body>
<div id="chartContainer" style="height: 400px; max-width: 920px; margin: 0px auto;"></div>
</body>
</html>