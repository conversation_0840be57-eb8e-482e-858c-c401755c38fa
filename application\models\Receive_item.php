<?php
class Receive_item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'receive_item';
        parent::__construct();
    }

    /**
     * Get Item By Receive
     */
    public function get_by_receive($receive_id)
    {
        $sql = <<<EOT
SELECT receive_item.*,
    item.name, item.unit, purchase_item.price,
    purchase_item.amount, purchase_item.qty AS ordered,
    IFNULL(all_received.qty, 0) AS received
FROM receive_item
LEFT JOIN purchase_item ON purchase_item.id = receive_item.purchase_item_id
LEFT JOIN (
    SELECT receive_item.purchase_item_id, IFNULL(SUM(receive_item.qty), 0) AS qty
    FROM receive_item
    LEFT JOIN receive ON receive.id = receive_item.receive_id
    WHERE receive.is_deleted = 0
        AND receive_item.is_deleted = 0
    GROUP BY receive_item.purchase_item_id
) all_received ON all_received.purchase_item_id = purchase_item.id
LEFT JOIN item ON item.id = purchase_item.item_id
WHERE receive_item.receive_id = ?
    AND receive_item.is_deleted = 0
HAVING receive_item.qty > 0
EOT;
        $binds = array($receive_id);

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Generate Receive Item
     */
    public function generate($receive_id, $purchase_item_id, $item_id, $qty, $added_by)
    {
        $sql =<<<EOT
INSERT INTO receive_item(receive_id, purchase_item_id, item_id, qty, added_by) (
    SELECT receive.id AS receive_id, purchase_item.id AS purchase_item_id, ? AS item_id, ? AS qty, ? AS added_by
    FROM receive
    CROSS JOIN purchase_item
    WHERE receive.id = ?
        AND receive.is_deleted = 0
        AND purchase_item.id = ?
        AND purchase_item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    receive_item.qty = VALUES(receive_item.qty),
    receive_item.updated_by = VALUES(receive_item.added_by),
    receive_item.is_deleted = 0
EOT;
        $binds = array($item_id, $qty, $added_by, $receive_id, $purchase_item_id);

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */