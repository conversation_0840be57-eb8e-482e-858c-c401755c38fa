+function ($) {
    'use strict';

    function createDataTable(selector) {
        var $table = $(selector);

        var dataTable = $table.DataTable({
            language: {
                infoFiltered: ''
            },
            lengthChange: false,
            info: false,
            paging: false, // Disable pagination
            searching: true, // Keep search functionality
            ordering: true, // Keep sorting functionality
            order: [[0, 'asc']], // Order by SKU ascending
            columnDefs: [
                {
                    className: 'text-center',
                    targets: [1, 2, 3, 4, 5] // Center align data columns
                },
                {
                    className: 'text-right',
                    targets: [6] // Actions column
                }
            ],
            columns: [
                { data: 'sku', title: 'SKU', defaultContent: '' },
                { data: 'size', title: 'Size', defaultContent: '' },
                { data: 'range', title: 'Range', defaultContent: '' },
                { data: 'no', title: 'NO', defaultContent: '' },
                { data: 'pcs', title: 'PCS', defaultContent: '' },
                { data: 'kgs', title: 'KGS', defaultContent: '' },
                { data: 'actions', title: '', orderable: false, searchable: false }
            ],
            autoWidth: false,
            processing: true,
            serverSide: true,
            dom: 'frt', // Only show filter (search), table, and processing
            ajax: {
                url: BASE_URI + 'fetch/Carcasses',
                type: 'POST',
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX error:', error, thrown);
                    alertify.error('Error loading data. Please try again.');
                }
            }
        });

        return dataTable;
    }

    $(document).ready(function() {
        // Initialize DataTable
        var dataTable = createDataTable('#carcasses-table');

        // Handle delete action
        $(document).on('click', '.action-delete', function() {
            var id = $(this).data('id');
            if (confirm('Are you sure you want to delete this carcass record?')) {
                window.location.href = BASE_URI + 'Carcasses/delete/' + id;
            }
        });
    });

}(jQuery);