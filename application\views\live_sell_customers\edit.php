<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class='row'>
					<div class="col-md-4 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" class="form-control" value="<?=set_value('name', $live_sell_customer->name);?>">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address">Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address', $live_sell_customer->address);?>">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no', $live_sell_customer->phone_no);?>">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>
				</div>

				<div class='row'>
					<div class="col-md-6 form-group">
						<label for="email">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email', $live_sell_customer->email);?>">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="credit_limit">Credit Limit</label>

						<input type="number" step="any" name="credit_limit" class="form-control" value="<?=set_value('credit_limit', $live_sell_customer->credit_limit);?>">

						<div class="red-text flash-message">
							<?=form_error('credit_limit');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf, image/*"  multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

				<div class="col-md-12 form-group">
					<table class="table table-bordered attachment-table" width="100%">
							<thead>
							<tr>
								<th class="text-center" style="width: 80%;">Attachment</th>
								<th class="text-center" style="width: 20%;">Action</th>
							</tr>
							</thead>
							<tbody>
								<?php foreach (array_filter(explode('🔥', $live_sell_customer->attachment)) as $attachment): ?>
								<tr class="attachment">
									<td>
									<li>
										<a href="<?=base_url('assets/live_sell_customers/' . $live_sell_customer->id . '/' . $attachment);?>" target="blank"><?=$attachment;?></a>
									</li>
									</td>
									<td class="text-center">
										<button type="button" class="btn btn-sm btn-danger action-delete-attachment" data-id="<?=$live_sell_customer->id?>" data-filename="<?=$attachment?>">Delete</button>
									</td>
								</tr>
								<?php endforeach;?>
							</tbody>
					</table>
				</div>

			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save Changes</button>
				<a href="<?=site_url('live_sell_customers')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
