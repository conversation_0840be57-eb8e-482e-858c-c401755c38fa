!function(t){"use strict";function e(){var e=t(r());n(e);var a=t(".item-table"),i=a.find(".order_slip_item");0===i.length&&a.find(".no-item").remove(),a.find("tbody").append(e),l()}function n(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:t(this).attr("data-placeholder"),minimumInputLength:3,width:"280px",minimumResultsForSearch:10,ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(t){var e={term:t.term};return e},processResults:function(e){return{results:t.map(e,function(t){return{text:t.name,id:t.id}})}}}})}function r(){var t=a();return'<tr class="order_slip_item"><td><input type="hidden" name="order_slip_item_'+t+'" value="'+t+'"><select style="min-width:200px" width="200px" name="item_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="pref_alw_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="est_due_amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function a(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(){return'<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'}function l(){var e=t(".item-table"),n=e.find(".order_slip_item");n.each(function(e,n){t(n).find('input[name^="row"]').val(e+1)})}function o(t,e=null){t.find('input[name^="unit_"]').val(e)}function s(e){return t.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})}function u(e){t(e.currentTarget).closest(".order_slip_item").remove();var n=t(e.delegateTarget),r=n.find(".order_slip_item");if(r.length<1){var a=t(i());n.find("tbody").html(a)}l()}t(function(){t(".action-add-item").click(e),t(".item-table").on("click",".action-delete-item",function(t){u(t)}).on("change",'select[name^="item"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item"),r=e.currentTarget.value;t.when(s(r)).then(function(t){o(n,t.unit)},function(){o(n,null)}),console.log(r)})})}(jQuery);