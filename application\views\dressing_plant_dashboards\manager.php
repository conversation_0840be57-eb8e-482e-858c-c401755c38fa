<style>
    .card {
        background-color: #fff;
        color: #000;
        border-radius: 10px;
        border-width: 1px;
        border-color: #555;
        text-align: center;
        transition: ease 0.3s;
        flex: 1;
        margin: 0 5px;
        min-width: 100px;
        height: 10vh;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding: 10px;
    }

    @keyframes spinner {
        0% {
            transform: translate3d(-50%, -50%, 0) rotate(0deg);
        }
        100% {
            transform: translate3d(-50%, -50%, 0) rotate(360deg);
        }
    }

    .spin {
        animation: 1.5s linear infinite spinner;
        animation-play-state: inherit;
        border: solid 5px #cfd0d1;
        border-bottom-color: #1c87c9;
        border-radius: 50%;
        content: "";
        height: 20px;
        position: relative;
        top: 5px;
        left: 10px;
        width: 20px;
        display: inline-block;
        will-change: transform;
    }

    .card:hover {
        -webkit-box-shadow: 0 10px 6px -6px #777;
        -moz-box-shadow: 0 10px 6px -6px #777;
        box-shadow: 0 10px 6px -6px #777;
        cursor: pointer;
        background-color: #555;
        color: #fff;
    }

    .card-invisible:hover {
        -webkit-box-shadow: 0 !important;
        -moz-box-shadow: 0 !important;
        box-shadow: 0 !important;
        cursor: arrow;
    }

    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .redirect {
        cursor: pointer;
        padding: 4px;
        transition: background-color .7s;
    }

    .redirect:hover {
        border-radius: 10px;
    }

    .card-m {
        height: 15vh;
        width: 100%;
    }

    .row {
        margin: 2vh 0;
        display: flex;
        justify-content: flex-start; /* Align to the left */
        margin-left: 2%; /* Adjust left margin to move cards to the right */
    }

    .emphasize-sm {
        font-size: 14px;
    }

    .title {
        padding-left: 5%;
    }

    .app-content,
    .app-footer {
        margin-left: 15%;
    }

    @media screen and (max-width: 768px) {
        .row {
            flex-wrap: wrap;
        }
        .card-m {
            width: 48%;
            margin-bottom: 2%;
        }

        .emphasize-sm {
            font-size: 1.2em;
        }

        .text-left.h4.text-center {
            text-align: left;
            font-size: 1.4em;
            padding-top: 2%;
        }

        .title {
            padding: 5%;
            padding-bottom: 0;
        }

        hr {
            padding: 0%;
            padding-bottom: 0;
            width: 100%;
        }

        .app-content,
        .app-footer {
            margin: 5%;
        }
    }
</style>

<div class="col">
    <div class="row center">
        <div class="col-8 text-left">
            <h1>Dashboard</h1>
        </div>
        <div class="col-4 text-left">
            <form method="get" class="form-inline" id="tableFilter">
                <label for="month" class="ml-sm-2 mr-sm-2 mb-2">Month:</label>
                <select name="month" id="month" class="form-control dropsearch mb-2 mr-sm-2" data-placeholder="Month">
                    <option></option>
                    <?php 
                        $months = [
                            '01' => 'January', 
                            '02' => 'February', 
                            '03' => 'March', 
                            '04' => 'April',
                            '05' => 'May', 
                            '06' => 'June', 
                            '07' => 'July', 
                            '08' => 'August',
                            '09' => 'September', 
                            '10' => 'October', 
                            '11' => 'November', 
                            '12' => 'December'
                        ];
                        
                        foreach ($months as $key => $month): 
                    ?>
                        <option value="<?=$key;?>" <?=($key == ($selected_month ?? date('m')) ? 'selected' : '');?>><?=$month?></option>
                    <?php endforeach; ?>
                </select>

                <label for="to" class="ml-sm-2 mr-sm-2 mb-2">Year:</label>
                <select name="year" id="year" class="form-control mb-2 mr-sm-2">
                    <?php
                        $currentYear = date('Y');
                        for ($year = $currentYear; $year >= $first_so_year; $year--) {
                            echo "<option value=\"$year\" " . ($year == $selected_year ? 'selected' : '') . ">$year</option>";
                        }
                    ?>
                </select>

                <button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
            </form>
        </div>
    </div>

    <hr style="width:96%; border-color: black; margin-left: 3%;">

    <div class="dashboard">
        <div class="row center">
            <div onclick="//window.open('<?=base_url('purchases/manager?status=pending'); ?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h4 text-center">Pending PO</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?= number_format($pending_po)?>" class="spin" id="spin_1"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="//window.open('<?=base_url('purchases/manager?status=urgent');?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h5 text-center">Urgent PO</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?= number_format($urgent_po)?>" class="spin" id="spin_2"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="//window.open('<?=base_url('receivables');?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h5 text-center">Receivables</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱ '. number_format($total_receivables, 2)?>" class="spin" id="spin_3"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="//window.open('<?=base_url('customers/customers_sale'); ?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h5 text-center">Monthly Sales</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱ '. number_format($total_monthly_sales, 2)?>" class="spin" id="spin_4"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="//window.open('<?=base_url('expenses');?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h5 text-center">Monthly Expenses</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱ '. number_format($total_monthly_expenses, 2)?>" class="spin" id="spin_5"></div></h1>
                    </div>
                </div>
            </div>

            <div onclick="//window.open('<?=base_url('new_section');?>','_self')" class="card card-m d-flex flex-column justify-content-center ml-2">
                <p class="h5 text-center">CAPEX</p>
                <div class="d-flex flex-row justify-content-center align-items-center">
                    <div class="redirect blue_hover" style="margin: 0 3%;">
                        <h1 class="emphasize-sm"><div data-val="<?='₱ '. number_format($total_monthly_capex, 2)?>" class="spin" id="spin_6"></div></h1>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="row center">
            <div class="col-12">
                <canvas id="myChart"></canvas>
            </div>
        </div> -->
    </div>
</div>
