<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$payment->id;?>">
		<div class="receipt-header">
			<div class="clearfix">
				<strong class="pull pull-right">Payment</strong>
			</div>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="row sect-jr">
				<div class="col-sm-2 offset-sm-10">
					<p class="text-left"><small>Doc No : </small> <?=$payment->id;?></p>
					<p class="text-left"><small>Date : </small> <?=date('n/j/Y', strtotime($payment->payment_date));?></p>
				</div>
			</div>
		</div>

		<div class="clearfix"></div>

		<?php if ($payment->type === 'check'): ?>
			<div class="row sect-jr">
				<div class="col-sm-2">
					<p class="text-left"><small>Check No : </small> <?=$payment_detail->check_no;?></p>
					<p class="text-left"><small>Check Date : </small> <?=date('n/j/Y', strtotime($payment_detail->check_date));?></p>
				</div>

				<div class="col-sm-2 offset-sm-8">
					<p class="text-left"><small>Bank Name: </small> <?=$payment_detail->bank_name;?></p>
					<p class="text-left"><small>Check Amount : </small> <?=number_format($payment_detail->amount, 2);?></p>
				</div>
			</div>

			<div class="clearfix"></div>
		<?php endif; ?>

		<table class="receipt-body">
			<thead>
				<tr>
					<th width="20%">Invoice No</th>
					<th width="50%">Customer</th>
					<th width="30%" class="text-right">Amount</th>
				</tr>
			</thead>

			<tbody>
			<?php foreach ($paid_invoices as $paid_invoice): ?>
				<tr class="item">
					<td><?=$paid_invoice->invoice_no;?></td>
					<td><?=$paid_invoice->customer;?></td>
					<td class="text-right"><?=number_format($paid_invoice->amount, 2);?></td>
				</tr>
			<?php endforeach;?>
			</tbody>

			<tfoot>
				<tr>
					<td colspan="2" class="text-right"><strong>Total</strong></td>
					<td class="text-right"><?=number_format($payment->total_paid, 2);?></td>
				</tr>
			</tfoot>
		</table>

		<div class="row">
			<div class="col-sm-4 offset-sm-8 text-center" style="margin-top: 30px">
				<i>I hereby acknowledge that the above entries are all accurate.</i>
				<br><br><br>
				<hr class="signature-line" style="margin-bottom: 5px">

				<p>Signature above printed name</p>
				Approved by
			</div>
		</div>
	</div>

	<div class="clearfix text-center">
		<button type="button" class="btn btn-primary action-print">Print</button>
		<a class="btn btn-light" href="<?=site_url('payments/manager');?>">Close</a>
	</div>
</div>