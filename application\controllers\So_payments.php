<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class So_payments extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('so_payments/manager'));
    }

    /**
     * Add so_Payment
     */
    public function add()
    {
        $paid_invoice_ids = $this->_get_invoice_inputs();
        $rules = array_merge(
            $this->config->item('so_payments/add'),
            $this->_get_invoice_rules('', $paid_invoice_ids),
            $this->_get_type_rules($this->input->post('type'))
        );

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('so_payments/manager'));
        } else {
            $this->title = 'Add Payment';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/so_payments/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'paid_invoice_ids' => $paid_invoice_ids,
                'invoices' => $this->order_slip->get_partially_paid()
            ];

            $this->build_content('default', 'so_payments/add', $data);
        }
    }

    /**
     * Purchase Order Print Preview
     */
    public function preview($so_payment_id)
    {
        $where = [
            'id' => $so_payment_id,
            'is_deleted' => 0
        ];
        $so_payment = $this->so_payment->select('', $where, 1) or show_404();

        $this->title = 'Print Preview: Payment No. ' . $so_payment->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/so_payments/preview'
        ];

        $where = [
            'so_payment_id' => $so_payment->id
        ];
        $so_payment_detail = $this->so_payment_detail->select('', $where, 1);

        $paid_sos = $this->paid_so->get_by_so_payment($so_payment_id);

        $data = [
            'so_payment' => $so_payment,
            'so_payment_detail' => $so_payment_detail,
            'paid_sos' => $paid_sos
        ];
        $this->build_content('default', 'so_payments/preview', $data);
    }

    /**
     * so_Payments
     */
    public function manager()
    {
        $this->title = 'Payments';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/so_payments/manager'
        ];

        $this->build_content('default', 'so_payments/manager');
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('so_payment');
        $this->load->model('so_payment_detail');
        $this->load->model('paid_invoice');
        $this->load->model('paid_so');
        $this->load->model('invoice');
        $this->load->model('order_slip');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_invoice_inputs()
    {
        $paid_invoice_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'paid_invoice_') === 0) {
                $paid_invoice_ids[] = $value;
            }
        }

        return $paid_invoice_ids;
    }

    /**
     * Get Invoice Rules
     */
    protected function _get_invoice_rules($so_id, $paid_invoice_ids)
    {
        $rules = [];

        foreach ($paid_invoice_ids as $paid_invoice_id) {
            $rules[] = [
                'field' => 'paid_invoice_' . $paid_invoice_id,
                'label' => 'Paid Invoice',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'invoice_' . $paid_invoice_id,
                'label' => 'Invoice No',
                'rules' => 'required|trim|callback__is_invoice|callback__no_invoice_duplicate',
                'errors' => [
                    '_is_invoice' => 'Invoice is not found.',
                    '_no_invoice_duplicate' => 'Invoice is added more than once.'
                ]
            ];

            $where = [
                'id' => $this->input->post('invoice_' . $paid_invoice_id)
            ];
            if (!$invoice = $this->order_slip->select('', $where, 1) or $invoice->grand_total < $invoice->paid_amount) {
                $balance = 0;
            } else {
                $balance = $invoice->grand_total - $invoice->paid_amount;
            }

            $rules[] = [
                'field' => 'amount_' . $paid_invoice_id,
                'label' => 'Amount',
                'rules' => 'required|trim|numeric|less_than_equal_to[' . $balance . ']|greater_than[0]'
            ];
        }

        return $rules;
    }

    /**
     * Get Type Rules
     */
    protected function _get_type_rules($so_payment_type)
    {
        switch ($so_payment_type) {
            case 'cash':
                return [
                    [
                        'field' => 'cash_amount',
                        'label' => 'Cash Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            case 'check':
                return [
                    [
                        'field' => 'check_date',
                        'label' => 'Check Date',
                        'rules' => 'required|trim|is_date[m/d/Y]'
                    ],
                    [
                        'field' => 'check_no',
                        'label' => 'Check No',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'bank_name',
                        'label' => 'Bank Name',
                        'rules' => 'required|trim'
                    ],
                    [
                        'field' => 'check_amount',
                        'label' => 'Check Amount',
                        'rules' => 'required|trim|is_numeric'
                    ]
                ];

            default:
                return [];
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $paid_invoices = $this->_get_invoice_data(); //get invoices

        if (empty($paid_invoices)) {
            $this->_error = 'no_paid_invoice_added';
            return false;
        }

        if (!$so_payment_id = $this->_add_so_payment($paid_invoices) or
            !$this->_add_so_payment_detail($so_payment_id) or
            !$this->_add_paid_invoices($so_payment_id, $paid_invoices)
        ){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        $this->db->trans_commit();
        return true;
    }

    /**
     * Get Invoice Data
     */
    protected function _get_invoice_data()
    {
        $data = [];

        $paid_invoice_ids = $this->_get_invoice_inputs();
        foreach ($paid_invoice_ids as $paid_invoice_id) {
            $data[] = [
                'id' => is_numeric($paid_invoice_id) ? $paid_invoice_id : null,
                'invoice_id' => $this->input->post('invoice_' . $paid_invoice_id),
                'amount' => $this->input->post('amount_' . $paid_invoice_id),
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Add so_Payment
     */
    protected function _add_so_payment($paid_invoices)
    {
        $type = $this->input->post('type');
        $amount = ($type === 'check') ? floatval(str_replace(',','',$this->input->post('check_amount'))) : floatval(str_replace(',','',$this->input->post('cash_amount')));
        $item_amount = array_reduce($paid_invoices, function ($current_total, $paid_invoice) {
            return $current_total + $paid_invoice['amount'];
        });


        if (number_format($amount,2) !== number_format($item_amount,2)) {
            $this->_error = ucfirst($type) . ' amount and total amount in table does not match.';
            return false;
        }

        $so_payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('so_payment_date'));

        $values = [
            'so_payment_date' => $so_payment_date?$so_payment_date->format('Y-m-d'):date('Y-m-d'),
            'total_paid' => $amount,
            'type' => $type,
            'added_by' => $_SESSION['user']->id
        ];


        if (!$so_payment_id = $this->so_payment->insert($values)) {
            $this->_error = 'server_error';
            return false;
        }



        return $so_payment_id;
    }

    /**
     * Add so_Payment Detail
     */
    protected function _add_so_payment_detail($so_payment_id)
    {
        $detail_id = true;

        if ($this->input->post('type') === 'check') {
            $check_date = DateTime::createFromFormat('m/d/Y', $this->input->post('check_date'));
            $values = [
                'so_payment_id' => $so_payment_id,
                'check_no' => $this->input->post('check_no'),
                'check_date' => $check_date->format('Y-m-d'),
                'bank_name' => $this->input->post('bank_name'),
                'amount' => $this->input->post('check_amount'),
                'added_by' => $_SESSION['user']->id
            ];
            if (!$detail_id = $this->so_payment_detail->insert($values)) {
                $this->_error = 'server_error';
                return false;
            }
        }

        return true;
    }

    /**
     * Add Paid Invoices
     */
    protected function _add_paid_invoices($so_payment_id, $paid_invoices)
    {
        foreach ($paid_invoices as &$paid_invoice) {
            $paid_invoice['so_payment_id'] = $so_payment_id;
            $paid_invoice['added_by'] = $_SESSION['user']->id;
        }
        unset($paid_invoice);

        if (!$is_inserted = $this->paid_so->insert_batch($paid_invoices)) {
            $this->_error = 'server_error';
            return false;
        }


        return $is_inserted;
    }

    /**
     * Callback: Check if Invoice Exists
     */
    public function _is_invoice($invoice_id)
    {
        $invoice = $this->order_slip->get_by_id($invoice_id);
        return !empty($invoice);
    }

    /**
     * Callback: Check if Invoice No Has No Duplicate
     */
    public function _no_invoice_duplicate($invoice_id)
    {
        $copies = 0;

        foreach ($_POST as $field => $value) {
            if (strpos($field, 'invoice_') === 0 && $value === $invoice_id) {
                $copies++;
            }
        }

        return $copies <= 1;
    }
}
