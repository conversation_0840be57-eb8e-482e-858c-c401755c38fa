<?php
class Pos_invoice extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'pos_invoice';
        parent::__construct();
    }

    /**
     * Get Invoice By ID
     */
    public function get_by_id($pos_invoice_id)
    {
        $sql = <<<EOT
SELECT pos_invoice.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, user.full_name AS cashier, pos_payment.total_paid, pos_payment.total_change
FROM pos_invoice
LEFT JOIN (
    SELECT pos_payment.pos_invoice_id AS pos_invoice_id, SUM(IFNULL(pos_payment.paid_amount,0)) AS total_paid, SUM(IFNULL(pos_payment.change,0)) AS total_change
    FROM pos_payment
    WHERE pos_payment.is_deleted = 0
    GROUP BY pos_payment.pos_invoice_id
) AS pos_payment ON pos_payment.pos_invoice_id = pos_invoice.id
LEFT JOIN customer ON customer.id = pos_invoice.customer_id
LEFT JOIN user ON user.id = pos_invoice.added_by
WHERE pos_invoice.id = ?
    AND pos_invoice.is_deleted = 0
EOT;
        $binds = [$pos_invoice_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
