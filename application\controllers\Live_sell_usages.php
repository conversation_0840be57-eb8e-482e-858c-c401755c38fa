<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_usages extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->live_sell_usage_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_usages/manager'));
    }

    /**
     * Add live_sell_Usage
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('live_sell_usages/add')) && $this->_attempt_add()) {
            redirect(site_url('live_sell_usages/manager'));
        } else {
            $this->title = 'Add Usage';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'live_sell_usages/add', $data);
        }
    }

    /**
     * Get units of item
     */
    public function get_sub_usage($usage_id = null)
    {
        $where = [
            'main_usage_id' => $usage_id,
            'is_deleted' => 0
        ];
        if (!$sub_usages = $this->live_sell_sub_usage->select('', $where)) {
            http_response_code(400);
            $response = [
                'message' => 'No sub usage is found.'
            ];
        } else {
            $response = [
                'message' => 'Sub usages are found.',
                'sub_usages' => $sub_usages,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Edit live_sell_Usage
     */
    public function edit($live_sell_usage_id)
    {
        $where = [
            'id' => $live_sell_usage_id,
            'is_deleted' => 0
        ];
        $live_sell_usage = $this->live_sell_usage->select('', $where, 1) or show_404();

        $this->live_sell_usage_name = $live_sell_usage->name;

        if ($this->_validate_form($this->config->item('live_sell_usages/edit')) && $this->_attempt_edit($live_sell_usage)) {
            redirect(site_url('live_sell_usages/manager'));
        } else {
            $this->title = 'Edit Usage';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'live_sell_usage' => $live_sell_usage
            ];
            $this->build_content('default', 'live_sell_usages/edit', $data);
        }
    }

    /**
     * Callback: Check if usage name exists
     */
    public function _is_usage($usage_name)
    {
        $where = [
            'name' => $usage_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->live_sell_usage_name) === strtolower($usage_name)) {
            return true;
        }

        return empty($this->live_sell_usage->select('', $where, 1));
    }

    /**
     * Delete live_sell_Usage
     */
    public function delete($live_sell_usage_id)
    {
        $where = [
            'id' => $live_sell_usage_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_usage = $this->live_sell_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Usage is not found.'
            ];
        } elseif (!$this->_attempt_delete($live_sell_usage)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Live Sell Usage is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * live_sell_Usages
     */
    public function manager()
    {
        $this->title = 'Usages';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_usages/manager'
        ];

        $this->build_content('default', 'live_sell_usages/manager');
    }

    /**
     * Get Info of live_sell_Usage
     */
    public function info($live_sell_usage_id = '')
    {
        $where = [
            'id' => $live_sell_usage_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_usage = $this->live_sell_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Usage is found.',
                'live_sell_usage' => $live_sell_usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of live sell usage by name
     */
    public function info_by_name($live_sell_usage_name = '')
    {
        $where = [
            'name' => urldecode($live_sell_usage_name),
            'is_deleted' => 0
        ];

        if (!$live_sell_usage = $this->live_sell_usage->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Usage is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Usage is found.',
                'live_sell_usage' => $live_sell_usage
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('live_sell_usage');
        $this->load->model('live_sell_sub_usage');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'added_by' => $_SESSION['user']->id
        ];
        if (!$live_sell_usage_id = $this->live_sell_usage->insert($values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $live_sell_usage_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($live_sell_usage)
    {
        $where = [
            'id' => $live_sell_usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->live_sell_usage->update($where, $values)) {
            $error = $this->db->error();
            if ($error['code'] === '23000/1062') {
                $this->_error = 'duplicate_name';
            } else {
                $this->_error = 'server_error';
            }
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($live_sell_usage)
    {
        $where = [
            'id' => $live_sell_usage->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $live_sell_usage->name . uniqid(),
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->live_sell_usage->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
