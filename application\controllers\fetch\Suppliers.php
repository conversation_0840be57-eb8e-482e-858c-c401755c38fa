<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Suppliers extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Suppliers
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'supplier';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_tin(),
            $this->_get_address(),
            $this->_get_zip_code(),
            $this->_get_actions(),
            $this->_get_is_active()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'supplier';
            }
        ];
    }

    /**
     * Get supplier name
     */
    protected function _get_name()
    {
        return [
            'db' => 'name',
            'dt' => 0,
            'field' => 'name'
        ];
    }

    /**
     * Get supplier tin
     */
    protected function _get_tin()
    {
        return [
            'db' => 'tin',
            'dt' => 1,
            'field' => 'tin'
        ];
    }

    /**
     * Get supplier address
     */
    protected function _get_address()
    {
        return [
            'db' => 'address',
            'dt' => 2,
            'field' => 'address'
        ];
    }

    /**
     * Get supplier zip code
     */
    protected function _get_zip_code()
    {
        return [
            'db' => 'zip_code',
            'dt' => 3,
            'field' => 'zip_code'
        ];
    }

    protected function _get_is_active()
    {
        return [
            'db' => 'is_active',
            'dt' => 4,
            'field' => 'is_active'
        ];
    }


    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 5,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                
                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                    $res .= anchor('suppliers/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $isActive = $row['is_active'];
                    $buttonText = $isActive ? 'Set Inactive' : 'Set Active';
                    $res .= '<button class="action-inactive dropdown-item text-left yellow-text" role="button" data-id="' . $d . '">' . $buttonText . '</button>';
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    
                }
                
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
