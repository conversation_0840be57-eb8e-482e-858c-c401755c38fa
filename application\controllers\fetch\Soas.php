<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Soas extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Drivers
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'soa';
        $primary_key = 'soa.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_doc_no(),
            $this->_get_customer(),
            $this->_get_billing_period(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
soa
LEFT JOIN customer ON customer.id = soa.customer_id
EOT;

        $where = <<<EOT
soa.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'soa.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'soa.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'soa';
            }
        ];
    }

    /**
     * Get SOA number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'soa.soa_no',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'
        ];
    }

    /**
     * Get billing period
     */
    protected function _get_billing_period()
    {
        return [
            'db' => 'CONCAT(DATE_FORMAT(soa.from_date, "%m/%d/%Y"), " - ", DATE_FORMAT(soa.to_date, "%m/%d/%Y"))',
            'as' => 'billing_period',
            'dt' => 2,
            'field' => 'billing_period'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'soa.id',
            'as' => 'actions',
            'dt' => 3,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('soas/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
