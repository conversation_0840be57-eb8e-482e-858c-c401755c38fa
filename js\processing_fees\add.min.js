!function(i){"use strict";function e(e){var t,n=i((a=e,'<tr class="processing_fee_item"><td><input type="hidden" name="classification_'+(t=function(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}())+'" value="'+a+'"><input type="hidden" name="processing_fee_item_'+t+'" value="'+t+'"><select style="min-width:200px" width="200px" name="item_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="unit_price_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>')),a=(c(n),i("."+e));a.attr("data-type");0===a.find(".processing_fee_item").length&&a.find(".no-item").remove(),i(n).find(".action-delete-item").on("click",function(e){o(e)}),i(n).find('input[name^="qty_"],input[name^="unit_price_"]').on("keyup",function(){var e=i(n).find('input[name^="qty_"]').val(),t=i(n).find('input[name^="unit_price_"]').val();i(n).find('input[name^="amount_"]').val(e*t)}),a.find("tbody").append(n),l()}function c(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:i(this).attr("data-placeholder"),width:"280px",ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(e){return{term:e.term}},processResults:function(e){return{results:i.map(e,function(e){return{text:e.name,id:e.id}})}}}})}function l(){i(".item-table").find(".processing_fee_item").each(function(e,t){i(t).find('input[name^="row"]').val(e+1)})}function n(e,t=null){e.find('input[name^="unit_"]:not([name^="unit_price_"])').val(t)}function o(e){console.log(e.currentTarget),i(e.currentTarget).closest(".processing_fee_item").remove();var t,e=i(e.delegateTarget);e.find(".processing_fee_item").length<1&&(t=i('<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'),e.find("tbody").html(t)),l()}i(function(){i(".action-add-item").on("click",function(){e(i(this).attr("data-table"))}),i(".item-table").on("click",".action-delete-item",function(e){o(e)}).on("change",'select[name^="item"]',function(e){var t=i(e.currentTarget).closest(".processing_fee_item"),e=e.currentTarget.value;i.when(i.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})).then(function(e){n(t,e.unit)},function(){n(t,null)})}),i('select[name="customer"]').on("change",function(e){i(e.currentTarget).closest(".processing_fee_item");e=e.currentTarget.value;i.when(i.ajax({url:BASE_URI+"live_sell_customers/get_data/"+e,type:"GET",dataType:"json"})).then(function(e){i('input[name="address"]').val(e.address),i('input[name="contact_number"]').val(e.contact_number),i('input[name="email"]').val(e.email),i('input[name="available_credit"]').val(e.available_credit)},function(){i('input[name="address"]').val(""),i('input[name="contact_number"]').val(""),i('input[name="email"]').val(""),i('input[name="available_credit"]').val("")})}),i(".processing_fee_item").each(function(e,t){i(t).find("option").not(":selected").remove(),c(i(t))})})}(jQuery);