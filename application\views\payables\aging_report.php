<div class="col topbtn">
	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">

		<select name="supplier" class="form-control dropsearch col-2" data-placeholder="Supplier">
			<option value=""></option>
			<?php foreach ($suppliers as $supplier): ?>
				<option value="<?=$supplier->id;?>"><?=$supplier->name;?></option>
			<?php endforeach;?>
		</select>

		<label for="date_from" class="ml-sm-2 mr-sm-2">Date From:</label>
		<input type="text" value="<?=set_value("date_from", date("m/01/Y"));?>" name="date_from" class="date_from form-control datepicker mb-2 mr-sm-2">
		
		<label for="date_to" class="ml-sm-2 mr-sm-2">Date To:</label>
		<input type="text" value="<?=set_value("date_to", date("m/d/Y"));?>" name="date_to" class="date_to form-control datepicker mb-2 mr-sm-2">

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="aging_report_payable">
		<table class="table table-hover tableA" data-package="aging_report_payable">
			<thead>
				<tr>
					<th>Supplier</th>
					<th>PO No.</th>
					<th>Outstanding</th>
					<th>1-30 Days</th>
					<th>31-60 Days</th>
					<th>61-90 Days</th>
					<th>90+ Days</th>
				</tr>
			</thead>

			<tbody></tbody>

			<tfoot>
				<tr>
					<th>Total</th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
					<th></th>
				</tr>
			</tfoot>
		</table>
	</div>
</div>

<style>
    /* tr td:nth-child(1) {
        cursor: pointer;
    }

    tr td:nth-child(1):hover {
        text-decoration: underline;
        color: blue;
    } */
</style>