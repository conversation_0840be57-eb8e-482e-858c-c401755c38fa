<?php
class Order_confirmation_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'order_confirmation_item';
        parent::__construct();
    }

    /**
     * Generate order_confirmation Item
     */
    public function generate($id, $order_confirmation_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO order_confirmation_item(id, order_confirmation_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, order_confirmation.id AS order_confirmation_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM order_confirmation
    CROSS JOIN item
    WHERE order_confirmation.id = ?
        AND order_confirmation.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    order_confirmation_item.id = LAST_INSERT_ID(order_confirmation_item.id),
    order_confirmation_item.qty = VALUES(order_confirmation_item.qty),
    order_confirmation_item.unit = VALUES(order_confirmation_item.unit),
    order_confirmation_item.updated_by = VALUES(order_confirmation_item.added_by),
    order_confirmation_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $order_confirmation_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get order_confirmation Item By ID
     */
    public function get_by_id($order_confirmation_item_id)
    {
        $sql = <<<EOT
SELECT order_confirmation_item.*, item.name AS item
FROM order_confirmation_item
LEFT JOIN item ON item.id = order_confirmation_item.item_id
WHERE order_confirmation_item.id = ?
    AND order_confirmation_item.is_deleted = 0
EOT;
        $binds = [$order_confirmation_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get order_confirmation Items by order_confirmation
     */
    public function get_by_order_confirmation($order_confirmation_id)
    {
        $sql = <<<EOT
SELECT order_confirmation_item.*, item.name AS item
FROM order_confirmation_item
LEFT JOIN item ON item.id = order_confirmation_item.item_id
WHERE order_confirmation_item.order_confirmation_id = ?
    AND order_confirmation_item.is_deleted = 0
EOT;
        $binds = [$order_confirmation_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
