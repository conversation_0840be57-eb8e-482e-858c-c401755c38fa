<div class="tab">
			<button type="button" class="tab-link <?=$status === 'pending' ? 'active' : '';?>" data-status="pending">Pending</button>
			<button type="button" class="tab-link <?=$status === 'approved' ? 'active' : '';?>" data-status="approved">Approved</button>
			<button type="button" class="tab-link <?=$status === 'check issued' ? 'active' : '';?>" data-status="check issued">Check Issued</button>
			<button type="button" class="tab-link <?=$status === 'for printing' ? 'active' : '';?>" data-status="for printing">For Printing</button>
			<button type="button" class="tab-link <?=$status === 'printed' ? 'active' : '';?>" data-status="printed">Printed</button>
			<button type="button" class="tab-link <?=$status === 'for release' ? 'active' : '';?>" data-status="for release">For Release</button>
			<button type="button" class="tab-link <?=$status === 'released' ? 'active' : '';?>" data-status="released">Released</button>
			<!-- <button type="button" class="tab-link <?=$status === 'audited' ? 'active' : '';?>" data-status="audited">Audited</button> -->
			<button type="button" class="tab-link <?=$status === 'incomplete audited' ? 'active' : '';?>" data-status="incomplete audited">Incomplete Audited</button>
			<button type="button" class="tab-link <?=$status === 'complete audited' ? 'active' : '';?>" data-status="complete audited">Complete Audited</button>
			<button type="button" class="tab-link <?=$status === 'deleted' ? 'active' : '';?>" data-status="deleted">Deleted</button>
	    	<button type="button" class="tab-link <?=$status === 'all' ? 'active' : '';?>" data-status="all">All</button>
			<button type="button" class="tab-link <?=$status === 'urgent' ? 'active' : '';?>" data-status="urgent">Urgent <span class="badge2" style="display:<?php if ($urgent === '0') {echo 'none';}?>"><?=$urgent;?></span></button>
</div>

<div class="col topbtn">

	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_OFFICE])): ?>
		<div class="dropdown float-right">
			<a href="<?=site_url('purchases/add');?>" class="btn btn-primary">Add New Purchase Order</a>
		</div>
	<?php endif;?>

	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<!-- <div class="row">
			<div class="col-md-3">
				<input id="item" name="item" type="text" class="form-control" placeholder="Enter Item's Name">
			</div>
			<div class="col-md-2">
				<label for="date_from" class="ml-sm-2 mr-sm-2">Date From:</label>
				<input type="text" value="<?=set_value("date_from", date("m/d/Y", strtotime("-1 months")));?>" name="date_from" class="form-control datepicker mb-2 mr-sm-2">
			</div>
			<div class="col-md-2">
				<label for="date_to" class="ml-sm-2 mr-sm-2">Date To:</label>
				<input type="text" value="<?=set_value("date_to", date("m/d/Y"));?>" name="date_to" class="form-control datepicker mb-2 mr-sm-2">
			</div>
			<div class="col-md-3">
				<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
			</div>
		</div> -->

		<input id="item" name="item" type="text" class="form-control" placeholder="Search">

		<label for="date_from" class="ml-sm-2 mr-sm-2">Date From:</label>
		<input type="text" value="<?=set_value("date_from", date("m/d/Y", strtotime("-1 months")));?>" name="date_from" class="date_from form-control datepicker mb-2 mr-sm-2">
		
		<label for="date_to" class="ml-sm-2 mr-sm-2">Date To:</label>
		<input type="text" value="<?=set_value("date_to", date("m/d/Y"));?>" name="date_to" class="date_to form-control datepicker mb-2 mr-sm-2">

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
	
	<!-- Add Download to Excel button here -->
	<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])): ?>
		<div class="row mt-3">
			<div class="col-md-3">
				<button type="button" class="btn btn-success btn-process" id="excel">Download Excel</button>
			</div>
		</div>
	<?php endif;?>
</div>


<div class="col">
	<div class="dt-asc-wrapper row" data-display="purchases?status=<?=$status?>">
		<table class="table table-hover tableA" data-package="purchases?status=<?=$status?>">
			<thead>
				<tr>
					<th>Doc No</th>
					<th>Item Name</th>
					<th>Supplier</th>
					<th>Purchase Date</th>
					<th>Plate No.</th>
					<th>Total</th>
					<!--<th>Check No</th>-->
					<th>Requisitioner</th>
					<th>Prepared By</th>
					<th>Recommended By</th>
					<th>Approved By</th>
					<th>Printed By</th>
					<th>Attachments</th>
					<th></th>
				</tr>
			</thead>

			<tbody></tbody>
		</table>
	</div>
</div>

<!-- The Modal -->
<div class="modal fade" id="checkNoModal">
  <div class="modal-dialog">
    <div class="modal-content">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Check Details</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>

      <!-- Modal body -->
      <div class="modal-body">

	  <div class="form-group row"><label class="col-sm-3 col-form-label"><b>Supplier :</b></label><div class="col-sm-8"><input type="text" class="form-control-plaintext" name="supplier"/></div></div>
		<div class="form-group row"><label class="col-sm-3 col-form-label"><b>Bank :</b></label><div class="col-sm-8"><input type="text" class="form-control-plaintext" name="bank"/></div></div>
		<div class="form-group row"><label class="col-sm-3 col-form-label"><b>Check # :</b></label><div class="col-sm-8"><input type="text" class="form-control-plaintext" name="checkNumber"/></div></div>
		<div class="form-group row"><label class="col-sm-3 col-form-label"><b>Check Date:</b></label><div class="col-sm-8"><input type="text" class="form-control-plaintext" name="check_date"/></div></div>

		<div class="col-m12">
			<table class="table table-hover modalTable">
				<thead>
					<tr>
						<th>PO #</th>
						<th>Usage</th>
						<th>Sub Usage</th>
						<th>Amount</th>
						<th></th>
					</tr>
				</thead>
				<tbody></tbody>
			</table>

		</div>

		<div class="form-group row"><label class="col-sm-4 col-form-label"><b>Check Amount :</b></label>
		<div class="col-sm-6"><input type="text" class="form-control-plaintext" name="checkAmount"/></div></div>

      </div>

      <!-- Modal footer -->
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
      </div>

    </div>
  </div>
</div>


<!-- Remarks Modal -->
<div class="modal fade" id="remarksModal">
  <div class="modal-dialog">
    <div class="modal-content">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Remarks</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>

      <!-- Modal body -->
      <div class="modal-body">

	  <p data-for="remarks"><span id="auditRemarks"></span></p>

      </div>

      <!-- Modal footer -->
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>

    </div>
  </div>
</div>