+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-left',
				targets: [0, 1, 2, 3, 4, 5]
			}, {
				className: 'dt-action',
				targets: 5
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(accountTypeId, dataTable) {
		$.when(deleteRow(accountTypeId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(accountTypeId) {
		return $.ajax({
			url: BASE_URI + 'account_types/delete/' + accountTypeId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.account_type').find('td');
			var account_name = typeof cols[1] !== 'undefined' ? cols[1].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete ' + account_name + '?',
				function () {
					var accountTypeId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(accountTypeId, dataTable);
				}
			).setHeader('<em>Delete Account Type</em>');
		});
	});
}(jQuery);
