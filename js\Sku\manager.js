+function ($) {
    'use strict';

    function createDataTable(selector) {
        var $table = $(selector);

        var dataTable = $table.DataTable({
            language: {
                infoFiltered: ''
            },
            lengthChange: false,
            info: false,
            paging: false, // Disable pagination
            searching: true, // Keep search functionality
            ordering: true, // Keep sorting functionality
            order: [[0, 'asc']], // Order by Customer Name ascending
            columnDefs: [
                {
                    className: 'text-center',
                    targets: [1, 2, 3, 4, 5, 6] // Center align all data columns
                },
                {
                    className: 'text-right',
                    targets: [7] // Actions column - align buttons to far right
                },
                {
                    // Allow HTML content in data columns
                    targets: [1, 2, 3, 4, 5, 6],
                    render: function(data, type, row) {
                        return data || '';
                    }
                }
            ],
            columns: [
                { data: 'customer_name', title: 'Customer Name' },
                { data: 'carcass_sku', title: 'SKU', defaultContent: '' },
                { data: 'carcass_size', title: 'Size', defaultContent: '' },
                { data: 'carcass_range', title: 'Range', defaultContent: '' },
                { data: 'carcass_by', title: 'By', defaultContent: '' },
                { data: 'giblets_sku', title: 'SKU', defaultContent: '' },
                { data: 'giblets_by', title: 'By', defaultContent: '' },
                { data: 'actions', title: '', orderable: false, searchable: false }
            ],
            autoWidth: false,
            processing: true,
            serverSide: true,
            dom: 'frt', // Only show filter (search), table, and processing - removes pagination controls
            ajax: {
                url: BASE_URI + 'fetch/Skus',
                type: 'POST',
                data: function(d) {
                    // Override pagination parameters to get all records
                    d.start = 0;
                    d.length = -1;
                    return d;
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    alertify.error('Error loading data. Please try refreshing the page.');
                }
            }
        });

        return dataTable;
    }

    function deleteAndReload(skuId, dataTable) {
        $.when(deleteRow(skuId)).then(
            function () {
                dataTable.ajax.reload(null, false);
            },
            function (jqXHR) {
                dataTable.ajax.reload(null, false);
                var response = JSON.parse(jqXHR.responseText);
                alertify.error(response.message || 'Delete failed.');
            }
        );
    }

    function deleteRow(skuId) {
        return $.ajax({
            url: BASE_URI + 'Skus/delete/' + skuId,
            type: 'POST',
            dataType: 'json'
        });
    }

    $(function () {
        var dataTable = createDataTable('.tableA');

        $('.tableA').on('click', '.action-delete', function (event) {
            var $row = $(event.currentTarget).closest('tr');
            var customerName = $row.find('td:first').text(); // First column = customer_name
            var skuId = $(event.currentTarget).data('id');

            alertify.confirm(
                'Are you sure you want to delete SKU for "' + customerName + '"?',
                function () {
                    deleteAndReload(skuId, dataTable);
                }
            ).setHeader('<em>Delete SKU</em>');
        });
    });
}(jQuery);
