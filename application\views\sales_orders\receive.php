<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<div class="w-100">
            	<h5 class="crud-subtitle"><span>Transfer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="font-weight-bold">Customer</label>
						<p class="underlined"><?=$transfer->customer;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="transfer_date" class="font-weight-bold">Transfer Date</label>
						<p class="underlined"><?=date('m/d/Y', strtotime($transfer->transfer_date));?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="font-weight-bold">Remarks</label>
						<p class="underlined"><?=$transfer->remarks;?></p>
					</div>

					<div class="col-md-12 form-group">
						<label for="attachment" class="control-label">Attachment</label>

						<div class="card">
							<div class="card-body">
								<ul>
									<?php foreach (array_filter(explode('🔥', $transfer->attachment)) as $attachment): ?>
										<li>
											<a href="<?=base_url('assets/pos/' . $transfer->id . '/' . $attachment);?>" target="blank"><?=$attachment;?></a>
										</li>
									<?php endforeach; ?>
								</ul>
							</div>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of transferd Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Qty</th>

									<th class="text-center" style="width: 15%;">Unit</th>
								</tr>
							</thead>

							<tbody>
							<?php
								if (!empty($transfer_items)):
									foreach ($transfer_items as $transfer_item):
							?>
										<tr class="transfer_item">
											<td><?=$transfer_item->item;?></td>

											<td class="text-right"><?=floatval($transfer_item->qty);?></td>

											<td><?=$transfer_item->unit;?></td>
										</tr>
							<?php
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
							</tbody>
						</table>
					</div>
				</div>

				<hr>

				<div class="clearfix text-center">
					<?php
						switch ($transfer->status):
						case 'pending':
					?>
							<button type="button" class="btn btn-sm btn-success action-approve-item" data-id="<?=$transfer_item->id?>">Receive</button>
					<?php
							break;
						endswitch;
					?>

					<?php
                        if ($_SESSION['user']->role_id == 1) {?>
						<a href="<?=site_url('transfers/edit/' . $transfer->id);?>" class="btn btn-warning">Edit</a>
						<a href="<?=site_url('transfers')?>" class="btn btn-light">Close</a>
			        <?php } ?>

				</div>
			</div>
		<form method="get" class="form-inline ml-1 p-0 row" id="tableFilter">
			<input type="hidden" name="customer_id" value="<?=$transfer->customer_id?>">
			<input type="hidden" name="transfer_id" value="<?=$transfer->id?>">
		</form>
	</div>
</div>

<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">Past Transfers</h1>

			<div class="dt-asc-wrapper row" data-display="transfers_by_customer">
				<table class="table table-hover tableA" data-package="transfers_by_customer">
					<thead>
						<tr>
							<th>Doc No</th>
							<th style="width: 25%;">Customer</th>
							<th>Transfer Date</th>
							<th style="width: 25%;">Items</th>
						</tr>
					</thead>

					<tbody></tbody>
				</table>
			</div>
		</div>
	</div>
</div>
