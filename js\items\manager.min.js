!function($){"use strict";$(function(){var b,a,c=((a=$(".tableA")).find("thead th"),a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[2,3,4,5]},{className:"dt-action",targets:6}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")}));$(".tableA").on("click",".action-delete",function(b){var a=$(b.currentTarget).closest("tr.item").find("td"),d=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+d+"?",function(){!function(b,c){var a;$.when((a=b,$.ajax({url:BASE_URI+"items/delete/"+a,type:"POST",dataType:"json"}))).then(function(){c.ajax.reload(null,!1)},function(a){c.ajax.reload(null,!1);var b=JSON.parse(a.responseText);alertify.error(b.message)})}(b.currentTarget.getAttribute("data-id"),c)}).setHeader("<em>Delete Item</em>")})})}(jQuery)