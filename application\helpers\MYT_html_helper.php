<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 * Application HTML Helpers (Based on CodeIgniter's Helpers)
 *
 * @package		Application
 * @subpackage	Helpers
 * @category	HTML
 */

// ------------------------------------------------------------------------

/**
 * Generates HTML BR tags based on number supplied
 *
 * @access	public
 * @param	integer
 * @return	string
 */
if ( ! function_exists('br'))
{
	function br($num = 1)
	{
		return str_repeat("<br />", $num);
	}
}

// ------------------------------------------------------------------------

/**
 * Image
 *
 * Generates an <img> element
 *
 * @access	public
 * @param	mixed
 * @return	string
 */
if ( ! function_exists('_insert_cachebusting_timestamp'))
{
	function img($src = '', $index_page = FALSE)
	{
		if ( ! is_array($src) )
		{
			$src = array('src' => $src);
		}

		// If there is no alt attribute defined, set it to an empty string
		if ( ! isset($src['alt']))
		{
			$src['alt'] = '';
		}

		$img = '<img';

		foreach ($src as $k=>$v)
		{

			if ($k == 'src' AND strpos($v, '//') === FALSE)
			{
				$CI =& get_instance();

				if ($index_page === TRUE)
				{
					$img .= ' src="'.$CI->config->site_url($v).'"';
				}
				else
				{
					$width = '';
					$height = '';
					$cachebusted_href = _insert_cachebusting_timestamp($v, array('.png', '.jpg', '.jpeg', 'gif'));
					if ($cachebusted_href !== FALSE)
					{
						$size = getimagesize($v);
						if ( ! isset($src['width']))
						{
							$width = ' width="'.$size[0].'"';
						}
						if ( ! isset($src['height']))
						{
							$height = ' height="'.$size[1].'"';
						}
					}
					$img .= ' src="'.$CI->config->slash_item('base_url').$cachebusted_href.'"' . $width . $height;
				}
			}
			else
			{
				$img .= " $k=\"$v\"";
			}
		}

		$img .= '>';

		return $img;
	}
}

// ------------------------------------------------------------------------

/**
 * Link
 *
 * Generates link to a CSS file
 *
 * @access	public
 * @param	mixed	stylesheet hrefs or an array
 * @param	string	rel
 * @param	string	type
 * @param	string	title
 * @param	string	media
 * @param	boolean	should index_page be added to the css path
 * @return	string
 */
if ( ! function_exists('link_tag'))
{
	function link_tag($href = '', $rel = 'stylesheet', $type = 'text/css', $title = '', $media = '', $index_page = FALSE)
	{
		$CI =& get_instance();

		$link = '<link ';

		if (is_array($href))
		{
			foreach ($href as $k=>$v)
			{
				if ($k == 'href' AND strpos($v, '//') === FALSE)
				{
					if ($index_page === TRUE)
					{
						$link .= 'href="'.$CI->config->site_url($v).'.css"';
					}
					elseif (defined('ENVIRONMENT'))
					{
						switch (ENVIRONMENT)
						{
							case 'development':
								$link .= 'href="'.$CI->config->slash_item('base_url').$v.'.css"';
								break;
							case 'testing':
							case 'production':
								$link .= 'href="'.$CI->config->slash_item('base_url').$v.'.min.css"';
								break;
						}
					}
					else
					{
						$link .= 'href="'.$CI->config->slash_item('base_url').$v.'.css"';
					}
				}
				else
				{
					$link .= " $k=\"$v\".css";
				}
			}

			$link .= ">";
		}
		else
		{
			if ($type === 'text/css')
			{
				$link .= 'rel="'.$rel.'" ';
			}
			else
			{
				$link .= 'rel="'.$rel.'" type="'.$type.'" ';
			}

			if ( strpos($href, '//') !== FALSE)
			{
				$link .= 'href="'.$href.'.css"';
			}
			elseif ($index_page === TRUE)
			{
				$link .= 'href="'.$CI->config->site_url($href).'.css"';
			}
			elseif (defined('ENVIRONMENT'))
			{
				switch (ENVIRONMENT)
				{
					case 'development':
						$link .= 'href="'.$CI->config->slash_item('base_url').$href.'.css"';
						break;
					case 'testing':
					case 'production':
						$link .= 'href="'.$CI->config->slash_item('base_url').$href.'.min.css"';
						break;
				}
			}
			else
			{
				$link .= 'href="'.$CI->config->slash_item('base_url').$href.'.css"';
			}

			if ($media	!= '')
			{
				$link .= ' media="'.$media.'"';
			}

			if ($title	!= '')
			{
				$link .= ' title="'.$title.'"';
			}

			$link .= '>';
		}


		return $link;
	}
}

// ------------------------------------------------------------------------

if ( ! function_exists('_insert_cachebusting_timestamp'))
{
	function _insert_cachebusting_timestamp($filename, array $file_extensions)
	{
		$is_found = FALSE;
		foreach ($file_extensions as $file_extension)
		{
			$file_pos = strripos($filename, $file_extension);
			if (FALSE !== $file_pos)
			{
				$is_found = TRUE;
				break;
			}
		}
		if ( ! $is_found)
		{
			return FALSE;
		}
		else
		{
			return substr_replace($filename, '.' . filemtime($filename) . substr($filename, $file_pos), $file_pos);
		}
	}
}

// ------------------------------------------------------------------------

/**
* Script
*
* Generates a script inclusion of a JavaScript file
* Based on the CodeIgniters original Link Tag.
*
* Author(s): Isern Palaus <<EMAIL>>, Viktor Rutberg <<EMAIL>>
*
* @access    public
* @param    mixed    javascript sources or an array
* @param    string    language
* @param    string    type
* @param    boolean    should index_page be added to the javascript path
* @return    string
*/

if ( ! function_exists('script_tag'))
{
	function script_tag($src = '', $defer = FALSE, $index_page = FALSE)
	{
		$CI =& get_instance();

		$script = '<script ';

		if(is_array($src))
		{
			foreach($src as $k => $v)
			{
				if ($k == 'src' AND strpos($v, '//') === FALSE)
				{
					if ($defer)
					{
						$script .= 'async defer ';
					}
					if ($index_page === TRUE)
					{
						$script .= 'src="'.$CI->config->site_url($v).'"';
					}
					elseif (defined('ENVIRONMENT'))
					{
						switch (ENVIRONMENT)
						{
							case 'development':
								$script .= 'src="'.$CI->config->slash_item('base_url').$v.'.js"';
								break;
							case 'testing':
							case 'production':
								$script .= 'src="'.$CI->config->slash_item('base_url').$v.'.min.js"';
								break;
						}
					}
					else
					{
						$script .= 'src="'.$CI->config->slash_item('base_url').$v.'.js"';
					}
				}
				else
				{
					$script .= "$k=\"$v\".js";
				}
			}

			$script .= ">";
		}
		else
		{
			if ($defer)
			{
				$script .= 'async defer ';
			}
			if ( strpos($src, '//') !== FALSE)
			{
				$script .= 'src="'.$src.'.js"';
			}
			elseif ($index_page === TRUE)
			{
				$script .= 'src="'.$CI->config->site_url($src).'.js"';
			}
			elseif (defined('ENVIRONMENT'))
			{
				switch (ENVIRONMENT)
				{
					case 'development':
						$script .= 'src="'.$CI->config->slash_item('base_url').$src.'.js"';
						break;
					case 'testing':
					case 'production':
						$script .= 'src="'.$CI->config->slash_item('base_url').$src.'.min.js"';
						break;
				}
			}
			else
			{
				$script .= 'src="'.$CI->config->slash_item('base_url').$src.'.js"';
			}

			$script .= '>';
		}

		$script .= '</script>';

		return $script;
	}
}

/* End of file my_html_helper.php */
/* Location: ./application/helpers/my_html_helper.php */
