<?php
class Eod_s extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_s';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($eodd_id)
    {
        $sql = <<<EOT
SELECT 
    eod_s.*,
    generator.full_name AS generator_name
FROM eod_s
LEFT JOIN user AS generator ON generator.id = eod_s.added_by
WHERE eod_s.is_deleted = 0
    AND eod_s.id = ?
EOT;
        $binds = [$eodd_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */