!function(u){"use strict";function t(t){var l=u(t);l.find("thead th");return l.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[2]},{className:"text-center",targets:[1,5]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,deferLoading:0,ajax:BASE_URI+"fetch/"+l.attr("data-package"),footerCallback:function(t,e,a,r,n){var o,i,c=this.api(),s=l.attr("data-col-sum");void 0!==s&&-1<s&&(o=function(t){return"string"==typeof t?+t.replace(/[\$,]/g,""):"number"==typeof t?t:0},i=c.column(s).data().reduce(function(t,e){return o(t)+o(e)},0),u(c.column(s).footer()).html(function(t){for(;/(\d+)(\d{3})/.test(t.toString());)t=t.toString().replace(/(\d+)(\d{3})/,"$1,$2");return t}(parseFloat(i).toFixed(2))))}})}u(function(){var e=t(".tableA");u("#tableFilter").submit(function(t){t.preventDefault();t=BASE_URI+"fetch/"+u(".tableA").attr("data-package")+"?"+u(t.currentTarget).serialize();e.ajax.url(t).load()}).submit()})}(jQuery);