<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formorder_confirmation" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" >
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $processing->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="processing_date" class="control-label">Production Date</label>

						<input type="text" name="processing_date" class="form-control datepicker" value="<?=set_value('processing_date',  DateTime::createFromFormat('Y-m-d', $processing->processing_date)->format('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('processing_date');?>
						</div>
					</div>

	            	<h5 class="crud-subtitle"><span>Efficiency Report</span></h5>

					<div class="col-md-4 form-group">
						<label for="total_live_arrived" class="control-label">Total Live Arrived <small class="font-italic"></small></label>

						<input type="number" name="total_live_arrived" class="form-control" value="<?=set_value('total_live_arrived', $processing->total_live_arrived);?>">

						<div class="red-text flash-message">
							<?=form_error('total_live_arrived');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_on_arrival" class="control-label">Total Death on Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_on_arrival" class="form-control" value="<?=set_value('total_death_on_arrival', $processing->total_death_on_arrival);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_on_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_death_after_arrival" class="control-label">Total Death after Arrival <small class="font-italic"></small></label>

						<input type="number" name="total_death_after_arrival" class="form-control" value="<?=set_value('total_death_after_arrival', $processing->total_death_after_arrival);?>">

						<div class="red-text flash-message">
							<?=form_error('total_death_after_arrival');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="la_in_kilos" class="control-label">LA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="la_in_kilos" class="form-control" value="<?=set_value('la_in_kilos', $processing ->la_in_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('la_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="doa_in_kilos" class="control-label">DOA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="doa_in_kilos" class="form-control" value="<?=set_value('doa_in_kilos', $processing->doa_in_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('doa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="daa_in_kilos" class="control-label">DAA in Kilos <small class="font-italic"></small></label>

						<input type="number" name="daa_in_kilos" class="form-control" value="<?=set_value('daa_in_kilos', $processing->daa_in_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('daa_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_to_be_processed" class="control-label">Total to be Processed <small class="font-italic"></small></label>

						<input type="number" name="total_to_be_processed" class="form-control" value="<?=set_value('total_to_be_processed', $processing->total_to_be_processed);?>">

						<div class="red-text flash-message">
							<?=form_error('total_to_be_processed');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="lp_in_kilos" class="control-label">LP in Kilos <small class="font-italic"></small></label>

						<input type="number" name="lp_in_kilos" class="form-control" value="<?=set_value('lp_in_kilos', $processing->lp_in_kilos);?>">

						<div class="red-text flash-message">
							<?=form_error('lp_in_kilos');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total_trips" class="control-label">Total Trips <small class="font-italic"></small></label>

						<input type="number" name="total_trips" class="form-control" value="<?=set_value('total_trips', $processing->total_trips);?>">

						<div class="red-text flash-message">
							<?=form_error('total_trips');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="alw" class="control-label">ALW <small class="font-italic"></small></label>

						<input type="number" name="alw" class="form-control" value="<?=set_value('alw', $processing->alw);?>">

						<div class="red-text flash-message">
							<?=form_error('alw');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="coops_loaded" class="control-label">Coops Loaded <small class="font-italic"></small></label>

						<input type="number" name="coops_loaded" class="form-control" value="<?=set_value('coops_loaded', $processing->coops_loaded);?>">

						<div class="red-text flash-message">
							<?=form_error('coops_loaded');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $processing->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Carcass</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table carcass" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($carcass_items)):
                                	$counter = 0;
                                    foreach ($carcass_items as $i => $carcass_item):
                                    $carcass_item = $carcass_items[$counter];
                            ?>
										<tr class="processing_item">
											<td>
												<input type="hidden" name=<?="classification_" . $carcass_item['id']?> value="carcass">
												<?php $fn_carcass_item = 'processing_item_' . $carcass_item['id'];?>
												<input type="hidden" name="<?=$fn_carcass_item?>" value="<?=set_value($fn_carcass_item, $carcass_item['id']);?>">

												<?php $fdn_item = 'stock_keeping_unit_' . $carcass_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$carcass_item['item_id']?>"><?=$carcass_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_size_range = 'size_range_' . $carcass_item['id'];?>
												<input type="text" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range, $carcass_items[$counter]['size_range']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_size_range);?>
												</div>
											</td>

											<td>
												<?php $fn_by_unit = 'by_unit_' . $carcass_item['id'];?>
												<input type="number" name="<?=$fn_by_unit;?>" class="form-control" value="<?=set_value($fn_by_unit, $carcass_items[$counter]['by_unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_by_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_crates = 'crates_' . $carcass_item['id'];?>
												<input type="text" name="<?=$fn_crates;?>" class="form-control" value="<?=set_value($fn_crates, $carcass_items[$counter]['crates']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_crates);?>
												</div>
											</td>

											<td>
												<?php $fn_heads = 'heads_' . $carcass_item['id'];?>
												<input type="text" name="<?=$fn_heads;?>" class="form-control" value="<?=set_value($fn_heads, $carcass_items[$counter]['heads']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_heads);?>
												</div>
											</td>

											<td>
												<?php $fn_kilos = 'kilos_' . $carcass_item['id'];?>
												<input type="text" name="<?=$fn_kilos;?>" class="form-control" value="<?=set_value($fn_kilos, $carcass_items[$counter]['kilos']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_kilos);?>
												</div>
											</td>

											<td>
												<?php $fn_recovery = 'recovery_' . $carcass_item['id'];?>
												<input type="text" name="<?=$fn_recovery;?>" class="form-control" value="<?=set_value($fn_recovery, $carcass_items[$counter]['recovery']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_recovery);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="carcass" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Incidental</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table incidental" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($incidental_items)):
                                	$counter = 0;
                                    foreach ($incidental_items as $i => $incidental_item):
                                    $incidental_item = $incidental_items[$counter];
                            ?>
										<tr class="processing_item">
											<td>
												<input type="hidden" name=<?="classification_" . $incidental_item['id']?> value="incidental">
												<?php $fn_incidental_item = 'processing_item_' . $incidental_item['id'];?>
												<input type="hidden" name="<?=$fn_incidental_item?>" value="<?=set_value($fn_incidental_item, $incidental_item['id']);?>">

												<?php $fdn_item = 'stock_keeping_unit_' . $incidental_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$incidental_item['item_id']?>"><?=$incidental_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_size_range = 'size_range_' . $incidental_item['id'];?>
												<input type="text" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range, $incidental_items[$counter]['size_range']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_size_range);?>
												</div>
											</td>

											<td>
												<?php $fn_by_unit = 'by_unit_' . $incidental_item['id'];?>
												<input type="number" name="<?=$fn_by_unit;?>" class="form-control" value="<?=set_value($fn_by_unit, $incidental_items[$counter]['by_unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_by_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_crates = 'crates_' . $incidental_item['id'];?>
												<input type="text" name="<?=$fn_crates;?>" class="form-control" value="<?=set_value($fn_crates, $incidental_items[$counter]['crates']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_crates);?>
												</div>
											</td>

											<td>
												<?php $fn_heads = 'heads_' . $incidental_item['id'];?>
												<input type="text" name="<?=$fn_heads;?>" class="form-control" value="<?=set_value($fn_heads, $incidental_items[$counter]['heads']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_heads);?>
												</div>
											</td>

											<td>
												<?php $fn_kilos = 'kilos_' . $incidental_item['id'];?>
												<input type="text" name="<?=$fn_kilos;?>" class="form-control" value="<?=set_value($fn_kilos, $incidental_items[$counter]['kilos']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_kilos);?>
												</div>
											</td>

											<td>
												<?php $fn_recovery = 'recovery_' . $incidental_item['id'];?>
												<input type="text" name="<?=$fn_recovery;?>" class="form-control" value="<?=set_value($fn_recovery, $incidental_items[$counter]['recovery']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_recovery);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="incidental" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Giblets</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table giblets" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($giblets_items)):
                                	$counter = 0;
                                    foreach ($giblets_items as $i => $giblets_item):
                                    $giblets_item = $giblets_items[$counter];
                            ?>
										<tr class="processing_item">
											<td>
												<input type="hidden" name=<?="classification_" . $giblets_item['id']?> value="giblets">
												<?php $fn_giblets_item = 'processing_item_' . $giblets_item['id'];?>
												<input type="hidden" name="<?=$fn_giblets_item?>" value="<?=set_value($fn_giblets_item, $giblets_item['id']);?>">

												<?php $fdn_item = 'stock_keeping_unit_' . $giblets_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$giblets_item['item_id']?>"><?=$giblets_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_size_range = 'size_range_' . $giblets_item['id'];?>
												<input type="text" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range, $giblets_items[$counter]['size_range']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_size_range);?>
												</div>
											</td>

											<td>
												<?php $fn_by_unit = 'by_unit_' . $giblets_item['id'];?>
												<input type="number" name="<?=$fn_by_unit;?>" class="form-control" value="<?=set_value($fn_by_unit, $giblets_items[$counter]['by_unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_by_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_crates = 'crates_' . $giblets_item['id'];?>
												<input type="text" name="<?=$fn_crates;?>" class="form-control" value="<?=set_value($fn_crates, $giblets_items[$counter]['crates']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_crates);?>
												</div>
											</td>

											<td>
												<?php $fn_heads = 'heads_' . $giblets_item['id'];?>
												<input type="text" name="<?=$fn_heads;?>" class="form-control" value="<?=set_value($fn_heads, $giblets_items[$counter]['heads']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_heads);?>
												</div>
											</td>

											<td>
												<?php $fn_kilos = 'kilos_' . $giblets_item['id'];?>
												<input type="text" name="<?=$fn_kilos;?>" class="form-control" value="<?=set_value($fn_kilos, $giblets_items[$counter]['kilos']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_kilos);?>
												</div>
											</td>

											<td>
												<?php $fn_recovery = 'recovery_' . $giblets_item['id'];?>
												<input type="text" name="<?=$fn_recovery;?>" class="form-control" value="<?=set_value($fn_recovery, $giblets_items[$counter]['recovery']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_recovery);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="giblets" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Total Byproducts</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table byproducts" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">SKU</th>

									<th class="text-center" style="width: 10%;">Size Range</th>

									<th class="text-center" style="width: 10%;">By</th>

									<th class="text-center" style="width: 10%;">Crates</th>

									<th class="text-center" style="width: 10%;">Heads</th>

									<th class="text-center" style="width: 10%;">Kilos</th>

									<th class="text-center" style="width: 20%;">Recovery %</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($byproducts_items)):
                                	$counter = 0;
                                    foreach ($byproducts_items as $i => $byproducts_item):
                                    $byproducts_item = $byproducts_items[$counter];
                            ?>
										<tr class="processing_item">
											<td>
												<input type="hidden" name=<?="classification_" . $byproducts_item['id']?> value="byproducts">
												<?php $fn_byproducts_item = 'processing_item_' . $byproducts_item['id'];?>
												<input type="hidden" name="<?=$fn_byproducts_item?>" value="<?=set_value($fn_byproducts_item, $byproducts_item['id']);?>">

												<?php $fdn_item = 'stock_keeping_unit_' . $byproducts_item['id'];?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option value="<?=$byproducts_item['item_id']?>"><?=$byproducts_item['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_size_range = 'size_range_' . $byproducts_item['id'];?>
												<input type="text" name="<?=$fn_size_range;?>" class="form-control" value="<?=set_value($fn_size_range, $byproducts_items[$counter]['size_range']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_size_range);?>
												</div>
											</td>

											<td>
												<?php $fn_by_unit = 'by_unit_' . $byproducts_item['id'];?>
												<input type="number" name="<?=$fn_by_unit;?>" class="form-control" value="<?=set_value($fn_by_unit, $byproducts_items[$counter]['by_unit']);?>" step="any" readonly>		

												<div class="red-text flash-message">
													<?=form_error($fn_by_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_crates = 'crates_' . $byproducts_item['id'];?>
												<input type="text" name="<?=$fn_crates;?>" class="form-control" value="<?=set_value($fn_crates, $byproducts_items[$counter]['crates']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_crates);?>
												</div>
											</td>

											<td>
												<?php $fn_heads = 'heads_' . $byproducts_item['id'];?>
												<input type="text" name="<?=$fn_heads;?>" class="form-control" value="<?=set_value($fn_heads, $byproducts_items[$counter]['heads']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_heads);?>
												</div>
											</td>

											<td>
												<?php $fn_kilos = 'kilos_' . $byproducts_item['id'];?>
												<input type="text" name="<?=$fn_kilos;?>" class="form-control" value="<?=set_value($fn_kilos, $byproducts_items[$counter]['kilos']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_kilos);?>
												</div>
											</td>

											<td>
												<?php $fn_recovery = 'recovery_' . $byproducts_item['id'];?>
												<input type="text" name="<?=$fn_recovery;?>" class="form-control" value="<?=set_value($fn_recovery, $byproducts_items[$counter]['recovery']);?>" step="any" >		

												<div class="red-text flash-message">
													<?=form_error($fn_recovery);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="byproducts" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('processings')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
