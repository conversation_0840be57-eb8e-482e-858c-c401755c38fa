<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Billing_statements extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('billing_statement');
        $this->load->model('billing_statement_item');
        $this->load->model('billing_statement_attachment');
        $this->load->model('item');
        $this->load->model('invoice');
        $this->load->model('sales_item');
        $this->load->model('invoice_item');
        $this->load->model('customer');


        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('billing_statements/manager'));
    }

    /**
     * Billing Statement
     */
    public function manager()
    {
        $this->title = 'Billing Statement';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/billing_statements/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'approved'
        ];

        $this->build_content('default', 'billing_statements/manager', $data);
    }
    
    /**
     * Add Billing Statement
     */
    public function add()
    {

        $billing_statement_ids = $this->_get_billing_statement_item_ids();
        $rules = array_merge($this->config->item('billing_statements/add'), $this->_get_billing_statement_item_rules('', $billing_statement_ids));
        
        $config = [
            'upload_path' => FCPATH . 'assets/asylum/billing_statements/',
            'allowed_types' => 'pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($rules) AND $this->_attempt_upload($config, 'attachments', true) AND $id = $this->_attempt_add() AND $this->_add_attachment($id)) {

            $new_path = FCPATH. 'assets/asylum/billing_statements/'.$id.'/';
            $this->_move_uploaded($new_path);

            redirect(site_url('billing_statements/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Billing Statement';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/billing_statements/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $customers = $this->customer->get_customer_with_invoices();

            $where = [
                'is_deleted' => 0
            ];

            $invoices = $this->invoice->get_invoices_by_customer($customer_id);
            // echo "<script>console.log('Customer ID:', " . json_encode($invoices) . ");</script>";
            $data = [
                'form_error' => $form_error,
                'billing_statement_item_ids' => $billing_statement_item_ids,
                'customers' => $customers,
                'invoices' => $invoices
            ];

            $this->build_content('default', 'billing_statements/add', $data);
        }
    }

    /**
     * Cancel Billing Statement
     */

     public function cancel($billing_statement_id)
     {
         if (!$this->_attempt_cancel($billing_statement_id)) {
             $response = [
                 'success' => false,
                 'message' => $this->_error_msg()
             ];
         } else {
             $response = [
                 'success' => true,
                 'message' => 'Billing Statement cancelled successfully.'
             ];
         }
 
         echo json_encode($response);
     }

    /**
     * Billing Statement Print Preview
     */
    public function preview($billing_statement_id)
    {
        ($billing_statement = $this->billing_statement->get_by_id($billing_statement_id) ) or show_404();

        $this->title = 'Print Preview: Billing Statement No. ' . $billing_statement->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/billing_statements/preview'
        ];

        $where = [
            'id' => $billing_statement->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $billing_statement_items = $this->billing_statement_item->get_by_billing_statement($billing_statement_id);

        $data = [
            'billing_statement' => $billing_statement,
            'customer' => $customer,
            'billing_statement_items' => $billing_statement_items
        ];
        $this->build_content('default', 'billing_statements/preview', $data);
    }
    
    //HELPER FUNCTIONS

    /**
     * Attempt Cancel
     */
    protected function _attempt_cancel($billing_statement_id)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $billing_statement_id
        ];

        $values = [
            'status'        => 'cancelled',
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if(!$this->billing_statement->update($where, $values)){
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }
        
        $this->db->trans_commit();
        return true;
    }

    /**
     * Get Input Suffices
     */
    protected function _get_billing_statement_item_ids()
    {
        $billing_statement_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'billing_statement_item') === 0) {
                $billing_statement_ids[] = $value;
            }
        }

        return $billing_statement_ids;
    }

    /**
     * Get Item Data
     */
    protected function _get_billing_statement_item_data()
    {
        $data = [];
        $billing_statement_item_ids = $this->_get_billing_statement_item_ids();

        foreach ($billing_statement_item_ids as $billing_statement_item_id) {
            $data[] = [
                'invoice_id'        => $this->input->post('invoice_no_'.$billing_statement_item_id),
                'total'            => $this->input->post('total_'.$billing_statement_item_id),
                'added_by'          => $_SESSION['user']->id,
                'added_on'          => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

   /**
     * Get Item Rules
     */
    protected function _get_billing_statement_item_rules($billing_statement_id, $billing_statement_item_ids)
    {
        $rules = [];

        foreach ($billing_statement_item_ids as $billing_statement_id) {
            $rules[] = [
                'field' => 'invoice_no_' . $billing_statement_id,
                'label' => 'Invoice No.',
                'rules' => 'required|trim'
            ];

            $rules[] = [
                'field' => 'total_' . $billing_statement_id,
                'label' => 'Total',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];
        }

        return $rules;
    }
    
    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Attempt add attachment
     */
    protected function _add_attachment($id)
    {
        if (!empty($this->_uploaded)) {
            $values = [];
            foreach ($this->_uploaded as $uploaded) {
                $values[] = [
                    'billing_statement_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => date('Y-m-d H:i:s')
                ];
            }
            if (!$attachment_id = $this->billing_statement_attachment->insert_batch($values)) {
                $this->_error = 'server_error';
                return false;
            }

            return $attachment_id;
        } else {
            return true;
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        echo "<script>console.log('Files:', " . json_encode($_FILES) . ");</script>";

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();

                $uploads = $this->_uploaded;

            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }


    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_id = $this->input->post('customer');

        if (!$billing_statement_items = $this->_get_billing_statement_item_data()) {
            $this->_error = 'No Billing statement item added';
            return false;
        }

        if (!$billing_statement_id = $this->_generate_billing_statement($billing_statement_items, $customer_id) or !$this->_generate_billing_statement_items($billing_statement_id, $billing_statement_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $billing_statement_id;
        }
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($billing_statement)
    {
        $where = [
            'id' => $billing_statement->id
        ];

        $values = [
            'is_deleted'    => 1,
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->billing_statement->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }


    /**
     * Generate Billing statement
     */
    protected function _generate_billing_statement($billing_statement_items, $customer_id)
    {

        $billing_date = DateTime::createFromFormat('m/d/Y', $this->input->post('billing_date'));

        $values = [
            'customer_id' => $customer_id,
            'billing_date' => $billing_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'status' => 'active',
            'grand_total' => $this->input->post('grand_total'),
            'remarks'  => $this->input->post('remarks') ?: null,
            'status' => 'approved',
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        return $this->billing_statement->insert($values);
    }

    


    /**
     * Generate Billing statement Items
     */
    protected function _generate_billing_statement_items($billing_statement_id, $billing_statement_items)
    {
        foreach ($billing_statement_items as $billing_statement_item) {
            $values = [
                'billing_statement_id'     => $billing_statement_id,
                'invoice_id'               => $billing_statement_item['invoice_id'],
                'total'                    => $billing_statement_item['total'],
                'added_by'                 => $billing_statement_item['added_by'],
                'added_on'                 => $billing_statement_item['added_on']
            ];

            if (!$this->billing_statement_item->insert($values)) {
                return false;
            }
        }

        return true;
    }
    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {

        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }


    /**
     * Get All Invoice of Customer
     */
    public function all_invoices($customer_id)
    {
        $data = [
            'invoices' => $this->invoice->get_invoices_by_customer($customer_id) ?: [],
        ];

        echo json_encode($data);
    }

}
