<?php
class Eod_bc extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_bc';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($eodd_id)
    {
        $sql = <<<EOT
SELECT 
    eod_bc.*,
    generator.full_name AS generator_name
FROM eod_bc
LEFT JOIN user AS generator ON generator.id = eod_bc.added_by
WHERE eod_bc.is_deleted = 0
    AND eod_bc.id = ?
EOT;
        $binds = [$eodd_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */