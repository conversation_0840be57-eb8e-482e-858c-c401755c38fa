<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Invoices extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch invoices
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice';
        $primary_key = 'invoice.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_status(),
            $this->_get_doc_no(),
            $this->_get_customer(),
            $this->_get_invoice_date(),
            $this->_get_terms(),
            $this->_get_due_date(),
            $this->_get_total(),
            $this->_get_issued_by(),
            $this->_get_approved_by(),
            $this->_get_file(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
LEFT JOIN user AS approver ON approver.id = invoice.approved_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'approved';

        switch($status){
//             case 'pending':
//                 $where = <<<EOT
// invoice.status = "pending"
// AND invoice.is_deleted = 0
// EOT;
//                 break;

//             case 'for_approval':
//                 $where = <<<EOT
// invoice.status = "for approval"
// AND invoice.is_deleted = 0
// EOT;
                // break;

            case 'approved':
                $where = <<<EOT
invoice.status = "approved"
AND invoice.is_deleted = 0
EOT;
                break;

            case 'bad_debt':
                $where = <<<EOT
invoice.status = "bad_debt"
AND invoice.is_deleted = 0
EOT;
                break;

//             case 'for_review':
//                 $where = <<<EOT
// invoice.is_reviewed = 0
// AND invoice.is_deleted = 0
// EOT;
//                 break;

//             case 'reviewed':
//                 $where = <<<EOT
// invoice.is_reviewed = 1
// AND invoice.is_deleted = 0
// EOT;
//                 break;

//             case 'cancelled':
//                 $where = <<<EOT
// invoice.status = "cancelled"
// AND invoice.is_deleted = 0
// EOT;
//                 break;

            case 'deleted':
                $where = <<<EOT
invoice.is_deleted = 1
EOT;
                break;
                
            default:
                break;
        }


        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'invoice.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(invoice.is_deleted = 1,"deleted",invoice.status)',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get invoice document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'invoice.invoice_no',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                $res = '<a href="../invoices/preview/' . $row['class'] . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                return $res;
            }
        ];
    }

    /**
     * Get invoice customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'
        ];
    }

    /**
     * Get invoice date
     */
    protected function _get_invoice_date()
    {
        return [
            'db' => 'DATE_FORMAT(invoice.invoice_date, "%b %d, %Y")',
            'as' => 'invoice_date',
            'dt' => 2,
            'field' => 'invoice_date'
        ];
    }

    /**
     * Get terms
     */
    protected function _get_terms()
    {
        return [
            'db' => 'invoice.terms',
            'dt' => 3,
            'field' => 'terms',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get due date
     */
    protected function _get_due_date()
    {
        return [
            'db' => 'DATE_FORMAT(invoice.due_date, "%b %d, %Y")',
            'as' => 'due_date',
            'dt' => 4,
            'field' => 'due_date'
        ];
    }

    /**
     * Get total
     */
    protected function _get_total()
    {
        return [
            'db' => 'FORMAT(invoice.total, 2)',
            'as' => 'total',
            'dt' => 5,
            'field' => 'total'
        ];
    }

    /**
     * Get issuing person
     */
    protected function _get_issued_by()
    {
        return [
            'db' => 'issuer.full_name',
            'as' => 'issued_by',
            'dt' => 6,
            'field' => 'issued_by'
        ];
    }

    /**
     * Get issuing person
     */
    protected function _get_approved_by()
    {
        return [
            'db' => 'approver.full_name',
            'as' => 'approved_by',
            'dt' => 7,
            'field' => 'approved_by'
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        $CI = &get_instance();
        return [
            'db' => 'invoice.id',
            'as' => 'file',
            'dt' => 8,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('invoice_attachment');
                $files = $CI->invoice_attachment->get_by_id($d);
                $res = '';

                foreach($files as $i => $file){
                    $res .= '<a href="'.base_url('assets/asylum/invoices/'.$d.'/'.$file->file_name).'" target="_blank">Attachment '.++$i.'</a><br>';
                }

                return $res;

            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'actions',
            'dt' => 9,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                switch($row['status']){
                    case 'pending' :
                        $res .= anchor('invoices/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('invoices/preview/' . $d, 'Recommend for Approval', 'title="Recommend for Approval" class="dropdown-item text-left"');
                        // $res .= '<button class="action-for-approval dropdown-item text-left" role="button" data-id="' . $d . '">Recommend for Approval</button>';
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                        }
                    break;
                    case 'for approval' :
                        $res .= anchor('invoices/preview/' . $d, 'Review for Approval', 'title="Review for Approval" class="dropdown-item text-left"');
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                        }
                    break;
                    case 'approved' :
                        $res .= anchor('invoices/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            // $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            $res .= '<button class="action-bad-debt dropdown-item text-left blue-text" role="button" data-id="' . $d . '">Bad Debt</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                            $res .= anchor('invoices/preview/' . $d, 'Review', 'title="Review" class="dropdown-item text-left"');
                        }
                    break;
                    case 'for_review' :
                        $res .= anchor('invoices/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                            $res .= anchor('invoices/preview/' . $d, 'Review', 'title="Review" class="dropdown-item text-left"');
                        }
                    break;
                    case 'reviewed' :
                        $res .= anchor('invoices/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                        }
                    break;
                    case 'cancelled' :
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                            // $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            // $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                        }
                    break;
                    default:
                        $res .= anchor('invoices/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER])){
                            // $res .= anchor('invoices/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                            // $res .= '<button class="action-pending dropdown-item text-left" role="button" data-id="' . $d . '">Return to Pending</button>';
                            // $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                        }
                    break;
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
