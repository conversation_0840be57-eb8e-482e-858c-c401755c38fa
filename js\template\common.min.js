!function(e,t){"use strict";if(e.fn.dataTable&&e.fn.dataTable.Api.register("sum()",function(){return this.flatten().reduce(function(e,t){return"string"==typeof e&&(e=1*e.replace(/[^\d.-]/g,"")),"string"==typeof t?1*t.replace(/[^\d.-]/g,""):e+t},0)}),"undefined"!=typeof autosize&&autosize(e(".autosize")),"undefined"!=typeof alertify&&alertify.set("notifier","position","bottom-center"),e.fn.select2&&(e('.dropsearch:not([multiple="multiple"])').select2({placeholder:e(this).attr("data-placeholder"),allowClear:!0}),e('.dropsearch[multiple="multiple"]').select2({placeholder:e(this).attr("data-placeholder"),allowClear:!1})),e.fn.timepicker&&e(".timepicker").timepicker({timeFormat:"hh:mm p"}),e.ui){var n=new Date,o=t.addYears(n,5);e(document).on("focus",".datepicker",function(n){e(n.currentTarget).datepicker({dateFormat:"mm/dd/yy",changeMonth:!0,changeYear:!0,yearRange:"1900:"+t.format(o,"YYYY"),onChangeMonthYear:function(t,n,o){e(this).datepicker("setDate",new Date(t,n-1,o.selectedDay)).trigger("change")}})}).on("focus",".datepicker-plus",function(a){e(a.currentTarget).datepicker({dateFormat:"mm/dd/yy",changeMonth:!0,changeYear:!0,minDate:n,yearRange:t.format(n,"YYYY")+":"+t.format(o,"YYYY"),onChangeMonthYear:function(t,n,o){e(this).datepicker("setDate",new Date(t,n-1,o.selectedDay)).trigger("change")}})}).on("focus",".datepicker-minus",function(o){e(o.currentTarget).datepicker({dateFormat:"mm/dd/yy",changeMonth:!0,changeYear:!0,maxDate:n,yearRange:"1900:"+t.format(n,"YYYY"),onChangeMonthYear:function(t,n,o){e(this).datepicker("setDate",new Date(t,n-1,o.selectedDay)).trigger("change")}})}).on("keydown.autocomplete","input.go-suggest, textarea.go-suggest",function(t){var n=t.currentTarget;e(n).on("keydown",function(t){t.keyCode===e.ui.keyCode.TAB&&e(n).autocomplete("instance").menu.active&&t.preventDefault()}).autocomplete({source:function(t,o){var a=BASE_URI+"autocomplete/"+n.getAttribute("data-suggest"),r={term:t.term.split(/,\s*|\n/).pop()};e.getJSON(a,r,o)},search:function(){if(n.value.split(/,\s*|\n/).pop().length<1)return!1},focus:function(){return!1},select:function(e,t){var o=n.value.split(/,\s*|\n/).pop(),a=new RegExp(o+"$");return n.value=o.replace(a,t.item.value),!1}})})}e.fn.addNumericCommas=function(e){var t=e.toString().split(".");return t[0]=t[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),t.join(".")},e.fn.removeNumericCommas=function(e){return Number(e.replace(/\,/g,""))},e.fn.makeRandomId=function(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(52*Math.random()));return e},e(function(){e("input, textarea").attr("autocomplete","disabled"),e(".datepicker").attr("autocomplete","off"),e(".modal").on("hidden.bs.modal",function(t){e(t.currentTarget).find("form").each(function(t,n){n.reset(),e(n).find(".error").html(""),e(".btn-process").text("Save"),e(".btn-process").css("pointer-events","auto")})}),e(".btn-process").on("click",function(){e(this).text("Processing..."),e(this).css("pointer-events","none")}),e(".whole-menu").hide(),e("#expense").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#sale").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#form").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#report").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#dressing-plant").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#manage-dressing-plant").on("click",function(){e(this).next(".whole-menu").toggle()}),e(".whole-menu").hide(),e("#live-sell").on("click",function(){e(this).next(".whole-menu").toggle()}),e("#expense-reveal").on("click",function(){e("#expense").toggle(),e("#sale").toggle(),e("#form").toggle(),e("#report").toggle(),e("#manage-dressing-plant").toggle(),e("#live-sell").toggle(),e("#logout").toggle()}),e("#expense-reveal").on("click",function(){e("#expense-reveal").click()}),e("#sale-reveal").on("click",function(){e("#sale-reveal").click()}),e("#form-reveal").on("click",function(){e("#form-reveal").click()}),e("#report-reveal").on("click",function(){e("#report-reveal").click()}),e("#live-sell-reveal").on("click",function(){e("#live-sell-reveal").click()}),e("#manage-dressing-reveal").on("click",function(){e("#manage-dressing-reveal").click()}),e(".whole-menu").hide(),e("#operation").on("click",function(){e(this).next(".whole-menu").toggle()}),e("#operation-reveal").on("click",function(){e("#operation").toggle(),e("#expense").toggle(),e("#sale").toggle(),e("#form").toggle(),e("#report").toggle(),e("#manage-dressing-plant").toggle(),e("#live-sell").toggle(),e("#logout").toggle()})})}(jQuery,dateFns);