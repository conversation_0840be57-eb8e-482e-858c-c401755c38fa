<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Check_PO_log extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }
        $this->load->model('user');
        $this->load->model('customer');
        $this->load->model('invoice');

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch purchase Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'trail';
        $primary_key = 'trail.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_date(),
            $this->_get_item_name(),
            $this->_get_action(),
            $this->_get_old_value(),
            $this->_get_new_value(),
            $this->_get_changed_by()
        ];

        $id = $this->input->get('purchase_id', true);

        $joins = <<<EOT
trail
LEFT JOIN purchase_item ON purchase_item.id = trail.source_id AND table_name="purchase_item" AND purchase_item.purchase_id = {$id}
LEFT JOIN item ON item.id = purchase_item.item_id
LEFT JOIN purchase ON purchase.id = trail.source_id AND table_name="purchase" AND purchase.id = {$id}
LEFT JOIN user AS adder ON adder.id = trail.added_by
EOT;

        $where = <<<EOT
(purchase.id > 0
        OR purchase_item.id > 0)
        AND col_name != 'updated_by'
EOT;

        $group_by = 'trail.id';
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'trail.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'trail.source_id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase';
            }
        ];
    }

    /**
     * Get SI Date
     */
    protected function _get_date()
    {
        return [
            'db' => 'DATE_FORMAT(CONVERT_TZ(trail.added_on,"US/Eastern","+8:00"), "%b %d, %Y, %h:%i %p")',
            'as' => 'added_on',
            'dt' => 0,
            'field' => 'added_on'
        ];
    }

    /**
     * Get SI Item Name
     */
    protected function _get_item_name()
    {
        return [
            'db' => 'item.name',
            'as' => 'item_name',
            'dt' => 1,
            'field' => 'item_name'
        ];
    }

    /**
     * Get Action
     */
    protected function _get_action()
    {
        return [
            'db' => 'trail.col_name',
            'as' => 'trail_action',
            'dt' => 2,
            'field' => 'trail_action',
            'formatter' => function ($d, $row) {
                if ($d == 'is_deleted') {
                    if ($row['trail_old_value'] == 0) {
                        return 'Deleted an item';
                    }
                    return 'Added new item';
                } elseif ($d == 'updated_by') {
                    $res = 'Update';
                } elseif ($d == 'customer_id') {
                    $res = 'Changed the Customer';
                } else {
                    $res = 'Changed the ' . $d;
                }
                return $res;
            }
        ];
    }

    /**
     * Get Old Value
     */
    protected function _get_old_value()
    {
        return [
            'db' => 'trail.old_value',
            'as' => 'trail_old_value',
            'dt' => 3,
            'field' => 'trail_old_value',
            'formatter' => function ($d, $row) {
                if ($row['trail_action'] == 'updated_by' || $row['trail_action'] == 'printed_by') {
                    $user = $this->user->get_by_id($d);
                    return $user[0]->full_name;
                } elseif ($row['trail_action'] == 'customer_id') {
                    $user = $this->customer->get_by_id($d);
                    return $user[0]->name;
                } elseif ($row['trail_action'] == 'is_deleted') {
                    return '';
                }
                return $d;
            }
        ];
    }

    /**
     * Get New Value
     */
    protected function _get_new_value()
    {
        return [
            'db' => 'trail.new_value',
            'as' => 'trail_new_value',
            'dt' => 4,
            'field' => 'trail_new_value',
            'formatter' => function ($d, $row) {
                if ($row['trail_action'] == 'updated_by' || $row['trail_action'] == 'printed_by') {
                    $user = $this->user->get_by_id($d);
                    return $user[0]->full_name;
                } elseif ($row['trail_action'] == 'customer_id') {
                    $user = $this->customer->get_by_id($d);
                    return $user[0]->name;
                } elseif ($row['trail_action'] == 'is_deleted') {
                    return '';
                }
                return $d;
            }
        ];
    }

    /**
     * Get Changed By
     */
    protected function _get_changed_by()
    {
        return [
            'db' => 'adder.full_name',
            'as' => 'trail_added_by',
            'dt' => 5,
            'field' => 'trail_added_by',
            'formatter' => function ($d, $row) {
                if (is_null($d)) {
                    $adder = $this->invoice->get_added_by_id($row['class']);
                    return $adder[0]->full_name;
                }
                return $d;
            }
        ];
    }

}