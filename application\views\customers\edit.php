<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class='row'>
					<div class="col-md-4 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" class="form-control" value="<?=set_value('name', $customer->name);?>">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address">Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address', $customer->address);?>">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="zip">ZIP Code</label>

						<input type="text" name="zip" class="form-control" value="<?=set_value('zip', $customer->zip_code);?>">

						<div class="red-text flash-message">
							<?=form_error('zip');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no', $customer->phone_no);?>">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email', $customer->email);?>">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="credit_limit">Credit Limit</label>

						<input type="number" step="any" name="credit_limit" class="form-control" value="<?=set_value('credit_limit', $customer->credit_limit);?>">

						<div class="red-text flash-message">
							<?=form_error('credit_limit');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="tin_number">Tin Number</label>

						<input type="number" step="any" name="tin_number" class="form-control" value="<?=set_value('tin_number', $customer->tin_number);?>">

						<div class="red-text flash-message">
							<?=form_error('tin_number');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="company">Company Name</label>

						<input type="text" name="company" class="form-control" value="<?=set_value('company', $customer->company_name);?>">

						<div class="red-text flash-message">
							<?=form_error('company');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="business_style">Business Style</label>

						<input type="text" name="business_style" class="form-control" value="<?=set_value('business_style', $customer->business_style);?>">

						<div class="red-text flash-message">
							<?=form_error('business_style');?>
						</div>
					</div>
				</div>

				<!-- <div class='row'>
					<div class="col-md-4 form-group">
						<label for="email">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email', $customer->email);?>">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="credit_limit">Credit Limit</label>

						<input type="number" step="any" name="credit_limit" class="form-control" value="<?=set_value('credit_limit', $customer->credit_limit);?>">

						<div class="red-text flash-message">
							<?=form_error('credit_limit');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="tin_number">Tin Number</label>

						<input type="number" step="any" name="tin_number" class="form-control" value="<?=set_value('tin_number', $customer->tin_number);?>">

						<div class="red-text flash-message">
							<?=form_error('tin_number');?>
						</div>
					</div>
				</div> -->
				
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save Changes</button>
				<a href="<?=site_url('customers')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
