+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: [2, 3, 4, 5]
			}, {
				className: 'dt-action',
				targets: 6
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(itemId, dataTable) {
		$.when(deleteRow(itemId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(itemId) {
		return $.ajax({
			url: BASE_URI + 'items/delete/' + itemId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.item').find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete ' + name + '?',
				function () {
					var itemId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(itemId, dataTable);
				}
			).setHeader('<em>Delete Item</em>');
		});
	});
}(jQuery);
