<?php
class Check_slip extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'check_slip';
        parent::__construct();
    }

    /**
     * Get Print Preview
     */
    public function get_preview($check_id)
    {
        $sql = <<<EOT
SELECT check_slip.*, bank.name AS bank, check_template.file_name AS print_template, supplier.name as supplier
FROM check_slip
LEFT JOIN bank ON bank.id = check_slip.bank_id
LEFT JOIN supplier ON supplier.id = check_slip.payee
LEFT JOIN check_template ON check_template.id = bank.print_template
WHERE check_slip.id = ?
    AND check_slip.is_deleted = 0
EOT;
        $binds = [$check_id];

        $res = $this->advanced_query($sql, $binds);

        return $res ? $res[0] : null;
    }

    public function get_due_payments()
    {
        $sql = <<<EOT
SELECT IFNULL(SUM(amount), 0) AS due_payments
FROM check_slip
WHERE is_deleted = 0
    AND check_date = CURDATE()
EOT;

        $result = $this->advanced_query($sql);
        return ($result ? $result[0]->due_payments : 0.00);
    }

    /**
     * Get Check Slip Sheet
     */
    public function get_by_check_slip($date_from, $date_to)
    {
        $sql = <<<EOT
SELECT bank.name AS mop, SUM(check_slip.amount) AS amount
FROM check_slip
LEFT JOIN bank ON bank.id = check_slip.bank_id
WHERE check_slip.is_deleted = 0
    AND check_slip.status = 'printed'
    AND check_slip.check_date BETWEEN ? AND ?
GROUP BY bank.name 
EOT;
        $binds = [$date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Check Slip Daily Breakdown
     */
    public function get_check_slip_breakdown_by_day($date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT calendar.date AS cash_out_date,
COALESCE(SUM(check_slip.amount), 0) AS total_cash_out_per_day
FROM (
    SELECT ? + INTERVAL n DAY AS date
    FROM (
        SELECT 
            a.N + b.N * 10 + c.N * 100 AS n
        FROM
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
        ) AS numbers
    WHERE ? + INTERVAL n DAY <= ?
) AS calendar
LEFT JOIN (
    SELECT check_slip.check_date AS date, check_slip.amount AS amount
    FROM check_slip
    WHERE check_slip.is_deleted = 0
        AND check_slip.status <> 'pending'
) AS check_slip ON calendar.date = check_slip.date
GROUP BY calendar.date
ORDER BY calendar.date;
EOT;
        $binds = [$date_from, $date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

