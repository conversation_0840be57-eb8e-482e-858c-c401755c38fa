+ function ($) {
	'use strict';

	function addItemRow() {
		var $newRow = $(itemRow());

	    setItemList($newRow);

		var $itemTable = $('.item-table');
		var sales_orderItems = $itemTable.find('.sales_order_item');

		if (sales_orderItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}

		$itemTable.find('tbody').append($newRow);		

		setRowNumbers();
	}

	function setItem(sales_order_item, item_id=null, item_name=null) {
		var $newOption = $("<option selected='selected'></option>").val(item_id).text(item_name);
		sales_order_item.find('select[name^="item"]').append($newOption);
	}

	function setItemList(sales_order_item) {
		sales_order_item.find('select[name^="item"]').select2({
		  tags: false,
		  allowClear: true,
		  placeholder: $(this).attr('data-placeholder'),
		  minimumInputLength: 0,
		  width: '280px',
		  minimumResultsForSearch: 10,
		  ajax: {
		      url: BASE_URI + 'live_sell_items/search_by_item',
		      dataType: "json",
		      type: "GET",
		      data: function (params) {
		          var queryParameters = {
		              term: params.term
		          }
		          return queryParameters;
		      },
		      processResults: function (data) {
		          return {
		              results: $.map(data, function (item) {
		                  return {
		                      text: item.name,
		                      id: item.id
		                  }
		              })
		          };
		      }
		  } 
		});
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="sales_order_item">' +
				'<td>' +
				'<input type="hidden" name="sales_order_item_' +
				rowId +
				'" value="' +
				rowId +
				'">' +
				'<select style="min-width:200px" width="200px" name="item_' +
				rowId +
				'" class="form-control dropsearch" data-placeholder=""></select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="actual_live_weight_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="text" name="unit_' + rowId + '" class="form-control go-suggest" value="" data-suggest="units" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="unit_price_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="amount_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td class="text-center">' +
					'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
				'</td>' +
			'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="7">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var sales_orderItems = $itemTable.find('.sales_order_item');

		sales_orderItems.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function setUnit(sales_order_item, available=null) {
		sales_order_item.find('input[name^="unit_"]').val(available);
	}

	function getUnit(itemId) {
		return $.ajax({
		  url: BASE_URI + 'live_sell_items/unit_of/' + itemId,
		  type: 'GET',
		  dataType: 'json',
	});
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.sales_order_item').remove();

		var $itemTable = $(event.delegateTarget);
		var sales_orderItems = $itemTable.find('.sales_order_item');
		if (sales_orderItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		setRowNumbers();
	}

	function computeRowAmount($row){
		var actualLiveWeight = parseFloat($row.find('input[name^="actual_live_weight_"]').val());
		if(isNaN(actualLiveWeight)){
			actualLiveWeight = 0;
		}
		var unitPrice = $row.find('input[name^="unit_price_"]').val();
		if(isNaN(unitPrice)){
			unitPrice = 0;
		}
		var amt = actualLiveWeight * unitPrice;

		$row.find('input[name^="amount_"]').val(amt);
	}

	$(function () {
		$('.action-add-item').click(addItemRow);
		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			})
			.on('change', 'select[name^="item"]', function (event) {
		        var sales_order_item = $(event.currentTarget).closest('.sales_order_item');
		        var itemId = event.currentTarget.value;

			    $.when(getUnit(itemId)).then(
			      function (response) {
			        setUnit(sales_order_item, response.unit);
			      },
			      function () {
			        setUnit(sales_order_item, null);
			      }
			    );
		        console.log(itemId);
			})
			.on("input", 'input[name^="actual_live_weight_"]', function(event){
				var $row = $(event.currentTarget).closest('.sales_order_item');
				computeRowAmount($row);
			})
			.on("input",'input[name^="unit_price_"]', function(event){
				var $row = $(event.currentTarget).closest('.sales_order_item');
				computeRowAmount($row);
			});

	    $('.sales_order_item').each(function(i, obj) {
	    	$(obj).find('option').not(':selected').remove();
			setItemList($(obj));
		});

		
	});
}(jQuery);
