!function(r){"use strict";function t(t,a){r.when(e(t,a)).then(function(r){window.location.href=BASE_URI+"purchase_orders/manager?status=for release"})}function e(t,e){return r.ajax({url:BASE_URI+"purchase_orders/approve_status/",type:"POST",dataType:"json",data:{purchase_order_id:t,status:e}})}r(function(){r("button.action-approve").click(function(r){var e=r.currentTarget.getAttribute("data-status");alertify.confirm("Are you sure you want to approve this Purchase Order for "+e+"?",function(){var a=r.currentTarget.getAttribute("data-id");t(a,e)}).setHeader("<em>Approve Purchase Order for "+e+"</em>")})})}(jQuery);