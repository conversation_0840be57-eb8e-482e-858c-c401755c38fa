$(document).ready(function(){function a(){var a=$("#carcassTableBody .carcass-row").length+1;return'<tr class="carcass-row"><td><input type="text" class="form-control" name="no[]" value="'+a+'" readonly style="background-color: #f9f9f9; cursor: not-allowed;"></td><td><input type="text" class="form-control" name="pcs[]" value=""></td><td><input type="text" class="form-control" name="kgs[]" value=""></td><td class="text-center" style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button></td></tr>'}function t(){$("#carcassTableBody .carcass-row").each(function(a){var t=a+1;$(this).find('input[name="no[]"]').val(t).prop("readonly",!0).css({"background-color":"#f9f9f9",cursor:"not-allowed"})}),$('#carcassTableBody .carcass-row:first input[name="no[]"]').css("font-weight","bold")}function c(){var t=$("#carcassTableBody .carcass-row:first"),c=t.find("td").length;$("#carcassTableBody .carcass-row:not(:first)").each(function(){var t=$(this),o=t.find("td").length;o!==c&&t.replaceWith(a())}),o()}function o(){$("#carcassTableBody .carcass-row").each(function(a){var t=a+1;$(this).find('input[name="no[]"]').val(t)})}$("#addCarcassRow").click(function(){var c=a();$("#carcassTableBody").append(c),t()}),setTimeout(function(){c(),t()},100),$("#carcassTable").on("click",".action-delete-row",function(){var a=$("#carcassTableBody .carcass-row").length;a>1?($(this).closest(".carcass-row").remove(),o()):alert("At least one row is required.")})});