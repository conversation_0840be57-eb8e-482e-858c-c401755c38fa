<div class="col-md-12 sect-jr">
	<div class="receipt" data-tag="<?=$payment->id;?>" style="border: 2px solid #000; padding: 10px;">
		<div class="row sect-jr" style="justify-content: center; padding: 0;">
			<div>
				<h3 class="text-center" style="font-family: 'Times New Roman', Times, serif; text-decoration: underline;"><strong>ACKNOWLEDGEMENT</strong></h3>
				<p class="text-center" style="font-style: italic; font-family: 'Calibri', sans-serif; font-size: 18px;">Proof of Payment</p>
			</div>
		</div>

		<div class="row sect-jr" style="margin-top: 15px;">
			<div class="col-sm-6" style="padding: 0; margin-left: 20px;">
				<input type="hidden" name="payment_id" value="<?= set_value('payment_id', $payment->id); ?>">
				<!-- <input type="hidden" name="audit_remarks" value="<?= set_value('audit_remarks', $audit_remarks); ?>"> -->
			</div>

			<div class="col-sm-5" style="padding: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Date : 
					<span class="text-left" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px;"><?= date('F d, Y', strtotime($payment->payment_date));?></span>
				</p>
			</div>

			<div class="col-sm-3" style="margin-right: 0;">
				
			</div>	
		</div>

		<br>

		<div class="row sect-jr" style="margin-top: 15px;">
			<div class="col-sm-6" style="padding: 0; margin-right: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Amount received in Figures: ( ₱ <span style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px; margin-left: 0;""><?=number_format($payment->total_paid, 2);?></span> )
				</p>
			</div>

			<div class="col-sm-5" style="padding: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Payment received for : 
					<span class="text-center" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px; margin-left: 0;"><?=$paid_invoice->concatenated_invoice_no;?></span>
				</p>
			</div>
		</div>

		<div class="row sect-jr" style="margin-top: 10px;">
			<div class="col-sm-6" style="padding: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Amount received in Words:
					<?php
						$whole = floor($payment->total_paid);
						$decimal = round(($payment->total_paid - $whole) * 100);
						$figures = array_filter([
							$whole > 0 ? spellout($whole) : null,
							$decimal ? $decimal . '/100' : null
						]);

						$payment_words = implode(' and ', $figures) . ' pesos only';
					?>
					<span class="text-center" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px; margin-left: 0;"><?=ucwords($payment_words);?></span>
				</p>
			</div>

			<div class="col-sm-5" style="padding: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Payment received by : 
					<span style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px;">
						<?= $payment->receiver . ' - ' . date('F d, Y g:i A', strtotime($payment->added_on));?>
					</span>
				</p>
				<p class="text-left" style=" font-style: italic; font-family: 'Times New Roman', Times, serif; font-size: 12px; margin-left:100px;"><?= "Authorized Agent (Name & Signature)";?></p>
			</div>
		</div>

		<div class="row sect-jr" style="margin-top: 30px;">
			<div class="col-sm-6" style="padding: 0; margin-right: 0; margin-left: 20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Payment received from: 
					<span class="text-center" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px; margin-left: 0;"><?=$paid_invoice->customer_name;?></span>
				</p>
				<p class="text-left" style=" font-style: italic; font-family: 'Times New Roman', Times, serif; font-size: 12px; margin-left:50px;"><?= "Signature Over Printed Name";?></p>
			</div>

			<div class="col-sm-5" style="padding: 0; margin-left: 20px;">
				<p class="text-center" style="font-family: 'Times New Roman', Times, serif; font-size: 16px; position: relative;">
					<span class="solid-line solid-line-left" style="position: absolute; top: 50%; height: 1px; background-color: black; left: 15px; width: calc(50% - 90px);"></span>
					To be filled by V.O.
					<span class="solid-line solid-line-right" style="position: absolute; top: 50%; height: 1px; background-color: black; right: 15px; width: calc(50% - 90px);"></span>
				</p>
			</div>
		</div>

		<div class="row sect-jr" style="margin-top: 5px;">
			<div class="col-sm-6" style="padding: 0; margin-right: 0; margin-left: 20px;"></div>

			<div class="col-sm-5" style="padding: 0; margin-left:20px;">
				<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Payment verified & received by: 
					<span class="text-center" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px;">
						<?= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'; ?>
					</span>
				</p>
			</div>
		</div>
	</div>

	<?php if (!empty($payment->audit_remarks)): ?>
		<div style="border: 2px solid #000; padding: 10px;">
			<div class="row sect-jr" style="justify-content: center; padding: 0;">
				<div>
					<h3 class="text-center" style="font-family: 'Times New Roman', Times, serif; text-decoration: underline;"><strong>AUDIT DETAILS</strong></h3>
					<p class="text-center" style="font-style: italic; font-family: 'Calibri', sans-serif; font-size: 18px;">Payment</p>
				</div>
			</div>

			<div class="row sect-jr" style="margin-top: 15px;">
				<div class="col-sm-6" style="padding: 0; margin-left: 20px;">
					<input type="hidden" name="payment_id" value="<?= set_value('payment_id', $payment->id); ?>">
					<input type="hidden" name="audit_remarks" value="<?= set_value('audit_remarks', $payment->audit_remarks); ?>">
					<input type="hidden" name="audit_attachment_file" value="<?= set_value('audit_attachment_file', $payment->audit_attachment_file); ?>">
					<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Audit Remarks: 
						<span class="text-left" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px; margin-left: 0;"><i><?= $payment->audit_remarks ?></i></span>
					</p>
				</div>

				<div class="col-sm-5" style="padding: 0; margin-left: 20px;">
					<p class="text-left" style="font-family: 'Times New Roman', Times, serif; font-size: 16px;">Audit Attachment: 
						<a href="<?= base_url('assets/audit/' . $payment->id . '/' . $payment->audit_attachment_file) ?>" download="<?= $payment->audit_attachment_file ?>">
							<span class="text-left" style="font-family: 'Times New Roman', Times, serif; border-bottom: 1px solid #000; font-size: 16px;"><?= $payment->audit_attachment_file ?></span>
						</a>
					</p>
				</div>

				<div class="col-sm-3" style="margin-right: 0;">
					
				</div>	
			</div>
		</div>
		<br>
	<?php endif; ?>

	<div class="clearfix text-center">
		<!-- <?php if (!empty($payment->audit_remarks)): ?>
			<a class="btn btn-primary" href="<?=site_url('payments/payment_or/'. $payment->id);?>">Create OR</a>
		<?php else: ?>
			<button type="button" class="btn btn-primary action-print">Print</button>
		<?php endif; ?>
		<a class="btn btn-light" href="<?=site_url('payments/manager');?>">Close</a> -->

		<?php if (empty($payment->audit_remarks)): ?>
			<button type="button" class="btn btn-info action-audit" id="auditBtn">Audit</button>
		<?php endif; ?>
		<?php if ($payment->audit_status === 'Incomplete'): ?>
			<button type="button" class="btn btn-success action-audit" id="auditUpdateBtn">Update Audit</button>
		<?php endif; ?>
		<a class="btn btn-light" href="<?=site_url('payments/manager');?>">Close</a>
	</div>
</div>

<style>
	@media print {
		.solid-line {
			display: block !important;
		}
	}

	.empty-border {
		min-height: 20px; /* Adjust height as needed */
	}
</style>