$(document).ready(function() {
    function carcassRowTemplate() {
        // Get the value from the first row's NO field
        var firstRowNo = $('#carcassTableBody .carcass-row:first input[name="no[]"]').val() || '1';

        // Create a row that exactly matches the first row structure
        return '<tr class="carcass-row">' +
            '<td><input type="text" class="form-control" name="no[]" value="' + firstRowNo + '" readonly style="background-color: #f9f9f9; cursor: not-allowed;"></td>' +
            '<td><input type="text" class="form-control" name="pcs[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="kgs[]" value=""></td>' +
            '<td class="text-center" style="vertical-align: middle;">' +
                '<button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>' +
            '</td>' +
        '</tr>';
    }

    // Function to update all NO fields when the first row changes
    function updateAllNoFields() {
        var firstRowNo = $('#carcassTableBody .carcass-row:first input[name="no[]"]').val() || '';

        // Make NO fields read-only for all rows except the first one
        $('#carcassTableBody .carcass-row:not(:first) input[name="no[]"]')
            .val(firstRowNo)
            .prop('readonly', true)
            .css({
                'background-color': '#f9f9f9',
                'cursor': 'not-allowed'
            });

        // Also update the first row's NO field styling if it has a value
        if (firstRowNo) {
            $('#carcassTableBody .carcass-row:first input[name="no[]"]').css('font-weight', 'bold');
        }
    }

    // Add new row with auto-filled NO value
    $('#addCarcassRow').click(function() {
        var newRow = carcassRowTemplate();
        $('#carcassTableBody').append(newRow);

        // Immediately update the NO field for the new row and apply styling
        updateAllNoFields();
    });

    // Update all NO fields when the first row's NO field changes (on input)
    $('#carcassTable').on('input', '.carcass-row:first input[name="no[]"]', function() {
        updateAllNoFields();
    });

    // Update all NO fields when the first row's NO field loses focus (on blur)
    $('#carcassTable').on('blur', '.carcass-row:first input[name="no[]"]', function() {
        updateAllNoFields();
    });

    // Function to fix row structure
    function fixRowStructure() {
        var $firstRow = $('#carcassTableBody .carcass-row:first');
        var firstRowCellCount = $firstRow.find('td').length;

        // Check all rows after the first one
        $('#carcassTableBody .carcass-row:not(:first)').each(function() {
            var $row = $(this);
            var currentCellCount = $row.find('td').length;

            // If this row doesn't match the first row's structure, replace it
            if (currentCellCount !== firstRowCellCount) {
                $row.replaceWith(carcassRowTemplate());
            }
        });
    }

    // Initialize synchronization on page load
    setTimeout(function() {
        // First fix any structural issues
        fixRowStructure();

        // Then update all NO fields to follow the first row
        updateAllNoFields();
    }, 100);

    // Delete row functionality
    $('#carcassTable').on('click', '.action-delete-row', function() {
        var rowCount = $('#carcassTableBody .carcass-row').length;
        if (rowCount > 1) {
            $(this).closest('.carcass-row').remove();
        } else {
            alert('At least one row is required.');
        }
    });
});