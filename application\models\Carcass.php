<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Carcass Model - Simplified for SKU, Size, Range, NO, PCS, KGS
 */
class Carcass extends MYT_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table_name = 'carcass';
    }

    /**
     * Create the carcass table if it doesn't exist
     */
   

    /**
     * Get all carcass entries
     */
    public function get_all_carcass($where = [], $limit = null, $order_by = null)
    {
        $this->db->select('*')
                 ->from($this->table_name)
                 ->where('is_deleted', 0);

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($order_by) {
            $this->db->order_by($order_by);
        } else {
            $this->db->order_by('added_on DESC');
        }

        if ($limit) {
            $this->db->limit($limit);
        }

        return $this->db->get()->result();
    }

    /**
     * Get carcass by ID
     */
    public function get_carcass_by_id($id)
    {
        return $this->db->where('id', $id)
                        ->where('is_deleted', 0)
                        ->get($this->table_name)
                        ->row();
    }

    /**
     * Check if SKU already exists
     */
    public function exists($sku, $exclude_id = null)
    {
        $this->db->where('sku', $sku);
        $this->db->where('is_deleted', 0);

        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table_name) > 0;
    }

    /**
     * Check if SKU exists for another customer
     */
    public function sku_exists_for_other_customer($sku, $customer_id)
    {
        $this->db->where('sku', $sku);
        $this->db->where('customer_id !=', $customer_id);
        $this->db->where('is_deleted', 0);

        return $this->db->count_all_results($this->table_name) > 0;
    }

    /**
     * Insert new carcass entry
     */
    public function insert_carcass($data)
    {
        $data['added_on'] = date('Y-m-d H:i:s');
        $data['added_by'] = $_SESSION['user']->id ?? 1;
        $data['is_deleted'] = 0;
        
        return $this->db->insert($this->table_name, $data);
    }

    /**
     * Update carcass entry
     */
    public function update_carcass($id, $data)
    {
        return $this->db->where('id', $id)
                        ->update($this->table_name, $data);
    }

    /**
     * Soft delete carcass entry
     */
    public function soft_delete_carcass($id, $deleted_by = null)
    {
        return $this->db->where('id', $id)
                        ->update($this->table_name, [
                           'is_deleted' => 1,
                           'deleted_on' => date('Y-m-d H:i:s'),
                           'deleted_by' => $deleted_by ?? ($_SESSION['user']->id ?? 1)
                        ]);
    }

    /**
     * Get carcass statistics
     */
    public function get_statistics()
    {
        $stats = [];

        // Total carcass entries
        $stats['total_entries'] = $this->db->where('is_deleted', 0)->count_all_results($this->table_name);

        // Recent entries (last7ys)
        $stats['recent_entries'] = $this->db->where('is_deleted', 0)
                                           ->where('added_on >=', date('Y-m-d H:i:s', strtotime('-7 days')))
                                           ->count_all_results($this->table_name);

        return $stats;
    }

    /**
     * Search carcass entries
     */
    public function search_carcass($search_term)
    {
        $this->db->select('*')
                 ->from($this->table_name)
                 ->where('is_deleted', 0)
                 ->group_start()
                 ->like('sku', $search_term)
                 ->or_like('size', $search_term)
                 ->or_like('size_range', $search_term)
                 ->or_like('no', $search_term)
                 ->group_end()
                 ->order_by('added_on DESC');

        return $this->db->get()->result();
    }

    /**
     * Get carcass entries for export
     */
    public function get_carcass_for_export($where = [])
    {
        $this->db->select('sku, size, size_range, no, pcs, kgs, added_on')
                 ->from($this->table_name)
                 ->where('is_deleted', 0);

        if (!empty($where)) {
            $this->db->where($where);
        }

        $this->db->order_by('added_on DESC');
        return $this->db->get()->result();
    }
} 