<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchases extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('purchase');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Purchase Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase';
        $primary_key = 'purchase.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_status(),
            $this->_get_verified_status(),
            $this->_get_doc_no(),
            $this->_get_item_name(),
            $this->_get_supplier(),
            $this->_get_purchase_date(),
            $this->_get_plate_no(),
            $this->_get_total(),
            //$this->_get_check_no(),
            $this->_get_requisitioner(),
            $this->_get_prepared_by(),
            $this->_get_recommended_by(),
            $this->_get_approved_by(),
            $this->_get_printed_by(),
            $this->_get_main_usage(),
            $this->_get_urgent(),
            $this->_get_audit_status(),
            $this->_get_file(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
purchase
LEFT JOIN (
    SELECT purchase_id, GROUP_CONCAT(CONCAT('• ',item.name) SEPARATOR '<br>') AS items
    FROM purchase_item
    LEFT JOIN item ON item.id = purchase_item.item_id
    WHERE purchase_item.is_deleted = 0
    GROUP BY purchase_id
) purchase_item ON purchase_item.purchase_id = purchase.id
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN user AS author ON author.id = purchase.added_by
LEFT JOIN user AS recommender ON recommender.id = purchase.recommended_by
LEFT JOIN user AS approver ON approver.id = purchase.approved_by
LEFT JOIN user AS printer ON printer.id = purchase.printed_by
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "for approval"
) as recommended_on ON recommended_on.source_id = purchase.id
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "approved"
) as approved_on ON approved_on.source_id = purchase.id
LEFT JOIN (
	SELECT added_on, source_id
    FROM trail
    WHERE trail.table_name = "purchase"
    AND col_name = "status"
    AND new_value = "printed"
) as printed_on ON printed_on.source_id = purchase.id
LEFT JOIN (
    SELECT DISTINCT item_id, purchase_id
    FROM purchase_item
    WHERE is_deleted = 0
) item ON item.purchase_id = purchase.id

EOT;

        $status = addslashes(urldecode($this->input->get('status', true))) ?: 'pending';

        if ($status == 'deleted') {
            $where = <<<EOT
purchase.is_deleted = 1
EOT;
        } elseif ($status === 'urgent') {
            $where = <<<EOT
purchase.is_urgent = 1
AND purchase.status != 'audited'
EOT;
        } elseif ($status === 'incomplete audited') {
            $where = <<<EOT
purchase.audit_status = 'incomplete'
EOT;
        } elseif ($status === 'complete audited') {
            $where = <<<EOT
purchase.audit_status = 'complete'
EOT;
        } elseif ($status === 'all') {
            $where = <<<EOT
1
EOT;
        } elseif ($status == 'for approval') {
            $where = <<<EOT
purchase.is_verified = 0
AND purchase.printed_by > 0
EOT;
        } else {
            $where = <<<EOT
purchase.status = "{$status}"
AND purchase.is_deleted = 0
EOT;
        }

        $item = $this->input->get('item', true);

        if ($item !== null && $item !== '') {
            $where .= <<<EOT

AND (
    item.item_id IN (
        SELECT item.id AS item_id
        FROM item
        WHERE item.name LIKE '%{$item}%'
    ) 
    OR supplier.name LIKE '%{$item}%'
    OR purchase.id LIKE '%{$item}%'
    OR purchase.plate_no LIKE '%{$item}%'
)
EOT;
        }

        if ($_SESSION['user']->role_id === ROLE_WAREHOUSE && $status !== 'approved') {
            $usage_warehouse = WAD_WAREHOUSE;
            $where .= <<<EOT

AND (purchase.added_by = {$_SESSION['user']->id} OR purchase.main_usage LIKE "%{$usage_warehouse}%")
EOT;
        // } elseif (!in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_PO_ADMIN, ROLE_AUDIT, ROLE_RECOMMENDER, ROLE_GENERAL_MANAGER]) && $status !== 'approved') {
        } elseif (!in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_PO_ADMIN, ROLE_AUDIT, ROLE_RECOMMENDER, ROLE_GENERAL_MANAGER]) && $status !== 'approved' && $status !== 'all') {
            $where .= <<<EOT

AND purchase.added_by = {$_SESSION['user']->id}
EOT;
        }

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->get('date_from'));
        $date_from = $date_from ? $date_from->format('Y-m-d') : '';
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->get('date_to'));
        $date_to = $date_to ? $date_to->format('Y-m-d') : '';

        if ($date_from && $date_to) {
            $where .= <<<EOT

AND purchase.purchase_date BETWEEN "{$date_from}" AND "{$date_to}"
EOT;
        } elseif ($date_from && !$date_to) {
            $where .= <<<EOT

AND purchase.purchase_date >= "{$date_from}"
EOT;
        } elseif (!$date_from && $date_to) {
            $where .= <<<EOT

AND purchase.purchase_date <= "{$date_to}"
EOT;
        }

        $group_by = <<<EOT

purchase.id
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(purchase.is_deleted = 1, "deleted", purchase.status)',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get Verified Status
     */
    protected function _get_verified_status()
    {
        return [
            'db' => 'IF(purchase.is_deleted = 1, "deleted", purchase.is_verified)',
            'as' => 'is_verified',
            'dt' => 'DT_RowVerified',
            'field' => 'is_verified'
        ];
    }

    /**
     * Get PO document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get PO document number
     */
    protected function _get_item_name()
    {
        return [
            'db' => "purchase_item.items",
            'as' => 'item_name',
            'dt' => 1,
            'field' => 'item_name'
        ];
    }

    /**
     * Get PO supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 2,
            'field' => 'supplier'
        ];
    }
    /**
     * Get PO Date
     */
    protected function _get_purchase_date()
    {
        return [
            'db' => 'purchase.purchase_date',
            'as' => 'purchase_date',
            'dt' => 3,
            'field' => 'purchase_date',
            'formatter' => function ($d, $row) {
                if ($date = DateTime::createFromFormat('Y-m-d', $d)) {
                    return $date->format('M d, Y');
                } else {
                    return null;
                }
            }
        ];
    }

    /**
     * Get Plate No
     */
    protected function _get_plate_no()
    {
        return [
            'db' => 'purchase.plate_no',
            'dt' => 4,
            'field' => 'plate_no'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_total()
    {
        $CI = &get_instance();

        return [
            'db' => 'purchase.total',
            'as' => 'total',
            'dt' => 5,
            'field' => 'total',
            'formatter' => function ($d, $row) use ($CI) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Check No
     */
    // protected function _get_check_no()
    // {
    //     $CI = &get_instance();
    //     return [
    //         'db' => 'purchase.id',
    //         'as' => 'check_no',
    //         'dt' => 6,
    //         'field' => 'check_no',
    //         'formatter' => function ($d, $row) use ($CI) {
    //             $CI->load->model('purchase');
    //             $check_purchases = $CI->purchase->get_check_no($d);

    //             $res = '';

    //             foreach ($check_purchases as $check_purchase) {
    //                 // $res .= '<button type="button" class="action-modal btn btn-link" data-toggle="modal" data-target="#checkNoModal" data-id="' . $check_purchase->check_id . '">' . $check_purchase->check_no . '</button><br>';
    //                 $res .= $check_purchase->check_id . ' - ' . $check_purchase->check_no . '<br>';
    //             }

    //             return $res;
    //         }
    //     ];
    // }

    /**
     * Get Requisitioner
     */
    protected function _get_requisitioner()
    {
        return [
            'db' => 'IFNULL(purchase.requisitioner, author.full_name)',
            'as' => 'requisitioner',
            'dt' => 6,
            'field' => 'requisitioner'
        ];
    }

    /**
     * Get Requisitioner
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'CONCAT(author.full_name, "<br>", DATE_FORMAT(purchase.added_on, "%b %d, %Y %h:%i %p"))',
            'as' => 'prepared_by',
            'dt' => 7,
            'field' => 'prepared_by'
        ];
    }

    /**
     * Get Recommended By
     */
    protected function _get_recommended_by()
    {
        return [
            'db' => 'CONCAT(recommender.full_name, "<br>", DATE_FORMAT(recommended_on.added_on, "%b %d, %Y %h:%i %p"))',
            'as' => 'recommended_by',
            'dt' => 8,
            'field' => 'recommended_by'
        ];
    }

    /**
     * Get Approved By
     */
    protected function _get_approved_by()
    {
        return [
            'db' => 'CONCAT(approver.full_name, "<br>", DATE_FORMAT(approved_on.added_on, "%b %d, %Y %h:%i %p"))',
            'as' => 'approved_by',
            'dt' => 9,
            'field' => 'approved_by'
        ];
    }

    /**
     * Get Printed By
     */
    protected function _get_printed_by()
    {
        return [
            'db' => 'CONCAT(printer.full_name, "<br>", DATE_FORMAT(printed_on.added_on, "%b %d, %Y %h:%i %p"))',
            'as' => 'printed_by',
            'dt' => 10,
            'field' => 'printed_by'
        ];
    }

    /**
     * Get main usage
     */
    protected function _get_main_usage()
    {
        return [
            'db' => 'purchase.main_usage',
            'dt' => 'DT_RowUsage',
            'field' => 'main_usage'
        ];
    }

    /**
     * Get Urgent PO
     */
    protected function _get_urgent()
    {
        return [
            'db' => 'purchase.is_urgent',
            'as' => 'is_urgent',
            'dt' => 'DT_RowUrgent',
            'field' => 'is_urgent'
        ];
    }

    /**
     * Get Urgent PO
     */
    protected function _get_audit_status()
    {
        return [
            'db' => 'purchase.audit_status',
            'as' => 'audit_status',
            'dt' => 'DT_RowAuditStatus',
            'field' => 'audit_status'
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'file',
            'dt' => 11,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->purchase->get_file_by_id($d);
                $res = '<ul>';

                if($files) {
                    $attachments = str_replace('?', '🔥', $files[0]->attachment);
                    $files = explode('🔥', $attachments);
                    $files = array_map('trim', $files);
                    $files = array_filter($files); 
                    $files = array_values($files);                   
                }

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/pos/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'actions',
            'dt' => 12,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                switch ($row['status']) {
                    case 'pending':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_OFFICE])) {
                            $res .= anchor('purchases/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        }

                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= anchor('purchases/recommend/' . $d, 'Recommend for Approval', 'title="Recommend for Approval" class="dropdown-item text-left"');
                        }

                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= anchor('purchases/review/' . $d, 'Review for Approval', 'title="Review for Approval" class="dropdown-item text-left"');
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        }

                        if ($row['is_urgent'] === '0' && !in_array($row['status'],['audited','deleted']) && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }

                        break;

                    case 'for approval':
                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }

                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            // $res .= anchor('purchases/verify/' . $d, 'Review for Verification', 'title="VERIFY" class="dropdown-item text-left tect-success"');
                            $res .= anchor('purchases/review/' . $d, 'Review for Verification', 'title="Review for Verification" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        }
                        break;


                    case 'approved':

                        $res .= anchor('purchases/preview/' . $d . '/Print', 'Print', 'title="Print" class="dropdown-item text-left"');
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            // $res .= anchor('checks/add?purchase=' . $d, 'Add Check', 'title="Add Check" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        }

                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;

                    case 'printed':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN]) && stripos($row['main_usage'], WAD_WAREHOUSE) !== false) {
                            $res .= anchor('purchases/receives/' . $d, 'Receives', 'title="Receives" class="dropdown-item text-left"');
                        } 
                        // elseif (!in_array($_SESSION['user']->role_id, [ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER])) {
                            // $res .= anchor('purchases/releases/' . $d, 'Releases', 'title="Releases" class="dropdown-item text-left"');
                        // } else {
                            // $res .= anchor('purchases/add_check_no/' . $d, 'Add Check No', 'title="Add Check No" class="dropdown-item text-left"');
                        // }

                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING])) {
                            $res .= anchor('purchases/simple_preview/' . $d . '/Releasing', 'Approve for Releasing', 'title="Approve for Releasing" class="dropdown-item text-left text-success"');
                        }

                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }

                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                            // $res .= anchor('checks/add?purchase=' . $d, 'Add Check', 'title="Add Check" class="dropdown-item text-left"');
                            // $res .= anchor('receives/add/' . $d, 'Add Purchase Invoice', 'title="Add Purchase Invoice" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_PO_ADMIN, ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                        }
                        break;
                    case 'check issued':
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchases/simple_preview/' . $d . '/Printing', 'Approve for Printing', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'for printing':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT])) {
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'Print', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'for release':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING])) {
                            $res .= anchor('purchases/preview_release/' . $d, 'Release', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'released':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT])) {
                            $res .= anchor('purchases/preview/' . $d . '/Audit', 'Audit', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                            $res .= anchor('purchases/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        }


                        if ($row['is_urgent'] === '0' && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'audited':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT])) {
                            if ($row['audit_status'] == 'incomplete') {
                                $res .= anchor('purchases/preview_update/' . $d . '/Audit', 'Update Audit', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                                $res .= '<button class="action-remarks dropdown-item text-left" role="button" data-id="' . $d . '" data-toggle="modal" data-target="#remarksModal">View Remarks</button>';
                                
                            }
    
                            if ($row['audit_status'] == 'complete') {
                                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                                    $res .= anchor('purchases/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                                }
                                $res .= '<button class="action-remarks dropdown-item text-left" role="button" data-id="' . $d . '" data-toggle="modal" data-target="#remarksModal">View Remarks</button>';
                            }
                        }

                        break;
                    case 'urgent':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN]) && stripos($row['main_usage'], WAD_WAREHOUSE) !== false) {
                            $res .= anchor('purchases/receives/' . $d, 'Receives', 'title="Receives" class="dropdown-item text-left"');
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                            $res .= anchor('purchases/releases/' . $d, 'Releases', 'title="Releases" class="dropdown-item text-left"');
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        } else {
                            // $res .= anchor('purchases/add_check_no/' . $d, 'Add Check No', 'title="Add Check No" class="dropdown-item text-left"');
                        }

                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                            // $res .= anchor('checks/add?purchase=' . $d, 'Add Check', 'title="Add Check" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_PO_ADMIN, ROLE_AUDIT])) {
                            $res .= anchor('purchases/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                        }
                        break;

                    case 'deleted':
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= anchor('purchases/check_log/' . $d, 'Check Log', 'title="Check Log" class="dropdown-item text-left blue-text"');
                        }
                        break;

                    default:
                        break;
                }

                if(!in_array($row['status'],['pending','deleted','urgent']) && in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT, ROLE_OFFICE, ROLE_ACCOUNTING])){
                    $res .= anchor('purchases/add_attachment/' . $d, 'Add PO Attachment', 'title="Add PO Attachment" class="dropdown-item text-left"');
                }


                // if ($row['is_verified'] == 0 && $row['printed_by'] != null && $_SESSION['user']->role_id === ROLE_ADMIN) {
                //     $res .= '<button class="action-verify dropdown-item text-left text-success" role="button" data-id="' . $d . '">Verify</button>';
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
