<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class='row'>
					<div class="col-md-4 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" class="form-control" value="<?=set_value('name');?>">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="size_range">Size Range</label>

						<input type="text" name="size_range" class="form-control" value="<?=set_value('size_range');?>">

						<div class="red-text flash-message">
							<?=form_error('size_range');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="by_unit">By</label>

						<input type="text" name="by_unit" class="form-control" value="<?=set_value('by_unit');?>">

						<div class="red-text flash-message">
							<?=form_error('by_unit');?>
						</div>
					</div>
				</div>

				<div class='row'>
					<div class="col-md-6 form-group">
						<label for="type">Type</label>
						<select name="type" class="dropsearch form-control" data-placeholder="" data-default="<?=set_value('type');?>">
							<option value="carcass" <?=set_select('type', 'carcass');?>>Carcass</option>
							<option value="incidental" <?=set_select('type', 'incidental');?>>Incidental</option>
							<option value="giblets" <?=set_select('type', 'giblets');?>>Giblets</option>
							<option value="byproducts" <?=set_select('type', 'byproducts');?>>Byproducts</option>
						</select>
						<!-- <input type="text" name="type" class="form-control" value="<?=set_value('type');?>"> -->

						<div class="red-text flash-message">
							<?=form_error('type');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('stock_keeping_units')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
