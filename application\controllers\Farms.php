<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Farms extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];
    protected $_fields = [
        'name'
    ];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->farm_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('farms/manager'));
    }

    /**
     * Get units of farm
     */
    public function get_data($farm_id = null)
    {
        $where = [
            'id' => $farm_id,
            'is_deleted' => 0
        ];
        if (!$farm = $this->farm->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No farm is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
            ];
            foreach($this->_fields AS $field) {
                $response[$field] = $farm->{$field};
            }
        }

        echo json_encode($response);
    }

    /**
     * Add farm
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('farms/add')) && $id = $this->_attempt_add()) {
            redirect(site_url('farms/manager'));
        } else {
            $this->title = 'Add Farm';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $farms = $this->farm->select('', $where);

            $data = [
                'farms' => $farms,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'farms/add', $data);
        }
    }

    /**
     * Callback: Check if farm name exists
     */
    public function _is_farm($farm_name)
    {
        $where = [
            'name' => $farm_name,
            'is_deleted' => 0
        ];

        if (strtolower($this->farm_name) === strtolower($farm_name)) {
            return true;
        }

        return empty($this->farm->select('', $where, 1));
    }

    /**
     * Edit farm
     */
    public function edit($farm_id)
    {
        $where = [
            'id' => $farm_id,
            'is_deleted' => 0
        ];
        $farm = $this->farm->select('', $where, 1) or show_404();
        $this->farm_name = $farm->name;

        $where = ['is_deleted' => 0];

        if ($this->_validate_form($this->config->item('farms/edit')) and $this->_attempt_edit($farm)) {
            redirect(site_url('farms/manager'));
        } else {
            $this->title = 'Edit Farm';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $farms = $this->farm->select('', $where);

            $data = [
                'form_error' => $form_error,
                'farm' => $farm,
                'farms' => $farms,
            ];
            $this->build_content('default', 'farms/edit', $data);
        }
    }

    /**
     * Delete farm
     */
    public function delete($farm_id)
    {
        $where = [
            'id' => $farm_id,
            'is_deleted' => 0
        ];
        if (!$farm = $this->farm->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Farm is not found.'
            ];
        } elseif (!$this->_attempt_delete($farm)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Farm is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * farms
     */
    public function manager()
    {
        $this->title = 'Farms';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/farms/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'farms/manager', $data);
    }

    /**
     * Get Info of farm
     */
    public function info($farm_id = '')
    {
        $where = [
            'id' => $farm_id,
            'is_deleted' => 0
        ];
        if (!$farm = $this->farm->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Farm is not found.'
            ];
        } else {
            $response = [
                'message' => 'Farm is found.',
                'farm' => $farm
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of farm by name
     */
    public function info_by_name($farm_name = '')
    {
        $where = [
            'name' => urldecode($farm_name),
            'is_deleted' => 0
        ];

        if (!$farm = $this->farm->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Farm is not found.'
            ];
        } else {
            $response = [
                'message' => 'Farm is found.',
                'farm' => $farm
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('farm');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        foreach($this->_fields AS $field) {
            $values[$field] = $this->input->post($field);
        }


        if (!$farm_id = $this->farm->insert($values)) {
            $this->_error = 'server_error';
        }

        return $farm_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($farm)
    {
        $where = [
            'id' => $farm->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        foreach($this->_fields AS $field) {
            $values[$field] = $this->input->post($field);
        }

        if (!$is_updated = $this->farm->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($farm)
    {
        $where = [
            'id' => $farm->id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s'),
        ];

        if (!$is_deleted = $this->farm->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
