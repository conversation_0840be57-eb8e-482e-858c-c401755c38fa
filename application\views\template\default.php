<!DOCTYPE html>
<html lang="en">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<title><?= isset($title) ? $title . ' | Leyte Agri Ventures Corp.' : 'Leyte Agri Ventures Corp.'; ?></title>

	<link href="<?= base_url('favicon.png?v=2'); ?>" rel="shortcut icon" type="image/x-icon">
	<link href="<?= base_url('lib/font-awesome-4.7.0/css/font-awesome.min.css'); ?>" rel="stylesheet" type="text/css">
	<link href="<?= base_url('lib/jquery-ui/jquery-ui.min.css'); ?>" rel="stylesheet" type="text/css">
	<link href="<?= base_url('lib/bootstrap-4/css/bootstrap.min.css'); ?>" rel="stylesheet" type="text/css">
	<?= $_styles; ?>
	<link href="<?= base_url('css/style.css'); ?>" rel="stylesheet" type="text/css">
</head>

<body>
	<div class="app-side-menu">

		<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER])): ?>
			<div class="brand">
				<a href="<?= site_url('dressing_plant_dashboards/manager'); ?>">
					<img src="<?= base_url('img/icons/lavc_logo.png'); ?>" style="height: 50px;" alt="Brand Logo">
				</a>
			</div>
		<?php else: ?>
			<div class="brand">
				<img src="<?= base_url('img/icons/lavc_logo.png'); ?>" style="height: 50px;" alt="Brand Logo">
			</div>
		<?php endif; ?>

		<i id="expense-reveal" class="fa fa-bars fa-2x toggle-btn" aria-hidden="true" data-toggle="collapse"
			data-target="#expense"></i>
		<i id="sale-reveal" class="fa fa-bars fa-2x toggle-btn d-none" aria-hidden="true" data-toggle="collapse"
			data-target="#sale"></i>
		<i id="form-reveal" class="fa fa-bars fa-2x toggle-btn d-none" aria-hidden="true" data-toggle="collapse"
			data-target="#form"></i>
		<i id="report-reveal" class="fa fa-bars fa-2x toggle-btn d-none" aria-hidden="true" data-toggle="collapse"
			data-target="#report"></i>
		<i id="manage-dressing-plant-reveal" class="fa fa-bars fa-2x toggle-btn d-none" aria-hidden="true"
			data-toggle="collapse" data-target="#manage-dressing-plant"></i>

		<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER])): ?>
			<div class="menu-list">
				<ul id="dashboard" class="menu-content collapse out">
					<li>
						<a href="<?= site_url('dressing_plant_dashboards/manager'); ?>"
							class="btn btn-link link-unstyled no-padding"><i class="fa fa-home fa-lg"></i> Dashboard</a>
					</li>
				</ul>
			</div>
		<?php endif; ?>

		<!-- Expenses -->
		<div class="menu-list">
			<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
				<ul id="expense" class="menu-content collapse out">
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menu-content_1">
							<i class="fa fa-money fa-lg"></i> Expenses</a>
					</li>
				</ul>
			<?php endif; ?>

			<ul id="menu-content_1" class="menu-content collapse out whole-menu">
				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('purchases/manager?status=' . ($_SESSION['user']->role_id === '3' ? 'printed' : 'pending')); ?>"
							class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cart-arrow-down fa-lg"></i> Purchase Orders</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_CENTRAL_ACCOUNTING])): ?>
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menuChekMaker">
							<i class="fa fa-check fa-lg"></i> Check Maker</a>

						<div id="menuChekMaker" class="collapse sub-menu">
							<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_CENTRAL_ACCOUNTING])): ?>
								<a href="<?= site_url('checks'); ?>" class="btn btn-link link-unstyled no-padding">Checks</a>
							<?php endif; ?>

							<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_CENTRAL_ACCOUNTING])): ?>
								<a href="<?= site_url('banks'); ?>" class="btn btn-link link-unstyled no-padding">Banks</a>
							<?php endif; ?>

							<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_CENTRAL_ACCOUNTING])): ?>
								<a href="<?= site_url('payees'); ?>" class="btn btn-link link-unstyled no-padding">Payees</a>
							<?php endif; ?>

							<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_CENTRAL_ACCOUNTING])): ?>
								<a href="<?= site_url('signatories'); ?>"
									class="btn btn-link link-unstyled no-padding">Signatories</a>
							<?php endif; ?>
						</div>
					</li>
				<?php endif; ?>

				<!-- <li>
					<a href="<?= site_url('cash_fund'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-money fa-lg"></i> Cash Fund</a>
				</li> -->
			</ul>
		</div>

		<!-- Sales -->
		<div class="menu-list">
			<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_AUDIT])): ?>
				<ul id="sale" class="menu-content collapse out">
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menu-content_2">
							<i class="fa fa-money fa-lg"></i> Sales</a>
					</li>
				</ul>
			<?php endif; ?>

			<ul id="menu-content_2" class="menu-content collapse out whole-menu">
				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_CASHIER])): ?>
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menuPOSInvoice">
							<i class="fa fa-desktop fa-lg"></i> POS Invoice</a>

						<div id="menuPOSInvoice" class="collapse sub-menu">
							<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_CASHIER])): ?>
								<a href="<?= site_url('pos_invoices/add'); ?>" class="btn btn-link link-unstyled no-padding">Add
									POS Invoice</a>
							<?php endif; ?>

							<a href="<?= site_url('pos_invoices/cash'); ?>"
								class="btn btn-link link-unstyled no-padding">Cash</a>

							<a href="<?= site_url('pos_invoices/credit'); ?>"
								class="btn btn-link link-unstyled no-padding">Credit</a>

							<a href="<?= site_url('pos_invoices/voided_invoices'); ?>"
								class="btn btn-link link-unstyled no-padding">Voided Invoices</a>

						</div>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('invoices/manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-envelope fa-lg"></i> Invoice</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('billing_statements/manager'); ?>"
							class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-file-text fa-lg"></i> Billing Statement</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_CASHIER, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('payments/manager?status=for_ar'); ?>"
							class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-dollar fa-lg"></i> Payments</a>
					</li>
				<?php endif; ?>
			</ul>
		</div>

		<!-- Form -->
		<div class="menu-list">
			<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
				<ul id="form" class="menu-content collapse out">
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menu-content_3">
							<i class="fa fa-table fa-lg"></i> Forms</a>
					</li>
				</ul>
			<?php endif; ?>

			<ul id="menu-content_3" class="menu-content collapse out whole-menu">
				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('cash_bonds/manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-money fa-lg"></i> Cash Bond</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_OFFICE, ROLE_ACCOUNTING])): ?>
					<li>
						<a href="<?= site_url('module_2307/manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-shopping-basket fa-lg"></i> 2307</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('eod/eodp_manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-clipboard fa-lg"></i> EOD-P</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('eod/eodd_manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-clipboard fa-lg"></i> EOD-D</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('eod/eods_manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-clipboard fa-lg"></i> EOD-S</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('eod/eodbc_manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-clipboard fa-lg"></i> EOD-BC</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('poc/manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-shopping-basket fa-lg"></i> POC</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<!-- <li>
						<a href="<?= site_url('gate_pass/manager'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-id-badge fa-lg"></i> Gate Pass</a>
					</li> -->
				<?php endif; ?>

				<!-- <li>
					<a href="<?= site_url('cash_bonds/manager'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa- fa-lg"></i> Cash Fund</a>
				</li> -->

				<!-- <li>
					<a href="<?= site_url('cash_bonds/manager'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa- fa-lg"></i> 2307</a>
				</li> -->
			</ul>
		</div>

		<!-- Report -->
		<div class="menu-list">
			<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_ACCOUNTING, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
				<ul id="report" class="menu-content collapse out">
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menu-content_4">
							<i class="fa fa-book fa-lg"></i> Reports</a>
					</li>
				</ul>
			<?php endif; ?>

			<ul id="menu-content_4" class="menu-content collapse out whole-menu">
				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('soas/add'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-file fa-lg"></i> Statements of Account</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_AUDIT, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menuReports">
							<i class="fa fa-money fa-lg"></i> Expenses Report</a>
						<div id="menuReports" class="collapse sub-menu">
							<a href="<?= site_url('account_types/purchase'); ?>"
								class="btn btn-link link-unstyled no-padding">By Account Names</a>

							<a href="<?= site_url('expenses/index'); ?>" class="btn btn-link link-unstyled no-padding">By
								Supplier</a>
						</div>
						</a>
					</li>
				<?php endif; ?>

				<!-- <li>
					<a href="<?= site_url('cash_bonds/manager'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa- fa-lg"></i> Disbursement Report</a>
				</li> -->

				<!-- <li>
					<a href="<?= site_url('cash_bonds/manager'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa- fa-lg"></i> Accounts Payable</a>
				</li> -->

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_AUDIT, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menuReports_2">
							<i class="fa fa-money fa-lg"></i> Sales Report</a>
						<div id="menuReports_2" class="collapse sub-menu">
							<a href="<?= site_url('account_types/sales'); ?>" class="btn btn-link link-unstyled no-padding">By
								Account Names</a>

							<a href="<?= site_url('customers/customers_sale'); ?>"
								class="btn btn-link link-unstyled no-padding">By Customer</a>
						</div>
						</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('pos_invoices/payment_ledger'); ?>"
							class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-book fa-lg"></i> Payment Ledger</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('pos_invoices/sales_report'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-book fa-lg"></i> POS Sales Report</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('payables/aging_report'); ?>" class="btn btn-link link-unstyled no-padding"><i
								class="fa fa-calendar fa-lg"></i> Accounts Payable Aging</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('receivables/aging_report'); ?>"
							class="btn btn-link link-unstyled no-padding"><i class="fa fa-calendar fa-lg"></i> Accounts
							Receivable Aging</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_BILLING, ROLE_CASHIER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('invoices/bad_debts'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-exclamation-triangle fa-lg"></i> Accounts Receivable Problematic
						</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_SUPERVISOR, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('incomes/index'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-line-chart fa-lg"></i> Income Statement Report</a>
					</li>
					<li>
						<a href="<?= site_url('cash_flows/index'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-line-chart fa-lg"></i> Cash Flow Statement</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_SUPERVISOR, ROLE_GENERAL_MANAGER, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('cash_bonds/report'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-handshake-o fa-lg"></i> Cash Bond Receivable Aging</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_SUPERVISOR, ROLE_GENERAL_MANAGER, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('purchases/capex_report'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-building fa-lg"></i> CAPEX Report</a>
					</li>
				<?php endif; ?>

				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_SUPERVISOR, ROLE_GENERAL_MANAGER, ROLE_OFFICE, ROLE_AUDIT])): ?>
					<li>
						<a href="<?= site_url('purchases/fund_transfer_report'); ?>"
							class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-exchange fa-lg"></i> Fund Transfer Report</a>
					</li>
				<?php endif; ?>

				<!-- <li>
					<a href="<?= site_url('cash_flows/index'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-dollar fa-lg"></i> Cash Fund Daily Report</a>
				</li> -->
			</ul>
		</div>

		<!-- Operations -->
		<div class="menu-list">
			<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING, ROLE_SUPERVISOR, ROLE_GENERAL_MANAGER, ROLE_OFFICE, ROLE_AUDIT])): ?>
				<ul id="operation" class="menu-content collapse out">
					<li>
						<a class="menu collapsed" role="button" data-toggle="collapse"
							data-target="#menu-content_operation">
							<i class="fa fa-gear fa-lg"></i> Operations</a>
					</li>
				</ul>
			<?php endif; ?>
			<ul id="menu-content_operation" class="menu-content collapse out whole-menu">
				<?php if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_SUPERVISOR])): ?>
					<li>
						<a href="<?= site_url('schedules'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-calendar fa-lg"></i> Schedule</a>
					</li>

					<li>
						<a href="<?= site_url('hauling_logs'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cogs fa-lg"></i> Hauling Log</a>
					</li>

					<li>
						<a href="<?= site_url('bird_counts'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cogs fa-lg"></i> Bird Count</a>
					</li>

					<li>
						<a href="<?= site_url('Skus'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cogs fa-lg"></i> SKU</a>
					</li>

					<li>
						<a href="<?= site_url('Carcasses'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cogs fa-lg"></i>Carcass</a>
					</li>

					<li>
						<a href="<?= site_url('Giblets'); ?>" class="btn btn-link link-unstyled no-padding">
							<i class="fa fa-cogs fa-lg"></i>Giblets</a>
					</li>
				<?php endif; ?>
			</ul>
		</div>

		<!-- Manage -->
		<div
			class="menu-list <?= !in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER]) ? 'd-none' : '' ?>">
			<ul id="manage-dressing-plant" class="menu-content collapse out">
				<li>
					<a class="menu collapsed" role="button" data-toggle="collapse" data-target="#menu-content_5">
						<i class="fa fa-cogs fa-lg"></i> Manage</a>
				</li>
			</ul>
			<ul id="menu-content_5" class="menu-content collapse out whole-menu">
				<li>
					<a href="<?= site_url('account_types'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-user fa-lg"></i> Chart of Accounts</a>
				</li>

				<li>
					<a href="<?= site_url('customers'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-users fa-lg"></i> Customers</a>
				</li>

				<li>
					<a href="<?= site_url('suppliers'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-truck fa-lg"></i> Suppliers</a>
				</li>

				<!-- <li>
					<a href="<?= site_url('farms'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-tree fa-lg"></i> Farms</a>
				</li> -->

				<li>
					<a href="<?= site_url('items'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-shopping-bag fa-lg"></i> Items</a>
				</li>

				<li>
					<a href="<?= site_url('pos_items'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-cart-arrow-down fa-lg"></i> POS Items</a>
				</li>

				<li>
					<a href="<?= site_url('poc_items'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-cart-arrow-down fa-lg"></i> POC Items</a>
				</li>

				<li>
					<a href="<?= site_url('sales_items'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-cart-arrow-down fa-lg"></i> Invoice Items</a>
				</li>

				<li>
					<a href="<?= site_url('usages'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-pie-chart fa-lg"></i> Usages</a>
				</li>

				<li>
					<a href="<?= site_url('users'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-users fa-lg"></i> Users</a>
				</li>
			</ul>
		</div>

		<div class="menu-list">
			<ul id="logout" class="menu-content collapse out">
				<li>
					<a href="<?= site_url('logout'); ?>" class="btn btn-link link-unstyled no-padding">
						<i class="fa fa-sign-out fa-lg"></i> Logout</a>
				</li>
			</ul>
		</div>
	</div>

	<div class="app-content">
		<?= $content; ?>

		<div class="mb-5"></div>
	</div>

	<footer class="app-footer">
		<small>Powered by MYT SoftDev Solutions. Cebu City, Philippines</small>
	</footer>

	<script>
		'use strict';
		var BASE_URI = '<?= site_url(); ?>';
	</script>

	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<script src="<?= base_url('lib/jquery/jquery-3.3.1.min.js'); ?>"></script>
	<script src="<?= base_url('lib/jquery-ui/jquery-ui.min.js'); ?>"></script>
	<script src="<?= base_url('lib/popper/popper.min.js'); ?>"></script>
	<script src="<?= base_url('lib/bootstrap-4/js/bootstrap.min.js'); ?>"></script>
	<script src="<?= base_url('lib/date-fns/date-fns.min.js'); ?>"></script>
	<?= $_external_scripts; ?>
	<?= $_inline_scripts; ?>
	<script src="<?= base_url('js/template/common.min.js'); ?>"></script>
</body>

</html>