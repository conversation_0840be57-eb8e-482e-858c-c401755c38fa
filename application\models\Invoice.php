<?php
class Invoice extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'invoice';
        parent::__construct();
    }

    /**
     * Get Invoice By ID
     */
    public function get_by_id($invoice_id)
    {
        $sql = <<<EOT
SELECT invoice.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer, approver.full_name as approver
FROM invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
LEFT JOIN user AS approver ON approver.id = invoice.approved_by
WHERE invoice.id = ?
EOT;
    // AND invoice.is_deleted = 0
        $binds = [$invoice_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Invoice By ID for Payment
     */
    public function get_for_payment($invoice_id)
    {
        $sql = <<<EOT
SELECT 
    invoice.id,
    invoice.customer_id,
    invoice.invoice_date,
    invoice.invoice_no,
    invoice.terms,
    invoice.remarks,
    invoice.due_date,
    invoice.subtotal,
    invoice.discount,
    invoice.total,
    SUM(paid_invoice.amount) as paid_amount,
    invoice.status,
    invoice.is_reviewed,
    invoice.approved_by,
    invoice.added_by,
    invoice.added_on,
    invoice.updated_by,
    invoice.updated_on,
    invoice.is_deleted,
    invoice.delete_reason,
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email, 
    issuer.full_name AS issuer, 
    approver.full_name as approver
FROM invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
LEFT JOIN user AS approver ON approver.id = invoice.approved_by
LEFT JOIN paid_invoice ON paid_invoice.invoice_id = invoice.id 
    AND paid_invoice.doc_type = 'invoice'
    AND paid_invoice.is_deleted = 0
WHERE invoice.id = ?
GROUP BY invoice.id
EOT;
    // AND invoice.is_deleted = 0
        $binds = [$invoice_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Partially Paid Invoices
     */
    public function get_partially_paid($customer_id)
    {
        $sql = <<<EOT
SELECT 
    invoice.id,
    invoice.customer_id,
    invoice.invoice_date,
    invoice.invoice_no,
    invoice.terms,
    invoice.remarks,
    invoice.due_date,
    invoice.subtotal,
    invoice.discount,
    invoice.total,
    SUM(paid_invoice.amount) as paid_amount,
    invoice.status,
    invoice.is_reviewed,
    invoice.approved_by,
    invoice.added_by,
    invoice.added_on,
    invoice.updated_by,
    invoice.updated_on,
    invoice.is_deleted,
    invoice.delete_reason,
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email, 
    issuer.full_name AS issuer
FROM invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
LEFT JOIN paid_invoice ON paid_invoice.invoice_id = invoice.id 
    AND paid_invoice.doc_type = 'invoice'
    AND paid_invoice.is_deleted = 0
WHERE paid_amount < invoice.total
    AND invoice.customer_id = ?
    AND invoice.is_deleted = 0
    AND invoice.status = "approved"
GROUP BY invoice.id
EOT;
        $binds = [$customer_id];
        
        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * Get Invoices Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT invoice.id, invoice.invoice_date, invoice.customer_id, invoice.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM invoice
LEFT JOIN (
    SELECT payment.payment_date, paid_invoice.invoice_id, paid_invoice.amount
    FROM payment
    LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND invoice.customer_id = ?
) payment ON payment.invoice_id = invoice.id
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
WHERE invoice.invoice_date < ?
    AND customer.id = ?
    AND invoice.is_deleted = 0
GROUP BY invoice.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Invoices Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT invoice.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM invoice
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
WHERE invoice.invoice_date BETWEEN ? AND ?
    AND invoice.customer_id = ?
    AND invoice.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Invoices Total Sales
     */
    public function get_total_monthly_sales($month = null, $year = null)
    {
        if(!empty($month) && !empty($year)){
            $sql = <<<EOT
SELECT SUM(invoice.paid_amount) AS total_monthly_sales
FROM invoice
WHERE is_deleted = 0
    AND MONTH(invoice.invoice_date) = ?
    AND YEAR(invoice.invoice_date) = ?
EOT;
            $binds = [$month, $year];
        } elseif(empty($month) && !empty($year)) {
            $sql = <<<EOT
SELECT SUM(invoice.paid_amount) AS total_monthly_sales
FROM invoice
WHERE is_deleted = 0
    AND YEAR(invoice.invoice_date) = ?
EOT;
            $binds = [$year];
        } else {
            $firstDayOfMonth = date('Y-m-01');
            $lastDayOfMonth = date('Y-m-t');

            $sql = <<<EOT
SELECT SUM(invoice.paid_amount) AS total_monthly_sales
FROM invoice
WHERE is_deleted = 0
    AND invoice.invoice_date BETWEEN ? AND ?
EOT;
            $binds = [$firstDayOfMonth, $lastDayOfMonth];
            
        }
        
        $res = $this->advanced_query($sql, $binds);

        if(!empty($res[0])){
            return $res[0]->total_monthly_sales;
        }else{
            return 0;
        }
    }

    /**
     * Get Invoices Total Receivables
     */
    public function get_total_receivables()
    {

        $sql = <<<EOT
SELECT SUM(invoice.total - invoice.paid_amount) AS total_receivables
FROM invoice
WHERE is_deleted = 0
    AND invoice.status = "approved"
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_receivables;
        }else{
            return 0;
        }
    }

    /**
     * Get the year of first transaction
     */
    public function get_first_year()
    {

        $sql = <<<EOT
SELECT MIN(years.first_year) AS first_year
FROM (
    SELECT DISTINCT MIN(YEAR(invoice_date)) AS first_year
    FROM invoice
    WHERE invoice.is_deleted = 0

    UNION

    SELECT DISTINCT MIN(YEAR(purchase_date)) AS first_year
    FROM purchase
    WHERE purchase.is_deleted = 0
) AS years
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->first_year;
        }else{
            return 0;
        }
    }


    /**
     * Get Invoices BY Customer
     */
    public function get_by_customer($customer_id, $date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT 
    invoice.id, 
    invoice.invoice_no, 
    invoice.terms, 
    invoice.invoice_date, 
    invoice.customer_id, 
    invoice.total AS charge, 
    invoice.total, 
    invoice.paid_amount,
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM invoice
LEFT JOIN (
    SELECT payment.payment_date, paid_invoice.invoice_id, paid_invoice.amount
    FROM payment
    LEFT JOIN paid_invoice ON paid_invoice.payment_id = payment.id
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    WHERE payment.is_deleted = 0
        AND paid_invoice.is_deleted = 0
        AND invoice.customer_id = ?
) payment ON payment.invoice_id = invoice.id
LEFT JOIN customer ON customer.id = invoice.customer_id
LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
WHERE customer.id = ?
    AND invoice.is_deleted = 0
EOT;
        $binds = [$customer_id, $customer_id];

    if($date_from && $date_to) {
        $sql .= <<<EOT

AND invoice.invoice_date BETWEEN ? AND ?
EOT;
        $binds[] = $date_from;
        $binds[] = $date_to;
    } elseif($date_from) {
        $sql .= <<<EOT

AND invoice.invoice_date >= ?
EOT;
        $binds[] = $date_from;
    } elseif($date_to) {
        $sql .= <<<EOT

AND invoice.invoice_date <= ?
EOT;
        $binds[] = $date_to;
    }

        $sql .= <<<EOT

GROUP BY invoice.id
EOT;

        // die($this->db->compile_binds($sql, $binds));
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Invoices BY Customer
     */
    public function get_entries_by_customer($customer_id, $date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT *
FROM (
    SELECT
        customer.id AS customer_id,
        invoice.id, 
        invoice.invoice_date AS transaction_date, 
        invoice.invoice_no AS reference, 
        '' AS mop, 
        invoice.total,
        invoice.paid_amount,
        '' AS ar_no,
        0 AS ar_amount,
        '' AS pr_no,
        0 AS pr_amount,
        issuer.full_name AS issuer,
        1 AS priority
    FROM invoice
    LEFT JOIN customer ON customer.id = invoice.customer_id
    LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
    WHERE invoice.is_deleted = 0
        
    UNION ALL

    SELECT
        customer.id AS customer_id,
        invoice.id, 
        payment.payment_date AS transaction_date, 
        '' AS reference, 
        payment.type AS mop, 
        0 AS total,
        0 AS paid_amount,
        CASE
            WHEN ((payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL)) THEN payment.ar_no
            ELSE ''
        END AS ar_no,
        CASE
            WHEN ((payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL)) THEN payment.total_paid
            ELSE 0
        END AS ar_amount,
        CASE
            WHEN ((payment.ar_no IS NOT NULL AND payment.pr_no IS NOT NULL) OR (payment.type = 'cash' AND payment.pr_no IS NOT NULL)) THEN payment.pr_no
            ELSE ''
        END AS pr_no,
        CASE
            WHEN ((payment.ar_no IS NOT NULL AND payment.pr_no IS NOT NULL) OR (payment.type = 'cash' AND payment.pr_no IS NOT NULL)) THEN payment.total_paid
            ELSE 0
        END AS pr_amount,
        issuer.full_name AS issuer,
        2 AS priority
    FROM paid_invoice
    LEFT JOIN payment ON payment.id = paid_invoice.payment_id
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    LEFT JOIN customer ON customer.id = invoice.customer_id
    LEFT JOIN user AS issuer ON issuer.id = invoice.added_by
    WHERE paid_invoice.is_deleted = 0
        AND paid_invoice.doc_type = "invoice"
        AND payment.is_deleted = 0
    GROUP BY paid_invoice.payment_id
) AS entries
WHERE entries.customer_id = ?
EOT;
        $binds = [$customer_id];

        if(!empty($date_from) && !empty($date_to)){
            $sql .= <<<EOT

AND entries.transaction_date BETWEEN ? AND ?
EOT;
            $binds[] = $date_from;
            $binds[] = $date_to;
        }

        $sql .= <<<EOT

ORDER BY entries.transaction_date ASC, entries.priority ASC
EOT;

        // die($this->db->compile_binds($sql, $binds));
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get All Invoices by Customer That has no Cash Bond
     */
    public function get_invoices_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT *
FROM invoice
WHERE invoice.is_deleted = 0
    AND invoice.customer_id = ?
    AND invoice.id NOT IN (
        SELECT DISTINCT invoice_id
        FROM cash_bond_item
        LEFT JOIN cash_bond ON cash_bond.id = cash_bond_item.cash_bond_id
        WHERE cash_bond_item.is_deleted = 0
            AND cash_bond.status <> 'cancelled'
    )
GROUP BY invoice.id
EOT;
        $binds = $customer_id;

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return false;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */