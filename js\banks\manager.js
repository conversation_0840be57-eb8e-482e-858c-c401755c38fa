(function($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			scrollX: true,
			autoWidth: false,
			pagingType: 'full_numbers',
			order: [[0, 'asc']],
			columnDefs: [
				{
					className: 'dt-action',
					targets: cols.length - 1
				}
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(bankId, dataTable) {
		$.when(deleteRow(bankId)).then(
			function() {
				dataTable.ajax.reload(null, false);
			},
			function(jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(bankId) {
		return $.ajax({
			url: BASE_URI + 'banks/delete/' + bankId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function() {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function(event) {
			var cols = $(event.currentTarget)
				.closest('tr.bank')
				.find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify
				.confirm('Are you sure you want to delete ' + name + '?', function() {
					var bankId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(bankId, dataTable);
				})
				.setHeader('<em>Delete Bank</em>');
		});
	});
})(jQuery);
