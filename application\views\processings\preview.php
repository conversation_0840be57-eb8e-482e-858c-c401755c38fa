<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><b><large>Customer : </large></b> <?=$processing->customer;?></p>
					<p class="text-left"><b><large>Remarks : </large></b> <?=$processing->remarks;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><b><large>Order Slip No. : </large></b> <?=anchor(site_url("order_slips/preview/" . $processing->order_slip_id), $processing->order_slip_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Schedule Hauling No. : </large></b> <?=anchor(site_url("schedule_haulings/preview/" . $processing->schedule_hauling_id), $processing->schedule_hauling_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p>
					<p class="text-left"><b><large>Processing Date : </large></b> <?=date('m/d/Y', strtotime($processing->processing_date));?></p>
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>Efficiency Report</span></h5>

				<div class="row sect-jr">
					<div class="col-sm-4">
						<p class="text-left"><b>Total Live Arrived : </b><?=$processing->total_live_arrived;?></p>
						<p class="text-left"><b>Total Death on Arrival : </b><?=$processing->total_death_on_arrival;?></p>
						<p class="text-left"><b>Total to be Processed : </b><?=$processing->total_to_be_processed;?></p>
						<p class="text-left"><b>ALW. : </b><?=$processing->alw;?></p>
					</div>

					<div class="col-sm-4">
						<p class="text-left"><b>Total Death after Arrival : </b><?=$processing->total_death_after_arrival;?></p>
						<p class="text-left"><b>LA in Kilos : </b><?=$processing->la_in_kilos;?></p>
						<p class="text-left"><b>LP in Kilos : </b><?=$processing->lp_in_kilos;?></p>
						<p class="text-left"><b>Coops Loaded : </b><?=$processing->coops_loaded;?></p>
					</div>

					<div class="col-sm-4">
						<p class="text-left"><b>DOA in Kilos : </b><?=$processing->doa_in_kilos;?></p>
						<p class="text-left"><b>DAA in Kilos : </b><?=$processing->daa_in_kilos;?></p>
						<p class="text-left"><b>Total Trips : </b><?=$processing->total_trips;?></p>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Processed Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Classification</th>

									<th class="text-center" style="width: 15%;">Crates</th>

									<th class="text-center" style="width: 15%;">Heads</th>

									<th class="text-center" style="width: 15%;">Kilos</th>

									<th class="text-center" style="width: 15%;">Recovery</th>
								</tr>
							</thead>

							<tbody>
							<?php
								$total_crates = 0;
								$total_heads = 0;
								$total_kilos = 0;
								$total_recovery = 0;
								if (!empty($processing_item_ids)):
                                	$counter = 0;
                                    foreach ($processing_item_ids as $i => $processing_item_id):
										$total_crates += $retain_items[$counter]['crates'];
										$total_heads += $retain_items[$counter]['heads'];
										$total_kilos += $retain_items[$counter]['kilos'];
										$total_recovery += $retain_items[$counter]['recovery'];
							?>
										<tr class="processing_item">
											<td><?=$retain_items[$counter]['item_name'];?></td>

											<td><?=$retain_items[$counter]['classification'];?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['crates']);?></td>
	
											<td class="text-right"><?=floatval($retain_items[$counter]['heads']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['kilos']);?></td>

											<td class="text-right"><?=floatval($retain_items[$counter]['recovery']);?></td>
										</tr>
							<?php
									$counter++;
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
									<tr class="processing_item">
										<td></td>

										<td class="text-right"><strong>Total: </td>

										<td class="text-right"><strong><?=floatval($total_crates);?></td>

										<td class="text-right"><strong><?=floatval($total_heads);?></td>

										<td class="text-right"><strong><?=floatval($total_kilos);?></td>

										<td class="text-right"><strong><?=floatval($total_recovery);?></td>
									</tr>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($processing->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>processing Attachments</span></h5>
						<?php $attachments = explode('🔥',$processing->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/processings/'.$processing->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$processing->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Order Confirmed By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('processings');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="processing_id" value="<?=$processing->id?>">
		</form>
	</div>
</div>