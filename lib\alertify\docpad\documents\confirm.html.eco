---
layout: 'default'
hljs: 	'light'
component:	'confirm'
---
<section class="blue">
    <div class="content">
      <div class="grid two">
        <div class="column">
          <h1> Confirm Dialog</h1>
          A confirm dialog is often used if you want the user to verify or accept something.
          When a confirm dialog pops up, the user will have to click either "OK" or "Cancel" to proceed.
        </div>
        <div class="right column">
          <%- @partial('ad') %>
        </div>
      </div>
    </div>
  </section>
  <section class="lic">
    <div class="content">
        Looking for a commercial license ?  Keep your source code proprietary and <a href="https://www.uplabs.com/posts/alertifyjs" target="_blank"> Buy a Commercial License Today!</a>
    </div>
  </section>
  <section class="dark">
    <div class="content">
      <!--Defult usage-->
      <div class="segment has-menu">
        <h1 id="usage">
          <a href="#usage"><i class="fa fa-book"></i></a>
          Default usage
        </h1>
        <hr />
        <p>You create a confirm dialog by invoking <code> alertify.confirm(...)</code></p>

        <div class="ui small info message">
          <div class="content">
            <p>
              <i class="fa fa-info-circle"></i>
              Calling <code>alertify.confirm()</code> the dialog parameter-less constructor will return the confirm dialog instance. You can use this syntax to modify dialog settings before showing it.
              Also you can get the dialog instance (singletons only) by invoking <code>alertify.dialog('confirm');</code>
            </p>
          </div>
        </div>

<pre>
<code class="lang-javascript">
/*
 * @title {String or DOMElement} The dialog title.
 * @message {String or DOMElement} The dialog contents.
 * @onok {Function} Invoked when the user clicks OK button.
 * @oncancel {Function} Invoked when the user clicks Cancel button or closes the dialog.
 *
 * alertify.confirm(title, message, onok, oncancel);
 *
 */
alertify.confirm('Confirm Title', 'Confirm Message', function(){ alertify.success('Ok') }
                , function(){ alertify.error('Cancel')});
</code> 
</pre>
        <div class="RIGHT">
          <a class="button example" href="javascript:void(0);">
            <span> Run </span>
          </a>
        </div>
        <h1 id="overloads">
		      <a href="#overloads"><i class="fa fa-cogs"></i></a>
		      Overloads
        </h1>
        <p>For convenience, the following overloads are also available:</p>
        
<pre>
<code class="lang-javascript">
/*
 * @message {String or DOMElement} The dialog contents.
 *
 *  alertify.confirm(message);
 *
 */
alertify.confirm('Confirm Message');
</code> 
</pre>
<pre>

<code class="lang-javascript">
/*
 * @message {String or DOMElement} The dialog contents.
 * @onok {Function} Invoked when the user clicks OK button.
 *
 *  alertify.confirm(message, onok);
 *
 */  
alertify.confirm('Confirm Message', function(){ alertify.success('Ok') });
</code> 
</pre>
<pre>
<code class="lang-javascript">
/*
 * @message {String or DOMElement} The dialog contents.
 * @onok {Function} Invoked when the user clicks OK button.
 * @oncancel {Function} Invoked when the user clicks Cancel button or closes the dialog.
 *
 *  alertify.confirm(message, onok, oncancel);
 *
 */
alertify.confirm('Confirm Message', function(){ alertify.success('Ok') }, function(){ alertify.error('Cancel')});
</code> 
</pre>
      </div>
      <!--//Defult usage-->
     
     <%- @partial('menu', true) %>
    </div>
  </section>