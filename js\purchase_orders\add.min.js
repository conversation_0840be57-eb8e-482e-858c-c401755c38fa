!function(e){"use strict";function t(){var t=e(r());n(t);var a=e(".item-table"),i=a.find(".order_slip_item");0===i.length&&a.find(".no-item").remove(),a.find("tbody").append(t),l()}function n(t){t.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:e(this).attr("data-placeholder"),width:"250px",ajax:{url:BASE_URI+"items/search_by_item",dataType:"json",type:"GET",data:function(e){var t={term:e.term};return t},processResults:function(t){return{results:e.map(t,function(e){return{text:e.name,id:e.id}})}}}})}function a(t){e('select[name^="sub_usage"]').select2({tags:!1,allowClear:!0,placeholder:e(this).attr("data-placeholder"),width:"100%",ajax:{url:BASE_URI+"live_sell_sub_usages/search_by_usage",dataType:"json",type:"GET",data:function(e){var n={term:e.term,usage_id:t};return n},processResults:function(t){return{results:e.map(t,function(e){return{text:e.name,id:e.id}})}}}})}function r(){var e=i();return'<tr class="purchase_order_item"><td><input type="hidden" name="purchase_order_item_'+e+'" value="'+e+'"><select name="item_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+e+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="pref_alw_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="unit_price_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="amount_'+e+'" class="form-control" value="" min="0" step="any" readonly></td><td><input type="number" name="account_type_'+e+'" class="form-control" value="" min="0" step="any" ></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function i(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function o(){return'<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'}function l(){var t=e(".item-table"),n=t.find(".order_slip_item");n.each(function(t,n){e(n).find('input[name^="row"]').val(t+1)})}function u(e,t=null){e.find('input[name^="unit_"]').val(t)}function c(t){return e.ajax({url:BASE_URI+"live_sell_items/unit_of/"+t,type:"GET",dataType:"json"})}function s(t){return e.ajax({url:BASE_URI+"live_sell_customers/get_data/"+t,type:"GET",dataType:"json"})}function d(t){return e.ajax({url:BASE_URI+"live_sell_usages/get_sub_usage/"+t,type:"GET",dataType:"json"})}function m(t){e(t.currentTarget).closest(".order_slip_item").remove();var n=e(t.delegateTarget),a=n.find(".order_slip_item");if(a.length<1){var r=e(o());n.find("tbody").html(r)}l()}function p(t,n){return e.ajax({url:BASE_URI+"purchase_orders/delete_attachment/"+t,type:"POST",dataType:"json",data:{file_name:n},dataType:"json"})}function _(e){var t=parseFloat(e.find('input[name^="qty_"]').val());isNaN(t)&&(t=0);var n=e.find('input[name^="unit_price_"]').val();isNaN(n)&&(n=0);var a=t*n;e.find('input[name^="amount_"]').val(a)}e(function(){e(".action-add-item").click(t),e(".item-table").on("click",".action-delete-item",function(e){m(e)}).on("change",'select[name^="item"]',function(t){var n=e(t.currentTarget).closest(".order_slip_item"),a=t.currentTarget.value;e.when(c(a)).then(function(e){u(n,e.unit)},function(){u(n,null)}),console.log(a)}).on("input",'input[name^="qty_"]',function(t){var n=e(t.currentTarget).closest(".purchase_order_item");_(n)}).on("input",'input[name^="unit_price_"]',function(t){var n=e(t.currentTarget).closest(".purchase_order_item");_(n)}),e('select[name="customer"]').on("change",function(t){e(t.currentTarget).closest(".order_slip_item");var n=t.currentTarget.value;e.when(s(n)).then(function(t){console.log(t),e('input[name="address"]').val(t.address),e('input[name="contact_number"]').val(t.contact_number),e('input[name="email"]').val(t.email),e('input[name="available_credit"]').val(t.available_credit)},function(){e('input[name="address"]').val(""),e('input[name="contact_number"]').val(""),e('input[name="email"]').val(""),e('input[name="available_credit"]').val("")}),console.log(n)}),e('select[name="usage"]').on("change",function(t){var n=t.currentTarget.value;console.log(n),e.when(d(n)).then(function(e){console.log(e),a(n)},function(){})}),e(".attachment-table").on("click",".action-delete-attachment",function(t){var n=e(t.currentTarget).closest("tr.attachment").find("td"),a=void 0!==n[0]?n[0].innerHTML:"";let r=e(this);alertify.confirm("Are you sure you want to delete "+a+"?",function(){var n=t.currentTarget.getAttribute("data-id"),a=t.currentTarget.getAttribute("data-filename");e.when(p(n,a)).then(function(e){console.log(r.html()),r.parent().parent().remove()})}).setHeader("<em>Delete Attachment</em>")}),e(".order_slip_item").each(function(t,a){e(a).find("option").not(":selected").remove(),n(e(a))})})}(jQuery);