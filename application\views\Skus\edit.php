<div class="sku-add-container">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 3rem;">
                    <div class="sect">
                        <div class="col-md-12 form-group">
                            <form id="skuForm" method="post" action="<?= site_url('Skus/edit/' . $sku->id); ?>" class="crud-box" accept-charset="utf-8">
                                <h1 class="crud-title">Edit SKU</h1>

                                <?php if (validation_errors()): ?>
                                    <div class="alert alert-danger">
                                        <?= validation_errors(); ?>
                                    </div>
                                <?php endif; ?>

                                <?php if ($this->session->flashdata('error')): ?>
                                    <div class="alert alert-danger" id="customer-error-message">
                                        <?= $this->session->flashdata('error'); ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($form_error)): ?>
                                    <div class="alert alert-danger">
                                        <?= $form_error; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Customer Name field below Edit SKU title -->
                                <div class="row mb-3">
                                    <div class="col-md-4 form-group">
                                        <label for="customer_id" class="control-label">Customer Name</label>
                                        <select id="customer_id" class="form-control customer-select" name="customer_id" data-placeholder="Choose a customer..." onchange="if(this.value) document.getElementById('customer-error-message').style.display='none'">
                                            <option></option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?= htmlspecialchars($customer->id) ?>" <?= ($customer->id == $sku->customer_id) ? 'selected' : '' ?>>
                                                    <?= ucwords($customer->name) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="red-text flash-message"><?= form_error('customer_id'); ?></div>
                                    </div>
                                </div>

                                <fieldset>
                                    <h5 class="crud-subtitle text-center"><span>Carcass</span></h5>
                                    <br>

                                    <!-- Single row table like reference image -->
                                    <table class="table table-bordered item-table" id="skusTable" style="background-color: white;">
                                        <thead>
                                            <tr>
                                                <th style="width: 100px; text-align: center;">SKU</th>
                                                <th>Size</th>
                                                <th>Range</th>
                                                <th>By</th>
                                                <th style="width: 100px; text-align: center;">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="skusTableBody">
                                            <?php if (!empty($carcass_skus)): ?>
                                                <?php foreach ($carcass_skus as $i => $sku_item): ?>
                                                    <tr class="sku-row" data-row="<?= $i + 1 ?>">
                                                        <td class="text-center" style="vertical-align: middle;">
                                                            <input type="text" class="form-control" name="sku[]" value="<?= htmlspecialchars($sku_item->sku) ?>">
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" name="size[]" value="<?= htmlspecialchars($sku_item->size) ?>">
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" name="range[]" value="<?= htmlspecialchars($sku_item->size_range) ?>">
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" name="by[]" value="<?= htmlspecialchars($sku_item->by) ?>">
                                                        </td>
                                                        <td class="text-center" style="vertical-align: middle;">
                                                            <button type="button" class="btn btn-danger btn-sm action-delete-item">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr class="sku-row" data-row="1">
                                                    <td class="text-center" style="vertical-align: middle;">
                                                        <input type="text" class="form-control" name="sku[]" value="">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="size[]" value="">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="range[]" value="">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="by[]" value="">
                                                    </td>
                                                    <td class="text-center" style="vertical-align: middle;">
                                                        <button type="button" class="btn btn-danger btn-sm action-delete-item">Delete</button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>

                                    <!-- Add button -->
                                    <button type="button" class="btn btn-dark action-add-item" id="addSkuRow">Add SKU</button>
                                </fieldset>
                                <fieldset>
                                    <h5 class="crud-subtitle text-center"><span>Giblets & Secondary By Products</span></h5>
                                    <br>

                                    <table class="table table-bordered item-table" id="gibletsTable" style="background-color: white;">
                                        <thead>
                                            <tr>
                                                <th>SKU</th>
                                                <th>By</th>
                                                <th style="width: 100px; text-align: center;">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="gibletsTableBody">
                                            <?php if (!empty($giblets_skus)): ?>
                                                <?php foreach ($giblets_skus as $i => $sku_item): ?>
                                                    <tr class="giblets-row" data-row="<?= $i + 1 ?>">
                                                        <td class="text-center" style="vertical-align: middle;">
                                                            <input type="text" class="form-control" name="giblets_sku[]" value="<?= htmlspecialchars($sku_item->sku) ?>">
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" name="giblets_by[]" value="<?= htmlspecialchars($sku_item->by) ?>">
                                                        </td>
                                                        <td class="text-center" style="vertical-align: middle;">
                                                            <button type="button" class="btn btn-danger btn-sm action-delete-giblets">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr class="giblets-row" data-row="1">
                                                    <td class="text-center" style="vertical-align: middle;">
                                                        <input type="text" class="form-control" name="giblets_sku[]" value="">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="giblets_by[]" value="">
                                                    </td>
                                                    <td class="text-center" style="vertical-align: middle;">
                                                        <button type="button" class="btn btn-danger btn-sm action-delete-giblets">Delete</button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>

                                    <!-- Add button -->
                                    <button type="button" class="btn btn-dark action-add-giblets" id="addGibletsRow">Add SKU</button>
                                </fieldset>
                                <hr>
                                <div class="clearfix text-center">
                                    <button type="submit" class="btn btn-primary">Update</button>
                                    <a href="<?= site_url('Skus'); ?>" class="btn btn-light">Close</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
