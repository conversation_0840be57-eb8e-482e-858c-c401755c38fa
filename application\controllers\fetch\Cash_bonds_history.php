<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cash_bonds_history extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch cash bonds
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'cash_bond';
        $primary_key = 'cash_bond.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_cash_bond_date(),
            $this->_get_cash_bond_no(),
            $this->_get_terms(),
            $this->_get_remarks(),
            $this->_get_due_date(),
            $this->_get_cb_status()
        ];

        $joins = <<<EOT
cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN cash_bond_item ON cash_bond_item.cash_bond_id = cash_bond.id
LEFT JOIN invoice ON invoice.id = cash_bond_item.invoice_id
EOT;

    $customer = $this->input->get('customer', true);

    $where = <<<EOT
cash_bond.customer_id = "{$customer}"
AND cash_bond.is_deleted = 0
AND invoice.is_deleted = 0
EOT;

    $group_by = " cash_bond.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'cash_bond.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'cash_bond.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'cash_bond';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'cash_bond.status',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }



    /**
     * Get cash_bond date
     */
    protected function _get_cash_bond_date()
    {
        return [
            'db' => 'DATE_FORMAT(cash_bond.cb_date, "%b %d, %Y")',
            'as' => 'cash_bond_date',
            'dt' => 0,
            'field' => 'cash_bond_date'
        ];
    }

    /**
     * Get cash_bond document number
     */
    protected function _get_cash_bond_no()
    {
        return [
            'db' => 'GROUP_CONCAT(CONCAT("• ",invoice.invoice_no) SEPARATOR "<br>")',
            'as' => 'doc_no',
            'dt' => 1,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get terms
     */
    protected function _get_terms()
    {
        return [
            'db' => 'cash_bond.terms',
            'dt' => 2,
            'field' => 'terms',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get Remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'cash_bond.remarks',
            'dt' => 3,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return nl2br($d);
            }
        ];
    }

    /**
     * Get due date
     */
    protected function _get_due_date()
    {
        return [
            'db' => 'DATE_FORMAT(cash_bond.due_date, "%b %d, %Y")',
            'as' => 'due_date',
            'dt' => 4,
            'field' => 'due_date'
        ];
    }

    /**
     * Get cash_bond Amount
     */
    protected function _get_cb_status()
    {
        return [
            'db' => 'cash_bond.status',
            'as' => 'cb_status',
            'dt' => 5,
            'field' => 'cb_status'
        ];
    }

}
