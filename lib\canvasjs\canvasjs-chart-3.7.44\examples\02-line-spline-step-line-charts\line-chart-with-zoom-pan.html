<!DOCTYPE HTML>
<html>
<head>  
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	zoomEnabled: true,
	title:{
		text: "Try Zooming and Panning" 
	},
	axisY :{
		includeZero:false
	},
	data: data  // random generator below
});
chart.render();

}

var limit = 1000;

var y = 0;
var data = [];
var dataSeries = { type: "line" };
var dataPoints = [];
for (var i = 0; i < limit; i += 1) {
	y += (Math.random() * 10 - 5);
	dataPoints.push({
		x: i - limit / 2,
		y: y                
	});
}
dataSeries.dataPoints = dataPoints;
data.push(dataSeries);               

</script>
<script src="../../canvasjs.min.js"></script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;">
</div>
</body>
</html>