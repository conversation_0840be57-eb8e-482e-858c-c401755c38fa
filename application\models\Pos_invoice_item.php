<?php
class Pos_invoice_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'pos_invoice_item';
        parent::__construct();
    }

    /**
     * Generate POS Invoice Item
     */
    public function generate($id, $pos_invoice_id, $pos_item_id, $qty, $price, $amount, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO pos_invoice_item(id, pos_invoice_id, pos_item_id, qty, price, amount, added_by, added_on)
VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE
pos_invoice_item.id = LAST_INSERT_ID(pos_invoice_item.id),
pos_invoice_item.pos_item_id = VALUES(pos_invoice_item.pos_item_id),
pos_invoice_item.qty = VALUES(pos_invoice_item.qty),
pos_invoice_item.price = VALUES(pos_invoice_item.price),
pos_invoice_item.amount = VALUES(pos_invoice_item.amount),
pos_invoice_item.updated_by = VALUES(pos_invoice_item.added_by),
pos_invoice_item.updated_on = VALUES(pos_invoice_item.added_on),
pos_invoice_item.is_deleted = 0;
EOT;

        $binds = [$id, $pos_invoice_id, $pos_item_id, $qty, $price, $amount, $added_by, $added_on];

        return $this->advanced_query($sql, $binds);
    }




    /**
     * Get POS Invoice Items by POS Invoice
     */
    public function get_by_pos_invoice($pos_invoice_id)
    {
        $sql = <<<EOT
SELECT pos_invoice_item.*, pos_item.name AS item
FROM pos_invoice_item
LEFT JOIN pos_item ON pos_item.id = pos_invoice_item.pos_item_id
WHERE pos_invoice_item.pos_invoice_id = ?
    AND pos_invoice_item.is_deleted = 0
EOT;
        $binds = [$pos_invoice_id];

        if($res = $this->advanced_query($sql, $binds)){
            return $res;
        }else{
            return null;
        }

    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
