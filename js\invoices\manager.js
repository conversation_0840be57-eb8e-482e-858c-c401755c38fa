+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: cols.length - 3
			}, {
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function forApprovalAndReload(invoiceId, dataTable) {
		$.when(forApproval(invoiceId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function forApproval(invoiceId) {
		return $.ajax({
			url: BASE_URI + 'invoices/for_approval/' + invoiceId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(invoiceId, reason, dataTable) {
		$.when(deleteRow(invoiceId, reason)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function setAndReload(invoiceId, dataTable) {
		$.when(badDebtRow(invoiceId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function badDebtRow(invoiceId, reason) {
		return $.ajax({
			url: BASE_URI + 'invoices/bad_debt/' + invoiceId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteRow(invoiceId, reason) {
		return $.ajax({
			url: BASE_URI + 'invoices/delete/' + invoiceId,
			type: 'POST',
			dataType: 'json',
			data:{
				reason : reason
			}
		});
	}

  function pendingAndReload(invoiceId, dataTable) {
		$.when(pendingRow(invoiceId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function pendingRow(invoiceId) {
		return $.ajax({
			url: BASE_URI + 'invoices/return_to_pending/' + invoiceId,
			type: 'POST',
			dataType: 'json'
		});
  }

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-for-approval', function (event) {
				var cols = $(event.currentTarget).closest('tr.invoice').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to recommend for approval the Billing No. ' + docNo + '?',
					function () {
						var invoiceId = event.currentTarget.getAttribute('data-id');
						forApprovalAndReload(invoiceId, dataTable);
					}
				).setHeader('<em>Recommend for Approval</em>');
			})
			// .on('click', '.action-pending', function (event) {
			// 	var cols = $(event.currentTarget).closest('tr.invoice').find('td');
			// 	var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			// 	alertify.confirm(
			// 		'Are you sure you want to return to PENDING the Billing No. ' + docNo + '?',
			// 		function () {
			// 			var invoiceId = event.currentTarget.getAttribute('data-id');
			// 			pendingAndReload(invoiceId, dataTable);
			// 		}
			// 	).setHeader('<em>Return to Pending</em>');
			// })
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.invoice').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to cancel the Billing No. ' + docNo + '?<br><br>' +
					'<label for="reason">Reason for cancelation:</label><br>' +
					'<input type="text" id="deletion-reason" name="reason" style="width: 100%;">' +
					'<div id="reason-error" style="color: red; display: none;">Please provide a reason.</div>',
					function () {
						var reason = document.getElementById('deletion-reason').value;
						if (!reason) {
							document.getElementById('reason-error').style.display = 'block';
							return false;
						}
			
						var invoiceId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(invoiceId, reason, dataTable);
					}
				).setHeader('<em>Delete Invoice</em>');
			})
			.on('click', '.action-bad-debt', function (event) {
				var cols = $(event.currentTarget).closest('tr.invoice').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you mark Billing No.' + docNo + 'as Bad Debt?<br><br>',
					function () {
						var invoiceId = event.currentTarget.getAttribute('data-id');
						setAndReload(invoiceId, dataTable);
					}
				).setHeader('<em>Mark as Bad Debt</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/invoices?status=' + event.currentTarget.getAttribute('data-status');
			dataTable.ajax.url(url).load();
		});
	});
}(jQuery);
