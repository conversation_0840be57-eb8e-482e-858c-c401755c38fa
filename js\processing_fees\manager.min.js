!function(e){"use strict";function t(t){var r=e(t),a=(r.find("thead th"),e(".tab-link.active").attr("data-status")),n=r.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package")+"?status="+a});return n}function r(t,r){e.when(a(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t){return e.ajax({url:BASE_URI+"processing_fees/receive_processing_fee/"+t,type:"POST",dataType:"json"})}function n(t,r){e.when(s(t)).then(function(){r.ajax.reload(null,!1)},function(e){r.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function s(t){return e.ajax({url:BASE_URI+"processing_fees/delete/"+t,type:"POST",dataType:"json"})}e(function(){var a=t(".tableA");e(".tableA").on("click",".action-receive",function(t){var n=e(t.currentTarget).closest("tr.processing_fee").find("td"),s=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the processing_fee "+s+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,a)}).setHeader("<em>receive processing_fee</em>")}).on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.processing_fee").find("td"),s=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete processing fee "+s+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,a)}).setHeader("<em>Delete Processing Fee</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var r=BASE_URI+"fetch/processing_fees?status="+t.currentTarget.getAttribute("data-status"),n=BASE_URI+"processing_fees/manager?status="+t.currentTarget.getAttribute("data-status");console.log(r),window.history.pushState(null,null,n),a.ajax.url(r).load()})})}(jQuery);