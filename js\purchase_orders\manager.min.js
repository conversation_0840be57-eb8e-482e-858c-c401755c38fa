!function(e){"use strict";function r(r){var t=e(r),a=(t.find("thead th"),e(".tab-link.active").attr("data-status")),n=t.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+t.attr("data-package")+"?status="+a});return n}function t(r,t){e.when(a(r)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var r=JSON.parse(e.responseText);alertify.error(r.message)})}function a(r){return e.ajax({url:BASE_URI+"purchase_orders/receive_purchase_order/"+r,type:"POST",dataType:"json"})}function n(r,t){e.when(o(r)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var r=JSON.parse(e.responseText);alertify.error(r.message)})}function o(r){return e.ajax({url:BASE_URI+"purchase_orders/delete/"+r,type:"POST",dataType:"json"})}function u(r,t){e.when(i(r)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var r=JSON.parse(e.responseText);alertify.error(r.message)})}function i(r){return e.ajax({url:BASE_URI+"purchase_orders/return_to_pending/"+r,type:"POST",dataType:"json"})}function c(r,t){e.when(s(r)).then(function(){t.ajax.reload(null,!1)},function(e){t.ajax.reload(null,!1);var r=JSON.parse(e.responseText);alertify.error(r.message)})}function s(r){return e.ajax({url:BASE_URI+"purchase_orders/mark_urgent/"+r,type:"POST",dataType:"json"})}e(function(){var a=r(".tableA");e(".tableA").on("click",".action-receive",function(r){var n=e(r.currentTarget).closest("tr").find("td"),o=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the purchase_order "+o+"?",function(){var e=r.currentTarget.getAttribute("data-id");t(e,a)}).setHeader("<em>receive purchase_order</em>")}).on("click",".action-delete",function(r){var t=e(r.currentTarget).closest("tr").find("td"),o=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to delete purchase order "+o+"?",function(){var e=r.currentTarget.getAttribute("data-id");n(e,a)}).setHeader("<em>Delete Purchase Order</em>")}).on("click",".action-pending",function(r){var t=e(r.currentTarget).closest("tr").find("td"),n=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to return purchase order "+n+" to pending?",function(){var e=r.currentTarget.getAttribute("data-id");u(e,a)}).setHeader("<em>Return to Pending</em>")}).on("click",".action-urgent",function(r){var t=e(r.currentTarget).closest("tr").find("td"),n=void 0!==t[0]?t[0].innerHTML:"";alertify.confirm("Are you sure you want to mark purchase order "+n+" as urgent?",function(){var e=r.currentTarget.getAttribute("data-id");c(e,a)}).setHeader("<em>Mark as Urgent</em>")}),e(".tab-link").click(function(r){e(".tab-link.active").removeClass("active"),r.currentTarget.classList.add("active");var t=r.currentTarget.getAttribute("data-status"),a=BASE_URI+"purchase_orders/manager?status="+encodeURI(t);window.location.href=a})})}(jQuery);