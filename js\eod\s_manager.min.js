!function(t){"use strict";function e(e){var a=t(e),n=a.find("thead th"),r=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(e,a){return t.ajax({url:BASE_URI+"eod/get_attachments/"+e+"/"+a,type:"GET",dataType:"json"})}function n(e,a){return t.ajax({url:BASE_URI+"eod/delete_attachment/"+e+"/"+a,type:"POST",dataType:"json"})}function r(e,a,r,o){return new Promise((c,i)=>{function l(){let s="<div>";if(a&&a.length>0){s+='<div class="old-attachments">',s+="<h5>Existing Attachments:</h5>",s+="<ul>";let t=0;for(let n of a)t++,s+=`\n\t\t\t\t\t\t\t<li style="display: flex; align-items: center;">\n\t\t\t\t\t\t\t\t• <a href="${BASE_URI}assets/eod/eod_s/${e}/${n.file_name}" target="_blank" style="flex-grow: 1;">Attachment ${t}</a>`,"1"===o&&(s+=`\n\t\t\t\t\t\t\t\t<button type="button" class="delete-attachment" data-id="${n.id}" style="background: none; border: none; color: red; cursor: pointer;">\n\t\t\t\t\t\t\t\t\t<i class="fa fa-trash"></i>\n\t\t\t\t\t\t\t\t</button>`),s+="</li>";s+="</ul>",s+="</div>"}a&&0!==a.length||(s+='<div class="new-attachments">',s+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*">',s+="</div>"),s+="</div>",alertify.confirm(s,function(){let t=document.getElementById("attachments").files;c({files:t})},function(){i("Upload Canceled")}).setting({reverseButtons:!0,closable:!1,title:"Add Attachment"}).show(),t(document).off("click",".delete-attachment"),t(document).on("click",".delete-attachment",function(t){let e=t.currentTarget.getAttribute("data-id");n(e,"eod_s").done(function(){a=a.filter(t=>t.id!=e),r.ajax.reload(null,!1),l()}).fail(function(t){let e=JSON.parse(t.responseText);alertify.error(e.message)})})}l()})}function o(e,a){var n=new FormData;n.append("table","eod_s"),n.append("id",e);for(var r=0;r<a.length;r++)n.append("attachments[]",a[r]);return t.ajax({url:BASE_URI+"eod/upload_attachment/",type:"POST",data:n,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Files Uploaded."}))}t(function(){var n=e(".tableA"),c=[];t(".tableA").on("click",".action-add",function(t){var e=t.currentTarget.getAttribute("data-id"),i=t.currentTarget.getAttribute("data-role-id");c=[],a(e,"eod_s").then(function(t){return t.data.forEach(function(t){t.text=t.file_name,c.push(t)}),r(e,c,n,i)}).then(({files:t})=>o(e,t)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})})}(jQuery);