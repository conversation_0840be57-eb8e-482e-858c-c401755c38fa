<!DOCTYPE HTML>
<html>
<head>
<script type="text/javascript" src="https://canvasjs.com/assets/script/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../../canvasjs.stock.min.js"></script>
<script type="text/javascript">
window.onload = function () {
  var dataPoints1 = [], dataPoints2 = [];
  var stockChart = new CanvasJS.StockChart("stockChartContainer",{
    title:{
      text:"Annual Temperature"
    },
    subtitles: [{
      text: "India 1901-2017"
    }],
    charts: [{
      axisY: {
        suffix: "°C"
      },
      data: [{
        type: "rangeSplineArea",
        xValueFormatString: "YYYY",
        toolTipContent: "<span style=\"color:#4F81BC\">Year: {x}</span><br/>Min: {y[0]}°C,<br/> Max: {y[1]}°C",
        dataPoints : dataPoints1
      }]
    }],
    navigator: {
      data: [{
        type: "line",
        dataPoints: dataPoints2
      }],
      slider: {
        minimum: new Date(1950, 00),
        maximum: new Date(1980, 00)
      }
    },
    rangeSelector: {
      buttons: [{
        label: "10Y",
        range: 10,
        rangeType: "year"
      },{
        label: "20Y",
        range: 20,
        rangeType: "year"
      },{
        label: "50Y",
        range: 50,
        rangeType: "year"
      },{
        label: "All",
        range: null,
        rangeType: "all"
      }]
    }
  });
  $.getJSON("https://canvasjs.com/data/gallery/stock-chart/weather-india.json", function(data) {
    for(var i = 0; i < data.length; i++){
      dataPoints1.push({x: new Date(data[i].year, 00, 00), y: [Number(data[i].min), Number(data[i].max)]});
      dataPoints2.push({x: new Date(data[i].year, 00, 00), y: Number(data[i].avg)});
    }
    stockChart.render();
  });
}
</script>
</head>
<body>
<div id="stockChartContainer" style="height: 400px; max-width: 920px; margin: 0px auto;"></div>
</body>
</html>