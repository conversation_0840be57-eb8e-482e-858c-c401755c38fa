!function(e){"use strict";function t(){return e.ajax({url:BASE_URI+"eod/generate_eodp",type:"POST",dataType:"json"})}e(function(){e(".action-print").click(function(e){let n=e.currentTarget.getAttribute("data-id");n=parseInt(n)+1,alertify.confirm("Are you sure you want to generate EOD-P No. "+n+"?",function(){window.print(),t().done(function(){window.location.reload()})}).setHeader("<em>Generate EOD-P</em>")})})}(jQuery);