@font-face {
	font-family: "Avenir Roman";
	src: url(fonts/AvenirRoman.otf);
}

@font-face {
	font-family: "Fira Code";
	src: url(fonts/FiraCode-Regular.ttf);
}

html {
	font-size: 13px;
}

body {
	background-color: #fff;
	font-family: "Open Sans", "Helvetica Neue", Arial, Helvetica, sans-serif;
}

p {
	margin: 5px 0;
}

table td,
table th {
	vertical-align: top;
}

.action-nav.active,
.bold-text,
.regular-text {
	font-weight: 700;
}

.no-left-padding,
.no-side-padding {
	padding-left: 0 !important;
}

.no-right-padding,
.no-side-padding {
	padding-right: 0 !important;
}

.dp-preview,
.dp-preview-lg,
.img-dp {
	object-fit: cover;
	object-position: center;
}

.action-nav,
.app-side-menu li a,
.box .box-header .box-icon i,
.crud-panel .nav-tabs li a {
	text-decoration: none;
}

.valign-top,
table,
table td,
table th {
	vertical-align: top;
}

.action-nav,
body {
	color: #3a3a3a;
}

#mainNavbar .navbar-toggler {
	border: 2px solid #fff;
	color: #fff;
}

#mainNavbar .navbar-toggler-icon {
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

#mainNavbar {
	background-color: #007bff;
}

#collapseNavbar li a {
	color: #fff;
}

#collapseNavbar .dropdown.open a.dropdown-toggle {
	color: #fff;
	background-color: #007bff;
}

#collapseNavbar .navbar-nav > .dropdown-menu {
	margin-top: 2px;
	background-color: #fff;
	border-radius: 4px;
}

#collapseNavbar .dropdown-menu > a {
	color: #000;
}

#collapseNavbar .dropdown-menu > a:hover {
	background-color: #fafafa;
}

.img-logo,
.no-padding {
	padding: 0;
}

.img-dp {
	height: 50px;
	width: 50px;
}

.sect {
	padding-top: 1%;
	margin-top: 64px;
}

.sect-jr {
	padding-top: 1%;
	margin-top: 32px;
}

.sect-iii {
	padding-top: 1%;
	margin-top: 16px;
}

.no-end-padding,
.no-top-padding {
	padding-top: 0;
}

.no-bottom-padding,
.no-end-padding {
	padding-bottom: 0;
}

.no-margin {
	margin: 0;
}

.no-end-margin {
	margin-top: 0;
	margin-bottom: 0;
}

.no-side-margin {
	margin-left: 0;
	margin-right: 0;
}

.img-logo {
	width: 50px;
	height: 50px;
}

.valign-bottom {
	vertical-align: bottom;
}

.action-nav {
	text-align: left;
}

.action-nav.active,
.action-nav:active,
.action-nav:focus,
.action-nav:hover {
	color: #007bff;
	text-decoration: none;
	outline: 0;
}

.action-nav .fa {
	width: 25px;
}

.full-width {
	width: 100%;
}

.full-width:after,
.full-width:before {
	display: table;
	content: " ";
}

.full-width:after {
	clear: both;
}

.modal-footer .btn + .btn {
	margin-bottom: 4px;
}

.btn-group-vertical .btn,
.nav-tabs .btn {
	margin-bottom: 0;
}

.btn.float-right {
	margin-left: 3px;
}

.btn.float-left {
	margin-right: 3px;
}

.dt-buttons {
	margin-right: 3px;
}

.dt-button-background {
	background: 0 0;
}

td.dt-control,
th.dt-control {
	width: 7%;
	text-align: center;
}

td.dt-action,
th.dt-action {
	width: 13%;
	text-align: center;
}

form label:not(.form-check-label) {
	color: #323232;
	font-weight: 700;
}

form label.form-check-label {
	color: #323232;
	font-weight: 400;
}

form textarea {
	resize: none;
}

form input[type="radio"].form-inline {
	margin-left: 8px;
}

form .ui-autocomplete {
	max-height: 100px;
	overflow-y: auto;
	overflow-x: hidden;
}

.crud-box {
	overflow: auto;
}

* html form .ui-autocomplete {
	height: 100px;
}

:not(.form-inline) > .dropsearch + .select2-container {
	width: 100%;
}

.form-inline > .dropsearch + .select2-container {
	margin: -7px 0 0 0;
}

.select2-selection,
.select2-selection__rendered .select2-selection__arrow {
	min-height: 30.75px;
	padding-top: 1px;
}

select[readonly].select2-hidden-accessible + .select2-container {
	pointer-events: none;
	touch-action: none;
}

select[readonly].select2-hidden-accessible
	+ .select2-container
	.select2-selection {
	background: #eee;
	box-shadow: none;
}

select[readonly].select2-hidden-accessible
	+ .select2-container
	.select2-selection__arrow,
select[readonly].select2-hidden-accessible
	+ .select2-container
	.select2-selection__clear {
	display: none;
}

.red-text {
	color: #dc3545;
}

.blue-text {
	color: #007bff;
}

.green-text {
	color: #28a745;
}

.yellow-text {
	color: #ffc107;
}

.violet-text {
	color: #6f42c1;
}

.orange-text {
	color: #fd7e14;
}

.grey-text {
	color: #6c757d;
}

.italic-text {
	font-style: italic;
}

.regular-text {
	font-style: normal;
}

.bg-default {
	background: #fafafa;
}

.bg-red {
	background: rgba(244, 67, 54, 0.22);
}

.bg-blue {
	background: rgba(51, 122, 183, 0.22);
}

.bg-green {
	background: rgba(76, 175, 80, 0.22);
}

.bg-yellow {
	background: rgba(255, 235, 59, 0.22);
}

.bg-violet {
	background: rgba(148, 0, 211, 0.22);
}

.bg-orange {
	background: rgba(240, 173, 78, 0.22);
}

.bg-grey {
	background: rgba(168, 168, 168, 0.22);
}

.bg-brown {
	background: rgba(197, 164, 136, 0.22);
}

.sign-box {
	width: 100%;
	max-width: 400px;
	margin: 50px auto 0;
	background: #fff;
	border: 1px solid #d8e2e7;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	-ms-border-radius: 5px;
	border-radius: 5px;
	padding: 20px;
	position: relative;
	margin-top: 200px;
}

.sign-box .sign-title {
	font-size: 18px;
	font-weight: 600;
	margin-top: 5px;
	color: #333;
}

.sign-box .checkbox {
	margin-top: 0;
}

.sign-box .memotic {
	display: inline-block;
	line-height: 30px;
}

.item-table {
	border-collapse: collapse;
	width: 100%;
}

.item-table,
.item-table td,
.item-table th {
	vertical-align: top;
	border: 1px solid #555;
	padding: 5px 7px;
	text-align: justify;
}

.item-table input.cuttlefish {
	border: 0;
	background: inherit;
	box-shadow: none;
	text-align: inherit;
	padding: 0;
}

.crud-box {
	width: 100%;
	margin: 0 auto;
	background: #fff;
	border: 1px solid #d8e2e7;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	-ms-border-radius: 5px;
	border-radius: 5px;
	padding: 20px;
	position: relative;
}

.crud-box .crud-title {
	font-size: 16px;
	font-weight: 600;
	margin-top: 5px;
	margin-bottom: 20px;
	color: #333;
}

.crud-box .crud-subtitle {
	font-weight: 600;
	width: 100%;
	text-align: center;
	border-bottom: 1px solid #eee;
	line-height: 0.1em;
	margin-top: 30px;
	margin-bottom: 20px;
}

.crud-box .crud-subtitle span {
	background: #fff;
	padding: 0 10px;
}

.crud-box table td {
	word-wrap: break-word;
}

.crud-box hr,
.crud-panel {
	margin-top: 10px;
	margin-bottom: 10px;
}

.crud-box .btn,
.crud-panel .btn,
.report-form .btn {
	padding: 5px 10px;
	line-height: 1.5;
	border-radius: 3px;
}

textarea.autosize {
	width: 100%;
	background-image: none;
	border: 1px solid #ccc;
	border-radius: 4px;
	padding: 6px 13px;
	line-height: 1.42857143;
	color: #555;
}

.crud-box .checkbox {
	margin-top: 0;
}

.crud-box .memotic {
	display: inline-block;
	line-height: 30px;
	margin-left: 13px;
}

.crud-box .well {
	background-color: inherit;
	padding-left: 0;
	padding-right: 0;
	border: none;
	box-shadow: none;
}

.crud-box .well .slider {
	width: 100%;
}

.crud-box .well .slider .slider-selection {
	background: #5bc0de;
}

.crud-panel .crud-title {
	font-size: 16px;
	font-weight: 600;
	margin-top: 5px;
	margin-bottom: 20px;
	color: #333;
}

.crud-panel .panel-body {
	background: #fff;
	border-right: 1px #ddd solid;
	border-bottom: 1px #ddd solid;
	border-left: 1px #ddd solid;
}

.app-side-menu {
	overflow: auto;
	font-weight: 200;
	background-color: #333;
	position: fixed;
	top: 0;
	width: 17%;
	height: 100%;
	color: #e1ffff;
	z-index: 2;
}

.app-side-menu .brand {
	background-color: #fff;
	line-height: 50px;
	display: block;
	text-align: center;
	padding: 2px 15px;
	color: #14171a;
	background: #f5f8fa;
}

.app-side-menu .toggle-btn {
	display: none;
}

.app-side-menu ul {
	list-style: none;
	padding: 0;
	margin: 0;
	line-height: 35px;
	cursor: pointer;
}

.app-side-menu li {
	padding: 5px 10px 5px 0;
}

.app-side-menu li a {
	display: block;
	color: #e1ffff;
}

.app-side-menu li a i {
	padding-left: 10px;
	width: 20px;
	padding-right: 20px;
}

.app-side-menu .sub-menu {
	padding-left: 30px;
	width: 20px;
}

.app-side-menu .menu:after {
	font-family: FontAwesome;
	content: "\f0d8";
	float: right;
}

.app-side-menu .menu.collapsed:after {
	content: "\f0d7";
}

.app-side-menu li:hover {
	border-left: 3px solid #fff;
	background-color: #555;
}

@media (max-width: 767px) {
	.app-side-menu {
		position: relative;
		width: 100%;
		margin-bottom: 10px;
	}

	.app-side-menu .toggle-btn {
		display: block;
		cursor: pointer;
		position: absolute;
		right: 10px;
		top: 10px;
		z-index: 10;
		padding: 3px;
		background-color: #fff;
		color: #000;
		width: 40px;
		text-align: center;
	}

	.brand {
		text-align: left;
		font-size: 22px;
		padding-left: 20px;
		line-height: 50px;
	}

	.app-content {
		margin-left: 0;
	}

	.app-content .sect {
		margin-top: 25px;
	}
}

@media (min-width: 767px) {
	.app-side-menu .menu-list .menu-content {
		display: block;
	}
}

.app-content {
	padding-bottom: 50px;
}

.app-content,
.app-footer {
	margin-left: 17%;
}

.app-footer {
	position: fixed;
	right: 1%;
	bottom: 0;
}

.box {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 1px 0 1px #e4e6eb;
	-moz-box-shadow: 0 1px 0 1px #e4e6eb;
	box-shadow: 0 1px 0 1px #e4e6eb;
	margin: 0 0 30px;
	display: block;
}

.box .box-header {
	background: #3498db;
	color: #fff;
	font-size: 16px;
	overflow: hidden;
	-webkit-border-radius: 2px 2px 0 0;
	-moz-border-radius: 2px 2px 0 0;
	border-radius: 2px 2px 0 0;
	display: block;
}

.box .box-header > h2 {
	float: left;
	padding: 0;
	margin: 0;
	font-size: 16px;
	line-height: 16px;
	font-weight: 300;
}

.box .box-header .box-icon {
	background: #d0104d;
	float: right;
}

.box .box-content {
	padding: 10px;
	background: #fff;
	-webkit-border-radius: 0 0 2px 2px;
	-moz-border-radius: 0 0 2px 2px;
	border-radius: 0 0 2px 2px;
}

.box .box-header .box-icon i,
.box .box-header h2 i {
	color: #fff;
	padding: 10px 0;
	width: 36px;
	display: inline-block;
	text-align: center;
}

.box .box-header .box-icon i {
	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-ms-transition: all 0.1s ease-in-out;
	-o-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
	opacity: 0.8;
	filter: "alpha(opacity=80)";
	-ms-filter: "alpha(opacity=80)";
	border-left: 1px solid #36a9e1;
}

.box .box-header h2 i {
	background: #8d8d94;
	margin-right: 10px;
}

.error {
	color: #dc3545;
	background-color: #fcc;
}

.success {
	color: #28a745;
	background-color: #cfc;
}

.error,
.success {
	text-align: center;
	overflow: hidden;
	-moz-animation: houdini 0s ease-in 60s forwards;
	-webkit-animation: houdini 0s ease-in 60s forwards;
	-o-animation: houdini 0s ease-in 60s forwards;
	animation: houdini 0s ease-in 60s forwards;
	-webkit-animation-fill-mode: forwards;
	animation-fill-mode: forwards;
}

.flash-message {
	text-align: left;
	overflow: hidden;
	-moz-animation: houdini 0s ease-in 60s forwards;
	-webkit-animation: houdini 0s ease-in 60s forwards;
	-o-animation: houdini 0s ease-in 60s forwards;
	animation: houdini 0s ease-in 60s forwards;
	-webkit-animation-fill-mode: forwards;
	animation-fill-mode: forwards;
}

@keyframes houdini {
	to {
		width: 0;
		height: 0;
		overflow: hidden;
	}
}

@-webkit-keyframes houdini {
	to {
		width: 0;
		height: 0;
		visibility: hidden;
	}
}

.topbtn {
	margin-top: 40px;
	margin-bottom: 40px;
}

.topbtn .title {
	font-size: 30px;
}

.topbtn .sub-title {
	font-size: 1em;
}

.receipt {
	background: #fff;
	border: 1px solid #d8e2e7;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	-ms-border-radius: 5px;
	border-radius: 5px;
	padding: 20px;
	position: relative;
	min-height: 500px;
	margin: 0 auto 20px 0;
}

.receipt-header {
	text-align: center;
	margin-bottom: 25px;
}

.receipt-header:after {
	display: table;
	content: " ";
	clear: both;
}

.receipt-header h4,
.receipt-header p {
	margin-bottom: 0;
	display: block;
}

.receipt-body {
	border-collapse: collapse;
	width: 100%;
}

.receipt-body thead th,
.receipt-body thead tr {
	border-top: 1px solid #000;
	border-bottom: 1px solid #000;
	background: #e3e3e3;
	padding: 1px 7px;
}

.receipt-body tbody td,
.receipt-body tbody tr {
	padding: 3px 7px;
}

.receipt-body tbody tr:last-child,
.receipt-body tbody tr:last-child td {
	border-bottom: 1px solid #000;
}

.receipt-body tfoot td,
.receipt-body tfoot tr {
	border: none;
}

.receipt .r-label,
.receipt .r-title,
.receipt .r-value {
	padding-right: 7px;
}

.receipt-body td,
.receipt-body th {
	vertical-align: top;
}

.receipt .r-title {
	color: #545454;
	font-weight: 700;
}

.receipt .r-subtitle {
	color: #1b5e20;
	font-weight: 700;
	padding-right: 7px;
}

.receipt .r-label {
	color: #545454;
	font-weight: 700;
}

.receipt .clear,
.receipt .clear-jr {
	clear: both;
	display: block;
	padding: 0;
}

.receipt .clear {
	margin-top: 5px;
	margin-bottom: 15px;
}

.receipt .clear-jr {
	margin-top: 2px;
	margin-bottom: 2px;
}

.receipt hr.signature-line {
	border-top: 1px solid #333;
}

.dataTable {
	width: 100%;
}

.no-padding-right {
	padding-right: 0;
}

.no-padding-left {
	padding-left: 0;
}

@media print and (orientation: portrait) {
	body * {
		visibility: hidden;
	}

	.dataTable,
	.dataTable *,
	.receipt,
	.receipt *,
	body.dt-print-view * {
		visibility: visible;
	}

	.receipt {
		font-size: 12pt;
		padding: 0;
		margin: 0 0 0 -17%;
		width: 113%;
		position: absolute;
		left: 0;
		top: 0;
		border: none;
	}
}

@media print and (orientation: landscape) {
	body * {
		visibility: hidden;
	}

	.dataTable,
	.dataTable *,
	.receipt,
	.receipt *,
	body.dt-print-view * {
		visibility: visible;
	}

	.receipt {
		font-size: 12pt;
		padding: 0;
		margin: 0 0 0 -17%;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border: none;
	}
}

.smaller-text {
	font-size: 90%;
}

.larger-text {
	font-size: 110%;
}

.dp-preview {
	height: 1.5in;
	width: 1.5in;
}

.dp-preview-lg {
	height: 2in;
	width: 2in;
}

.dashed-box {
	width: 100%;
	background: #fff;
	border: 1px dashed #d8e2e7;
	-webkit-border-radius: 0;
	-moz-border-radius: 50x;
	-ms-border-radius: 0;
	border-radius: 0;
	padding: 20px;
	position: relative;
	overflow: hidden;
}

.dashed-box span.label {
	margin-bottom: 7px;
}

.inline {
	display: inline-block;
}

.ui-front {
	z-index: 2000;
}

.text-left {
	text-align: left;
}

.badge {
	font-size: 90%;
}

.link-unstyled,
.link-unstyled:active,
.link-unstyled:active:hover,
.link-unstyled:focus,
.link-unstyled:hover,
.link-unstyled:visited {
	text-align: left;
	font-style: inherit;
	color: inherit;
	background-color: transparent;
	font-size: inherit;
	text-decoration: none;
	font-variant: inherit;
	font-weight: inherit;
	line-height: inherit;
	font-family: inherit;
	outline: inherit;
	box-shadow: inherit;
	vertical-align: inherit;
}

.form-control {
	margin-bottom: 7px;
}

.img-preview {
	object-fit: cover;
	object-position: center;
	height: 1.5in;
	width: 1.5in;
}

.underlined {
	min-height: 29px;
	border-bottom: 1px solid #d8e2e7;
}

.graham {
	height: 60px;
	width: 100%;
}

input[type="number"] {
	text-align: right;
}

.tab {
	overflow: hidden;
	border: 1px solid #ccc;
	background-color: #f1f1f1;
}

.tab .tab-link {
	background-color: inherit;
	float: left;
	border: none;
	outline: 0;
	cursor: pointer;
	padding: 14px 16px;
	transition: 0.3s;
	font-size: 17px;
	color: #333;
	min-width: 8.6%;
	text-align: center;
}

.tab .tab-link:hover {
	background-color: #333;
	color: #fff;
	text-decoration: none;
}

.tab .tab-link.active {
	background-color: #333;
	color: #fff;
}

.btn {
	margin-bottom: 3px;
}

.select2-container--default .select2-results__option[aria-disabled="true"] {
	display: none;
}

[data-status="urgent"] {
	background-color: #555;
	color: white;
	text-decoration: none;
	padding: 15px 26px;
	position: relative;
	display: inline-block;
	border-radius: 2px;
}

[data-status="urgent"]:hover {
	background: red;
}

[data-status="urgent"] .badge2 {
	position: absolute;
	left: 7.4rem;
	padding: 6px 11px;
	border-radius: 50%;
	background: red;
	color: white;
	font-size: 0.6em;
}


.ajs-input{
  width: 100%;
  padding: 12px 20px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.app-side-menu .expenses-menu.collapsed:after {
    content: "\f0d7";
}

.app-side-menu .expenses-menu:after {
    font-family: FontAwesome;
    content: "\f0d8";
    /* float: right; */
    padding-left: 156px;
}

.app-side-menu .sales-menu.collapsed:after {
    content: "\f0d7";
}

.app-side-menu .sales-menu:after {
    font-family: FontAwesome;
    content: "\f0d8";
    /* float: right; */
    padding-left: 180px;
}

.whole-menu li {
	padding-left: 10px;
}


@media print {
	.no-print, .no-print * {
		display: none !important;
	}
}