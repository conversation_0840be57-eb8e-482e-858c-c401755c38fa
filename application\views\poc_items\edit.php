<div class="sect">
	<div class="col-md-4 offset-md-3 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="crud-title"><?=$title;?></div>

			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-12 form-group">
						<label for="item_name">Item Name</label>

						<input type="text" name="item_name" value="<?=set_value('item_name', $poc_item->item_name);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('item_name');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="unit_price">Unit Price</label>

						<input type="number" name="unit_price" value="<?=set_value('unit_price', $poc_item->unit_price);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('unit_price');?>
						</div>
					</div>

					
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('poc_items/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>
