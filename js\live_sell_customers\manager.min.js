!function(e){"use strict";function t(t){var a=e(t),r=a.find("thead th"),n=a.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:r.length-1},{searchable:!1,targets:[5]}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(t,a){e.when(r(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function r(t){return e.ajax({url:BASE_URI+"live_sell_customers/delete/"+t,type:"POST",dataType:"json"})}e(function(){var r=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var n=e(t.currentTarget).closest("tr.live_sell_customer").find("td"),l=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to delete "+l+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,r)}).setHeader("<em>Delete Live Sell Customer</em>")})})}(jQuery);