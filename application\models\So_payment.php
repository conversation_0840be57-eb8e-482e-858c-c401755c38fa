<?php
class So_payment extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'so_payment';
        parent::__construct();
    }

    /**
     * Get Invoices Total Sales
     */
    public function get_total_monthly_sales()
    {
        $firstDayOfMonth = date('Y-m-01');
        $lastDayOfMonth = date('Y-m-t');

        $sql = <<<EOT
SELECT SUM(so_payment.total_paid) AS total_monthly_sales
FROM so_payment
WHERE is_deleted = 0
    AND so_payment.so_payment_date BETWEEN '$firstDayOfMonth' AND '$lastDayOfMonth';
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_monthly_sales;
        }else{
            return 0;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
