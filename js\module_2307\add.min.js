!function(t){"use strict";function e(){var e=t(a()),n=t(".item-table"),i=n.find("._2307_detail");0===i.length&&n.find(".no-item").remove(),n.find("tbody").append(e)}function a(){var t=n();return'<tr class="_2307_detail"><td><input type="hidden" name="_2307_detail_'+t+'" value="'+t+'"><select name="income_payment_subject_'+t+'" class="form-control" data-placeholder=""><option value=""></option><option value="Rentals">Rentals</option><option value="Goods">Goods</option><option value="Services">Services</option></select></td><td><input type="text" name="atc_'+t+'" class="form-control" value="" readonly></td><td><input type="number" name="total_amount_of_income_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="tax_withheld_'+t+'" class="form-control" value="" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function n(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(){return'<tr class="no-item"><td class="text-center" colspan="5">No items added yet.</td></tr>'}function o(){var e=0;t('[name^="tax_withheld"]').each(function(){var a=parseFloat(t(this).val());isNaN(a)||(e+=a)}),t("#overall-total").text(e.toFixed(2))}function l(e){t(e.currentTarget).closest("._2307_detail").remove();var a=t(e.delegateTarget),n=a.find("._2307_detail");if(n.length<1){var l=t(i());a.find("tbody").html(l)}o()}function r(e){return t.ajax({url:BASE_URI+"suppliers/info/"+e,type:"GET",dataType:"json"})}function c(t){var e=parseFloat(t.find('[name^="total_amount_of_income"]').val()),a=t.find('[name^="tax_withheld"]'),n=parseFloat(a.attr("data-percentage"))||0;if(!isNaN(e)&&n>0){var i=e/1.12*n;a.val(i.toFixed(2))}else a.val("");o()}function d(e,a){return t.ajax({url:BASE_URI+"module_2307/delete_attachment/"+e,type:"POST",data:{},dataType:"json"})}function s(){var e=t('select[name="supplier"]').val(),a=t("#btnSubmit");""!==e?a.prop("disabled",!1):a.prop("disabled",!0)}t(function(){function a(){t("._2307_detail").each(function(){var e=t(this),a=e.find('select[name^="income_payment_subject"]').val(),n=e.find('[name^="atc"]'),i=e.find('[name^="tax_withheld"]');"Rentals"===a?(n.val("WC100"),i.attr("data-percentage",.05)):"Goods"===a?(n.val("WC158"),i.attr("data-percentage",.01)):"Services"===a?(n.val("WC160"),i.attr("data-percentage",.02)):(n.val(""),i.attr("data-percentage",0)),c(e)})}t(".item-table").on("click",".action-delete-item",function(t){l(t)}).on("change",'select[name^="income_payment_subject"]',function(e){var a=t(e.currentTarget).closest("._2307_detail"),n=t(this).val(),i=a.find('[name^="atc"]'),o=a.find('[name^="tax_withheld"]');"Rentals"===n?(i.val("WC100"),o.attr("data-percentage",.05)):"Goods"===n?(i.val("WC158"),o.attr("data-percentage",.01)):"Services"===n?(i.val("WC160"),o.attr("data-percentage",.02)):(i.val(""),o.attr("data-percentage",0)),c(a)}).on("input",'[name^="total_amount_of_income_"]',function(e){var a=t(e.currentTarget).closest("._2307_detail");c(a)}),s(),t('select[name="supplier"]').on("change",function(e){var a=t(this).val();t('input[name="tin"]').val(""),t('input[name="address"]').val(""),t('input[name="zip_code"]').val(""),t.when(r(a)).then(function(e){t('input[name="tin"]').val(e.supplier.tin),t('input[name="address"]').val(e.supplier.address),t('input[name="zip_code"]').val(e.supplier.zip_code)}),s()}),t(".action-add-item").click(function(){e()}),t(".attachment-table").on("click",".action-delete-attachment",function(e){var a=t(this),n=a.closest("tr.attachment"),i=a.data("id"),o=a.data("filename");alertify.confirm("Are you sure you want to delete "+o+"? <br><br> This action cannot be undone.",function(){t.when(d(i,o)).then(function(t){"Module 2307 Attachment is successfully removed."===t.message?(n.remove(),alertify.success("Attachment deleted successfully.")):alertify.error("Failed to delete attachment: "+t.message)}).fail(function(){alertify.error("An error occurred while deleting the attachment.")})},function(){alertify.message("Deletion canceled.")}).setHeader("<em>Delete Attachment</em>")}),a(),o()})}(jQuery);