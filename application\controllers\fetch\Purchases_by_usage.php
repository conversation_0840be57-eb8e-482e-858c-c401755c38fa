<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchases_by_usage extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch POs
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase';
        $primary_key = 'purchase.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_status(),
            $this->_get_doc_no(),
            $this->_get_supplier(),
            $this->_get_purchase_date(),
            $this->_get_plate_no(),
            $this->_get_requisitioner(),
            $this->_get_total(),
            $this->_get_items()
        ];

        $joins = <<<EOT
purchase
LEFT JOIN (
    SELECT purchase_id, SUM(amount) AS total
    FROM purchase_item
    WHERE status = "approved"
        AND is_deleted = 0
    GROUP BY purchase_id
) purchase_item ON purchase_item.purchase_id = purchase.id
LEFT JOIN supplier ON supplier.id = purchase.supplier_id
LEFT JOIN user AS author ON author.id = purchase.added_by
LEFT JOIN user AS recommender ON recommender.id = purchase.recommended_by
LEFT JOIN user AS approver ON approver.id = purchase.approved_by
LEFT JOIN user AS printer ON printer.id = purchase.printed_by
EOT;

        $usage = addslashes($this->input->get('usage', true));
        $where = <<<EOT
purchase.main_usage = "{$usage}"
AND purchase.status <> "pending"
AND purchase.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'purchase.status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get PO document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'purchase.id',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no'
        ];
    }

    /**
     * Get PO supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 1,
            'field' => 'supplier'
        ];
    }
    /**
     * Get PO Date
     */
    protected function _get_purchase_date()
    {
        return [
            'db' => 'DATE_FORMAT(purchase.purchase_date, "%b %d, %Y")',
            'as' => 'purchase_date',
            'dt' => 2,
            'field' => 'purchase_date'
        ];
    }

    /**
     * Get Plate No
     */
    protected function _get_plate_no()
    {
        return [
            'db' => 'purchase.plate_no',
            'dt' => 3,
            'field' => 'plate_no'
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_total()
    {
        $CI =& get_instance();

        return [
            'db' => 'IF(purchase.status = "pending", purchase.total, IFNULL(purchase_item.total, 0))',
            'as' => 'total',
            'dt' => 4,
            'field' => 'total',
            'formatter' => function ($d, $row) use ($CI) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get Requisitioner
     */
    protected function _get_requisitioner()
    {
        return [
            'db' => 'purchase.requisitioner',
            'as' => 'requisitioner',
            'dt' => 5,
            'field' => 'requisitioner'
        ];
    }

    /**
     * Get Items
     */
    protected function _get_items()
    {
        return [
            'db' => '(
                SELECT GROUP_CONCAT(CONCAT(purchase_item.qty, " ", purchase_item.unit, " ", item.name) SEPARATOR "🔥")
                FROM purchase_item
                LEFT JOIN item ON item.id = purchase_item.item_id
                WHERE purchase_item.status = "approved"
                    AND purchase_item.purchase_id = purchase.id
                GROUP BY purchase_item.purchase_id
            )',
            'as' => 'items',
            'dt' => 6,
            'field' => 'items',
            'formatter' => function ($d, $row) {
                $res = '<ul>';

                $items = explode('🔥', $d);
                $items = array_map('trim', $items);
                $items=  array_filter($items);

                foreach ($items as $item) {
                    $res .= '<li>' . $item . '</li>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }
}
