---
layout: 'default'
hljs: 	'light'
---
<section class="blue">
	<div class="content">
		<div class="grid two">
			<div class="column">
				<h1> Tutorials </h1>
				 Learn How to Use and Get The Most out of AlertifyJS.
			</div>
			<div class="right column">
			  <%- @partial('ad') %>
			</div>
		</div>
	</div>
</section>
<section class="lic">
    <div class="content">
        Looking for a commercial license ?  Keep your source code proprietary and <a href="https://www.uplabs.com/posts/alertifyjs" target="_blank"> Buy a Commercial License Today!</a>
    </div>
</section>
<section class="dark">
	<div class="content">
	
	    <!--Webucator Video-->
	    <div class="segment">
		    <h1 id="*********"> <a href="#*********"><i class="fa fa-video-camera"></i> </a> How to Use AlertifyJS <sup>21 Jan. 2015 </sup> </h1>
		    <hr />

            <p> In this video, <PERSON> from <a href="https://www.webucator.com" target="_blank">Webucator</a> walks you through using AlertifyJS. Also he takes a look at the dialogs factory for creating custom dialogs.</p>

            <p> Webucator provides onsite and online training on technologies such as Microsoft ASP.NET, XML, Windows, Java, Adobe Flash, HTML5, <a href="https://www.webucator.com/webdesign/javascript.cfm" target="_blank">JavaScript</a>, Dreamweaver, and much more.</p>

            <div class="video">
                <iframe width="100%" height="750" src="//www.youtube.com/embed/GODhPuM5cEE?origin=http://www.alertifyjs.com" frameborder="0" allowfullscreen></iframe>
            </div>

        </div>
        <!--//Webucator Video-->	
	
	</div>

<br/><br/>
</section>