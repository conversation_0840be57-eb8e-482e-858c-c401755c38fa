<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Sales_order_payments extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('sales_order_payments/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review sales_order_payment Order for Approval
     */
    public function receive($sales_order_payment_id)
    {
        $sales_order_payment = $this->sales_order_payment->get_by_id($sales_order_payment_id) or show_404();

        $this->title = 'Receive sales_order_payment No. ' . $sales_order_payment->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_order_payments/receive'
        ];

        $data = [
            'sales_order_payment'          => $sales_order_payment
        ];
        $this->build_content('default', 'sales_order_payments/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add sales_order_payment
     */
    public function add()
    {
        $config = [
            'upload_path' => FCPATH . 'assets/sales_order_payments',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('sales_order_payments/add'));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($sales_order_payment_id = $this->_attempt_add())) {
            $new_path = FCPATH . 'assets/sales_order_payments/' . $sales_order_payment_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('sales_order_payments/manager'));
        } else {
            $this->title = 'Add Sales Order Payment';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/sales_order_payments/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $data = [
                'form_error'        => $form_error,
                'customers'         => $customers,
            ];

            $this->build_content('default', 'sales_order_payments/add', $data);
        }
    }

    /**
     * Edit sales_order_payment
     */
    public function edit($sales_order_payment_id)
    {
        $sales_order_payment = $this->sales_order_payment->get_by_id($sales_order_payment_id) or show_404();

        $rules = $this->config->item('sales_order_payments/edit');

        $rules = array_merge($this->config->item('sales_order_payments/edit'));

        if ($this->_validate_form($rules) && $this->_attempt_edit($sales_order_payment)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('sales_order_payments/preview/' . $sales_order_payment->id));
            } else {
                redirect(site_url('sales_order_payments/manager'));
            }
        } else {
            $this->title = 'Edit sales order payment';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/sales_order_payments/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'sales_order_payment' => $sales_order_payment,
            ];

            $this->build_content('default', 'sales_order_payments/edit', $data);
        }
    }

    /**
     * Approve sales_order_payment
     */
    public function receive_sales_order_payment($sales_order_payment_id)
    {
        $where = [
            'id' => $sales_order_payment_id,
            'is_deleted' => 0
        ];
        if (!$sales_order_payment = $this->sales_order_payment->select('', $where, 1)) {
            $response = [
                'message' => 'sales_order_payment is not found.'
            ];
        } elseif (!$this->_attempt_receive($sales_order_payment)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'sales_order_payment is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($sales_order_payment)
    {
        $where = [
            'id' => $sales_order_payment->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->sales_order_payment->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete sales_order_payment
     */
    public function delete($sales_order_payment_id)
    {
        $where = [
            'id' => $sales_order_payment_id,
            'is_deleted' => 0
        ];
        if (!$sales_order_payment = $this->sales_order_payment->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Sales Order Payment is not found.'
            ];
        } elseif (!$this->_attempt_delete($sales_order_payment)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Sales Order Payment is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * sales_order_payments
     */
    public function manager()
    {
        $this->title = "Sales Order Payments";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_order_payments/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'sales_order_payments/manager', $data);
    }

    /**
     * sales_order_payment Print Preview
     */
    public function preview($sales_order_payment_id)
    {
        $sales_order_payment = $this->sales_order_payment->get_by_id($sales_order_payment_id) or show_404();

        $this->title = 'Print Preview: sales order payment No. ' . $sales_order_payment->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/sales_order_payments/preview',
        ];

        $where = [
            'id' => $sales_order_payment->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);

        $data = [
            'sales_order_payment' => $sales_order_payment,
            'customer' => $customer,
        ];
        $this->build_content('default', 'sales_order_payments/preview', $data);
    }

    /**
     * Get Info for sales_order_payment
     */
    public function info($sales_order_payment_id = null)
    {
        $data = [
            'sales_order_payment' => $this->sales_order_payment->get_by_id($sales_order_payment_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid sales_order_payments
     */
    public function partially_paid()
    {
        $data = [
            'sales_order_payments' => $this->sales_order_payment->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('sales_order_payment');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        if (!$sales_order_payment_id = $this->_generate_sales_order_payment()) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $sales_order_payment_id;
        }
    }

    /**
     * Generate sales_order_payment
     */
    protected function _generate_sales_order_payment()
    {
        $customer_id = $this->input->post('customer');

        $sales_order_payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('sales_order_payment_date'));
        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }
        $values = [
            'customer_id' => $customer_id,
            'sales_order_payment_date' => $sales_order_payment_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->sales_order_payment->insert($values);
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($sales_order_payment)
    {
        $this->db->trans_begin();

        $where = [
            'sales_order_payment_id' => $sales_order_payment->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->_revise_sales_order_payment($sales_order_payment->id)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise sales_order_payment
     */
    protected function _revise_sales_order_payment($sales_order_payment_id)
    {
        $sales_order_payment_date = DateTime::createFromFormat('m/d/Y', $this->input->post('sales_order_payment_date'));
        
        $discount = $this->input->post('discount') ?: null;
        $where = [
            'id' => $sales_order_payment_id
        ];

        $customer_id = $this->input->post('customer');

        $values = [
            'customer_id' => $customer_id,
            'sales_order_payment_date' => $sales_order_payment_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'updated_by' => $_SESSION['user']->id
        ];

        return $this->sales_order_payment->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($sales_order_payment)
    {
        $where = [
            'id' => $sales_order_payment->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->sales_order_payment->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($sales_order_payment)
    {
        $where = [
            'id' => $sales_order_payment->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->sales_order_payment->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if sales_order_payment Term is Either Cash or Check
     */
    public function _is_sales_order_payment_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
