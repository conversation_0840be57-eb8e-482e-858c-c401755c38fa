<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formprocessing" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $processing->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="processing_fee_date" class="control-label">Processing Fee Date</label>

						<input type="text" name="processing_fee_date" class="form-control datepicker" value="<?=set_value('processing_fee_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('processing_fee_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
				</div>

				<div class="col-md-12 form-group d-none">
					<label for="attachment[]" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment[]');?>
					</div>
				</div>

			<div>
            	<h5 class="crud-subtitle"><span>Processing Fees</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table processing-fees" data-type="processing_fees" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
								<tr class="no-item">
									<td class="text-center" colspan="7">No items added yet.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="processing-fees" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>

        	<h5 class="crud-subtitle"><span>Add-ons</span></h5>

			<div>
				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table add-ons" data-type="add_ons" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
								<tr class="no-item">
									<td class="text-center" colspan="7">No items added yet.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="add-ons" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>

        	<h5 class="crud-subtitle"><span>Other Charges</span></h5>

			<div>
				<div class="row">
					<div class="col-md-12 form-group other-charges">
						<table class="item-table" data-type="other_charges" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>
									<th class="text-center" style="width: 20%;">Qty.</th>
									<th class="text-center" style="width: 10%;">Unit</th>
									<th class="text-center" style="width: 10%;">Unit Price</th>
									<th class="text-center" style="width: 10%;">Amount</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
								<tr class="no-item">
									<td class="text-center" colspan="7">No items added yet.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" data-table="other-charges" class="btn btn-sm btn-dark action-add-item">Add Item</button>
					</div>
				</div>
			</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save and Proceed</button>
				<a href="<?=site_url('processings')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
