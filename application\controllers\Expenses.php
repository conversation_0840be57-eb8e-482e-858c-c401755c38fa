<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Expenses extends MYT_Controller
{

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Expenses';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/select2/dist/js/select2.full',
            'js/expenses/index'
        ];

        $where = [
            'is_deleted' => 0
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name');
        $data = [
            'suppliers' => $suppliers
        ];
        $this->build_content('default', 'expenses/index', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('supplier');
    }
}
