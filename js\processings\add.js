+ function ($) {
	'use strict';

	function addItemRow(table_type) {
		var $newRow = $(itemRow(table_type));

	    setItemList($newRow);
	    setStockKeepingUnitList($newRow);

		var $itemTable = $(`.${table_type}`);
		var order_slipItems = $itemTable.find('.processing_item');

		if (order_slipItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);

		$newRow.find('select[name^="stock_keeping_unit_"]').on('change', function(event) {
	        var stock_keeping_unit_id = event.currentTarget.value;
		    $.when(getStockKeepingUnitData(stock_keeping_unit_id)).then(
		      function (response) {
		      	console.log(response);
		      	$newRow.find('input[name^="size_range_"]').val(response.size_range);
		      	$newRow.find('input[name^="by_unit_"]').val(response.by_unit);
		      },
		      function () {
		      	$newRow.find('input[name^="size_range_"]').val("");
		      	$newRow.find('input[name^="by_unit_"]').val("");
		      }
		    );
			console.log(stock_keeping_unit_id);
		})

		setRowNumbers();
	}

	function setItem(processing_item, item_id=null, item_name=null) {
		var $newOption = $("<option selected='selected'></option>").val(item_id).text(item_name);
		processing_item.find('select[name^="item"]').append($newOption);
	}

	function setItemList(processing_item) {
		processing_item.find('select[name^="item"]').select2({
		  tags: false,
		  allowClear: true,
		  placeholder: $(this).attr('data-placeholder'),
		  width: '280px',
		  ajax: {
		      url: BASE_URI + 'live_sell_items/search_by_item',
		      dataType: "json",
		      type: "GET",
		      data: function (params) {
		          var queryParameters = {
		              term: params.term
		          }
		          return queryParameters;
		      },
		      processResults: function (data) {
		          return {
		              results: $.map(data, function (item) {
		                  return {
		                      text: item.name,
		                      id: item.id
		                  }
		              })
		          };
		      }
		  } 
		});
	}

	function setStockKeepingUnitList(processing_item) {
		processing_item.find('select[name^="stock_keeping_unit_"]').select2({
		  tags: false,
		  allowClear: true,
		  placeholder: $(this).attr('data-placeholder'),
		  width: '200px',
		  ajax: {
		      url: BASE_URI + 'stock_keeping_units/search_by_stock_keeping_unit',
		      dataType: "json",
		      type: "GET",
		      data: function (params) {
		          var queryParameters = {
		              term: params.term
		          }
		          return queryParameters;
		      },
		      processResults: function (data) {
		          return {
		              results: $.map(data, function (item) {
		                  return {
		                      text: item.name,
		                      id: item.id,
		                  }
		              })
		          };
		      }
		  } 
		});
	}

	function itemRow(table_type) {
		var rowId = makeRandomId();

		return '<tr class="processing_item">' +
				'<td>' +
				'<input type="hidden" name="classification_' + rowId + '" value="' + table_type + '">' +
				'<input type="hidden" name="processing_item_' +
				rowId +
				'" value="' +
				rowId +
				'">' +
				'<select style="min-width:200px" width="200px" name="stock_keeping_unit_' +
				rowId +
				'" class="form-control dropsearch" data-placeholder=""></select>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="size_range_' + rowId + '" class="form-control go-suggest" value="" data-suggest="size_ranges" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="by_unit_' + rowId + '" class="form-control go-suggest" value="" data-suggest="by_units" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="crates_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="heads_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="kilos_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="recovery_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td class="text-center">' +
					'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
				'</td>' +
			'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="7">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var order_slipItems = $itemTable.find('.processing_item');

		order_slipItems.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function setUnit(processing_item, available=null) {
		processing_item.find('input[name^="unit_"]').val(available);
	}

	function getUnit(itemId) {
		return $.ajax({
		  url: BASE_URI + 'live_sell_items/unit_of/' + itemId,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function getStockKeepingUnitData(stock_keeping_unit_id) {
		return $.ajax({
		  url: BASE_URI + 'stock_keeping_units/get_data/' + stock_keeping_unit_id,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function getCustomerData(customerId) {
		return $.ajax({
		  url: BASE_URI + 'live_sell_customers/get_data/' + customerId,
		  type: 'GET',
		  dataType: 'json',
		});
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.processing_item').remove();

		var $itemTable = $(event.delegateTarget);
		var order_slipItems = $itemTable.find('.processing_item');
		if (order_slipItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		setRowNumbers();
	}

	$(function () {
		$('.action-add-item').on('click', function() {
			// console.log($(this).attr('data-table'));
			let table_type = $(this).attr('data-table');
			addItemRow(table_type);
		});
		// $('.action-add-item').click(addItemRow);

		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			}).on('change', 'select[name^="item"]', function (event) {
		        var processing_item = $(event.currentTarget).closest('.processing_item');
		        var itemId = event.currentTarget.value;

			    $.when(getUnit(itemId)).then(
			      function (response) {
			        setUnit(processing_item, response.unit);
			      },
			      function () {
			        setUnit(processing_item, null);
			      }
			    );
		        console.log(itemId);
			});



		$('select[name="customer"]').on('change', function(event) {
	        var processing_item = $(event.currentTarget).closest('.processing_item');
	        var customerId = event.currentTarget.value;
		    $.when(getCustomerData(customerId)).then(
		      function (response) {
		      	console.log(response);
		      	$('input[name="address"]').val(response.address);
		      	$('input[name="contact_number"]').val(response.contact_number);
		      	$('input[name="email"]').val(response.email);
		      	$('input[name="available_credit"]').val(response.available_credit);
		        // setUnit(processing_item, response.unit);
		      },
		      function () {
		      	$('input[name="address"]').val("");
		      	$('input[name="contact_number"]').val("");
		      	$('input[name="email"]').val("");
		      	$('input[name="available_credit"]').val("");
		      }
		    );
	        console.log(customerId);
		})

		// $('select[name^="stock_keeping_unit_"]').on('change', function(event) {
		// 	console.log("HELLO")
	        // var processing_item = $(event.currentTarget).closest('.processing_item');
	        // var customerId = event.currentTarget.value;
		    // $.when(getCustomerData(customerId)).then(
		    //   function (response) {
		    //   	console.log(response);
		    //   	$('input[name="address"]').val(response.address);
		    //   	$('input[name="contact_number"]').val(response.contact_number);
		    //   	$('input[name="email"]').val(response.email);
		    //   	$('input[name="available_credit"]').val(response.available_credit);
		    //     // setUnit(processing_item, response.unit);
		    //   },
		    //   function () {
		    //   	$('input[name="address"]').val("");
		    //   	$('input[name="contact_number"]').val("");
		    //   	$('input[name="email"]').val("");
		    //   	$('input[name="available_credit"]').val("");
		    //   }
		    // );
	        // console.log(customerId);
		// })

	    $('.processing_item').each(function(i, obj) {
	    	$(obj).find('option').not(':selected').remove();
			setItemList($(obj));
			setStockKeepingUnitList($(obj));
		});

		$('select[name^="stock_keeping_unit_"]').on('change', function(event) {
			let $this = $(this);
	        var stock_keeping_unit_id = event.currentTarget.value;
		    $.when(getStockKeepingUnitData(stock_keeping_unit_id)).then(
		      function (response) {
				$this.parent().parent().find('input[name^="size_range_"]').val(response.size_range);
		      	$this.parent().parent().find('input[name^="by_unit_"]').val(response.by_unit);
		      },
		      function () {
				$this.parent().parent().find('input[name^="size_range_"]').val("");
		      	$this.parent().parent().find('input[name^="by_unit_"]').val("");
		      }
		    );
			console.log(stock_keeping_unit_id);
		})
	});
}(jQuery);
