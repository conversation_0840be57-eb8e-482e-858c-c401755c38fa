+ function ($, fns) {
	'use strict';

	function getInvoices($customer_id) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/partially_paid/' + $customer_id,
			type: 'GET',
			dataType: 'json'
		});
	}

	function addInvoiceRow(items) {
		var $newRow = $(invoiceRow());

		setInvoiceList($newRow, items);

		var $itemTable = $('.item-table');
		var paidInvoices = $itemTable.find('.paid_cash_bond');

		if (paidInvoices.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);
	}

	function invoiceRow() {
		var rowId = makeRandomId();

		return '<tr class="paid_cash_bond">' +
				'<td>' +
					'<input type="hidden" name="paid_cash_bond_' + rowId + '" value="' + rowId + '">' +
					'<select name="cash_bond_' + rowId + '" class="form-control dropsearch" data-placeholder=""></select>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="customer_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="cb_date_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="terms_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="total_' + rowId + '" class="form-control-plaintext" value="" min="0" step="any" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="paid_amount_' + rowId + '" class="form-control-plaintext" value="" min="0" step="any" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="balance_' + rowId + '" class="form-control-plaintext" value="" min="0" step="any" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="amount_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td class="text-center">' +
					'<button type="button" class="btn btn-sm btn-danger action-delete-dr">Delete</button>' +
				'</td>' +
			'</tr>';
	}

	function makeRandomId() {
		var id = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			id += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return id;
	}

	function setInvoiceList($paidInvoice, invoices) {
		$paidInvoice.find('select[name^="cash_bond"]').select2({
			data: invoices,
			placeholder: $(this).attr('data-placeholder'),
			allowClear: true,
			width: '100%'
		});
	}

	function getInvoice(invoiceId) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/info/' + invoiceId,
			type: 'GET',
			dataType: 'json'
		});
	}

	function setInvoice($row, cashBond) {
		var customer = '';
		var invoiceDate = '';
		var terms = '';
		var total = 0;
		var paidAmount = 0;
		var balance = 0;

		if (cashBond !== null) {
			customer = cashBond.customer;

			invoiceDate = fns.format(fns.parse(cashBond.cb_date), 'MMM DD, YYYY');

			terms = cashBond.terms;

			total = parseFloat(cashBond.grand_total);
			if (isNaN(total)) {
				total = 0;
			}

			paidAmount = parseFloat(cashBond.paid_amount);
			if (isNaN(paidAmount)) {
				paidAmount = 0;
			}

			balance = total - paidAmount;
			if (isNaN(balance)) {
				balance = 0;
			}
		}

		$row.find('input[name^="customer_"]').val(customer);
		$row.find('input[name^="cb_date"]').val(invoiceDate);
		$row.find('input[name^="terms"]').val(terms);
		$row.find('input[name^="total"]').val(total > 0 ? total.toFixed(2) : 0.00);
		$row.find('input[name^="paid_amount"]').val(paidAmount > 0 ? paidAmount.toFixed(2) : 0.00);
		$row.find('input[name^="balance"]').val(balance > 0 ? balance.toFixed(2) : 0.00);
		$row.find('input[name^="amount"]').val(balance > 0 ? balance.toFixed(2) : 0.00);
	}

	function removeInvoiceRow(event) {
		$(event.currentTarget).closest('.paid_cash_bond').remove();

		var $itemTable = $(event.delegateTarget);
		var paidInvoices = $itemTable.find('.paid_cash_bond');
		if (paidInvoices.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		calculateTotal();
	}

	function emptyRow() {
		return '<tr class="no-item">' +
			'<td class="text-center" colspan="9">' +
			'No invoice added yet.' +
			'</td>' +
			'</tr>';
	}

	function calculateTotal() {
		var zGrandTotal = 0;
		var zTotalPaid = 0;
		var zBalance = 0;
		var zAmount = 0;

		$('.item-table').find('.paid_cash_bond').each(function (_, paidInvoice) {
			var zSums = getRowTotal($(paidInvoice));
			zGrandTotal += zSums.total;
			zTotalPaid += zSums.paidAmount;
			zBalance += zSums.balance;
			zAmount += zSums.amount;
		});

		$('input[name="z_total"]').val(zGrandTotal.toFixed(2));
		$('input[name="z_paid_amount"]').val(zTotalPaid.toFixed(2));
		$('input[name="z_balance"]').val(zBalance.toFixed(2));
		$('input[name="z_amount"]').val(zAmount.toFixed(2));
		$('input[name="cash_amount"]').val(zAmount.toFixed(2));
		$('input[name="check_amount"]').val(zAmount.toFixed(2));
	}

	function getRowTotal($row) {
		var total = parseFloat(
			$row.find('input[name^="total"]').val()
		);
		if (isNaN(total)) {
			total = 0;
		}

		var paidAmount = parseFloat(
			$row.find('input[name^="paid_amount"]').val()
		);
		if (isNaN(paidAmount)) {
			paidAmount = 0;
		}

		var balance = parseFloat($row.find('input[name^="balance"]').val());
		if (isNaN(balance)) {
			balance = 0;
		}

		var amount = parseFloat($row.find('input[name^="amount"]').val());
		if (isNaN(amount)) {
			amount = 0;
		}

		return {
			total: total,
			paidAmount: paidAmount,
			balance: balance,
			amount: amount
		};
	}

	$(function () {
		var cash_bonds = [{
			id: '',
			text: ''
		}];
	
		function initializeSelect2() {
			$('.item-table').find('select[name^="cash_bond"]').select2({
				data: cash_bonds,
				placeholder: $(this).attr('data-placeholder'),
				allowClear: true,
				width: '100%'
			});
		}
	
		// Initialize Select2 for invoices
		initializeSelect2();
	
		$('select[name="customer"]').on('change', function (event) {
			var customerId = $('select[name="customer"]').val();
	
			// Reset the select elements for invoices
			$('.item-table').find('select[name^="cash_bond"]').empty().trigger('change');
	
			// Clear the invoices array before fetching new invoices
			cash_bonds = [{
				id: '',
				text: ''
			}];
	
			$.when(getInvoices(customerId)).then(
				function (response) {
					response.cash_bonds.forEach(function (cashBond) {
						cashBond.text = cashBond.id;
						cash_bonds.push(cashBond);
					});
	
					// Destroy and reinitialize Select2 with the new invoices
					$('.item-table').find('select[name^="invoice"]').select2('destroy');
					initializeSelect2();
				}
			);
		});

		$('.action-add-dr').click(function () {
			addInvoiceRow(cash_bonds);
		});

		$('.item-table')
			.on('change', 'select[name^="cash_bond"]', function (event) {
				var existing = $(event.delegateTarget).find('select[name^="cash_bond"] option[value="' + event.currentTarget.value + '"]:selected');
				if (existing.length > 1) {
					alert('Cash Bond already selected.');
					$(event.currentTarget).val('').trigger('change');
					return;
				}

				var paidInvoice = $(event.currentTarget).closest('.paid_cash_bond');

				$.when(getInvoice(event.currentTarget.value)).then(
					function (response) {
						setInvoice(paidInvoice, response.cash_bond);
						calculateTotal();
					},
					function () {
						setInvoice(paidInvoice, null);
						calculateTotal();
					}
				)
			})
			.on('click', '.action-delete-dr', function (event) {
				removeInvoiceRow(event)
			})
			.on('change', 'input[name^="amount"]', calculateTotal).trigger('change');

		$('input[name="type"]').change(function (event) {
			$('.payment-type').addClass('d-none');

			var type = event.currentTarget.value;
			$('#' + type).removeClass('d-none');
		});

		calculateTotal();
	});
}(jQuery, dateFns);
