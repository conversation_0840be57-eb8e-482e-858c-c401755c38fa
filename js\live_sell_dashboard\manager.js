+ function ($) {
	'use strict';

	$(function () {
		setTimeout(function() {
			for(let i=1;i<=7;i++) {
				let val = $(`#spin_${i}`).attr("data-val");
				$(`#spin_${i}`).parent().html(`<span class='fade-value'>${val}</span>`).hide().fadeIn();
				$(`#spin_${i}`).removeClass('spin');
			}
			$(`.spin`).parent().html(`<span>0</span>`);
			$(`.spin`).removeClass('spin');
		}, 1000);

        $('.card').hover(function() {
            const $proceed = $(this).find('.proceed');
            let width = $proceed.width() - 120;

            $(this).find('.arrow').animate({'right': width + 'px'});
            $(this).find('.proceed').delay(300).fadeIn(500);
        }, function() {
            $(this).find('.proceed').fadeOut(300);
            $(this).find('.arrow').delay(300).animate({'right': '5%'});
        });

        $('.card').on("click", function() {
        	console.log($(this).attr("data-url"));
        })
	});
}(jQuery);