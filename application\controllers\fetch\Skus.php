<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Skus extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch SKUs
     */
    public function index()
    {
        // Get request parameters
        $draw = intval($this->input->get_post('draw'));
        $start = intval($this->input->get_post('start'));
        $length = intval($this->input->get_post('length'));
        $search_value = $this->input->get_post('search')['value'];

        $this->db->select('
            customer.id as customer_id,
            customer.name as customer_name,
            sku.id as sku_id,
            sku.sku as sku_value,
            sku.size,
            sku.size_range,
            sku.by,
            sku.category,
            sku.added_on,
            sku.added_by
        ');
        $this->db->from('sku');
        $this->db->join('customer', 'customer.id = sku.customer_id', 'inner');
        $this->db->where('customer.is_deleted', 0);
        $this->db->where('sku.is_deleted', 0);
        $this->db->order_by('sku.added_on DESC, customer.name, sku.added_by, sku.category, sku.id');

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('customer.name', $search_value);
            $this->db->or_like('sku.sku', $search_value);
            $this->db->group_end();
        }

        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results();

        if ($length > 0) {
            $this->db->limit($length, $start);
        }

        $query = $this->db->get();
        $data = [];

        if ($query->num_rows() > 0) {
            $customer_data = [];

            foreach ($query->result() as $row) {
                $customer_key = $row->customer_id;

                if (!isset($customer_data[$customer_key])) {
                    $customer_data[$customer_key] = [
                        'customer_id' => $row->customer_id,
                        'customer_name' => $row->customer_name,
                        'carcass_records' => [],
                        'giblets_records' => []
                    ];
                }

                if ($row->category == 'Carcass') {
                    $customer_data[$customer_key]['carcass_records'][] = [
                        'sku' => $row->sku_value,
                        'size' => $row->size,
                        'range' => $row->size_range,
                        'by' => $row->by,
                        'added_on' => $row->added_on,
                        'added_by' => $row->added_by
                    ];
                } elseif ($row->category == 'Giblets') {
                    $customer_data[$customer_key]['giblets_records'][] = [
                        'sku' => $row->sku_value,
                        'by' => $row->by,
                        'added_on' => $row->added_on,
                        'added_by' => $row->added_by
                    ];
                }
            }

            $grouped_skus = [];

            foreach ($customer_data as $customer_key => $customer_info) {
                $carcass_groups = [];
                foreach ($customer_info['carcass_records'] as $carcass) {
                    $carcass_sku = $carcass['sku'];
                    if (!isset($carcass_groups[$carcass_sku])) {
                        $carcass_groups[$carcass_sku] = [];
                    }
                    $carcass_groups[$carcass_sku][] = $carcass;
                }

                foreach ($carcass_groups as $carcass_sku => $carcass_records) {
                    $submission_time = $carcass_records[0]['added_on'];
                    $submission_user = $carcass_records[0]['added_by'];
                    $submission_key = $customer_key . '_' . $submission_time . '_' . $submission_user . '_' . $carcass_sku;

                    if (!isset($grouped_skus[$customer_key])) {
                        $grouped_skus[$customer_key] = [
                            'customer_id' => $customer_info['customer_id'],
                            'customer_name' => $customer_info['customer_name'],
                            'orders' => []
                        ];
                    }

                    $matching_giblets = [];
                    foreach ($customer_info['giblets_records'] as $giblets) {
                        if ($giblets['added_on'] == $submission_time && $giblets['added_by'] == $submission_user) {
                            $matching_giblets[] = $giblets;
                        }
                    }

                    $grouped_skus[$customer_key]['orders'][$submission_key] = [
                        'added_on' => $submission_time,
                        'added_by' => $submission_user,
                        'carcass' => $carcass_records,
                        'giblets' => $matching_giblets 
                    ];
                }
            }

            foreach ($grouped_skus as $customer_key => $group) {
                foreach ($group['orders'] as $submission_key => $order) {
                    $carcass_sizes = [];
                    $carcass_ranges = [];
                    $carcass_bys = [];
                    $first_carcass_sku = '';

                    foreach ($order['carcass'] as $index => $carcass) {
                        if ($index == 0) {
                            $first_carcass_sku = $carcass['sku']; 
                        }
                        $carcass_sizes[] = $carcass['size'];
                        $carcass_ranges[] = $carcass['range'];
                        $carcass_bys[] = $carcass['by'];
                    }
                    $giblets_bys = [];
                    $giblets_skus = [];
                    $first_giblets_sku = '';

                    foreach ($order['giblets'] as $index => $giblets) {
                        if ($index == 0) {
                            $first_giblets_sku = $giblets['sku']; 
                        }
                        $giblets_skus[] = $giblets['sku'];
                        $giblets_bys[] = $giblets['by'];
                    }

                    $data[] = [
                        'DT_RowId' => $submission_key,
                        'DT_RowClass' => 'sku',
                        'customer_name' => $group['customer_name'],
                        'carcass_sku' => $first_carcass_sku,
                        'carcass_size' => implode('<br>', $carcass_sizes),
                        'carcass_range' => implode('<br>', $carcass_ranges),
                        'carcass_by' => implode('<br>', $carcass_bys),
                        'giblets_sku' => $first_giblets_sku,
                        'giblets_by' => implode('<br>', $giblets_bys),
                        'actions' => $this->_generate_actions($group['customer_id'], $order['added_on'], $order['added_by'])
                    ];
                }
            }
        }
        $response = [
            'draw' => $draw,
            'recordsTotal' => $this->_get_total_customers_with_skus(),
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        exit(json_encode($response));
    }
    /**
     * Generate actions for a customer submission
     */
    protected function _generate_actions($customer_id, $submission_time, $submission_user)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">';
        $res .= 'Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';

        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
            $this->db->where('customer_id', $customer_id);
            $this->db->where('DATE_FORMAT(added_on, "%Y-%m-%d %H:%i") = ', date('Y-m-d H:i', strtotime($submission_time)));
            $this->db->where('is_deleted', 0);
            $this->db->order_by('id', 'ASC');
            $sku = $this->db->get('sku')->row();

            if ($sku) {
                $res .= anchor('Skus/edit/' . $sku->id, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $sku->id . '">Delete</button>';
            }
        }

        $res .= '</div></div>';
        return $res;
    }
    /**
     * Get total unique orders (submissions) with SKUs
     */
    protected function _get_total_customers_with_skus()
    {
        $this->db->select('DISTINCT CONCAT(sku.customer_id, "_", sku.added_on, "_", sku.added_by) as submission_key');
        $this->db->from('sku');
        $this->db->join('customer', 'customer.id = sku.customer_id', 'inner');
        $this->db->where('customer.is_deleted', 0);
        $this->db->where('sku.is_deleted', 0);
        return $this->db->count_all_results();
    }
}
