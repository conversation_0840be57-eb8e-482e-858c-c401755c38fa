<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Order_slips extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('order_slips/manager?status=all'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review order_slip Order for Approval
     */
    public function receive($order_slip_id)
    {
        $order_slip = $this->order_slip->get_by_id($order_slip_id) or show_404();

        $this->title = 'Receive order_slip No. ' . $order_slip->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_slips/receive'
        ];

        $order_slip_items = $this->order_slip_item->get_by_order_slip($order_slip->id);

        $data = [
            'order_slip'          => $order_slip,
            'order_slip_items'    => $order_slip_items
        ];
        $this->build_content('default', 'order_slips/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }


    /**
     * Order Slip has been changed into Sales Order
     */
    

    /**
     * Add order_slip
     */
    public function add()
    {
        $order_slip_item_ids = $this->_get_order_slip_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/order_slips',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('order_slips/add'), $this->_get_order_slip_item_rules('', $order_slip_item_ids));

        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($order_slip_id = $this->_attempt_add())) {
            $new_path = FCPATH . 'assets/order_slips/' . $order_slip_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('order_slips/manager?status=all'));
        } else {
            $this->title = 'Add Sales Order';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/order_slips/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where, '', 'name');

            $retain_items = $this->_get_order_slip_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $data = [
                'form_error'        => $form_error,
                'order_slip_item_ids' => $order_slip_item_ids,
                'customers'         => $customers,
                'retain_items'      => $retain_items
            ];

            $this->build_content('default', 'order_slips/add', $data);
        }
    }

    /**
     * Edit order_slip
     */
    public function edit($order_slip_id)
    {
        $order_slip = $this->order_slip->get_by_id($order_slip_id) or show_404();

        $order_slip_items = $this->order_slip_item->get_by_order_slip($order_slip->id);

        // Once Initial items is edited
        $retain_items = $this->_get_order_slip_item_data();
        $rules = $this->config->item('order_slips/edit');

        // Initial items
        if (!$order_slip_item_ids = $this->_get_order_slip_item_ids()) {
            foreach($order_slip_items AS $order_slip_item) {
                $item = $this->item->select('', ['id' => $order_slip_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $order_slip_item->id,
                    'item_id' => $order_slip_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $order_slip_item->qty?:0,
                    'pref_alw' => $order_slip_item->pref_alw?:0,
                    'current_price' => $order_slip_item->current_price?:0,
                    'est_due_amount' => $order_slip_item->est_due_amount?:0,
                ];
                $order_slip_item_ids[] = $order_slip_item->id;
            }

            $existing_ids = array_map(function ($order_slip_item) {
                return $order_slip_item->id;
            }, $order_slip_items);

            $item_rules = $this->_get_order_slip_item_rules($order_slip->id, $existing_ids);
        } else {
            $item_rules = $this->_get_order_slip_item_rules($order_slip->id, $order_slip_item_ids);
        }

        $rules = array_merge($this->config->item('order_slips/edit'), $item_rules);

        if ($this->_validate_form($rules) && $this->_attempt_edit($order_slip)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('order_slips/preview/' . $order_slip->id));
            } else {
                redirect(site_url('order_slips/manager?status=all'));
            }
        } else {
            $this->title = 'Edit Sales Order';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/order_slips/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'order_slip' => $order_slip,
                'order_slip_items' => $order_slip_items,
                'order_slip_item_ids' => $order_slip_item_ids,
                'retain_items' => $retain_items
            ];

            $this->build_content('default', 'order_slips/edit', $data);
        }
    }

    /**
     * Approve order_slip
     */
    public function receive_order_slip($order_slip_id)
    {
        $where = [
            'id' => $order_slip_id,
            'is_deleted' => 0
        ];
        if (!$order_slip = $this->order_slip->select('', $where, 1)) {
            $response = [
                'message' => 'order_slip is not found.'
            ];
        } elseif (!$this->_attempt_receive($order_slip)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'order_slip is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($order_slip)
    {
        $where = [
            'id' => $order_slip->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->order_slip->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete order_slip
     */
    public function delete($order_slip_id)
    {
        $where = [
            'id' => $order_slip_id,
            'is_deleted' => 0
        ];
        if (!$order_slip = $this->order_slip->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Order Slip is not found.'
            ];
        } elseif (!$this->_attempt_delete($order_slip)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Order Slip is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * order_slips
     */
    public function manager()
    {
        $this->title = "Sales Order";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_slips/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'order_slips/manager', $data);
    }

    /**
     * order_slip Print Preview
     */
    public function preview($order_slip_id)
    {
        $order_slip = $this->order_slip->get_by_id($order_slip_id) or show_404();
        $order_slip_items = $this->order_slip_item->get_by_order_slip($order_slip_id);

        if (!$order_slip_item_ids = $this->_get_order_slip_item_ids()) {
            foreach($order_slip_items AS $order_slip_item) {
                $item = $this->item->select('', ['id' => $order_slip_item->item_id, 'is_deleted' => 0], 1);
                $retain_items[] = [
                    'id' => $order_slip_item->id,
                    'item_id' => $order_slip_item->item_id,
                    'item_name' => $item->name,
                    'unit' => $item->unit,
                    'qty' => $order_slip_item->qty,
                    'pref_alw' => $order_slip_item->pref_alw,
                    'current_price' => $order_slip_item->current_price,
                    'est_due_amount' => $order_slip_item->est_due_amount,
                ];
                $order_slip_item_ids[] = $order_slip_item->id;
            }

            $existing_ids = array_map(function ($order_slip_item) {
                return $order_slip_item->id;
            }, $order_slip_items);

            $item_rules = $this->_get_order_slip_item_rules($order_slip->id, $existing_ids);
        } else {
            $item_rules = $this->_get_order_slip_item_rules($order_slip->id, $order_slip_item_ids);
        }

        $this->title = 'Sales Order';

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/order_slips/preview',
        ];

        $where = [
            'id' => $order_slip->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $data = [
            'order_slip' => $order_slip,
            'customer' => $customer,
            'order_slip_items' => $order_slip_items,
            'order_slip_item_ids' => $order_slip_item_ids,
            'retain_items' => $retain_items
        ];
        $this->build_content('default', 'order_slips/preview', $data);
    }

    /**
     * Get Info for order_slip
     */
    public function info($order_slip_id = null)
    {
        $data = [
            'order_slip' => $this->order_slip->get_by_id($order_slip_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid order_slips
     */
    public function partially_paid()
    {
        $data = [
            'order_slips' => $this->order_slip->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('item');
        $this->load->model('customer');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_order_slip_item_ids()
    {
        $order_slip_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'order_slip_item_') === 0) {
                $order_slip_item_ids[] = $value;
            }
        }

        return $order_slip_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_order_slip_item_rules($order_slip_id, $order_slip_item_ids)
    {
        $rules = [];

        foreach ($order_slip_item_ids as $order_slip_item_id) {
            $rules[] = [
                'field' => 'item_' . $order_slip_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'qty_' . $order_slip_item_id,
                'label' => 'Qty',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'pref_alw_' . $order_slip_item_id,
                'label' => 'Pref Alw',
                'rules' => 'required|trim|numeric'
            ];

            $rules[] = [
                'field' => 'current_price_' . $order_slip_item_id,
                'label' => 'Current Price',
                'rules' => 'required|trim|numeric'
            ];

            $rules[] = [
                'field' => 'est_due_amount_' . $order_slip_item_id,
                'label' => 'Est Due Amount',
                'rules' => 'required|trim|numeric'
            ];

            $rules[] = [
                'field' => 'unit_' . $order_slip_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]'
            ];
        }

        return $rules;
    }

    /**
     * Insert order slip Items
     */
    protected function _insert_order_slip_items($order_slip_id, $order_slip_items)
    {
        $grand_total = 0;
        foreach ($order_slip_items as $order_slip_item) {
            $grand_total += $order_slip_item['est_due_amount']?:0;
            $values = [
                'order_slip_id' => $order_slip_id,
                'item_id' => $order_slip_item['item_id'],
                'qty' => $order_slip_item['qty'],
                'pref_alw' => $order_slip_item['pref_alw'],
                'current_price' => $order_slip_item['current_price'],
                'est_due_amount' => $order_slip_item['est_due_amount'],
                'unit' => $order_slip_item['unit'],
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];

            if (!$this->order_slip_item->insert($values)) {
                return false;
            }
        }

        $where = [
            'id' => $order_slip_id
        ];
        $values =[
            'grand_total' => $grand_total
        ];

        if (!$this->order_slip->update($where, $values)) {
            return false;
        }

        return true;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        if (!$order_slip_items = $this->_get_order_slip_item_data()) {
            $this->_error = 'No Order Slip Item Added';
            return false;
        }

        if (!$order_slip_id = $this->_generate_order_slip() or !$this->_insert_order_slip_items($order_slip_id, $order_slip_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $order_slip_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_order_slip_item_data()
    {
        $data = [];
        $order_slip_item_ids = $this->_get_order_slip_item_ids();

        foreach ($order_slip_item_ids as $order_slip_item_id) {
            $item_id = $this->input->post('item_' . $order_slip_item_id);
            $unit = $this->input->post('unit_' . $order_slip_item_id);
            $qty = $this->input->post('qty_' . $order_slip_item_id)?:0;
            $pref_alw = $this->input->post('pref_alw_' . $order_slip_item_id)?:0;
            $current_price = $this->input->post('current_price_' . $order_slip_item_id)?:0;
            $est_due_amount = $this->input->post('est_due_amount_' . $order_slip_item_id)?:0;
            $added_by = $_SESSION['user']->id;

            $where = [
                'id' => $item_id,
                'is_deleted' => 0
            ];

            $item = $this->item->select('', $where, 1);

            $data[] = [
                'id' => is_numeric($order_slip_item_id) ? $order_slip_item_id : null,
                'item_id' => $item_id,
                'item_name' => $item ? $item->name : "",
                'unit' => $unit,
                'qty' => $qty,
                'pref_alw' => $pref_alw,
                'current_price' => $current_price,
                'est_due_amount' => $est_due_amount,
                'added_by' => $added_by,
                'added_on' => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Generate order_slip
     */
    protected function _generate_order_slip()
    {
        $customer_id = $this->input->post('customer');

        $order_slip_date = DateTime::createFromFormat('m/d/Y', $this->input->post('order_slip_date'));
        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }
        $values = [
            'customer_id' => $customer_id,
            'order_slip_date' => $order_slip_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->order_slip->insert($values);
    }


    /**
     * Attempt Edit
     */
    protected function _attempt_edit($order_slip)
    {
        $this->db->trans_begin();

        $where = [
            'order_slip_id' => $order_slip->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->order_slip_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$order_slip_items = $this->_get_order_slip_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No Order Slip Item Added';
            return false;
        } elseif(!$this->_attempt_delete_items($order_slip->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No Order Slip Item Added';
            return false;
        } elseif (!$this->_revise_order_slip($order_slip->id, $order_slip_items) || !$this->_insert_order_slip_items($order_slip->id, $order_slip_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise order_slip
     */
    protected function _revise_order_slip($order_slip_id, $order_slip_items)
    {
        $order_slip_date = DateTime::createFromFormat('m/d/Y', $this->input->post('order_slip_date'));
        $subtotal = array_reduce($order_slip_items, function ($current_total, $order_slip_item) {
            if (isset($order_slip_item['amount'])) {
                return $current_total + $order_slip_item['amount'];
            }
            return $current_total;
        });
        
        $discount = $this->input->post('discount') ?: null;
        $where = [
            'id' => $order_slip_id
        ];

        $customer_id = $this->input->post('customer');

        $values = [
            'customer_id' => $customer_id,
            'order_slip_date' => $order_slip_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->order_slip->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($order_slip)
    {
        $where = [
            'id' => $order_slip->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->order_slip->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($order_slip)
    {
        $where = [
            'id' => $order_slip->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->order_slip->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($order_slip_id)
    {
        $where = [
            'order_slip_id' => $order_slip_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$is_deleted = $this->order_slip_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if order_slip Item Belongs to order_slip
     */
    public function _is_order_slip_item($order_slip_item_id, $order_slip_id)
    {
        if (!is_numeric($order_slip_item_id)) {
            return true;
        }

        $where = [
            'id' => $order_slip_item_id,
            'order_slip_id' => $order_slip_id
        ];

        return !empty($this->order_slip_item->select('', $where, 1));
    }

    /**
     * Callback: Check if order_slip Term is Either Cash or Check
     */
    public function _is_order_slip_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
