<?php
class Invoice_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'invoice_item';
        parent::__construct();
    }

    /**
     * Generate Invoice Item
     */
    public function generate($id, $rs, $alw, $invoice_id, $item_id, $qty, $unit, $price, $amount, $account_type, $added_by)
    {
        $sql = <<<EOT
INSERT INTO invoice_item(id, rs, alw, invoice_id, item_id, qty, unit, price, amount, account_type, added_by) (
    SELECT ? AS id, ? AS rs, ? AS alw, invoice.id AS invoice_id, sales_item.id AS item_id, ? AS qty, ? AS unit, ? AS price, ? AS amount, ? AS account_type, ? AS added_by
    FROM invoice
    CROSS JOIN sales_item
    WHERE invoice.id = ?
        AND invoice.is_deleted = 0
        AND sales_item.id = ?
        AND sales_item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    invoice_item.id = LAST_INSERT_ID(invoice_item.id),
    invoice_item.rs = VALUES(invoice_item.rs),
    invoice_item.alw = VALUES(invoice_item.alw),
    invoice_item.qty = VALUES(invoice_item.qty),
    invoice_item.unit = VALUES(invoice_item.unit),
    invoice_item.price = VALUES(invoice_item.price),
    invoice_item.amount = VALUES(invoice_item.amount),
    invoice_item.account_type = VALUES(invoice_item.account_type),
    invoice_item.updated_by = VALUES(invoice_item.added_by),
    invoice_item.is_deleted = 0
EOT;
        $binds = [$id, $rs, $alw, $qty, $unit, $price, $amount, $account_type, $added_by, $invoice_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Invoice Item By ID
     */
    public function get_by_id($invoice_item_id)
    {
        $sql = <<<EOT
SELECT invoice_item.*, sales_item.name AS item
FROM invoice_item
LEFT JOIN sales_item ON sales_item.id = invoice_item.item_id
WHERE invoice_item.id = ?
    AND invoice_item.is_deleted = 0
EOT;
        $binds = [$invoice_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Invoice Items by Invoice
     */
    public function get_by_invoice($invoice_id)
    {
        $sql = <<<EOT
SELECT invoice_item.*, sales_item.name AS item
FROM invoice_item
LEFT JOIN sales_item ON sales_item.id = invoice_item.item_id
WHERE invoice_item.invoice_id = ?
    AND invoice_item.is_deleted = 0
EOT;
        $binds = [$invoice_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Revenue Sheet
     */
    public function get_by_revenue($date_from, $date_to)
    {
        $sql = <<<EOT
SELECT *
FROM (
    SELECT 
        invoice_item.account_type AS account_name, 
        SUM(invoice_item.amount) AS amount
    FROM invoice_item
    LEFT JOIN invoice ON invoice.id = invoice_item.invoice_id
    WHERE invoice.is_deleted = 0
        AND invoice.status = 'approved'
        AND invoice.invoice_date BETWEEN ? AND ?
    GROUP BY invoice_item.account_type 

    UNION ALL

    SELECT
        'Revenue - Ice Plant POS' AS account_name,
        SUM(pos_invoice.total) AS amount
    FROM pos_invoice
    WHERE pos_invoice.is_deleted = 0
        AND pos_invoice.invoice_date BETWEEN ? AND ?
) AS invoice_item 
ORDER BY invoice_item.account_name ASC
EOT;
        $binds = [$date_from, $date_to, $date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Revenue Sheet
     */
    public function get_revenue_by_year($year)
    {
        $sql = <<<EOT
SELECT *
FROM (
    SELECT 
        invoice_item.account_type AS account_name,
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 1 THEN invoice_item.amount END), 0) AS 'jan',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 2 THEN invoice_item.amount END), 0) AS 'feb',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 3 THEN invoice_item.amount END), 0) AS 'mar',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 4 THEN invoice_item.amount END), 0) AS 'apr',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 5 THEN invoice_item.amount END), 0) AS 'may',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 6 THEN invoice_item.amount END), 0) AS 'jun',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 7 THEN invoice_item.amount END), 0) AS 'jul',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 8 THEN invoice_item.amount END), 0) AS 'aug',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 9 THEN invoice_item.amount END), 0) AS 'sep',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 10 THEN invoice_item.amount END), 0) AS 'oct',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 11 THEN invoice_item.amount END), 0) AS 'nov',
        COALESCE(SUM(CASE WHEN MONTH(invoice.invoice_date) = 12 THEN invoice_item.amount END), 0) AS 'dec',
        COALESCE(SUM(invoice_item.amount), 0) AS 'grand_total'
    FROM invoice_item
    LEFT JOIN invoice ON invoice.id = invoice_item.invoice_id
    WHERE invoice.is_deleted = 0
        AND invoice.status = 'approved'
        AND YEAR(invoice.invoice_date) = ?
    GROUP BY invoice_item.account_type

    UNION ALL

    SELECT
        'Revenue - Ice Plant POS' AS account_name,
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 1 THEN pos_invoice.total END), 0) AS 'jan',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 2 THEN pos_invoice.total END), 0) AS 'feb',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 3 THEN pos_invoice.total END), 0) AS 'mar',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 4 THEN pos_invoice.total END), 0) AS 'apr',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 5 THEN pos_invoice.total END), 0) AS 'may',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 6 THEN pos_invoice.total END), 0) AS 'jun',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 7 THEN pos_invoice.total END), 0) AS 'jul',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 8 THEN pos_invoice.total END), 0) AS 'aug',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 9 THEN pos_invoice.total END), 0) AS 'sep',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 10 THEN pos_invoice.total END), 0) AS 'oct',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 11 THEN pos_invoice.total END), 0) AS 'nov',
        COALESCE(SUM(CASE WHEN MONTH(pos_invoice.invoice_date) = 12 THEN pos_invoice.total END), 0) AS 'dec',
        COALESCE(SUM(pos_invoice.total), 0) AS 'grand_total'
    FROM pos_invoice
    WHERE pos_invoice.is_deleted = 0
        AND YEAR(pos_invoice.invoice_date) = ?
) AS invoice_item 
ORDER BY invoice_item.account_name ASC
EOT;
        $binds = [$year, $year];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Revenue Breakdown
     */
    public function get_revenue_breakdown_by_day($date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT calendar.date AS revenue_date,
COALESCE(SUM(invoice.amount), 0) AS total_revenue_per_day
FROM (
    SELECT ? + INTERVAL n DAY AS date
    FROM (
        SELECT 
            a.N + b.N * 10 + c.N * 100 AS n
        FROM
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
        ) AS numbers
    WHERE ? + INTERVAL n DAY <= ?
) AS calendar
LEFT JOIN (
    SELECT
        invoice.invoice_date, 
        SUM(invoice_item.amount) AS amount
    FROM invoice_item
    LEFT JOIN invoice ON invoice.id = invoice_item.invoice_id
    WHERE invoice.is_deleted = 0
        AND invoice_item.is_deleted = 0
        AND invoice.status = 'approved'
    GROUP BY invoice.invoice_date

    UNION ALL

    SELECT
        pos_invoice.invoice_date,
        SUM(pos_invoice.total) AS amount
    FROM pos_invoice
    WHERE pos_invoice.is_deleted = 0
    GROUP BY pos_invoice.invoice_date
) AS invoice ON calendar.date = invoice.invoice_date
GROUP BY calendar.date
ORDER BY calendar.date;
EOT;
        $binds = [$date_from, $date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
