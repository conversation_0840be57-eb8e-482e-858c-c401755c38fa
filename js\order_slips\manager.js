+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
				{
				  searchable: true,
				  targets: [2, 3]
				},
				{
				  className: 'text-right',
				  targets: [4, 5],
				},
				{
				  className: 'text-left',
				  targets: [0, 1, 2, 3, 6],
				},
				{
				  className: 'exclude-print',
				  targets: [7],
				},
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status,
			footerCallback: function (row, data, start, end, display) {
				var api = this.api();
				var colSum = $table.attr("data-col-sum");

				if (typeof colSum !== "undefined" && colSum > -1) {
					// returns the numeric value of data
					var intVal = function (i) {
						return typeof i === "string"
							? i.replace(/[\$,]/g, "") * 1
							: typeof i === "number"
							? i
							: 0;
					};

					// total of all data in table
					var grandTotal = api
						.column(colSum)
						.data()
						.reduce(function (a, b) {
							return intVal(a) + intVal(b);
						}, 0);

					$(api.column(colSum).footer()).html(
						$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
					);
				}
			},
		});

		return dataTable;
	}

	function receiveAndReload(order_slipId, dataTable) {
		$.when(receiveRow(order_slipId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(order_slipId) {
		return $.ajax({
			url: BASE_URI + 'order_slips/receive_order_slip/' + order_slipId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(order_slipId, dataTable) {
		$.when(deleteRow(order_slipId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(order_slipId) {
		return $.ajax({
			url: BASE_URI + 'order_slips/delete/' + order_slipId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.order_slip').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the order slip ' + docNo + '?',
					function () {
						var order_slipId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(order_slipId, dataTable);
					}
				).setHeader('<em>receive order_slip</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.order_slip').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete order slip' + docNo + '?',
					function () {
						var order_slipId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(order_slipId, dataTable);
					}
				).setHeader('<em>Delete Order Slip</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/order_slips?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'order_slips/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
