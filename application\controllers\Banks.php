<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Serendipite Check Maker
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Banks extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('banks/manager'));
    }

    /**
     * Add bank main
     */
    public function add()
    {
        $rules = $this->config->item('banks/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('banks/manager'));
        } else {
            $this->title = 'Add Bank';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $check_templates = $this->check_template->select('', $where);

            $data = [
                'form_error' => $form_error,
                'check_templates' => $check_templates
            ];
            $this->build_content('default', 'banks/add', $data);
        }
    }

    /**
     * Add via AJAX main
     */
    public function add_remote()
    {
        $rules = $this->config->item('banks/add');

        if ($this->_validate_form($rules) and $bank_id = $this->_attempt_add()) {
            $response = [
                'message' => 'Bank is successfully added',
                'bank_id' => $bank_id
            ];
        } else {
            http_response_code(400);

            $response = [
                'message' => $this->_error_msg()
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Edit bank main
     */
    public function edit($bank_id = null)
    {
        $where = [
            'id' => $bank_id,
            'is_deleted' => 0
        ];
        $bank = $this->bank->select('', $where, 1) or show_404();

        $rules = $this->config->item('banks/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit($bank)) {
            redirect(site_url('banks/manager'));
        } else {
            $this->title = 'Edit Bank';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $check_templates = $this->check_template->select('', $where);

            $data = [
                'form_error' => $form_error,
                'bank' => $bank,
                'check_templates' => $check_templates
            ];
            $this->build_content('default', 'banks/edit', $data);
        }
    }

    /**
     * Delete bank main
     */
    public function delete($bank_id = null)
    {
        $where = [
            'id' => $bank_id,
            'is_deleted' => 0
        ];

        if (!$bank = $this->bank->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Bank is not found.'
            ];
        } elseif (!$this->_attempt_delete($bank)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Bank is successfully removed.'
            ];
        }

        exit(json_encode($response));
    }

    /**
     * Banks manager main
     */
    public function manager()
    {
        $this->title = 'Banks';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/banks/manager'
        ];
        $this->build_content('default', 'banks/manager');
    }

    /**
     * Load pre-requisite models, config files, and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('bank');
        $this->load->model('check_template');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'print_template' => $this->input->post('print_template'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];
        if (!$bank_id = $this->bank->insert($values)) {
            $this->_error = 'server_error';
        }

        return $bank_id;
    }

    /**
     * Attempt edit
     */
    protected function _attempt_edit($bank)
    {
        $where = [
            'id' => $bank->id
        ];
        $values = [
            'name' => $this->input->post('name'),
            'print_template' => $this->input->post('print_template'),
            'updated_by' => $_SESSION['user']->id
        ];

        return $this->bank->update($where, $values) or !$this->_error = 'server_error';
    }

    /**
     * Attempt delete
     */
    protected function _attempt_delete($bank)
    {
        $where = [
            'id' => $bank->id
        ];
        $values = [
            'name' => $bank->name . '🔥' . uniqid(),
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->bank->update($where, $values) or !$this->_error = 'server_error';
    }

    /**
     * Callback: Check if check template is valid
     */
    public function _is_check_template($check_template_id)
    {
        if (trim($check_template_id) === '') {
            return true;
        }

        $where = [
            'id' => $check_template_id
        ];
        $check_template = $this->check_template->select('', $where, 1);

        return !empty($check_template);
    }
}
