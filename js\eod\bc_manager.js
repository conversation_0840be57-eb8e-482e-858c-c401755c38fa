+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function getAttachments(dId, table) {
		return $.ajax({
			url: BASE_URI + 'eod/get_attachments/' + dId + '/' + table,
			type: 'GET',
			dataType: 'json'
		});
	}

	function deleteRow(attachmentId, table) {
		return $.ajax({
			url: BASE_URI + 'eod/delete_attachment/' + attachmentId + '/' + table,
			type: 'POST',
			dataType: 'json'
		});
	}

	function promptUploadAttachment(dId, oldAttachments, dataTable) {
		return new Promise((resolve, reject) => {
			function updateModal() {
				let html = '<div>';
	
				// Add old attachments if they exist
				if (oldAttachments && oldAttachments.length > 0) {
					html += '<div class="old-attachments">';
					html += '<h5>Existing Attachments:</h5>';
					html += '<ul>';
					let number = 0;
					for (let attachment of oldAttachments) {
						number++;
						html += `
							<li style="display: flex; align-items: center;">
								• <a href="${BASE_URI}assets/eod/eod_bc/${dId}/${attachment.file_name}" target="_blank" style="flex-grow: 1;">Attachment ${number}</a>
								<button type="button" class="delete-attachment" data-id="${attachment.id}" style="background: none; border: none; color: red; cursor: pointer;">
									<i class="fa fa-trash"></i>
								</button>
							</li>`;
					}
					html += '</ul>';
					html += '</div>';
				}
	
				// Add file input for new attachments
				html += '<div class="new-attachments">';
				html += '<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>';
				html += '</div>';
	
				html += '</div>';
	
				alertify.confirm(html,
					function () {
						let files = document.getElementById('attachments').files;
						resolve({ files: files });
					},
					function () {
						reject('Upload Canceled');
					}
				).setting({
					'reverseButtons': true,
					'closable': false,
					'title': 'Add Attachment'
				}).show();
	
				// Remove existing event listeners
				$(document).off('click', '.delete-attachment');
	
				// Add new event listeners for delete buttons
				$(document).on('click', '.delete-attachment', function (event) {
					let attachmentId = event.currentTarget.getAttribute('data-id');
	
					deleteRow(attachmentId, 'eod_bc').done(function() {
						oldAttachments = oldAttachments.filter(attachment => attachment.id != attachmentId);
						dataTable.ajax.reload(null, false);
						updateModal();
					}).fail(function(jqXHR) {
						let response = JSON.parse(jqXHR.responseText);
						alertify.error(response.message);
					});
				});
			}
	
			updateModal();
		});
	}

	function uploadAttachment(dId, files) {
        var formData = new FormData();
		formData.append('table', 'eod_bc');
        formData.append('id', dId);
        for (var i = 0; i < files.length; i++) { 
            formData.append('attachments[]', files[i]);
        }

        return $.ajax({
            url: BASE_URI + 'eod/upload_attachment/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json'
        }).then(response => {
            return {
                success: true,
                message: 'Files Uploaded.'
            };
        });
    }

	$(function () {
		var dataTable = createDataTable('.tableA');
		var attachments = [];

		$('.tableA')
			.on('click', '.action-add', function (event) {
				var dId = event.currentTarget.getAttribute('data-id');

				attachments = [];

				getAttachments(dId, 'eod_bc').then(function (response) {
					response.data.forEach(function (attachment) {
						attachment.text = attachment.file_name;
						attachments.push(attachment);
					});
			
					return promptUploadAttachment(dId, attachments, dataTable);
				}).then(({ files }) => {
					return uploadAttachment(dId, files);
				}).then(response => {
					alertify.success(response.message);
					window.location.reload();
				}).catch(error => {
					alertify.error(error);
				});
			});
	});
}(jQuery);
