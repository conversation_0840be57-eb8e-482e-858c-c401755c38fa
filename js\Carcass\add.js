$(document).ready(function() {
    function carcassRowTemplate() {
        // Get values from the first row's fields
        var firstRowNo = $('#carcassTableBody .carcass-row:first input[name="no[]"]').val() || '1';
        var firstRowPcs = $('#carcassTableBody .carcass-row:first input[name="pcs[]"]').val() || '';
        var firstRowKgs = $('#carcassTableBody .carcass-row:first input[name="kgs[]"]').val() || '';

        // Create a row that copies all values from the first row
        return '<tr class="carcass-row">' +
            '<td><input type="text" class="form-control" name="no[]" value="' + firstRowNo + '" readonly style="background-color: #f9f9f9; cursor: not-allowed;"></td>' +
            '<td><input type="text" class="form-control" name="pcs[]" value="' + firstRowPcs + '"></td>' +
            '<td><input type="text" class="form-control" name="kgs[]" value="' + firstRowKgs + '"></td>' +
            '<td class="text-center" style="vertical-align: middle;">' +
                '<button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>' +
            '</td>' +
        '</tr>';
    }

    // Function to update all fields when the first row changes
    function updateAllFields() {
        var firstRowNo = $('#carcassTableBody .carcass-row:first input[name="no[]"]').val() || '';
        var firstRowPcs = $('#carcassTableBody .carcass-row:first input[name="pcs[]"]').val() || '';
        var firstRowKgs = $('#carcassTableBody .carcass-row:first input[name="kgs[]"]').val() || '';

        // Update all fields for all rows except the first one
        $('#carcassTableBody .carcass-row:not(:first)').each(function() {
            $(this).find('input[name="no[]"]')
                .val(firstRowNo)
                .prop('readonly', true)
                .css({
                    'background-color': '#f9f9f9',
                    'cursor': 'not-allowed'
                });

            $(this).find('input[name="pcs[]"]').val(firstRowPcs);
            $(this).find('input[name="kgs[]"]').val(firstRowKgs);
        });

        // Also update the first row's NO field styling if it has a value
        if (firstRowNo) {
            $('#carcassTableBody .carcass-row:first input[name="no[]"]').css('font-weight', 'bold');
        }
    }

    // Add new row with auto-filled values from first row
    $('#addCarcassRow').click(function() {
        var newRow = carcassRowTemplate();
        $('#carcassTableBody').append(newRow);

        // Immediately update all fields for the new row and apply styling
        updateAllFields();
    });

    // Update all fields when any field in the first row changes (on input)
    $('#carcassTable').on('input', '.carcass-row:first input', function() {
        updateAllFields();
    });

    // Update all fields when any field in the first row loses focus (on blur)
    $('#carcassTable').on('blur', '.carcass-row:first input', function() {
        updateAllFields();
    });

    // Function to fix row structure
    function fixRowStructure() {
        var $firstRow = $('#carcassTableBody .carcass-row:first');
        var firstRowCellCount = $firstRow.find('td').length;

        // Check all rows after the first one
        $('#carcassTableBody .carcass-row:not(:first)').each(function() {
            var $row = $(this);
            var currentCellCount = $row.find('td').length;

            // If this row doesn't match the first row's structure, replace it
            if (currentCellCount !== firstRowCellCount) {
                $row.replaceWith(carcassRowTemplate());
            }
        });
    }

    // Initialize synchronization on page load
    setTimeout(function() {
        // First fix any structural issues
        fixRowStructure();

        // Then update all fields to follow the first row
        updateAllFields();
    }, 100);

    // Delete row functionality
    $('#carcassTable').on('click', '.action-delete-row', function() {
        var rowCount = $('#carcassTableBody .carcass-row').length;
        if (rowCount > 1) {
            $(this).closest('.carcass-row').remove();
        } else {
            alert('At least one row is required.');
        }
    });
});