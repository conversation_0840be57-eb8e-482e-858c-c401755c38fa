+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(customerId, dataTable) {
		$.when(deleteRow(customerId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(payeeId) {
		return $.ajax({
			url: BASE_URI + 'payees/delete/' + payeeId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.customer').find('td');
			var name = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete ' + name + '?',
				function () {
					var payeeId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(payeeId, dataTable);
				}
			).setHeader('<em>Delete Payee</em>');
		});
	});
}(jQuery);
