+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			language: {
				infoFiltered: '',
			},
			dom: 'Bfrtlip',
			// info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: [2, 3]
			}],
			buttons: [{
                extend: 'print',
                className: 'btn btn-secondary',
                footer: true,
                exportOptions: {
                    stripHtml: false
                }
            },
            {
                extend: 'excel',
                className: 'btn btn-success ml-2',
                text: 'Download Excel',
                footer: true,
                exportOptions: {
                    columns: 'th'
                }
            },
        ],
			autoWidth: false,
			processing: true,
			serverSide: true,
			lengthMenu: [
				[10, 25, 50, 100, -1],
				[10, 25, 50, 100, 'All'],
			],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('#tableFilter')
			.submit(function (event) {
				event.preventDefault();

				var queries = $(event.currentTarget).serialize();

				var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + queries;
				dataTable.ajax.url(url).load();

				window.history.pushState(null, null, BASE_URI + 'invoices/bad_debts?' + queries);
			})
			.trigger('submit');
	});
}(jQuery);
