<div class="segment">

	<h1 id="<%= @name %>">
		<a href="#<%= @name %>">
			<i class="fa fa-<%= if @type == 'p' then 'cog' else if @type == 'm' then 'wrench' else 'flash' %>"></i>
		</a>
		<%= @name %>
		<%if @type == 'm' :%>
		(
			<%for p, index in @params:%>	
				<%=p.name%><%=if index < @params.length-1 then "," %>
			<%end%>				
		)
		<%end%>
		<code><%= @label %></code>
	</h1>
    <hr/>
	<div>
		<p><span>Description:</span> <%- @desc %></p>
		<% if @defaultValue :%>
		<p><span> Default:</span> <code><%= @defaultValue %></code></p>
		<% else if @params : %>
			<% if @params.length > 0 :%>
				<p><span> Parameters:</span></p>
				<blockquote>
						<%for p in @params:%>	
							<p>@<%=p.name%> <code>{<%=p.type%>}</code> <%-p.desc%></p>
						<%end%>				
				</blockquote>
			<% else :%>
				<p><span> Parameters:</span> none</p>
			<% end %>			
		<%end%>
        <% if @return :%>
            <p><span> Returns:</span></p>
            <blockquote>
                <p><%=@return%></p>
            </blockquote>
        <%end%>
            
<pre>
<code class="lang-javascript"><% if typeof @script == 'string' :%> <%- @script %> <% else :%> <%for line in @script:%><%-line+'\n'%> <%end%> <% end %></code>
</pre>

		<% if @exec != false :%>
		<div class="RIGHT">			
			<% if @toggle :%>
			<div class="ui inverted toggle checkbox example">
					<input type="checkbox" <%=if @toggle.value == true then checked="checked"%> >
					<label>Toggle</label>
			</div>
			<% end %>
			
			<% if @list :%>
			 <div class="ui selection dropdown example" style="min-width:100px;">
                <input type="hidden" name="transition">
                <div class="default text"><%=@list.value%></div>
                <i class="fa fa-caret-down"></i>
                <div class="menu">
				<%for item in @list.items:%>
                  <div class="item"><%=item%></div>
				<%end%>
                </div>
              </div>
			<% end %>
			<a class="button example" href="javascript:void(0);">
				<span> Run </span>
			</a>
		</div>
		<% end %>           
       
		
	</div>
	<br/>
</div>


<%if (@isDialog && @type == 'p') || @remark :%>
<h2> <i class="fa fa-info-circle"></i> Remarks </h2>
<% end %>

<% if @remark :%>
<div class="ui small info message">
    <div class="content">
        <p>
            <%-@remark%> 
        </p>
    </div>
</div>
<% end %>

<%if @isDialog && @type == 'p' :%>
<div class="segment">
    <div class="ui small info message">
        <div class="content">
            <p>
                You can use the <code>.setting()</code> method to set multiple values at once, Also works as a getter. Or use <code>.get()</code> and <code>.set()</code> aliases.
            </p>
        </div>
    </div>
    <div>
<pre>
<code class="lang-javascript">
//get the closable setting value.
var closable = alertify.alert().setting('closable');
//grab the dialog instance using its parameter-less constructor then set multiple settings at once.
alertify.alert()
  .setting({
    'label':'Agree',
    'message': 'This dialog is : ' + (closable ? ' ' : ' not ') + 'closable.' ,
    'onok': function(){ alertify.success('Great');}
  }).show();
</code> 
</pre>
        <div class="RIGHT">
            <a class="button example" href="javascript:void(0);">
                <span> Run </span>
            </a>
        </div>
    </div>
</div>
<%end%>            