<?php
class Paid_so extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'paid_so';
        parent::__construct();
    }

    /**
     * Get Paid Invoices Between Dates
     */
    public function get_between_dates($from, $to)
    {
        $sql = <<<EOT
SELECT paid_so.*,
    payment.payment_date
FROM paid_so
LEFT JOIN payment ON payment.id = paid_so.so_payment_id
WHERE payment.payment_date BETWEEN ? AND ?
    AND paid_so.is_deleted = 0
EOT;
        $binds = [$from, $to];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Paid Invoices Between Dates
     */
    public function get_by_so_payment($so_payment_id)
    {
        $sql = <<<EOT
SELECT paid_so.*,
    payment.payment_date,
    invoice.invoice_date,
    invoice.invoice_no AS invoice_no,
    customer.name AS customer
FROM paid_so
LEFT JOIN payment ON payment.id = paid_so.so_payment_id
LEFT JOIN invoice ON invoice.id = paid_so.invoice_id
LEFT JOIN customer ON customer.id = invoice.customer_id
WHERE paid_so.so_payment_id = ?
    AND paid_so.is_deleted = 0
EOT;
        $binds = [$so_payment_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
