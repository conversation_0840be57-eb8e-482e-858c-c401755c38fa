<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Login
|--------------------------------------------------------------------------
 */

$config['login/index'] = [
    [
        'field' => 'username',
        'label' => 'Username',
        'rules' => 'required|max_length[150]'
    ],
    [
        'field' => 'password',
        'label' => 'Password',
        'rules' => 'required'
    ]
];

/*
|--------------------------------------------------------------------------
| Add user
|--------------------------------------------------------------------------
 */

$config['users/add'] = [
    [
        'field' => 'username',
        'label' => 'Username',
        'rules' => 'required|trim|max_length[150]'
    ],
    [
        'field' => 'full_name',
        'label' => 'Full Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'password',
        'label' => 'Password',
        'rules' => 'required|min_length[8]'
    ],
    [
        'field' => 'passconf',
        'label' => 'Confirm Password',
        'rules' => 'required|matches[password]'
    ],
    [
        'field' => 'role',
        'label' => 'Role',
        'rules' => 'required|trim|callback__is_role',
        'errors' => [
            '_is_role' => 'Role cannot be found.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit user
|--------------------------------------------------------------------------
 */

$config['users/edit'] = [
    [
        'field' => 'username',
        'label' => 'Username',
        'rules' => 'required|trim|max_length[150]'
    ],
    [
        'field' => 'full_name',
        'label' => 'Full Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'role',
        'label' => 'Role',
        'rules' => 'required|trim|callback__is_role',
        'errors' => [
            '_is_role' => 'Role cannot be found.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Reset user password
|--------------------------------------------------------------------------
 */

$config['users/reset_password'] = [
    [
        'field' => 'new_password',
        'label' => 'Password',
        'rules' => 'required|min_length[8]'
    ],
    [
        'field' => 'new_passconf',
        'label' => 'Confirm Password',
        'rules' => 'required|matches[new_password]'
    ]
];

/*
|--------------------------------------------------------------------------
| Order slip add
|--------------------------------------------------------------------------
 */

$config['order_slips/add'] = [
    [
        'field' => 'order_slip_date',
        'label' => 'Order Slip Date',
        'rules' => 'required'
    ],
    [
        'field' => 'customer',
        'label' => 'Customer',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Edit Order Slips
|--------------------------------------------------------------------------
 */

 $config['order_slips/edit'] = $config['order_slips/add'];



/*
|--------------------------------------------------------------------------
| Order confirmation add
|--------------------------------------------------------------------------
 */

$config['order_confirmations/add'] = [
    [
        'field' => 'order_confirmation_date',
        'label' => 'Order confirmation Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Order confirmation edit
|--------------------------------------------------------------------------
 */

$config['order_confirmations/edit'] = $config['order_confirmations/add'];

/*
|--------------------------------------------------------------------------
| Purchase order add
|--------------------------------------------------------------------------
 */

$config['purchase_orders/add'] = [
    [
        'field' => 'purchase_order_date',
        'label' => 'Purchase Order Date',
        'rules' => 'required'
    ],
    [
        'field' => 'date_covered',
        'label' => 'Date Covered',
        'rules' => 'required'
    ],
    [
        'field' => 'supplier',
        'label' => 'Supplier',
        'rules' => 'required'
    ],
    [
        'field' => 'usage',
        'label' => 'Usage',
        'rules' => 'required'
    ],
    [
        'field' => 'requisitioner',
        'label' => 'Requisitioner',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Purchase order edit
|--------------------------------------------------------------------------
 */

$config['purchase_orders/edit'] = $config['purchase_orders/add'];

/*
|--------------------------------------------------------------------------
| Schedule hauling add
|--------------------------------------------------------------------------
 */

$config['schedule_haulings/add'] = [
    [
        'field' => 'schedule_hauling_date',
        'label' => 'Schedule Hauling Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Add Payment
|--------------------------------------------------------------------------
 */
$config['sales_order_payments/add'] = [
    [
        'field' => 'payment_date',
        'label' => 'Payment Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must contain a date in mm/dd/yyyy format.'
        ]
    ],
    [
        'field' => 'payment_type',
        'label' => 'Payment Type',
        'rules' => 'required|trim'
    ]
];

/*
| Schedule hauling edit
|--------------------------------------------------------------------------
 */

 $config['schedule_haulings/edit'] = $config['schedule_haulings/add'];

/*
|--------------------------------------------------------------------------
| Processing add
|--------------------------------------------------------------------------
 */

$config['processings/add'] = [
    [
        'field' => 'processing_date',
        'label' => 'Processing Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Processing edit
|--------------------------------------------------------------------------
 */

 $config['processings/edit'] = $config['processings/add'];

/*
|--------------------------------------------------------------------------
| Processing fee add
|--------------------------------------------------------------------------
 */

$config['processing_fees/add'] = [
    [
        'field' => 'processing_fee_date',
        'label' => 'Processing Fee Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Processing fee edit
|--------------------------------------------------------------------------
 */

 $config['processing_fees/edit'] = $config['processing_fees/add'];

/*
|--------------------------------------------------------------------------
| Sales order add
|--------------------------------------------------------------------------
 */

$config['sales_orders/add'] = [
    [
        'field' => 'sales_order_date',
        'label' => 'Sales Order Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Sales order edit
|--------------------------------------------------------------------------
 */

$config['sales_orders/edit'] = $config['sales_orders/add'];

/*
|--------------------------------------------------------------------------
| Add Invoice
|--------------------------------------------------------------------------
 */

$config['invoices/add'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'address',
        'label' => 'Address',
        'rules' => 'trim'
    ],
    [
        'field' => 'Phone No',
        'label' => 'phone_no',
        'rules' => 'trim|max_length[100]'
    ],
    [
        'field' => 'email',
        'label' => 'Email',
        'rules' => 'trim|max_length[255]|valid_email'
    ],
    [
        'field' => 'invoice_date',
        'label' => 'Invoice Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow MM/DD/YYYY format.'
        ]
    ],
    [
        'field' => 'terms',
        'label' => 'Terms',
        'rules' => 'required|trim|in_list[cash,check,credit]',
        'errors' => [
            'in_list' => 'The {field} field must contain either cash, check or credit only.'
        ]
    ],
    [
        'field' => 'remarks',
        'label' => 'Remarks',
        'rules' => 'trim'
    ],
    [
        'field' => 'due_date',
        'label' => 'Due Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow MM/DD/YYYY format.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Invoice
|--------------------------------------------------------------------------
 */

$config['invoices/edit'] = $config['invoices/add'];

/*
|--------------------------------------------------------------------------
| Add live sell customer
|--------------------------------------------------------------------------
 */

$config['live_sell_customers/add'] = [
    [
        'field' => 'name',
        'label' => 'Customer',
        'rules' => 'required|trim|max_length[255]|callback__is_customer',
        'errors' => [
            '_is_customer' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'address',
        'label' => 'Address',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'Phone No',
        'label' => 'phone_no',
        'rules' => 'trim|max_length[100]'
    ],
    [
        'field' => 'email',
        'label' => 'Email',
        'rules' => 'required|trim|max_length[255]|valid_email'
    ],
    [
        'field' => 'credit_limit',
        'label' => 'Credit Limit',
        'rules' => 'required|trim|max_length[255]'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit live sell customer
|--------------------------------------------------------------------------
 */

 $config['live_sell_customers/edit'] = $config['live_sell_customers/add'];

/*
|--------------------------------------------------------------------------
| Add live sell supplier
|--------------------------------------------------------------------------
 */

$config['live_sell_suppliers/add'] = [
    [
        'field' => 'name',
        'label' => 'Supplier',
        'rules' => 'required|trim|max_length[255]|callback__is_supplier',
        'errors' => [
            '_is_supplier' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'address',
        'label' => 'Address',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'Phone No',
        'label' => 'phone_no',
        'rules' => 'trim|max_length[100]'
    ],
    [
        'field' => 'email',
        'label' => 'Email',
        'rules' => 'required|trim|max_length[255]|valid_email'
    ]
];

$config['live_sell_suppliers/edit'] = $config['live_sell_suppliers/add'];

/*
|--------------------------------------------------------------------------
| Add live sell items
|--------------------------------------------------------------------------
 */

$config['live_sell_items/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'unit',
        'label' => 'unit',
        'rules' => 'required|trim'
    ],
];

/*
|--------------------------------------------------------------------------
| Add stock keeping unit
|--------------------------------------------------------------------------
 */
$config['Skus/add'] = [
    [
        'field' => 'customer_id',
        'label' => 'Customer',
        'rules' => 'required|integer'
    ]
];

$config['Skus/edit'] = [];

$config['stock_keeping_units/add'] = [
    [
        'field' => 'name',
        'label' => 'Stock Keeping Unit',
        'rules' => 'required|trim|max_length[255]|callback__is_sku',
        'errors' => [
            '_is_sku' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'size_range',
        'label' => 'Size Range',
        'rules' => 'trim|max_length[255]'
    ],
    [
        'field' => 'by_unit',
        'label' => 'By',
        'rules' => 'numeric|trim|max_length[255]|greater_than_equal_to[1]'
    ],
    [
        'field' => 'type',
        'label' => 'type',
        'rules' => 'trim|max_length[255]'
    ],
];

/*
|--------------------------------------------------------------------------
| Edit stock keeping unit
|--------------------------------------------------------------------------
 */

$config['stock_keeping_units/edit'] = $config['stock_keeping_units/add'];

/*
|--------------------------------------------------------------------------
| Add farm items
|--------------------------------------------------------------------------
 */

$config['farms/add'] = [
    [
        'field' => 'name',
        'label' => 'Farm',
        'rules' => 'required|trim|max_length[255]|callback__is_farm',
        'errors' => [
            '_is_farm' => '{field} already exists.'
        ]
    ],
];

/*
|--------------------------------------------------------------------------
| Edit farm items
|--------------------------------------------------------------------------
 */

 $config['farms/edit'] = $config['farms/add'];

/*
|--------------------------------------------------------------------------
| Add live sell items
|--------------------------------------------------------------------------
 */

$config['live_sell_items/add'] = [
    [
        'field' => 'name',
        'label' => 'Item',
        'rules' => 'required|trim|max_length[255]|callback__is_item',
        'errors' => [
            '_is_item' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'unit',
        'label' => 'unit',
        'rules' => 'required|trim'
    ],
];

/*
|--------------------------------------------------------------------------
| Add customer
|--------------------------------------------------------------------------
 */

$config['customers/add'] = [
    [
        'field' => 'name',
        'label' => 'Customer',
        'rules' => 'required|trim|max_length[255]|callback__is_customer',
        'errors' => [
            '_is_customer' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'address',
        'label' => 'Address',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'zip',
        'label' => 'Zip Code',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'company',
        'label' => 'Company Name',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'business_style',
        'label' => 'Business Style',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'Phone No',
        'label' => 'phone_no',
        'rules' => 'trim|max_length[100]'
    ],
    [
        'field' => 'email',
        'label' => 'Email',
        'rules' => 'required|trim|max_length[255]|valid_email'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit customer
|--------------------------------------------------------------------------
 */

$config['customers/edit'] = $config['customers/add'];

/*
|--------------------------------------------------------------------------
| Edit live sell item
|--------------------------------------------------------------------------
 */

$config['live_sell_items/edit'] = $config['live_sell_items/add'];

/*
|--------------------------------------------------------------------------
| Add item
|--------------------------------------------------------------------------
 */

$config['items/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'unit',
        'label' => 'Unit',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'price',
        'label' => 'price',
        'rules' => 'required|trim|is_numeric|greater_than_equal_to[0]'
    ],
    [
        'field' => 'account_type',
        'label' => 'Account Name',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Add schedule
|--------------------------------------------------------------------------
 */

$config['job_schedules/add'] = [
    [
        'field' => 'date',
        'label' => 'Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow MM/DD/YYYY format.'
        ]
    ],
    [
        'field' => 'time_start',
        'label' => 'Time Start',
        'rules' => 'required'
    ],
    [
        'field' => 'time_end',
        'label' => 'Time End',
        'rules' => 'required'
    ],
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'qty_birds',
        'label' => 'Quantity of Birds',
        'rules' => 'required|trim|is_numeric|greater_than_equal_to[0]'
    ],
    [
        'field' => 'area',
        'label' => 'Area',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'details',
        'label' => 'Details',
        'rules' => 'required|trim|max_length[250]'
    ],
];

/*
|--------------------------------------------------------------------------
| Edit schedule
|--------------------------------------------------------------------------
 */

$config['job_schedules/edit'] = $config['job_schedules/add'];


/*
|--------------------------------------------------------------------------
| Add schedule
|--------------------------------------------------------------------------
 */

$config['schedules/add'] = [
    [
        'field' => 'customer',
        'label' => 'Grower Farm Name / Place',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'waf_rs_no',
        'label' => 'WAF No./RS No.',
        'rules' => 'required|trim|max_length[100]'
    ],
    [
        'field' => 'eta_date',
        'label' => 'ETA Date & Time',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'service_type',
        'label' => 'Service Type',
        'rules' => 'required|trim|in_list[dressing_only,live_and_dressing]',
        'errors' => [
            'in_list' => 'The {field} field must contain either Dressing Only or Live and Dressing.'
        ]
    ],
];

/*
|--------------------------------------------------------------------------
| Edit schedule
|--------------------------------------------------------------------------
 */

$config['schedules/edit'] = $config['schedules/add'];


/*
|--------------------------------------------------------------------------
| Edit item
|--------------------------------------------------------------------------
 */

$config['items/edit'] = $config['items/add'];

/*
|--------------------------------------------------------------------------
| Add Payment
|--------------------------------------------------------------------------
 */
$config['payments/add'] = [
    [
        'field' => 'payment_date',
        'label' => 'Payment Date',
        'rules' => 'required|trim|is_date[m/d/Y]'
    ],
    [
        'field' => 'type',
        'label' => 'Payment Type',
        'rules' => 'required|trim|in_list[cash,check]',
        'errors' => [
            'in_list' => 'The {field} field must contain only either cash or check.'
        ]
    ],
    [
        'field' => 'customer',
        'label' => 'Customer',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Add so Payment
|--------------------------------------------------------------------------
 */
$config['so_payments/add'] = [
    [
        'field' => 'payment_date',
        'label' => 'Payment Date',
        'rules' => 'required|trim|is_date[m/d/Y]'
    ],
];

/*
|--------------------------------------------------------------------------
| Add SOA
|--------------------------------------------------------------------------
 */
$config['soas/add'] = [
    [
        'field' => 'customer',
        'label' => 'Customer',
        'rules' => 'required|trim|callback__is_customer',
        'errors' => [
            '_is_customer' => 'The {field} field has a non-existing customer.'
        ]
    ],
    // [
    //     'field' => 'from_date',
    //     'label' => 'From Date',
    //     'rules' => 'required|trim|is_date[m/d/Y]'
    // ],
    // [
    //     'field' => 'to_date',
    //     'label' => 'To Date',
    //     'rules' => 'required|trim|is_date[m/d/Y]'
    // ]
];

/*
|--------------------------------------------------------------------------
| Add Purchase Order
|--------------------------------------------------------------------------
 */

$config['purchases/add'] = [
    [
        'field' => 'supplier',
        'label' => 'Supplier',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'purchase_date',
        'label' => 'Purchase Order Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow MM/DD/YYYY format.'
        ]
    ],
    [
        'field' => 'plate_no',
        'label' => 'Plate No.',
        'rules' => 'trim|max_length[50]'
    ],
    [
        'field' => 'remarks',
        'label' => 'Remarks',
        'rules' => 'trim'
    ],
    [
        'field' => 'main_usage',
        'label' => 'Usage',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'sub_usage',
        'label' => 'Sub-Usage',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'requisitioner',
        'label' => 'Requisitioner',
        'rules' => 'required|trim|max_length[100]'
    ],
    [
        'field' => 'date_covered',
        'label' => 'Date Covered',
        'rules' => 'required|trim|max_length[50]'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Purchase Order
|--------------------------------------------------------------------------
 */

$config['purchases/edit'] = $config['purchases/add'];

/*
|--------------------------------------------------------------------------
| Add Purchase Order
|--------------------------------------------------------------------------
 */

$config['purchases/add_check_no'] = [
    [
        'field' => 'check_no',
        'label' => 'Check No',
        'rules' => 'required|trim|max_length[100]'
    ]
];

/*
|--------------------------------------------------------------------------
| Add supplier
|--------------------------------------------------------------------------
 */

$config['suppliers/add'] = [
    [
        'field' => 'name',
        'label' => 'Name of supplier',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'tin',
        'label' => 'TIN',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'address',
        'label' => 'Address',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'zip_code',
        'label' => 'Zip Code',
        'rules' => 'required|trim|integer'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit supplier
|--------------------------------------------------------------------------
 */

$config['suppliers/edit'] = $config['suppliers/add'];

/*
|--------------------------------------------------------------------------
| Add Receiving Receipt
|--------------------------------------------------------------------------
 */

$config['receives/add'] = [
    [
        'field' => 'receive_date',
        'label' => 'Date Received',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow mm/dd/yyyy format.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Receiving Receipt
|--------------------------------------------------------------------------
 */

$config['receives/edit'] = $config['receives/add'];

/*
|--------------------------------------------------------------------------
| Add Release
|--------------------------------------------------------------------------
 */

$config['releases/add'] = [
    [
        'field' => 'release_date',
        'label' => 'Date Release',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must follow mm/dd/yyyy format.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Add Usage
|--------------------------------------------------------------------------
 */

$config['usages/add'] = [
    [
        'field' => 'name',
        'label' => 'Usage',
        'rules' => 'required|trim|max_length[50]|callback__is_usage',
        'errors' => [
            '_is_usage' => '{field} already exists.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Usage
|--------------------------------------------------------------------------
 */

$config['usages/edit'] = $config['usages/add'];

/*
|--------------------------------------------------------------------------
| Add Sub-usage
|--------------------------------------------------------------------------
 */

$config['live_sell_usages/add'] = [
    [
        'field' => 'name',
        'label' => 'Usage',
        'rules' => 'required|trim|max_length[50]|callback__is_usage',
        'errors' => [
            '_is_usage' => '{field} already exists.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Usage
|--------------------------------------------------------------------------
 */

$config['live_sell_usages/edit'] = $config['live_sell_usages/add'];

/*
|--------------------------------------------------------------------------
| Add Sub-usage
|--------------------------------------------------------------------------
 */

$config['live_sell_sub_usages/add'] = [
    [
        'field' => 'name',
        'label' => 'Sub-Usage',
        'rules' => 'required|trim|max_length[50]|callback__is_sub_usage',
        'errors' => [
            '_is_sub_usage' => '{field} already exists.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Live Sell Sub-Usage
|--------------------------------------------------------------------------
 */

$config['live_sell_sub_usages/edit'] = $config['live_sell_sub_usages/add'];

/*
|--------------------------------------------------------------------------
| Add Sub-usage
|--------------------------------------------------------------------------
 */

$config['sub_usages/add'] = [
    [
        'field' => 'name',
        'label' => 'Sub-Usage',
        'rules' => 'required|trim|max_length[50]|callback__is_sub_usage',
        'errors' => [
            '_is_sub_usage' => '{field} already exists.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Sub-usage
|--------------------------------------------------------------------------
 */

$config['sub_usages/edit'] = $config['sub_usages/add'];

/*
|--------------------------------------------------------------------------
| Add check
|--------------------------------------------------------------------------
 */

$config['checks/add'] = [
    [
        'field' => 'bank',
        'label' => 'Bank',
        'rules' => 'required|trim|callback__is_bank',
        'errors' => [
            '_is_bank' => 'The {field} field must have an exisiting bank.'
        ]
    ],
    [
        'field' => 'check_date',
        'label' => 'Check Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => '{field} field must follow MM/DD/YYYY format.'
        ]
    ],
    [
        'field' => 'check_no',
        'label' => 'Check No.',
        'rules' => 'required|trim|callback__check_no_taken',
        'errors' => [
            '_check_no_taken' => 'Check No. is not available.'
        ]

    ],
    [
        'field' => 'supplier',
        'label' => 'Supplier',
        'rules' => 'required|trim|max_length[150]'
    ],
    [
        'field' => 'particulars',
        'label' => 'Particulars',
        'rules' => 'trim'
    ],
    [
        'field' => 'sig_1',
        'label' => 'Signatory 1',
        'rules' => 'trim'
    ],
    [
        'field' => 'sig_2',
        'label' => 'Signatory 2',
        'rules' => 'trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit check
|--------------------------------------------------------------------------
 */

$config['checks/edit'] = $config['checks/add'];

/*
|--------------------------------------------------------------------------
| Enroll checks from bank
|--------------------------------------------------------------------------
 */

$config['checks/enroll'] = [
    [
        'field' => 'from',
        'label' => 'From',
        'rules' => 'required|trim|is_numeric|greater_than_equal_to[0]'
    ],
    [
        'field' => 'to',
        'label' => 'To',
        'rules' => 'required|trim|is_numeric|greater_than_equal_to[' . (isset($_POST['from']) ? $_POST['from'] : 0) . ']'
    ]
];

/*
|--------------------------------------------------------------------------
| Add bank
|--------------------------------------------------------------------------
 */

$config['banks/add'] = [
    [
        'field' => 'name',
        'label' => 'Name of Bank',
        'rules' => 'required|trim|max_length[150]'
    ],
    [
        'field' => 'print_template',
        'label' => 'Print Template',
        'rules' => 'callback__is_check_template',
        'errors' => [
            '_is_check_template' => 'The {field} field contains an invalid print template.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Edit bank
|--------------------------------------------------------------------------
 */

$config['banks/edit'] = $config['banks/add'];

/*
|--------------------------------------------------------------------------
| Add Payees
|--------------------------------------------------------------------------
 */

$config['payees/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Payees
|--------------------------------------------------------------------------
 */

$config['payees/edit'] = $config['payees/add'];

/*
|--------------------------------------------------------------------------
| Add Signatory
|--------------------------------------------------------------------------
 */

$config['signatories/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Signatory
|--------------------------------------------------------------------------
 */

$config['signatories/edit'] = $config['signatories/add'];


/*
|--------------------------------------------------------------------------
| Add Invoice Item
|--------------------------------------------------------------------------
 */

$config['sales_items/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'unit',
        'label' => 'Unit',
        'rules' => 'required|trim|max_length[50]'
    ],
    [
        'field' => 'price',
        'label' => 'price',
        'rules' => 'required|trim|is_numeric|greater_than_equal_to[0]'
    ],
];


/*
|--------------------------------------------------------------------------
| Edit Invoice Item
|--------------------------------------------------------------------------
 */
$config['sales_items/edit'] = $config['sales_items/add'];




/*
|--------------------------------------------------------------------------
| Add Purchase Release
|--------------------------------------------------------------------------
 */

$config['purchases/release'] = [
    [
        'field' => 'or_no',
        'label' => 'OR Number',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'or_date',
        'label' => 'OR Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must contain a date in mm/dd/yyyy format.'
        ]
    ],
    [
        'field' => 'or_amount',
        'label' => 'Amount',
        'rules' => 'required|trim'
    ]
];

// /*
// |--------------------------------------------------------------------------
// | Add Purchase Order Release
// |--------------------------------------------------------------------------
//  */

 $config['purchase_orders/release'] = [
    [
        'field' => 'or_no',
        'label' => 'OR Number',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'or_date',
        'label' => 'OR Date',
        'rules' => 'required|trim|is_date[m/d/Y]',
        'errors' => [
            'is_date' => 'The {field} field must contain a date in mm/dd/yyyy format.'
        ]
    ],
    [
        'field' => 'or_amount',
        'label' => 'Amount',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Add Purchase Order Attachment
|--------------------------------------------------------------------------
 */

$config['purchases/add_attachment'] = [
    [
        'field' => 'purchase_id',
        'label' => 'PO ID',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Add Live Sell Purchase Order Attachment
|--------------------------------------------------------------------------
 */

 $config['purchase_orders/add_attachment'] = [
    [
        'field' => 'purchase_order_id',
        'label' => 'Purchase Order',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Add POS Invoice
|--------------------------------------------------------------------------
 */

$config['pos_invoices/add'] = [
    [
        'field' => 'pos_invoice',
        'label' => 'POS INVOICE',
        'rules' => 'trim'
    ]
];


/*
|--------------------------------------------------------------------------
| Add POS Invoice Payment
|--------------------------------------------------------------------------
 */

$config['pos_invoices/add_payment'] = [
    [
        'field' => 'amount_paid',
        'label' => 'Amount',
        'rules' => 'required|trim'
    ]
];


/*
|--------------------------------------------------------------------------
| Add POS Item
|--------------------------------------------------------------------------
 */

$config['pos_items/add'] = [
    [
        'field' => 'name',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'unit',
        'label' => 'Unit',
        'rules' => 'required|trim'
    ],    [
        'field' => 'price',
        'label' => 'Price',
        'rules' => 'required|trim'
    ]
];

$config['poc_items/add'] = [
    [
        'field' => 'item_name',
        'label' => 'Item Name',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'unit_price',
        'label' => 'Unit Price',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit POS Item
|--------------------------------------------------------------------------
 */

$config['pos_items/edit'] = $config['pos_items/add'];
$config['poc_items/edit'] = $config['poc_items/add'];

$config['account_types/add'] = [
    [
        'field' => 'applied_to',
        'label' => 'Applied to',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'account_name',
        'label' => 'Account Name',
        'rules' => 'required'
    ],
    [
        'field' => 'account_type',
        'label' => 'Account Type',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'financial_statement',
        'label' => 'Financial Statement',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'account_nature',
        'label' => 'Nature of Account',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'balance_sheet_category',
        'label' => 'Balance Sheet Category',
        'rules' => 'required|trim'
    ]
];

/*
|--------------------------------------------------------------------------
| Edit Account Type
|--------------------------------------------------------------------------
 */

$config['account_types/edit'] = $config['account_types/add'];

/*
|--------------------------------------------------------------------------
| Add Live Sell Account Types
|--------------------------------------------------------------------------
 */

 $config['live_sell_account_types/add'] = [
    [
        'field' => 'account_name',
        'label' => 'Account Name',
        'rules' => 'required|trim|max_length[255]|callback__is_account_type',
        'errors' => [
            '_is_account_type' => '{field} already exists.'
        ]
    ],
    [
        'field' => 'account_type',
        'label' => 'Account Type',
        'rules' => 'required|trim|max_length[255]'
    ],
];

/*
|--------------------------------------------------------------------------
| Edit Live Sell Account Types
|--------------------------------------------------------------------------
 */

 $config['live_sell_account_types/edit'] = $config['live_sell_account_types/add'];

 /*
|--------------------------------------------------------------------------
| Receive Purchase Order
|--------------------------------------------------------------------------
 */

$config['purchase_orders/receive_po'] = [
    [
        'field' => 'received_on',
        'label' => 'Date Received',
        'rules' => 'required|trim'
    ]
];

 /*
|--------------------------------------------------------------------------
| Add Cash Bond
|--------------------------------------------------------------------------
 */

$config['cash_bonds/add'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'cash_bond_date',
        'label' => 'CB Date',
        'rules' => 'required'
    ],
    [
        'field' => 'terms',
        'label' => 'Terms',
        'rules' => 'required',
        'errors' => [
            'required' => 'Please select a term.'
        ]
    ],
];

$config['cash_bonds/release'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
];


 /*
|--------------------------------------------------------------------------
| Add Payment Cash Bond
|--------------------------------------------------------------------------
 */

$config['cash_bonds/add_payment'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
];

 /*
|--------------------------------------------------------------------------
| Add POC
|--------------------------------------------------------------------------
 */

$config['poc/add'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
];

 /*
|--------------------------------------------------------------------------
| Add POC
|--------------------------------------------------------------------------
 */

$config['gate_pass/add'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
];

/*
|--------------------------------------------------------------------------
| Add 2307
|--------------------------------------------------------------------------
 */

$config['module_2307/add'] = [
    [
        'field' => 'supplier',
        'label' => 'Supplier',
        'rules' => 'required|trim'
    ],[
        'field' => 'period_from',
        'label' => 'Period From',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'period_to',
        'label' => 'Period To',
        'rules' => 'required|trim'
    ],
];

/*
|--------------------------------------------------------------------------
| Edit 2307
|--------------------------------------------------------------------------
 */

$config['module_2307/edit'] = $config['module_2307/add'];


 /*
|--------------------------------------------------------------------------
| Add Cash Bond
|--------------------------------------------------------------------------
 */

 $config['billing_statements/add'] = [
    [
        'field' => 'customer',
        'label' => 'Name',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'billing_date',
        'label' => 'Billing Date',
        'rules' => 'required'
    ],
];

/*
|--------------------------------------------------------------------------
| Add Hauling Log
|--------------------------------------------------------------------------
 */

$config['hauling_logs/add'] = [
    [
        'field' => 'schedule_id',
        'label' => 'Grower Farm Name/Place',
        'rules' => 'required|numeric'
    ],
    [
        'field' => 'schedule_list_id',
        'label' => 'Trip No.',
        'rules' => 'required|numeric'
    ],
   
    [
        'field' => 'truck_name',
        'label' => 'Truck Name',
        'rules' => 'required|trim|max_length[255]'
    ],
    [
        'field' => 'truck_plate',
        'label' => 'Truck Plate#',
        'rules' => 'required|trim|max_length[50]'
    ],
  
    [
        'field' => 'ata_time',
        'label' => 'ATA',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'weighed_time',
        'label' => 'WEIGHED',
        'rules' => 'required|trim'
    ],
    [
        'field' => 'remarks',
        'label' => 'Remarks on Arrival',
        'rules' => 'trim|max_length[500]'
    ],
    [
        'field' => 'gross_weight_display',
        'label' => 'Gross WG in Ticket',
        'rules' => 'required|trim',
        'errors' => [
            'required' => 'The {field} field is required.'
        ]
    ],
    [
        'field' => 'gross_weight',
        'label' => 'Gross WG in Ticket',
        'rules' => 'required|trim|numeric|greater_than_equal_to[0]',
        'errors' => [
            'numeric' => 'The {field} field must contain a valid number.',
            'greater_than' => 'The {field} field must be greater than 0.'
        ]
    ],
    [
        'field' => 'tare_weight_display',
        'label' => 'Tare WG in Ticket',
        'rules' => 'required|trim',
        'errors' => [
            'required' => 'The {field} field is required.'
        ]
    ],
    [
        'field' => 'tare_weight',
        'label' => 'Tare WG in Ticket',
        'rules' => 'required|trim|numeric|greater_than_equal_to[0]',
        'errors' => [
            'numeric' => 'The {field} field must contain a valid number.',
            'greater_than_equal_to' => 'The {field} field must be 0 or greater.'
        ]
    ]
];

/*
|--------------------------------------------------------------------------
| Add Bird Count
|--------------------------------------------------------------------------
 */

$config['bird_counts/add'] = [
    [
        'field' => 'grower_farm',
        'label' => 'Grower Farm Name/Place',
        'rules' => 'required|numeric',
    ],
    [
        'field' => 'hauling_log_id',
        'label' => 'Trip No.',
        'rules' => 'required|numeric',
    ],
    [
        'field' => 'live_heads',
        'label' => 'Live Heads',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
    ],
    [
        'field' => 'doa_heads',
        'label' => 'Dead on Arrival',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
    ],
    [
        'field' => 'daa_heads',
        'label' => 'Dead after Arrival',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
    ],
    [
        'field' => 'runts_heads',
        'label' => 'RUNTS',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
      
    ],
    [
        'field' => 'rejected_heads',
        'label' => 'REJECTED',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
    ],
    [
        'field' => 'shackle_heads',
        'label' => 'Shackle Count',
        'rules' => 'required|numeric|greater_than_equal_to[0]',
        'errors' => [
            'numeric' => 'The {field} field must contain a valid number.',
        ]
    ],
    [
        'field' => 'net_weight_display',
        'label' => 'Net Weight',
        'rules' => 'required|trim',
        'errors' => [
            'required' => 'The {field} field is required.'
        ]
    ],
    [
        'field' => 'alw',
        'label' => 'ALW',
        'rules' => 'numeric|greater_than_equal_to[0]',
        'errors' => [
            'numeric' => 'The {field} field must contain a valid number.',
        ]
    ]
];

