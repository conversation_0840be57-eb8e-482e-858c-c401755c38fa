$(function(){function t(t){if(!t)return"";try{var a=new Date(t);if(isNaN(a.getTime()))return t;var i={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"2-digit",hour12:!0};return a.toLocaleDateString("en-US",i)}catch(a){return t}}function a(){var t=$("#gross_weight_display").val().trim(),a=$("#tare_weight_display").val().trim(),i=parseFloat(t),e=parseFloat(a),o=!isNaN(i)&&""!==t,n=!isNaN(e)&&""!==a;$("#gross_weight").val(o?i:""),$("#tare_weight").val(n?e:"");var l=(o?i:0)-(n?e:0);$("#net_weight_display").val(l.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),$("#net_weight").val(l)}$("#schedule_id").on("change",function(){var t=$(this).val(),a=$("#schedule_list_id");a.prop("disabled",!0).html('<option value="">Loading trips...</option>'),$("#waf_rs_no").val(""),$("#eta_display").val(""),$("#eta").val(""),t?$.ajax({url:BASE_URI+"hauling_logs/get_trips_by_customer/"+t,type:"GET",dataType:"json",success:function(t){if(t.success&&t.trips.length>0){var i='<option value="">Select Trip No.</option>';$.each(t.trips,function(t,a){var e="Trip "+a.trip_no;a.formatted_date&&a.formatted_time&&(e+=" - "+a.formatted_date+" "+a.formatted_time),i+='<option value="'+a.id+'" data-waf="'+a.waf_rs_no+'" data-eta="'+a.eta+'">'+e+"</option>"}),a.html(i).prop("disabled",!1)}else a.html('<option value="">No trips available</option>')},error:function(){a.html('<option value="">Error loading trips</option>')}}):a.html('<option value="">Select Trip No.</option>')}),$("#schedule_list_id").on("change",function(){var a=$(this).find("option:selected");if(a.val()){var i=a.data("waf"),e=a.data("eta");$("#waf_rs_no").val(i),$("#eta_display").val(t(e)),$("#eta").val(e)}else $("#waf_rs_no").val(""),$("#eta_display").val(""),$("#eta").val("")}),$("#gross_weight_display, #tare_weight_display").on("input",function(){this.value=this.value.replace(/[^0-9.]/g,"");var t=this.value.split(".");t.length>2&&(this.value=t[0]+"."+t.slice(1).join(""))}),$("#gross_weight_display, #tare_weight_display").on("input",a)});