!function(e){"use strict";function t(){var t=e(a()),n=e(".item-table"),i=n.find(".trip_item");0===i.length&&n.find(".no-item").remove(),n.find("tbody").append(t),l()}function a(){var e=i();return'<tr class="trip_item"><td><input type="hidden" name="trip_item_new_'+e+'" value="'+e+'"><input type="number" name="trip_no_new_'+e+'" class="form-control-plaintext" value="" readonly></td><td><input type="time" name="eta_time_new_'+e+'" class="form-control" value=""></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function n(){return'<tr class="no-item"><td class="text-center" colspan="3">No trips added yet.</td></tr>'}function i(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",a=0;a<32;a++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function r(t){var a=e(t.currentTarget).closest(".trip_item"),i=a.find('input[name^="trip_item_"]:not([name*="new_"])');if(i.length>0){var r=i.val();e("<input>").attr({type:"hidden",name:"deleted_trip_"+r,value:r}).appendTo(a.closest("form"))}a.remove();var o=e(t.delegateTarget),s=o.find(".trip_item");s.length<1&&o.find("tbody").html(e(n())),l()}function l(){var t=e('select[name="customer"]').val(),a=e('input[name="eta_date"]').val(),n=e(".item-table"),i=n.find(".trip_item");if(t&&a){var r=-1!==e("title, .title").text().indexOf("Reschedule"),l=null;r=-1!==e("title, .title").text().indexOf("Reschedule");r&&(l=e('input[name="schedule_id"]').val()),e.ajax({url:BASE_URI+"schedules/get_next_trip_number",type:"POST",data:{customer_id:t,eta_date:a,exclude_schedule_id:l},dataType:"json",success:function(t){if(t.next_trip_no){var a=t.next_trip_no,n=0;i.each(function(t,i){var r=e(i).find('input[name^="trip_no_"]'),l=a+n;r.val(l),n++})}},error:function(){i.each(function(t,a){var n=e(a).find('input[name^="trip_no_"]');n.val(t+1)})}})}else i.each(function(t,a){var n=e(a).find('input[name^="trip_no_"]');n.val(t+1)})}function o(){var t=e('select[name="customer"]').val(),a=e('input[name="eta_date"]').val();t&&a?e.ajax({url:BASE_URI+"schedules/check_duplicate_schedule",type:"POST",data:{customer_id:t,eta_date:a},dataType:"json",success:function(t){if(e(".duplicate-schedule-error").remove(),t.is_duplicate){var a=e('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');e('input[name="eta_date"]').closest(".form-group").append(a),e('input[name="eta_date"]').addClass("is-invalid")}else e('input[name="eta_date"]').removeClass("is-invalid")}}):(e(".duplicate-schedule-error").remove(),e('input[name="eta_date"]').removeClass("is-invalid"))}function s(){var t=[],a=!1;e('.item-table input[name^="eta_time_"]').each(function(){e(this).val()&&t.push(e(this).val())});var n=[...new Set(t)];if(n.length!==t.length&&(a=!0),e(".duplicate-time-error").remove(),e('.item-table input[name^="eta_time_"]').removeClass("is-invalid"),a){var i=e('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');e(".action-add-item").closest(".form-group").append(i);var r={};t.forEach(function(e){r[e]=(r[e]||0)+1}),e('.item-table input[name^="eta_time_"]').each(function(){r[e(this).val()]>1&&e(this).addClass("is-invalid")})}}e(function(){var a=-1!==e("title, .title").text().indexOf("Reschedule"),n=(new Date).toISOString().split("T")[0],i=e('input[name="eta_date"]');if(i.length>0&&i.attr("min",n),i.on("focus",function(){e(this).attr("min",n)}),a||(e(".action-add-item").click(t),e('select[name="customer"], input[name="eta_date"]').on("change",function(){l()})),a||(e(".action-add-item").click(t),e(".item-table").on("click",".action-delete-item",r),e('select[name="customer"], input[name="eta_date"]').on("change",l)),a){var c=e('input[name="eta_date"]').val();e('input[name="eta_date"]').on("change input blur",function(){l()}),e('input[name="eta_date"]').on("changeDate",function(){l()}),setInterval(function(){var t=e('input[name="eta_date"]').val();t!==c&&(c=t,l())},500),c&&l()}else l();e('select[name="customer"], input[name="eta_date"]').on("change",o),e(".item-table").on("change input",'input[name^="eta_time_"]',s),e("form.crud-box").on("submit",function(t){var a=e('select[name="customer"]').val(),n=e('input[name="eta_date"]').val(),i=!1,r=[];if(e(".eta-time-error, .duplicate-time-error").remove(),e('.item-table input[name^="eta_time_"]').removeClass("is-invalid"),e('.item-table input[name^="eta_time_"]').each(function(){if(!e(this).val())return i=!0,!1;r.push(e(this).val())}),i){var l=e('<div class="eta-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">ETA Time is required for all trips.</div>');e(".action-add-item").closest(".form-group").append(l),t.preventDefault()}var o=[...new Set(r)];if(o.length!==r.length){!0;var s={};r.forEach(function(e){s[e]=(s[e]||0)+1}),e('.item-table input[name^="eta_time_"]').each(function(){s[e(this).val()]>1&&e(this).addClass("is-invalid")});l=e('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');e(".action-add-item").closest(".form-group").append(l),t.preventDefault()}if(a&&n){var c=!1;e.ajax({url:BASE_URI+"schedules/check_duplicate_schedule",type:"POST",data:{customer_id:a,eta_date:n},dataType:"json",async:!1,success:function(t){if(t.is_duplicate){c=!0;var a=e('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');e('input[name="eta_date"]').closest(".form-group").append(a)}}}),c&&t.preventDefault()}})})}(jQuery);