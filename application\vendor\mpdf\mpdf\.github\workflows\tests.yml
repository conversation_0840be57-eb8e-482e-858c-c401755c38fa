# https://help.github.com/en/categories/automating-your-workflow-with-github-actions

name: "C<PERSON>"

on:
  pull_request:
  push:
    branches:
      - "master"
      - "development"
      - "test"

jobs:

  tests:

    name: "Tests"

    runs-on: ${{ matrix.operating-system }}

    strategy:
      fail-fast: false
      matrix:
        php-version:
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"
        operating-system: [ubuntu-latest, windows-latest]

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          extensions: "mbstring, gd, bcmath, bz2"
          tools: composer:v2
          ini-values: error_reporting=-1

      - name: "Install dependencies"
        run: "composer install --no-interaction --no-progress"

      - name: "Tests"
        run: composer test