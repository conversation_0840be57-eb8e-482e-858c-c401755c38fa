<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<fieldset>
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer <i class="red-text medium">*</i></label>
						<select name="customer" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Invoices</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th width="10%" class="text-center">Invoice No</th>

									<th width="20%" class="text-center">Customer Info</th>

									<th width="10%" class="text-center">Invoice Date</th>

									<th width="10%" class="text-center">Terms</th>

									<th width="10%" class="text-center">Grand Total</th>

									<th width="10%" class="text-center">Total Paid</th>

									<th width="10%" class="text-center">Balance</th>

									<th width="10%" class="text-center">Payment</th>

									<th width="10%" class="text-center">Action</th>
								</tr>
							</thead>

							<tbody>
					<?php
						if ( ! empty($paid_invoice_ids)):
							foreach ($paid_invoice_ids as $paid_invoice_id):
					?>
								<tr class="paid_invoice">
									<td>
										<?php $fdn_paid_invoice = 'paid_invoice_'.$paid_invoice_id; ?>

										<input type="hidden" name="<?= $fdn_paid_invoice; ?>" value="<?= set_value($fdn_paid_invoice, $paid_invoice_id); ?>">

										<?php $fdn_invoice = 'invoice_'.$paid_invoice_id; ?>

										<select name="<?= $fdn_invoice; ?>" class="form-control dropsearch" data-placeholder="" >
											<option></option>
										<?php foreach ($invoices as $invoice): ?>
											<option value="<?= $invoice->id; ?>" <?= set_select($fdn_invoice, $invoice->id); ?>><?= $invoice->invoice_no; ?></option>
										<?php endforeach; ?>
										</select>

										<div class="red-text flash-message">
											<?= form_error($fdn_paid_invoice); ?>
											<?= form_error($fdn_invoice); ?>
										</div>
									</td>

									<td>
                                        <?php $fdn_customer = 'customer_'.$paid_invoice_id; ?>

                                        <input type="text" name="<?= $fdn_customer; ?>" class="form-control-plaintext" value="<?= set_value($fdn_customer); ?>" readonly="readonly">
									</td>

									<td>
                                        <?php $fdn_invoice_date = 'invoice_date_'.$paid_invoice_id; ?>

                                        <input type="text" name="<?= $fdn_invoice_date; ?>" class="form-control-plaintext" value="<?= set_value($fdn_invoice_date); ?>" readonly="readonly">
									</td>

									<td>
                                        <?php $fdn_terms = 'terms_'.$paid_invoice_id; ?>

                                        <input type="text" name="<?= $fdn_terms; ?>" class="form-control-plaintext" value="<?= set_value($fdn_terms); ?>" readonly="readonly">
									</td>

									<td>
                                        <?php $fdn_grand_total = 'grand_total_'.$paid_invoice_id; ?>

                                        <input type="number" name="<?= $fdn_grand_total; ?>" class="form-control-plaintext" value="<?= set_value($fdn_grand_total); ?>" step="any" readonly="readonly">
									</td>

									<td>
                                        <?php $fdn_total_paid = 'total_paid_'.$paid_invoice_id; ?>

                                        <input type="number" name="<?= $fdn_total_paid; ?>" class="form-control-plaintext" value="<?= set_value($fdn_total_paid); ?>" step="any" readonly="readonly">
									</td>

                                    <td>
										<?php $fdn_balance = 'balance_'.$paid_invoice_id; ?>

                                        <input type="number" name="<?= $fdn_balance; ?>" class="form-control-plaintext" value="<?= set_value($fdn_balance); ?>" step="any" readonly="readonly">
									</td>

                                    <td>
                                        <?php $fdn_amount = 'amount_'.$paid_invoice_id; ?>

										<input type="number" name="<?= $fdn_amount; ?>" class="form-control" value="<?= set_value($fdn_amount); ?>" step="any">

										<div class="red-text flash-message">
											<?= form_error($fdn_amount); ?>
										</div>
                                    </td>

									<td class="text-center">
										<button type="button" class="btn btn-sm btn-danger action-delete-dr">Delete</button>
									</td>
								</tr>
					<?php
							endforeach;
						else:
					?>
								<tr class="no-item">
									<td class="text-center" colspan="9">No Invoice added yet.</td>
								</tr>
					<?php
						endif;
					?>
							</tbody>

                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-right">Summary</th>

                                    <td>
                                        <input type="text" name="z_grand_total" class="form-control-plaintext text-right" value="" step="any" readonly="readonly">
                                    </td>

                                    <td>
                                        <input type="text" name="z_total_paid" class="form-control-plaintext text-right" value="" step="any" readonly="readonly">
                                    </td>

                                    <td>
                                        <input type="text" name="z_balance" class="form-control-plaintext text-right" value="" step="any" readonly="readonly">
                                    </td>

                                    <td>
                                        <input type="text" name="z_amount" class="form-control-plaintext text-right" value="" step="any" readonly="readonly">
                                    </td>

                                    <td></td>
                                </tr>
                            </tfoot>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
						<button type="button" class="btn btn-sm btn-dark action-add-dr">Add Invoice No</button>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>Add Payment</span></h5>

				<div class="row">
					<div class="col-md-3 form-group">
						<label for="name">Payment Date</label>

						<input type="text" name="payment_date" class="form-control" value="<?= set_value('payment_date', date('m/d/Y')); ?>" readonly="readonly">

						<div class="red-text flash-message">
							<?= form_error('payment_date'); ?>
						</div>
					</div>

					<div class="col-md-3 form-group">
						<label for="name">Payment Type</label>

						<div class="clearfix"></div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="type" value="cash" <?= set_radio('type', 'cash'); ?>>Cash
							</label>
						</div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="type" value="check" <?= set_radio('type', 'check'); ?>>Check
							</label>
						</div>

						<div class="clearfix"></div>

						<div class="red-text flash-message">
							<?= form_error('type'); ?>
						</div>
					</div>
				</div>

				<div class="row payment-type <?= set_radio('type', 'cash') ? '' : 'd-none'; ?>" id="cash">
					<div class="col-md-3 form-group">
						<label for="name">Cash Amount</label>

						<input type="number" name="cash_amount" class="form-control" step="any" value="<?= set_value('cash_amount'); ?>" step="any">

						<div class="red-text flash-message">
							<?= form_error('cash_amount'); ?>
						</div>
					</div>
				</div>

				<div class="row payment-type <?= set_radio('type', 'check') ? '' : 'd-none'; ?>" id="check">
					<div class="col-md-3 form-group">
						<label for="check_date">Check Date</label>

						<input type="text" name="check_date" class="form-control datepicker" value="<?= set_value('check_date'); ?>">

						<div class="red-text flash-message">
							<?= form_error('check_date'); ?>
						</div>
					</div>

					<div class="col-md-3 form-group">
						<label for="check_no">Check No.</label>

						<input type="text" name="check_no" class="form-control" value="<?= set_value('check_no'); ?>">

						<div class="red-text flash-message">
							<?= form_error('check_no'); ?>
						</div>
					</div>

					<div class="col-md-3 form-group">
						<label for="bank_name">Bank Name</label>

						<input type="text" name="bank_name" class="form-control" value="<?= set_value('bank_name'); ?>">

						<div class="red-text flash-message">
							<?= form_error('bank_name'); ?>
						</div>
					</div>

					<div class="col-md-3 form-group">
						<label for="check_amount">Check Amount</label>

						<input type="number" name="check_amount" class="form-control" value="<?= set_value('check_amount'); ?>"step="any" >

						<div class="red-text flash-message">
							<?= form_error('check_amount'); ?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('payments')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
