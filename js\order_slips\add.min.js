!function(t){"use strict";function e(){var e=t(a());n(e);var r=t(".item-table"),i=r.find(".order_slip_item");0===i.length&&r.find(".no-item").remove(),r.find("tbody").append(e),o()}function n(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:t(this).attr("data-placeholder"),width:"250px",ajax:{url:BASE_URI+"items/search_by_item",dataType:"json",type:"GET",data:function(t){var e={term:t.term};return e},processResults:function(e){return{results:t.map(e,function(t){return{text:t.name,id:t.id}})}}}})}function a(){var t=r();return'<tr class="order_slip_item"><td><input type="hidden" name="order_slip_item_'+t+'" value="'+t+'"><select name="item_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="pref_alw_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="current_price_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="est_due_amount_'+t+'" class="form-control" value="" min="0" step="any" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function r(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(){return'<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'}function o(){var e=t(".item-table"),n=e.find(".order_slip_item");n.each(function(e,n){t(n).find('input[name^="row"]').val(e+1)})}function l(t,e=null){t.find('input[name^="unit_"]').val(e)}function u(e){return t.ajax({url:BASE_URI+"items/unit_of/"+e,type:"GET",dataType:"json"})}function c(e){return t.ajax({url:BASE_URI+"customers/get_data/"+e,type:"GET",dataType:"json"})}function s(e){t(e.currentTarget).closest(".order_slip_item").remove();var n=t(e.delegateTarget),a=n.find(".order_slip_item");if(a.length<1){var r=t(i());n.find("tbody").html(r)}o()}function m(t){var e=parseFloat(t.find('input[name^="qty_"]').val());isNaN(e)&&(e=0);var n=t.find('input[name^="pref_alw_"]').val();isNaN(n)&&(n=0);var a=t.find('input[name^="current_price_"]').val();isNaN(a)&&(a=0);var r=e*n*a;t.find('input[name^="est_due_amount_"]').val(r)}t(function(){t(".action-add-item").click(e),t(".item-table").on("click",".action-delete-item",function(t){s(t)}).on("change",'select[name^="item"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item"),a=e.currentTarget.value;t.when(u(a)).then(function(t){l(n,t.unit)},function(){l(n,null)}),console.log(a)}).on("input",'input[name^="qty_"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item");m(n)}).on("input",'input[name^="pref_alw_"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item");m(n)}).on("input",'input[name^="current_price_"]',function(e){var n=t(e.currentTarget).closest(".order_slip_item");m(n)}),t('select[name="customer"]').on("change",function(e){t(e.currentTarget).closest(".order_slip_item");var n=e.currentTarget.value;t.when(c(n)).then(function(e){console.log(e),t('input[name="address"]').val(e.address),t('input[name="contact_number"]').val(e.contact_number),t('input[name="email"]').val(e.email),t('input[name="available_credit"]').val(e.available_credit),t('input[name="tin_number"]').val(e.tin_number)},function(){t('input[name="address"]').val(""),t('input[name="contact_number"]').val(""),t('input[name="email"]').val(""),t('input[name="available_credit"]').val(""),t('input[name="tin_number"]').val("")}),console.log(n)}),t(".order_slip_item").each(function(e,a){t(a).find("option").not(":selected").remove(),n(t(a))})})}(jQuery);