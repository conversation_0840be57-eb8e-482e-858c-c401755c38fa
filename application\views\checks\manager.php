<div class="col topbtn">
	<?php
        if ($_SESSION['user']->role_id !== ROLE_GENERAL_MANAGER):
    ?>
	<div class="float-right">
		<a href="<?=site_url('checks/add');?>" class="btn btn-primary">Issue a Check</a>
	</div>
	<?php
        endif;
    ?>
	<div class="title"><?=$title;?></div>
</div>


<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<select name="bank" class="mb-2 mr-sm-2 dropsearch form-control" data-placeholder="Bank">
			<option value="">All</option>
			<?php foreach ($banks as $bank): ?>
				<option value="<?=$bank->id?>"><?=$bank->name?></option>
			<?php endforeach;?>
		</select>

		<input type="text" name="payee" class="ml-sm-2 mr-sm-2 form-control go-suggest" data-suggest="suppliers" placeholder="Supplier">

		<input type="text" name="from" class="form-control datepicker mb-2 mr-sm-2" placeholder="From Date">

		<input type="text" name="to" class="form-control datepicker mb-2 mr-sm-2" placeholder="To Date">

		<select name="status" class="form-control ml-sm-2 mb-2 mr-sm-2 dropsearch" data-placeholder="Status">
			<option></option>
			<option value="pending" selected>Pending</option>
			<option value="approved">Approved</option>
			<option value="printed">Printed</option>
			<option value="deleted">Voided</option>
		</select>

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="dt-asc-wrapper sect-iii" data-display="checks">
	<table class="table table-hover tableA nowrap" data-package="checks">
		<thead>
			<tr >
				<th>Bank Name</th>
				<th>Check Date</th>
				<th>Check No</th>
				<th>Supplier</th>
				<th>Amount</th>
				<th></th>
			</tr>
		</thead>

		<tbody></tbody>
	</table>
</div>
