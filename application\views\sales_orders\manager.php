<div class="tab d-none">
    		<button type="button" class="tab-link <?=$status === 'pending' ? 'active' : '';?>" data-status="pending">Pending</button>
    		<button type="button" class="tab-link <?=$status === 'purchased' ? 'active' : '';?>" data-status="purchased">Purchased</button>
			<button type="button" class="tab-link <?=$status === 'received' ? 'active' : '';?>" data-status="received">Received</button>
	    	<button type="button" class="tab-link <?=$status === 'all' ? 'active' : '';?>" data-status="all">All</button>
</div>

<div class="col topbtn">
		<div class="dropdown float-right d-none">
			<a href="<?= site_url('sales_orders/add'); ?>" class="btn btn-primary">Add New Sales Order</a>
		</div>

	<div class="title"><?= $title; ?></div>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="sales_orders">
		<table class="table table-hover tableA" data-package="sales_orders">
			<thead>
				<tr>
					<th>Doc No.</th>
					<th>Order Slip No.</th>
					<th>Sales Order Date</th>
					<th>Customer</th>
					<th>Grandtotal</th>
					<th>Paid Amount</th>
					<th>Balance</th>
					<th>Remarks</th>
					<th>Prepared By</th>
					<!-- <th>Attachments</th> -->
					<!-- <th>Status</th> -->
					<th></th>
				</tr>
			</thead>

			<tbody></tbody>
		</table>
	</div>

<div class="modal fade" id="addPayment">
    <div class="modal-md modal-dialog">
        <div class="modal-content">
            <form action="<?=site_url('retails/payment');?>" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Add Payment</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <fieldset>
						<div class="row">
							<div class="col-md-12 form-group">
								<label for="payment_date" class="control-label">Payment Date</label>
								<input type="text" name="payment_date" id="paymentDate" class="form-control datepicker" value="<?=date('m/d/Y');?>">
							</div>

							<div class="col-md-12 form-group">
								<label for="payment_type" class="control-label">Payment Type</label>
								<div class="clearfix"></div>
								<?php foreach ($payment_types as $payment_type): ?>
									<div class="form-check-inline">
										<label class="form-check-label">
											<input type="radio" name="payment_type" class="form-check-input" value="<?=$payment_type;?>" <?=set_checkbox('payment_type', $payment_type, $payment_type === 'cash')?>><?=ucwords($payment_type);?>
										</label>
									</div>
								<?php endforeach;?>
							</div>
						</div>

						<div class="row payment-type">
							<div class="col-md-12 form-group">
								<label for="cash_amount">Paid Amount</label>
								<input type="text" name="amount" class="form-control">
							</div>
						</div>

						<div class="row payment-type d-none" id="check">
							<div class="col-md-12 form-group">
								<label for="check_date">Check Date</label>
								<input type="text" name="check_date" class="form-control datepicker" value="">
							</div>

							<div class="col-md-12 form-group">
								<label for="check_no">Check Number</label>
								<input type="text" name="check_no" class="form-control" value="">
							</div>

							<div class="col-md-12 form-group">
								<label for="bank_name">Bank Name</label>
								<input type="text" name="bank_name" class="form-control" value="">
							</div>
						</div>

						<div class="row payment-type d-none" id="others">
							<div class="col-md-12 form-group">
								<label for="payment_description" class="control-label">Payment Description</label>

								<input type="text" name="payment_description" class="form-control" value="">

								<div class="red-text flash-message">
									<?=form_error('payment_description');?>
								</div>
							</div>
						</div>
					</fieldset>

                    <div class="error mr-auto"></div>
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary  btn-process">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>

