(function(e){"use strict";function t(t){var a=e(t),r=a.find("thead th"),n=a.DataTable({dom:"Bfrtip",language:{infoFiltered:""},scrollX:!1,autoWidth:!1,responsive:!0,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:4},{className:"dt-action",targets:r.length-1}],buttons:["print"],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(t,a){e.when(r(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function r(t){return e.ajax({url:BASE_URI+"checks/delete/"+t,type:"POST",dataType:"json"})}function n(t,a){e.when(o(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function o(t){return e.ajax({url:BASE_URI+"checks/approve/"+t,type:"POST",dataType:"json"})}e(function(){var r=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var n=e(t.currentTarget).closest("tr.check").find("td"),o=void 0!==n[0]?n[2].innerHTML:"";alertify.confirm("Are you sure you want to delete Check No "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,r)}).setHeader("<em>Delete Check</em>")}),e(".tableA").on("click",".action-approve",function(t){var a=e(t.currentTarget).closest("tr.check").find("td"),o=void 0!==a[0]?a[2].innerHTML:"";alertify.confirm("Are you sure you want to approve Check No "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,r)}).setHeader("<em>Approve Check</em>")}),e("#tableFilter").submit(function(t){t.preventDefault();var a=e(t.currentTarget).serialize(),n=BASE_URI+"fetch/"+e(".tableA").attr("data-package")+"?"+e(t.currentTarget).serialize();r.ajax.url(n).load(),window.history.pushState(null,null,BASE_URI+"checks/manager?"+a)})})})(jQuery);