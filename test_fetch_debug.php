<?php
// Test what the fetch controller returns
$url = 'http://localhost/lavc/fetch/Skus';

// Simulate DataTables request
$postData = [
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search[value]' => '',
    'order[0][column]' => 0,
    'order[0][dir]' => 'asc'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response:\n";
echo $response;

// Try to decode and format
$data = json_decode($response, true);
if ($data) {
    echo "\n\nFormatted Response:\n";
    print_r($data);
    
    if (isset($data['data']) && !empty($data['data'])) {
        echo "\n\nFirst Row Data:\n";
        print_r($data['data'][0]);
    }
}
?>
