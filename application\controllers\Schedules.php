<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Schedules extends MYT_Controller
{

    protected $_error = '';
    protected $_error_details = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('schedule');
        $this->load->model('schedule_list');
        $this->load->model('customer');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Schedules';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedules/manager'
        ];
        $this->build_content('default', 'schedules/manager');
    }

    /**
     * Add schedule main
     */
    public function add()
    {
        $rules = $this->config->item('schedules/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('schedules'));
        } else {
            $this->title = 'Add Schedule';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/schedules/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $this->customer->select('', ['is_deleted' => 0], null, 'name ASC')
            ];
            $this->build_content('default', 'schedules/add', $data);
        }
    }

    /**
     * Edit/Reschedule trip
     */
    public function edit($trip_id)
    {
        // Get the trip first
        $trip_where = [
            'id' => $trip_id,
            'is_deleted' => 0
        ];
        $trip = $this->schedule_list->select('', $trip_where, 1) or show_404();

        // Get the parent schedule
        $schedule_where = [
            'id' => $trip->schedule_id,
            'is_deleted' => 0
        ];
        $schedule = $this->schedule->select('', $schedule_where, 1) or show_404();

        $rules = $this->config->item('schedules/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit_trip($schedule, $trip)) {
            redirect(site_url('schedules'));
        } else {
            $this->title = 'Reschedule';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/schedules/edit'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            // Get the specific trip data
            $trip_data = [$trip]; // Only the single trip being edited

            $data = [
                'form_error' => $form_error,
                'schedule' => $schedule,
                'customers' => $this->customer->select('', ['is_deleted' => 0], null, 'name ASC'),
                'trip_data' => $trip_data
            ];
            $this->build_content('default', 'schedules/edit', $data);
        }
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Check for duplicate schedules based on customer, date, and time
     */
    protected function _check_duplicate_schedule($customer_id, $eta_date, $exclude_id = null)
    {
        $eta_times = $this->collect_eta_times_from_form();
        
        // If no times provided, return false (no duplicates)
        if (empty($eta_times)) {
            return false;
        }
        
        $date_only = date('Y-m-d', strtotime($eta_date));
        
        // Check for existing schedules with the same customer, date, and any of the times
        foreach ($eta_times as $eta_time) {
            $existing_schedule = $this->schedule->check_duplicate_schedule($customer_id, $eta_date, $eta_time, $exclude_id);

            if ($existing_schedule) {
                return $existing_schedule;
            }
        }
        
        return false; // No duplicates found
    }

    /**
     * Check for duplicate trip times within a schedule
     */
    protected function _check_duplicate_trip_times($schedule_id = null)
    {
        $eta_times = $this->collect_eta_times_from_form();
        
        // Check for duplicates
        if (count($eta_times) > 1) {
            $unique_times = array_unique($eta_times);
            if (count($unique_times) !== count($eta_times)) {
                return true; // Duplicates found
            }
        }
        
        return false; // No duplicates
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');
        $service_type = $this->input->post('service_type');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Check for duplicate schedule
        $duplicate_schedule = $this->_check_duplicate_schedule($customer_id, $eta_date);
        if ($duplicate_schedule) {
            // Create descriptive error message with specific details
            $customer_name = $customer->name ?? 'Unknown Customer';
            $formatted_date = date('F j, Y', strtotime($eta_date));
            $formatted_time = $duplicate_schedule->eta_time ? date('g:i A', strtotime($duplicate_schedule->eta_time)) : 'Unknown Time';
            $waf_rs_no = $duplicate_schedule->waf_rs_no ?? 'Unknown WAF/RS No.';

            $this->_error = "A schedule already exists for {$customer_name} on {$formatted_date} at {$formatted_time} (WAF/RS No: {$waf_rs_no}). Please choose a different date or time.";
            return false;
        }

        // Check for duplicate trip times
        if ($this->_check_duplicate_trip_times()) {
            $this->_error = 'duplicate_trip_times';
            return false;
        }

        // Start database transaction
        $this->db->trans_start();

        // Prepare the schedule data
        $values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'service_type' => $service_type,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        // Insert the schedule
        if (!$schedule_id = $this->schedule->insert($values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Process trip data
        if (!$this->_process_trip_data($schedule_id)) {
            $this->db->trans_rollback();
            return false;
        }

        // Complete the transaction
        $this->db->trans_complete();

        // Check if transaction was successful
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'server_error';
            return false;
        }

        return $schedule_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($schedule)
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');
        $service_type = $this->input->post('service_type');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Check for duplicate schedule (excluding current schedule)
        $duplicate_schedule = $this->_check_duplicate_schedule($customer_id, $eta_date, $schedule->id);
        if ($duplicate_schedule) {
            // Create descriptive error message with specific details
            $customer_name = $customer->name ?? 'Unknown Customer';
            $formatted_date = date('F j, Y', strtotime($eta_date));
            $formatted_time = $duplicate_schedule->eta_time ? date('g:i A', strtotime($duplicate_schedule->eta_time)) : 'Unknown Time';
            $waf_rs_no = $duplicate_schedule->waf_rs_no ?? 'Unknown WAF/RS No.';

            $this->_error = "A schedule already exists for {$customer_name} on {$formatted_date} at {$formatted_time} (WAF/RS No: {$waf_rs_no}). Please choose a different date or time.";
            return false;
        }

        // Check for duplicate trip times
        if ($this->_check_duplicate_trip_times($schedule->id)) {
            $this->_error = 'duplicate_trip_times';
            return false;
        }

        $where = [
            'id' => $schedule->id,
            'is_deleted' => 0
        ];

        // Start database transaction
        $this->db->trans_start();

        // Prepare the schedule data
        $values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'service_type' => $service_type,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        // Update the schedule
        if (!$is_updated = $this->schedule->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Process trip data for updates
        if (!$this->_process_trip_data_edit($schedule->id)) {
            $this->db->trans_rollback();
            return false;
        }

        // Complete the transaction
        $this->db->trans_complete();

        // Check if transaction was successful
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'server_error';
            return false;
        }

        return $is_updated;
    }

    /**
     * Attempt Edit Trip (for individual trip reschedule)
     */
    protected function _attempt_edit_trip($schedule, $trip)
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');
        $service_type = $this->input->post('service_type');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Check for duplicate schedule (excluding current schedule)
        $duplicate_schedule = $this->_check_duplicate_schedule($customer_id, $eta_date, $schedule->id);
        if ($duplicate_schedule) {
            // Create descriptive error message with specific details
            $customer_name = $customer->name ?? 'Unknown Customer';
            $formatted_date = date('F j, Y', strtotime($eta_date));
            $formatted_time = $duplicate_schedule->eta_time ? date('g:i A', strtotime($duplicate_schedule->eta_time)) : 'Unknown Time';
            $waf_rs_no = $duplicate_schedule->waf_rs_no ?? 'Unknown WAF/RS No.';

            $this->_error = "A schedule already exists for {$customer_name} on {$formatted_date} at {$formatted_time} (WAF/RS No: {$waf_rs_no}). Please choose a different date or time.";
            return false;
        }

        // Check for duplicate trip times
        if ($this->_check_duplicate_trip_times($schedule->id)) {
            $this->_error = 'duplicate_trip_times';
            return false;
        }

        // Start database transaction
        $this->db->trans_start();

        // Update the parent schedule
        $schedule_where = [
            'id' => $schedule->id,
            'is_deleted' => 0
        ];

        $schedule_values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'service_type' => $service_type,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->schedule->update($schedule_where, $schedule_values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Update the specific trip
        $trip_no = $this->input->post('trip_no_' . $trip->id);
        $eta_time = $this->input->post('eta_time_' . $trip->id);

        if (!empty($eta_time)) {
            $trip_values = [
                'trip_no' => $trip_no,
                'eta_time' => $eta_time,
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => date('Y-m-d H:i:s')
            ];

            $trip_where = [
                'id' => $trip->id,
                'schedule_id' => $schedule->id
            ];

            if (!$this->schedule_list->update($trip_where, $trip_values)) {
                $this->db->trans_rollback();
                $this->_error = 'server_error';
                return false;
            }
        }

        // Complete the transaction
        $this->db->trans_complete();

        // Check if transaction was successful
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    /**
     * Process trip data for edit
     */
    protected function _process_trip_data_edit($schedule_id)
    {
        $post_data = $this->input->post();
        $existing_trip_ids = [];
        $updated_trips = [];

        // Process existing trips (update them)
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_') === 0) {
                $trip_id = $value;
                $existing_trip_ids[] = $trip_id;

                $trip_no = $this->input->post('trip_no_' . $trip_id);
                $eta_time = $this->input->post('eta_time_' . $trip_id);

                if (!empty($eta_time)) {
                    // Update existing trip
                    $trip_values = [
                        'trip_no' => $trip_no,
                        'eta_time' => $eta_time,
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => date('Y-m-d H:i:s')
                    ];

                    if (!$this->schedule_list->update(
                        ['id' => $trip_id, 'schedule_id' => $schedule_id],
                        $trip_values
                    )) {
                        $this->_error = 'Failed to update trip with ID: ' . $trip_id;
                        return false;
                    }
                }
            }
        }

        // Process new trips (those added via JavaScript)
        if (!$this->_process_new_trip_data($schedule_id)) {
            return false;
        }

        // Process deleted trips
        if (!$this->_process_deleted_trips($schedule_id)) {
            return false;
        }

        return true;
    }

    /**
     * Process new trip data (for trips added via JavaScript)
     */
    protected function _process_new_trip_data($schedule_id)
    {
        // Get schedule info to determine customer and date
        $schedule = $this->schedule->select('', ['id' => $schedule_id], 1);
        if (!$schedule) {
            return false;
        }

        $post_data = $this->input->post();
        $trip_counter = $this->_get_next_trip_number($schedule->customer_id, $schedule->eta_date);

        // Look for new trip data (these will have numeric keys from JavaScript)
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_new_') === 0) {
                $temp_id = str_replace('trip_item_new_', '', $key);
                $eta_time = $this->input->post('eta_time_new_' . $temp_id);

                if (!empty($eta_time)) {
                    $trip_values = [
                        'schedule_id' => $schedule_id,
                        'trip_no' => $trip_counter,
                        'eta_time' => $eta_time,
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => date('Y-m-d H:i:s')
                    ];

                    if (!$this->schedule_list->insert($trip_values)) {
                        $this->_error = 'Failed to insert new trip for schedule ID: ' . $schedule_id;
                        return false;
                    }
                    $trip_counter++; // Increment for next trip
                }
            }
        }

        return true;
    }

    /**
     * Process deleted trips
     */
    protected function _process_deleted_trips($schedule_id)
    {
        $post_data = $this->input->post();

        // Look for deleted trip data
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'deleted_trip_') === 0) {
                $trip_id = $value;

                // Soft delete the trip
                if (!$this->schedule_list->update(
                    ['id' => $trip_id, 'schedule_id' => $schedule_id],
                    [
                        'is_deleted' => 1,
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => date('Y-m-d H:i:s')
                    ]
                )) {
                    $this->_error = 'Failed to delete trip with ID: ' . $trip_id;
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Process trip data (for adding new schedules)
     */
    protected function _process_trip_data($schedule_id)
    {
        // Get schedule info to determine customer and date
        $schedule = $this->schedule->select('', ['id' => $schedule_id], 1);
        if (!$schedule) {
            return false;
        }

        $post_data = $this->input->post();
        $trip_counter = $this->_get_next_trip_number($schedule->customer_id, $schedule->eta_date);

        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_') === 0) {
                $trip_id = $value;
                $eta_time = $this->input->post('eta_time_' . $trip_id);

                if (!empty($eta_time)) {
                    $trip_values = [
                        'schedule_id' => $schedule_id,
                        'trip_no' => $trip_counter,
                        'eta_time' => $eta_time,
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => date('Y-m-d H:i:s')
                    ];

                    if (!$this->schedule_list->insert($trip_values)) {
                        $this->_error = 'Failed to insert trip for schedule ID: ' . $schedule_id;
                        return false;
                    }
                    $trip_counter++; // Increment for next trip
                }
            }
        }

        return true;
    }

    /**
     * Get next trip number for customer on specific date
     */
    protected function _get_next_trip_number($customer_id, $eta_date, $exclude_schedule_id = null)
    {
        return $this->schedule_list->get_next_trip_number($customer_id, $eta_date, $exclude_schedule_id);
    }

    /**
     * Delete Trip
     */
    public function delete($trip_id)
    {
        $where = [
            'id' => $trip_id,
            'is_deleted' => 0
        ];
        if (!$trip = $this->schedule_list->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Trip is not found.'
            ];
        } elseif (!$this->_attempt_delete_trip($trip)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Trip is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get next trip number for AJAX requests
     */
    public function get_next_trip_number()
    {
        $customer_id = $this->input->post('customer_id');
        $eta_date = $this->input->post('eta_date');
        $exclude_schedule_id = $this->input->post('exclude_schedule_id');



        if (empty($customer_id) || empty($eta_date)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing customer_id or eta_date']);
            return;
        }

        $next_trip_no = $this->_get_next_trip_number($customer_id, $eta_date, $exclude_schedule_id);



        echo json_encode(['next_trip_no' => $next_trip_no]);
    }

    /**
     * Check for duplicate schedule via AJAX
     */
    public function check_duplicate_schedule()
    {
        $customer_id = $this->input->post('customer_id');
        $eta_date = $this->input->post('eta_date');

        if (empty($customer_id) || empty($eta_date)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing customer_id or eta_date']);
            return;
        }

        $duplicate_schedule = $this->_check_duplicate_schedule($customer_id, $eta_date);
        $is_duplicate = !empty($duplicate_schedule);

        echo json_encode(['is_duplicate' => $is_duplicate]);
    }

    /**
     * Attempt Delete Trip
     */
    protected function _attempt_delete_trip($trip)
    {
        // Start database transaction
        $this->db->trans_start();

        $where = [
            'id' => $trip->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->schedule_list->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Complete the transaction
        $this->db->trans_complete();

        // Check if transaction was successful
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'server_error';
            return false;
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Schedule (Legacy - for entire schedule deletion)
     */
    protected function _attempt_delete($schedule)
    {
        // Start database transaction
        $this->db->trans_start();

        $where = [
            'id' => $schedule->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->schedule->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        }

        // Complete the transaction
        $this->db->trans_complete();

        // Check if transaction was successful
        if ($this->db->trans_status() === FALSE) {
            $this->_error = 'server_error';
            return false;
        }

        return $is_deleted;
    }
}
