!function(t){"use strict";function e(){var e=t(a());n(e);var i=t(".item-table"),r=i.find(".sales_order_item");0===r.length&&i.find(".no-item").remove(),i.find("tbody").append(e),o()}function n(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:t(this).attr("data-placeholder"),minimumInputLength:0,width:"280px",minimumResultsForSearch:10,ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(t){var e={term:t.term};return e},processResults:function(e){return{results:t.map(e,function(t){return{text:t.name,id:t.id}})}}}})}function a(){var t=i();return'<tr class="sales_order_item"><td><input type="hidden" name="sales_order_item_'+t+'" value="'+t+'"><select style="min-width:200px" width="200px" name="item_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="actual_live_weight_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+t+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="unit_price_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function i(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function r(){return'<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'}function o(){var e=t(".item-table"),n=e.find(".sales_order_item");n.each(function(e,n){t(n).find('input[name^="row"]').val(e+1)})}function l(t,e=null){t.find('input[name^="unit_"]').val(e)}function s(e){return t.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})}function u(e){t(e.currentTarget).closest(".sales_order_item").remove();var n=t(e.delegateTarget),a=n.find(".sales_order_item");if(a.length<1){var i=t(r());n.find("tbody").html(i)}o()}function c(t){var e=parseFloat(t.find('input[name^="actual_live_weight_"]').val());isNaN(e)&&(e=0);var n=t.find('input[name^="unit_price_"]').val();isNaN(n)&&(n=0);var a=e*n;t.find('input[name^="amount_"]').val(a)}t(function(){t(".action-add-item").click(e),t(".item-table").on("click",".action-delete-item",function(t){u(t)}).on("change",'select[name^="item"]',function(e){var n=t(e.currentTarget).closest(".sales_order_item"),a=e.currentTarget.value;t.when(s(a)).then(function(t){l(n,t.unit)},function(){l(n,null)}),console.log(a)}).on("input",'input[name^="actual_live_weight_"]',function(e){var n=t(e.currentTarget).closest(".sales_order_item");c(n)}).on("input",'input[name^="unit_price_"]',function(e){var n=t(e.currentTarget).closest(".sales_order_item");c(n)}),t(".sales_order_item").each(function(e,a){t(a).find("option").not(":selected").remove(),n(t(a))})})}(jQuery);