!function(e){"use strict";function r(r){var a=e(r),t=a.find("thead th"),n=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"text-right",targets:t.length-3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return n}function a(r){e.when(t(r)).then(function(){window.location.href=BASE_URI+"purchases/manager?status=for approval"},function(e){var r=JSON.parse(e.responseText);alertify.error(r.message)})}function t(r){return e.ajax({url:BASE_URI+"purchases/mark_for_approval/"+r,type:"POST",dataType:"json"})}e(function(){r(".tableA"),e(".action-for-approval-purchase").click(function(e){var r=e.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to recommend Purchase Order No. "+r+" for approval?",function(){a(r)}).setHeader("<em>Recommend Purchase Order for Approval</em>")})})}(jQuery);