!function(t){"use strict";function e(e,a){t.when(r(e,a)).then(function(t){window.location.href=BASE_URI+"purchases/manager?status=for release"})}function r(e,r){return t.ajax({url:BASE_URI+"purchases/approve_status/",type:"POST",dataType:"json",data:{purchase_id:e,status:r}})}t(function(){t("button.action-approve").click(function(t){var r=t.currentTarget.getAttribute("data-status");alertify.confirm("Are you sure you want to approve this Purchase Order for "+r+"?",function(){var a=t.currentTarget.getAttribute("data-id");e(a,r)}).setHeader("<em>Approve Purchase Order for "+r+"</em>")})})}(jQuery);