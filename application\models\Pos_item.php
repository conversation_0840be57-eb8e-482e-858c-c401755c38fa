<?php
class Pos_item extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'pos_item';
        parent::__construct();
    }

    /**
     * Create New Pos Item
     */
    public function generate($name, $unit, $price, $added_by)
    {
        $sql = <<<EOT
INSERT INTO pos_item(name, unit, price, added_by) VALUES(?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    unit = VALUES(unit),
    price = VALUES(price),
    is_deleted = 0
EOT;
        $binds = [$name, $unit, $price, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }

    public function get_by_id($pos_item_id, $customer_id = null)
    {
        $sql = <<<EOT
SELECT pos_item.*, IFNULL(customer_prices.customer_price, pos_item.price) AS price
FROM pos_item
LEFT JOIN customer_prices ON customer_prices.pos_item = pos_item.id AND customer_prices.is_deleted = 0
WHERE pos_item.id = ?
 AND pos_item.is_deleted = 0
EOT;
        $binds = [$pos_item_id];

        if($customer_id !== null){
        $sql .= <<<EOT

AND customer_prices.customer_id = ?
EOT;

        $binds[] = $customer_id;

        }

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */