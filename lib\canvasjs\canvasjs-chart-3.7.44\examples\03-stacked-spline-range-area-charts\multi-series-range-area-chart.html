<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	exportEnabled: true,
	animationEnabled: true,
	theme: "light2",
	title:{
		text: "Temperature Variation - Ladakh vs Chandigarh"
	},
	axisX: {
		title: "April 2017",
		valueFormatString: "DD MMM"
	},
	axisY: {
		suffix: " °C"
	},     
	toolTip: {
		shared: true
	},
	legend: {
		cursor: "pointer",
		horizontalAlign: "right",
		itemclick: toggleDataSeries
	},
	data: [{
		type: "rangeArea",
		showInLegend: true,
		name: "<PERSON><PERSON><PERSON>",
		markerSize: 0,
		yValueFormatString: "#0.## °C",
		dataPoints: [
			{ x: new Date(2017, 03, 01), y: [05, 21] },
			{ x: new Date(2017, 03, 02), y: [07, 15] },
			{ x: new Date(2017, 03, 03), y: [07, 18] },
			{ x: new Date(2017, 03, 04), y: [09, 16] },
			{ x: new Date(2017, 03, 05), y: [10, 17] },
			{ x: new Date(2017, 03, 06), y: [08, 13] },
			{ x: new Date(2017, 03, 07), y: [06, 10] },
			{ x: new Date(2017, 03, 08), y: [06, 15] },
			{ x: new Date(2017, 03, 09), y: [06, 20] },
			{ x: new Date(2017, 03, 10), y: [05, 21] },
			{ x: new Date(2017, 03, 11), y: [06, 21] },
			{ x: new Date(2017, 03, 12), y: [07, 14] },
			{ x: new Date(2017, 03, 13), y: [07, 17] },
			{ x: new Date(2017, 03, 14), y: [05, 20] },
			{ x: new Date(2017, 03, 15), y: [07, 18] },
			{ x: new Date(2017, 03, 16), y: [07, 15] },
			{ x: new Date(2017, 03, 17), y: [08, 15] },
			{ x: new Date(2017, 03, 18), y: [07, 13] },
			{ x: new Date(2017, 03, 19), y: [07, 13] },
			{ x: new Date(2017, 03, 20), y: [07, 18] },
			{ x: new Date(2017, 03, 21), y: [06, 20] },
			{ x: new Date(2017, 03, 22), y: [09, 23] },
			{ x: new Date(2017, 03, 23), y: [09, 24] },
			{ x: new Date(2017, 03, 24), y: [08, 23] },
			{ x: new Date(2017, 03, 25), y: [12, 24] },
			{ x: new Date(2017, 03, 26), y: [10, 21] },
			{ x: new Date(2017, 03, 27), y: [07, 24] },
			{ x: new Date(2017, 03, 28), y: [10, 27] },
			{ x: new Date(2017, 03, 29), y: [10, 26] },
			{ x: new Date(2017, 03, 30), y: [12, 27] } 
		]
	},
	{
		type: "rangeArea",
		showInLegend: true,
		name: "Chandigarh",
		markerSize: 0,
		yValueFormatString: "#0.## °C",
		dataPoints: [
			{ x: new Date(2017, 03, 01), y: [15, 31] },
			{ x: new Date(2017, 03, 02), y: [16, 30] },
			{ x: new Date(2017, 03, 03), y: [14, 30] },
			{ x: new Date(2017, 03, 04), y: [15, 30] },
			{ x: new Date(2017, 03, 05), y: [17, 33] },
			{ x: new Date(2017, 03, 06), y: [19, 35] },
			{ x: new Date(2017, 03, 07), y: [20, 30] },
			{ x: new Date(2017, 03, 08), y: [15, 31] },
			{ x: new Date(2017, 03, 09), y: [16, 32] },
			{ x: new Date(2017, 03, 10), y: [16, 33] },
			{ x: new Date(2017, 03, 11), y: [16, 35] },
			{ x: new Date(2017, 03, 12), y: [17, 36] },
			{ x: new Date(2017, 03, 13), y: [20, 32] },
			{ x: new Date(2017, 03, 14), y: [17, 35] },
			{ x: new Date(2017, 03, 15), y: [18, 36] },
			{ x: new Date(2017, 03, 16), y: [20, 34] },
			{ x: new Date(2017, 03, 17), y: [17, 30] },
			{ x: new Date(2017, 03, 18), y: [19, 29] },
			{ x: new Date(2017, 03, 19), y: [16, 32] },
			{ x: new Date(2017, 03, 20), y: [17, 33] },
			{ x: new Date(2017, 03, 21), y: [16, 35] },
			{ x: new Date(2017, 03, 22), y: [19, 36] },
			{ x: new Date(2017, 03, 23), y: [20, 36] },
			{ x: new Date(2017, 03, 24), y: [21, 37] },
			{ x: new Date(2017, 03, 25), y: [21, 38] },
			{ x: new Date(2017, 03, 26), y: [21, 39] },
			{ x: new Date(2017, 03, 27), y: [22, 39] },
			{ x: new Date(2017, 03, 28), y: [22, 39] },
			{ x: new Date(2017, 03, 29), y: [22, 41] },
			{ x: new Date(2017, 03, 30), y: [23, 42] }
		]
	}]
});
chart.render();

function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;
	}
	e.chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>