<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Customers_sale extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }
        $this->load->model('invoice_item');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch invoice Orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice';
        $primary_key = 'invoice.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_doc_no(),
            $this->_get_invoice_date(),
            $this->_get_customer_name(),
            $this->_get_doc_link(),
            $this->_get_remarks(),
            $this->_get_desc(),
            $this->_get_qty(),
            $this->_get_unit(),
            $this->_get_price(),
            $this->_get_amount(),
            $this->_get_terms(),
            $this->_get_discount(),
            $this->_get_total_due()
        ];

        $joins = <<<EOT
invoice
LEFT JOIN customer ON invoice.customer_id = customer.id
EOT;

        $where = <<<EOT
invoice.is_deleted = 0
        AND status="approved"
EOT;
        $from_date = date('Y-m-d', strtotime(urldecode($this->input->get('from', true))));
        $to_date = date('Y-m-d', strtotime(urldecode($this->input->get('to', true))));

//         if ($from_date != '1970-01-01' and $to_date != '1970-01-01') {
        //             $where .= <<<EOT

//     AND invoice.invoice_date BETWEEN "{$from_date}" AND "{$to_date}"
        // EOT;
        //         }

        $where .= <<<EOT

    AND invoice.invoice_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;

        $group_by = 'invoice.id';
        // exit();
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
            //SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'invoice.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'invoice';
            }
        ];
    }
    
    /**
     * Get class
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'invoice.invoice_no',
            'as' => 'invoice_no',
            'dt' => 'DT_InvoiceNo',
            'field' => 'invoice_no',
        ];
    }

    protected function _get_invoice_date()
    {
        return [
            'db' => 'invoice.invoice_date',
            'as' => 'invoice_date',
            'dt' => 0,
            'field' => 'invoice_date'
        ];
    }

    /**
     * Get Customer's name
     */
    protected function _get_customer_name()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer_name',
            'dt' => 1,
            'field' => 'customer_name'
        ];
    }

    /**
     * Get PO document number
     */
    protected function _get_doc_link()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'doc_no',
            'dt' => 2,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                $res = '<a href="../invoices/preview/' . $d . '" target="_blank" class="btn btn-link">' . $row['invoice_no'] . '</a>';
                return $res;
            }
        ];
    }

    protected function _get_remarks()
    {
        return [
            'db' => 'invoice.remarks',
            'dt' => 3,
            'field' => 'remarks'
        ];
    }

    /**
     * Get PO Date
     */
    protected function _get_desc()
    {
        return [
            'db' => 'DATE_FORMAT(invoice.invoice_date, "%b %d, %Y")',
            'as' => 'description',
            'dt' => 4,
            'field' => 'description',
            'formatter' => function ($d, $row) {
                $invoice_items = $this->invoice_item->get_by_invoice($row['id']);
                $res = '';
                foreach ($invoice_items as $invoice_item) {
                    $res .= '<li  style="white-space: nowrap;">' . $invoice_item->item . '</li>';
                }
                return $res;
            }
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_qty()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'qty',
            'dt' => 5,
            'field' => 'qty',
            'formatter' => function ($d, $row) {
                $invoice_items = $this->invoice_item->get_by_invoice($row['id']);
                $res = '';
                foreach ($invoice_items as $invoice_item) {
                    $res .= '<li  style="white-space: nowrap;">' . number_format($invoice_item->qty, 2) . '</li>';
                }
                return $res;
            }
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_unit()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'unit',
            'dt' => 6,
            'field' => 'unit',
            'formatter' => function ($d, $row) {
                $invoice_items = $this->invoice_item->get_by_invoice($row['id']);
                $res = '';
                foreach ($invoice_items as $invoice_item) {
                    $res .= '<li  style="white-space: nowrap;">' . $invoice_item->unit . '</li>';
                }
                return $res;
            }
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_price()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'price',
            'dt' => 7,
            'field' => 'price',
            'formatter' => function ($d, $row) {
                $invoice_items = $this->invoice_item->get_by_invoice($row['id']);
                $res = '';
                foreach ($invoice_items as $invoice_item) {
                    $res .= '<li  style="white-space: nowrap;">' . number_format($invoice_item->price, 2) . '</li>';
                }
                return $res;
            }
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'invoice.id',
            'as' => 'amount',
            'dt' => 8,
            'field' => 'amount',
            'formatter' => function ($d, $row) {
                $invoice_items = $this->invoice_item->get_by_invoice($row['id']);
                $res = '';
                foreach ($invoice_items as $invoice_item) {
                    $res .= '<li  style="white-space: nowrap;">' . number_format($invoice_item->amount, 2) . '</li>';
                }
                return $res;
            }
        ];
    }

    protected function _get_terms()
    {
        return [
            'db' => 'invoice.terms',
            'as' => 'terms',
            'dt' => 9,
            'field' => 'terms'
        ];
    }

    protected function _get_discount()
    {
        return [
            'db' => 'invoice.discount',
            'as' => 'discount',
            'dt' => 10,
            'field' => 'discount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    protected function _get_total_due()
    {
        return [
            'db' => 'invoice.total',
            'as' => 'total',
            'dt' => 11,
            'field' => 'total',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }
}
