<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Billing_statements extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Billing_Statements
     */
    public function index(){
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];

        $table = 'billing_statement';
        $primary_key = 'billing_statement.id';
    
        $columns = [
            $this->_get_status(),
            $this->_get_id(),
            $this->_get_customer(),
            $this->_get_billing_date(),
            $this->_get_attachment(),
            $this->_get_grand_total(),
            $this->_get_remarks(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
billing_statement
LEFT JOIN customer ON customer.id = billing_statement.customer_id
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'active';

        // var_dump($status);die();

        switch($status){
            case 'active':
                $where = <<<EOT
billing_statement.status = "active"
AND billing_statement.is_deleted = 0
EOT;
                break;

            case 'cancelled':
                $where = <<<EOT
billing_statement.status = "cancelled"
AND billing_statement.is_deleted = 0
EOT;
                break;
            default:
                break;
        }



        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }


    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'billing_statement.status',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

     /**
     * Get billing_statement_id
     */
    protected function _get_id()
    {
        return [
            'db' => 'billing_statement.id',
            'dt' => 0,
            'field' => 'id',
            'formatter' => function ($d, $row) {
                $res = '<a href="../billing_statements/preview/' . $d . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                return $res;
            }
        ];
    }
     /**
     * Get customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'
        ];
    }
     /**
     * Get Billing_date
     */
    protected function _get_billing_date()
    {
        return [
            'db' => 'DATE_FORMAT(billing_statement.billing_date, "%b %d, %Y")',
            'as' => 'billing_date',
            'dt' => 2,
            'field' => 'billing_date'
        ];
    }
    /**
     * Get Attachments
     */
    protected function _get_attachment()
    {
        $CI = &get_instance();
        return [
            'db' => 'billing_statement.id',
            'as' => 'file',
            'dt' => 3,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('billing_statement_attachment');
                $files = $CI->billing_statement_attachment->get_by_id($d);
                $res = '';

                foreach($files as $i => $file){
                    $res .= '<a href="'.base_url('assets/asylum/billing_statements/'.$d.'/'.$file->file_name).'" target="_blank">Attachment '.++$i.'</a><br>';
                }

                return $res;

            }
        ];
    }
    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'billing_statement.remarks',
            'as' => 'remarks',
            'dt' => 4,
            'field' => 'remarks'
        ];
    }

     /**
     * Get total
     */
    protected function _get_grand_total()
    {
        return [
            'db' => 'FORMAT(billing_statement.grand_total, 2)',
            'as' => 'grand_total',
            'dt' => 5,
            'field' => 'grand_total'
        ];
    }
     /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'billing_statement.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                
                // Always show the Print Preview option
                $res .= anchor('billing_statements/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN])){
                    switch($row['status']){
                        case 'active' :
                            $res .= '<button class="action-cancel dropdown-item text-left red-text" role="button" data-id="' . $d . '">Cancel</button>';
                            break;
                    }
                }
                
                
                $res .= '</div>'; // Close dropdown menu
                $res .= '</div>'; // Close dropdown
    
                return $res;
            }
        ];
    }

}
