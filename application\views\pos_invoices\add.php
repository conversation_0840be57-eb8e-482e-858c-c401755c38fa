<div class="col topbtn no-print ">
    <div class="title"><?=$title;?></div>
</div>


<div class="container-fluid">
    <div class='row'>
        <div class="col-6">
            <div class="row">
                <div class="col-8"></div>
                <input type="text" name="search" class="form-control ml-sm-2 mb-2 mr-sm-2 col-3" value="" placeholder="Search">
                <!-- <div class="h3 ml-4">Item List</div> -->
                <div class="col-xs-11 col-md-11 col-sm-11 gray mx-3 mb-2 p-0 text-left">
                    <div class="card text-white bg-dark m-0 p-0 text-left">
                        <div class="card-body row text-left ml-3">
                            <h4 class="card-title col-6 text-left" style="text-align:center;">Name</h4>
                            <h4 class="card-title col-3 text-left" style="text-align:center;">Unit</h4>
                            <h4 class="card-title col-3 text-left" style="text-align:center;">Price</h4>
                        </div>
                    </div>
                </div>
                <?php
                foreach ($pos_items as $item): ?>
                    <div class="col-xs-11 col-md-11 col-sm-11 gray mx-3 mb-2 p-0 text-left">
                        <div class="card bg-light pos-item m-0 p-0 text-left" style="cursor: pointer;" data-id="<?=$item->id?>" data-name="<?=$item->name;?>" data-unit="<?=$item->unit;?>" data-price="<?=$item->price?>">
                            <div class="card-body row text-left ml-3 pt-3 pb-0">
                                <h5 class="card-title col-6 text-left" style="text-align:center;"><?=$item->name;?></h5>
                                <h5 class="card-title col-3 text-left" style="text-align:center;"><?=$item->unit;?></h5>
                                <h5 class="card-title col-3 text-left" style="text-align:center;"><?=$item->price;?></h5>
                            </div>
                        </div>
                    </div>
                <?php endforeach;?>
            </div>
        </div>

        <div class="col-6">
        <form method="post" role="form" accept-charset="utf-8" id="orderForm">
            <div class="card" >
                <div class="card-body">
                    <h3 class="card-title" style="text-align:center; font-weight:bold">Order Summary</h5>
                    <div class=order-items>
                        <table class="table table-bordered noWrap" id="table-item">
                            <thead>
                                <tr>
                                <td class="text-center" width="30%"><b>Items</b></td>
                                <td class="text-center" width="40%"><b>Qty</b></td>
                                <td class="text-right" width="20%"><b>Amount</b></td>
                                </tr>
                            </thead>
                            <tbody>

                                <!-- items here -->
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="2" class="text-right"><b>Sub Total</b></td>
                                <td>
                                <input type="text" name="subtotal" id="subtotal" class="form-control-plaintext subtotal text-right" readonly>
                                <input type="hidden" name="sub_total" id="sub_total">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" class="text-right"><b>Grand Total</b></td>
                                <td>
                                <input type="text" name="grandtotal" id="grandtotal" class="form-control-plaintext grandtotal text-right" readonly>
                                <input type="hidden" name="grand_total" id="grand_total">
                                </td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Payment Modal -->
                    <div class="modal fade" id="paymentModal"  role="dialog" aria-labelledby="paymentModal" aria-hidden="true">
                        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLongTitle">Payment Information</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <table class="table">
                                    <tbody>
                                        <tr>
                                            <td><b>Total</b></td>
                                            <td>
                                            <input type="text" name="total_payable" id="total_payable" class="form-control-plaintext total_payable text-right font-weight-bold" readonly></td>
                                        </tr>
                                        <tr>
                                            <td colspan='2'>
                                                <form id="payment-type-form">
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="payment_type" id="cash" value="cash">
                                                        <label class="form-check-label" for="cash">Cash</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="payment_type" id="credit" value="credit">
                                                        <label class="form-check-label" for="credit">Credit</label>
                                                    </div>
                                                </form>
                                            </td>
                                        </tr>
                                        <tr class="amount-paid d-none">
                                            <td><b>Amount Paid</b></td>
                                            <td><input type="number" name="amount_paid" id="amount_paid" class="form-control amount_paid text-right" step="any"></td>
                                        </tr>

                                        <tr class="amount-paid d-none">
                                            <td><b>Change</b></td>
                                            <td><input type="text" name="change" class="form-control-plaintext text-right" readonly></td>
                                        </tr>

                                        <tr class="credit-limit d-none">
                                            <td><b>Credit Limit</b></td>
                                            <td><input type="text" name="limit" class="form-control-plaintext text-right" readonly></td>
                                        </tr>

                                        <tr class="credit-limit d-none">
                                            <td><b>Customer</b></td>
                                            <td>
                                                <select style="width:100%" name="customer" class="form-control dropsearch" data-placeholder="Select Customer">
                                                <option></option>
                                                <?php foreach ($customers as $customer): ?>
                                                   <option value="<?=$customer->id?>" <?=set_select('customer', $customer->id)?> data-credit-limit="<?=$customer->available_credit ? $customer->available_credit : 0 ?>"><?=$customer->name?></option>
                                                <?php endforeach;?>
                                                </select>
                                            </td>
                                        </tr>

                                        <tr class="amount-paid d-none">
                                            <td><b>Customer</b></td>
                                            <td>
                                                <input type="text" name="customer_name" class="form-control" value="<?=set_value('customer_name');?>">
                                            </td>
                                        </tr>

                                        <tr class="remarks">
                                            <td><b>Remarks</b></td>
                                            <td><textarea name="remarks" class="form-control"></textarea></td>
                                        </tr>

                                    </tbody>
                                    </table>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" name="save" class="btn btn-primary action-save">Save</button>
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                        <input name="pay" value="Pay" class="btn btn-primary action-pay w-100" data-toggle="modal" style="width:50%; align-content:center" readonly="true">

                <div class="error">
                    <?=$form_error;?>
                </div>

                </div>
            </div>
        </div>
        </form>
    </div>
</div>

