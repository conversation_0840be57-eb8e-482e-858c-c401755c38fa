<div class="col-md-12 sect-jr">
		<div class="receipt" data-tag="<?=$billing_statement->id;?>">
 			<div class="receipt-header">
				<div class="clearfix">
					<strong class="pull pull-right">Billing Statement</strong>
				</div>

				<h4><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></h4>

				<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

				<div class="row sect-jr">
					<div class="col-sm-6">
						<p class="text-left">Name : <?=strtoupper($customer->name);?></p>
						<p class="text-left">Address :  <?=$customer->address;?></p>
						<p class="text-left">TIN : <?=$customer->tin_number;?> </p>
						<p class="text-left">Business Style : <?=$customer->business_style;?> </p>
					</div>

					<div class="col-sm-2 offset-sm-4">
						<p class="text-left">Billing ID :  <?=$billing_statement->id;?></p>
						<p class="text-left">Date :  <?=date('n/j/Y', strtotime($billing_statement->billing_date));?></p>
					</div>
				</div>
			</div>

			<div class="clearfix"></div>

			<table class="receipt-body">
				<thead>
					<tr>
						<th width="25%">Item No.</th>
						<th width="50%">Invoice No.</th>
						<th width="25%" class="text-right">Total</th>
					</tr>
				</thead>

				<tbody>
				<?php 
					$item_no = 0;
					foreach ($billing_statement_items as $billing_statement_item): 
						$item_no ++;
					?>
						<tr class="item">
							<td><?= $item_no ?></td>
							<td><?=$billing_statement_item->invoice_no;?></td>
							<td class="text-right"><?=number_format($billing_statement_item->total, 2);?></td>
						</tr>
					<?php endforeach;?>
				</tbody>
				<tfoot>
					<tr class="item">
						<td colspan="1">
							<?php if (trim($billing_statement->remarks) !== ''): ?>
								<div class="row">
									<div class="col-md-12">
										<p class="text-justify"><strong>Remarks: </strong><?= nl2br($billing_statement->remarks); ?></p>
									</div>
								</div>
							<?php endif; ?>
						</td>
						<td colspan="1" class="text-right"><strong>Grand Total</strong></td>
						<td class="text-right"><?= number_format($billing_statement->grand_total, 2); ?></td>
					</tr>
				</tfoot>

			</table>

			<br>
			<br>
			<br>

			<div class="row">
				<div class="col-3 text-center mt-auto" style="margin-top: 30px;">
				</div>

				<div class="col-3 text-center mt-auto" style="margin-top: 30px;">
				</div>

				<div class="col-3 text-center mt-auto" style="margin-top: 30px;">
				</div>

				<div class="col-3 text-center mt-auto" style="margin-top: 30px;">
					<?php if (!empty($billing_statement->prep_sigature)): ?>
						<img src="<?=base_url('assets/asylum/signatures/' . $billing_statement->added_by . '/' . $billing_statement->prep_sigature);?>" class="img-responsive" style="height: 100px;">
					<?php endif;?>
					<br>
					<?=$billing_statement->prep;?>
					<hr class="signature-line" style="margin: 5px">
					<p>Prepared by</p>
				</div>
			</div>
		</div>

		<div class="clearfix text-center">
            <button type="button" class="btn btn-primary action-print"><i class="fa fa-print"></i> Print</button>
            <a class="btn btn-light" href="<?= site_url('billing_statements/manager'); ?>">Close</a>
        </div>
	</div>