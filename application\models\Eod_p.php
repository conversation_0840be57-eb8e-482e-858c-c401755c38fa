<?php
class Eod_p extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_p';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_id($eodp_id)
    {
        $sql = <<<EOT
SELECT 
    eod_p.*,
    generator.full_name AS generator_name
FROM eod_p
LEFT JOIN user AS generator ON generator.id = eod_p.added_by
WHERE eod_p.is_deleted = 0
    AND eod_p.id = ?
EOT;
        $binds = [$eodp_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */