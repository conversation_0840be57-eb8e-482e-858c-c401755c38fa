+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		
		console.log(BASE_URI + 'fetch/' + $table.attr('data-package'));

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function cancelAndReload(billing_statement_id, dataTable) {
		$.when(cancelRow(billing_statement_id)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function cancelRow(billing_statement_id) {
		return $.ajax({
			url: BASE_URI + 'billing_statements/cancel/' + billing_statement_id,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-cancel', function (event) {
				var row = $(event.currentTarget).closest('tr');
				var cols = row.find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';
				alertify.confirm(
					'Are you sure you want to cancel Billing Statement No.' + docNo + '?',
					function () {
						var billing_statement_id = event.currentTarget.getAttribute('data-id');
						cancelAndReload(billing_statement_id, dataTable);
					}
				).setHeader('<em>Cancel Billing Statement</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/billing_statements?status=' + event.currentTarget.getAttribute('data-status');
			dataTable.ajax.url(url).load();
		});
	});
}(jQuery);
