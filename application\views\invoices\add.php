<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Name</label>

						<select id = "nameSelect" class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach($customers as $customer):?>
								<option value="<?=$customer->id?>" data-walkin="<?=$customer->is_walkin?>" <?=set_select('customer', $customer->id);?>><?=ucwords($customer->name)?></option>
							<?php endforeach;?>
						</select>
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group walkin-customer d-none">
						<label for="walkin_customer" class="control-label">Customer Name</label>

						<input type="text" name="walkin_customer" class="form-control go-suggest" data-suggest="walkin_customer" value="<?=set_value('walkin_customer');?>">

						<div class="red-text flash-message">
							<?=form_error('walkin_customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address <small class="font-italic">(Optional)</small></label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>" >

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no" class="control-label">Phone No. <small class="font-italic">(Optional)</small></label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no');?>">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email <small class="font-italic">(Optional)</small></label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group credit-terms d-none">
						<label for="available_credit" class="control-label">Available Credit</label>

						<input type="text" name="available_credit" class="form-control" value="<?=set_value('available_credit');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('available_credit');?>
						</div>
					</div>

					<div class="col-md-4 form-group invoice-history d-none">
						<label for="history" class="control-label"></label>

						<p><a href="#" class="history" target="_blank">View Invoice History</a></p>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>Invoice Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="invoice_date" class="control-label">Invoice Date</label>

						<input type="text" name="invoice_date" class="form-control datepicker" value="<?=set_value('invoice_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('invoice_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="terms" class="control-label">Terms</label>

						<div class="clearfix"></div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="terms" value="cash" <?=set_radio('terms', 'cash')?>>Cash
							</label>
						</div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="terms" value="check" <?=set_radio('terms', 'check')?>>Check
							</label>
						</div>

						<div class="form-check-inline credit-terms d-none">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="terms" value="credit" <?=set_radio('terms', 'credit')?>>Credit
							</label>
						</div>

						<div class="red-text flash-message">
							<?=form_error('terms');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="due_date" class="control-label">Due Date</label>

						<input type="text" name="due_date" class="form-control datepicker" value="<?=set_value('due_date');?>">

						<div class="red-text flash-message">
							<?=form_error('due_date');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="attachments" class="control-label">Upload Attachments</label>

						<input type="file" name="attachments[]" class="form-control-file" accept=".pdf, image/*"  multiple="">

						<div class="red-text flash-message">
							<?=form_error('attachments');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Invoice Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 5%;">Item</th>

									<th class="text-center" style="width: 15%;">Qty</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Description</th>

									<th class="text-center" style="width: 10%;">Unit Price</th>

									<th class="text-center" style="width: 10%;">Line Total</th>

									<th class="text-center" style="width: 10%;">RS/SO</th>

									<th class="text-center" style="width: 10%;">ALW</th>

									<th class="text-center" style="width: 10%;">Account Name</th>

									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($invoice_item_ids)):
									foreach ($invoice_item_ids as $i => $invoice_item_id):
                            ?>
										<tr class="invoice_item">
											<td>
												<?php $fn_invoice_item = 'invoice_item_' . $invoice_item_id;?>
												<input type="hidden" name="<?=$fn_invoice_item;?>" value="<?=set_value($fn_invoice_item, $invoice_item_id);?>">

												<?php $fn_row = 'row_' . $invoice_item_id;?>
												<input type="number" name="<?=$fn_row;?>" class="form-control-plaintext" value="<?=$i + 1;?>" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_invoice_item);?>
												</div>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $invoice_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $invoice_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit);?>" data-suggest="units">

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_item = 'item_' . $invoice_item_id;?>

												<select name="<?=$fn_item;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
												<?php foreach($items as $item):?>
													<option value="<?= $item->id; ?>" <?=set_select($fn_item, $item->id); ?>><?= $item->name; ?></option>
												<?php endforeach;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_item);?>
												</div>
											</td>

											<td>
												<?php $fn_price = 'price_' . $invoice_item_id;?>
												<input type="number" name="<?=$fn_price;?>" class="form-control" value="<?=set_value($fn_price);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $invoice_item_id;?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control-plaintext" value="<?=set_value($fn_amount);?>" readonly="readonly" step="any">
											</td>

											<td>
												<?php $fn_rs = 'rs_' . $invoice_item_id;?>
												<input type="text" name="<?=$fn_rs;?>" class="form-control" value="<?=set_value($fn_rs);?>">
											</td>

											<td>
												<?php $fn_alw = 'alw_' . $invoice_item_id;?>
												<input type="number" name="<?=$fn_alw;?>" class="form-control" value="<?=set_value($fn_alw);?>" step="any">
											</td>

											<td>
												<?php $fn_account_type = 'account_type_' . $invoice_item_id;?>

												<select name="<?=$fn_account_type;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach ($account_types as $account_type): ?>
														<option value="<?=$account_type->account_name;?>" <?=set_select($fn_account_type, $account_type->account_name);?>><?=$account_type->account_name?></option>
													<?php endforeach; ?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_account_type);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="12">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item" disabled>Add Item</button>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>Invoice Summary</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="subtotal" class="control-label">Subtotal</label>

						<input type="number" name="subtotal" class="form-control-plaintext" value="<?=set_value('subtotal');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('subtotal');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="discount" class="control-label">Discount <small class="font-italic">(Optional)</small></label>

						<input type="number" name="discount" class="form-control" value="<?=set_value('discount');?>" step="any">

						<div class="red-text flash-message">
							<?=form_error('discount');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="total" class="control-label">Total</label>

						<input type="number" name="total" class="form-control-plaintext" value="<?=set_value('total');?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('total');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('invoices')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
