+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'desc']
			],
			columnDefs: [{
				className: 'text-right',
				targets: cols.length - 3
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function addItemRow(account_types) {
		var $newRow = $(itemRow());
		setAccountTypes($newRow, account_types);

		var $itemTable = $('.item-table');
		var purchaseItems = $itemTable.find('.purchase_item');

		if (purchaseItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="purchase_item">' +
			'<td>' +
			'<input type="hidden" name="purchase_item_' + rowId + '" value="' + rowId + '">' +
			'<input type="hidden" name="status_' + rowId + '" value="pending">' +
			'<input type="text" name="item_' + rowId + '" class="form-control go-suggest" value="" data-suggest="items">' +
			'</td>' +
			'<td>' +
			'<input type="number" name="qty_' + rowId + '" class="form-control" value="" min="0" step="any">' +
			'</td>' +
			'<td>' +
			'<input type="text" name="unit_' + rowId + '" class="form-control go-suggest" value="" data-suggest="units">' +
			'</td>' +
			'<td>' +
			'<input type="number" name="price_' + rowId + '" class="form-control" value="" step="any">' +
			'</td>' +
			'<td>' +
			'<input type="number" name="amount_' + rowId + '" class="form-control-plaintext" value="" step="any" readonly>' +
			'</td>' +
			'<td>' +
			'<select name="account_type_' + rowId + '" class="dropsearch form-control" data-placeholder="" style="width:100%;">' +
			'</td>' +
			'<td class="text-center">' +
			'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
			'</tr>';
	}

	function setAccountTypes($poItem, account_types) {
		$poItem.find('select[name^="account_type"]').select2({
		  data: account_types,
		  placeholder: $(this).attr('data-placeholder'),
		  allowClear: true,
		  width: '100%',
		  sorter: function (data) {
			return data.sort(function (a, b) {
			  return a.text.localeCompare(b.text);
			});
		  },
		});
	  }

	function makeRandomId() {
		var text = '';
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
			'<td class="text-center" colspan="6">No items added yet.</td>' +
			'</tr>';
	}

	function getRowTotal($row) {
		var qty = parseFloat($row.find('input[name^="qty"]').val());
		if (isNaN(qty)) {
			qty = 0;
		}

		var price = parseFloat($row.find('input[name^="price"]').val());
		if (isNaN(price)) {
			price = 0;
		}

		var amount = price * qty;
		$row.find('input[name^="amount"]').val(amount.toFixed(2));

		return amount;
	}

	function calculateTotal() {
		var total = 0;
		$('.item-table').find('.purchase_item').each(function (_, purchaseItem) {
			total += getRowTotal($(purchaseItem));
		});

		$('.total-amount').text(total.toFixed(2));
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.purchase_item').remove();

		var $itemTable = $(event.delegateTarget);
		var purchaseItems = $itemTable.find('.purchase_item');
		if (purchaseItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		calculateTotal();
	}

	function showActiveSubUsages(activeList) {
		var $subUsageDropdown = $('select[name="sub_usage"]');

		$subUsageDropdown.find('option').each(function(_, option) {
			var text = option.text;

			if (text && activeList.includes(text)) {
				option.disabled = false;
			} else {
				option.disabled = true;
			}
		});

		$subUsageDropdown.select2();
	    var defaultSubUsage = $subUsageDropdown.attr('data-default');
        $subUsageDropdown.attr('data-default', '');
        $subUsageDropdown.val(defaultSubUsage).trigger('change');
	}


  function deleteAttachment(attachmentId, file_name) {
		return $.ajax({
			url: BASE_URI + 'purchases/delete_attachment/' + attachmentId,
			type: 'POST',
			data: {
        file_name: file_name
      },
			dataType: 'json'
		});
	}

	function getAccountTypes() {
		return $.ajax({
			url: BASE_URI + 'account_types/all/purchase_order', 	//rename here for later
			type: 'POST',
			dataType: 'json',
		});
	}

	$(function () {
		var account_types = [
			{
				id: '',
				text: '',
			},
		];

		$.when(getAccountTypes()).then(function (response) {
			response.account_types.forEach(function (account_type) {
				account_type.text = account_type.account_name;
				account_type.id = account_type.account_name;
				account_types.push(account_type);
			});
		});

		$('.action-add-item').click(function() {
			addItemRow(account_types);
		});

		var dataTable = createDataTable('.tableA');

		$('input[name="main_usage"], select[name="main_usage"]')
			.change(function (event) {
				var url = BASE_URI + 'fetch/purchases_by_usage?usage=' + event.currentTarget.value;
				dataTable.ajax.url(url).load();
			})
			.trigger('change');

		$('select[name="main_usage"]').change(function (event) {
		    $.ajax({
				url: BASE_URI + 'sub_usages/subs_of?usage=' + encodeURIComponent(event.currentTarget.value),
				type: 'POST',
				dataType: 'json'
			})
			.done(function(response) {
				var activeList = [];
				if (Array.isArray(response.sub_usages)) {
					activeList = response.sub_usages
				}
				showActiveSubUsages(activeList);
			})
			.fail(function() {
			    showActiveSubUsages([]);
			});
		});

		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			})
			.on('change', 'input[name^="qty"]', calculateTotal)
			.on('change', 'input[name^="price"]', calculateTotal);

		calculateTotal();

    $('.attachment-table').on('click', '.action-delete-attachment', function (event) {
     	var cols = $(event.currentTarget).closest('tr.attachment').find('td');
      	var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';
		let $this = $(this);
      	alertify.confirm(
					'Are you sure you want to delete ' + docNo + '?',
					function () {
						var attachmentId = event.currentTarget.getAttribute('data-id');
           				var fileName = event.currentTarget.getAttribute('data-filename');

						$.when(deleteAttachment(attachmentId, fileName)).then(
							function (response) {
								console.log($this.html())
								$this.parent().parent().remove();
							})
					}
				).setHeader('<em>Delete Attachment</em>');
    });

		$('.toggle-usage-mode').click(function (event) {
		    var $daddy = $(event.currentTarget).closest('.usage-mode');
		    var $uncle = $daddy.siblings('.usage-mode');

		    $daddy.addClass('d-none');
		    $daddy.find('input,select').attr('disabled', true).trigger('change');

		    $uncle.removeClass('d-none');
		    $uncle.find('input,select').attr('disabled', false).trigger('change');
		});
	});
}(jQuery);
