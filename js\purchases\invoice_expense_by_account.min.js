!function(e,t){"use strict";function a(t){var a=e(t),r=a.DataTable({lengthChange:!1,info:!1,paging:!1,searching:!1,order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:3}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+encodeURI(a.attr("data-package")),footerCallback:function(){var t=this.api(),a=3,r=function(e){if("number"==typeof e)return e;if("string"==typeof e){e=e.replace(/[$,]/g,"");var t=parseFloat(e);return isNaN(t)?0:t}return 0},n=t.column(a).data().reduce(function(e,t){return r(e)+r(t)},0);console.log(n),e(t.column(a).footer()).html(e.fn.addNumericCommas(parseFloat(n).toFixed(2)))}});return r}e(function(){a(".tableA")})}(jQuery,dateFns);