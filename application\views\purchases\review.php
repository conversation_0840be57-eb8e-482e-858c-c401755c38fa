<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<div class="w-100">
            	<h5 class="crud-subtitle"><span>Purchase Order Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="supplier" class="font-weight-bold">Supplier</label>
						<p class="underlined"><?=$purchase->supplier;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="purchase_date" class="font-weight-bold">Purchase Date</label>
						<p class="underlined"><?=date('m/d/Y', strtotime($purchase->purchase_date));?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="plate_no" class="font-weight-bold">Plate No.</label>
						<p class="underlined"><?=$purchase->plate_no;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="font-weight-bold">Remarks</label>
						<p class="underlined"><?=$purchase->remarks;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="main_usage" class="font-weight-bold">Usage</label>
						<p class="underlined"><?=$purchase->main_usage;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="font-weight-bold">Sub-usage</label>
						<p class="underlined"><?=$purchase->sub_usage;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="font-weight-bold">Requisitioner</label>
						<p class="underlined"><?=$purchase->requisitioner;?></p>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="font-weight-bold">Date Covered</label>
						<p class="underlined"><?=$purchase->date_covered;?></p>
					</div>

					<div class="col-md-12 form-group">
						<label for="attachment" class="control-label">Attachment</label>

						<div class="card">
							<div class="card-body">
								<ul>
									<?php foreach (array_filter(explode('🔥', $purchase->attachment)) as $attachment): ?>
										<li>
											<a href="<?=base_url('assets/pos/' . $purchase->id . '/' . $attachment);?>" target="blank"><?=$attachment;?></a>
										</li>
									<?php endforeach;?>
								</ul>
							</div>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Purchased Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Qty</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Unit Price</th>

									<th class="text-center" style="width: 15%;">Amount</th>

									<th class="text-center" style="width: 15%;">Account Name</th>

									<th class="text-center" style="width: 15%;">Action</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                $total = 0;
                                if (!empty($purchase_items)):
                                    foreach ($purchase_items as $purchase_item):
                                        if ($purchase_item->status !== 'disapproved') {
                                            $total += $purchase_item->amount;
                                        }

                                    ?>

												<tr class="purchase_item">
													<td><?=$purchase_item->item;?></td>

													<td class="text-right"><?=floatval($purchase_item->qty);?></td>

													<td><?=$purchase_item->unit;?></td>

													<td class="text-right"><?=number_format($purchase_item->price, 2);?></td>

													<td class="text-right "><?=number_format($purchase_item->amount, 2);?></td>

													<td class="text"><?=$purchase_item->account_type;?></td>

													<td class="text-center">
														<?php
                                                                    switch ($purchase_item->status):
                                                                    case 'pending':
                                                                    ?>
																<button type="button" class="btn btn-sm btn-success action-approve-item" data-id="<?=$purchase_item->id?>">Approve</button>
																<button type="button" class="btn btn-sm btn-danger action-disapprove-item" data-id="<?=$purchase_item->id?>">Disapprove</button>
														<?php
                                                                    break;

                                                                    case 'approved':
                                                                    ?>
																<span class="badge badge-success">Approved</span>
																<button type="button" class="btn btn-sm btn-warning action-pending-item" data-id="<?=$purchase_item->id?>">Return to Pending</button>
														<?php
                                                                    break;

                                                                    case 'disapproved':
                                                                    ?>
																<span class="badge badge-danger">Disapproved</span>
																<button type="button" class="btn btn-sm btn-warning action-pending-item" data-id="<?=$purchase_item->id?>">Return to Pending</button>
														<?php
                                                                    break;
                                                                        endswitch;
                                                                    ?>
													</td>
												</tr>
															<?php
                                                                    endforeach;
                                                                    else:
                                                                ?>
								<tr class="no-item">
									<td class="text-center" colspan="6">No items added yet.</td>
								</tr>
							<?php
                                endif;
                                ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
						<div class="float-right">
							<strong>Total: </strong><span class="total-amount"><?=number_format($purchase->total, 2);?></span>
						</div>
					</div>
				</div>

				<hr>

				<div class="clearfix text-center">
				    <?php
                        if ($_SESSION['user']->role_id == 1) {?>
					    <button type="button" class="btn btn-primary action-approve-purchase" data-id="<?=$purchase->id;?>">Approve for Check Issuance</button>
					<?php } ?>
					<a href="<?=site_url('purchases/edit/' . $purchase->id);?>" class="btn btn-warning">Edit</a>
					<a href="<?=site_url('purchases')?>" class="btn btn-light">Close</a>
				</div>
			</div>
		</form>
	</div>
</div>

<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<div class="crud-box">
			<h1 class="crud-title">Past Purchase Orders</h1>

			<div class="dt-asc-wrapper row" data-display="purchases_by_usage">
				<table class="table table-hover tableA" data-package="purchases_by_usage?usage=<?=$purchase->main_usage;?>">
					<thead>
						<tr>
							<th>Doc No</th>
							<th style="width: 25%;">Supplier</th>
							<th>Purchase Date</th>
							<th>Plate No.</th>
							<th>Total</th>
							<th>Requisitioner</th>
							<th style="width: 25%;">Items</th>
						</tr>
					</thead>

					<tbody></tbody>
				</table>
			</div>
		</div>
	</div>
</div>
