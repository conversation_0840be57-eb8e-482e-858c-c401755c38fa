!function(p){"use strict";function g(t){for(;/(\d+)(\d{3})/.test(t.toString());)t=t.toString().replace(/(\d+)(\d{3})/,"$1,$2");return t}p(function(){t=(f=p(t=".tableA")).find("thead th");var t,f,e=f.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[t.length-1,4,5]},{className:"text-center",targets:[1]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,deferLoading:0,ajax:BASE_URI+"fetch/"+f.attr("data-package"),footerCallback:function(t,e,a,r,n){var o,i,c,l,s=this.api(),u=f.attr("data-col-sum"),d=f.attr("data-col-paid");void 0!==u&&-1<u&&void 0!==d&&-1<d&&(o=function(t){return"string"==typeof t?+t.replace(/[\$,]/g,""):"number"==typeof t?t:0},i=s.column(u).data().reduce(function(t,e){return o(t)+o(e)},0),c=function(t){return"string"==typeof t?+t.replace(/[\$,]/g,""):"number"==typeof t?t:0},l=s.column(d).data().reduce(function(t,e){return c(t)+c(e)},0),p(s.column(u).footer()).html(g(parseFloat(i).toFixed(2))),p(s.column(d).footer()).html(g(parseFloat(l).toFixed(2))))}});p("#tableFilter").submit(function(t){t.preventDefault();t=BASE_URI+"fetch/"+p(".tableA").attr("data-package")+"?"+p(t.currentTarget).serialize();e.ajax.url(t).load()}).submit()})}(jQuery);