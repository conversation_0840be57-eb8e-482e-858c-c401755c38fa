<!DOCTYPE HTML>
<html>
<head>  
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	title:{
		text: "Composition of Internet Traffic in North America"
	},
	axisX: {
		interval: 1,
		intervalType: "year",
		valueFormatString: "YYYY"
	},
	axisY: {
		suffix: "%"
	},
	toolTip: {
		shared: true
	},
	legend: {
		reversed: true,
		verticalAlign: "center",
		horizontalAlign: "right"
	},
	data: [{
		type: "stackedColumn100",
		name: "Real-Time",
		showInLegend: true,
		xValueFormatString: "YYYY",
		yValueFormatString: "#,##0'%'",
		dataPoints: [
			{ x: new Date(2010,0), y: 40 },
			{ x: new Date(2011,0), y: 50 },
			{ x: new Date(2012,0), y: 60 },
			{ x: new Date(2013,0), y: 61 },
			{ x: new Date(2014,0), y: 63 },
			{ x: new Date(2015,0), y: 65 },
			{ x: new Date(2016,0), y: 67 }
		]
	}, 
	{
		type: "stackedColumn100",
		name: "Web Browsing",
		showInLegend: true,
		xValueFormatString: "YYYY",
		yValueFormatString: "#,##0'%'",
		dataPoints: [
			{ x: new Date(2010,0), y: 28 },
			{ x: new Date(2011,0), y: 18 },
			{ x: new Date(2012,0), y: 12 },
			{ x: new Date(2013,0), y: 10 },
			{ x: new Date(2014,0), y: 10 },
			{ x: new Date(2015,0), y: 7 },
			{ x: new Date(2016,0), y: 5 }
		]
	}, 
	{
		type: "stackedColumn100",
		name: "File Sharing",
		showInLegend: true,
		xValueFormatString: "YYYY",
		yValueFormatString: "#,##0'%'",
		dataPoints: [
			{ x: new Date(2010,0), y: 15 },
			{ x: new Date(2011,0), y: 12 },
			{ x: new Date(2012,0), y: 10 },
			{ x: new Date(2013,0), y: 9 },
			{ x: new Date(2014,0), y: 7 },
			{ x: new Date(2015,0), y: 5 },
			{ x: new Date(2016,0), y: 1 }
		]
	},
	{
		type: "stackedColumn100",
		name: "Others",
		showInLegend: true,
		xValueFormatString: "YYYY",
		yValueFormatString: "#,##0'%'",
		dataPoints: [
			{ x: new Date(2010,0), y: 17 },
			{ x: new Date(2011,0), y: 20 },
			{ x: new Date(2012,0), y: 18 },
			{ x: new Date(2013,0), y: 20 },
			{ x: new Date(2014,0), y: 20 },
			{ x: new Date(2015,0), y: 23 },
			{ x: new Date(2016,0), y: 27 }
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>