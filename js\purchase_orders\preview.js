+(function ($) {
	"use strict";

	function printPurchase(purchaseOrderId) {
		$.when(markPrinted(purchaseOrderId)).then(
			function () {
				window.print();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function changeStatus(purchaseOrderId, type, remarks) {
		$.when(markStatus(purchaseOrderId, type, remarks)).then(
			function (response) {
				window.location.href = BASE_URI + "purchase_orders/manager/";
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function markPrinted(purchaseOrderId) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/mark_printed/" + purchaseOrderId,
			type: "POST",
			dataType: "json",
		});
	}

	function markStatus(purchaseOrderId, type, remarks) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/mark_status/",
			type: "POST",
			dataType: "json",
			data: { purchase_order_id: purchaseOrderId, type: type, remarks: remarks },
		});
	}

	$(function () {
		$("button.action-print").click(function (event) {
			var type = event.currentTarget.getAttribute("data-type");

			if (type === "Audit") {
				alertify.prompt(
					"Audit Purchase Order",
					"Remarks",
					"",
					function (evt, value) {
						var purchaseOrderId = event.currentTarget.getAttribute("data-id");
						var remarks = value;
						if (remarks === "") {
							alertify.alert('<span class="red-text">Missing Remarks!</span>')
              .setHeader("<em>Required</em>");
						} else {
							changeStatus(purchaseOrderId, type, remarks);
						}
					},
					function () {
						alertify.error("Cancel");
					}
				);
			} else {
				alertify
					.confirm(
						"Are you sure you want to " + type + " this Purchase Order?",
						function () {
							var purchaseOrderId = event.currentTarget.getAttribute("data-id");
							if (type == "Print") {
								printPurchase(purchaseOrderId);
							} else {
								changeStatus(purchaseOrderId, type, null);
							}
						}
					)
					.setHeader("<em>" + type + " Purchase Order</em>");
			}
		});
	});
})(jQuery);
