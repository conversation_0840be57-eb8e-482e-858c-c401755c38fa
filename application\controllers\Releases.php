<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Releases extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('releases/manager'));
    }

    /**
     * Add Item Release
     */
    public function add($purchase_id)
    {
        if (!$purchase = $this->purchase->get_by_id($purchase_id) or
            $purchase->status === 'pending' or
            stripos($purchase->main_usage, WAD_WAREHOUSE) !== false
        )  {
            show_404();
        }

        $purchase_items = $this->purchase_item->get_w_state_by_purchase($purchase_id);

        $rules = array_merge(
            $this->config->item('releases/add'),
            $this->_get_purchase_item_rules($purchase_items)
        );

        if ($this->_validate_form($rules) and $release_id = $this->_attempt_add($purchase, $purchase_items)) {
            redirect(site_url('purchases/releases/' . $purchase->id));
        } else {
            $this->title = 'Add Item Release';

            $where = [
                'id' => $purchase->supplier_id
            ];
            $supplier = $this->supplier->select('', $where, 1);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'purchase' => $purchase,
                'supplier' => $supplier,
                'purchase_items' => $purchase_items
            ];
            $this->build_content('default', 'releases/add', $data);
        }
    }

    /**
     * Edit Item Release
     */
    public function edit($release_id)
    {
        $release = $this->release->get_by_id($release_id) or show_404();

        $release_items = $this->release_item->get_by_release($release->id);

        $rules = array_merge(
            $this->config->item('releases/edit'),
            $this->_get_release_item_rules($release_items)
        );

        $where = [
            'id' => $release->purchase_id,
            'status <>' => 'pending',
            'is_deleted' => 0
        ];
        $purchase = $this->purchase->select('', $where, 1);

        if ($this->_validate_form($rules) && $this->_attempt_edit($release, $release_items)) {
            redirect(site_url('purchases/releases/' . $release->purchase_id));
        } else {
            $this->title = 'Edit Item Release';

            $where = [
                'id' => $release->supplier_id
            ];
            $supplier = $this->supplier->select('', $where, 1);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'release' => $release,
                'supplier' => $supplier,
                'release_items' => $release_items
            ];
            $this->build_content('default', 'releases/edit', $data);
        }
    }

    /**
     * Delete Item Release
     */
    public function delete($release_id)
    {
        if (!$release = $this->release->get_by_id($release_id)) {
            http_response_code(400);
            $response = [
                'message' => 'Item Release is not found.'
            ];
        } elseif (!$this->_attempt_delete($release)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Item Release is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Item Releases
     */
    public function manager()
    {
        $this->title = 'Item Releases';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/releases/manager'
        ];
        $this->build_content('default', 'releases/manager');
    }

    /**
     * Item Release Preview
     */
    public function preview($release_id)
    {
        $release = $this->release->get_by_id($release_id) or show_404();

        $this->title = 'Preview: Item Release No. ' . $release->id;
        $this->javascript = [
            'js/releases/preview'
        ];

        $where = [
            'id' => $release->supplier_id
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $release_items = $this->release_item->get_by_release($release->id);

        $data = [
            'release' => $release,
            'supplier' => $supplier,
            'release_items' => $release_items
        ];
        $this->build_content('default', 'releases/preview', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('release');
        $this->load->model('release_item');
        $this->load->model('purchase');
        $this->load->model('purchase_item');
        $this->load->model('supplier');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return $error ? '<p>' . $error . '</p>' : '';
        }
    }

    /**
     * Get Purchase Order Item Rules
     */
    protected function _get_purchase_item_rules($purchase_items)
    {
        $rules = [];

        foreach ($purchase_items as $purchase_item) {
            $rules[] = [
                'field' => 'released_' . $purchase_item->id,
                'label' => 'Released Qty',
                'rules' => sprintf(
                    'trim|numeric|greater_than_equal_to|less_than_equal_to[%d]',
                    $purchase_item->qty - $purchase_item->released
                )
            ];
        }

        return $rules;
    }

    /**
     * Get Purchase Order Item Data
     */
    protected function _get_purchase_item_data($purchase_items)
    {
        $data = [];

        foreach ($purchase_items as $purchase_item) {
            $data[] = [
                'purchase_item_id' => $purchase_item->id,
                'item_id' => $purchase_item->item_id,
                'qty' => $this->input->post('released_' . $purchase_item->id) ?: 0,
                'added_by' => $_SESSION['user']->id
            ];
        }
        
        var_dump($data);die();

        return $data;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($purchase, $purchase_items)
    {
        if (!$release_items = $this->_get_purchase_item_data($purchase_items)) {
            $this->_error = 'no_release_item';
            return false;
        }

        $release_date = DateTime::createFromFormat('m/d/Y', $this->input->post('release_date'));
        $values = [
            'purchase_id' => $purchase->id,
            'supplier_id' => $purchase->supplier_id,
            'release_date' => $release_date->format('Y-m-d'),
            'added_by' => $_SESSION['user']->id
        ];

        $this->db->trans_begin();

        if (!$release_id = $this->release->insert($values) or
            !$this->_generate_release_items($release_id, $release_items)
        ) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $release_id;
        }
    }

    /**
     * Generate Item Release Items
     */
    protected function _generate_release_items($release_id, $release_items)
    {
        foreach ($release_items as $release_item) {
            if (!$this->release_item->generate(
                $release_id,
                $release_item['purchase_item_id'],
                $release_item['item_id'],
                $release_item['qty'],
                $release_item['added_by']
            )) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get Item Rules
     */
    protected function _get_release_item_rules($release_items)
    {
        $rules = [];

        foreach ($release_items as $release_item) {
            $rules[] = [
                'field' => sprintf('released_%s', $release_item->purchase_item_id),
                'label' => 'Released',
                'rules' => sprintf(
                    'trim|numeric|greater_than_equal_to|less_than_equal_to[%d]',
                    $release_item->ordered - $release_item->released + $release_item->qty
                )
            ];
        }

        return $rules;
    }

    /**
     * Get Item Data
     */
    protected function _get_release_item_data($release_items)
    {
        $data = [];

        foreach ($release_items as $release_item) {
            $data[] = [
                'purchase_item_id' => $release_item->purchase_item_id,
                'item_id' => $release_item->item_id,
                'qty' => $this->input->post('released_' . $release_item->purchase_item_id) ?: 0,
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($release, $release_items)
    {
        $this->db->trans_begin();

        $where = [
            'release_id' => $release->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];
        if (!$this->release_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$release_items = $this->_get_release_item_data($release_items)) {
            $this->_error = 'no_release_item';
            return false;
        }

        $where = [
            'id' => $release->id
        ];
        $release_date = DateTime::createFromFormat('m/d/Y', $this->input->post('release_date'));
        $values = [
            'release_date' => $release_date->format('Y-m-d'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->release->update($where, $values) ||
            !$this->_generate_release_items($release->id, $release_items)
        ) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($release)
    {
        $where = [
            'id' => $release->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->release->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Callback: Check if Supplier Exists
     */
    public function _is_supplier($supplier_id)
    {
        $supplier = $this->supplier->get_by_id($_SESSION['user'], $supplier_id);
        return !empty($supplier);
    }
}
