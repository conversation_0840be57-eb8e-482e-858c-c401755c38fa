<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Poc extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch cash bonds
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'poc';
        $primary_key = 'poc.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_status(),
            $this->_get_customer_id(),
            $this->_get_doc_no(),
            $this->_get_customer(),
            $this->_get_poc_date(),
            $this->_get_total(),
            $this->_get_issued_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
poc
LEFT JOIN customer ON customer.id = poc.customer_id
LEFT JOIN user AS issuer ON issuer.id = poc.added_by
EOT;

        $where = <<<EOT
poc.is_deleted = 0
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'for_audit';

        switch($status){
            case 'for_audit':
                $where .= <<<EOT

AND poc.status = "for_audit"
EOT;
                break;

            case 'for_approval':
                $where .= <<<EOT

AND poc.status = "for_approval"
EOT;
                break;

            case 'approved':
                $where .= <<<EOT

AND poc.status = "approved"
EOT;
                break;

            case 'disapproved':
                $where .= <<<EOT

AND poc.status = "disapproved"
EOT;
                break;

            default:
                break;
        }


        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'poc.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'poc.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'poc';
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'poc.status',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status',
        ];
    }

    /**
     * Get Customer ID
     */
    protected function _get_customer_id()
    {
        return [
            'db' => 'customer.id',
            'as' => 'customer_id',
            'dt' => 'DT_RowCustomerId',
            'field' => 'customer_id',
        ];
    }

    /**
     * Get poc document number
     */
    protected function _get_doc_no()
    {
        return [
            'db' => 'poc.reference',
            'as' => 'doc_no',
            'dt' => 0,
            'field' => 'doc_no',
            'formatter' => function ($d, $row) {
                $res = '<a href="../poc/preview/' . $row['actions'] . '" target="_blank" class="btn btn-link">' . $d . '</a>';
                return $res;
            }
        ];
    }

    /**
     * Get poc customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer'
        ];
    }

    /**
     * Get poc date
     */
    protected function _get_poc_date()
    {
        return [
            'db' => 'DATE_FORMAT(poc.poc_date, "%b %d, %Y")',
            'as' => 'poc_date',
            'dt' => 2,
            'field' => 'poc_date'
        ];
    }

    /**
     * Get total
     */
    protected function _get_total()
    {
        return [
            'db' => 'poc.grand_total',
            'as' => 'grand_total',
            'dt' => 3,
            'field' => 'grand_total',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get issuing person
     */
    protected function _get_issued_by()
    {
        return [
            'db' => 'issuer.full_name',
            'as' => 'issued_by',
            'dt' => 4,
            'field' => 'issued_by'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'poc.id',
            'as' => 'actions',
            'dt' => 5,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('poc/preview/' . $d, 'Preview', 'title="Preview" class="dropdown-item text-left"');

                if(in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_GENERAL_MANAGER, ROLE_AUDIT])){
                    switch($row['status']){
                        case 'for_audit' :
                            $res .= '<button class="action-audit dropdown-item text-left green-text" role="button" data-id="' . $d . '" user-name="' . $_SESSION['user']->full_name . '" customer-id="' . $row['customer_id'] . '">Audit</button>';
                            break;
                        default:
                            break;
                    }
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
