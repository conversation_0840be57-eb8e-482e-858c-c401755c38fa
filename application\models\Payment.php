<?php
class Payment extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'payment';
        parent::__construct();
    }

    /**
     * Get Payment by ID
     */
    public function get_by_id($payment_id)
    {
        $sql = <<<EOT
SELECT payment.*, 
    receiver.full_name AS receiver,
    IFNULL(ard.full_name, '') AS ard_name,
    IFNULL(prd.full_name, '') AS prd_name,
    audit.audit_remarks, audit.audit_status,
    audit_attachment.file_name AS audit_attachment_file
FROM payment
LEFT JOIN payment_audit AS audit ON audit.payment_id = payment.id AND audit.is_deleted = 0
LEFT JOIN payment_audit_attachment AS audit_attachment ON audit_attachment.payment_id = payment.id AND audit_attachment.is_deleted = 0
LEFT JOIN user AS receiver ON receiver.id = payment.added_by
LEFT JOIN user AS ard ON ard.id = payment.ar_by
LEFT JOIN user AS prd ON prd.id = payment.pr_by
WHERE payment.is_deleted = 0
    AND payment.id = ?
EOT;

        $binds = [$payment_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }

    }

    /**
     * Get All Payments for AR to PR
     */
    public function get_all_for_ar_and_pr()
    {
        $sql = <<<EOT
SELECT 
    payment.id,
    payment.payment_date,
    payment.total_paid,
    payment.type,
    payment.ar_no,
    payment.counter,
    payment.pr_no,
    payment.counter_pr,
    CASE
        WHEN payment.type <> 'cash' AND payment.ar_no IS NULL THEN "for_ar"
        WHEN (payment.type = 'cash' AND payment.pr_no IS NULL) OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL) THEN "for_pr"
        ELSE "none"
    END as status
FROM payment
WHERE payment.is_deleted = 0
    AND (
        (payment.type <> 'cash' AND payment.ar_no IS NULL) 
        OR (
            (payment.type = 'cash' AND payment.pr_no IS NULL) 
            OR (payment.type = 'check' AND payment.ar_no IS NOT NULL AND payment.pr_no IS NULL)
        )
    )
    AND payment.payment_date < "2024-06-30"
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * Get Payment by ID
     */
    public function get_ar_payment_by_id($payment_id)
    {
        $sql = <<<EOT
SELECT *
FROM payment
WHERE payment.is_deleted = 0
    AND payment.id = ?
    AND payment.ar_no IS NOT NULL
EOT;

        $binds = [$payment_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }

    }

        /**
     * Get Payment Sheet
     */
    public function get_by_payment($date_from, $date_to)
    {
        $sql = <<<EOT
SELECT IF(payment.type = "cash", "Cash", payment_detail.bank_name) AS mop, SUM(payment.total_paid) AS total_paid
FROM payment
LEFT JOIN payment_detail ON payment_detail.payment_id = payment.id       
WHERE payment.is_deleted = 0
    AND payment.payment_date BETWEEN ? AND ?
GROUP BY IF(payment.type = 'cash', 'cash', payment_detail.bank_name) 
EOT;
        $binds = [$date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Payment Daily Breakdown
     */
    public function get_payment_breakdown_by_day($date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT calendar.date AS cash_in_date,
COALESCE(SUM(payment.amount), 0) AS total_cash_in_per_day
FROM (
    SELECT ? + INTERVAL n DAY AS date
    FROM (
        SELECT 
            a.N + b.N * 10 + c.N * 100 AS n
        FROM
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b,
            (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
        ) AS numbers
    WHERE ? + INTERVAL n DAY <= ?
) AS calendar
LEFT JOIN (
    SELECT payment.payment_date AS date, payment.total_paid AS amount
    FROM payment
    WHERE payment.is_deleted = 0
) AS payment ON calendar.date = payment.date
GROUP BY calendar.date
ORDER BY calendar.date;
EOT;
        $binds = [$date_from, $date_from, $date_to];
        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
