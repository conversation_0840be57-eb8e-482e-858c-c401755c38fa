<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var dataPoints = [];
var y = 1000;
var limit = 50000;

for ( var i = 0; i < limit; i++ ) {
	y += Math.round( 10 + Math.random() * (-10 -10));	
	dataPoints.push({ y: y });
}

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	zoomEnabled: true,
	title:{
		text: "Performance Demo with 50,000 Data Points"
	}, 
	subtitles:[{
		text: "Try Zooming and Panning"
	}],
	data: [{
		type: "line",
		dataPoints: dataPoints
	}],
	axisY:{
		includeZero: false
	}
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>