<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Order_confirmations extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('order_confirmation');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch order_confirmations
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'order_confirmation';
        $primary_key = 'order_confirmation.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_number(),
            $this->_get_order_confirmation_date(),
            $this->_get_order_confirmation_number(),
            $this->_get_customer(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
order_confirmation
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
LEFT JOIN user ON user.id = order_confirmation.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        // var_dump($status);
        // die();
        $where = <<<EOT
order_confirmation.is_deleted = 0
EOT;            

        if ($status !== 'all') {
            $where .= <<<EOT

AND order_confirmation.status = "$status"
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'order_confirmation.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'order_confirmation.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'order_confirmation';
            }
        ];
    }

    /**
     * Get order_confirmation number
     */
    protected function _get_order_confirmation_number()
    {
        return [
            'db' => 'order_confirmation.id',
            'as' => 'order_confirmation_number',
            'dt' => 0,
            'field' => 'order_confirmation_number'
        ];
    }

    /**
     * Get order slip number
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'order_confirmation.order_slip_id',
            'as' => 'order_slip_number',
            'dt' => 1,
            'field' => 'order_slip_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get order_confirmation date
     */
    protected function _get_order_confirmation_date()
    {
        return [
            'db' => 'DATE_FORMAT(order_confirmation.order_confirmation_date, "%b %d, %Y")',
            'as' => 'order_confirmation_date',
            'dt' => 2,
            'field' => 'order_confirmation_date'
        ];
    }

    /**
     * Get order_confirmation customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 3,
            'field' => 'customer'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'order_confirmation.remarks',
            'dt' => 4,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(order_confirmation.is_deleted = 0, order_confirmation.status, "cancelled")',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 5,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'order_confirmation.id',
            'as' => 'file',
            'dt' => 6,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->order_confirmation->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/order_confirmations/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'order_confirmation.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        // $res .= anchor('sales_orders/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        $res .= anchor('purchase_orders/add/' . $d, 'Purchase Order', 'title="Confirm Order" class="dropdown-item text-left green-text"');
                        $res .= anchor('order_confirmations/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('order_confirmations/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } else {
                        $res .= anchor('order_confirmations/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
