<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 * Application HTML Helpers (Based on <PERSON>Igni<PERSON>'s Helpers)
 *
 * @package		Application
 * @subpackage	Helpers
 * @category	HTML
 */

 // ------------------------------------------------------------------------

/**
 * Checks if Date String is a Valid Date
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('is_date'))
{
	function is_date($date_str, $format = 'Y-m-d')
	{
        $date_obj = DateTime::createFromFormat($format, $date_str);

        return $date_obj && $date_str === $date_obj->format($format);
	}
}

// ------------------------------------------------------------------------

/**
 * Checks if Date String is a Past Date
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('is_past_date'))
{
	function is_past_date($date_str, $format = 'Y-m-d')
	{
        $date_now = new DateTime();
        $date_obj = DateTime::createFromFormat($format, $date_str);

        return $date_obj && $date_str === $date_obj->format($format) && $date_obj < $date_now;
	}
}

// ------------------------------------------------------------------------

/**
 * Checks if Date String is a Future Date
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('is_future_date'))
{
	function is_future_date($date_str, $format = 'Y-m-d')
	{
        $date_now = new DateTime();
        $date_obj = DateTime::createFromFormat($format, $date_str);

        return $date_obj && $date_str === $date_obj->format($format) && $date_obj > $date_now;
	}
}

// ------------------------------------------------------------------------

/**
 * Get How Much Time Elapsed Since DateTime
 *
 * @access	public
 * @param	string
 * @param	string
 * @return	bool
 */

if ( ! function_exists('time_elapsed'))
{
	function time_elapsed($datetime, $full = FALSE)
	{
		$date_now = new DateTime();
		$date_obj = new DateTime($datetime);
		$diff = $date_now->diff($date_obj);

		$diff->w = floor($diff->d / 7);
		$diff->d -= $diff->w * 7;

		$string = array(
			'y' => 'year',
			'm' => 'month',
			'w' => 'week',
			'd' => 'day',
			'h' => 'hour',
			'i' => 'minute',
			's' => 'second',
		);
		foreach ($string as $k => &$v)
		{
			if ($diff->$k)
			{
				$v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
			}
			else
			{
				unset($string[$k]);
			}
		}

		! $full && $string = array_slice($string, 0, 1);
		return $string ? implode(', ', $string) . ' ago' : 'just now';
	}
}

/* End of file myt_date_validate_helper.php */
/* Location: ./application/helpers/myt_date_validate_helper.php */
