<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Purchase_orders extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('purchase_order');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch purchase_orders
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'purchase_order';
        $primary_key = 'purchase_order.id';

        $columns = [
            $this->_get_id(),
            // $this->_get_order_slip_number(),
            $this->_get_purchase_order_date(),
            $this->_get_purchase_order_number(),
            // $this->_get_customer(),
            $this->_get_supplier(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_main_usage(),
            $this->_get_urgent(),
            $this->_get_prepared_by(),
            $this->_get_approved_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
purchase_order
LEFT JOIN customer AS customer ON customer.id = purchase_order.customer_id
LEFT JOIN supplier AS supplier ON supplier.id = purchase_order.supplier_id
LEFT JOIN user ON user.id = purchase_order.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        if ($status == 'deleted') {
            $where = <<<EOT
purchase_order.is_deleted = 1
EOT;
        } elseif ($status === 'urgent') {
            $where = <<<EOT
purchase_order.is_urgent = 1
AND purchase_order.is_deleted = 0
AND purchase_order.status NOT IN('audited','received')
EOT;
        } elseif ($status == 'all') {
            $where = '1';
        } elseif ($status == 'for approval') {
            $where = <<<EOT
purchase_order.is_verified = 0
AND purchase_order.printed_by > 0
AND purchase_order.is_deleted = 0
EOT;
        } else {
            $where = <<<EOT
purchase_order.status = "{$status}"
AND purchase_order.is_deleted = 0
EOT;
        }

        $item = $this->input->get('item', true);

        if ($item !== null && $item !== '') {
            $where .= <<<EOT

AND item.item_id IN (
    SELECT item.id AS item_id
    FROM item
    WHERE item.name LIKE '%{$item}%'
)
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'purchase_order.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'purchase_order.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'purchase_order';
            }
        ];
    }

    /**
     * Get main usage
     */
    protected function _get_main_usage()
    {
        return [
            'db' => 'purchase_order.usage_id',
            'dt' => 'DT_RowUsage',
            'field' => 'usage_id'
        ];
    }

    /**
     * Get purchase_order number
     */
    protected function _get_purchase_order_number()
    {
        return [
            'db' => 'purchase_order.id',
            'as' => 'purchase_order_number',
            'dt' => 0,
            'field' => 'purchase_order_number'
        ];
    }

    // /**
    //  * Get order slip number
    //  */
    // protected function _get_order_slip_number()
    // {
    //     return [
    //         'db' => 'purchase_order.order_slip_id',
    //         'as' => 'order_slip_number',
    //         'dt' => 1,
    //         'field' => 'order_slip_number',
    //         'formatter' => function($d, $row) {
    //             return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
    //         }
    //     ];
    // }

    /**
     * Get purchase_order date
     */
    protected function _get_purchase_order_date()
    {
        return [
            'db' => 'DATE_FORMAT(purchase_order.purchase_order_date, "%b %d, %Y")',
            'as' => 'purchase_order_date',
            'dt' => 1,
            'field' => 'purchase_order_date'
        ];
    }

    // /**
    //  * Get purchase_order customer
    //  */
    // protected function _get_customer()
    // {
    //     return [
    //         'db' => 'customer.name',
    //         'as' => 'customer',
    //         'dt' => '',
    //         'field' => 'customer'
    //     ];
    // }

    /**
     * Get PO supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 2,
            'field' => 'supplier'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'purchase_order.remarks',
            'dt' => 3,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(purchase_order.is_deleted = 1, "deleted", purchase_order.status)',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get Urgent PO
     */
    protected function _get_urgent()
    {
        return [
            'db' => 'purchase_order.is_urgent',
            'as' => 'is_urgent',
            'dt' => 'DT_RowUrgent',
            'field' => 'is_urgent'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' =>  'CONCAT(user.full_name, "<br>", DATE_FORMAT(ADDTIME(purchase_order.added_on, "12:00:00"), "%b %d, %Y %h:%i %p"))',
            'dt' => 4,
            'field' => 'prepared_by',
            'as' => 'prepared_by',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_approved_by()
    {
        return [
            'db' =>  'CONCAT(user.full_name, "<br>", DATE_FORMAT(ADDTIME(purchase_order.approved_on, "12:00:00"), "%b %d, %Y %h:%i %p"))',
            'dt' => 5,
            'field' => 'full_name',
            'as' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'purchase_order.id',
            'as' => 'file',
            'dt' => 7,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->purchase_order->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/purchase_orders/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'purchase_order.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                switch ($row['status']) {
                    case 'pending':
                        if (!in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        }

                        if ($_SESSION['user']->role_id === ROLE_RECOMMENDER) {
                            $res .= anchor('purchase_orders/recommend/' . $d, 'Recommend for Approval', 'title="Recommend for Approval" class="dropdown-item text-left"');
                        }

                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchase_orders/review/' . $d, 'Review for Approval', 'title="Review for Approval" class="dropdown-item text-left"');
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                        }

                        // if ($row['is_urgent'] === '0' && !in_array($row['status'],['audited','deleted']) && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        // }

                        break;

                    case 'for approval':
                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }

                        if (($_SESSION['user']->role_id === ROLE_ADMIN && $_SESSION['user']->id !== 5) || $_SESSION['user']->role_id === ROLE_PO_ADMIN) {
                            $res .= anchor('purchase_orders/review/' . $d, 'Review for Verification', 'title="Review for Verification" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                        }
                        break;


                    case 'approved':

                        $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'Print', 'title="Print" class="dropdown-item text-left"');
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                        }

                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;

                    case 'printed':
                        if (!in_array($_SESSION['user']->role_id, [ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER]) && stripos($row['usage_id'], WAD_WAREHOUSE) !== false) {
                            $res .= anchor('purchase_orders/receives/' . $d, 'Receives', 'title="Receives" class="dropdown-item text-left"');
                        } elseif (!in_array($_SESSION['user']->role_id, [ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER])) {
                        } else {
                        }

                        if ($_SESSION['user']->role_id === ROLE_CENTRAL_ACCOUNTING) {
                            $res .= anchor('purchase_orders/simple_preview/' . $d . '/Releasing', 'Approve for Releasing', 'title="Approve for Releasing" class="dropdown-item text-left text-success"');
                        }

                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }

                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchase_orders/simple_preview/' . $d . '/Releasing', 'Approve for Releasing', 'title="Approve for Releasing" class="dropdown-item text-left text-success"');
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_PO_ADMIN, ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                        }
                        break;
                    case 'check issued':
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchase_orders/simple_preview/' . $d . '/Printing', 'Approve for Printing', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'for printing':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'Print', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'for release':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_CENTRAL_ACCOUNTING])) {
                            $res .= anchor('purchase_orders/preview_release/' . $d, 'Release', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'released':
                        if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/preview/' . $d . '/Audit', 'Audit', 'title="Approve for Printing" class="dropdown-item text-left text-success"');
                        }


                        if ($row['is_urgent'] === '0' && !in_array($_SESSION['user']->role_id, [ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= '<button class="action-urgent dropdown-item text-left" role="button" data-id="' . $d . '">Mark as Urgent</button>';
                        }
                        break;
                    case 'audited':
                        $res .= anchor('purchase_orders/receive_po/' . $d . '/', 'Receive PO', 'title="Receive PO" class="dropdown-item text-left text-success"');
                        break;
                    case 'urgent':
                        if (!in_array($_SESSION['user']->role_id, [ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_AUDIT]) && stripos($row['main_usage'], WAD_WAREHOUSE) !== false) {
                            $res .= anchor('purchase_orders/receives/' . $d, 'Receives', 'title="Receives" class="dropdown-item text-left"');
                        } elseif (!in_array($_SESSION['user']->role_id, [ROLE_ACCOUNTING, ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/releases/' . $d, 'Releases', 'title="Releases" class="dropdown-item text-left"');
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                        } else {
                        }

                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                        } elseif (in_array($_SESSION['user']->role_id, [ROLE_PO_ADMIN, ROLE_GENERAL_MANAGER, ROLE_AUDIT])) {
                            $res .= anchor('purchase_orders/preview/' . $d . '/Print', 'View', 'title="View" class="dropdown-item text-left"');
                        }
                        break;

                    case 'deleted':
                        if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                            $res .= '<button class="action-pending dropdown-item text-left orange-text" role="button" data-id="' . $d . '">Return To Pending</button>';
                        }
                        break;

                    default:
                        break;
                }

                if(!in_array($row['status'],['pending','deleted','urgent']) && !in_array($_SESSION['user']->role_id, [ROLE_AUDIT])){
                    $res .= anchor('purchase_orders/add_attachment/' . $d, 'Add PO Attachment', 'title="Add PO Attachment" class="dropdown-item text-left"');
                }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
