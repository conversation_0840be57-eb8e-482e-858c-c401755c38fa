<!DOCTYPE html>
<html>

<head>
    <title>Asia United Bank Print Check (Voucher)</title>

    <style>
        @media screen {
            html, body {
                font-size: 9pt!important;
            }
        }

        @media print {
            @page {
                size: 8in 8.5in;
                size: portrait;
                margin: 0;
            }

            html, body {
                font-size: 9pt!important;
                width: 8in;
                height: 8.5in;
            }
        }

        table.voucher,
        table.voucher th,
        table.voucher td {
            vertical-align: top;
            padding: 7px;
            border: 1px solid black;
            border-collapse: collapse;
            font-size: 9pt!important;
        }
	</style>
</head>

<body>
    <div style="position: absolute; margin-top: 10mm; margin-left: 3mm; width: 190mm;">
        <table class="voucher" style="width: 100%;">
            <tbody>
                <tr>
                    <td colspan="2" style="width: 65%; font-weight: bold;">LEYTE AGRI VENTURES CORPORATION</td>
                    <td style="width: 35%;">CHECK NO: <?=$check->check_no;?></td>
                </tr>

                <tr>
                    <td style="width: 10%;">DATE:</td>
                    <td style="width: 55%;">
                        <?php
                            $check_date = new DateTime($check->check_date);
                            echo $check_date->format('F j, Y');
                        ?>
                    </td>
                    <td style="width: 35%;">AMOUNT: <?='Php ' . number_format($check->amount, 2);?></td>
                </tr>

                <tr>
                    <td style="width: 10%;">PAYEE:</td>
                    <td colspan="2" style="width: 90%;"><?=$check->supplier;?></td>
                </tr>

                <tr style="height: 50mm;">
                    <td style="width: 20%;">PARTICULARS</td>
                    <td colspan="2" style="width: 80%;">
                    <?php if(!empty($check_purchases)):?>
                            <?php foreach($check_purchases as $check_purchase):?>
                            PO #<?=$check_purchase->purchase_id?> <br>
                            <?php endforeach;?>
                    <?php endif;?>
                    </td>
                </tr>

                <tr>
                    <td style="width: 20%;">REFERENCE NO:</td>
                    <td style="width: 40%;"></td>
                    <td style="width: 40%;">RECEIVED DATE:</td>
                </tr>

                <tr>
                    <td style="width: 20%;">PREPARED BY:</td>
                    <td style="width: 40%;"><?=$_SESSION['user']->username;?></td>
                    <td style="width: 40%;"></td>
                </tr>

                <tr>
                    <td style="width: 20%;">APPROVED BY:</td>
                    <td style="width: 40%;"></td>
                    <td style="width: 40%;"></td>
                </tr>
            </tbody>
        </table>
    </div>


	<script src="<?=base_url('lib/jquery/jquery-3.3.1.min.js');?>"></script>

    <script>
        function markPrinted() {
            $.ajax({
                url: '<?=site_url('checks/printed/' . $check->id);?>',
                type: 'POST'
            })
            .done(function () {
                window.print();
            })
            .fail(function () {
                alert('Something went wrong. Please refresh page.');
            });
        }

        markPrinted();
    </script>
</body>

</html>
