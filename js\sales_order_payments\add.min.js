!function(n){"use strict";function e(){var e=n('<tr class="order_slip_item"><td><input type="hidden" name="order_slip_item_'+(e=function(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}())+'" value="'+e+'"><select style="min-width:200px" width="200px" name="item_'+e+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="qty_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="text" name="unit_'+e+'" class="form-control go-suggest" value="" data-suggest="units" readonly></td><td><input type="number" name="pref_alw_'+e+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="est_due_amount_'+e+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'),t=(a(e),n(".item-table"));0===t.find(".order_slip_item").length&&t.find(".no-item").remove(),t.find("tbody").append(e),i()}function a(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:n(this).attr("data-placeholder"),width:"280px",ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(e){return{term:e.term}},processResults:function(e){return{results:n.map(e,function(e){return{text:e.name,id:e.id}})}}}})}function i(){n(".item-table").find(".order_slip_item").each(function(e,t){n(t).find('input[name^="row"]').val(e+1)})}function l(e,t=null){e.find('input[name^="unit_"]').val(t)}n(function(){n(".action-add-item").click(e),n(".item-table").on("click",".action-delete-item",function(e){var t;n((e=e).currentTarget).closest(".order_slip_item").remove(),(e=n(e.delegateTarget)).find(".order_slip_item").length<1&&(t=n('<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'),e.find("tbody").html(t)),i()}).on("change",'select[name^="item"]',function(e){var t=n(e.currentTarget).closest(".order_slip_item"),e=e.currentTarget.value;n.when(n.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})).then(function(e){l(t,e.unit)},function(){l(t,null)}),console.log(e)}),n('select[name="customer"]').on("change",function(e){n(e.currentTarget).closest(".order_slip_item");e=e.currentTarget.value;n.when(n.ajax({url:BASE_URI+"live_sell_customers/get_data/"+e,type:"GET",dataType:"json"})).then(function(e){console.log(e),n('input[name="address"]').val(e.address),n('input[name="contact_number"]').val(e.contact_number),n('input[name="email"]').val(e.email),n('input[name="available_credit"]').val(e.available_credit)},function(){n('input[name="address"]').val(""),n('input[name="contact_number"]').val(""),n('input[name="email"]').val(""),n('input[name="available_credit"]').val("")}),console.log(e)}).change(),n(".order_slip_item").each(function(e,t){n(t).find("option").not(":selected").remove(),a(n(t))})})}(jQuery);