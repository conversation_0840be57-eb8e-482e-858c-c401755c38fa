<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sales_order_payments extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('sales_order_payment');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch sales_order_payments
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'sales_order_payment';
        $primary_key = 'sales_order_payment.id';

        $columns = [
            $this->_get_id(),
            $this->_get_sales_order_payment_number(),
            $this->_get_sales_order_number(),
            $this->_get_payment_date(),
            $this->_get_amount(),
            $this->_get_payment_type(),
            $this->_get_customer(),
            // $this->_get_remarks(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
sales_order_payment
LEFT JOIN sales_order ON sales_order.id = sales_order_payment.sales_order_id
LEFT JOIN live_sell_customer AS customer ON customer.id = sales_order.customer_id
LEFT JOIN user ON user.id = sales_order_payment.added_by
EOT;


        $where = <<<EOT
sales_order_payment.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'sales_order_payment.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'sales_order_payment.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'sales_order_payment';
            }
        ];
    }

    /**
     * Get sales_order_payment date
     */
    protected function _get_sales_order_payment_number()
    {
        return [
            'db' => 'sales_order_payment.id',
            'as' => 'sales_order_payment_number',
            'dt' => 0,
            'field' => 'sales_order_payment_number'
        ];
    }

    /**
     * Get sales_order id
     */
    protected function _get_sales_order_number()
    {
        return [
            'db' => 'sales_order.id',
            'as' => 'sales_order_number',
            'dt' => 1,
            'field' => 'sales_order_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("sales_orders/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get sales_order_payment date
     */
    protected function _get_payment_date()
    {
        return [
            'db' => 'DATE_FORMAT(sales_order_payment.payment_date, "%b %d, %Y")',
            'as' => 'payment_date',
            'dt' => 2,
            'field' => 'payment_date'
        ];
    }

    /**
     * Get sales_order_payment customer
     */
    protected function _get_amount()
    {
        return [
            'db' => 'sales_order_payment.amount',
            'as' => 'sales_order_payment_amount',
            'dt' => 3,
            'field' => 'sales_order_payment_amount'
        ];
    }


    /**
     * Get payment type
     */
    protected function _get_payment_type()
    {
        return [
            'db' => 'sales_order_payment.payment_type',
            'as' => 'sales_order_payment_payment_type',
            'dt' => 4,
            'field' => 'sales_order_payment_payment_type'
        ];
    }

    /**
     * Get customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'sales_order_payment_customer',
            'dt' => 5,
            'field' => 'sales_order_payment_customer'
        ];
    }


    /**
     * Get remarks
     */
    // protected function _get_remarks()
    // {
    //     return [
    //         'db' => 'sales_order_payment.remarks',
    //         'dt' => 6,
    //         'field' => 'remarks',
    //         'formatter' => function ($d, $row) {
    //             return ucwords($d);
    //         }
    //     ];
    // }

    /**
     * Get status
     */
    // protected function _get_status()
    // {
    //     return [
    //         'db' => 'IF(sales_order_payment.is_deleted = 0, sales_order_payment.status, "cancelled")',
    //         'as' => 'status',
    //         'dt' => 'DT_RowStatus',
    //         'field' => 'status'
    //     ];
    // }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 6,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    // protected function _get_file()
    // {
    //     return [
    //         'db' => 'sales_order_payment.id',
    //         'as' => 'file',
    //         'dt' => 5,
    //         'field' => 'file',
    //         'formatter' => function ($d, $row) {
    //             $files = $this->sales_order_payment->get_file_by_id($d);
    //             $res = '<ul>';

    //             $files = explode('🔥', $files[0]->attachment);
    //             $files = array_map('trim', $files);
    //             $files = array_filter($files);

    //             foreach ($files as $i => $file) {
    //                 $res .= '<a href="' . base_url('assets/sales_order_payments/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
    //             }

    //             $res .= '</ul>';

    //             return $res;
    //         }
    //     ];
    // }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'sales_order_payment.id',
            'as' => 'actions',
            'dt' => 7,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                // $res .= anchor('sales_order_payments/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                // $res .= anchor('sales_order_payments/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
