+ function ($,fns) {
	'use strict';

	function addItemRow(items) {
		var $newRow = $(itemRow());

		setItemList($newRow, items);


		var $itemTable = $('.item-table');
		var cash_bondItems = $itemTable.find('.cash_bond_item');

		if (cash_bondItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);

		setRowNumbers();
	}

	function addCashBondRow(cashBondData) {

		var $itemTable = $('.item-table');
		var cashBondIdExists = false;
	
		$itemTable.find('select[name^="invoice_no_"]').each(function () {
			if ($(this).val() === cashBondData.id) {
				cashBondIdExists = true;
				return false; 
			}
		});

		if (cashBondIdExists) {
			
			return; 
		}

		var $newRow = $(itemRow());
		const formattedDate = fns.format(fns.parse(cashBondData.cb_date), 'MMM DD, YYYY');

		console.log($newRow);

		$newRow.find('select[name^="invoice_no_"]')
		.append(
			$('<option>', {
				value: cashBondData.id,
				text: cashBondData.id,
			}).prop('selected', true)
		)
		.on('mousedown', function (e) {
			e.preventDefault();
		});

		$newRow.find('input[name^="cb_date_"]')
        .val(formattedDate) 
        .prop('readonly', true);
		
		$newRow.find('input[name^="total_cb_"]')
			.val(parseFloat(cashBondData.grand_total || 0).toFixed(2))
			.prop('readonly', true);

		$newRow.find('input[name^="remarks_"]')
			.val(cashBondData.remarks)
			.prop('readonly', true);

		var cash_bondItems = $itemTable.find('.cash_bond_item');

		if (cash_bondItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);

		setRowNumbers();
	}
	


	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="cash_bond_item">' +
			'<td>' +
				'<input type="hidden" name="cash_bond_item_' + rowId + '" value="' + rowId + '">' +
				'<input type="number" name="row_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
			'</td>' +
			'<td>' +
				'<select name="invoice_no_' + rowId + '" class="form-control dropsearch" data-placeholder=""></select>' +
			'</td>' +
			'<td>' +
				'<input type="text" name="cb_date_' + rowId + '" class="form-control" value="" readonly>' +
			'</td>' +
			'<td>' +
				'<input type="number" name="total_cb_' + rowId + '" class="form-control" value="" min="0" step="any" readonly>' +
			'</td>' +
			'<td>' +
				'<input type="text" name="remarks_' + rowId + '" class="form-control" value="" readonly>' +
			'</td>' +
			'<td class="text-center">' +
				'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
		'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function setItemList($itemRow, items) {
		$itemRow.find('select[name^="invoice_no"]').select2({
			data: items,
			placeholder: $(this).attr('data-placeholder'),
			allowClear: true,
			width: '100%'
		});
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="9">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var cash_bondItems = $itemTable.find('.cash_bond_item');

		cash_bondItems.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function getRowTotal($row) {
		var total = parseFloat($row.find('input[name^="total_cb"]').val());
		if (isNaN(total)) {
			total = 0;
		}

		return total;
	}

	function calculateTotal() {
		var grand_total = 0;
		$('.item-table').find('.cash_bond_item').each(function (_, cash_bondItem) {
			grand_total += getRowTotal($(cash_bondItem));
		});
		$('input[name="grand_total"]').val(grand_total.toFixed(2));
		$('.footer_total').text($.fn.addNumericCommas(grand_total.toFixed(2)));
	}


	function removeItemRow(event) {
		$(event.currentTarget).closest('.cash_bond_item').remove();

		var $itemTable = $(event.delegateTarget);
		var cash_bondItems = $itemTable.find('.cash_bond_item');
		if (cash_bondItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		setRowNumbers();
		calculateTotal();
  	}

	function getCustomerInfo(customerId) {
		return $.ajax({
			url: BASE_URI + 'customers/info/' + customerId,
			type: 'GET',
			dataType: 'json',
		});
	}

  function getCustomerInfoByName(customerName) {
    return $.ajax({
      url: BASE_URI + 'customers/info_by_name/' + encodeURIComponent(customerName),
      type: 'GET',
      dataType: 'json',
    });
    }

	function getItemInfo(itemId,customerId) {
	return $.ajax({
		url: BASE_URI + 'sales_items/info/' + itemId + '/' + customerId,
		type: 'GET',
		dataType: 'json',
	});
	}

	function getItems(customerID) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/customer_cash_bonds/' + customerID,
			type: 'GET',
			dataType: 'json'
		});
	}

	function getCustomerPrices() {
		return $.ajax({
			url: BASE_URI + 'sales_items/customer_prices',
			type: 'GET',
			dataType: 'json'
		});
	}

	function setData($row, item) {
		var unit = '';
		var price = '';

		if (item !== null) {
			unit = item.unit;
			price = item.price;
		}

		$row.find('input[name^="unit"]').val(unit);
		$row.find('input[name^="price"]').val(price);

		$.when(getCustomerPrices()).then(
			function (response) {
				var customerId = $('select[name="customer"]').val();
				var  customerPrices = response.customer_prices_dictionary;
				if (typeof customerPrices[customerId][item.id] != 'undefined') {
					$row.find('input[name^="price"]').val(customerPrices[customerId][item.id]);
				}
			}
		);
	}

	function deleteAttachment(attachmentId) {
		return $.ajax({
			url: BASE_URI + 'cash_bonds/delete_attachment/' + attachmentId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		let selectedIds = [];
		var items = [{
			id: '',
			text: ''
		}];


		if($('select[name="customer"]').val() !== ""){
			$('#btnSubmit').prop('disabled',false);
			$('.cash_bond-history').removeClass('d-none');
		}else{
			$('#btnSubmit').prop('disabled',true);
			$('.cash_bond-history').addClass('d-none');
			$('.item-table').find('tbody').html($(emptyRow()));
		}

		if($('select[name="customer"] option:selected').attr('data-walkin') === '0' && $('select[name="customer"]').val() === '16'){
			$('.credit-terms').addClass('d-none');
			$('.walkin-customer').removeClass('d-none');
		}else{
			$('.credit-terms').removeClass('d-none');
			$('.walkin-customer').addClass('d-none');
		}

        var customerId = $('input[name="customer"]').val();
		// console.log(typeof(customerId))
		if(typeof cashBondData !== "undefined"){
			addCashBondRow(cashBondData);
		}
			
        $.when(getItems(customerId)).then(
            function (response) {
                response.cash_bonds.forEach(function (item) {
                    item.text = item.id;
                    items.push(item);
                });
            }
        );

		$('select[name="customer"]').on('change',function(event){
			// var isWalkin = $('option:selected', this).attr('data-walkin');
			var customerId = $(this).val();
			items = [{
				id: '',
				text: ''
			}];


			$('input[name="address"]').val('');
			$('input[name="phone_no"]').val('');
			$('input[name="email"]').val('');
			$('input[name="available_credit"]').val('');
			$(".history").attr("href", '#');

			$.when(getCustomerInfo(customerId)).then(
				function (response) {
					var availableCredit = response.customer.available_credit;

					if(isNaN(availableCredit) || availableCredit === null){
						availableCredit = 0;
					}
					$('input[name="address"]').val(response.customer.address);
					$('input[name="phone_no"]').val(response.customer.phone_no);
					$('input[name="email"]').val(response.customer.email);
					$('input[name="available_credit"]').val($.fn.addNumericCommas(availableCredit));
					$(".history").removeClass('d-none');
					$(".history").attr("href", BASE_URI + 'cash_bonds/history/' + response.customer.id);
				}
			);

			$.when(getItems(customerId)).then(
				function (response) {
					response.cash_bonds.forEach(function (item) {
						item.text = item.id;
						items.push(item);
					});
				}
			);
			// if(customerId !== '16' && isWalkin === '0'){

			// 	$.when(getCustomerInfo(customerId)).then(
			// 		function (response) {
			// 			var availableCredit = response.customer.available_credit;

			// 			if(isNaN(availableCredit) || availableCredit === null){
			// 				availableCredit = 0;
			// 			}
			// 			$('input[name="address"]').val(response.customer.address);
			// 			$('input[name="phone_no"]').val(response.customer.phone_no);
			// 			$('input[name="email"]').val(response.customer.email);
			// 			$('input[name="available_credit"]').val($.fn.addNumericCommas(availableCredit));
			// 			$(".history").removeClass('d-none');
			// 			$(".history").attr("href", BASE_URI + 'cash_bonds/history/' + response.customer.id);
			// 		}
			// 	);

			// 	$('.credit-terms').removeClass('d-none');
			// 	$('.walkin-customer').addClass('d-none');
			// }else{
			// 	$('.credit-terms').addClass('d-none');
			// 	$('.walkin-customer').removeClass('d-none');
			// }

			//for enable and disable for add item
			var btnelem = $('#btnSubmit');

			if(customerId !== ""){
				btnelem.prop('disabled',false);
				$('.cash_bond-history').removeClass('d-none');
				$('.item-table').find('tbody').html($(emptyRow()));
			}else{
				btnelem.prop('disabled',true);
				$('.cash_bond-history').addClass('d-none');
			}
            

		});



		$('input[name="walkin_customer"]').on('change',function(event){
			var customerName = $(this).val();

			$.when(getCustomerInfoByName(customerName)).then(
				function (response) {
					$('input[name="address"]').val(response.customer.address);
					$('input[name="phone_no"]').val(response.customer.phone_no);
					$('input[name="email"]').val(response.customer.email);
					$(".history").removeClass('d-none');
					$(".history").attr("href", BASE_URI + 'cash_bonds/history/' + response.customer.id);
				},
				function (jqXHR) {
					$(".history").addClass('d-none');
				}
			);

			//for enable and disable for add item
			var btnelem = $('#btnSubmit');

			if(customerName !== ""){
				btnelem.prop('disabled',false);
				$('.item-table').find('tbody').html($(emptyRow()));
			}else{
				btnelem.prop('disabled',true);
			}

			});

		$('input[name="terms"]').on('change',function(event){
			var terms = $('input[name="terms"]:checked').val();

			if(terms === "check"){
				$('.check-terms').removeClass('d-none');
				$('.non-credit-terms').removeClass('d-none');
			}else if(terms === "credit"){
				$('.non-credit-terms').addClass('d-none');
				$('.check-terms').addClass('d-none');
			}else{
				$('.check-terms').addClass('d-none');
				$('.non-credit-terms').removeClass('d-none');
			}

		}).trigger('change');

		$('.action-add-item').click(function () {
			addItemRow(items);
		});

        $('.item-table')
            .on('change', 'select[name^="invoice_no"]', function (event) {
                var $currentSelect = $(event.currentTarget);
                var existing = $(event.delegateTarget).find('select[name^="invoice_no"] option[value="' + $currentSelect.val() + '"]:selected');
        

                if (existing.length > 1) {
                    alert('Cash Bond already selected.');
                    $currentSelect.val('').trigger('change');
                    return;
                }
        
                var $row = $currentSelect.closest(".cash_bond_item");
                var selectedId = $currentSelect.val();
        
 
                if (!selectedId) {

                    $row.find('input[name^="total_cb"]').val('');
					$row.find('input[name^="cb_date"]').val('');
					$row.find('input[name^="remarks"]').val('');
                    return;
                }

                var selectedItem = items.find(item => item.id === selectedId);
                var $totalField = $row.find('input[name^="total_cb"]');
				var $dateField = $row.find('input[name^="cb_date"]');
				var $remarksField = $row.find('input[name^="remarks"]');
        
                if (selectedItem) {
                    const formattedPrice = parseFloat(selectedItem.grand_total).toFixed(2);
					const formattedDate = fns.format(fns.parse(selectedItem.cb_date), 'MMM DD, YYYY');
                    $totalField.val(formattedPrice);
					$dateField.val(formattedDate);
					$remarksField.val(selectedItem.remarks);
        
                } else {
                    $totalField.val('');
					$dateField.val('');
					$remarksField.val('');
                }

				calculateTotal();
        
            })
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			})

		$('.attachment-table').on('click', '.action-delete-attachment', function (event) {
			var cols = $(event.currentTarget).closest('tr.attachment').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
				'Are you sure you want to delete ' + docNo + '?',
				function () {
					var attachmentId = event.currentTarget.getAttribute('data-id');
					deleteAttachment(attachmentId);
					location.reload();
				}
			).setHeader('<em>Delete Attachment</em>');
		});
	});

}(jQuery, dateFns);
