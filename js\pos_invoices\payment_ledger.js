+ function ($) {
	'use strict';

	function addNumberCommas(val) {
        while (/(\d+)(\d{3})/.test(val.toString())) {
            val = val.toString().replace(/(\d+)(\d{3})/, '$1' + ',' + '$2');
        }
    
        return val;
    }
	
	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			dom: 'Brtip',
			lengthChange: true,
			paging: false,
			filter: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
			{
				className: 'text-right',
				targets: [2]
			},
			// {
			// 	className: 'text-left',
			// 	targets: [5]
			// },
			{
				className: 'text-center',
				targets: [1, 5]
			}
			], 
			buttons: [{
		        extend: 'print',
				className: 'btn btn-secondary',
		        footer: true,
                exportOptions: {
                    stripHtml: false
                }
			},
			{
				extend: 'excel',
			  	className: 'btn btn-success ml-2',
			  	text: 'Download Excel File',
				footer: true,
				exportOptions: {
				 	columns: 'th'
				}
			}],
			autoWidth: false,
			processing: true,
			serverSide: true,
			deferLoading: 0,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package'),
			footerCallback: function (row, data, start, end, display) {
                var api = this.api();
                var colSum = $table.attr('data-col-sum');

                if (typeof colSum !== 'undefined' && colSum > -1) {
                    // returns the numeric value of data
                    var intVal = function (i) {
                        return typeof i === 'string' ?
                            i.replace(/[\$,]/g, '') * 1 :
                            typeof i === 'number' ?
                            i : 0;
                    };

                    // total of all data in table
                    var grandTotal = api
                        .column(colSum)
                        .data()
                        .reduce(function (a, b) {
                            return intVal(a) + intVal(b);
                        }, 0);

                    $(api.column(colSum).footer())
                        .html(addNumberCommas(parseFloat(grandTotal).toFixed(2)));
                }
            }
		});

		return dataTable;
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('#tableFilter').submit(function(event) {
			event.preventDefault();

			var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + $(event.currentTarget).serialize()
			dataTable.ajax.url(url).load();
			
		}).submit();
	});
}(jQuery);