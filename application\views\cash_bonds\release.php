<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
			<?php if (isset($cash_bond) && $cash_bond): ?>
				<script>
					const cashBondData = <?= json_encode($cash_bond); ?>;
					console.log(cashBondData); // For debugging
				</script>
			<?php endif; ?>

				<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Name</label>
						
						<?php if (isset($customer) && $customer): ?>
							<input type="text" id = "nameSelect" class="form-control" name="customer_name" data-placeholder="" value="<?= isset($customer) ? $customer->name : ''; ?>" readonly></input>
							<input type="hidden" name="customer" value="<?= $customer->id; ?>">
						<?php else: ?>
							<select 
								id="nameSelect" 
								class="form-control dropsearch" 
								name="customer"
								data-placeholder="">
								<option></option>
								<?php foreach($customers as $cus):?>
									<option value="<?=$cus->id?>" <?=set_select('customer', $cus->id);?>><?=ucwords($cus->name)?></option>
								<?php endforeach; ?>
							</select>
						<?php endif; ?>

                        <div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address</label>
						<input 
							type="text" 
							name="address" 
							class="form-control" 
							value="<?= isset($customer) && $customer ? $customer->address : set_value('address', ''); ?>" 
							readonly>
										
						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no" class="control-label">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?= isset($customer) && $customer ? $customer->phone_no :set_value('phone_no', ''); ?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email</label>

						<input type="text" name="email" class="form-control" value="<?= isset($customer) && $customer ? $customer->email :set_value('email', ''); ?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group credit-terms d-none">
						<label for="available_credit" class="control-label">Available Credit</label>

						<input type="text" name="available_credit" class="form-control" value="<?= isset($customer) && $customer ? $customer->available_credit : set_value('available_credit', ''); ?>" readonly>

						<div class="red-text flash-message">
							<?=form_error('available_credit');?>
						</div>
					</div>
				</div>
				

            	<h5 class="crud-subtitle"><span>List of Cash bonds Connected to this Customer</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%"  style="font-size: 1rem;">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">No.</th>

									<th class="text-center" style="width: 20%;">Cash Bond No.</th>

									<th class="text-center" style="width: 20%;">CB Date</th>						

									<th class="text-center" style="width: 20%;">Amount</th>

									<th class="text-center" style="width: 20%;">Remarks</th>

									<th class="text-center" style="width: 10%;">Action</th>
							</thead>

							<tbody>
							<?php
                                if (!empty($cash_bond_item_ids)):
									foreach ($cash_bond_item_ids as $i => $cash_bond_item_id):
                            ?>
										<tr class="cash_bond_item">
											<td>
												<?php $fn_cash_bond_item = 'cash_bond_item_' . $cash_bond_item_id;?>
												<input type="hidden" name="<?=$fn_cash_bond_item;?>" value="<?=set_value($fn_cash_bond_item, $cash_bond_item_id);?>">

												<?php $fn_row = 'row_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_row;?>" class="form-control-plaintext" value="<?=$i + 1;?>" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_cash_bond_item);?>
												</div>
											</td>

											<td>
												<?php $fn_invoice_no = 'invoice_no_' . $cash_bond_item_id;?>

												<select name="<?=$fn_invoice_no;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($items as $item):?>
														<option value="<?= $item->id; ?>" <?=set_select($fn_invoice_no, $item->id); ?>><?= $item->id; ?></option>
													<?php endforeach;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_invoice_no);?>
												</div>
											</td>
								
											<td>
												<?php $fn_cb_date = 'cb_date_' . $cash_bond_item_id;?>
												<input type="text" name="<?=$fn_cb_date;?>" class="form-control" value="<?=set_value($fn_cb_date);?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_cb_date);?>
												</div>
											</td>

											<td>
												<?php $fn_total_cb = 'total_cb_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_total_cb;?>" class="form-control" value="<?=set_value($fn_total_cb);?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_total_cb);?>
												</div>
											</td>

											<td>
												<?php $fn_remarks = 'remarks_' . $cash_bond_item_id;?>
												<input type="text" name="<?=$fn_remarks;?>" class="form-control" value="<?=set_value($fn_remarks);?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_remarks);?>
												</div>
											</td>


											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="12">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
							<tfoot>
								<tr>
									<th class="text-right" colspan="3">GRAND TOTAL</th>
									<th class="text-right footer_total">
									</th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item" disabled>Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('cash_bonds/manager')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
