+ function ($) {
	'use strict';

	function addItemRow(items) {
		var $newRow = $(itemRow());

		setItemList($newRow, items);

		var $itemTable = $('.item-table');
		var pocItems = $itemTable.find('.poc_item');

		if (pocItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);
	}


	function setItemList($itemRow, items) {
		const $descriptionSelect = $itemRow.find('select[name^="description"]');
		$descriptionSelect.select2({
			data: items,
			placeholder: $descriptionSelect.attr('data-placeholder'),
			allowClear: true,
			width: '100%'
		});

		$descriptionSelect.on('change', function() {
			const selectedId = $(this).val();
			
			const selectedItem = items.find(item => item.id === selectedId);
			if (selectedItem) {
				const formattedPrice = parseFloat(selectedItem.unit_price).toFixed(2);
				$itemRow.find('input[name^="unit_price"]').val(formattedPrice);
			} else {
				$itemRow.find('input[name^="unit_price"]').val('');
			}

		});
	}

	function getItems() {
		return $.ajax({
			url: BASE_URI + 'poc/all_item_for_poc',
			type: 'GET',
			dataType: 'json'
		});
	}
	

	function itemRow() {
		var rowId = makeRandomId();
		return '<tr class="poc_item">' +
			'<td>' +
				'<input type="hidden" name="poc_item_' + rowId + '" value="' + rowId + '">' +
				'<select name="description_' + rowId + '" class="form-control dropsearch" data-placeholder=""></select>' +
			'</td>' +
			'<td>' +
				'<input type="number" name="qty_' + rowId + '" class="form-control" value="" min="0" step="any">' +
			'</td>' +
			'<td>' +
				'<input type="number" name="unit_price_' + rowId + '" class="form-control" value="" min="0" step="any">' +
			'</td>' +
			'<td>' +
				'<input type="number" name="total_amount_' + rowId + '" class="form-control" value="" min="0" step="any" readonly>' +
			'</td>' +
			'<td class="text-center">' +
				'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
		'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="5">No items added yet.</td>' +
			'</tr>';
	}

	function getRowTotal($row) {
		var qty = parseFloat($row.find('input[name^="qty"]').val());
		if (isNaN(qty)) {
			qty = 0;
		}

		var price = parseFloat($row.find('input[name^="unit_price"]').val());
		if (isNaN(price)) {
			price = 0;
		}

		var amount = price * qty;
		if (amount < 0) {
			amount = 0;
		}
		$row.find('input[name^="total_amount"]').val(amount > 0 ? amount.toFixed(2) : '');

		return amount;
	}

	function calculateTotal() {
		var grand_total = 0;
		$('.item-table').find('.poc_item').each(function (_, pocItem) {
			grand_total += getRowTotal($(pocItem));
		});

		$('input[name="grand_total"]').val(grand_total.toFixed(4));
		$('.footer_total').text($.fn.addNumericCommas(grand_total.toFixed(2)));
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.poc_item').remove();

		var $itemTable = $(event.delegateTarget);
		var pocItems = $itemTable.find('.poc_item');
		if (pocItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		calculateTotal();
  	}

	function getCustomerInfo(customerId) {
		return $.ajax({
			url: BASE_URI + 'customers/info/' + customerId,
			type: 'GET',
			dataType: 'json',
		});
	}

	function setReference() {
		var id = $('input[name="latest_id"]').val();
	
		id = parseInt(id, 10);
		id = id + 1;
		let refNo = `POC-${id}`;
	
		// return refNo;
		var referenceNo = $('input[name="reference"]');
		referenceNo.val(refNo);
	}

	$(function () {
		var items = [{
			id: '',
			text: ''
		}];

		$.when(getItems()).then(
			function (response) {
				response.item_for_poc.forEach(function (item) {
					item.text = item.item_name;
					items.push(item);
				});
			}
		);

		$('select[name="customer"]').on('change',function(event){
			var customerId = $(this).val();

			$('input[name="company_name"]').val('');
			$('input[name="address"]').val('');
			$('input[name="phone_no"]').val('');
			$('input[name="email"]').val('');
			$('input[name="available_credit"]').val('');

			$.when(getCustomerInfo(customerId)).then(
				function (response) {
					var availableCredit = response.customer.available_credit;

					if(isNaN(availableCredit) || availableCredit === null){
						availableCredit = 0;
					}
					$('input[name="company_name"]').val(response.customer.company_name);
					$('input[name="address"]').val(response.customer.address);
					$('input[name="phone_no"]').val(response.customer.phone_no);
					$('input[name="email"]').val(response.customer.email);
					$('input[name="available_credit"]').val($.fn.addNumericCommas(availableCredit));
				}
			);

			//for enable and disable for add item
			var btnelem = $('#btnSubmit');

			if(customerId !== ""){
				btnelem.prop('disabled',false);
				$('.item-table').find('tbody').html($(emptyRow()));
			}else{
				btnelem.prop('disabled',true);
			}
		});

		$('.action-add-item').click(function () {
			addItemRow(items);
		});

		$('input[name="other_services"]')
			.on('click', function (event) {
				var servicesField = $('input[name="other_services_field"]');
				var isMarked = $(event.currentTarget).prop('checked');

				if (isMarked) {
					servicesField.prop('readonly', false);
					servicesField.val('');
				  } else {
					servicesField.prop('readonly', true);
					servicesField.val('');
				  }
			});

		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			})
			.on('input', 'input[name^="qty_"]', calculateTotal)
			.on('input', 'input[name^="unit_price_"]', calculateTotal);

		calculateTotal();
		// setReference();
	});

}(jQuery);