<?php
class Cash_bond_audit_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'cash_bond_audit_attachment';
        parent::__construct();
    }

    /**
     * Get Attachment by Cash Bond Id
     */
    public function get_by_id($id){

    $sql = <<<EOT
SELECT cash_bond_audit_attachment.*
FROM cash_bond_audit_attachment
WHERE cash_bond_audit_attachment.is_deleted = 0
AND cash_bond_audit_attachment.cash_bond_id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */