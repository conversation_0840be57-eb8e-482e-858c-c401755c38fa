<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sales_items extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Items
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'sales_item';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_unit(),
            $this->_get_received(),
            $this->_get_released(),
            $this->_get_inventory(),
            $this->_get_price(),
            $this->_get_actions()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'item';
            }
        ];
    }

    /**
     * Get name
     */
    protected function _get_name()
    {
        return [
            'db' => 'name',
            'dt' => 0,
            'field' => 'name'
        ];
    }

    /**
     * Get unit of measure
     */
    protected function _get_unit()
    {
        return [
            'db' => 'unit',
            'dt' => 1,
            'field' => 'unit'
        ];
    }

    /**
     * Get price
     */
    protected function _get_price()
    {
        return [
            'db' => 'FORMAT(price, 2)',
            'as' => 'price',
            'dt' => 2,
            'field' => 'price'
        ];
    }

    /**
     * Get received
     */
    protected function _get_received()
    {
        return [
            'db' => '(SELECT SUM(qty) FROM receive_item LEFT JOIN receive ON receive.id = receive_item.receive_id WHERE receive_item.item_id = sales_item.id AND receive_item.is_deleted = 0 AND receive.is_deleted = 0 GROUP BY receive_item.item_id)',
            'as' => 'received',
            'dt' => 3,
            'field' => 'received'
        ];
    }

    /**
     * Get released
     */
    protected function _get_released()
    {
        return [
            'db' => '(SELECT SUM(qty) FROM release_item LEFT JOIN `release` ON `release`.id = release_item.release_id WHERE release_item.item_id = sales_item.id AND release_item.is_deleted = 0 AND `release`.is_deleted = 0 GROUP BY release_item.item_id)',
            'as' => 'released',
            'dt' => 4,
            'field' => 'released'
        ];
    }

    /**
     * Get inventory
     */
    protected function _get_inventory()
    {
        return [
            'db' => 'id',
            'as' => 'inventory',
            'dt' => 5,
            'field' => 'inventory',
            'formatter' => function ($d, $row) {
                return $row['received'] - $row['released'];
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                    $res .= anchor('sales_items/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
