+ function ($) {
	'use strict';

	function addItemRow(items, account_types) {
		var $newRow = $(itemRow());
		setAccountTypes($newRow, account_types);

		setItemList($newRow, items);

		var $itemTable = $('.item-table');
		var invoiceItems = $itemTable.find('.invoice_item');

		if (invoiceItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);

		setRowNumbers();
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="invoice_item">' +
				'<td>' +
					'<input type="hidden" name="invoice_item_' + rowId + '" value="' + rowId + '">' +
					'<input type="number" name="row_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="qty_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="text" name="unit_' + rowId + '" class="form-control go-suggest" value="" data-suggest="units">' +
				'</td>' +
				'<td>' +
					'<select name="item_' + rowId + '" class="form-control dropsearch" data-placeholder=""></select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="price_' + rowId + '" class="form-control" value="" min="0" step="any">' +
				'</td>' +
				'<td>' +
					'<input type="number" name="amount_' + rowId + '" class="form-control-plaintext" value="" min="0" step="any" readonly>' +
				'</td>' +
		        '<td>' +
					'<input type="text" name="rs_' + rowId + '" class="form-control" value="">' +
				'</td>' +
				'<td>' +
				'<input type="number" name="alw_' + rowId + '" class="form-control" value="" step="any">' +
				'</td>' +
				'<td>' +
				'<select name="account_type_' + rowId + '" class="dropsearch form-control" data-placeholder="" style="width:100%;">' +
				'</td>' +
				'<td class="text-center">' +
					'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
				'</td>' +
			'</tr>';
	}

	function setAccountTypes($poItem, account_types) {
		$poItem.find('select[name^="account_type"]').select2({
		  data: account_types,
		  placeholder: $(this).attr('data-placeholder'),
		  allowClear: true,
		  width: '100%',
		  sorter: function (data) {
			return data.sort(function (a, b) {
			  return a.text.localeCompare(b.text);
			});
		  },
		});
	  }

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function setItemList($itemRow, items) {
		$itemRow.find('select[name^="item"]').select2({
			data: items,
			placeholder: $(this).attr('data-placeholder'),
			allowClear: true,
			width: '100%'
		});
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="9">No items added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var $itemTable = $('.item-table');
		var invoiceItems = $itemTable.find('.invoice_item');

		invoiceItems.each(function (index, row) {
			$(row).find('input[name^="row"]').val(index + 1);
		});
	}

	function getRowTotal($row) {
		var qty = parseFloat($row.find('input[name^="qty"]').val());
		if (isNaN(qty)) {
			qty = 0;
		}

		var price = parseFloat($row.find('input[name^="price"]').val());
		if (isNaN(price)) {
			price = 0;
		}

		var amount = price * qty;
		if (amount < 0) {
			amount = 0;
		}
		$row.find('input[name^="amount"]').val(amount > 0 ? amount.toFixed(2) : '');

		return amount;
	}

	function calculateTotal() {
		var subtotal = 0;
		$('.item-table').find('.invoice_item').each(function (_, invoiceItem) {
			subtotal += getRowTotal($(invoiceItem));
		});
		$('input[name="subtotal"]').val(subtotal.toFixed(2));

		var discount = $('input[name="discount"]').val();

		var total = subtotal - discount;
		$('input[name="total"]').val(total.toFixed(2));
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest('.invoice_item').remove();

		var $itemTable = $(event.delegateTarget);
		var invoiceItems = $itemTable.find('.invoice_item');
		if (invoiceItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		setRowNumbers();
		calculateTotal();
  	}

	function getCustomerInfo(customerId) {
	return $.ajax({
		url: BASE_URI + 'customers/info/' + customerId,
		type: 'GET',
		dataType: 'json',
	});
	}

  function getCustomerInfoByName(customerName) {
    return $.ajax({
      url: BASE_URI + 'customers/info_by_name/' + encodeURIComponent(customerName),
      type: 'GET',
      dataType: 'json',
    });
    }

	function getItemInfo(itemId,customerId) {
	return $.ajax({
		url: BASE_URI + 'sales_items/info/' + itemId + '/' + customerId,
		type: 'GET',
		dataType: 'json',
	});
	}

	function getItems() {
		return $.ajax({
			url: BASE_URI + 'sales_items/all_infos',
			type: 'GET',
			dataType: 'json'
		});
	}

	function getCustomerPrices() {
		return $.ajax({
			url: BASE_URI + 'sales_items/customer_prices',
			type: 'GET',
			dataType: 'json'
		});
	}

	function setData($row, item) {
		var unit = '';
		var price = '';

		if (item !== null) {
			unit = item.unit;
			price = item.price;
		}

		$row.find('input[name^="unit"]').val(unit);
		$row.find('input[name^="price"]').val(price);

		$.when(getCustomerPrices()).then(
			function (response) {
				var customerId = $('select[name="customer"]').val();
				var  customerPrices = response.customer_prices_dictionary;
				if (typeof customerPrices[customerId][item.id] != 'undefined') {
					$row.find('input[name^="price"]').val(customerPrices[customerId][item.id]);
				}
			}
		);
	}

		function deleteAttachment(attachmentId) {
		return $.ajax({
			url: BASE_URI + 'invoices/delete_attachment/' + attachmentId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function getAccountTypes() {
		return $.ajax({
			url: BASE_URI + 'account_types/all/sales_income', 	//rename here for later
			type: 'POST',
			dataType: 'json',
		});
	}

	$(function () {
		var items = [{
			id: '',
			text: ''
		}];

		var account_types = [
			{
				id: '',
				text: '',
			},
		];
		
		$.when(getAccountTypes()).then(function (response) {
			response.account_types.forEach(function (account_type) {
				account_type.text = account_type.account_name;
				account_type.id = account_type.account_name;
				account_types.push(account_type);
			});
		});

		$.when(getItems()).then(
			function (response) {
				response.items.forEach(function (item) {
					item.text = item.name;
					items.push(item);
				});
			}
		);

    if($('select[name="customer"]').val() !== ""){
      $('#btnSubmit').prop('disabled',false);
      $('.invoice-history').removeClass('d-none');
    }else{
      $('#btnSubmit').prop('disabled',true);
      $('.invoice-history').addClass('d-none');
      $('.item-table').find('tbody').html($(emptyRow()));
    }

    console.log($('select[name="customer"] option:selected').attr('data-walkin'));
    if($('select[name="customer"] option:selected').attr('data-walkin') === '0' && $('select[name="customer"]').val() === '16'){
      $('.credit-terms').addClass('d-none');
      $('.walkin-customer').removeClass('d-none');
    }else{
      $('.credit-terms').removeClass('d-none');
      $('.walkin-customer').addClass('d-none');
    }

		$('select[name="customer"]').on('change',function(event){
			var customerId = $(this).val();
      var isWalkin = $('option:selected', this).attr('data-walkin');

      $('input[name="address"]').val('');
      $('input[name="phone_no"]').val('');
      $('input[name="email"]').val('');
      $('input[name="available_credit"]').val('');
      $(".history").attr("href", '#');

      if(customerId !== '16' && isWalkin === '0'){

        $.when(getCustomerInfo(customerId)).then(
          function (response) {
          var availableCredit = response.customer.available_credit;

          if(isNaN(availableCredit) || availableCredit === null){
            availableCredit = 0;
          }
          $('input[name="address"]').val(response.customer.address);
          $('input[name="phone_no"]').val(response.customer.phone_no);
          $('input[name="email"]').val(response.customer.email);
          $('input[name="available_credit"]').val($.fn.addNumericCommas(availableCredit));
          $(".history").removeClass('d-none');
          $(".history").attr("href", BASE_URI + 'invoices/history/' + response.customer.id);
          }
        );

        $('.credit-terms').removeClass('d-none');
        $('.walkin-customer').addClass('d-none');
      }else{
        $('.credit-terms').addClass('d-none');
        $('.walkin-customer').removeClass('d-none');
      }

			//for enable and disable for add item
      var btnelem = $('#btnSubmit');

        if(customerId !== ""){
          btnelem.prop('disabled',false);
          $('.invoice-history').removeClass('d-none');
          $('.item-table').find('tbody').html($(emptyRow()));
        }else{
          btnelem.prop('disabled',true);
          $('.invoice-history').addClass('d-none');
        }

		});

    $('input[name="walkin_customer"]').on('change',function(event){
			var customerName = $(this).val();


        $.when(getCustomerInfoByName(customerName)).then(
          function (response) {
          $('input[name="address"]').val(response.customer.address);
          $('input[name="phone_no"]').val(response.customer.phone_no);
          $('input[name="email"]').val(response.customer.email);
          $(".history").removeClass('d-none');
          $(".history").attr("href", BASE_URI + 'invoices/history/' + response.customer.id);
          },
          function (jqXHR) {
            $(".history").addClass('d-none');
          }
        );

			//for enable and disable for add item
      var btnelem = $('#btnSubmit');

        if(customerName !== ""){
          btnelem.prop('disabled',false);
          $('.item-table').find('tbody').html($(emptyRow()));
        }else{
          btnelem.prop('disabled',true);
        }

		});

    $('input[name="terms"]').on('change',function(event){
			var terms = $('input[name="terms"]:checked').val();

        if(terms === "check"){
          $('.check-terms').removeClass('d-none');
          $('.non-credit-terms').removeClass('d-none');
        }else if(terms === "credit"){
          $('.non-credit-terms').addClass('d-none');
          $('.check-terms').addClass('d-none');
        }else{
          $('.check-terms').addClass('d-none');
          $('.non-credit-terms').removeClass('d-none');
        }

		}).trigger('change');

		$('.action-add-item').click(function () {
			addItemRow(items, account_types);
		});

		$('.item-table')
			.on('change', 'select[name^="item"]', function (event) {

				var invoiceItem = $(event.currentTarget).closest('.invoice_item');
        var customerId = $('select[name="customer"').val();

				$.when(getItemInfo(event.currentTarget.value, customerId)).then(
					function (response) {
						setData(invoiceItem, response.item);
					}
				)
		})
		.on('click', '.action-delete-item', function (event) {
			removeItemRow(event)
		})
		.on('change', 'input[name^="qty"]', calculateTotal)
		.on('change', 'input[name^="price"]', calculateTotal);

		$('input[name="discount"]').change(calculateTotal);

		calculateTotal();

		    $('.attachment-table').on('click', '.action-delete-attachment', function (event) {
      var cols = $(event.currentTarget).closest('tr.attachment').find('td');
      var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

      alertify.confirm(
					'Are you sure you want to delete ' + docNo + '?',
					function () {
						var attachmentId = event.currentTarget.getAttribute('data-id');
						deleteAttachment(attachmentId);
            location.reload();
					}
				).setHeader('<em>Delete Attachment</em>');
    });
	});

}(jQuery);
