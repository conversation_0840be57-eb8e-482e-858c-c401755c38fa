<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Name</label>

						<select id = "nameSelect" class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach($customers as $customer):?>
								<option value="<?=$customer->id?>" <?=set_select('customer', $customer->id);?>><?=ucwords($customer->name)?></option>
							<?php endforeach;?>
						</select>
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group walkin-customer d-none">
						<label for="walkin_customer" class="control-label">Customer Name</label>

						<input type="text" name="walkin_customer" class="form-control go-suggest" data-suggest="walkin_customer" value="<?=set_value('walkin_customer');?>">

						<div class="red-text flash-message">
							<?=form_error('walkin_customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no" class="control-label">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>Billing Statement Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="billing_date" class="control-label">Billing Date</label>

						<input type="text" name="billing_date" class="form-control datepicker" value="<?=set_value('billing_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('billing_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>
                    <input type="hidden" name="grand_total" class="form-control" value="<?=set_value('grand_total');?>">
					<div class="col-md-12 form-group">
						<label for="attachments" class="control-label">Upload Attachments</label>

						<input type="file" name="attachments[]" class="form-control-file" accept=".pdf, image/*"  multiple="">

						<div class="red-text flash-message">
							<?=form_error('attachments');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Billing Statement Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
                                    <th class="text-center" style="width: 10%;">Item No.</th>

                                    <th class="text-center" style="width: 20%;">Invoice No.</th>
                                    
                                    <th class="text-center" style="width: 10%;">Total</th>

                                    <th class="text-center" style="width: 10%;">Action</th>
							</thead>

							<tbody>
							<?php
                                if (!empty($billing_statement_item_ids)):
									foreach ($billing_statement_item_ids as $i => $billing_statement_item_id):
                            ?>
										<tr class="billing_statement_item">
											<td>
												<?php $fn_billing_statement_item = 'billing_statement_item_' . $billing_statement_item_id;?>
												<input type="hidden" name="<?=$fn_billing_statement_item;?>" value="<?=set_value($fn_billing_statement_item, $billing_statement_item_id);?>">

												<?php $fn_row = 'row_' . $billing_statement_item_id;?>
												<input type="number" name="<?=$fn_row;?>" class="form-control-plaintext" value="<?=$i + 1;?>" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_billing_statement_item);?>
												</div>
											</td>

											<td>
												<?php $fn_invoice_no = 'invoice_no_' . $billing_statement_item_id;?>

												<select name="<?=$fn_invoice_no;?>" class="form-control dropsearch"  style="width: 100%"  data-placeholder="">
													<option></option>
													<?php foreach($invoices as $invoice):?>
														<option value="<?= $invoice->id; ?>" <?=set_select($fn_invoice_no, $invoice->id); ?>><?= $invoice->invoice_no; ?></option>
													<?php endforeach;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_invoice_no);?>
												</div>
											</td>
`
											<td>
												<?php $fn_total = 'total_' . $billing_statement_item_id;?>
												<input type="number" name="<?=$fn_total;?>" class="form-control" value="<?=set_value($fn_total);?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_total);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="12">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
                            <tfoot>
								<tr>
									<th class="text-right" colspan="2">GRAND TOTAL</th>
									<th class="text-right footer_total">
									</th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item" disabled>Add Invoices</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('billing_statements/manager')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
