<div class="sect container">
	<div class="col-md-8 offset-md-1">
		<form role="form" method="POST" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title"><?=$title;?></h1>

			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-6 form-group">
						<label for="full_name">Full Name</label>

						<input type="text" name="full_name" class="form-control" value="<?=set_value('full_name');?>">

						<div class="red-text flash-message">
							<?=form_error('full_name');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="username">Username</label>

						<input type="text" name="username" class="form-control" value="<?=set_value('username');?>">

						<div class="red-text flash-message">
							<?=form_error('username');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="password">Password</label>

						<input type="password" name="password" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('password');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="passconf">Confirm Password</label>

						<input type="password" name="passconf" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('passconf');?>
						</div>
					</div>

				</div>

				<div class="row">
					<div class="col-md-6 form-group">
						<label for="role">Role</label>

						<select name="role" class="form-control dropsearch" data-placeholder="">
							<option></option>
						<?php foreach ($roles as $role): ?>
							<option value="<?=$role->id;?>" <?=set_select('role', $role->id);?>><?=$role->name;?></option>
						<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('role');?>
						</div>
					</div>
				</div>

				<div class="row">
    					<div class="col-md-6">
    						<label for="signature" class="font-weight-bold">Upload Signature</label>

    						<div class="signature-wrapper">
								<input type="file" id="signature" name="signature" class="form-control-file" accept=".png">
    						</div>
    					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>

				<a href="<?=site_url('users/manager');?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
