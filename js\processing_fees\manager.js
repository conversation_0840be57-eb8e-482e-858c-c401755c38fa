+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(processing_feeId, dataTable) {
		$.when(receiveRow(processing_feeId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(processing_feeId) {
		return $.ajax({
			url: BASE_URI + 'processing_fees/receive_processing_fee/' + processing_feeId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(processing_feeId, dataTable) {
		$.when(deleteRow(processing_feeId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(processing_feeId) {
		return $.ajax({
			url: BASE_URI + 'processing_fees/delete/' + processing_feeId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.processing_fee').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the processing_fee ' + docNo + '?',
					function () {
						var processing_feeId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(processing_feeId, dataTable);
					}
				).setHeader('<em>receive processing_fee</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.processing_fee').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete processing fee ' + docNo + '?',
					function () {
						var processing_feeId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(processing_feeId, dataTable);
					}
				).setHeader('<em>Delete Processing Fee</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/processing_fees?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'processing_fees/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
