<div class="sect">
	<div class="col-md-12 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype='multipart/form-data'>
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Customer Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Name</label>

						<select id = "nameSelect" class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach($customers as $customer):?>
								<option value="<?=$customer->id?>" <?=set_select('customer', $customer->id);?>><?=ucwords($customer->name)?></option>
							<?php endforeach;?>
						</select>
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group walkin-customer d-none">
						<label for="walkin_customer" class="control-label">Customer Name</label>

						<input type="text" name="walkin_customer" class="form-control go-suggest" data-suggest="walkin_customer" value="<?=set_value('walkin_customer');?>">

						<div class="red-text flash-message">
							<?=form_error('walkin_customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="address" class="control-label">Address</label>

						<input type="text" name="address" class="form-control" value="<?=set_value('address');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('address');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="phone_no" class="control-label">Phone No.</label>

						<input type="text" name="phone_no" class="form-control" value="<?=set_value('phone_no');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('phone_no');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="email" class="control-label">Email</label>

						<input type="text" name="email" class="form-control" value="<?=set_value('email');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('email');?>
						</div>
					</div>

					<div class="col-md-4 form-group credit-terms d-none">
						<label for="available_credit" class="control-label">Available Credit</label>

						<input type="text" name="available_credit" class="form-control" value="<?=set_value('available_credit');?>" readonly="readonly">

						<div class="red-text flash-message">
							<?=form_error('available_credit');?>
						</div>
					</div>

					<div class="col-md-4 form-group cash_bond-history d-none">
						<label for="history" class="control-label"></label>

						<p><a href="#" class="history" target="_blank">View Cash Bond History</a></p>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>Cash Bond Info</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="cash_bond_date" class="control-label">CB Date</label>

						<input type="text" name="cash_bond_date" class="form-control datepicker" value="<?=set_value('cash_bond_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('cash_bond_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="terms" class="control-label">Terms</label>

						<div class="clearfix"></div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="terms" value="cash" <?=set_radio('terms', 'cash')?>>Cash
							</label>
						</div>

						<div class="form-check-inline">
							<label class="form-check-label">
								<input type="radio" class="form-check-input" name="terms" value="check" <?=set_radio('terms', 'check')?>>Check
							</label>
						</div>

						<div class="red-text flash-message">
							<?=form_error('terms');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks');?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="due_date" class="control-label">Due Date</label>

						<input type="hidden" name="grand_total" class="form-control" value="<?=set_value('grand_total');?>">
						<input type="text" name="due_date" class="form-control datepicker" value="<?=set_value('due_date');?>">

						<div class="red-text flash-message">
							<?=form_error('due_date');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="attachments" class="control-label">Upload Attachments</label>

						<input type="file" name="attachments[]" class="form-control-file" accept=".pdf, image/*"  multiple="">

						<div class="red-text flash-message">
							<?=form_error('attachments');?>
						</div>
					</div>
				</div>

            	<h5 class="crud-subtitle"><span>List of Billing Statements Connected to this CB</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">Item No.</th>

									<th class="text-center" style="width: 20%;">Invoice No.</th>

									<th class="text-center" style="width: 20%;">Total No. of Heads</th>

									<th class="text-center" style="width: 20%;">Unit Price</th>

									<th class="text-center" style="width: 20%;">Total CB</th>

									<th class="text-center" style="width: 10%;">Action</th>
							</thead>

							<tbody>
							<?php
                                if (!empty($cash_bond_item_ids)):
									foreach ($cash_bond_item_ids as $i => $cash_bond_item_id):
                            ?>
										<tr class="cash_bond_item">
											<td>
												<?php $fn_cash_bond_item = 'cash_bond_item_' . $cash_bond_item_id;?>
												<input type="hidden" name="<?=$fn_cash_bond_item;?>" value="<?=set_value($fn_cash_bond_item, $cash_bond_item_id);?>">

												<?php $fn_row = 'row_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_row;?>" class="form-control-plaintext" value="<?=$i + 1;?>" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_cash_bond_item);?>
												</div>
											</td>

											<td>
												<?php $fn_invoice_no = 'invoice_no_' . $cash_bond_item_id;?>

												<select name="<?=$fn_invoice_no;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach($items as $item):?>
														<option value="<?= $item->id; ?>" <?=set_select($fn_invoice_no, $item->id); ?>><?= $item->invoice_no; ?></option>
													<?php endforeach;?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_invoice_no);?>
												</div>
											</td>

											<td>
												<?php $fn_total_heads_no = 'total_heads_no_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_total_heads_no;?>" class="form-control" value="<?=set_value($fn_total_heads_no);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_total_heads_no);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price);?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>
`
											<td>
												<?php $fn_total_cb = 'total_cb_' . $cash_bond_item_id;?>
												<input type="number" name="<?=$fn_total_cb;?>" class="form-control" value="<?=set_value($fn_total_cb);?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_total_cb);?>
												</div>
											</td>

											<td class="text-center">
												<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
											</td>
										</tr>
							<?php
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="12">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item" disabled>Add Item</button>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('cash_bonds/manager')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
