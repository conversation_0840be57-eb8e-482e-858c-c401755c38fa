<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_suppliers extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->live_sell_supplier_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_suppliers/manager'));
    }

    /**
     * Get units of item
     */
    public function get_data($supplier_id = null)
    {
        $where = [
            'id' => $supplier_id,
            'is_deleted' => 0
        ];
        if (!$supplier = $this->live_sell_supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'name' => $supplier->name,
                'address' => $supplier->address,
                'contact_number' => $supplier->phone_no,
                'email' => $supplier->email,
                'available_credit' => $supplier->available_credit,
                'credit_limit' => $supplier->credit_limit,
            ];
        }

        echo json_encode($response);
    }

    /**
     * Add Live Sell Supplier
     */
    public function add()
    {

        $config = [
            'upload_path' => FCPATH . 'assets/live_sell_suppliers',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($this->config->item('live_sell_suppliers/add')) && $this->_attempt_upload($config, 'attachment', false) && $id = $this->_attempt_add()) {

            $new_path = FCPATH . 'assets/live_sell_suppliers/' . $id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('live_sell_suppliers/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Add Live Sell Supplier';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'items' => $items,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'live_sell_suppliers/add', $data);
        }
    }

    /**
     * Callback: Check if supplier name exists
     */
    public function _is_supplier($supplier_name)
    {
        $where = [
            'name' => $supplier_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->live_sell_supplier_name) === strtolower($supplier_name)) {
            return true;
        }

        return empty($this->live_sell_supplier->select('', $where, 1));
    }

    /**
     * Edit live_sell_supplier
     */
    public function edit($live_sell_supplier_id)
    {
        $where = [
            'id' => $live_sell_supplier_id,
            'is_deleted' => 0
        ];
        $live_sell_supplier = $this->live_sell_supplier->select('', $where, 1) or show_404();

        $this->live_sell_supplier_name = $live_sell_supplier->name;

        $where = ['is_deleted' => 0];
        // $live_sell_supplier_prices = $this->live_sell_supplier_prices->select('live_sell_supplier_id, item_id, live_sell_supplier_price', $where);

        // if (!empty($live_sell_supplier_prices)) {
        //     //creates two dimenstional array for live_sell_supplier price checker
        //     foreach ($live_sell_supplier_prices as $live_sell_supplier_price) {
        //         $curr_live_sell_supplier_id = $live_sell_supplier_price->live_sell_supplier_id;
        //         $curr_item_id = $live_sell_supplier_price->item_id;
        //         $curr_live_sell_supplier_price = $live_sell_supplier_price->live_sell_supplier_price;
        //         $live_sell_supplier_prices_dictionary[$curr_live_sell_supplier_id][$curr_item_id] = $curr_live_sell_supplier_price;
        //     }
        // } else {
        //     $live_sell_supplier_prices_dictionary = null;
        // }

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'pdf|jpg|jpeg|png|heic|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($this->config->item('live_sell_suppliers/edit')) and $this->_attempt_upload($config, 'attachments', false) and $this->_attempt_edit($live_sell_supplier)) {

            $new_path = FCPATH . 'assets/asylum/live_sell_suppliers/' . $live_sell_supplier_id . '/';
            $this->_move_uploaded($new_path);

            redirect(site_url('live_sell_suppliers/manager'));
        } else {
            $this->title = 'Edit Live Sell supplier';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'form_error' => $form_error,
                'live_sell_supplier' => $live_sell_supplier,
                'items' => $items,
            ];
            $this->build_content('default', 'live_sell_suppliers/edit', $data);
        }
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Delete live_sell_supplier
     */
    public function delete($live_sell_supplier_id)
    {
        $where = [
            'id' => $live_sell_supplier_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_supplier = $this->live_sell_supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Supplier is not found.'
            ];
        } elseif (!$this->_attempt_delete($live_sell_supplier)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Live Sell Supplier is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Check live_sell_supplier remaining Credit Limit
     */
    public function check_credit($live_sell_supplier_id)
    {
        $used_credit = $this->live_sell_supplier->used_credit($live_sell_supplier_id) ?: 0;

        $where = [
            'id' => $live_sell_supplier_id,
            'is_deleted' => 0
        ];

        if (!$live_sell_supplier = $this->live_sell_supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Supplier is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Supplier is found.',
                'credit_limit' => $live_sell_supplier->credit_limit ? (float)$live_sell_supplier->credit_limit - (float)$used_credit->credit : 0
            ];
        }

        echo json_encode($response);
    }

    /**
     * Live Sell Suppliers
     */
    public function manager()
    {
        $this->title = 'Live Sell Suppliers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_suppliers/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'live_sell_suppliers/manager', $data);
    }


    /**
     * live_sell_suppliers SOA
     */
    public function soa()
    {
        $this->title = 'live_sell_suppliers';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'js/live_sell_suppliers/soa'
        ];

        $this->build_content('default', 'live_sell_suppliers/soa');
    }

    /**
     * live_sell_suppliers with negative credit
     */
    public function credit_report()
    {
        $this->title = 'Credit Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_suppliers/credit_report'
        ];

        $this->build_content('default', 'live_sell_suppliers/credit_report');
    }

    /**
     * live_sell_suppliers
     */
    public function live_sell_suppliers_sale()
    {
        $this->title = 'Sales Report';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/select2/dist/js/select2.full',
            'js/live_sell_suppliers/sales_report'
        ];

        $this->build_content('default', 'live_sell_suppliers/sales_report');
    }

    /**
     * Get Info of live_sell_supplier
     */
    public function info($live_sell_supplier_id = '')
    {
        $where = [
            'id' => $live_sell_supplier_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_supplier = $this->live_sell_supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Supplier is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Supplier is found.',
                'live_sell_supplier' => $live_sell_supplier
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of live sell supplier by name
     */
    public function info_by_name($live_sell_supplier_name = '')
    {
        $where = [
            'name' => urldecode($live_sell_supplier_name),
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        if (!$live_sell_supplier = $this->live_sell_supplier->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Supplier is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Supplier is found.',
                'live_sell_supplier' => $live_sell_supplier
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('live_sell_supplier');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'available_credit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        if (!$live_sell_supplier_id = $this->live_sell_supplier->insert($values)) {
            $this->_error = 'server_error';
        }

        return $live_sell_supplier_id;
    }

    /**
     * Attempt add attachment
     */
    // protected function _add_attachment($id)
    // {
    //     if (!empty($this->_uploaded)) {
    //         $values = [];
    //         foreach ($this->_uploaded as $uploaded) {
    //             $values[] = [
    //                 'live_sell_supplier_id' => $id,
    //                 'file_name' => $uploaded['file_name'],
    //                 'added_by' => $_SESSION['user']->id,
    //                 'added_on' => (new DateTime())->format('Y-m-d H:i:s')
    //             ];
    //         }
    //         if (!$attachment_id = $this->live_sell_supplier_attachment->insert_batch($values)) {
    //             $this->_error = 'server_error';
    //             return false;
    //         }

    //         return $attachment_id;
    //     } else {
    //         return true;
    //     }

    // }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($live_sell_supplier)
    {
        $where = [
            'id' => $live_sell_supplier->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'address' => $this->input->post('address'),
            'phone_no' => $this->input->post('phone_no'),
            'email' => $this->input->post('email'),
            'credit_limit' => floatval(str_replace(',', '', $this->input->post('credit_limit'))),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->live_sell_supplier->update($where, $values)) {
            $this->_error = 'server_error';
        }

        $new_item_prices = $this->input->post('item_price');

        foreach ($new_item_prices as $item_id => $new_item_price) {
            if ($new_item_price == null) {
                continue;
            }

            //Check whether a variable is empty. Also check whether the variable is set/declared:
            if (isset($live_sell_supplier_prices_dictionary[$live_sell_supplier->id][$item_id])) {
                $where = [
                    'item_id' => $item_id,
                    'live_sell_supplier_id' => $live_sell_supplier->id,
                    'is_deleted' => 0
                ];

                $values = [
                    'live_sell_supplier_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$is_updated = $this->live_sell_supplier_prices->update($where, $values)) {
                    $this->_error = 'server_error';
                }
            } else {
                $values = [
                    'item_id' => $item_id,
                    'live_sell_supplier_id' => $live_sell_supplier->id,
                    'live_sell_supplier_price' => (float) $new_item_price,
                    'updated_by' => $_SESSION['user']->id,
                    'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];

                if (!$this->live_sell_supplier_prices->insert($values)) {
                    $this->db->trans_rollback();
                    $this->_error = 'server_error';
                }
            }

        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($live_sell_supplier)
    {
        $where = [
            'id' => $live_sell_supplier->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $live_sell_supplier->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->live_sell_supplier->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }
}
