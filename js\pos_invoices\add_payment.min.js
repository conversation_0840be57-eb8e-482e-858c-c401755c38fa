!function(t){"use strict";t(function(){t('input[name="amount_paid"]').on("input",function(n){var n=t.fn.removeNumericCommas(n.currentTarget.value),a=t.fn.removeNumericCommas(t('input[name="total_payable"]').val()),e=t('input[name="balance"]'),i=n-a,i=(t('input[name="change"]').val(t.fn.addNumericCommas(i.toFixed(2)<0?0:i.toFixed(2))),a-n);e.val(t.fn.addNumericCommas(i.toFixed(2)<0?0:i.toFixed(2))),a<n?t(".invalid-amount").removeClass("d-none"):t(".invalid-amount").addClass("d-none")}).trigger("input")})}(jQuery);