<?php
defined('BASEPATH') or exit('No direct script access allowed');

class So_payments extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch So_payments
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'so_payment';
        $primary_key = 'so_payment.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_so_payment_date(),
            $this->_get_customers(),
            $this->_get_amount(),
            $this->_get_type(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
so_payment
LEFT JOIN paid_so ON paid_so.so_payment_id = so_payment.id AND paid_so.is_deleted = 0
LEFT JOIN order_slip ON order_slip.id = paid_so.invoice_id AND order_slip.is_deleted = 0
LEFT JOIN customer ON customer.id = order_slip.customer_id AND customer.is_deleted = 0
EOT;

        $where = <<<EOT
so_payment.is_deleted = 0
EOT;

    $from = $this->input->get('from', true);
    $to = $this->input->get('to', true);
    if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
        isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
    ) {
        $where .= <<<EOT

AND IFNULL(so_payment.so_payment_date, so_payment.added_on) BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
        }

    if($type = $this->input->get('so_payment_type')) {
        $where .= <<<EOT

AND so_payment.type = "$type"
EOT;
    }

        $group_by = <<<EOT
so_payment.id
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'so_payment.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'so_payment.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'so_payment';
            }
        ];
    }

    /**
     * Get so_payment date
     */
    protected function _get_so_payment_date()
    {
        return [
            'db' => 'so_payment.so_payment_date',
            'as' => 'so_payment_date',
            'dt' => 0,
            'field' => 'so_payment_date',
            'formatter' => function($d, $row){
                return date('F j, Y',strtotime($d));
            }

        ];
    }

    /**
     * Get Customers
     */
    protected function _get_customers()
    {
        return [
            'db' => 'GROUP_CONCAT(customer.name SEPARATOR "<br>")',
            'as' => 'customer',
            'dt' => 1,
            'field' => 'customer',
            'formatter' => function ($d, $row) {
                $res = '';
                $res .= '<ul>';

                $customers = explode('<br>', $d);
                // $customers = array_map('trim', $customers);
                // $customers=  array_filter($customers);

                foreach ($customers as $customer) {
                    $res .= '<li>' . $customer . '</li>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'FORMAT(so_payment.total_paid, 2)',
            'as' => 'amount',
            'dt' => 2,
            'field' => 'amount'
        ];
    }

    /**
     * Get type
     */
    protected function _get_type()
    {
        return [
            'db' => 'so_payment.type',
            'as' => 'so_payment_type',
            'dt' => 3,
            'field' => 'so_payment_type'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'so_payment.id',
            'as' => 'actions',
            'dt' => 4,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('so_payments/preview/' . $d, 'Preview', 'title="View" class="dropdown-item text-left"');
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
