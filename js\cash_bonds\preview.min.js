!function(e){"use strict";e(function(){e(".action-print").click(function(){window.print()}),e(document).on("click",".action-approve",function(i){var n=i.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to approve the Billing No. "+n+"?",function(){!function(i){e.ajax({url:BASE_URI+"invoices/approve/"+i,type:"POST",dataType:"json"})}(n),window.print(),document.location.href=BASE_URI+"invoices/preview/"+n}).setHeader("<em>Approve Billing</em>")}).on("click",".action-review",function(){let i=e(this).attr("data-invoiceid");alertify.confirm("Are you sure you want to mark invoice as Reviewed?",function(){e.when(function(i){return e.ajax({url:BASE_URI+"invoices/review/"+i,type:"POST",dataType:"json"})}(i)).then(function(e){e.isSuccess?window.location.href=BASE_URI+"invoices/manager":alertify.error(e.message)})}).setHeader("<em>Mark as Reviewed</em>")})})}(jQuery);