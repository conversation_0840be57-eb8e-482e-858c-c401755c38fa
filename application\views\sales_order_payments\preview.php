<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<p class="text-left"><small>Customer : </small> <?=$order_slip->customer;?></p>
					<p class="text-left"><small>Remarks. : </small> <?=$order_slip->remarks;?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><small>Order Confirmation Date : </small> <?=date('m/d/Y', strtotime($order_slip->order_slip_date));?></p>
					<!-- <p class="text-left"><small>Prepared By : </small> <?=$order_slip->issuer;?></p> -->
				</div>
			</div>

            	<h5 class="crud-subtitle"><span>List of Order Slip Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 25%;">Item</th>

									<th class="text-center" style="width: 15%;">Unit</th>

									<th class="text-center" style="width: 15%;">Total Qty</th>
								</tr>
							</thead>

							<tbody>
							<?php
								if (!empty($order_slip_items)):
									foreach ($order_slip_items as $order_slip_item):
							?>
										<tr class="order_slip_item">
											<td><?=$order_slip_item->item;?></td>

											<td><?=$order_slip_item->unit;?></td>

											<td class="text-right"><?=floatval($order_slip_item->qty);?></td>
										</tr>
							<?php
									endforeach;
								else:
							?>
									<tr class="no-item">
										<td class="text-center" colspan="6">No items added yet.</td>
									</tr>
							<?php
								endif;
							?>
							</tbody>
						</table>
					</div>
				</div>

				<?php if(!empty($order_slip->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>order_slip Attachments</span></h5>
						<?php $attachments = explode('🔥',$order_slip->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/order_slips/'.$order_slip->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<br />

				<div class="row">
					<div class="col-sm-3 text-center mt-auto" style="margin-top: 30px;">
						<?=$order_slip->issuer;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Order Confirmed By</p>
					</div>
				</div>

				<div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('order_slips');?>">Close</a>

				</div>
			</div>

			<input type="hidden" name="order_slip_id" value="<?=$order_slip->id?>">
		</form>
	</div>
</div>