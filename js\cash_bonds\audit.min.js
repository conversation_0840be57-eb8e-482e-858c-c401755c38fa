!function(t){"use strict";function e(){return new Promise((e,a)=>{var n='<input type="radio" name="auditStatus" value="complete" checked> Complete &nbsp; &nbsp;';n+='<input type="radio" name="auditStatus" value="incomplete"> Incomplete<br>',n+="&nbsp;",n+='<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100"></textarea>',n+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, .jpeg, image/*" multiple>',alertify.confirm(n,function(n,s){var i=t("#auditRemarks").val(),r=t('input[name="auditStatus"]:checked').val(),c=t("#attachments")[0].files;""!==i.trim()?e({remarks:i,status:r,files:c}):a("Missing Remarks: Audit remarks cannot be empty.")},function(){a("Audit canceled.")}).setting({reverseButtons:!0,closable:!1,title:"Audit Cash Bond"}).show()})}function a(e,a,n,s){var i=new FormData;i.append("cash_bond_id",e),i.append("audit_remarks",a),i.append("audit_status",n);for(var r=0;r<s.length;r++)i.append("attachments[]",s[r]);return t.ajax({url:BASE_URI+"cash_bonds/mark_audited/",type:"POST",data:i,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Cash Bond Audited."}))}function n(e){return new Promise((a,n)=>{var s='<input type="radio" name="auditStatus" value="complete"> Complete &nbsp; &nbsp;';s+='<input type="radio" name="auditStatus" value="incomplete" checked> Incomplete<br>',s+="&nbsp;",s+='<textarea id="auditRemarks" placeholder="Enter remarks..." class="w-100">'+e+"</textarea>",s+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>',alertify.confirm(s,function(e,s){var i=t("#auditRemarks").val(),r=t('input[name="auditStatus"]:checked').val(),c=t("#attachments")[0].files;""!==i.trim()?a({remarks:i,status:r,files:c}):n("Missing Remarks: Audit remarks cannot be empty.")},function(){n("Audit canceled.")}).setting({reverseButtons:!0,closable:!1,title:"Audit Cash Bond"}).show()})}function s(e,a,n,s){var i=new FormData;i.append("cash_bond_id",e),i.append("audit_remarks",a),i.append("audit_status",n);for(var r=0;r<s.length;r++)i.append("attachments[]",s[r]);return t.ajax({url:BASE_URI+"cash_bonds/update_mark_audited/",type:"POST",data:i,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Cash Bond Audited."}))}t(function(){t("#auditBtn").click(function(){var n=t('input[name="cash_bond_id"]').val();e().then(({remarks:t,status:e,files:s})=>a(n,t,e,s)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})}),t("#auditUpdateBtn").click(function(){var e=t('input[name="cash_bond_id"]').val(),a=t('input[name="audit_remarks"]').val();n(a).then(({remarks:t,status:a,files:n})=>s(e,t,a,n)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})})}(jQuery);