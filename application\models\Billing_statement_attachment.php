<?php
class Billing_statement_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'billing_statement_attachment';
        parent::__construct();
    }

    /**
     * Get Attachment by Billing_statement Id
     */
    public function get_by_id($id){

    $sql = <<<EOT
SELECT billing_statement_attachment.*
FROM billing_statement_attachment
WHERE billing_statement_attachment.is_deleted = 0
AND billing_statement_attachment.billing_statement_id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */