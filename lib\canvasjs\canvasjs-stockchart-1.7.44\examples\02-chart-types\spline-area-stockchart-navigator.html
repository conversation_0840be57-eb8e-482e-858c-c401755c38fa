<!DOCTYPE HTML>
<html>
<head>
<script type="text/javascript" src="https://canvasjs.com/assets/script/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../../canvasjs.stock.min.js"></script>
<script type="text/javascript">
window.onload = function () {
  var dataPoints = [];
  var stockChart = new CanvasJS.StockChart("stockChartContainer",{
    title:{
      text:"Exchange Rate for EUR to USD"
    },
    charts: [{
      data: [{
        type: "splineArea",
        color: "#3698C5",
        yValueFormatString: "€1 = $#,###.##",
        dataPoints : dataPoints
      }]
    }],
    navigator: {
      slider: {
        minimum: new Date(2015, 00, 01),
        maximum: new Date(2016, 00, 01)
      }
    }
  });
  $.getJSON("https://canvasjs.com/data/gallery/stock-chart/usdeur.json", function(data) {
    for(var i = 0; i < data.length; i++){
      dataPoints.push({x: new Date(data[i].date), y: Number(data[i].price)});
    }
    stockChart.render();
  });
}
</script>
</head>
<body>
<div id="stockChartContainer" style="height: 400px; max-width: 920px; margin: 0px auto;"></div>
</body>
</html>