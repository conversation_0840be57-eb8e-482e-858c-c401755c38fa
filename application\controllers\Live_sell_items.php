<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_items extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();

        // This is used for validation of the name in the edit function
        $this->live_sell_item_name = null;
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_items/manager'));
    }

    /**
     * Get units of item
     */
    public function unit_of($item_id = null)
    {
        $where = [
            'id' => $item_id,
            'is_deleted' => 0
        ];
        if (!$item = $this->live_sell_item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'No item is found.'
            ];
        } else {
            $response = [
                'message' => 'Unit is found.',
                'unit' => $item->unit,
            ];
        }

        echo json_encode($response);
    }

    public function search_by_item()
    {
        $term = $this->input->get('term', true);

        $where = <<<EOT
CONCAT(name) LIKE '%{$term}%'
AND is_deleted = 0
EOT;

        $this->live_sell_item->db->distinct();
        $items = $this->live_sell_item->select('', $where);

        $response =  $items;

        echo json_encode($response);
    }

    /**
     * Add live_sell_item
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('live_sell_items/add')) && $id = $this->_attempt_add()) {

            $new_path = FCPATH . 'assets/live_sell_items/' . $id . '/';

            redirect(site_url('live_sell_items/manager'));
        } else {

            $this->title = 'Add Live Sell Item';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'items' => $items,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'live_sell_items/add', $data);
        }
    }

    /**
     * Callback: Check if item name exists
     */
    public function _is_item($item_name)
    {
        $where = [
            'name' => $item_name,
            'is_deleted' => 0
        ];

        if(strtolower($this->live_sell_item_name) === strtolower($item_name)) {
            return true;
        }

        return empty($this->live_sell_item->select('', $where, 1));
    }

    /**
     * Edit live_sell_item
     */
    public function edit($live_sell_item_id)
    {
        $where = [
            'id' => $live_sell_item_id,
            'is_deleted' => 0
        ];
        $live_sell_item = $this->live_sell_item->select('', $where, 1) or show_404();

        $this->live_sell_item_name = $live_sell_item->name;

        $where = ['is_deleted' => 0];

        if ($this->_validate_form($this->config->item('live_sell_items/edit')) and $this->_attempt_edit($live_sell_item)) {

            $new_path = FCPATH . 'assets/asylum/live_sell_items/' . $live_sell_item_id . '/';

            redirect(site_url('live_sell_items/manager'));
        } else {
            $this->title = 'Edit Live Sell item';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = ['is_deleted' => 0];
            $items = $this->item->select('', $where);

            $data = [
                'form_error' => $form_error,
                'live_sell_item' => $live_sell_item,
                'items' => $items,
            ];
            $this->build_content('default', 'live_sell_items/edit', $data);
        }
    }

    /**
     * Delete live_sell_item
     */
    public function delete($live_sell_item_id)
    {
        $where = [
            'id' => $live_sell_item_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_item = $this->live_sell_item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Item is not found.'
            ];
        } elseif (!$this->_attempt_delete($live_sell_item)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Live Sell Item is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * live_sell_items
     */
    public function manager()
    {
        $this->title = 'Live Sell items';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_items/manager'
        ];

        $data = [
            'type' => $this->input->get('type', true) ?: 'credit'
        ];

        $this->build_content('default', 'live_sell_items/manager', $data);
    }

    /**
     * Get Info of live_sell_item
     */
    public function info($live_sell_item_id = '')
    {
        $where = [
            'id' => $live_sell_item_id,
            'is_deleted' => 0
        ];
        if (!$live_sell_item = $this->live_sell_item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Item is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Item is found.',
                'live_sell_item' => $live_sell_item
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get Info of live_sell_item by name
     */
    public function info_by_name($live_sell_item_name = '')
    {
        $where = [
            'name' => urldecode($live_sell_item_name),
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        if (!$live_sell_item = $this->live_sell_item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Live Sell Item is not found.'
            ];
        } else {
            $response = [
                'message' => 'Live Sell Item is found.',
                'live_sell_item' => $live_sell_item
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('live_sell_item');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$live_sell_item_id = $this->live_sell_item->insert($values)) {
            $this->_error = 'server_error';
        }

        return $live_sell_item_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($live_sell_item)
    {
        $where = [
            'id' => $live_sell_item->id,
            'is_deleted' => 0
        ];

        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->live_sell_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($live_sell_item)
    {
        $where = [
            'id' => $live_sell_item->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $live_sell_item->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->live_sell_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
