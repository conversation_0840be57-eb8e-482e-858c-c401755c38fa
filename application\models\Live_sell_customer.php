<?php
class Live_sell_customer extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'live_sell_customer';
        parent::__construct();
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM live_sell_customer
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get credit limit
     */
    public function used_credit($live_sell_customer_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS credit
FROM pos_invoice
WHERE live_sell_customer_id = ?
AND terms = "credit"
AND is_deleted = 0
EOT;
        $binds = [$live_sell_customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get POS live_sell_customer balance
     */
    public function pos_live_sell_customer_balance($live_sell_customer_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS balance
FROM pos_invoice
WHERE live_sell_customer_id = ?
AND is_deleted = 0
EOT;
        $binds = [$live_sell_customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }    

    /**
     * Create New live_sell_customer
     */
    public function generate($name, $address, $phone_no, $email, $added_by)
    {
        $sql = <<<EOT
INSERT INTO live_sell_customer(name, address, phone_no, email, added_by) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    phone_no = VALUES(phone_no),
    email = VALUES(email),
    is_deleted = 0
EOT;
        $binds = [$name, $address, $phone_no, $email, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
