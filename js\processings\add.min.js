!function(i){"use strict";function e(e){var t,n=i((a=e,'<tr class="processing_item"><td><input type="hidden" name="classification_'+(t=function(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}())+'" value="'+a+'"><input type="hidden" name="processing_item_'+t+'" value="'+t+'"><select style="min-width:200px" width="200px" name="stock_keeping_unit_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="size_range_'+t+'" class="form-control go-suggest" value="" data-suggest="size_ranges" readonly></td><td><input type="text" name="by_unit_'+t+'" class="form-control go-suggest" value="" data-suggest="by_units" readonly></td><td><input type="number" name="crates_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="heads_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="kilos_'+t+'" class="form-control" value="" min="0" step="any"></td><td><input type="number" name="recovery_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>')),a=(s(n),o(n),i("."+e));0===a.find(".processing_item").length&&a.find(".no-item").remove(),a.find("tbody").append(n),n.find('select[name^="stock_keeping_unit_"]').on("change",function(e){e=e.currentTarget.value;i.when(l(e)).then(function(e){console.log(e),n.find('input[name^="size_range_"]').val(e.size_range),n.find('input[name^="by_unit_"]').val(e.by_unit)},function(){n.find('input[name^="size_range_"]').val(""),n.find('input[name^="by_unit_"]').val("")}),console.log(e)}),r()}function s(e){e.find('select[name^="item"]').select2({tags:!1,allowClear:!0,placeholder:i(this).attr("data-placeholder"),width:"280px",ajax:{url:BASE_URI+"live_sell_items/search_by_item",dataType:"json",type:"GET",data:function(e){return{term:e.term}},processResults:function(e){return{results:i.map(e,function(e){return{text:e.name,id:e.id}})}}}})}function o(e){e.find('select[name^="stock_keeping_unit_"]').select2({tags:!1,allowClear:!0,placeholder:i(this).attr("data-placeholder"),width:"200px",ajax:{url:BASE_URI+"stock_keeping_units/search_by_stock_keeping_unit",dataType:"json",type:"GET",data:function(e){return{term:e.term}},processResults:function(e){return{results:i.map(e,function(e){return{text:e.name,id:e.id}})}}}})}function r(){i(".item-table").find(".processing_item").each(function(e,t){i(t).find('input[name^="row"]').val(e+1)})}function n(e,t=null){e.find('input[name^="unit_"]').val(t)}function l(e){return i.ajax({url:BASE_URI+"stock_keeping_units/get_data/"+e,type:"GET",dataType:"json"})}i(function(){i(".action-add-item").on("click",function(){e(i(this).attr("data-table"))}),i(".item-table").on("click",".action-delete-item",function(e){var t;i((e=e).currentTarget).closest(".processing_item").remove(),(e=i(e.delegateTarget)).find(".processing_item").length<1&&(t=i('<tr class="no-item"><td class="text-center" colspan="7">No items added yet.</td></tr>'),e.find("tbody").html(t)),r()}).on("change",'select[name^="item"]',function(e){var t=i(e.currentTarget).closest(".processing_item"),e=e.currentTarget.value;i.when(i.ajax({url:BASE_URI+"live_sell_items/unit_of/"+e,type:"GET",dataType:"json"})).then(function(e){n(t,e.unit)},function(){n(t,null)}),console.log(e)}),i('select[name="customer"]').on("change",function(e){i(e.currentTarget).closest(".processing_item");e=e.currentTarget.value;i.when(i.ajax({url:BASE_URI+"live_sell_customers/get_data/"+e,type:"GET",dataType:"json"})).then(function(e){console.log(e),i('input[name="address"]').val(e.address),i('input[name="contact_number"]').val(e.contact_number),i('input[name="email"]').val(e.email),i('input[name="available_credit"]').val(e.available_credit)},function(){i('input[name="address"]').val(""),i('input[name="contact_number"]').val(""),i('input[name="email"]').val(""),i('input[name="available_credit"]').val("")}),console.log(e)}),i(".processing_item").each(function(e,t){i(t).find("option").not(":selected").remove(),s(i(t)),o(i(t))}),i('select[name^="stock_keeping_unit_"]').on("change",function(e){let t=i(this);e=e.currentTarget.value;i.when(l(e)).then(function(e){t.parent().parent().find('input[name^="size_range_"]').val(e.size_range),t.parent().parent().find('input[name^="by_unit_"]').val(e.by_unit)},function(){t.parent().parent().find('input[name^="size_range_"]').val(""),t.parent().parent().find('input[name^="by_unit_"]').val("")}),console.log(e)})})}(jQuery);