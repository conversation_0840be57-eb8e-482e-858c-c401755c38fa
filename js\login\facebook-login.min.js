"use strict";function statusChangeCallback(t){"connected"===t.status?attemptLogin(t):document.getElementById("error").innerHTML="Please log into this app."}function checkLoginState(){FB.getLoginStatus(function(t){statusChangeCallback(t)})}function attemptLogin(t){var e=t.authResponse.accessToken;window.location.href=BASE_URI+"login/fb_login?token="+e}window.fbAsyncInit=function(){FB.init({appId:"256961608204790",cookie:!0,xfbml:!0,version:"v2.8"}),FB.AppEvents.logPageView(),FB.getLoginStatus(function(t){statusChangeCallback(t)})},function(t,e,n){var o,a=t.getElementsByTagName(e)[0];t.getElementById(n)||(o=t.createElement(e),o.id=n,o.src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId=256961608204790&autoLogAppEvents=1",a.parentNode.insertBefore(o,a))}(document,"script","facebook-jssdk");