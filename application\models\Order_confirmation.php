<?php
class Order_confirmation extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'order_confirmation';
        parent::__construct();
    }

    /**
     * Get order_confirmation By ID
     */
    public function get_by_id($order_confirmation_id)
    {
        $sql = <<<EOT
SELECT order_confirmation.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit
FROM order_confirmation
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
WHERE order_confirmation.id = ?
    AND order_confirmation.is_deleted = 0
EOT;
        $binds = [$order_confirmation_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM order_confirmation
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid order_confirmations
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT order_confirmation.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM order_confirmation
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_confirmation.added_by
WHERE order_confirmation.paid_amount < order_confirmation.total
    AND order_confirmation.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get order_confirmations Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT order_confirmation.id, order_confirmation.order_confirmation_date, order_confirmation.customer_id, order_confirmation.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM order_confirmation
LEFT JOIN (
    SELECT payment.payment_date, paid_order_confirmation.order_confirmation_id, paid_order_confirmation.amount
    FROM payment
    LEFT JOIN paid_order_confirmation ON paid_order_confirmation.payment_id = payment.id
    LEFT JOIN order_confirmation ON order_confirmation.id = paid_order_confirmation.order_confirmation_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND order_confirmation.customer_id = ?
) payment ON payment.order_confirmation_id = order_confirmation.id
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_confirmation.added_by
WHERE order_confirmation.order_confirmation_date < ?
    AND customer.id = ?
    AND order_confirmation.is_deleted = 0
GROUP BY order_confirmation.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get order_confirmations Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT order_confirmation.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM order_confirmation
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_confirmation.added_by
WHERE order_confirmation.order_confirmation_date BETWEEN ? AND ?
    AND order_confirmation.customer_id = ?
    AND order_confirmation.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get order_confirmations BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT order_confirmation.id, order_confirmation.order_confirmation_no, order_confirmation.terms, order_confirmation.order_confirmation_date, order_confirmation.customer_id, order_confirmation.total AS charge, order_confirmation.total, order_confirmation.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM order_confirmation
LEFT JOIN (
    SELECT payment.payment_date, paid_order_confirmation.order_confirmation_id, paid_order_confirmation.amount
    FROM payment
    LEFT JOIN paid_order_confirmation ON paid_order_confirmation.payment_id = payment.id
    LEFT JOIN order_confirmation ON order_confirmation.id = paid_order_confirmation.order_confirmation_id
    WHERE payment.payment_date
        AND order_confirmation.customer_id = ?
) payment ON payment.order_confirmation_id = order_confirmation.id
LEFT JOIN live_sell_customer AS customer ON customer.id = order_confirmation.customer_id
LEFT JOIN user AS issuer ON issuer.id = order_confirmation.added_by
WHERE customer.id = ?
    AND order_confirmation.is_deleted = 0
GROUP BY order_confirmation.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */