<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Receivables extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Receivables
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'customer';
        $primary_key = 'customer.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_customer(),
            $this->_get_balance()
        ];

        $joins = <<<EOT
customer
LEFT JOIN invoice ON invoice.customer_id = customer.id
EOT;

        $where = <<<EOT
customer.is_deleted = 0
    AND invoice.is_deleted = 0
EOT;

    if($type = $this->input->get('payment_type')) {
        $where .= <<<EOT

AND invoice.terms = "$type"
EOT;
    }

    if($status = $this->input->get('invoice_status')) {
        $where .= <<<EOT

AND invoice.status = "$status"
EOT;
    }

        $group_by = <<<EOT
customer.id
EOT;

        $having = <<<EOT
balance > 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by, $having)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'customer.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'customer.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'customer';
            }
        ];
    }

    /**
     * Get customer
     */
    // protected function _get_customer()
    // {
    //     return [
    //         'db' => 'customer.name',
    //         'as' => 'customer_name',
    //         'dt' => 0,
    //         'field' => 'customer_name',
    //         'formatter' => function ($d, $row) {
    //             $res = '<div>';
    //             // $res .= "<span class='d-none' id='redirect' data-href='soas/preview/" . $row['id'] . "'></span> " . $d;
    //             $res .= anchor('soas/preview/'. $row['id'],$d,"target='_blank'");
    //             $res .= '</div>';
    //             return $res;
    //         }
    //     ];
    // }
    // protected function _get_customer()
    // {
    //     return [
    //         'db' => 'customer.name',
    //         'as' => 'customer',
    //         'dt' => 0,
    //         'field' => 'customer'
    //     ];
    // }
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer_name',
            'dt' => 0,
            'field' => 'customer_name',
            'formatter' => function ($d, $row) {
                $dateFrom = $this->input->get('date_from');
                $dateTo = $this->input->get('date_to');
                
                // Construct the URL for the SOA page with customer ID
                $url = base_url('soas/preview/' . $row['id']);
                
                // Append date filters to the URL if they exist
                if ($dateFrom && $dateTo) {
                    $url .= '?from_date=' . urlencode($dateFrom) . '&to_date=' . urlencode($dateTo);
                }
    
                // Create the anchor tag with the constructed URL
                $res = '<div>';
                $res .= anchor($url, $d, "target='_blank'");
                $res .= '</div>';
                return $res;
            }
        ];
    }
    


    /**
     * Get balance
     */
    protected function _get_balance()
    {
        return [
            'db' => 'SUM(invoice.total - invoice.paid_amount)',
            'as' => 'balance',
            'dt' => 1,
            'field' => 'balance',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Preview Statement of Account
     */
    // public function preview($customer_id)
    // {
    //     // $where = [
    //     //     'id' => $soa_id,
    //     //     'is_deleted' => 0
    //     // ];
    //     // $soa = $this->soa->select('', $where, 1) or show_404();

    //     // $this->title = 'Statement of Account #' . $soa->soa_no;

    //     $where = [
    //         'id' => $customer_id
    //     ];
    //     $customer = $this->customer->select('', $where, 1);

    //     // $entries = $this->soa_entry->get_by_soa($soa_id);

    //     $from_date = $this->input->get('from_date')?DateTime::createFromFormat('m/d/Y', $this->input->get('from_date'))->format('Y-m-d'):null;
    //     $to_date = $this->input->get('from_date')?DateTime::createFromFormat('m/d/Y', $this->input->get('to_date'))->format('Y-m-d'):null;

    //     $entries = $this->invoice->get_by_customer($customer_id, $from_date, $to_date);

    //     $data = [
    //         'date_from' => $from_date,
    //         'date_to' => $to_date,
    //         'customer' => $customer,
    //         'entries' => $entries
    //     ];
    //     $this->build_content('default', 'soas/preview', $data);
    // }
}
