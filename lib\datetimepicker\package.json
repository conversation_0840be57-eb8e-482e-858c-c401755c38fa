{"name": "j<PERSON>y-datetimepicker", "version": "2.5.4", "description": "jQuery Plugin DateTimePicker it is DatePicker and TimePicker in one", "main": "build/jquery.datetimepicker.full.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "concat": "concat-cli -f bower_components/php-date-formatter/js/php-date-formatter.js jquery.datetimepicker.js bower_components/jquery-mousewheel/jquery.mousewheel.js -o build/jquery.datetimepicker.full.js", "minify": "uglifyjs jquery.datetimepicker.js -c -m -o build/jquery.datetimepicker.min.js && uglifycss jquery.datetimepicker.css > build/jquery.datetimepicker.min.css", "minifyconcat": "uglifyjs build/jquery.datetimepicker.full.js -c -m -o build/jquery.datetimepicker.full.min.js", "github": "git add --all  && git commit -m \"New version %npm_package_version% \" && git tag %npm_package_version% && git push --tags origin HEAD:master && npm publish", "build": "npm run minify && npm run concat && npm run minifyconcat"}, "repository": {"type": "git", "url": "https://github.com/xdan/datetimepicker.git"}, "keywords": ["jquery-plugin", "calendar", "date", "time", "datetime", "datepicker", "timepicker"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://xdsoft.net/)", "license": "MIT", "bugs": {"url": "https://github.com/xdan/datetimepicker/issues"}, "homepage": "https://github.com/xdan/datetimepicker", "dependencies": {"jquery": ">= 1.7.2", "jquery-mousewheel": ">= 3.1.13", "php-date-formatter": ">= 1.3.3"}, "devDependencies": {"concat": "azer/concat", "concat-cli": "^4.0.0", "uglifycss": "0.0.20", "uglifyjs": "^2.4.10"}}