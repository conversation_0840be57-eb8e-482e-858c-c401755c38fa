!function($){"use strict";$(function(){var a=$(".img-preview"),c=a.attr("src"),b=$('input[name="image_upload"]');a.click(function(){b.click()}),b.change(function(a){!function(a,c){var d=$(".img-preview");if(a.files&&a.files[0]){var b=new FileReader;b.onload=function(a){d.attr("src",a.currentTarget.result)},b.readAsDataURL(a.files[0])}else d.attr("src",c)}(a.currentTarget,c)}),$("select[name = 'applied_to']").change(function(){$("select[name = 'account_type']").empty(),"Purchase Order"==$(this).val()?$("select[name = 'account_type']").select2({data:[{id:"Cost of sales",text:"Cost of sales"},{id:"Current assets",text:"Current assets"},{id:"Current liabilities",text:"Current liabilities"},{id:"Equity",text:"Equity"},{id:"Fixed asset",text:"Fixed asset"},{id:"Operating expenses",text:"Operating expenses"},{id:"Other current assets",text:"Other current assets"},{id:"Other current liabilities",text:"Other current liabilities"}],placeholder:$(this).attr("data-placeholder"),width:"100%",allowClear:!0}):"Sales Income"==$(this).val()&&$("select[name = 'account_type']").select2({data:[{id:"Revenue",text:"Revenue"}],placeholder:$(this).attr("data-placeholder"),width:"100%",allowClear:!0}),$("select[name = 'account_type']").val($("select[name = 'account_type']").attr("data-default")).trigger("change")}),$("select[name = 'applied_to']").trigger("change"),$("select[name = 'financial_statement']").change(function(){$("select[name = 'balance_sheet_category']").empty(),"Balance sheet"==$(this).val()?$("select[name = 'balance_sheet_category']").select2({data:[{id:"Cash on hand and in bank",text:"Cash on hand and in bank"},{id:"Cumulative earnings - Appropriated",text:"Cumulative earnings - Appropriated"},{id:"Cumulative earnings - Unappropriated",text:"Cumulative earnings - Unappropriated"},{id:"Income tax payables",text:"Income tax payables"},{id:"Inventories",text:"Inventories"},{id:"Other assets",text:"Other assets"},{id:"Other current assets",text:"Other current assets"},{id:"Property and equipment - Net",text:"Property and equipment - Net"},{id:"Share capital",text:"Share capital"},{id:"Trade and other payables",text:"Trade and other payables"},{id:"Trade and other receivables",text:"Trade and other receivables"}],placeholder:$(this).attr("data-placeholder"),width:"100%",allowClear:!0}):"Income statement"==$(this).val()&&$("select[name = 'balance_sheet_category']").select2({data:[{id:"",text:""}],placeholder:$(this).attr("data-placeholder"),width:"100%",allowClear:!0}),$("select[name = 'balance_sheet_category']").val($("select[name = 'balance_sheet_category']").attr("data-default")).trigger("change")}),$("select[name = 'financial_statement']").trigger("change")})}(jQuery)