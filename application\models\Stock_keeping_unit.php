<?php
class Stock_keeping_unit extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'stock_keeping_unit';
        parent::__construct();
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM stock_keeping_unit
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get credit limit
     */
    public function used_credit($stock_keeping_unit_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS credit
FROM pos_invoice
WHERE stock_keeping_unit_id = ?
AND terms = "credit"
AND is_deleted = 0
EOT;
        $binds = [$stock_keeping_unit_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get POS stock_keeping_unit balance
     */
    public function pos_stock_keeping_unit_balance($stock_keeping_unit_id)
    {
        $sql = <<<EOT
SELECT SUM(total - paid_amount) AS balance
FROM pos_invoice
WHERE stock_keeping_unit_id = ?
AND is_deleted = 0
EOT;
        $binds = [$stock_keeping_unit_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }    

    /**
     * Create New stock_keeping_unit
     */
    public function generate($name, $address, $phone_no, $email, $added_by)
    {
        $sql = <<<EOT
INSERT INTO stock_keeping_unit(name, address, phone_no, email, added_by) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    phone_no = VALUES(phone_no),
    email = VALUES(email),
    is_deleted = 0
EOT;
        $binds = [$name, $address, $phone_no, $email, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
