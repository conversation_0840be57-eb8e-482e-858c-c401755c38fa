!function(t){"use strict";function a(a,n,i){var r=e(),o=m.find("tbody #order_summary"+a);if(o.length>0)o.find(".btn-plus").click();else{var d="<tr class='order_summary' data-id='"+r+"' id='order_summary"+a+"'><td><input type='hidden' data-modifiers='"+n+"' name='order_"+r+"' id='item"+r+"' value='"+r+"' class='form-control-plaintext'><p>"+n+"</p></td><td><input type='hidden' name='pos_item_id_"+r+"' value='"+a+"'><div class='input-group'><span class='input-group-btn'> <button data-id='"+r+"' class='btn btn-minus' type='button'>-</button></span><input type='text' id='qty' value='1' name='qty_"+r+"' class='form-control qty text-center'><span class='input-group-btn'><button data-id='"+r+"' class='btn btn-plus' type='button'>+</button></span></div></td><td><input type='hidden' id='price"+r+"' name='price_"+r+"' class='price' value='"+t.fn.addNumericCommas(parseFloat(i).toFixed(2))+"'><input type='text' name='amount_"+r+"' id='amount"+r+"' class='form-control-plaintext amount text-right' value='"+t.fn.addNumericCommas(parseFloat(i).toFixed(2))+"' readonly></td><td><button class='btn btn-danger action-delete-order'><i class='fa fa-trash'></i></button></td></tr>";m.find("tbody").append(d)}}function e(){for(var t="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",e=0;e<32;e++)t+=a.charAt(Math.floor(Math.random()*a.length));return t}function n(a){t(a.currentTarget).closest(".order_summary").remove();var e=t(a.delegateTarget),n=e.find(".order_summary");if(n.length<1){var i=t(r());e.find("tbody").html(i)}}function i(){var a=0,e=0;t(".order_summary").each(function(){var a=t(this).closest(".order_summary"),n=a.find('input[name^="qty"]').val();isNaN(n)&&(n=0);var i=t.fn.removeNumericCommas(a.find('input[name^="price"]').val());isNaN(i)&&(i=0),e+=n*i});var n=parseFloat(t("#discount").val());isNaN(n)&&(n=0),a=e-n,t(".subtotal").val(t.fn.addNumericCommas(parseFloat(e).toFixed(2))),t("#sub_total").val(e),t(".grandtotal").val(t.fn.addNumericCommas(parseFloat(a).toFixed(2))),t("#grand_total").val(a),t("#total").val(t.fn.addNumericCommas(parseFloat(a).toFixed(2)))}function r(){return localStorage.clear(),""}function o(a){return t.ajax({type:"GET",url:BASE_URI+"customers/check_credit/"+a,dataType:"JSON"})}var m=t("#table-item");t(function(){t(".pos-item").on("click",function(e){var n=t(this).attr("data-id"),r=t(this).attr("data-name"),o=t(this).attr("data-price");a(n,r,o),i()}),m.on("click",".btn-minus",function(){var a=t(this).closest(".order_summary"),e=a.find('input[name^="qty"]').val();a.attr("data-id");if(e>0){e--,a.find(".qty").val(e),a.find(".qty").attr("value",e);var n=t.fn.removeNumericCommas(a.find(".price").val()),r=n*e;a.find(".amount").val(t.fn.addNumericCommas(parseFloat(r).toFixed(2))),a.find(".amount").attr("value",t.fn.addNumericCommas(parseFloat(r).toFixed(2)))}i()}),m.on("click",".btn-plus",function(){var a=t(this).closest(".order_summary"),e=a.find('input[name^="qty"]').val();a.attr("data-id");e++,a.find(".qty").val(e),a.find(".qty").attr("value",e);var n=t.fn.removeNumericCommas(a.find('input[name^="price"]').val()),r=n*e;a.find(".amount").val(t.fn.addNumericCommas(parseFloat(r).toFixed(2))),a.find(".amount").attr("value",t.fn.addNumericCommas(parseFloat(r).toFixed(2))),i()}),t(".discount").on("change",function(){localStorage.setItem("discount",t(this).val()),i()}),m.on("click",".action-delete-order",function(t){n(t),i()}).on("change",'input[name^="qty"]',function(a){var e=t(this).closest(".order_summary"),n=a.currentTarget.value,r=t.fn.removeNumericCommas(e.find('input[name^="price"]').val()),o=r*n;e.find(".amount").val(t.fn.addNumericCommas(parseFloat(o).toFixed(2))),e.find(".amount").attr("value",t.fn.addNumericCommas(parseFloat(o).toFixed(2))),i()}),t(".action-pay").click(function(){var a=m.find("tbody tr").length;if(0==a)return alertify.alert("Item is empty",'<em class="text-danger">No Items Added.</em>'),!1;t(".modal").modal("show");var e=t("#grandtotal").val();0===e.length&&(e="0"),t("#total_payable").val(t.fn.addNumericCommas(parseFloat(e.replace(",","")).toFixed(2)))}),t("input[name='payment_type']").on("change",function(){var a=t("input[name=payment_type]:checked").val();t('input[name="credit_limit"]');if("cash"==a){t(".amount-paid").removeClass("d-none");var e=t('input[name="change"]'),n=t('input[name="amount_paid"]');e.val(t.fn.addNumericCommas(parseFloat(0).toFixed(2))),n.val(0),t(".credit-limit").addClass("d-none")}else"credit"==a&&(t(".credit-limit").removeClass("d-none"),t(".amount-paid").addClass("d-none"))}),t("input[name='amount_paid']").on("input",function(a){var e=a.currentTarget.value,n=t.fn.removeNumericCommas(t('input[name="total_payable"]').val());isNaN(n)&&(n=0);var i=t('input[name="change"]'),r=e-n;i.val(t.fn.addNumericCommas(parseFloat(r<0?0:r).toFixed(2)))}),t("select[name^='customer']").on("change",function(a){var e=t('select[name="customer"]').val();t.when(o(e)).then(function(a){t("input[name='limit']").val(parseFloat(a.credit_limit).toFixed(2))},function(){t("input[name='limit']").val("0.00")})}),t("input[name='search']").on("keyup",function(){let a=t(".pos-item"),e=t(this).val().toLowerCase();a.each(function(a,n){let i=t(n).attr("data-name").toLowerCase()+" "+t(n).attr("data-unit").toLowerCase();i.includes(e)?t(n).show():t(n).hide()})}),t("#orderForm").on("click",".action-save",function(a){a.preventDefault();var e=t("#table-item tbody"),n=t("input[name=payment_type]:checked").val(),i=t('select[name="customer"]').val(),r=parseFloat(t.fn.removeNumericCommas(t('input[name="amount_paid"]').val())),o=t.fn.removeNumericCommas(t('input[name="total_payable"]').val());if(isNaN(o)&&(o=0),0==e.children().length)alertify.alert("Order is Empty!").setHeader('<em class="text-danger">Required*</em>');else if("cash"===n&&r<0)alertify.alert("Invalid Amount!").setHeader('<em class="text-danger">Required*</em>');else if("credit"===n&&""===i)alertify.alert("Customer is Missing!").setHeader('<em class="text-danger">Required*</em>');else{if(""!==i&&"credit"===n){a.preventDefault();var m=t('input[name="limit"]').val(),d=m-o;if(m>0){if(m<o)return alertify.alert("Not Enough Credit.").setHeader('<em class="text-danger">Notification</em>'),!1;if(d<0)return alertify.alert("Insufficient Credit.").setHeader('<em class="text-danger">Notification</em>'),!1}}alertify.confirm("Are you sure you want to continue with this transaction?",function(){t("form").submit()}).setHeader("<em>Save Transaction</em>")}})})}(jQuery);