<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	theme: "light2", //"light1", "light2", "dark1", "dark2"
	animationEnabled: true,
	title:{
		text: "Income Statement, 2016"
	},
	axisX:{
		labelFontSize: 12,
		labelMaxWidth: 70
	},
	axisY: {
		prefix: "$"
	},	
	data: [{
		type: "waterfall",
		yValueFormatString: "$#,##0",
		risingColor: "#5B9BD5",
		fallingColor: "#ED7D31",
		lineColor: "#222222",
		dataPoints: [
			{ label: "Net Revenue", y: 240631 },
			{ label: "Inventory", y: -115000 },
			{ label: "Merchandise", y: -18900 },
			{ label: "Other Sales Cost", y: -6990 },
			{ label: "Gross Income", isIntermediateSum: true, color: "#C7C7C7" },
			{ label: "Staff", y: -27500 },
			{ label: "Marketing", y: -12000 },
			{ label: "Other Facilities", y: -35100 },
			{ label: "Operating Income", isCumulativeSum: true, color: "#C7C7C7" },
			{ label: "Taxes", y: -4500 },
			{ label: "Net Income", isCumulativeSum: true}
		]
	}]
});
chart.render();

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>