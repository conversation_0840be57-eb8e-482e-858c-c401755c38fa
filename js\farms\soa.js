+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
      dom: 'lBfrtip',
			lengthChange: true,
      lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			},
      {
				className: 'text-right',
				targets: 1
			}
    ],
      buttons: [ {
        extend: 'print',
        exportOptions: {
            columns: [ 0, 1]
        }
    }],
			autoWidth: true,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}



	$(function () {
		var dataTable = createDataTable('.tableA');

	});
}(jQuery);
