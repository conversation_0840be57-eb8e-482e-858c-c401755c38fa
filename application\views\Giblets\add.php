<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <form id="gibletsForm" method="post" action="<?= site_url('Giblets/add'); ?>" class="crud-box" accept-charset="utf-8">
                        <h1 class="crud-title">Add Giblets</h1>

                        <?php if (validation_errors()): ?>
                            <div class="alert alert-danger">
                                <?= validation_errors(); ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($this->session->flashdata('error')): ?>
                            <div class="alert alert-danger">
                                <?= $this->session->flashdata('error'); ?>
                            </div>
                        <?php endif; ?>

                        <table class="table table-bordered item-table" id="gibletsTableForm" style="background-color: white;">
                            <thead>
                                <tr>
                                    <th>SKU</th>
                                    <th>By</th>
                                    <th>NO</th>
                                    <th>PCS</th>
                                    <th>KGS</th>
                                    <th style="width: 100px; text-align: center;">Action</th>
                                </tr>
                            </thead>
                            <tbody id="gibletsTableBody">
                                <tr class="giblets-row">
                                    <td><input type="text" class="form-control" name="sku[]" value=""></td>
                                    <td><input type="text" class="form-control" name="by[]" value=""></td>
                                    <td><input type="text" class="form-control" name="no[]" value=""></td>
                                    <td><input type="text" class="form-control" name="pcs[]" value=""></td>
                                    <td><input type="text" class="form-control" name="kgs[]" value=""></td>
                                    <td class="text-center" style="vertical-align: middle;">
                                        <button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" class="btn btn-dark" id="addGibletsRow">Add Row</button>
                        <hr>
                        <div class="clearfix text-center">
                            <button type="submit" class="btn btn-primary">Save</button>
                            <a href="<?= site_url('Giblets'); ?>" class="btn btn-light">Close</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= base_url('js/Giblets/add.js') ?>"></script> 