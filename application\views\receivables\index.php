<div class="col topbtn">
	<div class="title"><?=$title;?></div>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">

		<select name="payment_type" class="form-control col-1 dropsearch" data-placeholder="Type">
			<option></option>
			<option value="check">Check</option>
			<option value="cash">Cash</option>
			<option value="credit">Credit</option>
		</select>

		<div class="mx-1"></div>

		<select name="invoice_status" class="form-control col-1 dropsearch" data-placeholder="Status">
			<option></option>
			<option value="pending">Pending</option>
			<option value="approved">Approved</option>
			<option value="cancelled">Cancelled</option>
			<option value="for approval">For Approval</option>
		</select>

		<!-- <div class="mx-1"></div>
		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<input type="text" value="<?=set_value("from", date("m/d/Y", strtotime("-1 years")));?>" name="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" class="form-control datepicker mb-2 mr-sm-2"> -->

        <!-- <label for="customer" class="mr-sm-2 mld-4">Customer:</label>
		<select name="customer" class="form-control mb-2 mr-sm-2 dropsearch" data-placeholder="Customer">
			<option value=""></option>
			<?php foreach ($customers as $customer): ?>
				<option value="<?=$customer->id;?>"><?=$customer->name;?></option>
			<?php endforeach;?>
		</select> -->

		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<input type="text" value="<?=set_value('date_from');?>" name="date_from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<input type="text" value="<?=set_value('date_to');?>" name="date_to" class="form-control datepicker mb-2 mr-sm-2">

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="receivables">
		<table class="table table-hover tableA" data-package="receivables">
			<thead>
				<tr>
					<th>Customer</th>
					<th>Amount</th>
				</tr>
			</thead>

			<tbody></tbody>

			<tfoot>
				<tr>
					<th>Total</th>
					<th></th>
				</tr>
			</tfoot>
		</table>
	</div>
</div>

<style>
    tr td:nth-child(1) {
        cursor: pointer;
    }

    tr td:nth-child(1):hover {
        text-decoration: underline;
        color: blue;
    }
</style>