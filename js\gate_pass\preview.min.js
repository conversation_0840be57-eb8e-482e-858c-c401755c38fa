!function(t){"use strict";function e(e,o){return t.ajax({url:BASE_URI+"poc/change_status/"+e+"/"+o,type:"POST",dataType:"json"})}t(function(){t(".action-print").click(function(){window.print()}),t(".action-approve").click(function(t){let o=t.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to approve POC No. "+o+"?",function(){e(o,"approved").done(function(){window.location.reload()})}).setHeader("<em>Approve POC</em>")}),t(".action-disapprove").click(function(t){let o=t.currentTarget.getAttribute("data-id");alertify.confirm("Are you sure you want to disapprove POC No. "+o+"?",function(){e(o,"disapproved").done(function(){window.location.reload()})}).setHeader("<em>Disapprove POC</em>")})})}(jQuery);