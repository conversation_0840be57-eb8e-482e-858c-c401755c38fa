<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cash_outs extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }
    
    /**
     * Fetch Payables
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'invoice_item';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_mop(),
            $this->_get_amount()
        ];

        $joins = <<<EOT
check_slip
LEFT JOIN bank ON bank.id = check_slip.bank_id
EOT;

        $where = <<<EOT
check_slip.is_deleted = 0
AND check_slip.status = 'printed'
EOT;

$from = $this->input->get('from', true);
$to = $this->input->get('to', true);
if (isset($from) AND $from_date = date("Y-m-d", strtotime(urldecode($from))) and
    isset($to) AND $to_date = date("Y-m-d", strtotime(urldecode($to))) 
) {
    $where .= <<<EOT

AND check_slip.check_date BETWEEN "{$from_date}" AND "{$to_date}"
EOT;
}

        $group_by = <<<EOT
bank.name
EOT;
        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'check_slip.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'check_slip.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'check_slip';
            }
        ];
    }

    /**
     * Get Account Name
     */
    protected function _get_mop()
    {
        return [
            'db' => 'bank.name',
            'as' => 'mop',
            'dt' => 0,
            'field' => 'mop',
        ];
    }

    /**
     * Get Amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'SUM(check_slip.amount)',
            'as' => 'amount',
            'dt' => 1,
            'field' => 'amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }
}
