<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Signatories extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('signatories/manager'));
    }

    /**
     * Add signatory
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('signatories/add')) && $this->_attempt_add()) {
            redirect(site_url('signatories/manager'));
        } else {
            $this->title = 'Add Signatory';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error
            ];
            $this->build_content('default', 'signatories/add', $data);
        }
    }

    /**
     * Edit signatory
     */
    public function edit($signatory_id)
    {
        $where = [
            'id' => $signatory_id,
            'is_deleted' => 0
        ];
        $signatory = $this->signatory->select('', $where, 1) or show_404();

        if ($this->_validate_form($this->config->item('signatories/edit')) && $this->_attempt_edit($signatory)) {
            redirect(site_url('signatories/manager'));
        } else {
            $this->title = 'Edit Signatory';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'signatory' => $signatory
            ];
            $this->build_content('default', 'signatories/edit', $data);
        }
    }

    /**
     * Delete signatory
     */
    public function delete($signatory_id)
    {
        $where = [
            'id' => $signatory_id,
            'is_deleted' => 0
        ];
        if (!$signatory = $this->signatory->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Signatory is not found.'
            ];
        } elseif (!$this->_attempt_delete($signatory)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Signatory is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * signatories
     */
    public function manager()
    {
        $this->title = 'Signatories';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/signatories/manager'
        ];

        $this->build_content('default', 'signatories/manager');
    }

    /**
     * Get Info of signatory
     */
    public function info($signatory_id = '')
    {
        $where = [
            'id' => $signatory_id,
            'is_deleted' => 0
        ];
        if (!$signatory = $this->signatory->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Signatory is not found.'
            ];
        } else {
            $response = [
                'message' => 'Signatory is found.',
                'signatory' => $signatory
            ];
        }

        echo json_encode($response);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('signatory');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date("Y-m-d H:i:s")
        ];
        if (!$signatory_id = $this->signatory->insert($values)) {
            $this->_error = 'server_error';
        }

        return $signatory_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($signatory)
    {
        $where = [
            'id' => $signatory->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $this->input->post('name'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date("Y-m-d H:i:s")
        ];

        if (!$is_updated = $this->signatory->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($signatory)
    {
        $where = [
            'id' => $signatory->id,
            'is_deleted' => 0
        ];
        $values = [
            'name' => $signatory->name,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'update_on' => date("Y-m-d H:i:s")
        ];

        if (!$is_deleted = $this->signatory->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
