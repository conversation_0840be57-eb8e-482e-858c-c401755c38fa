function prepareIframe(n) { iframe || (iframe = document.createElement("iframe"), iframe.height = "99%", iframe.width = "100%", iframe.frameBorder = "no"); iframe.src = n; alertify.alert(iframe).setting({ padding: !1, overflow: !1, onclose: function () { alertify.alert().setting({ padding: !0, overflow: !0, onclose: null }) } }).maximize() } function ViewArabic() { prepareIframe("arabic.html") } function ViewSemantic() { prepareIframe("semantic.html") } function ViewBootstrap() { prepareIframe("bootstrap.html") } $(function () { function u() { n.addClass("active").siblings(".menu").addClass("visible") } function i() { n.removeClass("active").siblings(".menu").removeClass("visible") } function r(r) { n.is(r) || n.has(r).length || t.is(r) || t.has(r).length || i() } var n = $(".bars"), t = n.siblings(".menu"); t.scrollLock(); n.click(function () { return n.hasClass("active") ? i() : u(), !1 }); $(document).click(function (n) { r(n.target) }).on("tap", function (n) { r(n.target) }); $(".checkbox.example").each(function () { var n = $(this); n.checkbox({ onChange: function () { var i = this[0].checked, t = n.closest(".segment").find("pre code.javascript"); t.find("span").each(function () { var n = $(this); n.text(n.text().replace(/true|false/ig, function (n) { return n == "true" ? "false" : "true" })) }) } }) }); $(".dropdown.example").each(function () { var n = $(this); n.dropdown({ onChange: function (t, i) { var u = n.data("text"), r; n.data("text", i); r = n.closest(".segment").find("pre code.javascript"); r.find("span").each(function () { var n = $(this); n.text(n.text().replace(new RegExp(u, "i"), i)) }); n.parent().find(".button").click() } }); n.data("text", n.dropdown("get text")) }); $(".button.example").each(function () { $(this).click(function () { var n = $(this).closest(".segment").find("pre code.javascript"); new Function("(function(){ " + n.text() + "})();").call(); return false;})})});var iframe;