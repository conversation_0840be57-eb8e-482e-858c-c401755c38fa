<?php
class Schedule_hauling_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'schedule_hauling_item';
        parent::__construct();
    }

    /**
     * Generate schedule_hauling Item
     */
    public function generate($id, $schedule_hauling_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO schedule_hauling_item(id, schedule_hauling_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, schedule_hauling.id AS schedule_hauling_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM schedule_hauling
    CROSS JOIN item
    WHERE schedule_hauling.id = ?
        AND schedule_hauling.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    schedule_hauling_item.id = LAST_INSERT_ID(schedule_hauling_item.id),
    schedule_hauling_item.qty = VALUES(schedule_hauling_item.qty),
    schedule_hauling_item.unit = VALUES(schedule_hauling_item.unit),
    schedule_hauling_item.updated_by = VALUES(schedule_hauling_item.added_by),
    schedule_hauling_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $schedule_hauling_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get schedule_hauling Item By ID
     */
    public function get_by_id($schedule_hauling_item_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling_item.*, item.name AS item
FROM schedule_hauling_item
LEFT JOIN item ON item.id = schedule_hauling_item.item_id
WHERE schedule_hauling_item.id = ?
    AND schedule_hauling_item.is_deleted = 0
EOT;
        $binds = [$schedule_hauling_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get schedule_hauling Items by schedule_hauling
     */
    public function get_by_schedule_hauling($schedule_hauling_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling_item.*, item.name AS item, live_sell_supplier.name AS supplier_name
FROM schedule_hauling_item
LEFT JOIN item ON item.id = schedule_hauling_item.item_id
LEFT JOIN live_sell_supplier ON live_sell_supplier.id = schedule_hauling_item.supplier_id
WHERE schedule_hauling_item.schedule_hauling_id = ?
    AND schedule_hauling_item.is_deleted = 0
EOT;
        $binds = [$schedule_hauling_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
