<?php
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['s3_upload_userfile_not_set'] = 'Unable to find a post variable called userfile.';
$lang['s3_upload_file_exceeds_limit'] = 'The uploaded file exceeds the maximum allowed size in your PHP configuration file.';
$lang['s3_upload_file_exceeds_form_limit'] = 'The uploaded file exceeds the maximum size allowed by the submission form.';
$lang['s3_upload_file_partial'] = 'The file was only partially uploaded.';
$lang['s3_upload_no_temp_directory'] = 'The temporary folder is missing.';
$lang['s3_upload_unable_to_write_file'] = 'The file could not be written to disk.';
$lang['s3_upload_stopped_by_extension'] = 'The file upload was stopped by extension.';
$lang['s3_upload_no_file_selected'] = 'You did not select a file to upload.';
$lang['s3_upload_invalid_filetype'] = 'The filetype you are attempting to upload is not allowed.';
$lang['s3_upload_invalid_filesize'] = 'The file you are attempting to upload is larger than the permitted size.';
$lang['s3_upload_invalid_dimensions'] = 'The image you are attempting to upload doesn\'t fit into the allowed dimensions.';
$lang['s3_upload_destination_error'] = 'A problem was encountered while attempting to move the uploaded file to the final destination.';
$lang['s3_upload_no_filepath'] = 'The upload path does not appear to be valid.';
$lang['s3_upload_no_file_types'] = 'You have not specified any allowed file types.';
$lang['s3_upload_bad_filename'] = 'The file name you submitted already exists on the server.';
$lang['s3_upload_not_writable'] = 'The upload destination folder does not appear to be writable.';
