<?php
class Cash_bond extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'cash_bond';
        parent::__construct();
    }

    /**
     * Get Cash_bond By ID
     */
    public function get_by_id($cash_bond_id)
    {
        $sql = <<<EOT
SELECT 
    cash_bond.*, 
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email, 
    issuer.full_name AS issuer,
    GROUP_CONCAT(CONCAT_WS(', ', invoice.invoice_no)) AS concatenated_invoice_no
FROM cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN cash_bond_item ON cash_bond_item.cash_bond_id = cash_bond.id
LEFT JOIN invoice ON invoice.id = cash_bond_item.invoice_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
WHERE cash_bond.id = ?
    AND cash_bond.is_deleted = 0
GROUP BY cash_bond.id
EOT;
        $binds = [$cash_bond_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Cash_bond By ID for Payment
     */
    public function get_for_payment($cash_bond_id)
    {
        $sql = <<<EOT
SELECT 
    cash_bond.id,
    cash_bond.customer_id,
    cash_bond.cb_date,
    cash_bond.due_date,
    cash_bond.terms,
    cash_bond.remarks,
    cash_bond.status,
    cash_bond.counter,
    cash_bond.grand_total,
    SUM(paid_invoice.amount) as paid_amount,
    cash_bond.audit_remarks,
    cash_bond.audited_by,
    cash_bond.audited_on,
    cash_bond.added_by,
    cash_bond.added_on,
    cash_bond.updated_by,
    cash_bond.updated_on,
    cash_bond.is_deleted,
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email, 
    issuer.full_name AS issuer,
    GROUP_CONCAT(CONCAT_WS(', ', invoice.invoice_no)) AS concatenated_invoice_no
FROM cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN cash_bond_item ON cash_bond_item.cash_bond_id = cash_bond.id
LEFT JOIN invoice ON invoice.id = cash_bond_item.invoice_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
LEFT JOIN paid_invoice ON paid_invoice.invoice_id = cash_bond.id 
    AND paid_invoice.doc_type = 'cash_bond'
    AND paid_invoice.is_deleted = 0
WHERE cash_bond.id = ?
    AND cash_bond.is_deleted = 0
GROUP BY cash_bond.id
EOT;
        $binds = [$cash_bond_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get Partially Paid Cash_bonds
     */
    public function get_partially_paid($customer_id)
    {
        $sql = <<<EOT
SELECT 
    cash_bond.id,
    cash_bond.customer_id,
    cash_bond.cb_date,
    cash_bond.due_date,
    cash_bond.terms,
    cash_bond.remarks,
    cash_bond.status,
    cash_bond.counter,
    cash_bond.grand_total,
    SUM(paid_invoice.amount) as paid_amount,
    cash_bond.audit_remarks,
    cash_bond.audited_by,
    cash_bond.audited_on,
    cash_bond.added_by,
    cash_bond.added_on,
    cash_bond.updated_by,
    cash_bond.updated_on,
    cash_bond.is_deleted,
    customer.name AS customer, 
    customer.address, 
    customer.phone_no, 
    customer.email, 
    issuer.full_name AS issuer
FROM cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
LEFT JOIN paid_invoice ON paid_invoice.invoice_id = cash_bond.id 
    AND paid_invoice.doc_type = 'cash_bond'
    AND paid_invoice.is_deleted = 0
WHERE paid_amount < cash_bond.grand_total
    AND cash_bond.customer_id = ?
    AND cash_bond.is_deleted = 0
GROUP BY cash_bond.id
EOT;
        $binds = [$customer_id];
        
        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * Get Cash_bonds Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT cash_bond.id, cash_bond.cash_bond_date, cash_bond.customer_id, cash_bond.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM cash_bond
LEFT JOIN (
    SELECT payment.payment_date, paid_cash_bond.cash_bond_id, paid_cash_bond.amount
    FROM payment
    LEFT JOIN paid_cash_bond ON paid_cash_bond.payment_id = payment.id
    LEFT JOIN cash_bond ON cash_bond.id = paid_cash_bond.cash_bond_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND cash_bond.customer_id = ?
) payment ON payment.cash_bond_id = cash_bond.id
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
WHERE cash_bond.cash_bond_date < ?
    AND customer.id = ?
    AND cash_bond.is_deleted = 0
GROUP BY cash_bond.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Cash_bonds Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT cash_bond.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
WHERE cash_bond.cash_bond_date BETWEEN ? AND ?
    AND cash_bond.customer_id = ?
    AND cash_bond.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Cash_bonds Total Sales
     */
    public function get_total_monthly_sales()
    {
        $firstDayOfMonth = date('Y-m-01');
        $lastDayOfMonth = date('Y-m-t');

        $sql = <<<EOT
SELECT SUM(cash_bond.paid_amount) AS total_monthly_sales
FROM cash_bond
WHERE is_deleted = 0
    AND cash_bond.cash_bond_date BETWEEN '$firstDayOfMonth' AND '$lastDayOfMonth';
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_monthly_sales;
        }else{
            return 0;
        }
    }

    /**
     * Get Cash_bonds Total Receivables
     */
    public function get_total_receivables()
    {

        $sql = <<<EOT
SELECT SUM(cash_bond.total - cash_bond.paid_amount) AS total_receivables
FROM cash_bond
WHERE is_deleted = 0
EOT;
        $res = $this->advanced_query($sql);

        if(!empty($res[0])){
            return $res[0]->total_receivables;
        }else{
            return 0;
        }
    }


    /**
     * Get Cash_bonds BY Customer
     */
    public function get_by_customer($customer_id, $date_from = null, $date_to = null)
    {
        $sql = <<<EOT
SELECT cash_bond.id, cash_bond.cash_bond_no, cash_bond.terms, cash_bond.cash_bond_date, cash_bond.customer_id, cash_bond.total AS charge, cash_bond.total, cash_bond.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM cash_bond
LEFT JOIN (
    SELECT payment.payment_date, paid_cash_bond.cash_bond_id, paid_cash_bond.amount
    FROM payment
    LEFT JOIN paid_cash_bond ON paid_cash_bond.payment_id = payment.id
    LEFT JOIN cash_bond ON cash_bond.id = paid_cash_bond.cash_bond_id
    WHERE payment.payment_date
        AND cash_bond.customer_id = ?
) payment ON payment.cash_bond_id = cash_bond.id
LEFT JOIN customer ON customer.id = cash_bond.customer_id
LEFT JOIN user AS issuer ON issuer.id = cash_bond.added_by
WHERE customer.id = ?
    AND cash_bond.is_deleted = 0
EOT;
        $binds = [$customer_id, $customer_id];

    if($date_from && $date_to) {
        $sql .= <<<EOT

AND cash_bond.cash_bond_date >= ? AND cash_bond.cash_bond_date <= ?
EOT;
    $binds[] = $date_from;
    $binds[] = $date_to;
    } elseif($date_from) {
        $sql .= <<<EOT

AND cash_bond.cash_bond_date >= ?
EOT;
    $binds[] = $date_from;
    } elseif($date_to) {
        $sql .= <<<EOT

AND cash_bond.cash_bond_date <= ?
EOT;
    $binds[] = $date_to;
    }

        $sql .= <<<EOT

GROUP BY cash_bond.id
EOT;

        // die($this->db->compile_binds($sql, $binds));

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Cash_bonds BY Customer
     */
    public function get_customers()
    {
        $sql = <<<EOT
SELECT
    DISTINCT customer.*
FROM cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
WHERE cash_bond.is_deleted = 0
    AND cash_bond.status <> "cancelled"
    AND cash_bond.grand_total > cash_bond.paid_amount
EOT;
        $binds = [];

        return $this->advanced_query($sql, $binds);
    }


    public function get_cash_bonds_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT
    cash_bond.*
FROM 
    cash_bond
WHERE 
    cash_bond.customer_id = ?
    AND cash_bond.is_deleted = 0
    AND cash_bond.status = "active"
EOT;
        $binds = [$customer_id];

        return $this->advanced_query($sql, $binds);
    }

}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */