/**
 * alertifyjs 1.11.1 http://alertifyjs.com
 * AlertifyJS is a javascript framework for developing pretty browser dialogs and notifications.
 * Copyright 2018 <PERSON> <<EMAIL>> (http://alertifyjs.com) 
 * Licensed under GPL 3 <https://opensource.org/licenses/gpl-3.0>*/
.alertify .ajs-dimmer {
  background-color: rgba(0, 0, 0, 0.85);
  opacity: 1;
}
.alertify .ajs-dialog {
  max-width: 50%;
  min-height: 137px;
  background-color: #F4F4F4;
  border: 1px solid #DDD;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 5px;
}
.alertify .ajs-header {
  padding: 1.5rem 2rem;
  border-bottom: none;
  border-radius: 5px 5px 0 0;
  color: #555;
  background-color: #fff;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 1.6em;
  font-weight: 700;
}
.alertify .ajs-body {
  font-family: 'Roboto', sans-serif;
  color: #555;
}
.alertify .ajs-body .ajs-content .ajs-input {
  width: 100%;
  margin: 0;
  padding: .65em 1em;
  font-size: 1em;
  background-color: #FFF;
  border: 1px solid rgba(0, 0, 0, 0.15);
  outline: 0;
  color: rgba(0, 0, 0, 0.7);
  border-radius: .3125em;
  -webkit-transition: background-color 0.3s ease-out, border-color 0.2s ease, -webkit-box-shadow 0.2s ease;
  transition: background-color 0.3s ease-out, border-color 0.2s ease, -webkit-box-shadow 0.2s ease;
  transition: background-color 0.3s ease-out, box-shadow 0.2s ease, border-color 0.2s ease;
  transition: background-color 0.3s ease-out, box-shadow 0.2s ease, border-color 0.2s ease, -webkit-box-shadow 0.2s ease;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.alertify .ajs-body .ajs-content .ajs-input:active {
  border-color: rgba(0, 0, 0, 0.3);
  background-color: #FAFAFA;
}
.alertify .ajs-body .ajs-content .ajs-input:focus {
  border-color: rgba(0, 0, 0, 0.2);
  color: rgba(0, 0, 0, 0.85);
}
.alertify.ajs-resizable .ajs-content,
.alertify.ajs-maximized:not(.ajs-resizable) .ajs-content {
  top: 64px;
  bottom: 74px;
}
.alertify .ajs-footer {
  background-color: #fff;
  padding: 1rem 2rem;
  border-top: none;
  border-radius: 0 0 5px 5px;
}
.alertify-notifier .ajs-message {
  background: rgba(255, 255, 255, 0.95);
  color: #000;
  text-align: center;
  border: solid 1px #ddd;
  border-radius: 2px;
}
.alertify-notifier .ajs-message.ajs-success {
  color: #fff;
  background: rgba(91, 189, 114, 0.95);
  text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.5);
}
.alertify-notifier .ajs-message.ajs-error {
  color: #fff;
  background: rgba(217, 92, 92, 0.95);
  text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.5);
}
.alertify-notifier .ajs-message.ajs-warning {
  background: rgba(252, 248, 215, 0.95);
  border-color: #999;
}
