!function(t){"use strict";function e(e){var a=t(n());i(a,e);var o=t(".item-table"),r=o.find(".billing_statement_item");0===r.length&&o.find(".no-item").remove(),o.find("tbody").append(a),l()}function n(){var t=a();return'<tr class="billing_statement_item"><td><input type="hidden" name="billing_statement_item_'+t+'" value="'+t+'"><input type="number" name="row_'+t+'" class="form-control-plaintext" value="" readonly></td><td><select name="invoice_no_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="number" name="total_'+t+'" class="form-control" value="" min="0" step="any" readonly></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button></td></tr>'}function a(){for(var t="",e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function i(e,n){e.find('select[name^="invoice_no"]').select2({data:n,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function o(){return'<tr class="no-item"><td class="text-center" colspan="9">No items added yet.</td></tr>'}function l(){var e=t(".item-table"),n=e.find(".billing_statement_item");n.each(function(e,n){t(n).find('input[name^="row"]').val(e+1)})}function r(t){var e=parseFloat(t.find('input[name^="total"]').val());return isNaN(e)&&(e=0),e}function c(){var e=0;t(".item-table").find(".billing_statement_item").each(function(n,a){e+=r(t(a))}),t('input[name="grand_total"]').val(e.toFixed(2)),t(".footer_total").text(t.fn.addNumericCommas(e.toFixed(2)))}function s(e){t(e.currentTarget).closest(".billing_statement_item").remove();var n=t(e.delegateTarget),a=n.find(".billing_statement_item");if(a.length<1){var i=t(o());n.find("tbody").html(i)}l(),c()}function d(e){return t.ajax({url:BASE_URI+"customers/info/"+e,type:"GET",dataType:"json"})}function m(e){return t.ajax({url:BASE_URI+"billing_statements/all_invoices/"+e,type:"GET",dataType:"json"})}t(function(){var n=[{id:"",text:""}];t('select[name="customer"]').on("change",function(e){var a=t(this).val();t('input[name="address"]').val(""),t('input[name="phone_no"]').val(""),t('input[name="email"]').val(""),t.when(d(a)).then(function(e){t('input[name="address"]').val(e.customer.address),t('input[name="phone_no"]').val(e.customer.phone_no),t('input[name="email"]').val(e.customer.email)});var i=t("#btnSubmit");""!==a?(i.prop("disabled",!1),t(".item-table").find("tbody").html(t(o()))):i.prop("disabled",!0),t.when(m(a)).then(function(t){t.invoices.forEach(function(t){t.text=t.invoice_no,n.push(t)})})}),t(".action-add-item").click(function(){e(n)}),t(".item-table").on("change",'select[name^="invoice_no"]',function(e){var n=t(e.delegateTarget).find('select[name^="invoice_no"] option[value="'+e.currentTarget.value+'"]:selected');n.length>1&&(alert("Invoice No already selected."),t(e.currentTarget).val("").trigger("change"),stop)}).on("click",".action-delete-item",function(t){s(t)}).on("change",'select[name^="invoice_no"]',function(e){var a=t(e.currentTarget).closest(".billing_statement_item"),i=t(this).val(),o=n.find(t=>t.id===i);console.log(n);var l=a.find('input[name^="total"]');if(console.log(l),o){const t=parseFloat(o.total).toFixed(2);l.val(t)}else l.val("");c()})})}(jQuery);