<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="background-color: #f8f9fa; padding: 2rem;">
                    <form id="gibletsEditForm" method="post" action="<?= site_url('Giblets/update/' . $giblets->id); ?>" class="crud-box" accept-charset="utf-8">
                        <h1 class="crud-title">Edit Giblets</h1>

                        <?php if (validation_errors()): ?>
                            <div class="alert alert-danger">
                                <?= validation_errors(); ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($this->session->flashdata('error')): ?>
                            <div class="alert alert-danger">
                                <?= $this->session->flashdata('error'); ?>
                            </div>
                        <?php endif; ?>

                        <table class="table table-bordered item-table" style="background-color: white;">
                            <thead>
                                <tr>
                                    <th>SKU</th>
                                    <th>By</th>
                                    <th>NO</th>
                                    <th>PCS</th>
                                    <th>KGS</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="text" class="form-control" name="sku" value="<?= htmlspecialchars($giblets->sku) ?>"></td>
                                    <td><input type="text" class="form-control" name="by" value="<?= htmlspecialchars($giblets->by) ?>"></td>
                                    <td><input type="text" class="form-control" name="no" value="<?= htmlspecialchars($giblets->no) ?>"></td>
                                    <td><input type="text" class="form-control" name="pcs" value="<?= htmlspecialchars($giblets->pcs) ?>"></td>
                                    <td><input type="text" class="form-control" name="kgs" value="<?= htmlspecialchars($giblets->kgs) ?>"></td>
                                </tr>
                            </tbody>
                        </table>
                        <hr>
                        <div class="clearfix text-center">
                            <button type="submit" class="btn btn-primary">Save</button>
                            <a href="<?= site_url('Giblets'); ?>" class="btn btn-light">Close</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= base_url('js/Giblets/edit.js') ?>"></script> 