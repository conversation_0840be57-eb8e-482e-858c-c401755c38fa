<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Live_sell_items extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('live_sell_item');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch live_sell_items
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'live_sell_item';
        $primary_key = 'id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_unit(),
            $this->_get_actions()
        ];

        $where = <<<EOT
is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $table, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'live_sell_item';
            }
        ];
    }

    /**
     * Get live_sell_item name
     */
    protected function _get_name()
    {
        return [
            'db' => 'name',
            'dt' => 0,
            'field' => 'name'
        ];
    }

    /**
     * Get live_sell_item unit
     */
    protected function _get_unit()
    {
        return [
            'db' => 'unit',
            'dt' => 1,
            'field' => 'unit'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'id',
            'as' => 'actions',
            'dt' => 2,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('live_sell_items/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
