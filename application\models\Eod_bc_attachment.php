<?php
class Eod_bc_attachment extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'eod_bc_attachment';
        parent::__construct();
    }

    /**
     * Get POC by ID
     */
    public function get_by_parent_id($eodp_id)
    {
        $sql = <<<EOT
SELECT 
    eod_bc_attachment.*
FROM eod_bc_attachment
WHERE eod_bc_attachment.is_deleted = 0
    AND eod_bc_attachment.eod_bc_id = ?
EOT;
        $binds = [$eodp_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res;
        } else {
            return false;
        }
    }
    
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */