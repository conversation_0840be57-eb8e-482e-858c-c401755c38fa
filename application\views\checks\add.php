<div class="sect">
	<div class="col-md-8 offset-md-2 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title"><?=$title;?></h1>

			<fieldset>
				<div class="form-row">
					<div class="col-md-12 form-group">
						<label for="bank">Bank</label>

						<select name="bank" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($banks as $bank): ?>
								<option value="<?=$bank->id;?>" <?=set_select('bank', $bank->id);?>><?=$bank->name;?></option>
							<?php endforeach;?>
						</select>

						<!-- <button type="button" class="btn btn-sm btn-link no-padding action-new-bank">Bank not found? Click here to add it.</button> -->

						<div class="red-text flash-message">
							<?=form_error('bank');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="check_date">Check Date</label>

						<input type="text" name="check_date" class="form-control datepicker" value="<?=set_value('check_date', date('m/d/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('check_date');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="check_no">Check No.</label>


						<input type="text" name="check_no" class="form-control" value="<?=set_value('check_no');?>">


						<div class="red-text flash-message">
							<?=form_error('check_no');?>
						</div>
					</div>
				</div>

				<div class="form-row">
					<div class="col-md-12 form-group">
						<label for="supllier">Supplier </label>

						<select name="supplier" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php
                            foreach ($suppliers as $supplier): ?>
								<option value="<?=$supplier->id;?>" <?=set_select('supplier', $supplier->id);?>><?=$supplier->name;?></option>
							<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('supplier');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="particulars">Particulars <small class="font-italic">(Optional)</small></label>

						<input type="text" name="particulars" class="form-control" value="<?=set_value('particulars');?>">

						<div class="red-text flash-message">
							<?=form_error('particulars');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="sig_1">Signatory 1 <small class="font-italic">(Optional)</small></label>

						<input type="text" name="sig_1" class="form-control" value="<?=set_value('sig_1');?>">

						<div class="red-text flash-message">
							<?=form_error('sig_1');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="sig_2">Signatory 2 <small class="font-italic">(Optional)</small></label>

						<input type="text" name="sig_2" class="form-control" value="<?=set_value('sig_2');?>">

						<div class="red-text flash-message">
							<?=form_error('sig_2');?>
						</div>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>List of Purchase Orders</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="table table-bordered purchase-table">
							<thead>
								<tr>
									<th style="width: 50%;">Purchase Orders</th>

									<th style="width: 25%;" class="text-right">Amount</th>

									<th style="width: 25%;"></th>
								</tr>
							</thead>

							<tbody>
					<?php
                        if (!empty($check_purchase_ids)):
                            foreach ($check_purchase_ids as $check_purchase_id):
                        ?>
								<tr class="check_purchase">
									<td>
									<?php $fdn_check_purchase = 'check_purchase_' . $check_purchase_id;?>

										<input type="hidden" name="<?=$fdn_check_purchase;?>" value="<?=set_value($fdn_check_purchase, $check_purchase_id);?>">

										<?php $fdn_purchase = 'purchase_' . $check_purchase_id;?>

										<select name="<?=$fdn_purchase;?>" class="form-control dropsearch" data-placeholder="" >
											<option></option>
											<?php foreach ($purchases as $purchase): ?>
												<option value="<?=$purchase->id;?>" <?=set_select($fdn_purchase, $purchase->id);?>><?='Purchase Order#' . $purchase->id . ' - Php' . number_format($purchase->total, 2, '.', '');?>
												</option>
											<?php endforeach;?>
									</select>

									<div class="red-text flash-message">
										<?=form_error($fdn_check_purchase);?>
										<?=form_error($fdn_purchase);?>
									</div>
								</td>

								<td>
									<?php $fdn_amount = 'amount_' . $check_purchase_id;?>

									<input type="text" name="<?=$fdn_amount;?>" class="form-control" value="<?=set_value($fdn_amount);?>" step="any">

									<div class="red-text flash-message">
										<?=form_error($fdn_amount);?>
									</div>
								</td>

								<td class="text-right">
									<button type="button" class="btn btn-small btn-danger action-delete-purchase">Delete</button>
								</td>
							</tr>
					<?php
                        endforeach;
                        else:
                    ?>
							<tr class="no-purchase">
								<td class="text-center" colspan="3">No purchase orders added yet.</td>
							</tr>
					<?php
                        endif;
                    ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-6 form-group">
						<button type="button" class="btn btn-sm btn-dark action-add-payment" disabled>Add Payment</button>
					</div>

					<div class="col-3 form-group">
						<div class="row">
							<div class="col-6 text-right">
								<label for="total" class="control-label">Total Amount</label>
							</div>

							<div class="col-6">
								<input type="text" name="total" class="form-control-plaintext text-right" value="0.00" readonly="readonly" step="any">
							</div>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>

				<a href="<?=site_url('checks/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>