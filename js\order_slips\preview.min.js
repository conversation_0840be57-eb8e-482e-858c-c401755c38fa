!function(n){"use strict";n(function(){var t,e,a;(t=n(t=".tableA")).find("thead th"),e=n('input[name="customer_id"]').val(),a=n('input[name="transfer_id"]').val(),t.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"d-none",targets:[0,1,3,4,8]},{className:"text-left",targets:[5,7]},{className:"text-right",targets:[6]}],searching:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+t.attr("data-package")+"?customer_id="+e+"&transfer_id="+a});n(".action-print").click(function(){window.print()})})}(jQuery);