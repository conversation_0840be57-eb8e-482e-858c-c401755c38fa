$(function(){$(".action-print").click(function(){window.print()})}),window.addEventListener("beforeunload",function(e){fetch('<?= site_url("module_2307/delete_image") ?>',{method:"POST",body:JSON.stringify({image_path:'<?= $this->session->userdata("generated_image_path") ?>'}),headers:{"Content-Type":"application/json"}}).then(e=>{console.log("Image deletion request sent")}).catch(e=>{console.error("Error deleting image:",e)})});