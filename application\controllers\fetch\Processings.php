<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Processings extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->model('processing');
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch processings
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'processing';
        $primary_key = 'processing.id';

        $columns = [
            $this->_get_id(),
            $this->_get_order_slip_number(),
            $this->_get_schedule_hauling_number(),
            $this->_get_processing_date(),
            $this->_get_processing_number(),
            $this->_get_customer(),
            // $this->_get_file(),
            $this->_get_remarks(),
            $this->_get_status(),
            $this->_get_prepared_by(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
processing
LEFT JOIN live_sell_customer AS customer ON customer.id = processing.customer_id
LEFT JOIN user ON user.id = processing.added_by
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        // var_dump($status);
        // die();
        $where = <<<EOT
processing.is_deleted = 0
EOT;            

        if ($status !== 'all') {
            $where .= <<<EOT

AND processing.status = "$status"
EOT;
        }

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'processing.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'processing.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'processing';
            }
        ];
    }

    /**
     * Get processing number
     */
    protected function _get_processing_number()
    {
        return [
            'db' => 'processing.id',
            'as' => 'processing_number',
            'dt' => 0,
            'field' => 'processing_number'
        ];
    }

    /**
     * Get order slip number
     */
    protected function _get_order_slip_number()
    {
        return [
            'db' => 'processing.order_slip_id',
            'as' => 'order_slip_number',
            'dt' => 1,
            'field' => 'order_slip_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("order_slips/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get schedule hauling number
     */
    protected function _get_schedule_hauling_number()
    {
        return [
            'db' => 'processing.schedule_hauling_id',
            'as' => 'schedule_hauling_number',
            'dt' => 2,
            'field' => 'schedule_hauling_number',
            'formatter' => function($d, $row) {
                return anchor(site_url("schedule_haulings/preview/" . $d), $d, 'target="_blank" class="btn btn-link link-unstyled"');
            }
        ];
    }

    /**
     * Get processing date
     */
    protected function _get_processing_date()
    {
        return [
            'db' => 'DATE_FORMAT(processing.processing_date, "%b %d, %Y")',
            'as' => 'processing_date',
            'dt' => 3,
            'field' => 'processing_date'
        ];
    }

    /**
     * Get processing customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer',
            'dt' => 4,
            'field' => 'customer'
        ];
    }


    /**
     * Get remarks
     */
    protected function _get_remarks()
    {
        return [
            'db' => 'processing.remarks',
            'dt' => 5,
            'field' => 'remarks',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'IF(processing.is_deleted = 0, processing.status, "cancelled")',
            'as' => 'status',
            'dt' => 'DT_RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get prepared by
     */
    protected function _get_prepared_by()
    {
        return [
            'db' => 'user.full_name',
            'dt' => 6,
            'field' => 'full_name',
            'formatter' => function ($d, $row) {
                return ucwords($d);
            }
        ];
    }

    /**
     * Get File
     */
    protected function _get_file()
    {
        return [
            'db' => 'processing.id',
            'as' => 'file',
            'dt' => 7,
            'field' => 'file',
            'formatter' => function ($d, $row) {
                $files = $this->processing->get_file_by_id($d);
                $res = '<ul>';

                $files = explode('🔥', $files[0]->attachment);
                $files = array_map('trim', $files);
                $files = array_filter($files);

                foreach ($files as $i => $file) {
                    $res .= '<a href="' . base_url('assets/processings/' . $d . '/' . $file) . '" target="_blank">Attachment ' . ++$i . '</a><br>';
                }

                $res .= '</ul>';

                return $res;
            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'processing.id',
            'as' => 'actions',
            'dt' => 7,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                // if ($_SESSION['user']->role_id === '1') {
                    if($row['status'] === 'pending') {
                        // $res .= anchor('sales_orders/add/' . $d, 'Proceed SO', 'title="Proceed SO" class="dropdown-item text-left"');
                        // $res .= anchor('sales_orders/add/' . $d, 'Sales Order', 'title="Confirm Order" class="dropdown-item text-left green-text"');

                        $res .= anchor('processing_fees/add/' . $d, 'Processing Fee', 'title="Confirm Order" class="dropdown-item text-left green-text"');
                        $res .= anchor('processings/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                        $res .= anchor('processings/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                        $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                    } else {
                        $res .= anchor('processings/preview/' . $d, 'Print Preview', 'title="Print Preview" class="dropdown-item text-left"');
                    }
                // }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
