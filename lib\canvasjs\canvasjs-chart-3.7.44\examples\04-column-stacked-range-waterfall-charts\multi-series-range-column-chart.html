<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	animationEnabled: true,
	exportEnabled: true,
	title:{
		text: "Temperature Comparison of Two Cities"
	},
	axisX: {
		valueFormatString: "MMM"
	},
	axisY: {
		includeZero: false,
		suffix: " °C"
	},
	toolTip: {
		shared: true
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [{
		type: "rangeColumn",
		name: "City 1",
		showInLegend: true,
		yValueFormatString: "#0.## °C",
		xValueFormatString: "MMM, YYYY",
		dataPoints: [   
			{ x: new Date(2016, 00), y: [08, 20] },
			{ x: new Date(2016, 01), y: [10, 24] },
			{ x: new Date(2016, 02), y: [16, 29] },
			{ x: new Date(2016, 03), y: [21, 36] },
			{ x: new Date(2016, 04), y: [26, 39] },
			{ x: new Date(2016, 05), y: [22, 39] },
			{ x: new Date(2016, 06), y: [20, 35] },
			{ x: new Date(2016, 07), y: [20, 34] },
			{ x: new Date(2016, 08), y: [20, 34] },
			{ x: new Date(2016, 09), y: [19, 33] },
			{ x: new Date(2016, 10), y: [13, 28] },
			{ x: new Date(2016, 11), y: [09, 23] }
		]
		},
		{
			type: "rangeColumn",
			name: "City 2",
			showInLegend: true,
			yValueFormatString: "#0.## °C",
			xValueFormatString: "MMM, YYYY",
			dataPoints: [   
				{ x: new Date(2016, 00), y: [16, 28] },
				{ x: new Date(2016, 01), y: [18, 31] },
				{ x: new Date(2016, 02), y: [20, 33] },
				{ x: new Date(2016, 03), y: [22, 34] },
				{ x: new Date(2016, 04), y: [22, 33] },
				{ x: new Date(2016, 05), y: [20, 29] },
				{ x: new Date(2016, 06), y: [20, 28] },
				{ x: new Date(2016, 07), y: [20, 28] },
				{ x: new Date(2016, 08), y: [20, 28] },
				{ x: new Date(2016, 09), y: [20, 28] },
				{ x: new Date(2016, 10), y: [14, 27] },
				{ x: new Date(2016, 11), y: [11, 26] }
			]
	}]
});
chart.render();

function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;
	}
	e.chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>