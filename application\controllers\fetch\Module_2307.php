<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Module_2307 extends MYT_Controller
{
      /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch    
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'form_2307';
        $primary_key = 'form_2307.id';

        $columns = [
            $this->_get_2307_id(),
            $this->_get_supplier_id(),
            $this->_get_period_from(),
            $this->_get_period_to(),
            $this->_get_supplier_address(),
            $this->_get_file(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
form_2307
LEFT JOIN supplier ON supplier.id = form_2307.suppliers_id 
LEFT JOIN user AS issuer ON issuer.id = form_2307.added_by
EOT;

        $where = <<<EOT
form_2307.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }


    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'form_2307.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }


    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'form_2307.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'supplier';
            }
        ];
    }


    /**
     * Get supplier name
     */
    protected function _get_2307_id()
    {
        return [
            'db' => 'form_2307.id',
            'dt' => 0,
            'field' => 'id'
        ];
    }


    /**
     * Get supplier id
     */
    protected function _get_supplier_id()
    {
        return [
            'db' => 'supplier.name',
            'dt' => 1,
            'field' => 'name'
        ];
    }


    /**
     * Get supplier address
     */
    protected function _get_period_from()
    {
        return [
            'db' => 'form_2307.period_from',
            'dt' => 2,
            'field' => 'period_from'
        ];
    }


    /**
     * Get period to
     */
    protected function _get_period_to()
    {
        return [
            'db' => 'form_2307.period_to',
            'dt' => 3,
            'field' => 'period_to'
        ];
    }

    
    /**
     * Get supplier address
     */
    protected function _get_supplier_address()
    {
        return [
            'db' => 'supplier.address',
            'dt' => 4,
            'field' => 'address'
        ];
    }
    

    protected function _get_file()
    {
        $CI = &get_instance();
        return [
            'db' => 'form_2307.id',
            'as' => 'file',
            'dt' => 5,
            'field' => 'file',
            'formatter' => function($d, $row) use($CI){

                $CI->load->model('module_2307_attachment');
                $files = $CI->module_2307_attachment->get_by_2307_attachment_id($d);
                $res = '';

                foreach($files as $i => $file){
                    $res .= '<a href="'.base_url('assets/module_2307/'.$d.'/'.$file->file_name).'" target="_blank">Attachment '.++$i.'</a><br>';
                }
                return $res;

            }
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'form_2307.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                
                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_OFFICE, ROLE_ACCOUNTING])) {
                    $res .= anchor('module_2307/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= anchor('module_2307/preview/' . $d, 'Preview', 'title="Preview" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }
                
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
