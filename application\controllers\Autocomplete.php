<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

/**
 *
 * This class object extends the MYT controller class.
 * Also, this class fetches data for jQuery UI's Autocomplete input fields.
 *
 * @package     Cañete Medical and Pediatric Clinics
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 *
 */

class Autocomplete extends MYT_Controller
{

    /**
     * Constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }
    }

        /**
     * Get Walkin Customer Names
     *
     * @access  public
     * @return  string
     */
    public function walkin_customer()
    {
        $this->load->model('customer');

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%',
            'is_walkin' => 1
        ];
        $customers = $this->customer->select('', $where, '', 'name ASC');

        $list = [];
        foreach ($customers as $customer) {
            $list[] = $customer->name;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }


    /**
     * Get Customer Names
     *
     * @access  public
     * @return  string
     */
    public function customers()
    {
        $this->load->model('customer');

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $customers = $this->customer->select('', $where, '', 'name ASC');

        $list = [];
        foreach ($customers as $customer) {
            $list[] = $customer->name;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Supplier Names
     *
     * @access  public
     * @return  string
     */
    public function suppliers()
    {
        $this->load->model('supplier');

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $suppliers = $this->supplier->select('', $where, '', 'name ASC');

        $list = [];
        foreach ($suppliers as $supplier) {
            $list[] = $supplier->name;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Item Names
     *
     * @access  public
     * @return  string
     */
    public function items()
    {
        $this->load->model('item');

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $items = $this->item->select('', $where, '', 'name ASC');

        $list = [];
        foreach ($items as $item) {
            $list[] = $item->name;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Item Units
     *
     * @access  public
     * @return  string
     */
    public function units()
    {
        $this->load->model('item');

        $where = [
            'unit LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $items = $this->item->select('', $where, '', 'unit ASC');

        $list = [];
        foreach ($items as $item) {
            $list[] = $item->unit;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Purchase Usages
     *
     * @access  public
     * @return  string
     */
    public function main_usages()
    {
        $this->load->model('purchase');
        $this->load->model('usage');

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%',
            'is_deleted' => 0
        ];
        $usages = $this->usage->select('', $where, '', 'name ASC');

        $list = array_map(function ($usage) {
            return $usage->name;
        }, $usages);

        $where = [
            'main_usage LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $purchases = $this->purchase->select('', $where, '', 'main_usage ASC');

        foreach ($purchases as $purchase) {
            $list[] = $purchase->main_usage;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Purchase Sub-usages
     *
     * @access  public
     * @return  string
     */
    public function sub_usages()
    {
        $this->load->model('purchase');
        $this->load->model('sub_usage');

        if (!$this->input->get('term', true)) {
            exit(json_encode([]));
        }

        $where = [
            'name LIKE' => '%' . $this->input->get('term', true) . '%',
            'is_deleted' => 0
        ];
        $sub_usages = $this->sub_usage->select('', $where, '', 'name ASC');

        $list = array_map(function ($sub_usage) {
            return $sub_usage->name;
        }, $sub_usages);

        $where = [
            'sub_usage LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $purchases = $this->purchase->select('', $where, '', 'sub_usage ASC');

        foreach ($purchases as $purchase) {
            $list[] = $purchase->sub_usage;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }

    /**
     * Get Purchase Requisitioner
     *
     * @access  public
     * @return  string
     */
    public function requisitioners()
    {
        $this->load->model('purchase');

        $where = [
            'requisitioner LIKE' => '%' . $this->input->get('term', true) . '%'
        ];
        $purchases = $this->purchase->select('', $where, '', 'requisitioner ASC');

        $list = [];
        foreach ($purchases as $purchase) {
            $list[] = $purchase->requisitioner;
        }
        $list = array_filter($list);
        $list = array_map('trim', $list);
        $list = array_unique($list, SORT_REGULAR);

        echo json_encode($list);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
