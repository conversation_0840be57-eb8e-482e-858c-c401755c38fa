!function($){"use strict";function createDataTable(selector){var $table=$(selector),cols=$table.find("thead th"),status=$(".tab-link.active").attr("data-status"),dataTable;return $table.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+$table.attr("data-package")+"?status="+status})}function receiveAndReload(purchaseOrderId,dataTable){$.when(receiveRow(purchaseOrderId)).then((function(){dataTable.ajax.reload(null,!1)}),(function(jqXHR){dataTable.ajax.reload(null,!1);var response=JSON.parse(jqXHR.responseText);alertify.error(response.message)}))}function receiveRow(purchaseOrderId){return $.ajax({url:BASE_URI+"purchase_orders/receive_purchase_order/"+purchaseOrderId,type:"POST",dataType:"json"})}function deleteAndReload(purchaseOrderId,dataTable){$.when(deleteRow(purchaseOrderId)).then((function(){dataTable.ajax.reload(null,!1)}),(function(jqXHR){dataTable.ajax.reload(null,!1);var response=JSON.parse(jqXHR.responseText);alertify.error(response.message)}))}function deleteRow(purchaseOrderId){return $.ajax({url:BASE_URI+"purchase_orders/delete/"+purchaseOrderId,type:"POST",dataType:"json"})}function pendingAndReload(purchaseOrderId,dataTable){$.when(pendingRow(purchaseOrderId)).then((function(){dataTable.ajax.reload(null,!1)}),(function(jqXHR){dataTable.ajax.reload(null,!1);var response=JSON.parse(jqXHR.responseText);alertify.error(response.message)}))}function pendingRow(purchaseOrderId){return $.ajax({url:BASE_URI+"purchase_orders/return_to_pending/"+purchaseOrderId,type:"POST",dataType:"json"})}function urgentAndReload(purchaseOrderId,dataTable){$.when(urgentRow(purchaseOrderId)).then((function(){dataTable.ajax.reload(null,!1)}),(function(jqXHR){dataTable.ajax.reload(null,!1);var response=JSON.parse(jqXHR.responseText);alertify.error(response.message)}))}function urgentRow(purchaseOrderId){return $.ajax({url:BASE_URI+"purchase_orders/mark_urgent/"+purchaseOrderId,type:"POST",dataType:"json"})}$((function(){var dataTable=createDataTable(".tableA");$(".tableA").on("click",".action-receive",(function(event){var cols=$(event.currentTarget).closest("tr").find("td"),docNo=void 0!==cols[0]?cols[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the purchase_order "+docNo+"?",(function(){var purchaseOrderId;receiveAndReload(event.currentTarget.getAttribute("data-id"),dataTable)})).setHeader("<em>receive purchase_order</em>")})).on("click",".action-delete",(function(event){var cols=$(event.currentTarget).closest("tr").find("td"),docNo=void 0!==cols[0]?cols[0].innerHTML:"";alertify.confirm("Are you sure you want to delete purchase order "+docNo+"?",(function(){var purchaseOrderId;deleteAndReload(event.currentTarget.getAttribute("data-id"),dataTable)})).setHeader("<em>Delete Purchase Order</em>")})).on("click",".action-pending",(function(event){var cols=$(event.currentTarget).closest("tr").find("td"),docNo=void 0!==cols[0]?cols[0].innerHTML:"";alertify.confirm("Are you sure you want to return purchase order "+docNo+" to pending?",(function(){var purchaseOrderId;pendingAndReload(event.currentTarget.getAttribute("data-id"),dataTable)})).setHeader("<em>Return to Pending</em>")})).on("click",".action-urgent",(function(event){var cols=$(event.currentTarget).closest("tr").find("td"),docNo=void 0!==cols[0]?cols[0].innerHTML:"";alertify.confirm("Are you sure you want to mark purchase order "+docNo+" as urgent?",(function(){var purchaseOrderId;urgentAndReload(event.currentTarget.getAttribute("data-id"),dataTable)})).setHeader("<em>Mark as Urgent</em>")})),$(".tab-link").click((function(event){$(".tab-link.active").removeClass("active"),event.currentTarget.classList.add("active");var purchaseStatus=event.currentTarget.getAttribute("data-status"),address=BASE_URI+"purchase_orders/manager?status="+encodeURI(purchaseStatus);window.location.href=address}))}))}(jQuery);