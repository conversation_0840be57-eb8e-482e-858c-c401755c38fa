---
layout: 'default'
hljs: 	'light'
---
<section class="blue">
	<div class="content">
		<div class="grid two">
			<div class="column">
				<h1> Examples</h1>
				 Live examples of using AlertifyJS.
			</div>
			<div class="right column">
			  <%- @partial('ad') %>
			</div>
		</div>
	</div>
</section>
<section class="lic">
    <div class="content">
        Looking for a commercial license ?  Keep your source code proprietary and <a href="https://www.uplabs.com/posts/alertifyjs" target="_blank"> Buy a Commercial License Today!</a>
    </div>
</section>
<section class="dark">
	<div class="content">

	<!--Arabic dialogs-->
	<div class="segment">
		<h1 id="RTL"> <a href="#RTL"><i class="fa fa-code"></i> </a> RTL Layout - Arabic </h1>
		<hr />		
<pre>
<code class="lang-html">
<!-- set document direction -->
<html dir="rtl" lang="ar"> ... </html>

<!-- include the RTL css files-->
<link rel="stylesheet" href="{PATH}/alertify.rtl.css">
<link rel="stylesheet" href="{PATH}/themes/default.rtl.css">

<!-- include alertify script -->
<script src="{PATH}/alertify.js"></script>

<!-- then override glossary values -->
<script type="text/javascript">
alertify.defaults.glossary.title = 'أليرتفاي جي اس';
alertify.defaults.glossary.ok = 'موافق';
alertify.defaults.glossary.cancel = 'إلغاء';
</script>

</code> 
</pre>
			<div class="RIGHT">		
				<a class="button" href="javascript:ViewArabic();">
					<span> Run Example </span>
				</a>
			</div>
	<br/>
  </div><!--//Arabic-->
	
	
	
	
<!--Semantic dialogs-->
	<div class="segment">
		<h1 id="semantic"><a href="#semantic"><i class="fa fa-code"></i> </a> Themes -  Semantic UI </h1>
		<hr />
<pre>
<code class="lang-html">
<!-- include alertify.css -->
<link rel="stylesheet" href="{PATH}/alertify.css">

<!-- include semantic ui theme  -->
<link rel="stylesheet" href="{PATH}/themes/semantic.css">

<!-- include alertify script -->
<script src="{PATH}/alertify.js"></script>

<script type="text/javascript">		
//override defaults
alertify.defaults.transition = "zoom";
alertify.defaults.theme.ok = "ui positive button";
alertify.defaults.theme.cancel = "ui black button";
</script>		

</code> 
</pre>
			<div class="RIGHT">		
				<a class="button" href="javascript:ViewSemantic();">
					<span> Run Example </span>
				</a>
			</div>
	<br/>			
  </div><!--//Semantic dialogs-->
	
	
<!--Bootstrap dialogs-->
	<div class="segment">
		<h1 id="bootstrap"><a href="#bootstrap"><i class="fa fa-code"></i> </a> Themes -  Bootstrap </h1>
		<hr />
<pre>
<code class="lang-html">
<!-- include alertify.css -->
<link rel="stylesheet" href="{PATH}/alertify.css">

<!-- include boostrap theme  -->
<link rel="stylesheet" href="{PATH}/themes/bootstrap.css">

<!-- include alertify script -->
<script src="{PATH}/alertify.js"></script>

<script type="text/javascript">
//override defaults
alertify.defaults.transition = "slide";
alertify.defaults.theme.ok = "btn btn-primary";
alertify.defaults.theme.cancel = "btn btn-danger";
alertify.defaults.theme.input = "form-control";
</script>

</code> 
</pre>
			<div class="RIGHT">		
				<a class="button" href="javascript:ViewBootstrap();">
					<span> Run Example </span>
				</a>
			</div>
	<br/>		
  </div><!--//Bootstrap dialogs-->
		


<!--Multi dialogs-->
	<div class="segment">
		<h1 id="multi-dialog"><a href="#multi-dialog"><i class="fa fa-code"></i> </a> Multi Dialogs</h1>
		<hr />
<pre>
<code class="lang-javascript">
window.showAlert = function(){
	alertify.alert('<a href="javascript:showConfirm();">Show Confirm</a>');
}

window.showConfirm = function(){
	alertify.confirm('<a href="javascript:showAlert();">Show Alert</a>');
}
//works with modeless too
alertify.alert().setting('modal', false);
alertify.confirm().setting('modal', false);

window.showAlert();

</code> 
</pre>
			<div class="RIGHT">
				<div class="ui inverted toggle checkbox example">
					<input type="checkbox" >
					<label>Toggle</label>
				</div>
			
				<a class="button example" href="javascript:void(0);">
					<span> Run </span>
				</a>
			</div>
	<br/>
  </div><!--//Multi dialogs-->



	<!--License Agreement-->
		<div class="segment">
			<h1 id="license"><a href="#license"><i class="fa fa-code"></i> </a> License Agreement</h1>
			<hr />

<div id="la" style="display:none">
The MIT License (MIT)

Copyright (c) 2014 Mohammad Younes

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</div>

<pre>
<code class="lang-html">
<div id="la">

The MIT License (MIT)

Copyright (c) 2014 Mohammad Younes

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

</div>
</code> 
</pre>

<pre>
<code class="lang-javascript">
var pre = document.createElement('pre');
//custom style.
pre.style.maxHeight = "400px";
pre.style.margin = "0";
pre.style.padding = "24px";
pre.style.whiteSpace = "pre-wrap";
pre.style.textAlign = "justify";
pre.appendChild(document.createTextNode($('#la').text()));
//show as confirm
alertify.confirm(pre, function(){
		alertify.success('Accepted');
	},function(){
		alertify.error('Declined');
	}).set({labels:{ok:'Accept', cancel: 'Decline'}, padding: false});

</code> 
</pre>
			<div class="RIGHT">
				<a class="button example" href="javascript:void(0);">
					<span> Run </span>
				</a>
			</div>
  </div><!--//License Agreement-->



  <!--Minimal dialog-->
	<div class="segment">
		<h1 id="minimal"><a href="#minimal"><i class="fa fa-code"></i> </a> Minimal Dialog </h1>
		<hr />
<pre>
<code class="lang-javascript">
alertify.minimalDialog || alertify.dialog('minimalDialog',function(){
    return {
        main:function(content){
            this.setContent(content); 
        }
    };
});
alertify.minimalDialog("Minimal button-less dialog.");
</code> 
</pre>
			<div class="RIGHT">		
				<a class="button example" href="javascript:void(0);">
					<span> Run </span>
				</a>
			</div>
	<br/>		
  </div><!--//Minimal dialog-->



  <!--Generic dialog-->
	<div class="segment">
		<h1 id="generic"><a href="#generic"><i class="fa fa-code"></i> </a> Button-Less Generic Dialog </h1>
		<hr />
<div style="display:none">
<form id="loginForm">
    <fieldset>
        <label> Username </label>
        <input type="text" value="Mohammad"/> 

        <label> Password </label>
        <input type="password" value="password"/> 

        <input type="submit" value="Login"/>
    </fieldset>
</form>
</div>
<pre>
<code class="lang-html"><!-- the form to be viewed as dialog-->
<form id="loginForm">
    <fieldset>
        <label> Username </label>
        <input type="text" value="Mohammad"/> 

        <label> Password </label>
        <input type="password" value="password"/> 

        <input type="submit" value="Login"/>
    </fieldset>
</form>
</code>
</pre>
<pre>
<code class="lang-javascript">
alertify.genericDialog || alertify.dialog('genericDialog',function(){
    return {
        main:function(content){
            this.setContent(content);
        },
        setup:function(){
            return {
                focus:{
                    element:function(){
                        return this.elements.body.querySelector(this.get('selector'));
                    },
                    select:true
                },
                options:{
                    basic:true,
                    maximizable:false,
                    resizable:false,
                    padding:false
                }
            };
        },
        settings:{
            selector:undefined
        }
    };
});
//force focusing password box
alertify.genericDialog ($('#loginForm')[0]).set('selector', 'input[type="password"]');
</code> 
</pre>
			<div class="RIGHT">		
				<a class="button example" href="javascript:void(0);">
					<span> Run </span>
				</a>
			</div>
	<br/>		
  </div><!--//Generic dialog-->
		

  <!--Youtube dialog-->
	<div class="segment">
		<h1 id="youtube"><a href="#youtube"><i class="fa fa-code"></i> </a> Youtube Dialog </h1>
		<hr />
<pre>
<code class="lang-javascript">
alertify.YoutubeDialog || alertify.dialog('YoutubeDialog',function(){
    var iframe;
    return {
        // dialog constructor function, this will be called when the user calls alertify.YoutubeDialog(videoId)
        main:function(videoId){
            //set the videoId setting and return current instance for chaining.
            return this.set({ 
                'videoId': videoId
            });
        },
        // we only want to override two options (padding and overflow).
        setup:function(){
            return {
                options:{
                    //disable both padding and overflow control.
                    padding : !1,
                    overflow: !1,
                }
            };
        },
        // This will be called once the DOM is ready and will never be invoked again.
        // Here we create the iframe to embed the video.
        build:function(){           
            // create the iframe element
            iframe = document.createElement('iframe');
            iframe.frameBorder = "no";
            iframe.width = "100%";
            iframe.height = "100%";
            // add it to the dialog
            this.elements.content.appendChild(iframe);

            //give the dialog initial height (half the screen height).
            this.elements.body.style.minHeight = screen.height * .5 + 'px';
        },
        // dialog custom settings
        settings:{
            videoId:undefined
        },
        // listen and respond to changes in dialog settings.
        settingUpdated:function(key, oldValue, newValue){
            switch(key){
               case 'videoId':
                    iframe.src = "https://www.youtube.com/embed/" + newValue + "?enablejsapi=1";
                break;   
            }
        },
        // listen to internal dialog events.
        hooks:{
            // triggered when the dialog is closed, this is seperate from user defined onclose
            onclose: function(){
                iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}','*');
            },
            // triggered when a dialog option gets update.
            // warning! this will not be triggered for settings updates.
            onupdate: function(option,oldValue, newValue){
                switch(option){
                    case 'resizable':
                        if(newValue){
                            this.elements.content.removeAttribute('style');
                            iframe && iframe.removeAttribute('style');
                        }else{
                            this.elements.content.style.minHeight = 'inherit';
                            iframe && (iframe.style.minHeight = 'inherit');
                        }
                    break;    
                }    
            }
        }
    };
});
//show the dialog
alertify.YoutubeDialog('GODhPuM5cEE').set({frameless:false});
</code> 
</pre>
			<div class="RIGHT">		
                <div class="ui inverted toggle checkbox example">
					<input type="checkbox">
					<label>Frameless</label>
				</div>
				<a class="button example" href="javascript:void(0);">
					<span> Run </span>
				</a>
			</div>
	<br/>		


  </div><!--//Youtube dialog-->



		<br/>
		<br/>
	</div>
</section>