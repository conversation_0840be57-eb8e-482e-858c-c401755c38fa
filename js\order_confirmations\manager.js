+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(order_confirmationId, dataTable) {
		$.when(receiveRow(order_confirmationId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(order_confirmationId) {
		return $.ajax({
			url: BASE_URI + 'order_confirmations/receive_order_confirmation/' + order_confirmationId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(order_confirmationId, dataTable) {
		$.when(deleteRow(order_confirmationId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(order_confirmationId) {
		return $.ajax({
			url: BASE_URI + 'order_confirmations/delete/' + order_confirmationId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.order_confirmation').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the order_confirmation ' + docNo + '?',
					function () {
						var order_confirmationId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(order_confirmationId, dataTable);
					}
				).setHeader('<em>receive order_confirmation</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.order_confirmation').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete order confirmation ' + docNo + '?',
					function () {
						var order_confirmationId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(order_confirmationId, dataTable);
					}
				).setHeader('<em>Delete Order Confirmation</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/order_confirmations?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'order_confirmations/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
