<?php
class Release extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = '`release`';
        parent::__construct();
    }

    /**
     * Get Release By ID
     */
    public function get_by_id($release_id)
    {
        $sql = <<<EOT
SELECT `release`.*,
    purchase.purchase_date, purchase.total
FROM `release`
LEFT JOIN release_item ON release_item.release_id = `release`.id
LEFT JOIN purchase ON purchase.id = `release`.purchase_id
WHERE `release`.id = ?
    AND `release`.is_deleted = 0
    AND purchase.is_deleted = 0
EOT;
        $binds = [$release_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
