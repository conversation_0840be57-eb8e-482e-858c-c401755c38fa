<?php
defined('BASEPATH') or exit('No direct script access allowed');
/**
 * MYT Controller Class
 *
 * @package        MYT
 * @subpackage    Application
 * @category    Core
 * <AUTHOR> SoftDev Solutions, Inc.
 * @link        http://mytsoftdevsolutions.com
 */
class MYT_Controller extends CI_Controller
{
    // Page info
    /**
     * @var string
     */
    protected $template = 'main';

    // Page contents
    /**
     * @var array
     */
    public $scripts = [];
    /**
     * @var array
     */
    public $javascript = [];
    /**
     * @var array
     */
    public $css = [];
    /**
     * @var array
     */
    public $g_font = [];
    /**
     * @var mixed
     */
    public $content = false;

    // Page meta
    /**
     * @var mixed
     */
    public $title = false;
    /**
     * @var mixed
     */
    public $description = false;
    /**
     * @var mixed
     */
    public $public_page = false;

    /**
     * @desc build and setup basic info
     */
    public function __construct()
    {
        parent::__construct();

        $this->page_id = strtolower(get_class($this));

        if (!$this->public_page) {
            $this->_access_manager();
        }
    }

    private function _access_manager()
    {
        if (!isset($_SESSION['user'])) {
            redirect(site_url('login'));
        }

        $this->load->model('permission');

        $user = $_SESSION['user'];

        // $class = $this->router->class;
        // $method = $this->router->method;

        $class = $this->uri->segment(1);
        $method = $this->uri->segment(2) ?: 'index';

        if (!$this->permission->is_authorized($user->role_id, $class, $method)) {
            show_404();
        }
    }

    /**
     * @desc render the final page composed on template and page content
     */
    private function _render_page()
    {
        // -----------------------------------
        // STYLES
        // -----------------------------------

        // CSS
        $to_tpl['_styles'] = '';
        if (!empty($this->css)) {
            foreach ($this->css as $c) {
                $to_tpl['_styles'] .= link_tag($c);
            }
        }

        // Google Fonts
        $to_tpl['_google_fonts'] = '';
        if (!empty($this->g_font)) {
            foreach ($this->g_font as $f) {
                $to_tpl['_google_fonts'] .= link_tag('//fonts.googleapis.com/css?family=' . $f);
            }
        }

        // -----------------------------------
        // CONTENT
        // -----------------------------------
        $to_tpl['content'] = $this->content;
        $to_tpl['description'] = $this->description;

        // -----------------------------------
        // SCRIPTS
        // -----------------------------------

        // External Scripts
        $to_tpl['_external_scripts'] = '';
        if (!empty($this->javascript)) {
            foreach ($this->javascript as $js) {
                $to_tpl['_external_scripts'] .= script_tag($js);
            }
        }

        // Inline Scripts
        $to_tpl['_inline_scripts'] = '';
        if (!empty($this->scripts)) {
            $to_tpl['_inline_scripts'] = '<script>';
            foreach ($this->scripts as $script) {
                $to_tpl['_inline_scripts'] .= $script;
            }
            $to_tpl['_inline_scripts'] .= '</script>';
        }

        // Google Analytics
        $to_tpl['_google_analytics'] = '';
        if (defined('ENVIRONMENT')) {
            if ('development' !== ENVIRONMENT) {
                $to_tpl['_google_analytics'] = "<script>var _gaq=[['_setAccount','UA-********-1'],['_setDomainName', 'jakenpoy.com'],['_trackPageview']];(function(d,t){var g=d.createElement(t),s=d.getElementsByTagName(t)[0];g.src=('https:'==location.protocol?'//ssl':'//www')+'.google-analytics.com/ga.js';s.parentNode.insertBefore(g,s)}(document,'script'));</script>";
            }
        }

        // -----------------------------------
        // Finally render the page :)
        // -----------------------------------
        $this->load->view('template/' . $this->template, $to_tpl);

        switch (ENVIRONMENT) {
            case 'development':
                // $this->output->enable_profiler(TRUE);
                break;
            case 'testing':
            case 'production':
                $this->output->enable_profiler(false);
                break;
        }
    }

    /**
     * @desc Create content for the current page
     */
    public function build_content($template, $name, $page_content = [])
    {
        $page_content['title'] = $this->title;
        $this->template = $template;
        $this->content = $this->load->view($name, $page_content, true);
        $this->_render_page();
    }

    /**
     * @desc get function for page_id
     */
    public function get_page_id()
    {
        return $this->page_id;
    }

    /**
     * @desc get function for view
     */
    public function get_view()
    {
        return $this->view;
    }

    /**
     * @desc get function for template
     */
    public function get_template()
    {
        return $this->template;
    }

    /**
     * Collect ETA times from form data
     * @return array Array of ETA times from form fields with names starting with 'eta_time_'
     */
    protected function collect_eta_times_from_form()
    {
        $post_data = $this->input->post();
        $eta_times = [];

        // Collect all ETA times from the form
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'eta_time_') === 0) {
                if (!empty($value)) {
                    $eta_times[] = $value;
                }
            }
        }

        return $eta_times;
    }

}

/*End of file MYT_Controller.php*/
/*Location .application/core/MYT_Controller.php*/
