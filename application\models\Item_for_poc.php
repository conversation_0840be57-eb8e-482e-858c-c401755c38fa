<?php
class Item_for_poc extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'item_for_poc';
        parent::__construct();
    }

    /**
     * Create New Poc Item
     */
    public function generate($item_name, $unit_price, $added_by)
    {
        $sql = <<<EOT
            INSERT INTO item_for_poc(item_name, unit_price, added_by) VALUES(?, ?, ?)
            ON DUPLICATE KEY UPDATE
            id = LAST_INSERT_ID(id),
            item_name = VALUES(item_name),
            unit_price = VALUES(unit_price),
            is_deleted = 0
EOT;
        $binds = [$item_name, $unit_price, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }

    public function get_by_id($item_for_poc)
        {
            $sql = <<<EOT
        SELECT * 
        FROM item_for_poc 
        WHERE id = ? 
        AND is_deleted = 0
EOT;

            // Bind the item ID
            $binds = [$item_for_poc];

            // Execute the query
            if ($res = $this->advanced_query($sql, $binds)) {
                return $res[0]; // Return the first result if found
            } else {
                return null; // Return null if no result found
            }
        }


    public function get_all()
        {
            $sql = <<<EOT
        SELECT id, item_name, unit_price 
        FROM item_for_poc 
        WHERE is_deleted = 0
EOT;


            // Execute the query
            if ($res = $this->advanced_query($sql)) {

                return $res; // Return the array of results
            } else {
                return []; // Return an empty array if no results found
            }
        }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
