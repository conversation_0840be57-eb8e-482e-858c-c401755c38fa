+function ($) {
	'use strict';

	// =========================
	// Row Management Functions
	// =========================

	function addItemRow() {
		var $newRow = $(itemRow());
		var $itemTable = $('.item-table');
		var tripItems = $itemTable.find('.trip_item');

		if (tripItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}

		$itemTable.find('tbody').append($newRow);
		setRowNumbers();
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="trip_item">' +
			'<td>' +
				'<input type="hidden" name="trip_item_new_' + rowId + '" value="' + rowId + '">' +
				'<input type="number" name="trip_no_new_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
			'</td>' +
			'<td>' +
				'<input type="time" name="eta_time_new_' + rowId + '" class="form-control" value="">' +
			'</td>' +
			'<td class="text-center">' +
				'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
		'</tr>';
	}

	function emptyRow() {
		return '<tr class="no-item">' +
			'<td class="text-center" colspan="3">No trips added yet.</td>' +
		'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	function removeItemRow(event) {
		var $row = $(event.currentTarget).closest('.trip_item');
		var $hiddenInput = $row.find('input[name^="trip_item_"]:not([name*="new_"])');

		if ($hiddenInput.length > 0) {
			var tripId = $hiddenInput.val();
			$('<input>').attr({
				type: 'hidden',
				name: 'deleted_trip_' + tripId,
				value: tripId
			}).appendTo($row.closest('form'));
		}

		$row.remove();

		var $itemTable = $(event.delegateTarget);
		var tripItems = $itemTable.find('.trip_item');

		if (tripItems.length < 1) {
			$itemTable.find('tbody').html($(emptyRow()));
		}

		setRowNumbers();
	}

	function setRowNumbers() {
		var customerId = $('select[name="customer"]').val();
		var etaDate = $('input[name="eta_date"]').val();
		var $itemTable = $('.item-table');
		var tripItems = $itemTable.find('.trip_item');

		if (customerId && etaDate) {
			// In reschedule mode, NEVER exclude the current schedule
			// We want to see what trip number this would be as a NEW trip
			var isRescheduleForm = $('title, .title').text().indexOf('Reschedule') !== -1;



			// Get the schedule ID if we're in reschedule mode
			var scheduleId = null;
			var isRescheduleForm = $('title, .title').text().indexOf('Reschedule') !== -1;
			if (isRescheduleForm) {
				scheduleId = $('input[name="schedule_id"]').val();
			}



			$.ajax({
				url: BASE_URI + 'schedules/get_next_trip_number',
				type: 'POST',
				data: {
					customer_id: customerId,
					eta_date: etaDate,
					exclude_schedule_id: scheduleId
				},
				dataType: 'json',
				success: function (response) {
					if (response.next_trip_no) {
						var startingTripNo = response.next_trip_no;
						var newTripIndex = 0;

						tripItems.each(function (_, row) {
							var $tripNoInput = $(row).find('input[name^="trip_no_"]');
							var newTripNo = startingTripNo + newTripIndex;

							$tripNoInput.val(newTripNo);
							newTripIndex++;
						});
					}
				},
				error: function () {
					tripItems.each(function (index, row) {
						var $tripNoInput = $(row).find('input[name^="trip_no_"]');

						// On error, just number sequentially starting from 1
						$tripNoInput.val(index + 1);
					});
				}
			});
		} else {
			tripItems.each(function (index, row) {
				var $tripNoInput = $(row).find('input[name^="trip_no_"]');

				// If no customer/date selected, just number sequentially
				$tripNoInput.val(index + 1);
			});
		}
	}

	// =============================
	// Real-Time Validation Methods
	// =============================

	function checkDuplicateSchedule() {
		var customerId = $('select[name="customer"]').val();
		var etaDate = $('input[name="eta_date"]').val();

		if (customerId && etaDate) {
			$.ajax({
				url: BASE_URI + 'schedules/check_duplicate_schedule',
				type: 'POST',
				data: { customer_id: customerId, eta_date: etaDate },
				dataType: 'json',
				success: function (response) {
					$('.duplicate-schedule-error').remove();

					if (response.is_duplicate) {
						var errorMsg = $('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');
						$('input[name="eta_date"]').closest('.form-group').append(errorMsg);
						$('input[name="eta_date"]').addClass('is-invalid');
					} else {
						$('input[name="eta_date"]').removeClass('is-invalid');
					}
				}
			});
		} else {
			$('.duplicate-schedule-error').remove();
			$('input[name="eta_date"]').removeClass('is-invalid');
		}
	}

	function checkDuplicateTripTimes() {
		var etaTimes = [];
		var hasDuplicates = false;

		$('.item-table input[name^="eta_time_"]').each(function () {
			if ($(this).val()) {
				etaTimes.push($(this).val());
			}
		});

		var uniqueTimes = [...new Set(etaTimes)];
		if (uniqueTimes.length !== etaTimes.length) {
			hasDuplicates = true;
		}

		$('.duplicate-time-error').remove();
		$('.item-table input[name^="eta_time_"]').removeClass('is-invalid');

		if (hasDuplicates) {
			var errorMsg = $('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');
			$('.action-add-item').closest('.form-group').append(errorMsg);

			var timeCounts = {};
			etaTimes.forEach(function (time) {
				timeCounts[time] = (timeCounts[time] || 0) + 1;
			});

			$('.item-table input[name^="eta_time_"]').each(function () {
				if (timeCounts[$(this).val()] > 1) {
					$(this).addClass('is-invalid');
				}
			});
		}
	}

	// ==============
	// Init on Ready
	// ==============

	$(function () {
		var isRescheduleForm = $('title, .title').text().indexOf('Reschedule') !== -1;

		// Disable past dates for ETA date input
		var today = new Date().toISOString().split('T')[0];
		var $etaDate = $('input[name="eta_date"]');
		if ($etaDate.length > 0) {
			$etaDate.attr('min', today);
		}
		$etaDate.on('focus', function () {
			$(this).attr('min', today);
		});

		// Only enable Add Trip functionality if not a reschedule form
		if (!isRescheduleForm) {
			$('.action-add-item').click(addItemRow);

			// Update trip numbers when customer or date changes
			$('select[name="customer"], input[name="eta_date"]').on('change', function() {
				setRowNumbers();
			});
		}

		// Add and delete item handlers
		if (!isRescheduleForm) {
			$('.action-add-item').click(addItemRow);
			$('.item-table').on('click', '.action-delete-item', removeItemRow);
			$('select[name="customer"], input[name="eta_date"]').on('change', setRowNumbers);
		}

		// Initial trip number setup
		if (!isRescheduleForm) {
			setRowNumbers();
		} else {
			// Store the initial date value
			var lastDateValue = $('input[name="eta_date"]').val();

			// In reschedule mode, recalculate when date changes
			$('input[name="eta_date"]').on('change input blur', function() {
				setRowNumbers();
			});

			// Handle Bootstrap datepicker events
			$('input[name="eta_date"]').on('changeDate', function() {
				setRowNumbers();
			});

			// Also check for value changes periodically (fallback)
			setInterval(function() {
				var currentDateValue = $('input[name="eta_date"]').val();
				if (currentDateValue !== lastDateValue) {
					lastDateValue = currentDateValue;
					setRowNumbers();
				}
			}, 500);

			// Trigger immediately if there's already a date value
			// This handles auto-increment when user selects an existing date
			if (lastDateValue) {
				setRowNumbers();
			}
		}

		// Real-time validation
		$('select[name="customer"], input[name="eta_date"]').on('change', checkDuplicateSchedule);
		$('.item-table').on('change input', 'input[name^="eta_time_"]', checkDuplicateTripTimes);

		// Form submission validation
		$('form.crud-box').on('submit', function (e) {
			var customerId = $('select[name="customer"]').val();
			var etaDate = $('input[name="eta_date"]').val();
			var hasEmptyEtaTime = false;
			var hasDuplicateTime = false;
			var etaTimes = [];

			$('.eta-time-error, .duplicate-time-error').remove();
			$('.item-table input[name^="eta_time_"]').removeClass('is-invalid');

			$('.item-table input[name^="eta_time_"]').each(function () {
				if (!$(this).val()) {
					hasEmptyEtaTime = true;
					return false;
				}
				etaTimes.push($(this).val());
			});

			if (hasEmptyEtaTime) {
				var errorMsg = $('<div class="eta-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">ETA Time is required for all trips.</div>');
				$('.action-add-item').closest('.form-group').append(errorMsg);
				e.preventDefault();
			}

			var uniqueTimes = [...new Set(etaTimes)];
			if (uniqueTimes.length !== etaTimes.length) {
				hasDuplicateTime = true;

				var timeCounts = {};
				etaTimes.forEach(function (time) {
					timeCounts[time] = (timeCounts[time] || 0) + 1;
				});

				$('.item-table input[name^="eta_time_"]').each(function () {
					if (timeCounts[$(this).val()] > 1) {
						$(this).addClass('is-invalid');
					}
				});

				var errorMsg = $('<div class="duplicate-time-error" style="color:#d9534f; font-size:14px; margin-top:5px;">Duplicate ETA times are not allowed. Each trip must have a unique time.</div>');
				$('.action-add-item').closest('.form-group').append(errorMsg);
				e.preventDefault();
			}

			if (customerId && etaDate) {
				var hasDuplicate = false;
				$.ajax({
					url: BASE_URI + 'schedules/check_duplicate_schedule',
					type: 'POST',
					data: { customer_id: customerId, eta_date: etaDate },
					dataType: 'json',
					async: false,
					success: function (response) {
						if (response.is_duplicate) {
							hasDuplicate = true;
							var errorMsg = $('<div class="duplicate-schedule-error" style="color:#d9534f; font-size:14px; margin-top:5px;">A schedule already exists for this customer on the selected date.</div>');
							$('input[name="eta_date"]').closest('.form-group').append(errorMsg);
						}
					}
				});
				if (hasDuplicate) e.preventDefault();
			}
		});
	});

}(jQuery);
