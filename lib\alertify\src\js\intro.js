(function (window) {
	'use strict';

	/**
	 * Keys enum
	 * @type {Object}
	 */
	var keys = {
		ENTER: 13,
		ESC: 27,
		F1: 112,
		F12: 123,
		LEFT: 37,
		RIGHT: 39
	};
	/**
	 * Default options
	 * @type {Object}
	 */
	var defaults = {
		autoReset: true,
		basic: false,
		closable: true,
		closableByDimmer: true,
		frameless: false,
		maintainFocus: true, //global default not per instance, applies to all dialogs
		maximizable: true,
		modal: true,
		movable: true,
		moveBounded: false,
		overflow: true,
		padding: true,
		pinnable: true,
		pinned: true,
		preventBodyShift: false, //global default not per instance, applies to all dialogs
		resizable: true,
		startMaximized: false,
		transition: 'pulse',
		notifier: {
			delay: 5,
			position: 'bottom-right',
			closeButton: false
		},
		glossary: {
			title: 'AlertifyJS',
			ok: 'OK',
			cancel: 'Cancel',
			acccpt: 'Accept',
			deny: 'Deny',
			confirm: 'Confirm',
			decline: 'Decline',
			close: 'Close',
			maximize: 'Maximize',
			restore: 'Restore',
		},
		theme: {
			input: 'ajs-input',
			ok: 'ajs-ok',
			cancel: 'ajs-cancel',
		}
	};

	//holds open dialogs instances
	var openDialogs = [];

	/**
	 * [Helper]  Adds the specified class(es) to the element.
	 *
	 * @element {node}      The element
	 * @className {string}  One or more space-separated classes to be added to the class attribute of the element.
	 *
	 * @return {undefined}
	 */
	function addClass(element, classNames) {
		element.className += ' ' + classNames;
	}

	/**
	 * [Helper]  Removes the specified class(es) from the element.
	 *
	 * @element {node}      The element
	 * @className {string}  One or more space-separated classes to be removed from the class attribute of the element.
	 *
	 * @return {undefined}
	 */
	function removeClass(element, classNames) {
		var original = element.className.split(' ');
		var toBeRemoved = classNames.split(' ');
		for (var x = 0; x < toBeRemoved.length; x += 1) {
			var index = original.indexOf(toBeRemoved[x]);
			if (index > -1) {
				original.splice(index, 1);
			}
		}
		element.className = original.join(' ');
	}

	/**
	 * [Helper]  Checks if the document is RTL
	 *
	 * @return {Boolean} True if the document is RTL, false otherwise.
	 */
	function isRightToLeft() {
		return window.getComputedStyle(document.body).direction === 'rtl';
	}
	/**
	 * [Helper]  Get the document current scrollTop
	 *
	 * @return {Number} current document scrollTop value
	 */
	function getScrollTop() {
		return ((document.documentElement && document.documentElement.scrollTop) || document.body.scrollTop);
	}

	/**
	 * [Helper]  Get the document current scrollLeft
	 *
	 * @return {Number} current document scrollLeft value
	 */
	function getScrollLeft() {
		return ((document.documentElement && document.documentElement.scrollLeft) || document.body.scrollLeft);
	}

	/**
	 * Helper: clear contents
	 *
	 */
	function clearContents(element) {
		while (element.lastChild) {
			element.removeChild(element.lastChild);
		}
	}
	/**
	 * Extends a given prototype by merging properties from base into sub.
	 *
	 * @sub {Object} sub The prototype being overwritten.
	 * @base {Object} base The prototype being written.
	 *
	 * @return {Object} The extended prototype.
	 */
	function copy(src) {
		if (null === src) {
			return src;
		}
		var cpy;
		if (Array.isArray(src)) {
			cpy = [];
			for (var x = 0; x < src.length; x += 1) {
				cpy.push(copy(src[x]));
			}
			return cpy;
		}

		if (src instanceof Date) {
			return new Date(src.getTime());
		}

		if (src instanceof RegExp) {
			cpy = new RegExp(src.source);
			cpy.global = src.global;
			cpy.ignoreCase = src.ignoreCase;
			cpy.multiline = src.multiline;
			cpy.lastIndex = src.lastIndex;
			return cpy;
		}

		if (typeof src === 'object') {
			cpy = {};
			// copy dialog pototype over definition.
			for (var prop in src) {
				if (src.hasOwnProperty(prop)) {
					cpy[prop] = copy(src[prop]);
				}
			}
			return cpy;
		}
		return src;
	}
	/**
	 * Helper: destruct the dialog
	 *
	 */
	function destruct(instance, initialize) {
		//delete the dom and it's references.
		var root = instance.elements.root;
		root.parentNode.removeChild(root);
		delete instance.elements;
		//copy back initial settings.
		instance.settings = copy(instance.__settings);
		//re-reference init function.
		instance.__init = initialize;
		//delete __internal variable to allow re-initialization.
		delete instance.__internal;
	}
})();
