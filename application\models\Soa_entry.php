<?php
class Soa_entry extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'soa_entry';
        parent::__construct();
    }

    /**
     * Get by SOA ID
     */
    public function get_by_soa($soa_id)
    {
        $sql = <<<EOT
SELECT *
FROM (
    SELECT soa_entry.entry_date, invoice.invoice_no, soa_entry.entry_type, soa_entry.amount AS charge, 0 AS credit, invoice.added_on
    FROM soa_entry
    LEFT JOIN invoice ON invoice.id = soa_entry.entry_id
    WHERE soa_entry.entry_type = "invoice"
        AND soa_entry.soa_id = ?

    UNION

    SELECT soa_entry.entry_date, invoice.invoice_no, soa_entry.entry_type, 0 AS charge, soa_entry.amount AS credit, paid_invoice.added_on
    FROM soa_entry
    LEFT JOIN paid_invoice ON paid_invoice.id = soa_entry.entry_id
    LEFT JOIN invoice ON invoice.id = paid_invoice.invoice_id
    WHERE soa_entry.entry_type = "payment"
        AND soa_entry.soa_id = ?
) AS entry
ORDER BY entry_date, added_on
EOT;
        $binds = [$soa_id, $soa_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
