!function(t){"use strict";function e(t){for(;/(\d+)(\d{3})/.test(t.toString());)t=t.toString().replace(/(\d+)(\d{3})/,"$1,$2");return t}function a(a){var r=t(a),n=r.find("thead th"),l=r.DataTable({lengthChange:!0,dom:"Bfrtlip",pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:n.length-1}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2 mr-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+r.attr("data-package"),footerCallback:function(a,r,n,l,o){var i=this.api(),s=1,c=i.column(s,{search:"applied"}).data().reduce(function(t,e){var a=parseFloat(e.replace(/[^0-9.-]+/g,""));return t+a},0);t(i.column(s).footer()).html(e(parseFloat(c).toFixed(2)))}});return l}t(function(){let e=a(".tableA");t("#tableFilter").submit(function(a){a.preventDefault();var r=t(a.currentTarget).serialize(),n=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+r;e.ajax.url(n).load(),window.history.pushState(null,null,BASE_URI+"receivables/index?"+r)}).trigger("submit")})}(jQuery);