$(function () {
    $('.action-print').click(function () {
        window.print();
    });

    
});
window.addEventListener('beforeunload', function (e) {
    fetch('<?= site_url("module_2307/delete_image") ?>', {
        method: 'POST',
        body: JSON.stringify({ image_path: '<?= $this->session->userdata("generated_image_path") ?>' }),
        headers: {
            'Content-Type': 'application/json'
        }
    }).then(response => {
        // Handle any response if needed (e.g., logging or errors)
        console.log('Image deletion request sent');
    }).catch(error => {
        console.error('Error deleting image:', error);
    });
});
