<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Checks extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch checks
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'check_slip';
        $primary_key = 'check_slip.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_bank(),
            $this->_get_check_date(),
            $this->_get_check_no(),
            $this->_get_supplier(),
            $this->_get_amount(),
            // $this->_get_particulars(),
            $this->_get_status(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
check_slip
LEFT JOIN bank ON bank.id = check_slip.bank_id
LEFT JOIN supplier ON supplier.id = check_slip.supplier_id
EOT;

        $status = addslashes($this->input->get('status', true)) ?: 'pending';

        if($status === 'deleted'){
            $where = <<<EOT
check_slip.is_deleted = 1
EOT;
        }else{
            $where = <<<EOT
check_slip.is_deleted = 0
EOT;
}
        
        if ($bank_id = $this->input->get('bank', true)) {
            $where .= <<<EOT

AND check_slip.bank_id = {$bank_id}
EOT;
        }

        if ($supplier = $this->input->get('supplier', true)) {
            $where .= <<<EOT

AND check_slip.supplier_id = "{$supplier}"
EOT;
        }

        if($status !== 'deleted'){
            $where .= <<<EOT

AND check_slip.status = "{$status}"
EOT;
        }


        $date = DateTime::createFromFormat('m/d/Y', $this->input->get('from', true));
        $from = $date ? $date->format('Y-m-d') : null;

        $date = DateTime::createFromFormat('m/d/Y', $this->input->get('to', true));
        $to = $date ? $date->format('Y-m-d') : null;

        if (!is_null($from) && !is_null($to)) {
            $where .= <<<EOT

AND check_slip.check_date BETWEEN "{$from}" AND "{$to}"
EOT;
        }

        if (!in_array($_SESSION['user']->role_id,[ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING])) {
            $where .= <<<EOT

AND check_slip.added_by = {$_SESSION['user']->id}
EOT;
        }

        exit(json_encode(SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)));
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'check_slip.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'check_slip.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'check';
            }
        ];
    }

    /**
     * Get bank
     */
    protected function _get_bank()
    {
        return [
            'db' => 'bank.name',
            'as' => 'bank',
            'dt' => 0,
            'field' => 'bank'
        ];
    }

    /**
     * Get check date
     */
    protected function _get_check_date()
    {
        return [
            'db' => 'DATE_FORMAT(check_slip.check_date, "%b %d, %Y")',
            'as' => 'check_date',
            'dt' => 1,
            'field' => 'check_date'
        ];
    }

    /**
     * Get check no
     */
    protected function _get_check_no()
    {
        return [
            'db' => 'check_slip.check_no',
            'dt' => 2,
            'field' => 'check_no'
        ];
    }

    /**
     * Get supplier
     */
    protected function _get_supplier()
    {
        return [
            'db' => 'supplier.name',
            'as' => 'supplier',
            'dt' => 3,
            'field' => 'supplier'
        ];
    }

    /**
     * Get amount
     */
    protected function _get_amount()
    {
        return [
            'db' => 'check_slip.amount',
            'dt' => 4,
            'field' => 'amount',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    /**
     * Get status
     */
    protected function _get_status()
    {
        return [
            'db' => 'check_slip.status',
            'dt' => 'RowStatus',
            'field' => 'status'
        ];
    }

    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'check_slip.id',
            'as' => 'actions',
            'dt' => 5,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-boundary=".dt-asc-wrapper">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                if ($row['status'] === 'pending') {
                    if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING])) {
                        $res .= anchor('checks/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    }
                } elseif ($row['status'] === 'approved') {
                    if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN, ROLE_ACCOUNTING, ROLE_CENTRAL_ACCOUNTING])) {
                        $res .= anchor('checks/preview/' . $d . '/Print', 'Print', 'title="Print" class="dropdown-item text-left"');
                    }
                } else {
                    $res .= anchor('checks/print_checkvoucher/' . $d, 'Print Check Voucher', 'title="Print Check Voucher" class="dropdown-item text-left"');
                    $res .= anchor('checks/preview/' . $d . '/Print', 'Reprint', 'title="Reprint" class="dropdown-item text-left"');
                }

                if ($_SESSION['user']->role_id === ROLE_ADMIN) {
                    if ($row['status'] === 'pending') {
                        // $res .= '<button class="action-approve dropdown-item text-left" role="button" data-id="' . $d . '">Approve</button>';
                        $res .= anchor('checks/edit/' . $d, 'Edit/Approve', 'title="Edit/Approve" class="dropdown-item text-left"');
                    }

                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Void</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
