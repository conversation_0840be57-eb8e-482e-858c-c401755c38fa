!function(e){"use strict";function t(t){var a=e(t),n=(a.find("thead th"),a.DataTable({language:{infoFiltered:""},lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[3,4,5,6,7,8,9,10]}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")}));return n}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"bird_counts/delete/"+t,type:"POST",dataType:"json"})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr").find("td"),i=void 0!==r[1]?r[1].innerHTML:"";alertify.confirm("Are you sure you want to delete the bird count for "+i+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Delete Bird Count</em>")})})}(jQuery);