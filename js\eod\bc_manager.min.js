!function(t){"use strict";function e(e){var a=t(e),n=a.find("thead th"),c=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"desc"]],columnDefs:[{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return c}function a(e,a){return t.ajax({url:BASE_URI+"eod/get_attachments/"+e+"/"+a,type:"GET",dataType:"json"})}function n(e,a){return t.ajax({url:BASE_URI+"eod/delete_attachment/"+e+"/"+a,type:"POST",dataType:"json"})}function c(e,a,c){return new Promise((o,l)=>{function r(){let i="<div>";if(a&&a.length>0){i+='<div class="old-attachments">',i+="<h5>Existing Attachments:</h5>",i+="<ul>";let t=0;for(let n of a)t++,i+=`\n\t\t\t\t\t\t\t<li style="display: flex; align-items: center;">\n\t\t\t\t\t\t\t\t• <a href="${BASE_URI}assets/eod/eod_bc/${e}/${n.file_name}" target="_blank" style="flex-grow: 1;">Attachment ${t}</a>\n\t\t\t\t\t\t\t\t<button type="button" class="delete-attachment" data-id="${n.id}" style="background: none; border: none; color: red; cursor: pointer;">\n\t\t\t\t\t\t\t\t\t<i class="fa fa-trash"></i>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</li>`;i+="</ul>",i+="</div>"}i+='<div class="new-attachments">',i+='<input type="file" id="attachments" name="attachments[]" class="form-control-file" accept=".pdf, image/*" multiple>',i+="</div>",i+="</div>",alertify.confirm(i,function(){let t=document.getElementById("attachments").files;o({files:t})},function(){l("Upload Canceled")}).setting({reverseButtons:!0,closable:!1,title:"Add Attachment"}).show(),t(document).off("click",".delete-attachment"),t(document).on("click",".delete-attachment",function(t){let e=t.currentTarget.getAttribute("data-id");n(e,"eod_bc").done(function(){a=a.filter(t=>t.id!=e),c.ajax.reload(null,!1),r()}).fail(function(t){let e=JSON.parse(t.responseText);alertify.error(e.message)})})}r()})}function o(e,a){var n=new FormData;n.append("table","eod_bc"),n.append("id",e);for(var c=0;c<a.length;c++)n.append("attachments[]",a[c]);return t.ajax({url:BASE_URI+"eod/upload_attachment/",type:"POST",data:n,processData:!1,contentType:!1,dataType:"json"}).then(t=>({success:!0,message:"Files Uploaded."}))}t(function(){var n=e(".tableA"),l=[];t(".tableA").on("click",".action-add",function(t){var e=t.currentTarget.getAttribute("data-id");l=[],a(e,"eod_bc").then(function(t){return t.data.forEach(function(t){t.text=t.file_name,l.push(t)}),c(e,l,n)}).then(({files:t})=>o(e,t)).then(t=>{alertify.success(t.message),window.location.reload()}).catch(t=>{alertify.error(t)})})})}(jQuery);