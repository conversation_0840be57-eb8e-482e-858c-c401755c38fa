+ function ($, fns) {
	'use strict';
	// Register a function that will return sum of column data for Datatables
	if ($.fn.dataTable) {
		$.fn.dataTable.Api.register('sum()', function () {
			return this.flatten().reduce(function (a, b) {
				if (typeof a === 'string') {
					a = a.replace(/[^\d.-]/g, '') * 1;
				}

				if (typeof b === 'string') {
					return b = b.replace(/[^\d.-]/g, '') * 1;
				}

				return a + b;
			}, 0);
		});
	}

	// Initialize autosize
	if (typeof autosize !== 'undefined') {
		autosize($('.autosize'));
	}

	// Initialize Alertify
	if (typeof alertify !== 'undefined') {
		alertify.set('notifier', 'position', 'bottom-center');
	};

	// Initialize select2
	if ($.fn.select2) {
		$('.dropsearch:not([multiple="multiple"])').select2({
			placeholder: $(this).attr('data-placeholder'),
			allowClear: true
		});

		$('.dropsearch[multiple="multiple"]').select2({
			placeholder: $(this).attr('data-placeholder'),
			allowClear: false
		});
	}

	// Initialize time pickers
	if ($.fn.timepicker) {
		$('.timepicker').timepicker({
			timeFormat: 'hh:mm p'
		});
	}

	// Initialize date pickers
	if ($.ui) {
		var today = new Date();
		var plusFiveYears = fns.addYears(today, 5);

		$(document)
			.on('focus', '.datepicker', function (event) {
				$(event.currentTarget).datepicker({
					dateFormat: 'mm/dd/yy',
					changeMonth: true,
					changeYear: true,
					yearRange: '1900:' + fns.format(plusFiveYears, 'YYYY'),
					onChangeMonthYear: function (year, month, inst) {
						$(this).datepicker(
							'setDate',
							new Date(year, month - 1, inst.selectedDay)
						).trigger('change');
					}
				});
			})
			.on('focus', '.datepicker-plus', function(event) {
				$(event.currentTarget).datepicker({
					dateFormat: 'mm/dd/yy',
					changeMonth: true,
					changeYear: true,
					minDate: today,
					yearRange: fns.format(today, 'YYYY') +
						':' +
						fns.format(plusFiveYears, 'YYYY'),
					onChangeMonthYear: function (year, month, inst) {
						$(this).datepicker(
							'setDate',
							new Date(year, month - 1, inst.selectedDay)
						).trigger('change');
					}
				});
			})
			.on('focus', '.datepicker-minus', function(event) {
				$(event.currentTarget).datepicker({
					dateFormat: 'mm/dd/yy',
					changeMonth: true,
					changeYear: true,
					maxDate: today,
					yearRange: '1900:' + fns.format(today, 'YYYY'),
					onChangeMonthYear: function (year, month, inst) {
						$(this).datepicker(
							'setDate',
							new Date(year, month - 1, inst.selectedDay)
						).trigger('change');
					}
				})
			})
			.on('keydown.autocomplete', 'input.go-suggest, textarea.go-suggest', function (event) {
				var element = event.currentTarget;

				$(element)
					.on('keydown', function (event) {
						if (event.keyCode === $.ui.keyCode.TAB && $(element).autocomplete('instance').menu.active) {
							event.preventDefault();
						}
					})
					.autocomplete({
						source: function (request, response) {
							var url = BASE_URI + 'autocomplete/' + element.getAttribute('data-suggest');
							var options = {
								term: request.term.split(/,\s*|\n/).pop()
							};
							$.getJSON(url, options, response);
						},
						search: function () {
							// custom minLength
							var term = element.value.split(/,\s*|\n/).pop();

							if (term.length < 1) {
								return false;
							}
						},
						focus: function () {
							// prevent value inserted on focus
							return false;
						},
						select: function (_, ui) {
							var term = element.value.split(/,\s*|\n/).pop();
							var pattern = term + '$';
							var regex = new RegExp(pattern);

							element.value = term.replace(regex, ui.item.value);

							return false;
						}
					});
			});
	}


  	// Add comma separators to mumbers
	$.fn.addNumericCommas = function (num) {
		var parts = num.toString().split(".");
		parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
		return parts.join(".");
	};

	// Remove comma separators from numbers
	$.fn.removeNumericCommas = function (str) {
		var num = Number(str.replace(/\,/g, ""));
		return num;
	};

	// Generate Random UUID
	$.fn.makeRandomId = function () {
		var text = "";
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	};

	$(function () {
		// Set autocomplete off
		$('input, textarea').attr('autocomplete', 'disabled');

    $('.datepicker').attr('autocomplete', 'off');

		// Reset forms inside modal after closing
		$('.modal').on('hidden.bs.modal', function (event) {
			$(event.currentTarget).find('form').each(function (_, form) {
				form.reset();
				$(form).find('.error').html('');
        $('.btn-process').text('Save');
        $('.btn-process').css('pointer-events', 'auto');
			});
		});

    $('.btn-process').on('click', function() {
      $(this).text('Processing...');
      $(this).css('pointer-events', 'none');
    });

		$(".whole-menu").hide();
    $("#expense").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#sale").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#form").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#report").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#dressing-plant").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#manage-dressing-plant").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

		$(".whole-menu").hide();
    $("#live-sell").on('click', function() {
    	$(this).next(".whole-menu").toggle();
    })

    $('#expense-reveal').on('click', function() {
		// $('#dressing-plant-reveal').click();
    	// $('#live-sell-reveal').click();
    	// $('#manage-dressing-reveal').click();
    	$('#expense').toggle();
    	$('#sale').toggle();
    	$('#form').toggle();
    	$('#report').toggle();
    	$('#manage-dressing-plant').toggle();
    	$('#live-sell').toggle();
    	$('#logout').toggle();
	})

	$('#expense-reveal').on('click', function() {
    	$('#expense-reveal').click();
    })

	$('#sale-reveal').on('click', function() {
    	$('#sale-reveal').click();
    })

	$('#form-reveal').on('click', function() {
    	$('#form-reveal').click();
    })

	$('#report-reveal').on('click', function() {
    	$('#report-reveal').click();
    })

	$('#live-sell-reveal').on('click', function() {
    	$('#live-sell-reveal').click();
    })

	$('#manage-dressing-reveal').on('click', function() {
    	$('#manage-dressing-reveal').click();
    })

	});
}(jQuery, dateFns);
