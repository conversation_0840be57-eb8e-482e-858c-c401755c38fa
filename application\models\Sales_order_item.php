<?php
class Sales_order_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'sales_order_item';
        parent::__construct();
    }

    /**
     * Generate sales_order Item
     */
    public function generate($id, $sales_order_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO sales_order_item(id, sales_order_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, sales_order.id AS sales_order_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM sales_order
    CROSS JOIN item
    WHERE sales_order.id = ?
        AND sales_order.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    sales_order_item.id = LAST_INSERT_ID(sales_order_item.id),
    sales_order_item.qty = VALUES(sales_order_item.qty),
    sales_order_item.unit = VALUES(sales_order_item.unit),
    sales_order_item.updated_by = VALUES(sales_order_item.added_by),
    sales_order_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $sales_order_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get sales_order Item By ID
     */
    public function get_by_id($sales_order_item_id)
    {
        $sql = <<<EOT
SELECT sales_order_item.*, item.name AS item
FROM sales_order_item
LEFT JOIN item ON item.id = sales_order_item.item_id
WHERE sales_order_item.id = ?
    AND sales_order_item.is_deleted = 0
EOT;
        $binds = [$sales_order_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get sales_order Items by sales_order
     */
    public function get_by_sales_order($sales_order_id)
    {
        $sql = <<<EOT
SELECT sales_order_item.*, item.name AS item
FROM sales_order_item
LEFT JOIN item ON item.id = sales_order_item.item_id
WHERE sales_order_item.sales_order_id = ?
    AND sales_order_item.is_deleted = 0
EOT;
        $binds = [$sales_order_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
