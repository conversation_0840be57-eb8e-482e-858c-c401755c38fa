Output.SortAttr
TYPE: bool
VERSION: 3.2.0
DEFAULT: false
--DESCRIPTION--
<p>
  If true, HTML Purifier will sort attributes by name before writing them back
  to the document, converting a tag like: <code>&lt;el b="" a="" c="" /&gt;</code>
  to <code>&lt;el a="" b="" c="" /&gt;</code>. This is a workaround for
  a bug in FCKeditor which causes it to swap attributes order, adding noise
  to text diffs. If you're not seeing this bug, chances are, you don't need
  this directive.
</p>
--# vim: et sw=4 sts=4
