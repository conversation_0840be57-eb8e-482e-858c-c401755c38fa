<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Pos_items extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('pos_items/manager'));
    }

    /**
     * Add POS Item main
     */
    public function add()
    {
        $rules = $this->config->item('pos_items/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('pos_items/manager'));
        } else {

            $this->title = 'Add POS Item';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                "is_deleted" => 0
            ];

            $branches = $this->branch->select("", $where);
            $categories = $this->category->select("", $where);

            $data = [
                'branches' => $branches,
                'categories' => $categories,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'pos_items/add', $data);
        }
    }

    /**
     * Edit Pos Item main
     */
    public function edit($pos_item_id = null)
    {
        $where = [
            'id' => $pos_item_id,
            'is_deleted' => 0
        ];
        $pos_item = $this->pos_item->select('', $where, 1) or show_404();
        $rules = $this->config->item('pos_items/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit($pos_item)) {
            redirect(site_url('pos_items/manager'));
        } else {
            $this->title = 'Edit POS Item';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/users/crud'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                "is_deleted" => 0
            ];

            $branches = $this->branch->select("", $where);
            $categories = $this->category->select("", $where);


            $data = [
                'branches' => $branches,
                'categories' => $categories,
                'form_error' => $form_error,
                'pos_item' => $pos_item
            ];
            $this->build_content('default', 'pos_items/edit', $data);
        }
    }

    /**
     * Delete Pos Item main
     */
    public function delete($pos_item_id = null)
    {
        $where = [
            'id' => $pos_item_id,
            'is_deleted' => 0
        ];
        if (!$pos_item = $this->pos_item->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'POS item is not found.'
            ];
        } elseif (!$this->_attempt_delete($pos_item)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'POS item is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


    public function manager()
    {
        $this->title = 'POS Items';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/pos_items/manager'
        ];
        $this->build_content('default', 'pos_items/manager');
    }


    /**
     * Load pre-requisite models, config files, and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('pos_item');
        $this->load->model('branch');
        $this->load->model('category');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }


      /**
     * Validate form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            case 'upload_error':
                return $this->upload->display_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }



        /**
     * Attempt add
     */
    protected function _attempt_add()
    {
        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'price' => $this->input->post('price'),
            'priority' => $this->input->post('priority') ? : 0,
            'branch_id' => $this->input->post('branch_id'),
            'category_id' => $this->input->post('category_id'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d')
        ];

        if (!$pos_item_id = $this->pos_item->insert($values)) {
            $this->_error = 'server_error';
        }

        return $pos_item_id;
    }

    /**
     * Attempt edit
     */
    protected function _attempt_edit($user)
    {
        $where = [
            'id' => $user->id
        ];

        $values = [
            'name' => $this->input->post('name'),
            'unit' => $this->input->post('unit'),
            'price' => $this->input->post('price'),
            'priority' => $this->input->post('priority'),
            'branch_id' => $this->input->post('branch_id'),
            'category_id' => $this->input->post('category_id'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d')
        ];

        if (!$is_updated = $this->pos_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt delete
     */
    protected function _attempt_delete($pos_item)
    {
        $where = [
            'id' => $pos_item->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d')
        ];

        if (!$is_deleted = $this->pos_item->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

}
