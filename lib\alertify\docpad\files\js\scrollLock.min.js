/*https://github.com/MohammadYounes/jquery-scrollLock*/;(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n(jQuery)})(function(n){function u(n){var t=n.prop("clientWidth"),i=n.prop("offsetWidth"),r=parseInt(n.css("border-right-width"),10),u=parseInt(n.css("border-left-width"),10);return t+u+r<i}var i="onmousewheel"in window?"ActiveXObject"in window?"wheel":"mousewheel":"DOMMouseScroll",t=".scrollLock",r=n.fn.scrollLock;n.fn.scrollLock=function(r){return r=="off"?this.each(function(){n(this).off(t)}):this.each(function(){n(this).on(i+t,function(t){var i,o;if(!t.ctrlKey&&(i=n(this),u(i))){var f=i.scrollTop(),s=i.prop("scrollHeight"),h=i.prop("clientHeight"),e=t.originalEvent.wheelDelta||-1*t.originalEvent.detail||-1*t.originalEvent.deltaY,r=0;t.type=="wheel"&&(o=i.height()/n(window).height(),r=t.originalEvent.deltaY*o);(e>0&&f+r<=0||e<0&&f+r>=s-h)&&(t.stopPropagation(),t.preventDefault(),r&&i.scrollTop(f+r))}})})};n.fn.scrollLock.noConflict=function(){return n.fn.scrollLock=r,this}});