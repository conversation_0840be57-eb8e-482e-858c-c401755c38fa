<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?= $title; ?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Add schedule</span></h5>

				<div class="row">
					<!-- Customer Selection -->
					<div class="col-md-3 form-group">
						<label for="customer" class="control-label">Grower Farm Name/Place</label>
						<select id="customer" class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?= $customer->id ?>" <?= set_select('customer', $customer->id); ?>>
									<?= ucwords($customer->name) ?>
								</option>
							<?php endforeach; ?>
						</select>
						<div class="red-text flash-message">
							<?= form_error('customer'); ?>
						</div>
					</div>

					<!-- WAF / RS Number -->
					<div class="col-md-3 form-group">
						<label for="waf_rs_no" class="control-label">WAF No. / RS No.</label>
						<input type="text" id="waf_rs_no" name="waf_rs_no" class="form-control" value="<?= set_value('waf_rs_no'); ?>">
						<div class="red-text flash-message">
							<?= form_error('waf_rs_no'); ?>
						</div>
					</div>

					<!-- ETA Date -->
					<div class="col-md-3 form-group">
						<label for="eta_date" class="control-label">ETA Date</label>
						<input type="date" id="eta_date" name="eta_date" class="form-control" value="<?= set_value('eta_date'); ?>">
						<div class="red-text flash-message">
							<?= form_error('eta_date'); ?>
						</div>
					</div>

					<!-- Service Type -->
					<div class="col-md-3 form-group">
						<label for="service_type" class="control-label">Service Type</label>
						<select id="service_type" class="form-control" name="service_type">
							<option value="">Select Service Type</option>
							<option value="dressing_only" <?= set_select('service_type', 'dressing_only'); ?>>Dressing Only</option>
							<option value="live_and_dressing" <?= set_select('service_type', 'live_and_dressing'); ?>>Live and Dressing</option>
						</select>
						<div class="red-text flash-message">
							<?= form_error('service_type'); ?>
						</div>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>List of Trip</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">Trip No.</th>
									<th class="text-center" style="width: 20%;">ETA Time</th>
									<th class="text-center" style="width: 10%;">Action</th>
								</tr>
							</thead>
							<tbody>
								<tr class="no-item">
									<td class="text-center" colspan="3">No trips added yet.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<!-- Add Trip Button -->
				<div class="row">
					<div class="col-md-12 form-group">
						<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item">Add Trip</button>
					</div>
				</div>

				<!-- Error Output -->
				<div class="error">
					<?= $form_error; ?>
				</div>

				<hr>

				<!-- Submit & Close -->
				<div class="clearfix text-center">
					<button type="submit" name="submit" class="btn btn-primary">Save</button>
					<a href="<?= site_url('schedules') ?>" class="btn btn-light">Close</a>
				</div>
			</fieldset>
		</form>
	</div>
</div>
