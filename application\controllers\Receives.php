<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Receives extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('receives/manager'));
    }

    /**
     * Add Receiving Receipt
     */
    public function add($purchase_id)
    {
        if (!$purchase = $this->purchase->get_by_id($purchase_id) or
            $purchase->status === 'pending' or
            stripos($purchase->main_usage, WAD_WAREHOUSE) === false
        )  {
            show_404();
        }

        $purchase_items = $this->purchase_item->get_w_state_by_purchase($purchase_id);

        $rules = array_merge(
            $this->config->item('receives/add'),
            $this->_get_purchase_item_rules($purchase_items)
        );

        if ($this->_validate_form($rules) and $receive_id = $this->_attempt_add($purchase, $purchase_items)) {
            redirect(site_url('purchases/receives/' . $purchase->id));
        } else {
            $this->title = 'Add Receiving Receipt';

            $where = [
                'id' => $purchase->supplier_id
            ];
            $supplier = $this->supplier->select('', $where, 1);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'purchase' => $purchase,
                'supplier' => $supplier,
                'purchase_items' => $purchase_items
            ];
            $this->build_content('default', 'receives/add', $data);
        }
    }

    /**
     * Edit Receiving Receipt
     */
    public function edit($receive_id)
    {
        $receive = $this->receive->get_by_id($receive_id) or show_404();

        $receive_items = $this->receive_item->get_by_receive($receive->id);

        $rules = array_merge(
            $this->config->item('receives/edit'),
            $this->_get_receive_item_rules($receive_items)
        );

        $where = [
            'id' => $receive->purchase_id,
            'status <>' => 'pending',
            'is_deleted' => 0
        ];
        $purchase = $this->purchase->select('', $where, 1);

        if ($this->_validate_form($rules) && $this->_attempt_edit($receive, $receive_items)) {
            redirect(site_url('purchases/receives/' . $receive->purchase_id));
        } else {
            $this->title = 'Edit Receiving Receipt';

            $where = [
                'id' => $receive->supplier_id
            ];
            $supplier = $this->supplier->select('', $where, 1);

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'receive' => $receive,
                'supplier' => $supplier,
                'receive_items' => $receive_items
            ];
            $this->build_content('default', 'receives/edit', $data);
        }
    }

    /**
     * Delete Receiving Receipt
     */
    public function delete($receive_id)
    {
        if (!$receive = $this->receive->get_by_id($receive_id)) {
            http_response_code(400);
            $response = [
                'message' => 'Receiving Receipt is not found.'
            ];
        } elseif (!$this->_attempt_delete($receive)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Receiving Receipt is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Receiving Receipts
     */
    public function manager()
    {
        $this->title = 'Receiving Receipts';
        $this->css = [
            'lib/select2/dist/css/select2',
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/select2/dist/js/select2',
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/receives/manager'
        ];
        $this->build_content('default', 'receives/manager');
    }

    /**
     * Receiving Receipt Preview
     */
    public function preview($receive_id)
    {
        $receive = $this->receive->get_by_id($receive_id) or show_404();

        $this->title = 'Preview: Receiving Receipt No. ' . $receive->id;
        $this->javascript = [
            'js/receives/preview'
        ];

        $where = [
            'id' => $receive->supplier_id
        ];
        $supplier = $this->supplier->select('', $where, 1);

        $receive_items = $this->receive_item->get_by_receive($receive->id);

        $data = [
            'receive' => $receive,
            'supplier' => $supplier,
            'receive_items' => $receive_items
        ];
        $this->build_content('default', 'receives/preview', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('receive');
        $this->load->model('receive_item');
        $this->load->model('purchase');
        $this->load->model('purchase_item');
        $this->load->model('supplier');
        $this->load->model('item');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return $error ? '<p>' . $error . '</p>' : '';
        }
    }

    /**
     * Get Purchase Order Item Rules
     */
    protected function _get_purchase_item_rules($purchase_items)
    {
        $rules = [];

        foreach ($purchase_items as $purchase_item) {
            $rules[] = [
                'field' => 'received_' . $purchase_item->id,
                'label' => 'Received Qty',
                'rules' => sprintf(
                    'trim|numeric|greater_than_equal_to|less_than_equal_to[%d]',
                    $purchase_item->qty - $purchase_item->received
                )
            ];
        }

        return $rules;
    }

    /**
     * Get Purchase Order Item Data
     */
    protected function _get_purchase_item_data($purchase_items)
    {
        $data = [];

        foreach ($purchase_items as $purchase_item) {
            $data[] = [
                'purchase_item_id' => $purchase_item->id,
                'item_id' => $purchase_item->item_id,
                'qty' => $this->input->post('received_' . $purchase_item->id) ?: 0,
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($purchase, $purchase_items)
    {
        if (!$receive_items = $this->_get_purchase_item_data($purchase_items)) {
            $this->_error = 'no_receive_item';
            return false;
        }

        $receive_date = DateTime::createFromFormat('m/d/Y', $this->input->post('receive_date'));
        $values = [
            'purchase_id' => $purchase->id,
            'supplier_id' => $purchase->supplier_id,
            'receive_date' => $receive_date->format('Y-m-d'),
            'added_by' => $_SESSION['user']->id
        ];

        $this->db->trans_begin();

        if (!$receive_id = $this->receive->insert($values) or
            !$this->_generate_receive_items($receive_id, $receive_items)
        ) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $receive_id;
        }
    }

    /**
     * Generate Receiving Receipt Items
     */
    protected function _generate_receive_items($receive_id, $receive_items)
    {
        foreach ($receive_items as $receive_item) {
            if (!$this->receive_item->generate(
                $receive_id,
                $receive_item['purchase_item_id'],
                $receive_item['item_id'],
                $receive_item['qty'],
                $receive_item['added_by']
            )) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get Item Rules
     */
    protected function _get_receive_item_rules($receive_items)
    {
        $rules = [];

        foreach ($receive_items as $receive_item) {
            $rules[] = [
                'field' => sprintf('received_%s', $receive_item->purchase_item_id),
                'label' => 'Received',
                'rules' => sprintf(
                    'trim|numeric|greater_than_equal_to|less_than_equal_to[%d]',
                    $receive_item->ordered - $receive_item->received + $receive_item->qty
                )
            ];
        }

        return $rules;
    }

    /**
     * Get Item Data
     */
    protected function _get_receive_item_data($receive_items)
    {
        $data = [];

        foreach ($receive_items as $receive_item) {
            $data[] = [
                'purchase_item_id' => $receive_item->purchase_item_id,
                'item_id' => $receive_item->item_id,
                'qty' => $this->input->post('received_' . $receive_item->purchase_item_id) ?: 0,
                'added_by' => $_SESSION['user']->id
            ];
        }

        return $data;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($receive, $receive_items)
    {
        $this->db->trans_begin();

        $where = [
            'receive_id' => $receive->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];
        if (!$this->receive_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$receive_items = $this->_get_receive_item_data($receive_items)) {
            $this->_error = 'no_receive_item';
            return false;
        }

        $where = [
            'id' => $receive->id
        ];
        $receive_date = DateTime::createFromFormat('m/d/Y', $this->input->post('receive_date'));
        $values = [
            'receive_date' => $receive_date->format('Y-m-d'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->receive->update($where, $values) ||
            !$this->_generate_receive_items($receive->id, $receive_items)
        ) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($receive)
    {
        $where = [
            'id' => $receive->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->receive->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Callback: Check if Supplier Exists
     */
    public function _is_supplier($supplier_id)
    {
        $supplier = $this->supplier->get_by_id($_SESSION['user'], $supplier_id);
        return !empty($supplier);
    }

    /**
     * Get Receives with Balance
     */
    public function get_w_balance($supplier_id)
    {

        $where = <<<EOT
is_deleted = 0
AND check_slip_id IS NULL
EOT;
// AND supplier_id = {$supplier_id}


        if (!$receives = $this->purchase->select('',$where)) {
            http_response_code(400);
            $response = [
                'message' => 'Purchase Invoice is not found.'
            ];
        } else {
            $response = [
                'message' => 'Purchase Invoice is found.',
                'purchases' => $receives
            ];
        }

        echo json_encode($response);
    }

}
