<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="./jquery.datetimepicker.css"/>
<style type="text/css">

.custom-date-style {
	background-color: red !important;
}

.input{	
}
.input-wide{
	width: 500px;
}

</style>
</head>
<body>
	
	<p><a href="http://xdsoft.net/jqplugins/datetimepicker/">Homepage</a></p>
	<h3>DateTimePicker</h3>
	<input type="text" value="" id="datetimepicker"/><br><br>
  <h3>DateTimePickers selected by class</h3>
	<input type="text" class="some_class" value="" id="some_class_1"/>
	<input type="text" class="some_class" value="" id="some_class_2"/>
	<h3>Mask DateTimePicker</h3>
	<input type="text" value="" id="datetimepicker_mask"/><br><br>
	<h3>TimePicker</h3>
	<input type="text" id="datetimepicker1"/><br><br>
	<h3>DatePicker</h3>
	<input type="text" id="datetimepicker2"/><br><br>
	<h3>Inline DateTimePicker</h3>
	<!--<div id="console" style="background-color:#fff;color:red">sdfdsfsdf</div>-->
	<input type="text" id="datetimepicker3"/><input type="button" onclick="$('#datetimepicker3').datetimepicker({value:'2011/12/11 12:00'})" value="set inline value 2011/12/11 12:00"/><br><br>
	<h3>Button Trigger</h3>
	<input type="text" value="2013/12/03 18:00" id="datetimepicker4"/><input id="open" type="button" value="open"/><input id="close" type="button" value="close"/><input id="reset" type="button" value="reset"/>
	<h3>TimePicker allows time</h3>
	<input type="text" id="datetimepicker5"/><br><br>
	<h3>Destroy DateTimePicker</h3>
	<input type="text" id="datetimepicker6"/><input id="destroy" type="button" value="destroy"/>
	<h3>Set options runtime DateTimePicker</h3>
	<input type="text" id="datetimepicker7"/>
	<p>If select day is Saturday, the minimum set 11:00, otherwise 8:00</p>
	<h3>onGenerate</h3>
	<input type="text" id="datetimepicker8"/>
	<h3>disable all weekend</h3>
	<input type="text" id="datetimepicker9"/>
	<h3>Default date and time </h3>
	<input type="text" id="default_datetimepicker"/>
	<h3>Show inline</h3>
	<a href="javascript:void(0)" onclick="var si = document.getElementById('show_inline').style; si.display = (si.display=='none')?'block':'none';return false; ">Show/Hide</a>
	<div id="show_inline" style="display:none">
		<input type="text" id="datetimepicker10"/>
	</div>
	<h3>Disable Specific Dates</h3>
	<p>Disable the dates 2 days from now.</p>
	<input type="text" id="datetimepicker11"/>
	<h3>Custom Date Styling</h3>
	<p>Make the background of the date 2 days from now bright red.</p>
	<input type="text" id="datetimepicker12"/>
	<h3>Dark theme</h3>
	<p>thank for this <a href="https://github.com/lampslave">https://github.com/lampslave</a></p>
	<input type="text" id="datetimepicker_dark"/>
	<h3>Date time format and locale</h3>
	<p></p>
	<select id="datetimepicker_format_locale">
		<option value="en">English</option>
		<option value="de">German</option>
		<option value="ru">Russian</option>
		<option value="uk">Ukrainian</option>
		<option value="fr">French</option>
		<option value="es">Spanish</option>
	</select>
	<input type="text" value="D, l, M, F, Y-m-d H:i:s" id="datetimepicker_format_value"/>
	<input type="button" value="applay =>" id="datetimepicker_format_change"/>
	<input type="text" id="datetimepicker_format" class="input input-wide"/>
</body>
<script src="./jquery.js"></script>
<script src="build/jquery.datetimepicker.full.js"></script>
<script>/*
window.onerror = function(errorMsg) {
	$('#console').html($('#console').html()+'<br>'+errorMsg)
}*/

$.datetimepicker.setLocale('en');

$('#datetimepicker_format').datetimepicker({value:'2015/04/15 05:03', format: $("#datetimepicker_format_value").val()});
console.log($('#datetimepicker_format').datetimepicker('getValue'));

$("#datetimepicker_format_change").on("click", function(e){
	$("#datetimepicker_format").data('xdsoft_datetimepicker').setOptions({format: $("#datetimepicker_format_value").val()});
});
$("#datetimepicker_format_locale").on("change", function(e){
	$.datetimepicker.setLocale($(e.currentTarget).val());
});

$('#datetimepicker').datetimepicker({
dayOfWeekStart : 1,
lang:'en',
disabledDates:['1986/01/08','1986/01/09','1986/01/10'],
startDate:	'1986/01/05'
});
$('#datetimepicker').datetimepicker({value:'2015/04/15 05:03',step:10});

$('.some_class').datetimepicker();

$('#default_datetimepicker').datetimepicker({
	formatTime:'H:i',
	formatDate:'d.m.Y',
	//defaultDate:'8.12.1986', // it's my birthday
	defaultDate:'+03.01.1970', // it's my birthday
	defaultTime:'10:00',
	timepickerScrollbar:false
});

$('#datetimepicker10').datetimepicker({
	step:5,
	inline:true
});
$('#datetimepicker_mask').datetimepicker({
	mask:'9999/19/39 29:59'
});

$('#datetimepicker1').datetimepicker({
	datepicker:false,
	format:'H:i',
	step:5
});
$('#datetimepicker2').datetimepicker({
	yearOffset:222,
	lang:'ch',
	timepicker:false,
	format:'d/m/Y',
	formatDate:'Y/m/d',
	minDate:'-1970/01/02', // yesterday is minimum date
	maxDate:'+1970/01/02' // and tommorow is maximum date calendar
});
$('#datetimepicker3').datetimepicker({
	inline:true
});
$('#datetimepicker4').datetimepicker();
$('#open').click(function(){
	$('#datetimepicker4').datetimepicker('show');
});
$('#close').click(function(){
	$('#datetimepicker4').datetimepicker('hide');
});
$('#reset').click(function(){
	$('#datetimepicker4').datetimepicker('reset');
});
$('#datetimepicker5').datetimepicker({
	datepicker:false,
	allowTimes:['12:00','13:00','15:00','17:00','17:05','17:20','19:00','20:00'],
	step:5
});
$('#datetimepicker6').datetimepicker();
$('#destroy').click(function(){
	if( $('#datetimepicker6').data('xdsoft_datetimepicker') ){
		$('#datetimepicker6').datetimepicker('destroy');
		this.value = 'create';
	}else{
		$('#datetimepicker6').datetimepicker();
		this.value = 'destroy';
	}
});
var logic = function( currentDateTime ){
	if (currentDateTime && currentDateTime.getDay() == 6){
		this.setOptions({
			minTime:'11:00'
		});
	}else
		this.setOptions({
			minTime:'8:00'
		});
};
$('#datetimepicker7').datetimepicker({
	onChangeDateTime:logic,
	onShow:logic
});
$('#datetimepicker8').datetimepicker({
	onGenerate:function( ct ){
		$(this).find('.xdsoft_date')
			.toggleClass('xdsoft_disabled');
	},
	minDate:'-1970/01/2',
	maxDate:'+1970/01/2',
	timepicker:false
});
$('#datetimepicker9').datetimepicker({
	onGenerate:function( ct ){
		$(this).find('.xdsoft_date.xdsoft_weekend')
			.addClass('xdsoft_disabled');
	},
	weekends:['01.01.2014','02.01.2014','03.01.2014','04.01.2014','05.01.2014','06.01.2014'],
	timepicker:false
});
var dateToDisable = new Date();
	dateToDisable.setDate(dateToDisable.getDate() + 2);
$('#datetimepicker11').datetimepicker({
	beforeShowDay: function(date) {
		if (date.getMonth() == dateToDisable.getMonth() && date.getDate() == dateToDisable.getDate()) {
			return [false, ""]
		}

		return [true, ""];
	}
});
$('#datetimepicker12').datetimepicker({
	beforeShowDay: function(date) {
		if (date.getMonth() == dateToDisable.getMonth() && date.getDate() == dateToDisable.getDate()) {
			return [true, "custom-date-style"];
		}

		return [true, ""];
	}
});
$('#datetimepicker_dark').datetimepicker({theme:'dark'})


</script>
</html>
