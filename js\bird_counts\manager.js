+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [
				{
					className: 'text-right',
					targets: [3, 4, 5, 6, 7, 8, 9, 10]
				}
			],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(birdCountId, dataTable) {
		$.when(deleteRow(birdCountId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(birdCountId) {
		return $.ajax({
			url: BASE_URI + 'bird_counts/delete/' + birdCountId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr').find('td');
			var growerFarm = typeof cols[1] !== 'undefined' ? cols[1].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete the bird count for ' + growerFarm + '?',
				function () {
					var birdCountId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(birdCountId, dataTable);
				}
			).setHeader('<em>Delete Bird Count</em>');
		});
	});
}(jQuery);