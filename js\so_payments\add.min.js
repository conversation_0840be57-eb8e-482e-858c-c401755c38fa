!function(t,n){"use strict";function e(){return t.ajax({url:BASE_URI+"order_slips/partially_paid",type:"GET",dataType:"json"})}function a(n){var e=t(i());r(e,n);var a=t(".item-table"),o=a.find(".paid_invoice");0===o.length&&a.find(".no-item").remove(),a.find("tbody").append(e)}function i(){var t=o();return'<tr class="paid_invoice"><td><input type="hidden" name="paid_invoice_'+t+'" value="'+t+'"><select name="invoice_'+t+'" class="form-control dropsearch" data-placeholder=""></select></td><td><input type="text" name="customer_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="invoice_date_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="text" name="terms_'+t+'" class="form-control-plaintext" value="" readonly></td><td><input type="number" name="total_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="paid_amount_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="balance_'+t+'" class="form-control-plaintext" value="" min="0" step="any" readonly></td><td><input type="number" name="amount_'+t+'" class="form-control" value="" min="0" step="any"></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger action-delete-dr">Delete</button></td></tr>'}function o(){for(var t="",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",e=0;e<32;e++)t+=n.charAt(Math.floor(Math.random()*n.length));return t}function r(n,e){n.find('select[name^="invoice"]').select2({data:e,placeholder:t(this).attr("data-placeholder"),allowClear:!0,width:"100%"})}function l(n){return t.ajax({url:BASE_URI+"order_slips/info/"+n,type:"GET",dataType:"json"})}function d(t,e){var a="",i="",o="",r=0,l=0,d=0;console.log("WORKING = ",e),null!==e&&(a=e.customer,i=n.format(n.parse(e.order_slip_date),"MMM DD, YYYY"),o=e.terms,r=parseFloat(e.grand_total),isNaN(r)&&(r=0),l=parseFloat(e.paid_amount),l=l||0,isNaN(l)&&(l=0),d=r-l,isNaN(d)&&(d=0)),t.find('input[name^="customer"]').val(a),t.find('input[name^="invoice_date"]').val(i),t.find('input[name^="terms"]').val(o),t.find('input[name^="total"]').val(r>0?r.toFixed(2):""),t.find('input[name^="paid_amount"]').val(l>0?l.toFixed(2):0),t.find('input[name^="balance"]').val(d>0?d.toFixed(2):""),t.find('input[name^="amount"]').val(d>0?d.toFixed(2):"")}function c(n){t(n.currentTarget).closest(".paid_invoice").remove();var e=t(n.delegateTarget),a=e.find(".paid_invoice");if(a.length<1){var i=t(u());e.find("tbody").html(i)}p()}function u(){return'<tr class="no-item"><td class="text-center" colspan="9">No invoice added yet.</td></tr>'}function p(){var n=0,e=0,a=0,i=0;t(".item-table").find(".paid_invoice").each(function(o,r){var l=s(t(r));n+=l.total,e+=l.paidAmount,a+=l.balance,i+=l.amount}),t('input[name="z_total"]').val(n.toFixed(2)),t('input[name="z_paid_amount"]').val(e.toFixed(2)),t('input[name="z_balance"]').val(a.toFixed(2)),t('input[name="z_amount"]').val(i.toFixed(2)),t('input[name="cash_amount"]').val(i.toFixed(2)),t('input[name="check_amount"]').val(i.toFixed(2))}function s(t){var n=parseFloat(t.find('input[name^="total"]').val());isNaN(n)&&(n=0);var e=parseFloat(t.find('input[name^="paid_amount"]').val());isNaN(e)&&(e=0);var a=parseFloat(t.find('input[name^="balance"]').val());isNaN(a)&&(a=0);var i=parseFloat(t.find('input[name^="amount"]').val());return isNaN(i)&&(i=0),{total:n,paidAmount:e,balance:a,amount:i}}t(function(){var n=[{id:"",text:""}];t.when(e()).then(function(t){t.order_slips.forEach(function(t){t.text=t.id,n.push(t)})}),t(".action-add-dr").click(function(){a(n)}),t(".item-table").on("change",'select[name^="invoice"]',function(n){var e=t(n.delegateTarget).find('select[name^="invoice"] option[value="'+n.currentTarget.value+'"]:selected');if(e.length>1)return alert("Invoice No already selected."),void t(n.currentTarget).val("").trigger("change");var a=t(n.currentTarget).closest(".paid_invoice");t.when(l(n.currentTarget.value)).then(function(t){d(a,t.order_slip),p()},function(){d(a,null),p()})}).on("click",".action-delete-dr",function(t){c(t)}).on("change",'input[name^="amount"]',p).trigger("change"),t('input[name="type"]').change(function(n){t(".so_payment-type").addClass("d-none");var e=n.currentTarget.value;t("#"+e).removeClass("d-none")}),p()})}(jQuery,dateFns);