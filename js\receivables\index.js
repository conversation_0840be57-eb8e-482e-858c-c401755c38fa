+ function ($) {
    'use strict';

	function addNumberCommas(val) {
        while (/(\d+)(\d{3})/.test(val.toString())) {
            val = val.toString().replace(/(\d+)(\d{3})/, '$1' + ',' + '$2');
        }
        return val;
    }

    function createDataTable(selector) {
        var $table = $(selector);
        var cols = $table.find('thead th');

        var dataTable = $table.DataTable({
            lengthChange: true,
            // dom: 'B<"bottom"fl>rtip',
            dom: 'Bfrtlip',
            pagingType: 'full_numbers',
            order: [
                [0, 'asc']
            ],
            columnDefs: [{
                className: 'text-right',
                targets: cols.length - 1
            }],
            buttons: [{
                    extend: 'print',
                    className: 'btn btn-secondary',
                    footer: true,
                    exportOptions: {
                        stripHtml: false
                    }
                },
                {
                    extend: 'excel',
                    className: 'btn btn-success ml-2 mr-2',
                    text: 'Download Excel File',
                    footer: true,
                    exportOptions: {
                        columns: 'th'
                    }
                }
            ],
            autoWidth: false,
            processing: true,
            serverSide: true,
            ajax: BASE_URI + 'fetch/' + $table.attr('data-package'),
            footerCallback: function (row, data, start, end, display) {
                var api = this.api();
                var columnIdx = 1; // Assuming "Amount" column is the second column (index 1)

                // Calculate total sum of amounts
                var total = api
                    .column(columnIdx, { search: 'applied' })
                    .data()
                    .reduce(function (acc, curr) {
                        // Parse amount values properly
                        var amount = parseFloat(curr.replace(/[^0-9.-]+/g,""));
                        return acc + amount;
                    }, 0);

                // Output total sum in the footer row
                //$(api.column(columnIdx).footer()).html(total.toFixed(2));
				$(api.column(columnIdx).footer())
                        .html(addNumberCommas(parseFloat(total).toFixed(2)));
            }
        });

        return dataTable;
    }

    $(function () {
        let dataTable = createDataTable('.tableA');

        // $('.tableA').on('click', 'td', function (event) {
        //         var columnIndex = $(this).index();

        //         if (columnIndex === 0) {
        //             var previewLink = $(this).find('#redirect').attr('data-href');
        //             console.log(previewLink);
        //             window.open(previewLink);
        //         }
        //     });

        $('#tableFilter')
            .submit(function (event) {
                event.preventDefault();

                var queries = $(event.currentTarget).serialize();

                var url = BASE_URI + 'fetch/' + $('.tableA').attr('data-package') + '?' + queries;
                dataTable.ajax.url(url).load();

                window.history.pushState(null, null, BASE_URI + 'receivables/index?' + queries);
            }).trigger('submit');
    });
}(jQuery);
