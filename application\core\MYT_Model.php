<?php
defined('BASEPATH') or exit('No direct script access allowed');
/**
 * MYT Model Class
 *
 * @package        MYT
 * @subpackage    Application
 * @category    Core
 * <AUTHOR> SoftDev Solutions, Inc.
 * @link        http://mytsoftdevsolutions.com
 */
class MYT_Model extends CI_Model
{
    /**
     * @var mixed
     */
    protected $table_name = null;

    /**
     * Constructor
     *
     * @access public
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Generic Select
     *
     * @access    public
     * @param    string            table columns
     * @param    mixed            where conditions in this format - array('column' => 'data') - or - "name='Joe' AND status='boss' OR status='active'"
     * @param    int                limit; single select must be implicitly set
     * @param    mixed            order by in this format - 'title desc, name asc, id random' - or - 'rand()'
     * @return    array/object
     */
    public function select($columns = null, $conditions = null, $limit = null, $order = null, $offset = null)
    {
        if (!empty($columns)) {
            $this->db->select($columns);
        }

        if (!empty($conditions)) {
            $this->db->where($conditions);
        }

        if (!empty($order)) {
            $this->db->order_by($order);
        }

        if (!empty($limit) && !empty($offset)) {
            $query = $this->db->get($this->table_name, $limit, $offset);
        } elseif (!empty($limit)) {
            $query = $this->db->get($this->table_name, $limit);
        } else {
            $query = $this->db->get($this->table_name);
        }

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());

            return false;
        } else {
            if ($this->db->db_debug) {
                log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
                log_message('debug', $this->db->last_query());
            }

            if ($limit !== 1) {
                return $query->result();
            } else {
                return $query->row();
            }
        }
    }

    /**
     * Generic Insert
     *
     * @access    public
     * @param    array    values in this format - array('column' => 'value')
     * @return    int        id of inserted row
     */
    public function insert($values)
    {
        $query = $this->db->insert($this->table_name, $values);

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());

            return false;
        } else {
            if ($this->db->db_debug) {
                log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
                log_message('debug', $this->db->last_query());
            }

            return $this->db->insert_id();
        }
    }

    /**
     * Batch Insert
     *
     * @access    public
     * @param    array    data
     * @return    int        id of inserted row
     */
    public function insert_batch($data)
    {
        $query = $this->db->insert_batch($this->table_name, $data);

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());
        } else {
            if ($this->db->db_debug) {
                log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
                log_message('debug', $this->db->last_query());
            }
        }

        return $query;
    }

    /**
     * Generic Update
     *
     * @access    public
     * @param    array    where conditions in this format - array('column' => 'data')
     * @param    array    values this format - array('column' => 'value')
     * @return    boolean if update query is successful
     */
    public function update($conditions, $values)
    {
        $query = $this->db->update($this->table_name, $values, $conditions);

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());

            return false;
        } elseif ($this->db->db_debug) {
            log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
            log_message('debug', $this->db->last_query());
        }

        return true;
    }

    /**
     * Batch Update
     *
     * @access    public
     * @param    array    data
     * @param    string    where key
     * @return    boolean if update query is successful
     */
    public function update_batch($data, $where_key)
    {
        $query = $this->db->update_batch($this->table_name, $data, $where_key);

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());

            return false;
        } elseif ($this->db->db_debug) {
            log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
            log_message('debug', $this->db->last_query());
        }

        return true;
    }

    /**
     * Generic Delete
     *
     * @access    public
     * @param    array    where conditions in this format - array('column' => 'data')
     */
    public function delete($conditions)
    {
        $this->db->where($conditions);
        $query = $this->db->delete($this->table_name);

        if (!$query) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());

            return false;
        } elseif ($this->db->db_debug) {
            log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
            log_message('debug', $this->db->last_query());
        }

        return true;
    }

    /**
     * Advanced Query (Based on CodeIgniter's Query public function)
     *
     * WARNING: USE AS A LAST OPTION; THIS DOES NOT ESCAPE THE STRING
     * TIP: USE BINDINGS TO ESCAPE VALUES
     *
     * Accepts an SQL string as input and returns a result object upon
     * successful execution of a "read" type query.  Returns boolean TRUE
     * upon successful execution of a "write" type query. Returns boolean
     * FALSE upon failure, and if the $db_debug variable is Save to TRUE
     * will raise an error.
     *
     * @access    public
     * @param    string
     * @param    boolean
     * @return    mixed
     */
    public function advanced_query($sql, $binds = false, $strict = true)
    {
        $this->db->trans_start();
        $this->db->trans_strict($strict);
        if ($binds !== false) {
            $sql = $this->db->compile_binds($sql, $binds);
        }
        $query = $this->db->query($sql);
        $this->db->trans_complete();

        if (!$this->db->trans_status()) {
            log_message('error', 'Experiencing query error:');
            log_message('error', $this->db->error());
            log_message('error', 'in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method());
            log_message('error', 'SQL Query:');
            log_message('error', $this->db->last_query());
        } elseif ($this->db->db_debug) {
            log_message('debug', 'SQL Query in ' . $this->router->fetch_class() . '/' . $this->router->fetch_method() . ':');
            log_message('debug', $this->db->last_query());
        }

        return is_bool($query) ? $query : $query->result();
    }

    /**
     * Get Count of Rows
     *
     * @access    public
     * @param    array
     */
    public function count($conditions = null)
    {
        if (!empty($conditions)) {
            $this->db->where($conditions);
        }

        $this->db->from($this->table_name);

        return $this->db->count_all_results();
    }
}
// END MYT_Model Class

/* End of file MYT_Model.php */
/* Location: ./application/core/MYT_Model.php */
