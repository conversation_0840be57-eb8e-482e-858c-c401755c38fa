$(function() {

    // Handle grower farm selection to populate trips
    $('#grower_farm').on('change', function() {
        var customerId = $(this).val();
        var tripSelect = $('#hauling_log_id');

        tripSelect.prop('disabled', true).html('<option value="">Loading trips...</option>');
        $('#net_weight_display').val('');
        $('#net_weight').val('');

        if (customerId) {
            var baseUri = BASE_URI.endsWith('/') ? BASE_URI : BASE_URI + '/';
            var ajaxUrl = baseUri + 'bird_counts/get_trips_by_customer/' + encodeURIComponent(customerId);
       

            $.ajax({
                url: ajaxUrl,
                type: 'GET',
                dataType: 'json',
                cache: false,
                success: function(response) {
                    if (response && response.success && response.trips && response.trips.length > 0) {
                        var options = '<option value="">Select Trip No.</option>';
                        $.each(response.trips, function(_, trip) {
                            var tripDisplay = 'Trip ' + trip.trip_no;
                            if (trip.formatted_date && trip.formatted_time) {
                                tripDisplay += ' - ' + trip.formatted_date + ' ' + trip.formatted_time;
                            }
                            options += '<option value="' + trip.hauling_log_id + '" data-net-weight="' + trip.net_weight + '">' + tripDisplay + '</option>';
                        });
                        tripSelect.html(options).prop('disabled', false);
                    } else {
                        tripSelect.html('<option value="">No trips available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    tripSelect.html('<option value="">Error loading trips</option>');
                }
            });
        } else {
            tripSelect.html('<option value="">Select Trip No.</option>');
        }
    });

    // Handle trip selection to populate net weight
    $('#hauling_log_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var haulingLogId = $(this).val();

        if (haulingLogId && selectedOption.val()) {
            var netWeight = selectedOption.data('net-weight');

          
            var formattedNetWeight = formatNumberWithCommas(netWeight, 2);

            $('#net_weight_display').val(formattedNetWeight);
            $('#net_weight').val(netWeight);

         
            calculateALW();
        } else {
            $('#net_weight_display').val('');
            $('#net_weight').val('');
            $('#alw_display').val('');
            $('#alw').val('');
        }
    });


    $('#shackle_heads').on('input keyup change', function() {
        calculateALW();
    });

    // Function to calculate ALW (client-side calculation for UI responsiveness)
    function calculateALW() {
        var netWeight = parseFloat($('#net_weight').val()) || 0;
        var shackleCount = parseFloat($('#shackle_heads').val()) || 0;

        if (netWeight > 0 && shackleCount > 0) {
            var alw = netWeight / shackleCount;
            var formattedALW = formatNumberWithCommas(alw, 4);

            $('#alw_display').val(formattedALW);
            $('#alw').val(alw);
        } else {
            $('#alw_display').val('');
            $('#alw').val('');
        }
    }

    // Helper function to format numbers with commas
    function formatNumberWithCommas(x, decimals = 0) {
        if (isNaN(x)) return '';
        return Number(x).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    $('#grower_farm').select2({
        placeholder: 'Choose a grower farm...',
        allowClear: true
    });

});