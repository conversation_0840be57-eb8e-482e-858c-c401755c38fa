+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			language: {
				infoFiltered: ''
			},
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(live_sell_usageID, dataTable) {
		$.when(deletelive_sell_Usage(live_sell_usageID)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deletelive_sell_Usage(live_sell_usageID) {
		return $.ajax({
			url: BASE_URI + 'live_sell_usages/delete/' + live_sell_usageID,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			alertify.confirm(
				'Are you sure you want to delete the selected usage?',
				function () {
					var live_sell_usageID = event.currentTarget.getAttribute('data-id');
					deleteAndReload(live_sell_usageID, dataTable);
				}
			).setHeader('<em>Delete Live Sell Usage</em>');
		});
	});
}(jQuery);
