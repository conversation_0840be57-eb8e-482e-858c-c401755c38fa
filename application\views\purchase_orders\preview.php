<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header crud-box">

			<h1 class="crud-title text-right">
				<?=$title;?>
			</h1>

			<h4><strong><img src="<?=base_url('img/icons/lavc_logo.png')?>" width="20%"></strong></h4>
			<p>Brgy. 69, Anibong, Tacloban City | Cell No. (0939) 917 2550</p>

			<div class="w-100">

			<div class="row sect-jr">
				<div class="col-sm-6">
					<!-- <p class="text-left"><b><large>Customer : </large></b> <?=$purchase_order->customer;?></p> -->
					<!-- <p class="text-left"><b><large>Order Slip No. : </large></b> <?=anchor(site_url("order_slips/preview/" . $purchase_order->order_slip_id), $purchase_order->order_slip_id, 'target="_blank" class="btn btn-link link-unstyled m-0 p-0"');?></p> -->
					<p class="text-left"><b><large>Supplier : </large></b> <?=$purchase_order->supplier;?></p>
					<!-- <p class="text-left"><b><large>Remarks : </large></b> <?=$purchase_order->remarks;?></p> -->
					<!-- <p class="text-left"><b><large>Requisitioner : </large></b> <?=$purchase_order->requisitioner;?></p> -->
					<p class="text-left"><b><large>Usage : </large></b> <?=$purchase_order->usage_name;?></p>
					<p class="text-left"><b><large>Sub-usage : </large></b> <?=$purchase_order->sub_usage_name;?></p>
					<p class="text-left"><b><large>Date Covered : </large></b> <?=date('m/d/Y', strtotime($purchase_order->date_covered));?></p>
				</div>

				<div class="col-sm-2 offset-sm-4">
					<p class="text-left"><b><large>Doc No. : </large></b> <?=$purchase_order->id;?></p>
					<p class="text-left"><b><large>Date : </large></b> <?=date('m/d/Y', strtotime($purchase_order->purchase_order_date));?></p>
					<!-- <p class="text-left"><b><large>Date Covered : </large></b> <?=date('m/d/Y', strtotime($purchase_order->date_covered));?></p> -->
				</div>
			</div>

            	<h5 class="crud-subtitle"><span></span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="receipt-body" width="100%">
							<thead>
								<tr>
									<th class="text-left" style="width: 27%;">Item</th>

									<th class="text-left" style="width: 10%;">Unit</th>

									<th class="text-left" style="width: 10%;">Qty</th>

									<th class="text-left" style="width: 10%;">ALW</th>

									<th class="text-left" style="width: 20%;">Account Name</th>

									<th class="text-left" style="width: 10%;">Unit Price</th>

									<th class="text-right" style="width: 20%;">Price</th>

								</tr>
							</thead>

							<tbody>
							<?php
								$pref_alw_total = 0;
								$est_due_amount_total = 0;
								$total_qty = 0;
								$total_unit_price = 0;
								if (!empty($purchase_order_item_ids)):
                                	$counter = 0;
                                    foreach ($purchase_order_item_ids as $i => $purchase_order_item_id):
										$pref_alw_total += $retain_items[$counter]['pref_alw'];
										$est_due_amount_total += $retain_items[$counter]['amount'];
										$total_qty += $retain_items[$counter]['qty'];
										$total_unit_price += $retain_items[$counter]['unit_price'];
										?>
										<tr class="purchase_order_item">
											<td class="text-left"><?=$retain_items[$counter]['item_name'];?></td>

											<td class="text-left"><?=$purchase_order_item->unit;?></td>

											<td class="text-left"><?=number_format($retain_items[$counter]['qty'], 2, '.', ',');?></td>

											<td class="text-left"><?=number_format($retain_items[$counter]['pref_alw'], 2, '.', ',');?></td>

											<td class="text-left"><?=$purchase_order_item->account_type;?></td>

											<td class="text-left"><?=number_format($retain_items[$counter]['unit_price'], 2, '.', ',');?></td>

											<td class="text-right"><?=number_format($retain_items[$counter]['amount'], 2, '.', ',');?></td>
										</tr>
							<?php
									$counter++;
									endforeach;
								endif;
							?>
							</tbody>
							<tfoot>
								<tr class="item">
									<td colspan="3">
										<table width="100%">
											<tr>
												<td class="text-left"><strong>Remarks : </strong></td>
													<?php if (!empty($purchase_order->remarks)): ?>
														<td><?=$purchase_order->remarks;?></td>
													<?php endif;?>
											</tr>
										</table>
									</td>

									<td colspan="4">
										<table width="100%">
											<tr class="purchase_order_item">
												<td></td>

												<td class="text-right"><strong>Total: </td>

												<td class="text-right"><strong><?=number_format($est_due_amount_total, 2, '.', ',');?></td>
											</tr>
										</table>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

				<?php if(!empty($purchase_order->attachment)):?>
					<div class="col-md-12 sect-jr">
		            	<h5 class="crud-subtitle"><span>Purchase Order Attachments</span></h5>
						<?php $attachments = explode('🔥',$purchase_order->attachment);?>

						<table class="table table-bordered">
							 <tbody>
							 <?php foreach($attachments as $attachment):?>
								 <tr>
									<td>
										<a href="<?=base_url('assets/purchase_orders/'.$purchase_order->id.'/'.$attachment)?>" target="_blank"><?=$attachment?></a>
									</td>
								 </tr>
							 <?php endforeach;?>
							 </tbody>
						</table>
					</div>
				<?php endif;?>

				<?php if(!empty($or_attachments)):?>
					<div class="col-md-12 sect-jr">
						<h5 class="crud-subtitle"><span>OR Attachments</span></h5>
						<!-- <h3 class="text-center">OR Attachments</h3> -->
						<table class="table table-bordered">
						<tbody>

						<?php foreach($or_attachments as $or_attachment):?>
						<tr>
							<td><a href="<?=base_url('assets/purchase_order_or/'.$purchase_order->id.'/'.$or_attachment->file_name)?>" target="_blank"><?=$or_attachment->file_name?></a></td>
						</tr>
						<?php endforeach;?>

						</tbody>
						</table>
					</div>
				<?php endif;?>

				<br>
				<br>

				<div class="row">
					<div class="col-md-12">
						<p class="text-justify"><strong>Dear Supplier:</strong></p>
						<p class="text-justify">Please honor this request slip as PURCHASE ORDER (Proof of Purchase) for the items listed above. Should you have any verifications, don&apos;t hesitate to call me at (053) 321-1235.</p>
						<p class="text-justify">Thank you and God bless!</p>
						<br>
						<p class="text-justify">Please attach this original PO upon billing. No PO, no pay policy.</p>
						<p class="text-danger text-left">This PO is only valid for 3 days upon the date of issuance.</p>

					</div>
				</div>

				<br>
				<br>

				<div class="row">	
					<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
						<?=$purchase_order->requisitioner;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Requisitioned By</p>
					</div>

					<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
						<?=$issuer->full_name;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Prepared By</p>
					</div>

					<div class="col-sm-4 text-center mt-auto" style="margin-top: 30px;">
						<?=$issuer->full_name;?>
						<hr class="signature-line" style="margin: 5px">
						<p>Approved By</p>
					</div>
				</div>

				<br>
				<br>

				<!-- <div class="clearfix text-center d-print-none">

					<button type="button" class="btn btn-primary action-print">
						<i class="fa fa-print"></i> Print
					</button>

					<a class="btn btn-light" href="<?=site_url('purchase_orders/manager?status=approved');?>">Close</a>

				</div> -->
				<div class="clearfix text-center">
					<?php if ($_SESSION['user']->role_id !== '5' || $purchase_order->status !== 'printed'): ?>
						<?php if($type === 'Audit'){
							$type_name = 'Audited';
						}else{
							$type_name = $type;
						}?>

						<button type="button" class="btn btn-primary action-print" data-type="<?=$type?>" data-id="<?=$purchase_order->id;?>"><?=$type_name?></button>
					<?php endif;?>
					<a class="btn btn-light" href="<?=site_url('purchase_orders/manager?status=approved');?>">Close</a>
				</div>
			</div>

			<input type="hidden" name="purchase_order_id" value="<?=$purchase_order->id?>">
		</div>
	</div>
</div>