<?php
class Schedule_hauling extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'schedule_hauling';
        parent::__construct();
    }

    /**
     * Get schedule_hauling By ID
     */
    public function get_by_id($schedule_hauling_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, customer.available_credit, issuer.full_name AS issuer, farm.name AS farm
FROM schedule_hauling
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user AS issuer ON issuer.id = schedule_hauling.added_by
LEFT JOIN farm ON farm.id = schedule_hauling.farm_id
WHERE schedule_hauling.id = ?
    AND schedule_hauling.is_deleted = 0
EOT;
        $binds = [$schedule_hauling_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    public function get_file_by_id($id)
    {
        $sql = <<<EOT
SELECT attachment
FROM schedule_hauling
WHERE is_deleted = 0
        AND id = ?
EOT;
        $binds = [$id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get Partially Paid schedule_haulings
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT schedule_hauling.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM schedule_hauling
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user AS issuer ON issuer.id = schedule_hauling.added_by
WHERE schedule_hauling.paid_amount < schedule_hauling.total
    AND schedule_hauling.is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get schedule_haulings Before Date
     */
    public function get_before_date($date, $customer_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling.id, schedule_hauling.schedule_hauling_date, schedule_hauling.customer_id, schedule_hauling.total,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS paid_amount,
    issuer.full_name AS issuer
FROM schedule_hauling
LEFT JOIN (
    SELECT payment.payment_date, paid_schedule_hauling.schedule_hauling_id, paid_schedule_hauling.amount
    FROM payment
    LEFT JOIN paid_schedule_hauling ON paid_schedule_hauling.payment_id = payment.id
    LEFT JOIN schedule_hauling ON schedule_hauling.id = paid_schedule_hauling.schedule_hauling_id
    WHERE payment.payment_date
        AND payment.payment_date < ?
        AND schedule_hauling.customer_id = ?
) payment ON payment.schedule_hauling_id = schedule_hauling.id
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user AS issuer ON issuer.id = schedule_hauling.added_by
WHERE schedule_hauling.schedule_hauling_date < ?
    AND customer.id = ?
    AND schedule_hauling.is_deleted = 0
GROUP BY schedule_hauling.id
EOT;
        $binds = [$date, $customer_id, $date, $customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get schedule_haulings Between Dates
     */
    public function get_between_dates($from, $to, $customer_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling.*, customer.name AS customer, customer.address, customer.phone_no, customer.email, issuer.full_name AS issuer
FROM schedule_hauling
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user AS issuer ON issuer.id = schedule_hauling.added_by
WHERE schedule_hauling.schedule_hauling_date BETWEEN ? AND ?
    AND schedule_hauling.customer_id = ?
    AND schedule_hauling.is_deleted = 0
EOT;
        $binds = [$from, $to, $customer_id];

        return $this->advanced_query($sql, $binds);
    }


        /**
     * Get schedule_haulings BY Customer
     */
    public function get_by_customer($customer_id)
    {
        $sql = <<<EOT
SELECT schedule_hauling.id, schedule_hauling.schedule_hauling_no, schedule_hauling.terms, schedule_hauling.schedule_hauling_date, schedule_hauling.customer_id, schedule_hauling.total AS charge, schedule_hauling.total, schedule_hauling.paid_amount,
    customer.name AS customer, customer.address, customer.phone_no, customer.email,
    SUM(IFNULL(payment.amount, 0)) AS credit,
    issuer.full_name AS issuer
FROM schedule_hauling
LEFT JOIN (
    SELECT payment.payment_date, paid_schedule_hauling.schedule_hauling_id, paid_schedule_hauling.amount
    FROM payment
    LEFT JOIN paid_schedule_hauling ON paid_schedule_hauling.payment_id = payment.id
    LEFT JOIN schedule_hauling ON schedule_hauling.id = paid_schedule_hauling.schedule_hauling_id
    WHERE payment.payment_date
        AND schedule_hauling.customer_id = ?
) payment ON payment.schedule_hauling_id = schedule_hauling.id
LEFT JOIN live_sell_customer AS customer ON customer.id = schedule_hauling.customer_id
LEFT JOIN user AS issuer ON issuer.id = schedule_hauling.added_by
WHERE customer.id = ?
    AND schedule_hauling.is_deleted = 0
GROUP BY schedule_hauling.id
EOT;
        $binds = [$customer_id, $customer_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */