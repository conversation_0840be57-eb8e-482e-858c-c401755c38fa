+(function ($) {
	"use strict";

	var itemTable = $("#table-item");

	function itemClicked(id, name, price) {
		var rowId = makeRandomId();
		var itemRow = itemTable.find("tbody #order_summary" + id);

		if (itemRow.length > 0) {
			itemRow.find(".btn-plus").click();
		} else {
			var markup =
				"<tr class='order_summary' data-id='" +
				rowId +
				"' id='order_summary" +
				id +
				"'><td><input type='hidden' data-modifiers='" +
				name +
				"' name='order_" +
				rowId +
				"' id='item" +
				rowId +
				"' value='" +
				rowId +
				"' class='form-control-plaintext'><p>" +
				name +
				"</p></td>" +
				"<td><input type='hidden' name='pos_item_id_" +
				rowId +
				"' value='" +
				id +
				"'>" +
				"<div class='input-group'>" +
				"<span class='input-group-btn'> <button data-id='" +
				rowId +
				"' class='btn btn-minus' type='button'>-</button></span>" +
				"<input type='text' id='qty' value='1' name='qty_" +
				rowId +
				"' class='form-control qty text-center'>" +
				"<span class='input-group-btn'><button data-id='" +
				rowId +
				"' class='btn btn-plus' type='button'>+</button></span>" +
				"</div>" +
				"</td>" +
				"<td><input type='hidden' id='price" +
				rowId +
				"' name='price_" +
				rowId +
				"' class='price' value='" +
				$.fn.addNumericCommas(parseFloat(price).toFixed(2)) +
				"'>" +
				"<input type='text' name='amount_" +
				rowId +
				"' id='amount" +
				rowId +
				"' class='form-control-plaintext amount text-right' value='" +
				$.fn.addNumericCommas(parseFloat(price).toFixed(2)) +
				"' readonly></td>" +
				"<td><button class='btn btn-danger action-delete-order'><i class='fa fa-trash'></i></button></td>" +
				"</tr>";

			itemTable.find("tbody").append(markup);
		}
	}

	function makeRandomId() {
		var text = "";
		var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function removeItemRow(event) {
		$(event.currentTarget).closest(".order_summary").remove();

		var $itemTable = $(event.delegateTarget);
		var orderSummary = $itemTable.find(".order_summary");
		if (orderSummary.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find("tbody").html($emptyRow);
		}
	}

	function update_amounts() {
		var grandTotal = 0.0;
		var amount = 0.0;

		$(".order_summary").each(function () {
			var row = $(this).closest(".order_summary");
			var qty = row.find('input[name^="qty"]').val();

			if (isNaN(qty)) {
				qty = 0;
			}

			var price = $.fn.removeNumericCommas(
				row.find('input[name^="price"]').val()
			);
			if (isNaN(price)) {
				price = 0;
			}

			amount += qty * price;
		});

		var discount = parseFloat($("#discount").val());
		if (isNaN(discount)) {
			discount = 0;
		}
		grandTotal = amount - discount;

		$(".subtotal").val($.fn.addNumericCommas(parseFloat(amount).toFixed(2)));
		$("#sub_total").val(amount);

		$(".grandtotal").val(
			$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
		);
		$("#grand_total").val(grandTotal);

		$("#total").val($.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2)));
	}

	function emptyRow() {
		localStorage.clear();
		return "";
	}

	function checkAvailableCreditLimit(customer) {
		return $.ajax({
			type: "GET",
			url: BASE_URI + "customers/check_credit/" + customer,
			dataType: "JSON",
		});
	}

	$(function () {
		$(".pos-item").on("click", function (event) {
			var id = $(this).attr("data-id");
			var name = $(this).attr("data-name");
			var price = $(this).attr("data-price");

			itemClicked(id, name, price);
			update_amounts();
		});

		itemTable.on("click", ".btn-minus", function () {
			var row = $(this).closest(".order_summary");
			var qty = row.find('input[name^="qty"]').val();
			var rowId = row.attr("data-id");

			if (qty > 0) {
				qty--;
				row.find(".qty").val(qty);
				row.find(".qty").attr("value", qty);

				var price = $.fn.removeNumericCommas(row.find(".price").val());
				var totalAmt = price * qty;

				row
					.find(".amount")
					.val($.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2)));
				row
					.find(".amount")
					.attr(
						"value",
						$.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2))
					);
			}

			update_amounts();
		});

		itemTable.on("click", ".btn-plus", function () {
			var row = $(this).closest(".order_summary");
			var qty = row.find('input[name^="qty"]').val();
			var rowId = row.attr("data-id");

			qty++;
			row.find(".qty").val(qty);
			row.find(".qty").attr("value", qty);

			var price = $.fn.removeNumericCommas(
				row.find('input[name^="price"]').val()
			);
			var totalAmt = price * qty;

			row
				.find(".amount")
				.val($.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2)));
			row
				.find(".amount")
				.attr("value", $.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2)));

			update_amounts();
		});

		$(".discount").on("change", function () {
			localStorage.setItem("discount", $(this).val());
			update_amounts();
		});

		itemTable
			.on("click", ".action-delete-order", function (event) {
				removeItemRow(event);
				update_amounts();
			})
			.on("change", 'input[name^="qty"]', function (event) {
				var row = $(this).closest(".order_summary");
				var qty = event.currentTarget.value;
				var price = $.fn.removeNumericCommas(
					row.find('input[name^="price"]').val()
				);
				var totalAmt = price * qty;

				row
					.find(".amount")
					.val($.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2)));
				row
					.find(".amount")
					.attr(
						"value",
						$.fn.addNumericCommas(parseFloat(totalAmt).toFixed(2))
					);

				update_amounts();
			});

		$(".action-pay").click(function () {
			var rowCount = itemTable.find("tbody tr").length;

			if (rowCount == 0) {
				alertify.alert(
					"Item is empty",
					'<em class="text-danger">No Items Added.</em>'
				);
				return false;
			}

			$(".modal").modal("show");

			var total = $("#grandtotal").val();
			if (total.length === 0) {
				total = "0";
			}
			$("#total_payable").val(
				$.fn.addNumericCommas(parseFloat(total.replace(",", "")).toFixed(2))
			);
		});

		$("input[name='payment_type']").on("change", function () {
			var paymentType = $("input[name=payment_type]:checked").val();
			var credit_limit = $('input[name="credit_limit"]');

			if (paymentType == "cash") {
				$(".amount-paid").removeClass("d-none");
				var change = $('input[name="change"]');
				var amountPaid = $('input[name="amount_paid"]');
				change.val($.fn.addNumericCommas(parseFloat(0).toFixed(2)));
				amountPaid.val(0);
				$(".credit-limit").addClass("d-none");
			} else if (paymentType == "credit") {
				$(".credit-limit").removeClass("d-none");
				$(".amount-paid").addClass("d-none");
			}
		});

		$("input[name='amount_paid']").on("input", function (event) {
			var amount_paid = event.currentTarget.value;
			var totalPayable = $.fn.removeNumericCommas(
				$('input[name="total_payable"]').val()
			);
			if (isNaN(totalPayable)) {
				totalPayable = 0;
			}

			var change = $('input[name="change"]');
			var currentChange = amount_paid - totalPayable;

			change.val($.fn.addNumericCommas(parseFloat(currentChange<0?0:currentChange).toFixed(2)));
		});

		$("select[name^='customer']").on("change", function (event) {
				var customer = $('select[name="customer"]').val();
				$.when(checkAvailableCreditLimit(customer)).then(
				function (response) {
					$("input[name='limit']").val(parseFloat(response.credit_limit).toFixed(2));
				},
				function () {
					$("input[name='limit']").val("0.00");
				}
				);
		});

		$("input[name='search']").on("keyup", function() {
			let pos_items = $(".pos-item");
			let search_term = $(this).val().toLowerCase();
			pos_items.each(function(i, obj) {
				let item_name = $(obj).attr("data-name").toLowerCase() + " " + $(obj).attr("data-unit").toLowerCase();
				if(item_name.includes(search_term)) {
					$(obj).show();
				} else {
					$(obj).hide();
				}
			})
		})

		$("#orderForm").on("click", ".action-save", function (event) {
			event.preventDefault();

			var tbody = $("#table-item tbody");
			var paymentType = $("input[name=payment_type]:checked").val();
			var customer = $('select[name="customer"]').val();
			var amountPaid = parseFloat(
				$.fn.removeNumericCommas($('input[name="amount_paid"]').val())
			);
			var totalPayable = $.fn.removeNumericCommas(
				$('input[name="total_payable"]').val()
			);
			if (isNaN(totalPayable)) {
				totalPayable = 0;
			}

			if (tbody.children().length == 0) {
				alertify
					.alert("Order is Empty!")
					.setHeader('<em class="text-danger">Required*</em>');
			} else if (paymentType === "cash" && amountPaid < 0) {
				alertify
					.alert("Invalid Amount!")
					.setHeader('<em class="text-danger">Required*</em>');
			} else if (paymentType === "credit" && customer === "") {
				alertify
					.alert("Customer is Missing!")
					.setHeader('<em class="text-danger">Required*</em>');
			} else {
				if (customer !== "" && paymentType === "credit") {
					event.preventDefault();

					var creditLimit = $('input[name="limit"]').val();
					var creditLeft = creditLimit - totalPayable;

					if (creditLimit > 0) {
						if (creditLimit < totalPayable) {
							alertify
							.alert("Not Enough Credit.")
							.setHeader('<em class="text-danger">Notification</em>');
							return false;
						} else if (creditLeft < 0) {
							alertify
							.alert("Insufficient Credit.")
							.setHeader('<em class="text-danger">Notification</em>');
							return false;
						}
					}
				}


          alertify
          .confirm(
            "Are you sure you want to continue with this transaction?",
            function () {
              $("form").submit();
            }
          )
          .setHeader("<em>Save Transaction</em>");


			}
		});
	});
})(jQuery);
