<?php
class Bird_count extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'bird_count';
        parent::__construct();
    }


    /**
     * Get bird count by ID (only non-deleted)
     */
    public function get_by_id($bird_count_id)
    {
        $sql = <<<EOT
SELECT *
FROM bird_count
WHERE is_deleted = 0
    AND id = ?
EOT;
        $binds = [$bird_count_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get grower farm names from hauling logs for bird count dropdown
     */
    public function get_grower_farms_from_hauling_logs()
    {
        $sql = <<<EOT
SELECT DISTINCT
    customer.id,
    customer.id as customer_id,
    customer.name as grower_farm_name
FROM hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
LEFT JOIN bird_count ON bird_count.hauling_log_id = hauling_log.id
    AND bird_count.is_deleted = 0
WHERE hauling_log.is_deleted = 0
AND bird_count.id IS NULL
GROUP BY customer.id, customer.name
ORDER BY customer.name
EOT;

        return $this->advanced_query($sql);
    }



    /**
     * Get bird count data with hauling log information for DataTable
     */
    public function get_bird_counts_with_hauling_info()
    {
        $sql = <<<EOT
SELECT
    bird_count.id,
    schedule.eta_date,
    customer.name as grower_farm,
    schedule_list.trip_no,
    bird_count.live_heads,
    bird_count.doa,
    bird_count.daa,
    bird_count.runts,
    bird_count.rejected,
    bird_count.shackle_count,
    hauling_log.net_weight as kilos,
    bird_count.alw
FROM bird_count
LEFT JOIN hauling_log ON hauling_log.id = bird_count.hauling_log_id
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
WHERE bird_count.is_deleted = 0
ORDER BY bird_count.id DESC
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get bird count with detailed information for preview
     */
    public function get_bird_count_with_details($bird_count_id)
    {
        $sql = <<<EOT
SELECT
    bird_count.id,
    bird_count.live_heads,
    bird_count.doa,
    bird_count.daa,
    bird_count.runts,
    bird_count.rejected,
    bird_count.shackle_count,
    bird_count.alw,
    customer.name as grower_farm,
    customer.address as customer_address,
    customer.phone_no as customer_phone,
    customer.email as customer_email,
    schedule_list.trip_no,
    schedule.waf_rs_no,
    CONCAT(schedule.eta_date, ' ', schedule_list.eta_time) as eta,
    hauling_log.truck_name,
    hauling_log.truck_plate,
    hauling_log.ata_time,
    hauling_log.weighed_time,
    hauling_log.remarks,
    hauling_log.gross_weight,
    hauling_log.tare_weight,
    hauling_log.net_weight,
    bird_count.added_on
FROM bird_count
LEFT JOIN hauling_log ON hauling_log.id = bird_count.hauling_log_id
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
WHERE bird_count.id = ? AND bird_count.is_deleted = 0
EOT;

        return $this->advanced_query($sql, [$bird_count_id])[0] ?? null;
    }

    /**
     * Get summary data for bird counts
     */
    public function get_bird_count_summary()
    {
        $sql = <<<EOT
SELECT
    COUNT(*) as total_records,
    SUM(bird_count.live_heads) as total_live_heads,
    SUM(bird_count.doa) as total_doa,
    SUM(bird_count.daa) as total_daa,
    SUM(bird_count.runts) as total_runts,
    SUM(bird_count.rejected) as total_rejected,
    SUM(bird_count.shackle_count) as total_shackle_count,
    SUM(hauling_log.net_weight) as total_kilos,
    AVG(bird_count.alw) as average_alw
FROM bird_count
LEFT JOIN hauling_log ON hauling_log.id = bird_count.hauling_log_id
WHERE bird_count.is_deleted = 0
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res[0];
        } else {
            return null;
        }
    }
}
