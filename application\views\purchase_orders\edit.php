<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" id="formpurchase_order" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>

			<fieldset>
            	<h5 class="crud-subtitle"><span>Purchase Order Info</span></h5>

				<div class="row">
					<!-- <div class="col-md-4 form-group">
						<label for="customer" class="control-label">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="" readonly>
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?=$customer->id;?>" <?=set_select('customer', $customer->id, $purchase_order->customer_id === $customer->id);?>><?=$customer->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div> -->

					<div class="col-md-4 form-group">
						<label for="purchase_order_date" class="control-label">Purchase Date</label>

						<input type="text" name="purchase_order_date" class="form-control datepicker" value="<?= date('m/d/Y', strtotime(set_value('purchase_order_date', $purchase_order->purchase_order_date))); ?>">

						<div class="red-text flash-message">
							<?=form_error('purchase_order_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="date_covered" class="control-label">Date Covered</label>

						<input type="text" name="date_covered" class="form-control datepicker" value="<?= date('m/d/Y', strtotime(set_value('date_covered', $purchase_order->date_covered))); ?>">

						<div class="red-text flash-message">
							<?=form_error('date_covered');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="supplier" class="control-label">Supplier</label>

						<select name="supplier" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($suppliers as $supplier): ?>
								<option value="<?=$supplier->id;?>" <?=set_select('supplier', $supplier->id, $supplier->id === $purchase_order->supplier_id);?>><?=$supplier->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('supplier');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="usage" class="control-label">Usage</label>

						<select name="usage" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($usages as $usage): ?>
								<option value="<?=$usage->id;?>" <?=set_select('usage', $usage->id, $usage->id === $purchase_order->usage_id);?>><?=$usage->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('usage');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="sub_usage" class="control-label">Sub-Usage</label>

						<select name="sub_usage" class="form-control dropsearch" data-placeholder="">
							<option></option>
							<?php foreach ($sub_usages as $sub_usage): ?>
								<option value="<?=$sub_usage->id;?>" <?=set_select('sub_usage', $sub_usage->id, $sub_usage->id === $purchase_order->sub_usage_id);?>><?=$sub_usage->name?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('sub_usage');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="remarks" class="control-label">Remarks <small class="font-italic">(Optional)</small></label>

						<input type="text" name="remarks" class="form-control" value="<?=set_value('remarks', $purchase_order->remarks);?>">

						<div class="red-text flash-message">
							<?=form_error('remarks');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="requisitioner" class="control-label">Requisitioner <small class="font-italic"></small></label>

						<input type="text" name="requisitioner" class="form-control" value="<?=set_value('requisitioner', $purchase_order->requisitioner);?>">

						<div class="red-text flash-message">
							<?=form_error('requisitioner');?>
						</div>
					</div>
				</div>

				<!-- <div class="col-md-12 form-group">
					<label for="attachment" class="control-label">Attachment </label>

					<input type="file" name="attachment[]" class="form-control" accept=".pdf" multiple>

					<div class="red-text flash-message">
						<?=form_error('attachment');?>
					</div>
				</div> -->

            	<h5 class="crud-subtitle"><span>List of Purchase Order Items</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 20%;">Item</th>

									<th class="text-center" style="width: 12%;">Qty</th>

									<th class="text-center" style="width: 10%;">Unit</th>

									<th class="text-center" style="width: 12%;">ALW</th>

									<th class="text-center" style="width: 12%;">Unit Price</th>

									<th class="text-center" style="width: 12%;">Amount</th>

									<th class="text-center" style="width: 20%;">Account Name</th>
								</tr>
							</thead>

							<tbody>
							<?php
                                if (!empty($purchase_order_item_ids)):
                                	$counter = 0;
                                    foreach ($purchase_order_item_ids as $i => $purchase_order_item_id):
                                    $purchase_order_item = $purchase_order_items[$counter];
                            ?>
										<tr class="purchase_order_item">
											<td>
												<?php $fn_purchase_order_item = 'purchase_order_item_' . $purchase_order_item_id;?>
												<input type="hidden" name=<?=$fn_purchase_order_item?> value="<?=set_value($fn_purchase_order_item, $purchase_order_item_id);?>">

												<?php $fdn_item = 'item_' . $purchase_order_item_id;?>

												<select name="<?=$fdn_item;?>" class="form-control dropsearch" data-placeholder="" readonly>
													<!-- <option value=<?=$purchase_order_item->item_id?>><?=$purchase_order_item->item_name?></option> -->
													<option value=<?=$retain_items[$counter]['item_id']?>><?=$retain_items[$counter]['item_name']?></option>
												</select>
											</td>

											<td>
												<?php $fn_qty = 'qty_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_qty;?>" class="form-control" value="<?=set_value($fn_qty, sprintf("%.2f", $purchase_order_item->qty));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_qty);?>
												</div>
											</td>

											<td>
												<?php $fn_unit = 'unit_' . $purchase_order_item_id;?>
												<input type="text" name="<?=$fn_unit;?>" class="form-control go-suggest" value="<?=set_value($fn_unit, $purchase_order_item->unit);?>" data-suggest="units" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_unit);?>
												</div>
											</td>

											<td>
												<?php $fn_pref_alw = 'pref_alw_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_pref_alw;?>" class="form-control" value="<?=set_value($fn_pref_alw, sprintf("%.2f", $purchase_order_item->pref_alw));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_pref_alw);?>
												</div>
											</td>

											<td>
												<?php $fn_unit_price = 'unit_price_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_unit_price;?>" class="form-control" value="<?=set_value($fn_unit_price, sprintf("%.2f", $purchase_order_item->unit_price));?>" step="any">

												<div class="red-text flash-message">
													<?=form_error($fn_unit_price);?>
												</div>
											</td>

											<td>
												<?php $fn_amount = 'amount_' . $purchase_order_item_id;?>
												<input type="number" name="<?=$fn_amount;?>" class="form-control" value="<?=set_value($fn_amount, sprintf("%.2f", $purchase_order_item->amount));?>" step="any" readonly>

												<div class="red-text flash-message">
													<?=form_error($fn_amount);?>
												</div>
											</td>

											<td>
												<?php $fn_account_type = 'account_type_' . $purchase_order_item_id;?>
						
												<select name="<?=$fn_account_type;?>" class="form-control dropsearch" data-placeholder="">
													<option></option>
													<?php foreach ($account_types as $account_type): ?>
														<option value="<?=$account_type->account_name;?>" <?=set_select($fn_account_type, $account_type->account_name, $account_type->account_name == $purchase_order_item->account_type);?>><?=$account_type->account_name?></option>
													<?php endforeach; ?>
												</select>

												<div class="red-text flash-message">
													<?=form_error($fn_account_type);?>
												</div>
											</td>
										</tr>
							<?php
									$counter++;
                                    endforeach;
                                else:
                            ?>
									<tr class="no-item">
										<td class="text-center" colspan="7">No items added yet.</td>
									</tr>
							<?php
                                endif;
                            ?>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12 form-group">
                		<!-- <button type="button" class="btn btn-sm btn-dark action-add-item">Add Item</button> -->
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Save and Proceed</button>
				<a href="<?=site_url('purchase_orders')?>" class="btn btn-light">Close</a>
			</div>
		</form>
	</div>
</div>
