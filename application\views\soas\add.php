<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer">Customer</label>

						<select name="customer" class="form-control dropsearch" data-placeholder="">
							<option value=""></option>
							<?php foreach($customers as $customer): ?>
								<option value="<?=$customer->id?>" <?=set_select('customer', $customer->id);?>><?=$customer->name;?></option>
							<?php endforeach; ?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="from_date">From</label>

						<input type="text" name="from_date" class="form-control datepicker" value="<?=set_value('from_date', date('m/01/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('from_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="to_date">To</label>

						<input type="text" name="to_date" class="form-control datepicker" value="<?=set_value('to_date', date('m/t/Y'));?>">

						<div class="red-text flash-message">
							<?=form_error('to_date');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('customers/soa')?>" class="btn btn-dark">View All Customer</a>
				<!-- <a href="<?=site_url('soas')?>" class="btn btn-light">Close</a> -->
			</div>
		</form>
	</div>
</div>
