!function(e){"use strict";function t(t){var a=e(t),n=a.find("thead th"),r=a.DataTable({lengthChange:!0,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"dt-action",targets:n.length-1},{className:"text-right",targets:[2,3,4]}],autoWidth:!1,processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(t,a,r){console.log(r),e.when(n(t,r)).then(function(e){console.log(e),a.ajax.reload(null,!1)},function(e){console.log(e),a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t,a){return e.ajax({url:BASE_URI+"pos_invoices/delete",type:"POST",dataType:"json",data:{pos_invoice_id:t,reason:a}})}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.customer").find("td"),o=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to void "+o+'? <br /> Reason: <input class="reason" name="reason" type=text />',function(){var r=t.currentTarget.getAttribute("data-id"),o=e(".reason").val().replace(" ","%20");a(r,n,o)}).setHeader("<em>Void Invoice</em>")})})}(jQuery);