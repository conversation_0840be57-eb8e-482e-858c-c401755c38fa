<div class="col-md-12 sect-jr">
	<div class="receipt">
		<div class="receipt-header">

			<h1 class="crud-title text-center" style="margin-bottom: 20px;">
				<?=$title;?>
			</h1>

			<div class="text-center mb-4">
				<img src="<?=base_url('img/icons/lavc_logo.png')?>" width="80" height="80">
				<h4><strong>LEYTE AGRI-VENTURES CORPORATION</strong></h4>
				<p style="margin-bottom: 30px;">69 Anibong, Tacloban City<br>
				Leyte 6500</p>
			</div>

			<div class="row sect-jr">
				<div class="col-4">
					<p class="text-left"><strong>Customer:</strong> <?= strtoupper($bird_count_data->grower_farm ?: 'RITE MEAT MARKETING'); ?></p>
					<p class="text-left"><strong>Address:</strong> <?= $bird_count_data->customer_address ?: 'Palo, Leyte'; ?></p>
				</div>
				<div class="col-4 text-center">
					<p><strong>TTN:</strong> <span style="border-bottom: 1px solid #000; display: inline-block; width: 120px; margin-left: 10px;">&nbsp;</span></p>
				</div>
				<div class="col-4 text-right">
					<p class="text-left"><strong>Prod date:</strong> <?= date('l, F j, Y', strtotime($bird_count_data->added_on)); ?></p>
					<p class="text-left"><strong>Contact:</strong> <?= $bird_count_data->customer_phone ?: '09177049277 Juhann Analyn'; ?></p>
				</div>
			</div>

		</div>

		<div class="receipt-body">
			<!-- Live Broiler Hauled Section -->
			<div class="row mb-3">
				<div class="col-md-12">
					<h5><strong>LIVE BROILER HAULED</strong></h5>
					<table class="table table-bordered" style="font-size: 12px;">
						<thead>
							<tr>
								<th rowspan="2" class="text-center align-middle" style="vertical-align: middle;">TRIP NO.</th>
								<th rowspan="2" class="text-center align-middle" style="vertical-align: middle;">HEADS</th>
								<th rowspan="2" class="text-center align-middle" style="vertical-align: middle;">KILOS</th>
								<th colspan="2" class="text-center">DOA</th>
								<th colspan="2" class="text-center">DAA</th>
								<th colspan="2" class="text-center">RUNTS</th>
								<th colspan="2" class="text-center">REJECTED</th>
								<th colspan="3" class="text-center">SHACKLE COUNT</th>
							</tr>
							<tr>
								<th class="text-center">HEADS</th>
								<th class="text-center">KILOS</th>
								<th class="text-center">HEADS</th>
								<th class="text-center">KILOS</th>
								<th class="text-center">HEADS</th>
								<th class="text-center">KILOS</th>
								<th class="text-center">HEADS</th>
								<th class="text-center">KILOS</th>
								<th class="text-center">HEADS</th>
								<th class="text-center">KILOS</th>
								<th class="text-center">ALW</th>
							</tr>
						</thead>
						<tbody>
							<?php
							// Calculate kilos for each category using heads × ALW
							$doa_kilos = $bird_count_data->doa * $bird_count_data->alw;
							$daa_kilos = $bird_count_data->daa * $bird_count_data->alw;
							$runts_kilos = $bird_count_data->runts * $bird_count_data->alw;
							$rejected_kilos = $bird_count_data->rejected * $bird_count_data->alw;
							?>
							<tr>
								<td class="text-center"><?= $bird_count_data->trip_no ?: '1'; ?></td>
								<td class="text-center"><?= $bird_count_data->live_heads; ?></td>
								<td class="text-center"><?= number_format($bird_count_data->net_weight, 2); ?></td>
								<td class="text-center"><?= $bird_count_data->doa; ?></td>
								<td class="text-center"><?= number_format($doa_kilos, 2); ?></td>
								<td class="text-center"><?= $bird_count_data->daa; ?></td>
								<td class="text-center"><?= number_format($daa_kilos, 2); ?></td>
								<td class="text-center"><?= $bird_count_data->runts; ?></td>
								<td class="text-center"><?= number_format($runts_kilos, 2); ?></td>
								<td class="text-center"><?= $bird_count_data->rejected; ?></td>
								<td class="text-center"><?= number_format($rejected_kilos, 2); ?></td>
								<td class="text-center"><?= $bird_count_data->shackle_count; ?></td>
								<td class="text-center"><?= number_format($bird_count_data->net_weight, 2); ?></td>
								<td class="text-center"><?= number_format($bird_count_data->alw, 3); ?></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Summary Section -->
			<div class="row mt-4">
				<div class="col-md-12">
					<h5><strong>Summary</strong></h5>
					<table class="table table-bordered" style="font-size: 12px;">
						<thead>
							<tr>
								<th class="text-center">Range</th>
								<th class="text-center">Received</th>
								<th class="text-center">DOA</th>
								<th class="text-center">DAA</th>
								<th class="text-center">Runts</th>
								<th class="text-center">Reject</th>
								<th class="text-center">Heads</th>
								<th class="text-center">Kilos</th>
								<th class="text-center">ALW</th>
								<th class="text-center">Rate/KG</th>
								<th class="text-center">Total Live Sales</th>
							</tr>
						</thead>
						<tbody>
							<?php
							// Calculate values based on ALW ranges
							$alw = floatval($bird_count_data->alw);
							$live_heads = intval($bird_count_data->live_heads);
							$doa = intval($bird_count_data->doa);
							$daa = intval($bird_count_data->daa);
							$runts = intval($bird_count_data->runts);
							$rejected = intval($bird_count_data->rejected);
							$net_weight = floatval($bird_count_data->net_weight);

							// Determine which range the ALW falls into
							$range_1_heads = 0; // <1.809
							$range_2_heads = 0; // >1.809
							$range_1_kilos = 0;
							$range_2_kilos = 0;

							if ($alw < 1.809) {
								$range_1_heads = $live_heads;
								$range_1_kilos = $net_weight;
							} else {
								$range_2_heads = $live_heads;
								$range_2_kilos = $net_weight;
							}

							// Sample rates (you may want to make these configurable)
							$rate_1 = 129.00; // Rate for <1.809
							$rate_2 = 127.00; // Rate for >1.809

							$total_sales_1 = $range_1_kilos * $rate_1;
							$total_sales_2 = $range_2_kilos * $rate_2;
							?>
							<tr>
								<td class="text-center">&lt;1.809</td>
								<td class="text-center"><?= $range_1_heads; ?></td>
								<td class="text-center"><?= $range_1_heads > 0 ? $doa : 0; ?></td>
								<td class="text-center"><?= $range_1_heads > 0 ? $daa : 0; ?></td>
								<td class="text-center"><?= $range_1_heads > 0 ? $runts : 0; ?></td>
								<td class="text-center"><?= $range_1_heads > 0 ? $rejected : 0; ?></td>
								<td class="text-center"><?= $range_1_heads; ?></td>
								<td class="text-center"><?= number_format($range_1_kilos, 2); ?></td>
								<td class="text-center"><?= $range_1_heads > 0 ? number_format($alw, 4) : '0.0000'; ?></td>
								<td class="text-center"><?= number_format($rate_1, 2); ?></td>
								<td class="text-center"><?= number_format($total_sales_1, 2); ?></td>
							</tr>
							<tr>
								<td class="text-center">&gt;1.809</td>
								<td class="text-center"><?= $range_2_heads; ?></td>
								<td class="text-center"><?= $range_2_heads > 0 ? $doa : 0; ?></td>
								<td class="text-center"><?= $range_2_heads > 0 ? $daa : 0; ?></td>
								<td class="text-center"><?= $range_2_heads > 0 ? $runts : 0; ?></td>
								<td class="text-center"><?= $range_2_heads > 0 ? $rejected : 0; ?></td>
								<td class="text-center"><?= $range_2_heads; ?></td>
								<td class="text-center"><?= number_format($range_2_kilos, 2); ?></td>
								<td class="text-center"><?= $range_2_heads > 0 ? number_format($alw, 4) : '0.0000'; ?></td>
								<td class="text-center"><?= number_format($rate_2, 2); ?></td>
								<td class="text-center"><?= number_format($total_sales_2, 2); ?></td>
							</tr>
						</tbody>
						<tfoot>
							<tr style="background-color: #f8f9fa;">
								<td class="text-center"><strong>Total &gt;&gt;</strong></td>
								<td class="text-center"><strong><?= $live_heads; ?></strong></td>
								<td class="text-center"><strong><?= $doa; ?></strong></td>
								<td class="text-center"><strong><?= $daa; ?></strong></td>
								<td class="text-center"><strong><?= $runts; ?></strong></td>
								<td class="text-center"><strong><?= $rejected; ?></strong></td>
								<td class="text-center"><strong><?= $live_heads; ?></strong></td>
								<td class="text-center"><strong><?= number_format($net_weight, 2); ?></strong></td>
								<td class="text-center"><strong><?= number_format($alw, 4); ?></strong></td>
								<td class="text-center"><strong></strong></td>
								<td class="text-center"><strong><?= number_format($total_sales_1 + $total_sales_2, 2); ?></strong></td>
							</tr>
						</tfoot>
					</table>

					<!-- Remarks Section -->
					<div class="row mt-4">
						<div class="col-md-8 offset-md-2 text-center">
							<p><strong>Remarks:</strong></p>
							<hr class="signature-line" style="margin: 20px auto 15px auto; width: 80%;">
							<hr class="signature-line" style="margin: 15px auto; width: 80%;">
							<hr class="signature-line" style="margin: 15px auto; width: 80%;">
						</div>
					</div>

					
					<div class="row mt-3">
						<div class="col-md-12 text-center">
							<p style="font-size: 11px;"><strong>Note:</strong> Check payment must be to the order of <strong>LEYTE AGRI-VENTURES CORPORATION</strong></p>
							<p style="font-size: 11px;"><strong>Account No.:</strong> Union Bank (UB): 00-622-001127-8</p>
						</div>
					</div>

					<!-- Signature Section -->
					<div class="row mt-5">
						
						<div class="col-6 text-center" style="margin-top: 30px;">
							<p><strong>Prepared by:</strong></p>
							<br><br>
							<hr class="signature-line" style="margin: 5px auto; width: 70%;">
							<p style="font-size: 11px;">LAVC Encoder</p>
						</div>
						
						<div class="col-6 text-center" style="margin-top: 30px;">
							<p><strong>Received by:</strong></p>
							<br><br>
							<hr class="signature-line" style="margin: 5px auto; width: 70%;">
							<p style="font-size: 11px;">Client Representative</p>
						</div>
					</div>

					<div class="row mt-4">
						
						<div class="col-6 text-center" style="margin-top: 30px;">
							<br><br>
							<hr class="signature-line" style="margin: 5px auto; width: 70%;">
							<p style="font-size: 11px;">LAVC Operations</p>
						</div>
						
						<div class="col-6 text-center" style="margin-top: 30px;">
							<br><br>
							<hr class="signature-line" style="margin: 5px auto; width: 70%;">
							<p style="font-size: 11px;">LAVC Cashier</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="text-center mt-3 no-print">
			<button type="button" class="btn btn-primary action-print"><i class="fa fa-print"></i> Print</button>
			<a class="btn btn-light" href="<?=site_url('bird_counts');?>">Close</a>
		</div>
	</div>
</div>