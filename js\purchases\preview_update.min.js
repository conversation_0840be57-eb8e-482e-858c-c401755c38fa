!function(t){"use strict";function e(e){t.when(r(e)).then(function(){window.print()},function(t){var e=JSON.parse(t.responseText);alertify.error(e.message)})}function a(e,a,r,n){var s=new FormData;s.append("purchase_id",e),s.append("type",a),s.append("remarks",r),s.append("audit_status",n);for(var i=document.getElementById("attachments").files,u=0;u<i.length;u++)s.append("attachments[]",i[u]);t.ajax({url:BASE_URI+"purchases/mark_audit_update/",type:"POST",data:s,processData:!1,contentType:!1,dataType:"json",success:function(t){t.is_success?window.location.href=BASE_URI+"purchases/manager/":alertify.error(t.message)},error:function(t){var e=JSON.parse(t.responseText);alertify.error(e.message)}})}function r(e){return t.ajax({url:BASE_URI+"purchases/mark_printed/"+e,type:"POST",dataType:"json"})}function n(e,a){t.ajax({url:BASE_URI+"purchases/fetch_audit_details/"+e,type:"GET",dataType:"json",success:function(t){a(t)},error:function(t){var e=JSON.parse(t.responseText);alertify.error(e.message)}})}t(function(){t("button.action-print").click(function(t){var r=t.currentTarget.getAttribute("data-type");if("Audit"===r){var s=t.currentTarget.getAttribute("data-id");n(s,function(e){alertify.confirm("Audit Purchase Order",`\n\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<input type="radio" id="complete" name="audit_status" value="complete" ${"complete"===e.audit_status?"checked":""}> Complete &nbsp; &nbsp;\n\t\t\t\t\t\t\t<input type="radio" id="incomplete" name="audit_status" value="incomplete" ${"incomplete"===e.audit_status?"checked":""}> Incomplete<br>\n\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t</div>\n                        <div>\n\t\t\t\t\t\t\t<textarea id="remarks" name="remarks" placeholder="Enter remarks..." class="w-100">${e.remarks}</textarea>\n                        </div>\n                        <div>\n                            <input type="file" id="attachments" name="attachments[]" accept=".pdf, .jpg, .jpeg, .png" multiple>\n                        </div>\n                        `,function(){var e=t.currentTarget.getAttribute("data-id"),n=document.getElementById("remarks").value,s=document.querySelector('input[name="audit_status"]:checked').value;""!==n&&s?a(e,r,n,s):alertify.alert('<span class="red-text">Missing Remarks or Audit Status!</span>').setHeader("<em>Required</em>")},function(){alertify.error("Cancel")}).setHeader("<em>Audit Purchase Order</em>")})}else alertify.confirm("Are you sure you want to "+r+" this Purchase Order?",function(){var n=t.currentTarget.getAttribute("data-id");"Print"==r?e(n):a(n,r,null)}).setHeader("<em>"+r+" Purchase Order</em>")})})}(jQuery);