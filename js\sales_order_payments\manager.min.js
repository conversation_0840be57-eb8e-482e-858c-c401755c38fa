!function(e){"use strict";function t(t){var a=e(t),r=(a.find("thead th"),e(".tab-link.active").attr("data-status")),n=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")+"?status="+r});return n}function a(t,a){e.when(r(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function r(t){return e.ajax({url:BASE_URI+"sales_order_payments/receive_sales_order_payment/"+t,type:"POST",dataType:"json"})}function n(t,a){e.when(s(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function s(t){return e.ajax({url:BASE_URI+"sales_order_payments/delete/"+t,type:"POST",dataType:"json"})}e(function(){var r=t(".tableA");e(".tableA").on("click",".action-receive",function(t){var n=e(t.currentTarget).closest("tr.sales_order_payment").find("td"),s=void 0!==n[0]?n[0].innerHTML:"";alertify.confirm("Are you sure you want to receive the sales_order_payment "+s+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,r)}).setHeader("<em>receive sales_order_payment</em>")}).on("click",".action-delete",function(t){var a=e(t.currentTarget).closest("tr.sales_order_payment").find("td"),s=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to delete Sales Order Payment "+s+"?",function(){var e=t.currentTarget.getAttribute("data-id");n(e,r)}).setHeader("<em>Delete Sales Order Payment</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var a=BASE_URI+"fetch/sales_order_payments?status="+t.currentTarget.getAttribute("data-status"),n=BASE_URI+"sales_order_payments/manager?status="+t.currentTarget.getAttribute("data-status");console.log(a),window.history.pushState(null,null,n),r.ajax.url(a).load()})})}(jQuery);