<div class="sect">
	<div class="col-md-8 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<h1 class="crud-title">
				<?=$title;?>
			</h1>
			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-4 form-group">
						<label for="date" class="control-label">Date</label>

						<input type="text" name="date" class="form-control datepicker" value="<?=set_value('date', date('m/d/Y', strtotime($job_schedule->date)));?>">

						<div class="red-text flash-message">
							<?=form_error('schedule_date');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="time_start" class="control-label">Time Start</label>

						<input type="time" name="time_start" class="form-control" value="<?=set_value('time_start', $job_schedule->time_start);?>">

						<div class="red-text flash-message">
							<?=form_error('time_start');?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="time_end" class="control-label">Time End</label>

						<input type="time" name="time_end" class="form-control" value="<?=set_value('time_end', $job_schedule->time_end);?>">

						<div class="red-text flash-message">
							<?=form_error('time_end');?>
						</div>
					</div>

					<div class="col-md-5 form-group">
						<label for="name">Customer Name</label>

						<select class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach($customers as $customer):?>
							<option value="<?=$customer->id?>" <?=set_select('customer', $customer->id, $customer->id === $job_schedule->customer_id);?>><?=ucwords($customer->name)?></option>
							<?php endforeach;?>
						</select>
						<div class="red-text flash-message">
							<?=form_error('customer');?>
						</div>
					</div>

					<div class="col-md-3 form-group">
						<label for="qty_birds">Quantity of Birds</label>

						<input type="number" name="qty_birds" class="form-control" value="<?=set_value('qty_birds', $job_schedule->qty_birds);?>" step="any">

						<div class="red-text flash-message">
							<?=form_error('qty_birds');?>
						</div>
					</div>
					
					<div class="col-md-4 form-group">
						<label for="area">Area</label>

						<input type="text" name="area" class="form-control" value="<?=set_value('area', $job_schedule->area);?>">

						<div class="red-text flash-message">
							<?=form_error('area');?>
						</div>
					</div>

					<div class="col-xl-12 form-group">
						<label for="details">Details</label>
						
						<textarea name="details" class="form-control" rows="3" ><?=set_value('details');?><?=set_value('details', $job_schedule->details);?></textarea>
						
						<div class="red-text flash-message">
							<?=form_error('details');?>
						</div>
					</div>
				</div>
			</fieldset>
			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
			</div>
		</form>
	</div>
</div>