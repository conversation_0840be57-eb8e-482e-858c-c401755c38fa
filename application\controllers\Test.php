<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Test extends MYT_Controller
{
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
    }

    public function insert_pages()
    {
        $this->load->model('page');
        $this->load->library('controller_list');

        $pages = $this->controller_list->getControllers();
        $public_pages = [
            'Fetch',
            'Autocomplete',
            'Login',
            'Logout',
            'Forgot_password',
            'Api',
            'Welcome',
            'Test'
        ];
        $useless_methods = [
            'render_page',
            'build_content',
            'get_page_id',
            'get_view',
            'get_template'
        ];

        $data = [];
        foreach ($pages as $controller => $methods) {
            if (in_array($controller, $public_pages)) {
                continue;
            }

            foreach ($methods as $i => $method) {
                if (in_array($method, $useless_methods)) {
                    continue;
                }

                $data[] = [
                    'controller' => $controller,
                    'method' => $method
                ];

                echo $controller . '/' . $method . '<br>';
            }
        }

        $this->db->trans_begin();
        if ($data && $this->page->insert_batch($data)) {
            $this->db->trans_commit();
            echo 'You good, man.';
        } else {
            $this->db->trans_rollback();
            echo 'Something went wrong.';
        }
    }

    public function insert_permissions()
    {
        // Test
        $this->load->model('page');
        $this->load->model('permission');

        $pages = $this->page->select();

        $allowed_controllers = [
            'Items',
			'Sku',
        ];

        $allowed_methods = [
            'add',
            'index',
            'edit',
            'manager'
        ];

        $data = [];
        foreach ($pages as $page) {
            if (!in_array($page->controller, $allowed_controllers) || !in_array($page->method, $allowed_methods)) {
                continue;
            }

            $data[] = [
                'role_id' => 4,
                'page_id' => $page->id,
                'is_allowed' => 1
            ];
        }

        $this->db->trans_begin();
        if ($data && $this->permission->insert_batch($data)) {
            $this->db->trans_commit();
            echo 'You good, man.';
        } else {
            $this->db->trans_rollback();
            echo 'Something went wrong.';
        }
    }
	
    /**
     * Add SKU permissions for all roles
     */
    public function add_sku_permissions()
    {
        $this->load->model('page');
        $this->load->model('permission');

        // First, add SKU pages if they don't exist
        $sku_pages = [
            ['controller' => 'Sku', 'method' => 'index'],
            ['controller' => 'Sku', 'method' => 'add'],
            ['controller' => 'Sku', 'method' => 'edit'],
            ['controller' => 'Sku', 'method' => 'delete'],
        ];

        $page_ids = [];
        foreach ($sku_pages as $page_data) {
            // Check if page exists
            $existing = $this->page->select('', $page_data, 1);
            if (!$existing) {
                // Insert new page
                $page_id = $this->page->insert($page_data);
                $page_ids[] = $page_id;
                echo "<p>Added page: {$page_data['controller']}/{$page_data['method']} (ID: $page_id)</p>";
            } else {
                $page_ids[] = $existing->id;
                echo "<p>Page exists: {$page_data['controller']}/{$page_data['method']} (ID: {$existing->id})</p>";
            }
        }

        // Add permissions for all roles (1-12)
        $roles = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        $permission_data = [];

        foreach ($roles as $role_id) {
            foreach ($page_ids as $page_id) {
                // Check if permission already exists
                $existing_permission = $this->permission->select('', [
                    'role_id' => $role_id,
                    'page_id' => $page_id
                ], 1);

                if (!$existing_permission) {
                    $permission_data[] = [
                        'role_id' => $role_id,
                        'page_id' => $page_id,
                        'is_allowed' => 1
                    ];
                }
            }
        }

        // Insert permissions
        if (!empty($permission_data)) {
            $this->db->trans_begin();
            if ($this->permission->insert_batch($permission_data)) {
                $this->db->trans_commit();
                echo "<p>✅ Added " . count($permission_data) . " SKU permissions successfully!</p>";
            } else {
                $this->db->trans_rollback();
                echo "<p>❌ Failed to add permissions.</p>";
            }
        } else {
            echo "<p>✅ All SKU permissions already exist.</p>";
        }

        echo "<p><a href='" . site_url('Sku') . "'>Test SKU Access</a></p>";
    }

    /**
     * Add Carcass permissions for all roles
     */
    public function add_carcass_permissions()
    {
        $this->load->model('page');
        $this->load->model('permission');

        // First, add Carcass pages if they don't exist
        $carcass_pages = [
            ['controller' => 'Carcasses', 'method' => 'index'],
            ['controller' => 'Carcasses', 'method' => 'add'],
            ['controller' => 'Carcasses', 'method' => 'edit'],
            ['controller' => 'Carcasses', 'method' => 'update'],
            ['controller' => 'Carcasses', 'method' => 'delete'],
        ];

        $page_ids = [];
        foreach ($carcass_pages as $page_data) {
            // Check if page already exists
            $existing_page = $this->page->select('', $page_data, 1);

            if (!$existing_page) {
                // Insert new page
                $page_id = $this->page->insert($page_data);
                $page_ids[] = $page_id;
                echo "<p>✅ Added page: {$page_data['controller']}/{$page_data['method']}</p>";
            } else {
                $page_ids[] = $existing_page->id;
                echo "<p>ℹ️ Page already exists: {$page_data['controller']}/{$page_data['method']}</p>";
            }
        }

        // Add permissions for all roles (1-12)
        $roles = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        $permission_data = [];

        foreach ($roles as $role_id) {
            foreach ($page_ids as $page_id) {
                // Check if permission already exists
                $existing_permission = $this->permission->select('', [
                    'role_id' => $role_id,
                    'page_id' => $page_id
                ], 1);

                if (!$existing_permission) {
                    $permission_data[] = [
                        'role_id' => $role_id,
                        'page_id' => $page_id,
                        'is_allowed' => 1
                    ];
                }
            }
        }

        // Insert permissions
        if (!empty($permission_data)) {
            $this->db->trans_begin();
            if ($this->permission->insert_batch($permission_data)) {
                $this->db->trans_commit();
                echo "<p>✅ Added " . count($permission_data) . " Carcass permissions successfully!</p>";
            } else {
                $this->db->trans_rollback();
                echo "<p>❌ Failed to add permissions.</p>";
            }
        } else {
            echo "<p>✅ All Carcass permissions already exist.</p>";
        }

        echo "<p><a href='" . site_url('Carcasses') . "'>Test Carcasses Access</a></p>";
    }

    /**
     * Add sample SKU data
     */
    public function add_sample_sku_data()
    {
        $this->load->model('sku_model');
        $this->load->model('customer');

        // Get customers
        $customers = $this->customer->select('', ['is_deleted' => 0]);
        if (empty($customers)) {
            echo "<p>❌ No customers found! Please add customers first.</p>";
            return;
        }

        // Sample SKU data
        $sample_data = [
            [
                'customer_id' => $customers[0]->id,
                'sku' => 'SKU-001',
                'size' => 'Large',
                'size_range' => '10-15 kg',
                'buy' => 150.75,
                'added_on' => date('Y-m-d H:i:s'),
                'added_by' => 1,
                'is_deleted' => 0
            ],
            [
                'customer_id' => count($customers) > 1 ? $customers[1]->id : $customers[0]->id,
                'sku' => 'SKU-002',
                'size' => 'Medium',
                'size_range' => '5-10 kg',
                'buy' => 125.50,
                'added_on' => date('Y-m-d H:i:s'),
                'added_by' => 1,
                'is_deleted' => 0
            ],
            [
                'customer_id' => $customers[0]->id,
                'sku' => 'SKU-003',
                'size' => 'Small',
                'size_range' => '2-5 kg',
                'buy' => 89.25,
                'added_on' => date('Y-m-d H:i:s'),
                'added_by' => 1,
                'is_deleted' => 0
            ]
        ];

        $success_count = 0;
        foreach ($sample_data as $data) {
            if ($this->sku_model->insert($data)) {
                $success_count++;
            }
        }

        echo "<p>✅ Added $success_count sample SKU records!</p>";
        echo "<p><a href='" . site_url('Sku') . "'>View SKU Table</a></p>";
    }

    /**
     * Optimize SKU table with indexes for better performance
     */
    public function optimize_sku_table()
    {
        try {
            // Add indexes if they don't exist
            $indexes = [
                "ALTER TABLE sku ADD INDEX idx_customer_id (customer_id)",
                "ALTER TABLE sku ADD INDEX idx_is_deleted (is_deleted)",
                "ALTER TABLE sku ADD INDEX idx_added_on (added_on)",
                "ALTER TABLE sku ADD INDEX idx_sku (sku)"
            ];

            $added_count = 0;
            foreach ($indexes as $sql) {
                try {
                    $this->db->query($sql);
                    $added_count++;
                } catch (Exception $e) {
                    // Index might already exist, continue
                    echo "<p>Index already exists or error: " . $e->getMessage() . "</p>";
                }
            }

            echo "<p>✅ Added $added_count database indexes for better performance!</p>";
            echo "<p><a href='" . site_url('Sku') . "'>Test SKU Performance</a></p>";
        } catch (Exception $e) {
            echo "<p>❌ Error optimizing table: " . $e->getMessage() . "</p>";
        }
    }
}

