<?php
class Schedule_list extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'schedule_list';
        parent::__construct();
    }
    /**
     * Get events
     */
    public function get_events()
    {
        $sql = <<<EOT
SELECT *
FROM schedule_list
WHERE is_deleted = 0
EOT;

        if ($res = $this->advanced_query($sql)) {
            return $res;
        } else {
            return [];
        }
    }

    /**
     * Get next trip number for customer on specific date
     */
    public function get_next_trip_number($customer_id, $eta_date, $exclude_schedule_id = null)
    {
        // Extract just the date part (ignore time)
        $date_only = date('Y-m-d', strtotime($eta_date));

        // Find the highest trip number for this customer on this date
        $sql = "SELECT MAX(sl.trip_no) as max_trip_no
                FROM schedule_list sl
                INNER JOIN schedule s ON s.id = sl.schedule_id
                WHERE s.customer_id = ?
                AND DATE(s.eta_date) = ?
                AND sl.is_deleted = 0
                AND s.is_deleted = 0";

        $binds = [$customer_id, $date_only];

        // Exclude the current schedule if provided (for reschedule/edit)
        if ($exclude_schedule_id) {
            $sql .= " AND s.id != ?";
            $binds[] = $exclude_schedule_id;
        }

        $result = $this->advanced_query($sql, $binds);
        $max_trip_no = $result && isset($result[0]) ? $result[0]->max_trip_no : null;

        // Return next trip number (start from 1 if no existing trips)
        return $max_trip_no ? ($max_trip_no + 1) : 1;
    }
}
