<?php
class Processing_fee_item extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'processing_fee_item';
        parent::__construct();
    }

    /**
     * Generate processing_fee Item
     */
    public function generate($id, $processing_fee_id, $item_id, $qty, $unit, $added_by)
    {
        $sql = <<<EOT
INSERT INTO processing_fee_item(id, processing_fee_id, item_id, qty, unit, added_by) (
    SELECT ? AS id, processing_fee.id AS processing_fee_id, item.id AS item_id, ? AS qty, ? AS unit, ? AS added_by
    FROM processing_fee
    CROSS JOIN item
    WHERE processing_fee.id = ?
        AND processing_fee.is_deleted = 0
        AND item.id = ?
        AND item.is_deleted = 0
) ON DUPLICATE KEY UPDATE
    processing_fee_item.id = LAST_INSERT_ID(processing_fee_item.id),
    processing_fee_item.qty = VALUES(processing_fee_item.qty),
    processing_fee_item.unit = VALUES(processing_fee_item.unit),
    processing_fee_item.updated_by = VALUES(processing_fee_item.added_by),
    processing_fee_item.is_deleted = 0
EOT;
        $binds = [$id, $qty, $unit, $added_by, $processing_fee_id, $item_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get processing_fee Item By ID
     */
    public function get_by_id($processing_fee_item_id)
    {
        $sql = <<<EOT
SELECT processing_fee_item.*, item.name AS item
FROM processing_fee_item
LEFT JOIN item ON item.id = processing_fee_item.item_id
WHERE processing_fee_item.id = ?
    AND processing_fee_item.is_deleted = 0
EOT;
        $binds = [$processing_fee_item_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get processing_fee Items by processing_fee
     */
    public function get_by_processing_fee($processing_fee_id)
    {
        $sql = <<<EOT
SELECT processing_fee_item.*, item.name AS item
FROM processing_fee_item
LEFT JOIN item ON item.id = processing_fee_item.item_id
WHERE processing_fee_item.processing_fee_id = ?
    AND processing_fee_item.is_deleted = 0
EOT;
        $binds = [$processing_fee_id];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
