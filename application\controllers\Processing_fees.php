<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Processing_fees extends MYT_Controller
{

    protected $_error = '';

    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('processing_fees/manager'));
    }

    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {
        if (empty($this->_uploaded)) {
            return;
        }

        if (!is_dir($new_path)) {
            mkdir($new_path, 0755, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
        }
    }

    /**
     * Review processing_fee Order for Approval
     */
    public function receive($processing_fee_id)
    {
        $processing_fee = $this->processing_fee->get_by_id($processing_fee_id) or show_404();

        $this->title = 'Receive Processing Fee No. ' . $processing_fee->id;
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processing_fees/receive'
        ];

        $processing_fee_items = $this->processing_fee_item->get_by_processing_fee($processing_fee->id);

        $data = [
            'processing_fee'          => $processing_fee,
            'processing_fee_items'    => $processing_fee_items
        ];
        $this->build_content('default', 'processing_fees/receive', $data);
    }

    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && !empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            // Make this return false if you want to make file upload required
            return true;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();
            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }

    /**
     * Add processing_fee
     */
    public function add($processing_id = null)
    {
        $processing = $this->processing->get_by_id($processing_id) or show_404();
        $order_slip_id = $processing->order_slip_id;

        $processing_fee_item_ids = $this->_get_processing_fee_item_ids();

        $config = [
            'upload_path' => FCPATH . 'assets/processing_fees',
            'allowed_types' => 'pdf',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        $rules = array_merge($this->config->item('processing_fees/add'), $this->_get_processing_fee_item_rules('', $processing_fee_item_ids));
        if ($this->_validate_form($rules) && $this->_attempt_upload($config, 'attachment', true) && ($processing_fee_id = $this->_attempt_add($order_slip_id, $processing_id))) {
            $new_path = FCPATH . 'assets/processing_fees/' . $processing_fee_id . '/';
            $this->_move_uploaded($new_path);
            redirect(site_url('processing_fees/manager'));
        } else {
            $this->title = 'Processing Fee';
            $this->css = [
                'lib/datatables/datatables/css/dataTables.bootstrap4',
                'lib/datatables/buttons/css/buttons.dataTables',
                'lib/datatables/buttons/css/buttons.bootstrap4',
                'lib/select2/dist/css/select2',
                'lib/alertify/build/css/alertify'
            ];
            $this->javascript = [
                'lib/datatables/datatables/js/jquery.dataTables',
                'lib/datatables/datatables/js/dataTables.bootstrap4',
                'lib/datatables/buttons/js/dataTables.buttons',
                'lib/datatables/buttons/js/buttons.bootstrap4',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'lib/alertify/build/alertify',
                'js/processing_fees/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $retain_items = $this->_get_processing_fee_item_data();
            if($retain_items) {
                foreach ($retain_items as $key => $value) {
                    $where = [
                        'id'            => $value['item_id'],
                        'is_deleted'    => 0
                    ];
                    $item = $this->live_sell_item->select('', $where, 1);
                    if($item) {
                        $retain_items[$key]['item_name'] = $item->name;                        
                    }
                }
            }

            $where = [
                'is_deleted' => 0
            ];
            $farms = $this->farm->select('', $where, '', 'name');

            $data = [
                'processing'                => $processing,
                'form_error'                    => $form_error,
                'processing_fee_item_ids'     => $processing_fee_item_ids,
                'customers'                     => $customers,
                'retain_items'                  => $retain_items,
                'farms'                          => $farms
            ];

            $this->build_content('default', 'processing_fees/add', $data);
        }
    }

    /**
     * Edit processing_fee
     */
    public function edit($processing_fee_id)
    {
        $processing_fee = $this->processing_fee->get_by_id($processing_fee_id) or show_404();

        $processing_fee_items = $this->processing_fee_item->get_by_processing_fee($processing_fee->id);

        $processing_fees_items = $this->_get_processing_fee_item_data('processing-fees');
        $add_ons_items = $this->_get_processing_fee_item_data('add-ons');
        $other_charges_items = $this->_get_processing_fee_item_data('other-charges');

        $rules = $this->config->item('processing_fees/edit');
        
        if (!$processing_fee_item_ids = $this->_get_processing_fee_item_ids()) {
            foreach($processing_fee_items AS $processing_fee_item) {
                $item = $this->live_sell_item->select('', ['id' => $processing_fee_item->item_id, 'is_deleted' => 0], 1);
                

                if($processing_fee_item->classification === "processing-fees") {
                    $processing_fees_items[] = [
                        'id' => $processing_fee_item->id,
                        'item_id' => $processing_fee_item->item_id,
                        'item_name' => $item?$item->name:"",
                        'classification' => $processing_fee_item->classification,
                        'qty' => $processing_fee_item->qty?:0,
                        'unit' => $processing_fee_item->unit,
                        'unit_price' => $processing_fee_item->unit_price?:0,
                        'amount' => $processing_fee_item->amount?:0,
                    ];
                }
                
                elseif ($processing_fee_item->classification === "add-ons") {
                    $add_ons_items[] = [
                        'id' => $processing_fee_item->id,
                        'item_id' => $processing_fee_item->item_id,
                        'item_name' => $item?$item->name:"",
                        'classification' => $processing_fee_item->classification,
                        'qty' => $processing_fee_item->qty?:0,
                        'unit' => $processing_fee_item->unit,
                        'unit_price' => $processing_fee_item->unit_price?:0,
                        'amount' => $processing_fee_item->amount?:0,
                    ];
                }

                else {
                    $other_charges_items[] = [
                        'id' => $processing_fee_item->id,
                        'item_id' => $processing_fee_item->item_id,
                        'item_name' => $item?$item->name:"",
                        'classification' => $processing_fee_item->classification,
                        'qty' => $processing_fee_item->qty?:0,
                        'unit' => $processing_fee_item->unit,
                        'unit_price' => $processing_fee_item->unit_price?:0,
                        'amount' => $processing_fee_item->amount?:0,
                    ];
                }

                $processing_fee_item_ids[] = $processing_fee_item->id;
            }

            $existing_ids = array_map(function ($processing_fee_item) {
                return $processing_fee_item->id;
            }, $processing_fee_items);

            $item_rules = $this->_get_processing_fee_item_rules($processing_fee->id, $existing_ids);
        } else {
            $item_rules = $this->_get_processing_fee_item_rules($processing_fee->id, $processing_fee_item_ids);
        }

        $rules = array_merge($this->config->item('processing_fees/edit'), $item_rules);

        if ($this->_validate_form($rules) && $this->_attempt_edit($processing_fee)) {
            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('processing_fees/preview/' . $processing_fee->id));
            } else {
                redirect(site_url('processing_fees/manager'));
            }
        } else {
            $this->title = 'Edit Processing Fee';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/processing_fees/add'
            ];

            if ($this->_error_msg() === "" && validation_errors() === "") {
                $form_error = '';
            } else if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = 'No processing fee item added.';
            }

            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->live_sell_customer->select('', $where, '', 'name');

            $data = [
                'form_error' => $form_error,
                'customers' => $customers,
                'processing_fee' => $processing_fee,
                'processing_fee_items' => $processing_fee_items,
                'processing_fee_item_ids' => $processing_fee_item_ids,
                // 'retain_items' => $retain_items,
                'processing_fees_items' => $processing_fees_items,
                'add_ons_items' => $add_ons_items,
                'other_charges_items' => $other_charges_items,
            ];

            $this->build_content('default', 'processing_fees/edit', $data);
        }
    }

    /**
     * Approve processing_fee
     */
    public function receive_processing_fee($processing_fee_id)
    {
        $where = [
            'id' => $processing_fee_id,
            'is_deleted' => 0
        ];
        if (!$processing_fee = $this->processing_fee->select('', $where, 1)) {
            $response = [
                'message' => 'processing_fee is not found.'
            ];
        } elseif (!$this->_attempt_receive($processing_fee)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'processing_fee is successfully received.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_receive($processing_fee)
    {
        $where = [
            'id' => $processing_fee->id
        ];
        $values = [
            'status' => 'received',
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->processing_fee->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Delete processing_fee
     */
    public function delete($processing_fee_id)
    {
        $where = [
            'id' => $processing_fee_id,
            'is_deleted' => 0
        ];
        if (!$processing_fee = $this->processing_fee->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Processing Fee is not found.'
            ];
        } elseif (!$this->_attempt_delete($processing_fee)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Processing Fee is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * processing_fees
     */
    public function manager()
    {
        $this->title = "Processing Fee";
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processing_fees/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'pending',
        ];

        $this->build_content('default', 'processing_fees/manager', $data);
    }

    /**
     * processing_fee Print Preview
     */
    public function preview($processing_fee_id)
    {
        $processing_fee = $this->processing_fee->get_by_id($processing_fee_id) or show_404();
        $processing_fee_items = $this->processing_fee_item->get_by_processing_fee($processing_fee_id);

        if (!$processing_fee_item_ids = $this->_get_processing_fee_item_ids()) {
            foreach($processing_fee_items AS $processing_fee_item) {
                $item = $this->live_sell_item->select('', ['id' => $processing_fee_item->item_id, 'is_deleted' => 0], 1);
                
                $retain_items[] = [
                    'id' => $processing_fee_item->id,
                    'item_id' => $processing_fee_item->item_id,
                    'item_name' => $item?$item->name:"",
                    'classification' => $processing_fee_item->classification,
                    'qty' => $processing_fee_item->qty?:0,
                    'unit' => $processing_fee_item->unit,
                    'unit_price' => $processing_fee_item->unit_price?:0,
                    'amount' => $processing_fee_item->amount?:0,
                ];

                $processing_fee_item_ids[] = $processing_fee_item->id;
            }

            $existing_ids = array_map(function ($processing_fee_item) {
                return $processing_fee_item->id;
            }, $processing_fee_items);

            $item_rules = $this->_get_processing_fee_item_rules($processing_fee->id, $existing_ids);
        } else {
            $item_rules = $this->_get_processing_fee_item_rules($processing_fee->id, $processing_fee_item_ids);
        }

        $this->title = 'Print Preview: Processing Fee No. ' . $processing_fee->id;

        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];

        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/processing_fees/preview',
        ];

        $where = [
            'id' => $processing_fee->customer_id
        ];
        $customer = $this->live_sell_customer->select('', $where, 1);

        $processing_fee_items = $this->processing_fee_item->get_by_processing_fee($processing_fee_id);

        $data = [
            'processing_fee' => $processing_fee,
            'customer' => $customer,
            'processing_fee_items' => $processing_fee_items,
            'processing_fee_item_ids' => $processing_fee_item_ids,
            'retain_items' => $retain_items
        ];
        $this->build_content('default', 'processing_fees/preview', $data);
    }

    /**
     * Get Info for processing_fee
     */
    public function info($processing_fee_id = null)
    {
        $data = [
            'processing_fee' => $this->processing_fee->get_by_id($processing_fee_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid processing_fees
     */
    public function partially_paid()
    {
        $data = [
            'processing_fees' => $this->processing_fee->get_partially_paid()
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('farm');
        $this->load->model('order_slip');
        $this->load->model('order_slip_item');
        $this->load->model('processing');
        $this->load->model('processing_item');
        $this->load->model('order_confirmation');
        $this->load->model('order_confirmation_item');
        $this->load->model('processing_fee');
        $this->load->model('processing_fee_item');
        $this->load->model('live_sell_item');
        $this->load->model('live_sell_customer');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');

        $this->load->library('upload');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_processing_fee_item_ids($table_type = null)
    {
        $processing_fee_item_ids = [];
        $match = $table_type?0:1;
        foreach ($_POST as $field => $value) {
            if($table_type) {
                if (strpos($field, 'processing_fee_item_') !== 0 && strpos($field, 'classification_') === 0) {
                    if($table_type && $table_type === $value) {
                        $match = 1;
                    } else {
                        $match = 0;
                    }
                }
            }


            if($match === 1 && strpos($field, 'processing_fee_item_') === 0) {
                $processing_fee_item_ids[] = $value;
            }

            // if($table_type && strpos($field, 'classification_') !== $table_type) {
            //     array_pop($processing_item_ids);
            // }
        }

        return $processing_fee_item_ids;
    } 

    /**
     * Get Item Rules
     */
    protected function _get_processing_fee_item_rules($processing_fee_id, $processing_fee_item_ids)
    {
        $rules = [];

        foreach ($processing_fee_item_ids as $processing_fee_item_id) {
            $rules[] = [
                'field' => 'item_' . $processing_fee_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            // $rules[] = [
            //     'field' => 'est_due_amount_' . $processing_fee_item_id,
            //     'label' => 'Est_due_amount',
            //     'rules' => 'required|trim|numeric|greater_than[0]'
            // ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add($order_slip_id, $processing_id)
    {
        $this->db->trans_begin();

        if (!$processing_fee_items = $this->_get_processing_fee_item_data()) {
            $this->_error = 'No processing fee item added';
            return false;
        }

        if (!$processing_fee_id = $this->_insert_processing_fee($order_slip_id, $processing_id) or !$this->_insert_processing_fee_items($processing_fee_id, $processing_fee_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $processing_fee_id;
        }
    }

    /**
     * Get Item Data
     */
    protected function _get_processing_fee_item_data()
    {
        $data = [];
        $processing_fee_item_ids = $this->_get_processing_fee_item_ids();

        foreach ($processing_fee_item_ids as $processing_fee_item_id) {
            $where = [
                'id'            => $this->input->post('item_' . $processing_fee_item_id),
                'is_deleted'    => 0
            ];
            $item = $this->live_sell_item->select('', $where, 1);

            $data[] = [
                'id'                    => is_numeric($processing_fee_item_id) ? $processing_fee_item_id : null,
                'classification'        => $this->input->post('classification_' . $processing_fee_item_id),
                'item_id'               => $this->input->post('item_' . $processing_fee_item_id),
                'item_name'             => $item?$item->name:"",
                'qty'                   => $this->input->post('qty_' . $processing_fee_item_id)?:0,
                'unit'                  => $this->input->post('unit_' . $processing_fee_item_id),
                'unit_price'            => $this->input->post('unit_price_' . $processing_fee_item_id)?:0,
                'amount'                => $this->input->post('amount_' . $processing_fee_item_id)?:0,
                'added_by'              => $_SESSION['user']->id,
                'added_on'              => date('Y-m-d H:i:s')
            ];
        }

        return $data;
    }

    /**
     * Insert processing_fee
     */
    protected function _insert_processing_fee($order_slip_id, $processing_id)
    {
        $customer_id = $this->input->post('customer');

        $processing_fee_date = DateTime::createFromFormat('m/d/Y', $this->input->post('processing_fee_date'));
        $processing_fee_time = DateTime::createFromFormat('H:i:s', $this->input->post('processing_fee_time'));

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        $values = [
            'customer_id' => $customer_id,
            'processing_id' => $processing_id,
            'order_slip_id' => $order_slip_id,
            'processing_fee_date' => $processing_fee_date->format('Y-m-d'),
            'remarks' => $this->input->post('remarks') ?: null,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!empty($this->_uploaded)) {
            $attachments = [];
            foreach ($this->_uploaded as $uploaded) {
                $attachments[] = $uploaded['file_name'];
            }
            $values['attachment'] = implode('🔥', $attachments);
        }

        return $this->processing_fee->insert($values);
    }

    /**
     * Generate processing_fee Items
     */
    protected function _insert_processing_fee_items($processing_fee_id, $processing_fee_items)
    {

        foreach ($processing_fee_items as $processing_fee_item) {
            $values = [
                'processing_fee_id' => $processing_fee_id,
                'classification' => $processing_fee_item['classification'],
                'item_id' => $processing_fee_item['item_id'],
                'qty' => $processing_fee_item['qty']?:0,
                'unit' => $processing_fee_item['unit'],
                'unit_price' => $processing_fee_item['unit_price']?:0,
                'amount' => $processing_fee_item['amount']?:0,
                'added_by' => $_SESSION['user']->id,
                'added_on' => date('Y-m-d H:i:s')
            ];
            if(!$this->processing_fee_item->insert($values)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Old Generate processing_fee Items
     */
    // protected function _generate_processing_fee_items($processing_fee_id, $processing_fee_items)
    // {

    //     foreach ($processing_fee_items as $processing_fee_item) {
    //         $where = [
    //             'id' => $processing_fee_item['item_id'],
    //             'is_deleted' => 0
    //         ];
    //         $item = $this->live_sell_item->select('', $where, 1);
    //         // if(($item->current_stock - (int)$processing_fee_item['qty']) < 0) {
    //         //     $this->_error = "Can't processing_fee item, it will lead to negative inventory.";
    //         //     return false;
    //         // }
    //         $where = [
    //             'id' => $processing_fee_item['item_id']
    //         ];
    //         $values = [
    //             'updated_by' => $processing_fee_item['added_by']
    //         ];

    //         if (!$this->live_sell_item->update($where, $values) ||
    //             !$this->processing_fee_item->generate(
    //                 $processing_fee_item['id'],
    //                 $processing_fee_id,
    //                 $processing_fee_item['item_id'],
    //                 $processing_fee_item['qty'],
    //                 $processing_fee_item['est_alw'],
    //                 $processing_fee_item['est_due_amount'],
    //                 $processing_fee_item['added_by']
    //             )
    //         ) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($processing_fee)
    {
        $this->db->trans_begin();

        $where = [
            'processing_fee_id' => $processing_fee->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$this->processing_fee_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$processing_fee_items = $this->_get_processing_fee_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'No processing fee item added';
            return false;
        } elseif(!$this->_attempt_delete_items($processing_fee->id)) {
            $this->db->trans_rollback();
            $this->_error = 'No processing fee item added';
            return false;
        } elseif (!$this->_revise_processing_fee($processing_fee->id, $processing_fee_items) || !$this->_insert_processing_fee_items($processing_fee->id, $processing_fee_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise processing_fee
     */
    protected function _revise_processing_fee($processing_fee_id, $processing_fee_items)
    {
        $processing_fee_date = DateTime::createFromFormat('m/d/Y', $this->input->post('processing_fee_date'));
        // $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($processing_fee_items, function ($current_total, $processing_fee_item) {
            // return $current_total + $processing_fee_item['amount'];
            if (isset($processing_item['amount'])) {
                return $current_total + $processing_fee_item['amount'];
            }
            return $current_total;
        });
        $discount = $this->input->post('discount') ?: null;
        $customer_id = $this->input->post('customer');


        $where = [
            'id' => $processing_fee_id
        ];
        $values = [
            'customer_id' => $customer_id,
            'processing_fee_date' => $processing_fee_date->format('Y-m-d'),
            // 'terms' => $this->input->post('terms'),
            'remarks' => $this->input->post('remarks') ?: null,
            // 'due_date' => $due_date->format('Y-m-d'),
            // 'subtotal' => $subtotal,
            // 'discount' => $discount,
            // 'total' => $subtotal - $discount,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        return $this->processing_fee->update($where, $values);
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($processing_fee)
    {
        $where = [
            'id' => $processing_fee->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->processing_fee->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($processing_fee)
    {
        $where = [
            'id' => $processing_fee->id
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->processing_fee->update($where, $values)) {
            $this->_error = 'server_error';
            return $is_deleted;
        }

        $where = [
            'processing_fee_id' => $processing_fee->id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->processing_fee_item->update($where, $values)) {
            $this->_error = 'server_error';
            return $is_deleted;
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Item
     */
    protected function _attempt_delete_items($processing_fee_id)
    {
        $where = [
            'processing_fee_id' => $processing_fee_id,
            'is_deleted' => 0
        ];

        $values = [
            'is_deleted' => 1,
            'updated_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
        ];

        if (!$this->processing_fee_item->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;   
    }


    /**
     * Callback: Check if processing_fee Item Belongs to processing_fee
     */
    public function _is_processing_fee_item($processing_fee_item_id, $processing_fee_id)
    {
        if (!is_numeric($processing_fee_item_id)) {
            return true;
        }

        $where = [
            'id' => $processing_fee_item_id,
            'processing_fee_id' => $processing_fee_id
        ];

        return !empty($this->processing_fee_item->select('', $where, 1));
    }

    /**
     * Callback: Check if processing_fee Term is Either Cash or Check
     */
    public function _is_processing_fee_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }
}
