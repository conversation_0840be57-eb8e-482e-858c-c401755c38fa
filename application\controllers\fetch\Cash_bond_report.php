<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cash_bond_report extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch checks
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'cash_bond';
        $primary_key = 'cash_bond.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_customer(),
            $this->_get_cash_bonds(),
            $this->_get_total(),
            $this->_get_one_to_thirty(),
            $this->_get_thirtyone_to_sixty(),
            $this->_get_sixtyone_to_ninety(),
            $this->_get_over_ninety(),
        ];

        $joins = <<<EOT
cash_bond
LEFT JOIN customer ON customer.id = cash_bond.customer_id
EOT;

        $where = <<<EOT
cash_bond.is_deleted = 0
AND cash_bond.status <> "cancelled"
AND cash_bond.grand_total > cash_bond.paid_amount
EOT;

        $customer = $this->input->get('customer', true) ?: null;

        if(!empty($customer)){
            $where .= <<<EOT

AND cash_bond.customer_id = '{$customer}'
EOT;
        }

        $date_from = DateTime::createFromFormat('m/d/Y', $this->input->get('date_from'));
        $date_from = $date_from ? $date_from->format('Y-m-d') : '';
        $date_to = DateTime::createFromFormat('m/d/Y', $this->input->get('date_to'));
        $date_to = $date_to ? $date_to->format('Y-m-d') : '';

        if ($date_from && $date_to) {
            $where .= <<<EOT

AND cash_bond.cb_date BETWEEN "{$date_from}" AND "{$date_to}"
EOT;
        } elseif ($date_from && !$date_to) {
            $where .= <<<EOT

AND cash_bond.cb_date >= "{$date_from}"
EOT;
        } elseif (!$date_from && $date_to) {
            $where .= <<<EOT

AND cash_bond.cb_date <= "{$date_to}"
EOT;
        }

        $group_by = "customer.id";

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where, $group_by)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'cash_bond.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'cash_bond.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'check';
            }
        ];
    }

    /**
     * Get Customer
     */
    protected function _get_customer()
    {
        return [
            'db' => 'customer.name',
            'as' => 'customer_name',
            'dt' => 0,
            'field' => 'customer_name'
        ];
    }

    /**
     * Get cash_bonds
     */
    protected function _get_cash_bonds()
    {
        return [
            'db' => 'GROUP_CONCAT("• " , cash_bond.id SEPARATOR "<br>")',
            'as' => 'cash_bond_no',
            'dt' => 1,
            'field' => 'cash_bond_no'
        ];
    }

    /**
     * Get Total
     */
    protected function _get_total()
    {
        return [
            'db' => 'IF(SUM(cash_bond.grand_total - cash_bond.paid_amount) > 0, SUM(cash_bond.grand_total - cash_bond.paid_amount), 0)',
            'as' => 'total',
            'dt' => 2,
            'field' => 'total',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get One to Thirty
     */
    protected function _get_one_to_thirty()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) <= 30 THEN (cash_bond.grand_total - cash_bond.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) <= 30 THEN (cash_bond.grand_total - cash_bond.paid_amount) END), 0)",
            'as' => 'one_to_thirty',
            'dt' => 3,
            'field' => 'one_to_thirty',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Thirtyone to Sixty 
     */
    protected function _get_thirtyone_to_sixty()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) BETWEEN 31 AND 60 THEN (cash_bond.grand_total - cash_bond.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) BETWEEN 31 AND 60 THEN (cash_bond.grand_total - cash_bond.paid_amount) END), 0)",
            'as' => 'thirtyone_to_sixty',
            'dt' => 4,
            'field' => 'thirtyone_to_sixty',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Sixtyone to Ninety 
     */
    protected function _get_sixtyone_to_ninety()
    {
        $today = date('Y-m-d');

        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) BETWEEN 61 AND 90 THEN (cash_bond.grand_total - cash_bond.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) BETWEEN 61 AND 90 THEN (cash_bond.grand_total - cash_bond.paid_amount) END), 0)",
            'as' => 'sixtyone_to_ninety',
            'dt' => 5,
            'field' => 'sixtyone_to_ninety',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }

    /**
     * Get Over Ninety
     */
    protected function _get_over_ninety()
    {
        $today = date('Y-m-d');
        
        return [
            'db' => "IF(SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) > 90 THEN (cash_bond.grand_total - cash_bond.paid_amount) END) > 0, SUM(CASE WHEN DATEDIFF('$today', cash_bond.due_date) > 90 THEN (cash_bond.grand_total - cash_bond.paid_amount) END), 0)",
            'as' => 'over_ninety',
            'dt' => 6,
            'field' => 'over_ninety',
            'formatter' => function ($d, $row) {
                if($d > 0){
                    return number_format($d, 2);                    
                } else {
                    return '';
                }
            }
        ];
    }
}
