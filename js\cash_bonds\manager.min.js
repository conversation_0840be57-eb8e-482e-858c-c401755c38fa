!function(e){"use strict";function t(t){var a=e(t),n=a.find("thead th");console.log(BASE_URI+"fetch/"+a.attr("data-package"));var r=a.DataTable({lengthChange:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[]},{className:"dt-action",targets:n.length-1}],processing:!0,serverSide:!0,ajax:BASE_URI+"fetch/"+a.attr("data-package")});return r}function a(t,a){e.when(n(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function n(t){return e.ajax({url:BASE_URI+"cash_bonds/delete/"+t,type:"POST",dataType:"json"})}function r(t,a){e.when(o(t)).then(function(){a.ajax.reload(null,!1)},function(e){a.ajax.reload(null,!1);var t=JSON.parse(e.responseText);alertify.error(t.message)})}function o(t){return e.ajax({url:BASE_URI+"cash_bonds/cancel/"+t,type:"POST",dataType:"json"})}function c(t){console.log("The status has changed to: "+t);var a=e("#cash-bond-btn"),n=a.data("add-url"),r=a.data("release-url");"released"===t?(a.text("Release Cash Bond"),a.attr("href",r)):(a.text("Add New Cash Bond"),a.attr("href",n))}e(function(){var n=t(".tableA");e(".tableA").on("click",".action-cancel",function(t){var a=e(t.currentTarget).closest("tr.cash_bond").find("td"),o=void 0!==a[0]?a[0].innerHTML:"";alertify.confirm("Are you sure you want to cancel Cash Bond No."+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");r(e,n)}).setHeader("<em>Cancel Cash Bond</em>")}).on("click",".action-delete",function(t){var r=e(t.currentTarget).closest("tr.cash_bond").find("td"),o=void 0!==r[0]?r[0].innerHTML:"";alertify.confirm("Are you sure you want to delete the Billing No. "+o+"?",function(){var e=t.currentTarget.getAttribute("data-id");a(e,n)}).setHeader("<em>Delete Invoice</em>")}),e(".tab-link").click(function(t){e(".tab-link.active").removeClass("active"),t.currentTarget.classList.add("active");var a=BASE_URI+"fetch/cash_bonds?status="+t.currentTarget.getAttribute("data-status");n.ajax.url(a).load()}),e(".tab-link").on("click",function(){var t=e(this).data("status");c(t)})})}(jQuery);