<?php
class Account_type extends MYT_Model {
	/**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'account_type';
        parent::__construct();
    }

    /**
     * Create New Account Type
     */
    public function generate($applied_to, $account_name, $account_type, $financial_statement, $account_nature, $added_by)
    {
        
        $sql = <<<EOT
INSERT INTO item(applied_to, account_name, account_type, financial_statement, account_nature, added_by) VALUES(?, ?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    applied_to = VALUES(applied_to),
    account_name = VALUES(account_name),
    account_type = VALUES(account_type),
    financial_statement = VALUES(financial_statement),
    account_nature = VALUES(account_nature),
    is_deleted = 0
EOT;
        $binds = [$applied_to, $account_name, $account_type, $financial_statement, $account_nature, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */