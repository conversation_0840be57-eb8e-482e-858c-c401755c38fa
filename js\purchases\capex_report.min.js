!function(t){"use strict";function e(e){var a=t(e),r=a.find("thead th"),n=a.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:[4,r.length-1]},{className:"text-left",targets:[0,1,2,3]}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,deferLoading:0,ajax:BASE_URI+"fetch/"+a.attr("data-package"),footerCallback:function(e,r,n,o,i){var l=this.api(),s=a.attr("data-col-sum");if(void 0!==s&&s>-1){var c=function(t){return"string"==typeof t?1*t.replace(/[\$,]/g,""):"number"==typeof t?t:0},u=l.column(s).data().reduce(function(t,e){return c(t)+c(e)},0);t(l.column(s).footer()).html(t.fn.addNumericCommas(parseFloat(u).toFixed(2)))}}});return n}t(function(){var a=e(".tableA");t("#tableFilter").submit(function(e){e.preventDefault();var r=BASE_URI+"fetch/"+t(".tableA").attr("data-package")+"?"+t(e.currentTarget).serialize();a.ajax.url(r).load()}).trigger("submit")})}(jQuery);