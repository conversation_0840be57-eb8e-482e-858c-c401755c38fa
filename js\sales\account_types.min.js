!function($){"use strict";$(function(){var c,a,b,d,e=(b=(a=$(".tableA")).find("thead th"),a.DataTable({dom:"Brtip",lengthChange:!0,paging:!1,filter:!1,info:!1,pagingType:"full_numbers",order:[[0,"asc"]],columnDefs:[{className:"text-right",targets:b.length-1}],buttons:[{extend:"print",className:"btn btn-secondary",footer:!0,exportOptions:{stripHtml:!1}},{extend:"excel",className:"btn btn-success ml-2",text:"Download Excel File",footer:!0,exportOptions:{columns:"th"}}],autoWidth:!1,processing:!0,serverSide:!0,deferLoading:0,ajax:BASE_URI+"fetch/"+a.attr("data-package"),footerCallback:function(e,f,g,h,i){var c=this.api(),b=a.attr("data-col-sum");if(void 0!==b&&b> -1){var j=function(a){return"string"==typeof a?1*a.replace(/[\$,]/g,""):"number"==typeof a?a:0},d=c.column(b).data().reduce(function(a,b){return j(a)+j(b)},0);$(c.column(b).footer()).html(function(a){for(;/(\d+)(\d{3})/.test(a.toString());)a=a.toString().replace(/(\d+)(\d{3})/,"$1,$2");return a}(parseFloat(d).toFixed(2)))}}}));$("#tableFilter").submit(function(a){a.preventDefault();var b=BASE_URI+"fetch/"+$(".tableA").attr("data-package")+"?"+$(a.currentTarget).serialize();e.ajax.url(b).load()})})}(jQuery)