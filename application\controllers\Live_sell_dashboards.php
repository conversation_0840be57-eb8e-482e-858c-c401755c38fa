<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Live_sell_dashboards extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('live_sell_dashboards/manager'));
    }

    /**
     * Live Sell Dashboard Manager
     */
    public function manager()
    {
        $this->title = 'Live Sell Dashboard';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/live_sell_dashboard/manager',
        ];

        $where = [
            'status' => 'pending',
            'is_deleted' => 0
        ];
        $pending_po = $this->purchase_order->select('', $where);
        $pending_po = $pending_po?count($pending_po):0;

        $where = [
            'status <>' => 'audited',
            'status <>' => 'received',
            'is_urgent' => 1,
            'is_deleted' => 0
        ];
        $urgent_po = $this->purchase_order->select('', $where);
        $urgent_po = $urgent_po?count($urgent_po):0;

        $total_monthly_sales = $this->so_payment->get_total_monthly_sales();
        $total_receivables = $this->order_slip->get_total_receivables();
        $total_monthly_expenses = $this->purchase_order->get_total_monthly_expenses();

        $data = [
            'pending_po' => $pending_po,
            'urgent_po' => $urgent_po,
            'total_monthly_sales' => $total_monthly_sales,
            'total_receivables' => $total_receivables,
            'total_monthly_expenses' => $total_monthly_expenses,
        ];

        $this->build_content('default', 'live_sell_dashboards/manager', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('purchase_order');
        $this->load->model('so_payment');
        $this->load->model('order_slip');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }
}
