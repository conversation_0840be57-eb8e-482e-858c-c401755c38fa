<?php
class Hauling_log extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'hauling_log';
        parent::__construct();
    }

    public function get_events() {
        $sql = <<<EOT
SELECT *
FROM hauling_log
WHERE is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get schedule information for hauling log creation
     */
    public function get_schedule_info($schedule_list_id, $customer_id)
    {
        $sql = <<<EOT
SELECT
    schedule_list.id,
    schedule_list.trip_no,
    schedule.waf_rs_no,
    CONCAT(schedule.eta_date, ' ', schedule_list.eta_time) as eta,
    customer.name as grower_farm
FROM schedule_list
INNER JOIN schedule ON schedule.id = schedule_list.schedule_id
INNER JOIN customer ON customer.id = schedule.customer_id
WHERE schedule_list.id = ? AND customer.id = ? AND schedule_list.is_deleted = 0
EOT;

        $binds = [$schedule_list_id, $customer_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }




    /**
     * Get hauling log by ID (only non-deleted)
     */
    public function get_by_id($hauling_log_id)
    {
        $sql = <<<EOT
SELECT *
FROM hauling_log
WHERE is_deleted = 0
    AND id = ?
EOT;
        $binds = [$hauling_log_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Only shows grower farms that have at least one trip without a bird count
     */
    public function get_grower_farms_from_hauling_logs()
    {
        $sql = <<<EOT
SELECT DISTINCT
    customer.id,
    customer.id as customer_id,
    customer.name as grower_farm_name
FROM hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
LEFT JOIN bird_count ON bird_count.hauling_log_id = hauling_log.id
    AND bird_count.is_deleted = 0
WHERE hauling_log.is_deleted = 0
AND bird_count.id IS NULL
GROUP BY customer.id, customer.name
ORDER BY customer.name
EOT;

        return $this->advanced_query($sql);
    }





    /**
     * Get trips for a specific customer from hauling logs (excluding trips already used in bird counts)
     */
    public function get_trips_by_customer_for_bird_count($customer_id)
    {
        $sql = <<<EOT
SELECT
    hauling_log.id as hauling_log_id,
    schedule_list.trip_no,
    hauling_log.net_weight,
    customer.name as grower_farm,
    DATE_FORMAT(schedule.eta_date, '%M %d, %Y') as formatted_date,
    DATE_FORMAT(schedule_list.eta_time, '%l:%i %p') as formatted_time
FROM hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
LEFT JOIN bird_count ON bird_count.hauling_log_id = hauling_log.id
    AND bird_count.is_deleted = 0
WHERE customer.id = ?
AND hauling_log.is_deleted = 0
AND bird_count.id IS NULL
ORDER BY schedule_list.trip_no
EOT;

        $binds = [$customer_id];

        return $this->advanced_query($sql, $binds);
    }

    /**
     * Get hauling log info for bird count (net weight, etc.)
     */
    public function get_hauling_log_info($hauling_log_id)
    {
        $sql = <<<EOT
SELECT
    hauling_log.id,
    schedule_list.trip_no,
    hauling_log.net_weight,
    customer.name as grower_farm
FROM hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
WHERE hauling_log.id = ?
AND hauling_log.is_deleted = 0
EOT;

        $binds = [$hauling_log_id];

        if ($res = $this->advanced_query($sql, $binds)) {
            return $res[0];
        } else {
            return null;
        }
    }

    /**
     * Get hauling log data with schedule information for DataTable
     */
    public function get_hauling_logs_with_schedule_info($date_filter = null)
    {
        $sql = <<<EOT
SELECT
    hauling_log.id,
    customer.name as grower_farm,
    schedule_list.trip_no,
    schedule.waf_rs_no,
    CONCAT(schedule.eta_date, ' ', schedule_list.eta_time) as eta,
    hauling_log.truck_name,
    hauling_log.truck_plate,
    hauling_log.ata_time,
    hauling_log.weighed_time,
    hauling_log.remarks,
    hauling_log.gross_weight,
    hauling_log.tare_weight,
    hauling_log.net_weight
FROM hauling_log
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id
LEFT JOIN customer ON customer.id = schedule.customer_id
WHERE hauling_log.is_deleted = 0
EOT;

        $binds = [];

        // Add date filtering if provided
        if ($date_filter) {
            $filter_date = DateTime::createFromFormat('m/d/Y', $date_filter);
            if ($filter_date) {
                $formatted_date = $filter_date->format('Y-m-d');
                $sql .= " AND DATE(hauling_log.added_on) = ?";
                $binds[] = $formatted_date;
            }
        }

        $sql .= " ORDER BY hauling_log.id DESC";

        return $this->advanced_query($sql, $binds);
    }
}
