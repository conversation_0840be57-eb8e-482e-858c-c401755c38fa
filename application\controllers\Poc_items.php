<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POC Items Controller
 * 
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */
class Poc_items extends MYT_Controller
{
    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Redirect to the manager page
     */
    public function index()
    {
        redirect(site_url('poc_items/manager'));
    }

   
    /**
     * Add POC Item main
     */
    public function add()
    {
        $rules = $this->config->item('poc_items/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('poc_items/manager'));
        } else {

            $this->title = 'Add POC Item';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = [
                "is_deleted" => 0
            ];

            $categories = $this->category->select("", $where);

            $data = [
                'categories' => $categories,
                'form_error' => $form_error
            ];
            $this->build_content('default', 'poc_items/add', $data);
        }
    }

    /**
     * Edit an existing POC item
     */
    public function edit($item_for_poc_id = null)
    {
        $where = [
            'id' => $item_for_poc_id,
            'is_deleted' => 0
        ];
        
        $item_for_poc = $this->item_for_poc->select('', $where, 1) or show_404();
        $rules = $this->config->item('poc_items/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit($item_for_poc)) {
            redirect(site_url('poc_items/manager'));
        } else {
            $this->title = 'Edit POC Item';
            $this->css = ['lib/select2/dist/css/select2'];
            $this->javascript = ['lib/select2/dist/js/select2.full', 'js/users/crud'];

            $form_error = validation_errors() ?: '';

            $where = ["is_deleted" => 0];

            $data = [
                'categories' => $this->category->select("", $where),
                'form_error' => $form_error,
                'poc_item' => $item_for_poc // Corrected to use $poc_item
            ];

            $this->build_content('default', 'poc_items/edit', $data);
        }
    }

    /**
     * Delete a POC item
     */
    public function delete($item_for_poc_id = null)
    {
        $where = [
            'id' => $item_for_poc_id,
            'is_deleted' => 0
        ];

        if (!$item_for_poc = $this->item_for_poc->select('', $where, 1)) {
            http_response_code(400);
            echo json_encode(['message' => 'POC item not found.']);
            return;
        }
        
        if (!$this->_attempt_delete($item_for_poc)) {
            http_response_code(400);
            echo json_encode(['message' => $this->_error_msg()]);
            return;
        }

        echo json_encode(['message' => 'POC item successfully removed.']);
    }

    /**
     * Manage POC items view
     */
    public function manager()
    {
        $this->title = 'POC Items';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/poc_items/manager'
        ];

        $this->build_content('default', 'poc_items/manager');
    }

    /**
     * Load essential models and libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('item_for_poc');
        $this->load->model('category');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate form input
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            return !$this->_error = 'no_validation_rule';
        }
        $this->form_validation->set_rules($rules);

        return $this->form_validation->run() or !$this->_error = 'validation_error';
    }

    /**
     * Get error messages
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();
            case 'upload_error':
                return $this->upload->display_errors();
            default:
                return '<p>' . ($this->lang->line($this->_error) ?: $this->_error) . '</p>';
        }
    }

    /**
     * Attempt to add a new item
     */
    protected function _attempt_add()
    {
        $values = [
            'item_name' => $this->input->post('item_name'),
            'unit_price' => $this->input->post('unit_price'),
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        if (!$item_for_poc_id = $this->item_for_poc->insert($values)) {
            $this->_error = 'server_error';
        }

        return $item_for_poc_id;
    }


    /**
     * Attempt to edit an existing item
     */
    protected function _attempt_edit($item_for_poc_id)
    {
        $where = [
            'id' => $item_for_poc_id->id
        ];

        $values = [
            'item_name' => $this->input->post('item_name'),
            'unit_price' => $this->input->post('unit_price'),
            'added_on' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->item_for_poc->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return true; // Return true if the update was successful
    }

    /**
     * Attempt to delete an item
     */
    protected function _attempt_delete($item_for_poc)
    {
        $where = ['id' => $item_for_poc->id];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->item_for_poc->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }
        return true;
    }
}
