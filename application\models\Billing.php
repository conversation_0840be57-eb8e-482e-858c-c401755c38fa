<?php
class Billing extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'billing';
        parent::__construct();
    }

    /**
     * Generate Billing
     */
    public function generate($doc_no, $subscription_id, $billing_date, $due_date, $total_amount, $total_rebate, $added_by, $added_on)
    {
        $sql = <<<EOT
INSERT INTO billing(doc_no, subscription_id, billing_date, due_date, total_amount, total_rebate, added_by, added_on) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    due_date = VALUES(due_date),
    total_amount = VALUES(total_amount),
    total_rebate = VALUES(total_rebate),
    updated_by = VALUES(added_by),
    updated_on = VALUES(added_on),
    is_deleted = 0
EOT;
        $binds = [$doc_no, $subscription_id, $billing_date, $due_date, $total_amount, $total_rebate, $added_by, $added_on];
        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }

    /**
     * Get Partially Paid
     */
    public function get_partially_paid()
    {
        $sql = <<<EOT
SELECT *
FROM billing
WHERE paid_amount < previous_balance + total_amount
    AND is_deleted = 0
EOT;

        return $this->advanced_query($sql);
    }

    /**
     * Get By Branch
     */
    public function get_by_branch($branch, $billing_date)
    {
        $sql = <<<EOT
SELECT billing.*, CONCAT(customer.last_name, ", ", customer.first_name) AS subscriber, CONCAT(customer.street, ", ", customer.city) AS address, subscription.subscription_no
FROM billing
LEFT JOIN subscription ON subscription.id = billing.subscription_id
LEFT JOIN customer ON customer.id = subscription.customer_id
WHERE subscription.branch_id = ?
    AND billing.billing_date = ?
    AND billing.is_deleted = 0
EOT;
        $binds = [$branch, $billing_date];

        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
