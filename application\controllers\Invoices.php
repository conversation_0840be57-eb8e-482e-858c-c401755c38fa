<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Victoria Trading Billing
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Invoices extends MYT_Controller
{

    protected $_error = '';
    protected $_uploaded = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('invoices/manager'));
    }

    /**
     * Add Invoice
     */
    public function add()
    {

        $invoice_item_ids = $this->_get_invoice_item_ids();
        $rules = array_merge($this->config->item('invoices/add'), $this->_get_invoice_item_rules('', $invoice_item_ids));

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        if ($this->_validate_form($rules) and $this->_attempt_upload($config, 'attachments', true) and $id = $this->_attempt_add() and $this->_add_attachment($id)) {

            $new_path = FCPATH. 'assets/asylum/invoices/'.$id.'/';
            $this->_move_uploaded($new_path);

            redirect(site_url('invoices/manager'));
        } else {

            $this->_delete_uploaded();

            $this->title = 'Add Invoice';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/invoices/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = [
                'is_deleted' => 0,
                'is_active' => 1
            ];
            $customers = $this->customer->select('', $where,'','FIELD(id,"16") DESC, name');
            $where = [
                'is_deleted' => 0
            ];
            $items = $this->sales_item->select('', $where);

            $where = [
                'applied_to' => 'Sales Income',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);

            $data = [
                'account_types' => $account_types,
                'form_error' => $form_error,
                'invoice_item_ids' => $invoice_item_ids,
                'customers' => $customers,
                'items' => $items
            ];

            $this->build_content('default', 'invoices/add', $data);
        }
    }

    /**
     * Edit Invoice
     */
    public function edit($invoice_id)
    {
        $invoice = $this->invoice->get_by_id($invoice_id) or show_404();

        $invoice_items = $this->invoice_item->get_by_invoice($invoice->id);

        $rules = $this->config->item('invoices/edit');
        if (!$invoice_item_ids = $this->_get_invoice_item_ids()) {
            $existing_ids = array_map(function ($invoice_item) {
                return $invoice_item->id;
            }, $invoice_items);

            $item_rules = $this->_get_invoice_item_rules($invoice->id, $existing_ids);
        } else {
            $item_rules = $this->_get_invoice_item_rules($invoice->id, $invoice_item_ids);
        }

        $rules = array_merge($this->config->item('invoices/edit'), $item_rules);

        $config = [
            'upload_path' => FCPATH . 'assets/asylum/',
            'allowed_types' => 'pdf|PDF',
            'max_size' => 0,
            'overwrite' => false,
            'encrypt_name' => false
        ];

        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }


        if ($this->_validate_form($rules) and $this->_attempt_upload($config, 'attachments', false) and $this->_attempt_edit($invoice) and $this->_add_attachment($invoice_id)) {

            $new_path = FCPATH. 'assets/asylum/invoices/'.$invoice->id.'/';
            $this->_move_uploaded($new_path);

            if ($this->input->get('rdr') === 'preview') {
                redirect(site_url('invoices/preview/' . $invoice->id));
            } else {
                redirect(site_url('invoices/manager'));
            }
        } else {

            $this->_delete_uploaded();

            $this->title = 'Edit Invoice';
            $this->css = [
                'lib/alertify/build/css/alertify',
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/alertify/build/alertify',
                'lib/select2/dist/js/select2',
                'lib/input-autosize/dist/autosize',
                'js/invoices/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $where = ['is_deleted' => 0];
            $customers = $this->customer->select('', $where,'','FIELD(id,"16") DESC, name');
            $items = $this->sales_item->select('', $where);
            $invoice_attachments = $this->invoice_attachment->get_by_id($invoice_id);

            $where = [
                'id' => $invoice->customer_id,
                'is_deleted' => 0
            ];
            $customer_walkin = $this->customer->select('', $where , 1);

            $where = [
                'applied_to' => 'Sales Income',
                'is_deleted' => 0
            ];
            $account_types = $this->account_type->select('', $where);

            $data = [
                'account_types' => $account_types,
                'form_error' => $form_error,
                'invoice' => $invoice,
                'invoice_items' => $invoice_items,
                'invoice_item_ids' => $invoice_item_ids,
                'customers' => $customers,
                'items' => $items,
                'invoice_attachments' => $invoice_attachments,
                'customer_walkin' => $customer_walkin
            ];

            $this->build_content('default', 'invoices/edit', $data);
        }
    }

    /**
     * Approve Invoice
     */
    public function approve($invoice_id)
    {
        $where = [
            'id' => $invoice_id,
            'is_deleted' => 0
        ];
        if (!$invoice = $this->invoice->select('', $where, 1)) {
            $response = [
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_approve($invoice)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Invoice is successfully approved.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Approve Invoice
     */
    public function review($invoice_id)
    {
        $where = [
            'id' => $invoice_id,
            'is_deleted' => 0
        ];

        if (!$invoice = $this->invoice->select('', $where, 1)) {
            $response = [
                'isSuccess' => 0,
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_review($invoice)) {
            $response = [
                'isSuccess' => 0,
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'isSuccess' => 1,
                'message' => 'Invoice is successfully reviewed.'
            ];
        }

        echo json_encode($response);
    }


    /**
     * Mark for Approval Invoice
     */
    public function for_approval($invoice_id)
    {
        $where = [
            'id' => $invoice_id,
            'is_deleted' => 0
        ];
        if (!$invoice = $this->invoice->select('', $where, 1)) {
            $response = [
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_for_approval($invoice)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Invoice is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Delete Invoice
     */
    public function delete($invoice_id)
    {
        $where = [
            'id' => $invoice_id,
            'is_deleted' => 0
        ];
        if (!$invoice = $this->invoice->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_delete($invoice)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Invoice is successfully removed.'
            ];
        }

        echo json_encode($response);
    }


        /**
     * Delete Invoice Attachment
     */
    public function delete_attachment($attachment_id)
    {
        $where = [
            'id' => $attachment_id,
            'is_deleted' => 0
        ];
        if (!$invoice_attachment = $this->invoice_attachment->select('', $where, 1)) {
            $response = [
                'message' => 'Invoice Attachment is not found.'
            ];
        } elseif (!$this->_attempt_delete_attachment($invoice_attachment)) {
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Invoice Attachment is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Return to Pending Invoice
     */
    public function return_to_pending($invoice_id)
    {
        $where = [
            'id' => $invoice_id
        ];
        if (!$invoice = $this->invoice->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_pending($invoice)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Invoice is successfully changed to PENDING.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Invoices
     */
    public function manager()
    {
        $this->title = 'Invoice';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/invoices/manager'
        ];

        $data = [
            'status' => $this->input->get('status', true) ?: 'approved'
        ];

        $this->build_content('default', 'invoices/manager', $data);
    }


    /**
     * Invoices History by Customer
     */
    public function history($customer_id)
    {
        $this->title = 'Invoices History';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/invoices/history'
        ];

        $data = [
            'customer_id' => $customer_id
        ];

        $this->build_content('default', 'invoices/history', $data);
    }

    /**
     * Invoice Print Preview
     */
    public function preview($invoice_id)
    {
        ($invoice = $this->invoice->get_by_id($invoice_id) and in_array($invoice->status, ['approved','bad_debt','cancelled'])) or show_404();

        $this->title = 'Print Preview: Invoice No. ' . $invoice->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/invoices/preview'
        ];

        $where = [
            'id' => $invoice->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $invoice_items = $this->invoice_item->get_by_invoice($invoice_id);

        $data = [
            'invoice' => $invoice,
            'customer' => $customer,
            'invoice_items' => $invoice_items
        ];
        $this->build_content('default', 'invoices/preview', $data);
    }

    /**
     * Invoice PDF
     */
    public function make_pdf($invoice_id)
    {
        $invoice = $this->invoice->get_by_id($invoice_id) or show_404();

        $customer = $this->customer->select('', ['id' => $invoice->customer_id], 1);

        $invoice_items = $this->invoice_item->get_by_invoice($invoice->id);

        $temp = 'assets/asylum/mpdf';
        if (!is_dir(FCPATH . $temp)) {
            mkdir(FCPATH . $temp, 0755, true);
            write_file(FCPATH . $temp . '/index.html', 'Directory access is forbidden.');
        }

        $dir = 'assets/invoices/' . $invoice->id . '/';
        if (!is_dir(FCPATH . $dir)) {
            mkdir(FCPATH . $dir, 0755, true);
            write_file(FCPATH . $dir . '/index.html', 'Directory access is forbidden.');
        }

        $mpdf = new \Mpdf\Mpdf([
            'tempDir' => FCPATH . $temp,
            'mode' => 'html',
            'format' => 'A4',
            'orientation' => 'P',
            'default_font_size' => 8,
            'default_font' => 'Calibri'
        ]);

        $content = [
            'invoice' => $invoice,
            'customer' => $customer,
            'invoice_items' => $invoice_items
        ];
        $body = $this->load->view('invoices/for_pdf', $content, true);
        $mpdf->WriteHTML($body, \Mpdf\HTMLParserMode::HTML_BODY);

        $file_name = 'invoice-' . $invoice->id . '-' . date('Ymd') . '.pdf';
        $full_path = FCPATH . $dir . $file_name;
        chmod($full_path, 0755);

        file_exists($full_path) && unlink($full_path);
        $mpdf->Output($full_path, 'F'); // save to file
        redirect(base_url($dir . $file_name));
    }

    /**
     * Get Info for Invoice
     */
    public function info($invoice_id = null)
    {
        $data = [
            'invoice' => $this->invoice->get_for_payment($invoice_id)
        ];

        echo json_encode($data);
    }

    /**
     * Get Partially Paid Invoices
     */
    public function partially_paid($customer_id = null)
    {
        $data = [
            'invoices' => $this->invoice->get_partially_paid($customer_id)
        ];

        echo json_encode($data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('account_type');
        $this->load->model('invoice');
        $this->load->model('invoice_item');
        $this->load->model('item');
        $this->load->model('sales_item');
        $this->load->model('customer');
        $this->load->model('invoice_attachment');

        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
        $this->load->library('upload');
        $this->load->library('image_lib');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Get Input Suffices
     */
    protected function _get_invoice_item_ids()
    {
        $invoice_item_ids = [];
        foreach ($_POST as $field => $value) {
            if (strpos($field, 'invoice_item_') === 0) {
                $invoice_item_ids[] = $value;
            }
        }

        return $invoice_item_ids;
    }

    /**
     * Get Item Rules
     */
    protected function _get_invoice_item_rules($invoice_id, $invoice_item_ids)
    {
        $rules = [];

        foreach ($invoice_item_ids as $invoice_item_id) {
            $rules[] = [
                'field' => 'invoice_item_' . $invoice_item_id,
                'label' => 'Invoice Item',
                'rules' => 'trim|callback__is_invoice_item[' . $invoice_id . ']',
                'errors' => [
                    '_is_invoice_item' => 'Invoice Item is not found.'
                ]
            ];

            $rules[] = [
                'field' => 'item_' . $invoice_item_id,
                'label' => 'Item',
                'rules' => 'required|trim|max_length[255]'
            ];

            $rules[] = [
                'field' => 'qty_' . $invoice_item_id,
                'label' => 'Qty',
                'rules' => 'required|trim|numeric|greater_than[0]'
            ];

            $rules[] = [
                'field' => 'unit_' . $invoice_item_id,
                'label' => 'Unit',
                'rules' => 'required|trim|max_length[50]'
            ];

            $rules[] = [
                'field' => 'price_' . $invoice_item_id,
                'label' => 'Price',
                'rules' => 'required|trim|numeric|greater_than_equal_to[0]'
            ];

            $rules[] = [
                'field' => 'rs_alw_' . $invoice_item_id,
                'label' => 'RS/SO ALW',
                'rules' => 'trim'
            ];

            $rules[] = [
                'field' => 'account_type_' . $invoice_item_id,
                'label' => 'Account Name',
                'rules' => 'required'
            ];
        }

        return $rules;
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_type = $this->input->post('customer');
        $customer_id = null;

        if ($customer_type === WALKIN_CUSTOMER) {
            $customer_details = [
                'name' => $this->input->post('walkin_customer'),
                'address' => $this->input->post('address'),
                'phone_no' => $this->input->post('phone_no'),
                'email' => $this->input->post('email'),
                'is_walkin' => 1,
                'added_on' => date('Y-m-d H:i:s'),
                'added_by' => $_SESSION['user']->id
            ];

            if (!$customer_id = $this->customer->insert($customer_details)) {
                return false;
            }
        }

        if (!$invoice_items = $this->_get_invoice_item_data()) {
            $this->_error = 'no_invoice_item_added';
            return false;
        }

        if (!$invoice_id = $this->_generate_invoice($invoice_items, $customer_id) or !$this->_generate_invoice_items($invoice_id, $invoice_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $invoice_id;
        }
    }

    /**
     * Attempt add attachment
     */
    protected function _add_attachment($id)
    {
        if (!empty($this->_uploaded)) {
            $values = [];
            foreach ($this->_uploaded as $uploaded) {
                $values[] = [
                    'invoice_id' => $id,
                    'file_name' => $uploaded['file_name'],
                    'added_by' => $_SESSION['user']->id,
                    'added_on' => (new DateTime())->format('Y-m-d H:i:s')
                ];
            }
            if (!$attachment_id = $this->invoice_attachment->insert_batch($values)) {
                $this->_error = 'server_error';
                return false;
            }

            return $attachment_id;
        }else{
            return true;
        }


    }

    /**
     * Get Item Data
     */
    protected function _get_invoice_item_data()
    {
        $data = [];
        $invoice_item_ids = $this->_get_invoice_item_ids();

        foreach ($invoice_item_ids as $invoice_item_id) {
            $where = [
                'is_deleted' => 0,
                'id' => $this->input->post('item_' . $invoice_item_id)
            ];

            $item_name = $this->sales_item->select('name', $where)[0]->name;
            $rs = $this->input->post('rs_'.$invoice_item_id);
            $alw = $this->input->post('alw_'.$invoice_item_id);
            $unit = $this->input->post('unit_' . $invoice_item_id);
            $qty = $this->input->post('qty_' . $invoice_item_id);
            $price = $this->input->post('price_' . $invoice_item_id);
            $account_type = $this->input->post('account_type_' . $invoice_item_id);
            $item_date = DateTime::createFromFormat('m/d/Y', $this->input->post('item_date_' . $invoice_item_id));
            $added_by = $_SESSION['user']->id;

            $data[] = [
                'id' => is_numeric($invoice_item_id) ? $invoice_item_id : null,
                'rs' => $rs,
                'alw' => $alw,
                'item_id' => $this->sales_item->generate($item_name, $unit, $price, $added_by),
                'unit' => $unit,
                'qty' => $qty,
                'price' => $price,
                'amount' => $qty * $price,
                'account_type' => $account_type,
                'added_by' => $added_by
            ];
        }

        return $data;
    }

    /**
     * Generate Invoice
     */
    protected function _generate_invoice($invoice_items, $new_customer_id = null)
    {

        $invoice_date = DateTime::createFromFormat('m/d/Y', $this->input->post('invoice_date'));
        $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($invoice_items, function ($current_total, $invoice_item) {
            return $current_total + $invoice_item['amount'];
        });
        $discount = $this->input->post('discount') ?: null;

        $customer_id = $this->input->post('customer');

        $terms = $this->input->post('terms');

        if ($customer_id === WALKIN_CUSTOMER) {
            $customer_id = $new_customer_id;

            $customer_name = $this->input->post('walkin_customer');

            if (!$customer_id = $this->_get_customer_data($customer_name)) {
                return false;
            }

        }

        $where = [
            'YEAR(added_on) =' => date('Y')
        ];
        $existing = $this->invoice->select('', $where);
        $invoice_no = date('Y') . ' - ' . sprintf('%06d', count($existing) + 1);

        $values = [
            'customer_id' => $customer_id,
            'invoice_date' => $invoice_date->format('Y-m-d'),
            'invoice_no' => $invoice_no,
            'terms' => $terms,
            'remarks' => $this->input->post('remarks') ?: null,
            'status' => 'approved',
            'due_date' => $due_date->format('Y-m-d'),
            'subtotal' => $subtotal,
            'discount' => $discount,
            'total' => $subtotal - $discount,
            'added_by' => $_SESSION['user']->id,
            'added_on' => date('Y-m-d H:i:s')
        ];

        // if($terms === 'cash' || $terms === 'check'){
        //     $values['status'] = 'approved';
        // }

        return $this->invoice->insert($values);
    }

    /**
     * Get Customer Data
     */
    protected function _get_customer_data($customer_name)
    {
        $where = [
            'name' => $customer_name,
            'is_walkin' => 1,
            'is_deleted' => 0
        ];

        $customer = $this->customer->select('', $where, 1);

        if(!empty($customer)){
            $where = [
                'id' => $customer->id
            ];
            $values = [
                'address' => $this->input->post('address'),
                'phone_no' => $this->input->post('phone_no'),
                'email' => $this->input->post('email'),
                'updated_on' => date('Y-m-d H:i:s'),
                'updated_by' => $_SESSION['user']->id
            ];

            if(!$this->customer->update($where, $values)){
                return false;
            }
            $customer_id = $customer->id;
        }else{
            $values = [
                'name' => $customer_name,
                'address' => $this->input->post('address'),
                'phone_no' => $this->input->post('phone_no'),
                'email' => $this->input->post('email'),
                'is_walkin' => 1,
                'added_on' => date('Y-m-d H:i:s'),
                'added_by' => $_SESSION['user']->id
            ];

            if(!$customer_id = $this->customer->insert($values)){
                return false;
            }
        }

        return $customer_id;
    }

    /**
     * Generate Invoice Items
     */
    protected function _generate_invoice_items($invoice_id, $invoice_items)
    {
        foreach ($invoice_items as $invoice_item) {
            $where = [
                'id' => $invoice_item['item_id']
            ];
            $values = [
                'price' => $invoice_item['price'],
                'updated_by' => $invoice_item['added_by']
            ];

            if (!$this->sales_item->update($where, $values) ||
                !$this->invoice_item->generate(
                    $invoice_item['id'],
                    $invoice_item['rs'],
                    $invoice_item['alw'],
                    $invoice_id,
                    $invoice_item['item_id'],
                    $invoice_item['qty'],
                    $invoice_item['unit'],
                    $invoice_item['price'],
                    $invoice_item['amount'],
                    $invoice_item['account_type'],
                    $invoice_item['added_by']
                )
            ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($invoice)
    {
        $this->db->trans_begin();

        $where = [
            'invoice_id' => $invoice->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$this->invoice_item->update($where, $values)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } elseif (!$invoice_items = $this->_get_invoice_item_data()) {
            $this->db->trans_rollback();
            $this->_error = 'no_invoice_item_added';
            return false;
        } elseif (!$this->_revise_invoice($invoice->id, $invoice_items) || !$this->_generate_invoice_items($invoice->id, $invoice_items)) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    /**
     * Revise Invoice
     */
    protected function _revise_invoice($invoice_id, $invoice_items)
    {

        $invoice_date = DateTime::createFromFormat('m/d/Y', $this->input->post('invoice_date'));
        $due_date = DateTime::createFromFormat('m/d/Y', $this->input->post('due_date'));
        $subtotal = array_reduce($invoice_items, function ($current_total, $invoice_item) {
            return $current_total + $invoice_item['amount'];
        });
        $discount = $this->input->post('discount') ?: null;

        $customer_id = $this->input->post('customer');
        $terms = $this->input->post('terms');


        if ($customer_id === WALKIN_CUSTOMER) {
            $customer_name = $this->input->post('walkin_customer');

            if (!$customer_id = $this->_get_customer_data($customer_name)) {
                return false;
            }

        }

        $where = [
            'id' => $invoice_id
        ];
        $values = [
            'customer_id' => $customer_id,
            'invoice_date' => $invoice_date->format('Y-m-d'),
            'terms' => $terms,
            'remarks' => $this->input->post('remarks') ?: null,
            'due_date' => $due_date->format('Y-m-d'),
            'subtotal' => $subtotal,
            'discount' => $discount,
            'total' => $subtotal - $discount,
            'updated_by' => $_SESSION['user']->id
        ];

        return $this->invoice->update($where, $values);
    }

    /**
     * Attempt Review
     */
    protected function _attempt_review($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];
        $values = [
            'is_reviewed' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Approve
     */
    protected function _attempt_approve($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];
        $values = [
            'status' => 'approved',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_updated = $this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }


    /**
     * Attempt for Approval
     */
    protected function _attempt_for_approval($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];
        $values = [
            'status' => 'for approval',
            'approved_by' => $_SESSION['user']->id,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];

        $values = [
            'is_deleted'    => 1,
            'delete_reason' => $this->input->post('reason'),
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }


        /**
     * Attempt Delete Attachment
     */
    protected function _attempt_delete_attachment($invoice_attachment)
    {
        $where = [
            'id' => $invoice_attachment->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->invoice_attachment->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }else{
            $path = FCPATH . 'assets/asylum/invoices/' .$invoice_attachment->invoice_id.'/'. $invoice_attachment->file_name;
            unlink($path);
        }

        return $is_deleted;
    }

    /**
     * Attempt Pending
     */
    protected function _attempt_pending($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];
        $values = [
            'status' => 'pending',
            'is_deleted' => 0,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => date('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if Invoice Item Belongs to Invoice
     */
    public function _is_invoice_item($invoice_item_id, $invoice_id)
    {
        if (!is_numeric($invoice_item_id)) {
            return true;
        }

        $where = [
            'id' => $invoice_item_id,
            'invoice_id' => $invoice_id
        ];

        return !empty($this->invoice_item->select('', $where, 1));
    }

    /**
     * Callback: Check if Invoice Term is Either Cash or Check
     */
    public function _is_invoice_terms($terms)
    {
        return in_array($terms, ['cash', 'check']);
    }


    /**
     * Upload File to Server
     */
    protected function _attempt_upload($config, $field_name, $required = false)
    {
        $total_attempt = !empty($_FILES[$field_name]['name'][0]) ? count($_FILES[$field_name]['name']) : 0;

        if ($required && $total_attempt === 0 && empty($_FILES)) {
            $this->_error = 'You did not select a file to upload.';
            return false;
        }

        for ($i = 0; $i < $total_attempt; $i++) {
            /*
             * Because CI Upload Library does not support multiple upload,
             * trick it by setting individual file upload as $_FILES['userfile'].
             *
             */
            $_FILES['userfile']['name'] = $_FILES[$field_name]['name'][$i];
            $_FILES['userfile']['type'] = $_FILES[$field_name]['type'][$i];
            $_FILES['userfile']['tmp_name'] = $_FILES[$field_name]['tmp_name'][$i];
            $_FILES['userfile']['error'] = $_FILES[$field_name]['error'][$i];
            $_FILES['userfile']['size'] = $_FILES[$field_name]['size'][$i];

            $this->upload->initialize($config);

            if (!$this->upload->do_upload()) {
                $this->_error = 'upload_error';
                break;
            } else {
                $this->_uploaded[] = $this->upload->data();

                $uploads = $this->_uploaded;

            }
        }

        $total_uploaded = count($this->_uploaded);

        $is_uploaded = $total_uploaded === $total_attempt;

        return $required ? $is_uploaded : ($is_uploaded || empty($_FILES));
    }


    /**
     * Move Uploaded File
     */
    protected function _move_uploaded($new_path)
    {

        if (!is_dir($new_path)) {
            mkdir($new_path, 0777, true);
        }

        foreach ($this->_uploaded as $uploaded) {
            rename($uploaded['full_path'], $new_path . $uploaded['file_name']);
            $this->index_html($new_path);
        }
    }

    /**
     * Delete Uploaded File
     */
    protected function _delete_uploaded()
    {
        if (!empty($this->_uploaded)) {
            foreach ($this->_uploaded as $uploaded) {
                unlink($uploaded['full_path']);
            }
        }
    }

    /**
     * Write index.html file
     */
    protected function index_html($path)
    {
        write_file($path . '/index.html', 'Directory access if forbidden');
    }

    /**
     * Get All Info of Sales items
     */
    public function all_infos($customer_id)
    {
        $data = [
            'invoices' => $this->invoice->get_invoices_by_customer($customer_id) ?: [],
        ];

        echo json_encode($data);
    }

    /**
     * Mark Invoices as Bad Debt
     */
    public function bad_debt($invoice_id)
    {
        $this->db->trans_begin();

        $where = [
            'id' => $invoice_id,
            'is_deleted' => 0
        ];

        if (!$invoice = $this->invoice->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Invoice is not found.'
            ];
        } elseif (!$this->_attempt_bad_debt($invoice)) {
            $this->db->trans_rollback();
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $this->db->trans_commit();
            $response = [
                'message' => 'Invoice is marked as bad debt.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Mark as Bad Debt
     */
    protected function _attempt_bad_debt($invoice)
    {
        $where = [
            'id' => $invoice->id
        ];

        $values = [
            'status'        => 'bad_debt',
            'updated_by'    => $_SESSION['user']->id,
            'updated_on'    => date('Y-m-d H:i:s')
        ];

        if (!$this->invoice->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        return true;
    }

    /**
     * Bad Debts
     */
    public function bad_debts()
    {
        $this->title = 'Accounts Receivable Problematic';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/select2/dist/css/select2',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/jszip/jszip',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/datatables/buttons/js/buttons.print',
            'lib/alertify/build/alertify',
            'lib/select2/dist/js/select2.full',
            'js/invoices/bad_debts'
        ];

        $customers = $this->customer->get_with_bad_debts();

        $data = [
            'customers' => $customers
        ];

        $this->build_content('default', 'invoices/bad_debts', $data);
    }

    /**
     * Invoice Sales
     */
    public function invoice_sales()
    {
        $account_name = $this->input->get('account_name');
        $from         = !empty($this->input->get('from')) ? date('Y-m-d', strtotime($this->input->get('from'))) : null;
        $to           = !empty($this->input->get('to')) ? date('Y-m-d', strtotime($this->input->get('to'))) : null;

        if (empty($account_name) || empty($from) || empty($to)) {
            show_404();
        }

        $this->title = $account_name . ' <br>List of Invoices <br>' . date('F j, Y', strtotime($from)) . ' to ' . date('F j, Y', strtotime($to));
        $this->css   = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.html5',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'js/invoices/invoice_sales',
        ];

        $where = [
            'is_deleted' => 0,
        ];

        $data = [
            'account_name' => $account_name,
            'from'         => $from,
            'to'           => $to,
        ];

        $this->build_content('default', 'invoices/invoice_sales', $data);
    }
}
