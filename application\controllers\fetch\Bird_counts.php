<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Bird_counts extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Bird Counts
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'bird_count';
        $primary_key = 'bird_count.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_eta_date(),
            $this->_get_grower_farm(),
            $this->_get_trip_no(),
            $this->_get_live_heads(),
            $this->_get_doa(),
            $this->_get_daa(),
            $this->_get_runts(),
            $this->_get_rejected(),
            $this->_get_shackle_count(),
            $this->_get_kilos(),
            $this->_get_alw(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
bird_count
LEFT JOIN hauling_log ON hauling_log.id = bird_count.hauling_log_id AND hauling_log.is_deleted = 0
LEFT JOIN schedule_list ON schedule_list.id = hauling_log.schedule_list_id AND schedule_list.is_deleted = 0
LEFT JOIN schedule ON schedule.id = schedule_list.schedule_id AND schedule.is_deleted = 0
LEFT JOIN customer ON customer.id = schedule.customer_id AND customer.is_deleted = 0
EOT;

        $where = <<<EOT
bird_count.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }


    protected function _get_id()
    {
        return [
            'db' => 'bird_count.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    protected function _get_class()
    {
        return [
            'db' => 'bird_count.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'bird_count';
            }
        ];
    }

    protected function _get_eta_date()
    {
        return [
            'db' => 'schedule.eta_date',
            'as' => 'eta_date',
            'dt' => 0,
            'field' => 'eta_date',
            'formatter' => function ($d, $row) {
                return $d ? date('F j, Y', strtotime($d)) : '';
            }
        ];
    }

    protected function _get_grower_farm()
    {
        return [
            'db' => 'customer.name',
            'as' => 'grower_farm',
            'dt' => 1,
            'field' => 'grower_farm'
        ];
    }

    protected function _get_trip_no()
    {
        return [
            'db' => 'schedule_list.trip_no',
            'dt' => 2,
            'field' => 'trip_no'
        ];
    }

    protected function _get_live_heads()
    {
        return [
            'db' => 'bird_count.live_heads',
            'dt' => 3,
            'field' => 'live_heads'
        ];
    }

    protected function _get_doa()
    {
        return [
            'db' => 'bird_count.doa',
            'dt' => 4,
            'field' => 'doa'
        ];
    }

    protected function _get_daa()
    {
        return [
            'db' => 'bird_count.daa',
            'dt' => 5,
            'field' => 'daa'
        ];
    }

    protected function _get_runts()
    {
        return [
            'db' => 'bird_count.runts',
            'dt' => 6,
            'field' => 'runts'
        ];
    }

    protected function _get_rejected()
    {
        return [
            'db' => 'bird_count.rejected',
            'dt' => 7,
            'field' => 'rejected'
        ];
    }

    protected function _get_shackle_count()
    {
        return [
            'db' => 'bird_count.shackle_count',
            'dt' => 8,
            'field' => 'shackle_count'
        ];
    }

    protected function _get_kilos()
    {
        return [
            'db' => 'hauling_log.net_weight',
            'as' => 'kilos',
            'dt' => 9,
            'field' => 'kilos',
            'formatter' => function ($d, $row) {
                return number_format($d, 2);
            }
        ];
    }

    protected function _get_alw()
    {
        return [
            'db' => 'bird_count.alw',
            'dt' => 10,
            'field' => 'alw'
        ];
    }

    protected function _get_actions()
    {
        return [
            'db' => 'bird_count.id',
            'as' => 'actions',
            'dt' => 11,
            'field' => 'id',
            'formatter' => function ($d, $row) {
                return $this->_get_actions_html($d);
            }
        ];
    }

    protected function _get_actions_html($bird_count_id)
    {
        $res = '<div class="dropdown text-right">';
        $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
        $res .= '<div class="dropdown-menu dropdown-menu-right">';
        if (isset($_SESSION['user']->role_id) && in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
            $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $bird_count_id . '">Delete</button>';
            $res .= '<a href="' . site_url('bird_counts/preview/' . $bird_count_id) . '" class="dropdown-item text-left">Preview</a>';
        }
        $res .= '</div>';
        $res .= '</div>';
        return $res;
    }
}
