[{"type": "p", "name": "message", "label": "{ String or DOMElement }", "desc": "Sets the dialog contents", "defaultValue": "undefined", "script": "alertify.prompt().set('message', 'This is a new message!').show();"}, {"type": "p", "name": "labels", "label": "{ Object }", "desc": "Sets the OK/Cancel button's labels", "defaultValue": "{ok: alertify.defaults.glossary.ok, cancel: alertify.defaults.glossary.cancel}", "script": "alertify.prompt('labels changed!').set('labels', {ok:'Alright!', cancel:'Naa!'});"}, {"type": "p", "name": "reverseButtons", "label": "{ <PERSON><PERSON><PERSON> }", "desc": "Gets or sets a value indicating whether to reverse the dialog buttons order.", "defaultValue": "false", "script": "alertify.prompt('Buttons are reversed').set('reverseButtons', true);", "toggle": {"value": true}}, {"type": "e", "name": "onok", "label": "{ Function }", "desc": "Gets or sets a callback function to be invoked when the user clicks 'Ok' button.", "defaultValue": "undefined", "script": "alertify.prompt('a callback will be invoked on ok.').set('onok', function(closeEvent){ alertify.success('Ok');} );", "remark": "To prevent the dialog from closing, either set <code>closeEvent.cancel = true</code> or make your callback return <code>false</code> ."}, {"type": "e", "name": "oncancel", "label": "{ Function }", "desc": "Gets or sets a callback function to be invoked when the user clicks 'Cancel' button or closes the dialog.", "defaultValue": "undefined", "script": "alertify.prompt('a callback will be invoked on cancel.').set('oncancel', function(){ alertify.error('Cancel');} );", "remark": "To prevent the dialog from closing, either set <code>closeEvent.cancel = true</code> or make your callback return <code>false</code> ."}, {"type": "p", "name": "type", "label": "{ String }", "desc": "Gets or sets the HTML type of the input field. <br/><br/><span>Supported types:</span> <code>text</code>, <code>color</code>, <code>date</code>, <code>datetime-local</code>, <code>email</code>, <code>month</code>, <code>number</code>, <code>password</code>, <code>search</code>, <code>tel</code>, <code>time</code>, <code>week</code>.", "defaultValue": "text", "script": "alertify.prompt('Input (text):').set('type', 'text');", "list": {"value": "text", "items": ["text", "color", "date", "datetime-local", "email", "month", "number", "password", "search", "tel", "time", "week"]}}]