<?php
// Simple database test
try {
    $pdo = new PDO('mysql:host=localhost;dbname=wadcompa_lavc', 'root', '');
    echo "Database connection successful!\n";
    
    // Check customer table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer WHERE is_deleted = 0");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active customers: " . $count['count'] . "\n";
    
    // Check sku table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sku WHERE is_deleted = 0");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active SKUs: " . $count['count'] . "\n";
    
    // Test the JOIN
    $stmt = $pdo->query("SELECT sku.id, sku.sku, customer.name as customer_name FROM sku LEFT JOIN customer ON customer.id = sku.customer_id WHERE sku.is_deleted = 0 LIMIT 3");
    echo "Sample JOIN results:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: " . $row['id'] . ", SKU: " . $row['sku'] . ", Customer: " . $row['customer_name'] . "\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
