+ function ($) {
    'use strict';
	var total = 0.00;
    var ctx = document.getElementById('myChart');
    ctx.width = 600;
    ctx.height = 200;
    var chart_obj;

    function createDataTable(selector) {
        var $table = $(selector);
        var cols = $table.find('thead th');
        // console.log($table.attr('data-package'));
        var dataTable = $table.DataTable({
            dom: 'lrtip',
            lengthChange: true,
            info: false,
            pagingType: 'full_numbers',
            paging: false,
            order: [
                [0, 'asc']
            ],
            columnDefs: [{
                className: 'text-right',
                targets: cols.length - 1
            }],
            autoWidth: false,
            processing: true,
            serverSide: true,
            ajax: {
                url: BASE_URI + 'fetch/' + $table.attr('data-package'),
                data: function (data) {
                    // Add date filter values to the request data
                    data.from = $('#from').val();
                    data.to = $('#to').val();
                }
            },
            footerCallback: function (row, data, start, end, display) {
                var api = this.api();
                var colSum = $table.attr("data-col-sum");

                if (typeof colSum !== "undefined" && colSum > -1) {
                    // returns the numeric value of data
                    var intVal = function (i) {
                        return typeof i === "string"
                            ? i.replace(/[\$,]/g, "") * 1
                            : typeof i === "number"
                            ? i
                            : 0;
                    };

                    // total of all data in table
                    var grandTotal = api
                        .column(colSum)
                        .data()
                        .reduce(function (a, b) {
                            return intVal(a) + intVal(b);
                        }, 0);

                    $(api.column(colSum).footer()).html(
                        $.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
                    );
					
					if(selector === ".tableA") {
						$('#totalCI').html(
							$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
						);
						total += parseFloat(grandTotal);
					} else if(selector === ".tableB") {
						$('#totalCO').html(
							$.fn.addNumericCommas(parseFloat(grandTotal).toFixed(2))
						);
						total -= parseFloat(grandTotal);
					}
					$(".totalNetCash").html(
						$.fn.addNumericCommas(parseFloat(total).toFixed(2))
					)
				}
            },
        });

        return dataTable;
    }

    function initializeChart() {
        chart_obj = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Cash In',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Blue'
                    },
                    {
                        label: 'Cash Out',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Red'
                    },
                    {
                        label: 'Net Cash',
                        data: [],
                        borderWidth: 1,
                        borderColor: 'Green'
                    },
                ]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    initializeChart();

    $(function () {
        var dataTableA = createDataTable('.tableA');
        var dataTableB = createDataTable('.tableB');

        $('#tableFilter').submit(function (event) {
            event.preventDefault();
			total = 0.00;

            var fromDate = $('#from').val();
            var toDate = $('#to').val();

            $.ajax({
                type: 'POST',
                url: BASE_URI + 'cash_flows/chart_data',
                data: { fromDate: fromDate, toDate: toDate },
                dataType: 'json',
                success: function (response) {
                    chart_obj.destroy();
                    let cash_in_label = [];
                    let cash_in_data = [];
                    let cash_out_label = [];
                    let cash_out_data = [];
                    let income_data = [];
                    let data_cash_in = response.data.cash_in;
                    let data_cash_out = response.data.cash_out;
                    for (let i = 0; i < data_cash_in.length; i++) {
                        cash_in_label.push(data_cash_in[i].cash_in_date);
                        cash_in_data.push(parseFloat(data_cash_in[i].total_cash_in_per_day));
                        cash_out_label.push(data_cash_out[i].cash_out_date);

                        cash_out_data.push(parseFloat(data_cash_out[i].total_cash_out_per_day));
                        income_data.push(parseFloat(data_cash_in[i].total_cash_in_per_day - data_cash_out[i].total_cash_out_per_day));
                    }

                    chart_obj = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: cash_in_label,
                            datasets: [
                                {
                                    label: 'Cash In',
                                    data: cash_in_data,
                                    borderWidth: 1,
                                    borderColor: 'blue'
                                },
                                {
                                    label: 'Cash Out',
                                    data: cash_out_data,
                                    borderWidth: 1,
                                    borderColor: 'red'
                                },
                                {
                                    label: 'Net Cash',
                                    data: income_data,
                                    borderWidth: 1,
                                    borderColor: 'green'
                                },
                            ]
                        },
                        options: {
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                },
                error: function (error) {
                    console.error(error);
                }
            });
            
            // Reload tables with updated date filter values
            dataTableA.ajax.reload();
            dataTableB.ajax.reload();
        }).trigger("submit");
    });

    $('#exportToExcel').click(function () {
        // Get the date filter values
        var fromDate = $('#from').val();
        var toDate = $('#to').val();
    
        // Trigger an AJAX request to fetch data based on the date filter
        $.ajax({
            type: 'POST',
            url: BASE_URI + 'cash_flows/export_excel',
            data: { fromDate: fromDate, toDate: toDate },
            dataType: 'json',
            success: function (data) {
                // alert('Excel file downloaded successfully.');
                window.open(BASE_URI + 'assets/' + data.filename);
            },
            error: function (error) {
                console.error(error);
            }
        });
    });
}(jQuery);
