+(function ($) {
	"use strict";

	function printPurchase(purchaseOrderId) {
		$.when(markPrinted(purchaseOrderId)).then(
			function () {
				window.print();
			},
			function (jqXHR) {
				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function approve(purchaseOrderId, status) {
		$.when(approvePurchase(purchaseOrderId, status)).then(function (response) {
			window.location.href = BASE_URI + "purchase_orders/manager?status=for release";
		});
	}

	function approvePurchase(purchaseOrderId, status) {
		return $.ajax({
			url: BASE_URI + "purchase_orders/approve_status/",
			type: "POST",
			dataType: "json",
			data: { purchase_order_id: purchaseOrderId, status: status },
		});
	}

	$(function () {
		$("button.action-approve").click(function (event) {
			var status = event.currentTarget.getAttribute("data-status");

			alertify
				.confirm(
					"Are you sure you want to approve this Purchase Order for " +
						status +
						"?",
					function () {
						var purchaseOrderId = event.currentTarget.getAttribute("data-id");
						approve(purchaseOrderId, status);
					}
				)
				.setHeader("<em>Approve Purchase Order for " + status + "</em>");
		});
	});
})(jQuery);
