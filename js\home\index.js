+ function ($) {
	'use strict';

	function getItemInfo(itemID) {
		return $.ajax({
			url: BASE_URI + 'menu/info/' + itemID,
			type: 'GET',
			dataType: 'json'
		});
	}

	function openModalForm(item) {
		var $modal = $('#orderItem');
		$modal.find('.modal-title').html(item.name);
		$modal.find('form').attr('action', BASE_URI + 'carts/add_item/' + item.id);
		$modal.find('input[name="unit_price"]').val(item.unit_price);
		$modal.find('input[name="quantity"]').val(1);
		$modal.find('p[name="description"]').html(item.description);
		$modal.find('#totalPrice').html(item.unit_price);
		$modal.modal('show');
	}

	function closeModalForm() {
		var $modal = $('#orderItem');
		$modal.modal('hide');

		var $form = $modal.find('form');
		$form[0].reset();
	}

	function submitModalForm() {
		var $modal = $('#orderItem');
		var $form = $modal.find('form');
		var formData = new FormData($form[0]);

		return $.ajax({
			url: $form.attr('action'),
			type: 'POST',
			data: formData,
			processData: false,
			contentType: false,
			dataType: 'json'
		})
	}

	function computeTotal() {
		var $modal = $('#orderItem');

		var quantity = $modal.find('input[name="quantity"]').val();
		quantity = !isNaN(quantity) && quantity > 0 ? quantity : 0;

		var unitPrice = $modal.find('input[name="unit_price"]').val();
		unitPrice = !isNaN(unitPrice) ? unitPrice : 0;

		var total = quantity * unitPrice;
		$modal.find('#totalPrice').html(total.toFixed(2));
	}

	function changeCartQty(itemCount) {
		var $cartQty = $('.cart-qty');
		$cartQty.html(itemCount || '');
	}

	$(function () {
		$('.action-show-category').click(function (event) {
			var sectionID = event.currentTarget.getAttribute('data-show');
			$('.category').hide();
			$(sectionID).show();
		});

		$('.action-add-cart').click(function (event) {
			var itemID = event.currentTarget.getAttribute('data-item');
			$.when(getItemInfo(itemID)).then(
				function (response) {
					openModalForm(response.item)
				},
				function (jqXHR) {
					var response = JSON.parse(jqXHR.responseText);
					alertify.error(response.message);
				}
			);
		});

		$('#orderItem').on('submit', 'form', function (event) {
			event.preventDefault();

			$.when(submitModalForm()).then(
				function (response) {
					alertify.success(response.message);
					changeCartQty(response.item_count);
					closeModalForm();
				},
				function (jqXHR) {
					var response = JSON.parse(jqXHR.responseText);
					alertify.error(response.message);
				}
			);
		});

		$('input[name="quantity"]').change(computeTotal);
	});
}(jQuery);
