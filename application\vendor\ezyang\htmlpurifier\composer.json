{"name": "ezyang/htmlpurifier", "description": "Standards compliant HTML filter written in PHP", "type": "library", "keywords": ["html"], "homepage": "http://htmlpurifier.org/", "license": "LGPL-2.1-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-tidy": "Used for pretty-printing HTML"}, "config": {"sort-packages": true}, "repositories": [{"type": "vcs", "url": "https://github.com/ezyang/simpletest.git"}]}