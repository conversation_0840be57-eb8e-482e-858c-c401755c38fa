!function(n){"use strict";function t(n){var t=parseFloat(n.find('input[name^="act_qty_"]').val());isNaN(t)&&(t=0);var a=n.find('input[name^="act_alw_"]').val();isNaN(a)&&(a=0);var e=n.find('input[name^="current_price_"]').val();isNaN(e)&&(e=0);var i=t*a*e;n.find('input[name^="act_due_amount_"]').val(i)}n(function(){n(".item-table").on("input",'input[name^="act_qty_"]',function(a){var e=n(a.currentTarget).closest(".purchase_order_item");console.log(e),t(e)}).on("input",'input[name^="act_alw_"]',function(a){var e=n(a.currentTarget).closest(".purchase_order_item");t(e)})})}(jQuery);