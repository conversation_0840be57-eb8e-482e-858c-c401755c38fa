<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Soas extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Index
     */
    public function index()
    {
        redirect(site_url('soas/manager'));
    }

    /**
     * Add Statement of Account
     */
    public function add()
    {
        if ($this->_validate_form($this->config->item('soas/add')) and $soa_id = $this->_attempt_add()) {
        // if($this->_validate_form($this->config->item('soas/add'))){

            $where = [
                'id' => $soa_id
            ];
            $soa = $this->soa->select('', $where, 1);

            $from_date = $this->input->post('from_date') ?: date('Y-m-01');
            $to_date = $this->input->post('to_date') ?: date('Y-m-t');

            redirect(site_url('soas/preview/' . $soa->customer_id . '?from_date=' . urlencode($from_date) . '&to_date=' . urlencode($to_date) ));
        } else {
            $this->title = 'Issue Statement of Account';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $where = [
                'is_deleted' => 0
            ];
            $customers = $this->customer->select('', $where,'','name');
            $data = [
                'form_error' => $form_error,
                'customers' => $customers
            ];
            $this->build_content('default', 'soas/add', $data);
        }
    }

    /**
     * Edit Statement of Account
     */
    public function edit($soa_id)
    {
        $where = [
            'id' => $soa_id,
            'is_deleted' => 0
        ];
        $soa = $this->soa->select('', $where, 1) or show_404();

        if ($this->_validate_form($this->config->item('soas/edit')) && $this->_attempt_edit($soa)) {
            redirect(site_url('soas/manager'));
        } else {
            $this->title = 'Edit Statement of Account';

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }
            $data = [
                'form_error' => $form_error,
                'soa' => $soa
            ];
            $this->build_content('default', 'soas/edit', $data);
        }
    }

    /**
     * Delete Statement of Account
     */
    public function delete($soa_id)
    {
        $where = [
            'id' => $soa_id,
            'is_deleted' => 0
        ];
        if (!$soa = $this->soa->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Statement of Account is not found.'
            ];
        } elseif (!$this->_attempt_delete($soa)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Statement of Account is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Statements of Account
     */
    public function manager()
    {
        $this->title = 'Statements of Account';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/soas/manager'
        ];

        $this->build_content('default', 'soas/manager');
    }

    /**
     * Preview Statement of Account
     */
    public function preview($customer_id)
    {
        // $where = [
        //     'id' => $soa_id,
        //     'is_deleted' => 0
        // ];
        // $soa = $this->soa->select('', $where, 1) or show_404();

        // $this->title = 'Statement of Account #' . $soa->soa_no;

        $where = [
            'id' => $customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        // $entries = $this->soa_entry->get_by_soa($soa_id);

        $from_date = $this->input->get('from_date') ? DateTime::createFromFormat('m/d/Y', $this->input->get('from_date'))->format('Y-m-d') : null;
        $to_date = $this->input->get('to_date') ? DateTime::createFromFormat('m/d/Y', $this->input->get('to_date'))->format('Y-m-d') : null;

        $entries = $this->invoice->get_entries_by_customer($customer_id, $from_date, $to_date);

        // foreach ($entries as &$entry) {
        //     $where_paid = [
        //         'invoice_id' => $entry->id, //'invoice_id'
        //         'is_deleted' => 0
        //     ];
        //     $paid_invoice = $this->paid_invoice->select('', $where_paid, 1);
        //     $entry->is_paid = !empty($paid_invoice);

        //     $entry->ar = $this->paid_invoice->get_ar_payment($entry->id) ?: 0;
        //     $entry->pr = $this->paid_invoice->get_pr_payment($entry->id) ?: 0;
        // }

        $data = [
            'date_from' => $from_date,
            'date_to' => $to_date,
            'customer' => $customer,
            'entries' => $entries
        ];
        $this->build_content('default', 'soas/preview', $data);
    }

    /**
     * Invoice
     */
    public function soa_invoice($invoice_id)
    {
        ($invoice = $this->invoice->get_by_id($invoice_id) and in_array($invoice->status, ['approved','for approval','pending'])) or show_404();

        $this->title = 'Invoice No. ' . $invoice->id;
        $this->css = [
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/alertify/build/alertify',
            'js/soas/soa_invoice'
        ];

        $where = [
            'id' => $invoice->customer_id
        ];
        $customer = $this->customer->select('', $where, 1);

        $invoice_items = $this->invoice_item->get_by_invoice($invoice_id);

        $where_paid = [
            'invoice_id' => $invoice->id,
            'is_deleted' => 0
        ];
        $payment_ids = $this->paid_invoice->get_payment_ids_by_invoice($invoice->id);

        $entries = [];
        if(!empty($payment_ids)){
            foreach($payment_ids AS $payment){
                $entry = $this->payment->get_by_id($payment->payment_id);
                $entries[] = [
                    'id' => $entry->id,
                    'payment_date' => $entry->payment_date,
                    'total_paid' => $entry->total_paid,
                    'type' => $entry->type,
                    'ar_no' => $entry->ar_no,
                    'counter' => $entry->counter,
                    'pr_no' => $entry->pr_no,
                    'counter_pr' => $entry->counter_pr,
                    'added_by' => $entry->added_by,
                    'added_on' => $entry->added_on,
                    'updated_by' => $entry->updated_by,
                    'updated_on' => $entry->updated_on,
                    'ar_by' => $entry->ar_by,
                    'ar_on' => $entry->ar_on,
                    'pr_by' => $entry->pr_by,
                    'pr_on' => $entry->pr_on,
                    'is_deleted' => $entry->is_deleted,
                    'receiver' => $entry->receiver,
                    'ard_name' => $entry->ard_name,
                    'prd_name' => $entry->prd_name,
                    'audit_remarks' => $entry->audit_remarks,
                    'audit_status' => $entry->audit_status,
                    'audit_attachment_file' => $entry->audit_attachment_file
                ];
            }
        }

        $data = [
            'invoice' => $invoice,
            'customer' => $customer,
            'invoice_items' => $invoice_items,
            'payments' => $entries
        ];
        $this->build_content('default', 'soas/soa_invoice', $data);
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('soa');
        $this->load->model('soa_entry');
        $this->load->model('customer');
        $this->load->model('invoice');
        $this->load->model('invoice_item');
        $this->load->model('paid_invoice');
        $this->load->model('payment');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $this->db->trans_begin();

        $customer_id = $this->input->post('customer');

        $from_date = DateTime::createFromFormat('m/d/Y', $this->input->post('from_date'));
        $to_date = DateTime::createFromFormat('m/d/Y', $this->input->post('to_date'));

        // $invoices = $this->invoice->get_before_date($from_date->format('Y-m-d'), $customer_id);
        $invoices = $this->invoice->get_by_customer($customer_id);
        $previous_balance = array_reduce($invoices, function ($current_total, $invoice) {
            return $current_total + $invoice->total - $invoice->paid_amount;
        }, 0);

        $where = [
            'YEAR(added_on) =' => date('Y'),
            'is_deleted' => 0
        ];
        $existing = $this->soa->select('', $where);
        $soa_no = date('Y') . ' - ' . sprintf('%04d', count($existing) + 1);

        $values = [
            'customer_id' => $customer_id,
            'soa_no' => $soa_no,
            'from_date' => $from_date->format('Y-m-d'),
            'to_date' => $to_date->format('Y-m-d'),
            'previous_balance' => $previous_balance,
            'added_on' => date('Y-m-d H:i:s'),
            'added_by' => $_SESSION['user']->id
        ];
        if (!$soa_id = $this->soa->insert($values) or
            !$this->_add_soa_entries($soa_id, $from_date->format('Y-m-d'), $to_date->format('Y-m-d'), $customer_id)
        ) {
            $this->db->trans_rollback();
            $this->_error = 'server_error';
            return false;
        } else {
            $this->db->trans_commit();
            return $soa_id;
        }
    }

    /**
     * Add SOA Entries
     */
    protected function _add_soa_entries($soa_id, $from_date, $to_date, $customer_id)
    {
        $values = [];

        $invoices = $this->invoice->get_between_dates($from_date, $to_date, $customer_id);
        foreach ($invoices as $invoice) {
            $values[] = [
                'soa_id' => $soa_id,
                'entry_id' => $invoice->id,
                'entry_date' => $invoice->invoice_date,
                'entry_type' => 'invoice',
                'amount' => $invoice->total,
                'added_by' => $_SESSION['user']->id
            ];
        }

        $payments = $this->paid_invoice->get_between_dates($from_date, $to_date);
        foreach ($payments as $payment) {
            $values[] = [
                'soa_id' => $soa_id,
                'entry_id' => $payment->id,
                'entry_date' => $payment->payment_date,
                'entry_type' => 'payment',
                'amount' => $payment->amount,
                'added_by' => $_SESSION['user']->id
            ];
        }

        if (!$values) {
            return true;
        }

        return $this->soa_entry->insert_batch($values);
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($soa)
    {
        $where = [
            'id' => $soa->id,
            'is_deleted' => 0
        ];
        $values = [
            'customer_id' => $this->input->post('customer'),
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_updated = $this->soa->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_updated;
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($soa)
    {
        $where = [
            'id' => $soa->id,
            'is_deleted' => 0
        ];
        $values = [
            'mer' => $soa->mer,
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id
        ];

        if (!$is_deleted = $this->soa->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Callback: Check if customer ID exists
     */
    public function _is_customer($customer_id)
    {
        $where = [
            'id' => $customer_id,
            'is_deleted' => 0
        ];

        return !empty($this->customer->select('', $where, 1));
    }
}
