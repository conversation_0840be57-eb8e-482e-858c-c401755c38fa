<div class="sect">
	<div class="col-md-4 offset-md-3 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8">
			<div class="crud-title"><?=$title;?></div>

			<hr>

			<fieldset>
				<div class="row">
					<div class="col-md-12 form-group">
						<label for="name">Name</label>

						<input type="text" name="name" value="<?=set_value('name', $pos_item->name);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('name');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="unit">Unit</label>

						<input type="text" name="unit" value="<?=set_value('unit', $pos_item->unit);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('unit');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="price">Price</label>

						<input type="number" step="any" name="price" value="<?=set_value('price', $pos_item->price);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('price');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="branch">Branch</label>

						<select name="branch" value="<?=set_value('branch', $pos_item->branch_id);?>" class="form-control">
							<option></option>
							<?php foreach ($branches as $branch): ?>
								<option value="<?=$branch->id?>" <?=set_select('branch', $pos_item->branch_id);?>><?=$branch->name?></option>
							<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('branch');?>
						</div>
					</div>

					<div class="col-md-6 form-group">
						<label for="category">Category</label>

						<select name="category" value="<?=set_value('category', $pos_item->category_id);?>" class="form-control">
							<option></option>
							<?php foreach ($categories as $category): ?>
								<option value="<?=$category->id?>" <?=set_select('category', $pos_item->category_id);?>><?=$category->name?></option>
							<?php endforeach;?>
						</select>

						<div class="red-text flash-message">
							<?=form_error('category');?>
						</div>
					</div>

					<div class="col-md-12 form-group">
						<label for="priority">Priority</label>

						<input type="number" step="any" name="priority" value="<?=set_value('priority', $pos_item->priority);?>" class="form-control">

						<div class="red-text flash-message">
							<?=form_error('priority');?>
						</div>
					</div>
				</div>
			</fieldset>

			<div class="error">
				<?=$form_error;?>
			</div>

			<hr>

			<div class="clearfix">
				<button type="submit" name="submit" class="btn btn-primary">Save</button>
				<a href="<?=site_url('pos_items/manager');?>" class="btn btn-light">Cancel</a>
			</div>
		</form>
	</div>
</div>
