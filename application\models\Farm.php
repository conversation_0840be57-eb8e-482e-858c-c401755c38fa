<?php
class Farm extends MYT_Model {
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'farm';
        parent::__construct();
    }

    /**
     * Create New farm
     */
    public function generate($name, $address, $phone_no, $email, $added_by)
    {
        $sql = <<<EOT
INSERT INTO farm(name, address, phone_no, email, added_by) VALUES(?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    phone_no = VALUES(phone_no),
    email = VALUES(email),
    is_deleted = 0
EOT;
        $binds = [$name, $address, $phone_no, $email, $added_by];

        $this->advanced_query($sql, $binds);

        return $this->db->insert_id();
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
