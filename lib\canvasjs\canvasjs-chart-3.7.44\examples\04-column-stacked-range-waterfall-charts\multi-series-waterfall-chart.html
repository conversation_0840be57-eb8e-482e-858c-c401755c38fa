<!DOCTYPE HTML> 
<html>  
<head>  
<meta charset="UTF-8">
<script>
window.onload = function () {

var chart = new CanvasJS.Chart("chartContainer", {
	theme: "light2", // "light1", "light2", "dark1", "dark2"
	animationEnabled: true,
	title:{
		text: "Sales Comparision of 2 Sellers"
	},
	axisX: {
		interval: 1
	},
	axisY: {
		//prefix: "$",
		//suffix: "k",
      	valueFormatString: "$#,##0,.M"
	},
	toolTip: {
		shared: true
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [{
		type: "waterfall",
		yValueFormatString: "$#,##0,.00M",
		name: "Seller 1",
		showInLegend: true,
		indexLabelOrientation: "vertical",
		indexLabelFontColor: "black",
		dataPoints: [
			{ label: "Initial", y: 7645 },
			{ label: "Jan", y: 3312 },
			{ label: "Feb", y: 5065 },
			{ label: "Mar", y: -2564 },
			{ label: "Apr", y: 6004 },
			{ label: "May", y: 5324 },
			{ label: "Jun", y: -11543 },
			{ label: "July", y: 3802 },
			{ label: "Aug", y: 6673 },
			{ label: "Sep", y: -5997 },
			{ label: "Oct", y: 6654 },
			{ label: "Nov", y: -4943 },
			{ label: "Dec", y: 3324 },
			{ label: "Final", isCumulativeSum: true, indexLabel: "{y}" }
		]
		},
		{
			type: "waterfall",
			yValueFormatString: "$#,##0,.00M",
			lineDashType: "solid",
			name: "Seller 2",
			showInLegend: true,
			indexLabelOrientation: "vertical",
			indexLabelFontColor: "black",
			dataPoints: [
				{ label: "Initial", y: 4634 },
				{ label: "Jan", y: -2002 },
				{ label: "Feb", y: 5095 },
				{ label: "Mar", y: 2243 },
				{ label: "Apr", y: 1984 },
				{ label: "May", y: -6724 },
				{ label: "Jun", y: 1901 },
				{ label: "July", y: 3127 },
				{ label: "Aug", y: 3324 },
				{ label: "Sep", y: 2324 },
				{ label: "Oct", y: -3574 },
				{ label: "Nov", y: -1984 },
				{ label: "Dec", y: 3594 },
				{ label: "Final", isCumulativeSum: true, indexLabel: "{y}" }
			]
	}]
});
chart.render();

function toggleDataSeries(e) {
	if(typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	}
	else {
		e.dataSeries.visible = true;
	}
	chart.render();
}

}
</script>
</head>
<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="../../canvasjs.min.js"></script>
</body>
</html>