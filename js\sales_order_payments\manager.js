+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');
		var status = $('.tab-link.active').attr('data-status');

		var dataTable = $table.DataTable({
			lengthChange: false,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[0, 'asc']
			],
			// columnDefs: [{
			// 	className: 'text-right',
			// 	targets: cols.length - 2
			// }, {
			// 	className: 'dt-action',
			// 	targets: cols.length - 1
			// }],
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package') + '?status=' + status
		});

		return dataTable;
	}

	function receiveAndReload(sales_order_paymentId, dataTable) {
		$.when(receiveRow(sales_order_paymentId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function receiveRow(sales_order_paymentId) {
		return $.ajax({
			url: BASE_URI + 'sales_order_payments/receive_sales_order_payment/' + sales_order_paymentId,
			type: 'POST',
			dataType: 'json'
		});
	}

	function deleteAndReload(sales_order_paymentId, dataTable) {
		$.when(deleteRow(sales_order_paymentId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(sales_order_paymentId) {
		return $.ajax({
			url: BASE_URI + 'sales_order_payments/delete/' + sales_order_paymentId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA')
			.on('click', '.action-receive', function (event) {
				var cols = $(event.currentTarget).closest('tr.sales_order_payment').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to receive the sales_order_payment ' + docNo + '?',
					function () {
						var sales_order_paymentId = event.currentTarget.getAttribute('data-id');
						receiveAndReload(sales_order_paymentId, dataTable);
					}
				).setHeader('<em>receive sales_order_payment</em>');
			})
			.on('click', '.action-delete', function (event) {
				var cols = $(event.currentTarget).closest('tr.sales_order_payment').find('td');
				var docNo = typeof cols[0] !== 'undefined' ? cols[0].innerHTML : '';

				alertify.confirm(
					'Are you sure you want to delete Sales Order Payment ' + docNo + '?',
					function () {
						var sales_order_paymentId = event.currentTarget.getAttribute('data-id');
						deleteAndReload(sales_order_paymentId, dataTable);
					}
				).setHeader('<em>Delete Sales Order Payment</em>');
			});

		$('.tab-link').click(function(event) {
			$('.tab-link.active').removeClass('active');
			event.currentTarget.classList.add('active');

			var url = BASE_URI + 'fetch/sales_order_payments?status=' + event.currentTarget.getAttribute('data-status');
			var manager_url = BASE_URI + 'sales_order_payments/manager?status=' + event.currentTarget.getAttribute('data-status');
			console.log(url);
			window.history.pushState(
				null,
				null,
				manager_url
			);
			dataTable.ajax.url(url).load();
		})
	});
}(jQuery);
