<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pos_items extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Items
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname
        ];
        $table = 'pos_item';
        $primary_key = 'pos_item.id';

        $joins = <<< EOT

pos_item LEFT JOIN branch ON branch.id = pos_item.branch_id LEFT JOIN category ON category.id = pos_item.category_id
EOT;

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_name(),
            $this->_get_unit(),
            $this->_get_branch(),
            $this->_get_category(),
            $this->_get_price(),
            $this->_get_actions()
        ];

        $where = <<<EOT
pos_item.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'pos_item.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'pos_item.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'item';
            }
        ];
    }

    /**
     * Get name
     */
    protected function _get_name()
    {
        return [
            'db' => 'pos_item.name',
            'as' => 'pos_item_name',
            'dt' => 0,
            'field' => 'pos_item_name'
        ];
    }

    /**
     * Get unit of measure
     */
    protected function _get_unit()
    {
        return [
            'db' => 'unit',
            'dt' => 1,
            'field' => 'unit'
        ];
    }

    /**
     * Get branch
     */
    protected function _get_branch()
    {
        return [
            'db' => 'branch.name',
            'as' => 'branch_name',
            'dt' => 2,
            'field' => 'branch_name'
        ];
    }

    /**
     * Get branch
     */
    protected function _get_category()
    {
        return [
            'db' => 'category.name',
            'as' => 'category_name',
            'dt' => 3,
            'field' => 'category_name'
        ];
    }

    /**
     * Get price
     */
    protected function _get_price()
    {
        return [
            'db' => 'FORMAT(price, 2)',
            'as' => 'price',
            'dt' => 4,
            'field' => 'price'
        ];
    }


    /**
     * Get actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'pos_item.id',
            'as' => 'actions',
            'dt' => 5,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';

                if (in_array($_SESSION['user']->role_id, [ROLE_ADMIN])) {
                    $res .= anchor('pos_items/edit/' . $d, 'Edit', 'title="Edit" class="dropdown-item text-left"');
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $d . '">Delete</button>';
                }

                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}
