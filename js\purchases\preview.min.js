!function(e){"use strict";function t(t){e.when(a(t)).then(function(){window.print()},function(e){var t=JSON.parse(e.responseText);alertify.error(t.message)})}function a(t){return e.ajax({url:BASE_URI+"purchases/mark_printed/"+t,type:"POST",dataType:"json"})}function r(t,a,r,n){var s=new FormData;s.append("purchase_id",t),s.append("type",a),s.append("remarks",r),s.append("audit_status",n);for(var i=document.getElementById("attachments").files,u=0;u<i.length;u++)s.append("attachments[]",i[u]);e.ajax({url:BASE_URI+"purchases/mark_status/",type:"POST",data:s,processData:!1,contentType:!1,dataType:"json",success:function(e){e.is_success?window.location.href=BASE_URI+"purchases/manager/":alertify.error(e.message)},error:function(e){var t=JSON.parse(e.responseText);alertify.error(t.message)}})}e(function(){e(".action-print").click(function(e){var a=e.currentTarget.getAttribute("data-type");"Audit"===a?alertify.confirm("Audit Purchase Order",'\n                    <div>\n                        <input type="radio" id="complete" name="audit_status" value="complete" checked> Complete &nbsp; &nbsp;\n                        <input type="radio" id="incomplete" name="audit_status" value="incomplete"> Incomplete<br>\n                        &nbsp;\n                    </div>\n                    <div>\n                        <textarea id="remarks" name="remarks" placeholder="Enter remarks..." class="w-100"></textarea>\n                    </div>\n                    <div>\n                        <input type="file" id="attachments" name="attachments[]" accept=".pdf, .jpg, .jpeg, .png" multiple>\n                    </div>\n                    ',function(){var t=e.currentTarget.getAttribute("data-id"),n=document.getElementById("remarks").value,s=document.querySelector('input[name="audit_status"]:checked').value;""!==n&&s?r(t,a,n,s):alertify.alert('<span class="red-text">Missing Remarks or Audit Status!</span>').setHeader("<em>Required</em>")},function(){alertify.error("Cancel")}).setHeader("<em>Audit Purchase Order</em>"):alertify.confirm("Are you sure you want to "+a+" this Purchase Order?",function(){var n=e.currentTarget.getAttribute("data-id");"Print"==a?t(n):r(n,a,null)}).setHeader("<em>"+a+" Purchase Order</em>")})})}(jQuery);