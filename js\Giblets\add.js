$(document).ready(function() {
    function gibletsRowTemplate() {
        // Calculate the next NO value based on existing rows
        var nextNo = $('#gibletsTableBody .giblets-row').length + 1;

        // Create a row with auto-incremented NO, but empty PCS and KGS for manual entry
        return '<tr class="giblets-row">' +
            '<td><input type="text" class="form-control" name="no[]" value="' + nextNo + '" readonly style="background-color: #f9f9f9; cursor: not-allowed;"></td>' +
            '<td><input type="text" class="form-control" name="pcs[]" value=""></td>' +
            '<td><input type="text" class="form-control" name="kgs[]" value=""></td>' +
            '<td class="text-center" style="vertical-align: middle;">' +
                '<button type="button" class="btn btn-danger btn-sm action-delete-row">Delete</button>' +
            '</td>' +
        '</tr>';
    }

    // Function to update only the NO fields (sequential numbering)
    function updateNoFields() {
        // Update NO fields for all rows with sequential numbering
        $('#gibletsTableBody .giblets-row').each(function(index) {
            // Set sequential NO value (1-based index)
            var rowNumber = index + 1;

            $(this).find('input[name="no[]"]')
                .val(rowNumber)
                .prop('readonly', true)
                .css({
                    'background-color': '#f9f9f9',
                    'cursor': 'not-allowed'
                });
        });

        // Make the first row's NO field bold
        $('#gibletsTableBody .giblets-row:first input[name="no[]"]').css('font-weight', 'bold');
    }

    // Add new row with auto-incremented NO
    $('#addGibletsRow').click(function() {
        var newRow = gibletsRowTemplate();
        $('#gibletsTableBody').append(newRow);

        // Immediately update NO fields for proper sequential numbering
        updateNoFields();
    });

    // No need to update other fields when first row changes since we only auto-fill NO
    // The event listeners are removed since PCS and KGS should be manually entered

    // Function to fix row structure
    function fixRowStructure() {
        var $firstRow = $('#gibletsTableBody .giblets-row:first');
        var firstRowCellCount = $firstRow.find('td').length;

        // Check all rows after the first one
        $('#gibletsTableBody .giblets-row:not(:first)').each(function() {
            var $row = $(this);
            var currentCellCount = $row.find('td').length;

            // If this row doesn't match the first row's structure, replace it
            if (currentCellCount !== firstRowCellCount) {
                $row.replaceWith(gibletsRowTemplate());
            }
        });

        // After fixing structure, ensure all rows are numbered sequentially
        renumberRows();
    }

    // Initialize sequential numbering on page load
    setTimeout(function() {
        // First fix any structural issues
        fixRowStructure();

        // Then update NO fields for sequential numbering
        updateNoFields();
    }, 100);

    // Delete row functionality
    $('#gibletsTable').on('click', '.action-delete-row', function() {
        var rowCount = $('#gibletsTableBody .giblets-row').length;
        if (rowCount > 1) {
            $(this).closest('.giblets-row').remove();
            // Renumber all rows after deletion
            renumberRows();
        } else {
            alert('At least one row is required.');
        }
    });

    // Function to renumber all rows sequentially
    function renumberRows() {
        $('#gibletsTableBody .giblets-row').each(function(index) {
            var rowNumber = index + 1;
            $(this).find('input[name="no[]"]').val(rowNumber);
        });
    }
});