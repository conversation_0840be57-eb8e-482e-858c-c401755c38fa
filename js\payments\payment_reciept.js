+ function ($) {
	'use strict';

	function mark_acknowledge(payment_id, pr_no) {
		return $.ajax({
			url: BASE_URI + 'payments/mark_payment/',
			type: 'POST',
			data: { payment_id: payment_id, pr_no: pr_no },
			dataType: 'json'
		});
	}

	$(function () {
		$('.action-print').click(function () {
			var paymentId = $('input[name="payment_id"]').val();
			var arNo = $('input[name="pr_no"]').val();
			alertify
				.confirm('Are you sure you want to print?', function (e) {
					if (e) {
						mark_acknowledge(paymentId, arNo)
							.done(function() {
								window.print();
								window.location.reload();
							})
							.fail(function(jqXHR, textStatus, errorThrown) {
								console.error('Error during mark_acknowledge:', textStatus, errorThrown);
								let response = JSON.parse(jqXHR.responseText);
								alertify.error(response.message);
							});
					} else {
						alertify.closeLogOnClick(true);
					}
            	})
				.setting({
					'title': 'Confirm Payment Receipt!',
					'message': '<div style="text-align: center;">Are you sure you want to print this payment receipt?</div>'
				})
		});
	});
}(jQuery);
