<?php
class Permission extends MYT_Model
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->table_name = 'permission';
        parent::__construct();
    }

    /**
     * Permission is authorized
     */
    public function is_authorized($role_id, $controller, $method)
    {
        $sql = <<<EOT
SELECT *
FROM permission
LEFT JOIN page ON page.id = permission.page_id
WHERE role_id = ?
AND controller = ?
AND method = ?
EOT;
        $binds = [$role_id, $controller, $method];
        // die($this->db->compile_binds($sql,$binds));
        return $this->advanced_query($sql, $binds);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
