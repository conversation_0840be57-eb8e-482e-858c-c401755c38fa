<div class="col topbtn">
	<div class="title"><?=$title;?></div>
	<p class="crud-subtitle"><i>Note: All POs that are completely audited.</i></p>
</div>

<div class="col">
	<form method="get" class="form-inline" id="tableFilter">
		<label for="supplier" class="mr-sm-2">Supplier:</label>
		<select name="supplier" class="form-control mb-2 mr-sm-2 dropsearch" data-placeholder="Select Supplier">
			<option>All</option>
			<?php foreach ($suppliers as $supplier): ?>
				<option value="<?=$supplier->id;?>"><?=$supplier->name;?></option>
			<?php endforeach;?>
		</select>

		<label for="from" class="ml-sm-2 mr-sm-2">From Date:</label>
		<!-- <input type="text" name="from" class="form-control datepicker mb-2 mr-sm-2"> -->
		<input type="text" value="<?=set_value("from", date('m/01/Y'));?>" name="from" class="form-control datepicker mb-2 mr-sm-2">

		<label for="to" class="ml-sm-2 mr-sm-2">To Date:</label>
		<!-- <input type="text" name="to" class="form-control datepicker mb-2 mr-sm-2"> -->
		<input type="text" value="<?=set_value("to", date("m/d/Y"));?>" name="to" class="form-control datepicker mb-2 mr-sm-2">

		<label for="main_usage" class="ml-sm-2 mr-sm-2">Usage:</label>
		<input type="text" name="main_usage" class="form-control go-suggest" data-suggest="main_usages">

		
		<label for="main_usage" class="ml-sm-2 mr-sm-2">Sub-Usage:</label>
		<input type="text" name="sub_usage" class="form-control go-suggest" data-suggest="sub_usages">
		

		<button type="submit" class="btn btn-primary ml-sm-2 mb-2">Filter</button>
	</form>
</div>

<div class="col">
	<div class="dt-asc-wrapper row" data-display="expenses">
		<table class="table table-hover tableA" data-package="expenses" data-col-sum="9">
			<thead>
				<tr>
					<th>Doc No.</th>
					<th>Supplier</th>
					<th>Purchase Date</th>
					<th>Usage</th>
					<th>Sub-Usage</th>
					<th>Items</th>
					<th>Requisitioner</th>
					<th>Approved By</th>
					<th>Printed By</th>
					<th class="text-right">Total</th>
				</tr>
			</thead>

			<tbody></tbody>
		<tfoot>
			<tr>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
				<th></th>
				<th>Total</th>
				<th></th>
			</tr>
		</tfoot>


		</table>
	</div>
</div>